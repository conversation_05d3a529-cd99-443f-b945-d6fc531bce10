<template>
  <main  v-if="headerVisible" class="default-main">
    <div class="default-main-header">
      <a-header @setHidden="setHidden" />
    </div>
    <router-view></router-view>
  </main>
  <router-view v-else></router-view>
</template>
<script>
import aHeader from './header/header.vue';
export default {
    data() {
        return {
            headerVisible: window.LOCAL_CONFIG.HEADER_VISIBLE === true
        };
    },
    methods: {
        setHidden() {
            this.headerVisible = false;
        }
    },
    components: { aHeader }
};
</script>
<style lang="less" scoped>
// @headerHeight: 62px;

.default-main {
    height: 100%;
    width: 100%;
    padding: 0;

    &-header {
        // height: @headerHeight;
        width: 100%;
    }

    /deep/ .h-menu-horizontal {
        height: unset;
    }
}
</style>
