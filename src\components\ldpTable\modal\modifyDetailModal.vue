<template>
    <div class="detail-content">
        <!-- 数据表查看、编辑弹窗 -->
        <h-msg-box-safe v-model="detialBoxData.status" :escClose="true" :mask-closable="false" :title="`查看内存表${tableInfo.tableName}数据结构`" :width="boxWidth" height="90" top="40">
            <div class="page-mode">
                <div class="page-group">
                    <h-icon name="ios-arrow-back" @on-click="pageUp"></h-icon>
                    <span class="page-group-span">
                        表序号：{{tableData[recordIndex].TableId}}&nbsp;&nbsp;
                        线程号：{{tableData[recordIndex].ThreadId}}&nbsp;&nbsp;
                        数据记录号：{{tableData[recordIndex].RecordId}} </span>
                    <h-icon name="ios-arrow-forward" @on-click="pageDown"></h-icon>
                </div>
                <h-radio-group v-model="modeType" class="radio-group" @on-change="changeMode">
                    <h-radio label="查询模式"></h-radio>
                    <h-radio label="编辑模式"></h-radio>
                </h-radio-group>
            </div>
            <div class="memory-tab">
               <memory-tab-data :key="tKey" ref="memory-tab" :isEdit="isEdit" :memoryKey="tableInfo.tableName" :memoryData="memoryData" :memoryStruct="memoryStruct"
               :parentStructList="parentStruct" :initStructList="initStructList" @struct-number="callBackStructNumber"></memory-tab-data>
            </div>

            <template v-slot:footer>
                <a-button type="primary" :disabled="!isEdit" @click="submitConfig">加入修改预览</a-button>
            </template>
        </h-msg-box-safe>
    </div>
</template>

<script>
import _ from 'lodash';
import aButton from '@/components/common/button/aButton';
import memoryTabData from '@/components/ldpProduct/memoryTabData/memoryTabData';
import { addPreview } from '@/api/httpApi';

export default {
    name: 'ModifyDetailModal',
    props: {
        detialBoxInfo: {
            type: Object,
            default: null
        },
        tableInfo: {
            type: Object,
            default: ''
        },
        memoryStruct: {
            type: Array,
            default: []
        },
        tableData: {
            type: Array,
            default: []
        }
    },
    data() {
        return {
            boxWidth: 80,
            detialBoxData: this.detialBoxInfo,
            recordIndex: 0,
            modeType: '查询模式',
            memoryData: {},
            parentStruct: [],
            isEdit: false,
            tKey: 0,
            initStructList: []
        };
    },
    mounted() {
        this.recordIndex = this.detialBoxInfo.tableIndex;
        this.modeType = this.detialBoxData.viewMode ? '编辑模式' : '查询模式';
        this.isEdit = this.detialBoxData.viewMode ? true : false;
        this.updataData();
        this.tKey++;
    },
    methods: {
        closeBox() {
            this.detialBoxData.status = false;
        },
        // 根据展开结构个数改变弹窗宽度
        callBackStructNumber(num) {
            const maxWidth = document.documentElement.clientWidth * 0.8;
            const computeLength = num * 250 + 35;
            this.boxWidth = num < 3 ? 550 : computeLength > maxWidth ? maxWidth : computeLength ;
        },
        // 更新数据
        updataData() {
            this.parentStruct = this.tableData[this.recordIndex]?.TableLevels.reverse();
            this.memoryData = _.cloneDeep(this.tableData[this.recordIndex]);
            delete this.memoryData.TableId;
            delete this.memoryData.ThreadId;
            delete this.memoryData.RecordId;
            delete this.memoryData.TableLevels;
            delete this.memoryData.TableAttributes;
        },

        // 向前翻表记录
        pageUp() {
            if (this.recordIndex > 0) {
                this.recordIndex--;
                this.initStructList = this.$refs['memory-tab'].getInitSturctList();
                this.updataData();
                this.tKey++;
            } else {
                this.$hMessage.warning('此为当前页第一条记录');
            }
        },
        // 向后翻表记录
        pageDown() {
            const length = this.tableData?.length;
            if (this.recordIndex < length - 1) {
                this.recordIndex++;
                this.initStructList = this.$refs['memory-tab'].getInitSturctList();
                this.updataData();
                this.tKey++;
            } else {
                this.$hMessage.warning('此为当前页最后一条记录');
            }
        },
        // 切换展示模式
        changeMode(value) {
            this.isEdit = value === '编辑模式';
        },
        // 从组件获取修改数据并整理
        getEditData() {
            const modifyObj = this.$refs['memory-tab'].callBackEditData(); // 从组件获取修改数据
            const conmitModifyList = [];
            for (const item of Object.keys(modifyObj))  {
                if (!modifyObj[item].validate) {
                    this.$hMessage.error('数据校验失败');
                    return 'fail';
                };
                if (modifyObj[item].newVal !== modifyObj[item].oldVal) {
                    let name = item.replace(/\.(\d+)/g, '[$1]');
                    const length = this.tableInfo.tableName.length + 1;
                    name = name.slice(length);
                    conmitModifyList.push({
                        fieldName: name,
                        fieldPath: name,
                        fieldType: modifyObj[item].type,
                        currentValue: modifyObj[item].oldVal,
                        updateValue: modifyObj[item].newVal
                    });
                }
            }
            return conmitModifyList;
        },

        // 加入修改单
        async submitConfig() {
            const that = this;
            if (this.getEditData() === 'fail') return;
            const conmitModifyList = this.getEditData();
            if (!conmitModifyList.length) return this.$hMessage.warning('无数据修改');
            const { TableId, ThreadId, RecordId } = this.tableData[this.recordIndex];
            const param = {
                databaseName: this.tableInfo.databaseName,
                tableName: this.tableInfo.tableName,
                ...this.tableInfo.endpointInfo,
                updateFields: conmitModifyList,
                requestBody: this.tableData[this.recordIndex],
                tableId: TableId,
                threadId: ThreadId,
                recordId: RecordId
            };
            // 调用接口 添加修改单列表
            try {
                const res = await addPreview(param);
                if (res) {
                    this.$hNotice.open({
                        title: '加入修改单',
                        duration: 3,
                        render(h) {
                            return h('div', {
                                style: {
                                    display: 'flex',
                                    'flex-flow': 'row wrap',
                                    'flex-direction': 'column',
                                    'align-items': 'center'
                                }
                            }, [
                                h('h3', {
                                    style: {
                                        width: '100%',
                                        lineHeight: '30px',
                                        margin: '0 0 20px 0'
                                    }
                                }, ['加入修改预览']),
                                h('p', {
                                    style: {
                                        width: '100%',
                                        fontSize: '15px',
                                        marginBottom: '30px',
                                        'text-align': 'center'
                                    }
                                }, [
                                    '加入修改预览成功'
                                ]),
                                h('h-button', {
                                    attrs: {
                                        type: 'primary'
                                    },
                                    style: {
                                        marginRight: '5px'
                                    },
                                    on: {
                                        click: () => {
                                            that.viewPreview();
                                        }
                                    }
                                }, ['查看修改预览'])
                            ]);
                        }
                    });
                }
            } catch (res) {
                this.$hMessage.error('加入修改预览失败');
            }
        },
        // 查看修改单
        viewPreview() {
            this.$parent.$parent.$parent.$parent.$parent.getPreviewTableData();
            this.$hNotice.destroy();
            this.closeBox();
        }
    },
    components: { aButton, memoryTabData }
};
</script>

<style lang="less" scoped>
/deep/ .h-modal-body {
    height: ~"calc(100% - 106px)" !important;
    padding: 5px 16px 0;

    .page-mode {
        height: 45px;
        padding: 10px 0 16px;
        display: flex;
        align-items: center;

        .page-group-span {
            padding: 0 3px;
        }

        .page-group {
            display: flex;
            align-items: center;

            &:hover {
                cursor: pointer;
            }
        }

        .radio-group {
            position: absolute;
            right: 16px;
        }
    }

    .memory-tab {
        width: 100%;
        height: ~"calc(100% - 45px)";
    }
}

.detail-content {
    padding: 5px;
}
</style>
