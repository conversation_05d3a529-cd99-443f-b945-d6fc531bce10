import { defineComponent } from 'vue';
import { renderTextWithMatch } from './util';

import './selectItem.less';

export default defineComponent({
    name: 'SelectItem',
    props: {
        index: {
            type: Number
        },
        /**
         * 源数据
         */
        source: {
            type: Object,
            default: () => {}
        },
        /**
         * 搜索关键词
         */
        keyword: {
            type: String,
            default: () => ''
        },
        /**
         * 下拉框选择时触发
         */
        onChange: {
            type: Function
        },
        /**
         * 当前聚焦的索引
         */
        focusItemIndex: {
            type: Number,
            default: () => null
        },
        /**
         * 是否忽视大小写
         */
        ignoreUpperCase: {
            type: Boolean
        },
        /**
         * 是否使用模糊搜索
         */
        useFuzzy: {
            type: Boolean,
            default: () => false
        },
        labelMapper: {
            type: String,
            default: () => 'label'
        }
    },
    computed: {
        list() {
            return renderTextWithMatch({
                label: this.source[this.labelMapper],
                keyword: this.keyword,
                ignoreUpperCase: this.ignoreUpperCase,
                useFuzzy: this.useFuzzy
            });
        }
    },
    render() {
        return (
            <div
                class="virtual-select-item"
                data-focus={this.focusItemIndex === this.index}
                onClick={() => {
                    this.onChange(this.source);
                }}
            >
                {this.list.map(
                    (labelItem, index) => (
                        <span
                            class="option-text"
                            key={index}
                            data-match={labelItem.match}
                        >
                            {labelItem.value}
                        </span>
                    )
                )}
            </div>
        );
    }
});
