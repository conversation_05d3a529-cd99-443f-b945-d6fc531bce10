<template>
    <div>
        <!-- 下发配置列表 -->
        <h-msg-box-safe v-model="modalData.status" :escClose="true" :mask-closable="false" title="数据清理配置" width="50"
            maxHeight="300" @on-open="getCollections" @on-close="cancelConfig">
                <p class='title'>{{ !nextStatus ? '选择清理条件':'清理条件确认'}}</p>
                <h-form v-if="!nextStatus" ref="formValidate" :model="formItem" :label-width="85">
                    <h-form-item label="产品节点:" prop="productInstNo" required>
                        <h-select v-model="formItem.productInstNo" placeholder="请选择" :positionFixed="true"
                            :setDefSelect="true">
                            <h-option v-for="item in productList" :key="item.id"
                                :value="item.productInstNo">{{item.productName}}</h-option>
                        </h-select>
                    </h-form-item>
                    <h-form-item label="数据日期:" prop="time" required>
                        <h-date-picker v-model="formItem.time" type="daterange" placeholder="选择日期"
                            :positionFixed="true"></h-date-picker>
                    </h-form-item>
                    <h-form-item label="数据类型:" prop="indexType" required>
                        <h-select v-model="formItem.indexType" placeholder="请选择" :positionFixed="true" :setDefSelect="true">
                            <h-option v-for="item in indexTypes" :key="item.key" :value="item.key">{{item.value}}</h-option>
                        </h-select>
                    </h-form-item>
                    <h-form-item v-if="formItem.indexType === 'latency'" label="资金账号：" prop="accountId">
                        <h-input v-model="formItem.accountId" placeholder="请输入"></h-input>
                    </h-form-item>
                </h-form>
                <div v-if='nextStatus' class="confirm-data">
                    <p>产品节点：<span>{{ confirmData.productInstNo }}</span></p>
                    <p>数据日期：<span>{{ confirmData.startTime }}</span> 至 <span>{{ confirmData.endTime }}</span></p>
                    <p>数据类型：<span>{{ confirmData.indexType }}</span></p>
                    <p v-if="formItem.indexType === 'latency'">资金账号：<span>{{ confirmData.accountId }}</span></p>
                    <p style="color: var(--error-color);">警告: 该操作将物理删除符合条件的所有数据, 操作不可逆!</p>
                </div>
            <template v-slot:footer>
                    <a-button v-if="!nextStatus" @click="ConfigConfirm">下一步</a-button>
                    <a-button v-if="nextStatus" @click="cancelConfig">取消</a-button>
                    <a-button v-if="nextStatus" type="primary" :loading="loading" @click="submitConfig">确认清理</a-button>
                </template>
        </h-msg-box-safe>
    </div>
</template>

<script>
import _ from 'lodash';
import { mapState, mapActions } from 'vuex';
import { formatDate } from '@/utils/utils';
import { clearManagementData } from '@/api/httpApi';
import aButton from '@/components/common/button/aButton';
export default ({
    props: {
        modalInfo: {
            type: Object,
            default: null
        }
    },
    data() {
        return {
            loading: false,
            modalData: this.modalInfo,
            indexTypes: [
                {
                    key: 'latency',
                    value: '时延跟踪数据'
                },
                {
                    key: 'monitor',
                    value: '监控指标数据'
                }
            ],
            formItem: {
                productInstNo: '',
                indexType: '',
                indexName: '',
                startTime: '',
                endTime: '',
                time: [formatDate(new Date()), formatDate(new Date())],
                accountId: ''
            },
            nextStatus: false,
            confirmData: {
                productInstNo: '',
                indexType: '',
                indexName: '',
                startTime: '',
                endTime: '',
                accountId: ''
            }
        };
    },
    computed: {
        ...mapState({
            productList: state => {
                return state.product.productListLight || [];
            }
        })
    },
    methods: {
        ...mapActions({ getProductList: 'product/getProductListLight' }),
        getCollections() {
            this.getProductList();
            this.formItem.time = [new Date(), new Date()];
            this.formItem.indexType = this.indexTypes[0].key;
            this.formItem.accountId = '';
        },
        ConfigConfirm() {
            this.$refs['formValidate'].validate(async (valid) => {
                if (valid) {
                    this.confirmData = {
                        ...this.formItem,
                        productInstNo: _.find(this.productList, ['productInstNo', this.formItem.productInstNo])?.productName,
                        indexType: _.find(this.indexTypes, ['key', this.formItem.indexType])?.value,
                        startTime: formatDate(this.formItem.time[0]) + ' 00:00:00',
                        endTime: formatDate(this.formItem.time[1]) + ' 00:00:00'
                    };
                    this.nextStatus = true;
                }
            });
        },
        cancelConfig() {
            this.nextStatus = false;
            // this.modalData.status = false;
        },
        async submitConfig() {
            this.formItem.indexName = this.formItem.indexType === 'latency' ? 'jaeger-span' : 'ldp-apm-metrics';
            const param = { ...this.formItem };
            param.startTime = formatDate(this.formItem.time[0]) + ' 00:00:00';
            param.endTime = formatDate(this.formItem.time[1]) + ' 00:00:00';
            const res = await clearManagementData(param);
            if (res.success) {
                this.$hMessage.success('清理指令发送成功！');
                this.modalData.status = false;
                this.nextStatus = false;
            } else {
                this.$hMessage.error('数据清理失败!');
                this.modalData.status = false;
                this.nextStatus = false;
            }
        }
    },
    components: { aButton }
});
</script>
<style lang="less" scoped>
.title {
    color: #666b79;
    font-size: 14px;
    font-weight: 600;
    position: absolute;
    top: 55px;
    left: 20px;
}

.confirm-data {
    font-size: var(--font-size);

    p {
        margin: 10px;
    }
}
</style>
