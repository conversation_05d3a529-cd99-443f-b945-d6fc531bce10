/*
 * @Description:
 * @Author: <PERSON><PERSON>
 * @Date: 2023-04-27 15:26:08
 * @LastEditTime: 2023-07-07 15:17:14
 * @LastEditors: <PERSON><PERSON>
 */
import './alarmList.less';
export default {
    name: 'alarmList',
    props: {
        total: {
            type: Number,
            default: 0
        },
        alarmList: {
            type: Array,
            default: []
        }
    },
    data() {
        return {
            current: 1,
            levelDesc: {
                INFO: '信息',
                WARN: '警告',
                ERROR: '错误'
            }
        };
    },
    mounted() {
    },
    methods: {
        handlePageChange(page) {
            this.$emit('alarmPageChange', page);
        }
    },
    render() {
        return <div class="alarm-wrapper" style="height: calc(100% - 20px);">
            <ul class="alarm-list">
                {
                    this.alarmList.map(item => {
                        const str = `${item.location.join()} ${item.source} ${item.message}`;
                        return <li>
                            <h-poptip
                                trigger="click"
                                title={`${item.time} (${this.levelDesc[item.level]})`}
                                customTransferClassName="pop-alarm"
                                placement="top-start"
                                transfer
                                positionFixed
                                width="400">
                                <div class="alarm-content">
                                    <span class="text-time" style="padding-right: 15px;">{item.time}</span>
                                    <span class="text-level">{this.levelDesc[item.level]}</span>
                                    <span class="text-location">{item.location.join()}</span>
                                    <span class="text-source">{item.source}</span>
                                    <span class="text-message">{item.message}</span>
                                </div>
                                <div slot="content" class="content-text">{str}</div>
                            </h-poptip>
                        </li>;
                    })
                }
            </ul>
            {
                this.total ? <h-page current={this.current} total={this.total} size="small" on-on-change={this.handlePageChange}></h-page>
                    : ''
            }
        </div>;
    }
};
