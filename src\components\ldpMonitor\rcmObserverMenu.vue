<template>
    <div class="context-menu">
        <h-select
            v-model="rcmId"
            widthAdaption
            :clearable="false"
            class="context-menu-type"
            style="margin-right: 8px; width: 120px;"
            @on-change="handleRcmChange">
            <h-option
                v-for="item in rcmList"
                :key="item.id"
                :value="item.id">{{ item.name }}</h-option>
        </h-select>

        <div v-if="topoType === 'all'" class="context-menu-form">
            <!--RCM主题选择-->
            <h-simple-select
                ref="topic"
                v-model="formValid.topics"
                placeholder="搜索主题"
                style="margin-right: 8px; width: 140px;"
                multiple
                filterable
                showTitle
                isCheckall
                widthAdaption
                @on-change="handleExchange">
                <h-select-block :data="showTopicList"></h-select-block>
            </h-simple-select>
            <!--RCM标签选择-->
            <h-select
                ref="tags"
                v-model="formValid.tags"
                style="margin-right: 8px; width: 120px;"
                :clearable="false"
                multiple
                placeholder="标签"
                showTitle
                isCheckall
                widthAdaption
                @on-change="handleExchange">
                <h-option
                    v-for="item in tagList"
                    :key="item"
                    :value="item">
                    {{ item }}
                </h-option>
            </h-select>
        </div>
        <div v-if="topoType === 'cluster'" class="context-menu-form">
            <!-- 按同步模式 -->
            <h-select v-model="formValid.syncMode" class="title-select" placeholder="同步模式" @on-change="handleExchange">
                <h-option v-for="item in asycModeList" :key="'asycModeList' + item.value" :value="item.value">
                    {{ item.label }}
                </h-option>
            </h-select>
            <!-- 按集群名称 -->
            <h-select ref="cluster" v-model="formValid.clusterName" style="margin: 0 8px;"
                class="title-select" widthAdaption :clearable="true" placeholder="集群名称" @on-change="handleExchange">
                <h-option v-for="item in appClustersList" :key="item" :value="item">
                    {{ item }}
                </h-option>
            </h-select>
        </div>

        <!--搜索上下文  注意：key为上下文节点id-->
        <h-simple-select
            ref="context"
            v-model="formValid.nodeIds"
            style="width: 150px;"
            filterable
            autoPlacement
            widthAdaption
            placeholder="搜索上下文"
            @on-change="handleExchange">
            <h-select-block :data="showCtxs" ></h-select-block>
        </h-simple-select>

        <div class="context-menu-line"></div>

        <!-- 上下游关系 & 集群同步关系 -->
        <h-select v-model="topoType" style="width: 120px;" :clearable="false" class="title-select"
            @on-change="handleTypeChange">
            <h-option value="all">上下游关系</h-option>
            <h-option value="cluster">集群同步关系</h-option>
        </h-select>
    </div>
</template>

<script>
import _ from 'lodash';
import { getRcmConfigList } from '@/api/productApi';
import { getRcmOverview, queryContextTags, queryTopics, queryContext } from '@/api/rcmApi';
import { isJSON } from '@/utils/utils';
export default {
    name: 'LdpRcmObserverMenu',
    props: {
        productId: {
            type: String,
            default: ''
        }
    },
    data() {
        return {
            asycModeList: [
                {
                    value: 0,
                    label: '状态机复制'
                },
                {
                    value: 1,
                    label: '消息复制'
                }
            ],
            showCtxs: [],
            rcmList: [],
            rcmId: '',
            topoType: 'all',
            formValid: {
                nodeIds: '',
                topics: [],
                tags: [],
                syncMode: ''
            },
            showTopicList: [],
            appClustersList: [],
            tagList: [],
            nodeList: [],
            edgesList: [],
            timer: null,
            loading: false,
            tagMap: {}
        };
    },
    methods: {
        async init(model) {
            this.clearData();
            // 设置上下文列表
            const list  = await this.getRcmConfigList();
            this.setRcmList(list);

            await this.getDynamicsParams();
            if (!model) {
                setTimeout(() => {
                    this.initDefaultTags();
                }, 500);
            }
        },
        async getDynamicsParams() {
            // 获取主题列表
            const topics = await this.queryTopics();
            if (this.formValid.topics.length) {
                this.formValid.topics.forEach(item => {
                    if (!_.find(topics, { value: item })) {
                        topics.push({
                            label: item,
                            value: item
                        });
                    }
                });
            }
            this.setShowTopicList(topics);

            // 获取标签列表
            this.tagList = await this.queryContextTags();

            // 获取集群列表
            this.appClustersList = await this.getContextClusters();

            // 获取rcm列表
            this.showCtxs = await this.queryRcmContext();
        },
        clearData() {
            this.formValid = {
                topics: [],
                tags: [],
                nodeIds: '',
                syncMode: ''
            };
            this.nodeList = [];
            this.edgesList = [];
            this.tagList = [];
            this.showTopicList = [];
            this.rcmList = [];
            this.rcmId = '';
            this.showCtxs = [];
        },
        /**
         * 初始化默认标签
         */
        initDefaultTags() {
            const cache = localStorage.getItem('__rcm_tags_map__');
            const config = isJSON(cache) ? JSON.parse(cache) : {};
            this.tagMap = config[this.productId] || {};
            if (config[this.productId]?.[this.rcmId]) {
                const tags = config[this.productId][this.rcmId] || [];
                this.formValid.tags = tags.filter(item => this.tagList.find(tag => tag === item));
            }
        },
        /**
         * 从外部设置rcm节点下拉列表
         */
        setRcmList(list) {
            this.rcmList = list;
            if (this.rcmList?.[0]) {
                this.rcmId = this.rcmId ? this.rcmId : this.rcmList?.[0]?.id;
                this.$emit('handleRcmChange', this.rcmId, list);
            }
        },
        /**
         * 从外部设置setTagList
         */
        setTagList(list) {
            this.tagList = list;
        },
        /**
         * 从外部设置主题下拉列表
         */
        setShowTopicList(list) {
            this.showTopicList = list;
        },
        /**
         * 从外部设置集群下拉列表
         */
        setAppClustersList(list) {
            this.appClustersList = list;
        },
        // 获取rcm产品实例总览基础信息
        async getRcmOverview() {
            const param = {
                id: this.rcmId
            };
            const res = await getRcmOverview(param);
            if (res.code === '200' && res.data?.singletonContextStatistics) {
                return (res.data?.singletonContextStatistics?.contextNum || 0) + (res.data?.clusterContextStatistics?.contextNum || 0);
            }
            return 0;
        },
        // 切换topo类型
        async handleTypeChange(val) {
            this.formValid = {
                topics: [],
                tags: [],
                nodeIds: '',
                syncMode: ''
            };
            this.formValid.syncMode = val === 'cluster' ? 1 : '';
            this.onQuery(this.formValid);
        },
        // 切换Rcm节点
        async handleRcmChange(v) {
            this.formValid = {
                topics: [],
                tags: [],
                nodeIds: '',
                syncMode: ''
            };
            await this.getDynamicsParams();
            this.$emit('handleRcmChange', v, this.rcmList);
            this.onQuery();
        },
        /**
         * 更新默认tags
         */
        updateTags(tagMap = this.tagMap, triggerQuery) {
            this.tagMap = tagMap;
            if (!tagMap[this.rcmId]) return;
            this.formValid.tags = tagMap[this.rcmId];
            triggerQuery && this.onQuery();
        },
        /**
         * 触发搜索
         */
        onQuery(arg = {}) {
            if (!this.rcmId) return;
            const query = {
                ...this.formValid,
                rcmId: this.rcmId,
                ...arg,
                topoType: this.topoType
            };
            if (this.topoType === 'all') {
                // 上下游模式
                query.clusterName = '';
                query.syncMode = '';
                if (query.tags?.length) {
                    // 检查标签是否存在
                    query.tags = query.tags.filter(item => this.tagList.find(tag => tag === item));
                }
            } else {
                // 集群同步模式
                query.topics = [];
                query.tags = [];
            }

            this.$emit('changeParam', query);
        },
        // 跳转RCM拓扑重置参数-上下游关系
        resetUpDownFormVal() {
            this.topoType = 'all';
            this.formValid.syncMode = '';
            this.formValid.clusterName = '';
        },
        // 跳转RCM拓扑重置参数-集群间关系
        resetInclusterFormVal() {
            this.topoType = 'cluster';
            this.formValid.topics = [];
            this.formValid.tags = [];
        },
        /**
         * 设置查询参数
         */
        async setQueryParam(arg = {}, type) {
            if (type === 'linkType') {
                await this.init('setQuery');
            }
            if ('rcmId' in arg) {
                this.rcmId = arg.rcmId;
            }
            if ('nodeIds' in arg) {
                this.formValid.nodeIds = arg.nodeIds;
            }

            // 上下游关系跳转
            if (arg.topologyView === 'UpstreamDownstream') {
                // 上下游关系条件下重置参数(前提)
                this.resetUpDownFormVal();
                // 判断topic是否存在在列表中
                const topics = arg?.topics || [];
                const list = await this.queryTopics();
                topics.forEach(item => {
                    if (!_.find(list, { value: item })) {
                        list.push({
                            label: item,
                            value: item
                        });
                    }
                });
                this.setShowTopicList(list);
                // 设置下钻主题及节点
                this.formValid.topics = arg.topics;
            } else if (arg.topologyView === 'inCluster') {
                // 集群间关系-重置参数
                this.resetInclusterFormVal();
                this.$nextTick(() => {
                    this.formValid.syncMode = '';
                    // 设置下钻搜索集群名
                    this.formValid.clusterName = arg.clusterName;
                });
            }

            setTimeout(async () => {
                this.onQuery(arg);
            }, 500);
        },
        // 切换查询参数
        handleExchange() {
            this.getRcmTopology();
        },
        // 手动搜索上下文
        handleCtxchange() {
            this.onQuery();
        },
        // 获取RCM列表
        async getRcmConfigList() {
            const res = await getRcmConfigList({
                productId: this.productId
            });
            return res?.data || [];
        },
        async queryContextTags() {
            const res = await queryContextTags({
                rcmId: this.rcmId
            });
            return res?.data?.tags || [];
        },
        // 获取应用连接关系
        async getRcmTopology() {
            if (!this.rcmId) return;
            const param = {
                productInstNo: this.productId,
                rcmId: this.rcmId
            };
            this.$nextTick(() => {
                if (this.topoType === 'all') {
                    param.topics = this.formValid?.topics || [];
                    param.tags = this.formValid?.tags || [];
                    param.topoType = 'all';
                } else if (this.topoType === 'cluster') {
                    param.clusterName = this.formValid?.clusterName;
                    param.syncMode = this.formValid?.syncMode;
                    param.topoType = 'cluster';
                }
                this.onQuery(param);
            });
        },
        // 获取topic列表
        async queryTopics() {
            const res = await queryTopics({
                rcmId: this.rcmId,
                page: 1,
                pageSize: 10000
            });
            const sortedList = _.chain(res?.data?.list || [])
                .uniqBy('topic')
                .sortBy([(item) => item.topic.length, 'topic'])
                .value();
            const newList = sortedList.map(item => ({
                label: item.topic,
                value: item.topic
            }));
            return newList;
        },
        // 获取上下文集群列表
        async getContextClusters() {
            const param = {
                rcmId: this.rcmId,
                mode: 'cluster',
                page: 1,
                pageSize: 2 ** 31 - 1
            };
            const res = await queryContext(param);
            const clustersList = (res?.data?.list || [])?.map(item => {
                return item?.tierName;
            });
            return [...new Set(clustersList)];
        },
        // 获取上下文列表
        async queryRcmContext() {
            try {
                const param = {
                    rcmId: this.rcmId,
                    page: 1,
                    pageSize: 2 ** 31 - 1
                };
                this.loading = true;
                const res = await queryContext(param);
                this.loading = false;
                const list = res?.data?.list || [];
                const newList = list.map(item => ({
                    label: item.name,
                    value: item.name
                }));
                return newList;
            } catch (err) {
                this.loading = false;
                this.$hMessage.error(err.message);
            }
        }
    }
};
</script>
<style lang="less" scoped>
@import url("@/assets/css/simpleSelect.less");

.context-menu {
    display: flex;
    align-items: center;
    justify-content: space-between;

    &-line {
        background: #474e6f;
        width: 1px;
        height: 26px;
        margin: 0 8px;
    }

    &-form {
        display: flex;
        align-items: center;
    }

    &-relation {
        width: 120px;
    }

    /deep/ .h-select {
        height: 32px;
    }

    /deep/ .h-select-dropdown {
        input {
            color: #afafaf;
        }
    }
}
</style>
