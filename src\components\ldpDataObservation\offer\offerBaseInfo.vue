<template>
    <div class="tab-box">
        <a-loading v-if="loading"></a-loading>
        <div v-else>
            <!-- 报盘对象处理 -->
            <obs-table ref="table" :title="title" :tableData="tableData" showTitle :columns="columns" notSetWidth autoHeadWidth
                highlightRow rowSelectOnly :maxHeight="220" @select-change="selectChange" @on-current-change="tableRowcheckedChange"/>
             <!-- 报盘对象信息 -->
            <div v-if="tableData.length && Object.keys(currentRow).length" class="detail-grid-box">
                <obs-title :title="offerTitle" />
                <div class="detail-box">
                    <info-grid ref="offerDetail" :gridData="offerDetail" @select-change="selectChange"></info-grid>
                </div>
            </div>
        </div>
    </div>
</template>

<script>
import aLoading from '@/components/common/loading/aLoading';
import obsTitle from '@/components/common/title/obsTitle';
import infoGrid from '@/components/common/infoBar/infoGrid';
import obsTable from '@/components/common/obsTable/obsTable';
import { formatChartNumber, nsConvertTime, formatNumber, compareObjArr } from '@/utils/utils';
import { getManagerProxy } from '@/api/mcApi';
export default {
    props: {
        nodeData: {
            type: Object,
            default: () => { }
        },
        pollTime: {
            type: Number,
            default: 0
        }
    },
    components: { aLoading, obsTitle, obsTable, infoGrid },
    data() {
        const offerColumns = [
            { title: 'OfferIndex', key: 'OfferIndex', ellipsis: true },
            { title: 'OfferName', key: 'OfferName', ellipsis: true },
            { title: 'OfferType', key: 'OfferType', ellipsis: true },
            { title: 'UseThread', key: 'UseThread', ellipsis: true },
            { title: 'Started', key: 'Started', ellipsis: true },
            { title: 'TotalMsgExecNum', key: 'TotalMsgExecNum', ellipsis: true },
            { title: 'AveMsgTotalExecTimeNs', key: 'AveMsgTotalExecTimeNs', ellipsis: true }
        ];
        return {
            loading: true,
            // 报盘对象处理
            title: {
                label: '报盘对象处理',
                slots: [
                    {
                        key: 'topSelect',
                        type: 'select',
                        defaultValue: '30',
                        options: [
                            {
                                value: '10',
                                label: 'Top10'
                            },
                            {
                                value: '30',
                                label: 'Top30'
                            },
                            {
                                value: '50',
                                label: 'Top50'
                            }
                        ]
                    }
                ]
            },
            AllTableData: [],
            tableData: [],
            columns: offerColumns,
            currentRow: {},
            // 报盘对象信息
            offerTitle: {
                label: {
                    labelDic: [
                        {
                            key: 'OfferName',
                            label: '报盘对象'
                        }
                    ],
                    labelInfo: {
                        OfferName: '-'
                    }
                },
                slots: []
            },
            // Redo处理性能
            offerDetail: {
                layout: [
                    { x: 0, y: 0, w: 4, h: 13, i: '1' },
                    { x: 4, y: 0, w: 8, h: 13, i: '2' },
                    { x: 0, y: 1, w: 4, h: 13, i: '3' },
                    { x: 4, y: 1, w: 8, h: 13, i: '4' }
                ],
                details: [
                    {
                        type: 'obj',
                        title: '报盘消息处理统计',
                        infoDic: [
                            {
                                label: 'TotalMsgExecNum',
                                key: 'TotalMsgExecNum'
                            },
                            {
                                label: 'OnMsgExecNum',
                                key: 'OnMsgExecNum'
                            },
                            {
                                label: 'OnLpcExecNum',
                                key: 'OnLpcExecNum'
                            }
                        ],
                        info: {
                            TotalMsgExecNum: '-',
                            OnMsgExecNum: '-',
                            OnLpcExecNum: '-'
                        }
                    },
                    {
                        type: 'chart',
                        info: {
                            basicOpiton: {
                                chartType: 'axis',
                                lineDeploy: [
                                    {
                                        name: 'TotalMsgExecNum',
                                        type: 'line'
                                    },
                                    {
                                        name: 'OnMsgExecNum',
                                        type: 'line'
                                    },
                                    {
                                        name: 'OnLpcExecNum',
                                        type: 'line'
                                    }
                                ],
                                yAxiDeploy: [
                                    {
                                        name: '个',
                                        axisLabel: {
                                            formatter: formatChartNumber
                                        }
                                    }
                                ]
                            },
                            additionalOpiton: {
                                backgroundColor: '#2d334c'
                            },
                            chartData: {
                                xData: [],
                                data: {
                                    TotalMsgExecNum: [],
                                    OnMsgExecNum: [],
                                    OnLpcExecNum: []
                                }
                            }
                        }
                    },
                    {
                        type: 'obj',
                        title: {
                            label: '报盘消息处理时延',
                            slots: [
                                {
                                    key: 'offerSelect',
                                    type: 'select',
                                    minWidth: '120px',
                                    defaultValue: 'Ave',
                                    options: [
                                        {
                                            value: 'Ave',
                                            label: 'Ave'
                                        },
                                        {
                                            value: 'Min',
                                            label: 'Min'
                                        },
                                        {
                                            value: 'Max',
                                            label: 'Max'
                                        },
                                        {
                                            value: 'Last',
                                            label: 'Last'
                                        }
                                    ]
                                }
                            ]
                        },
                        infoDic: [],
                        info: {}
                    },
                    {
                        type: 'chart',
                        info: {
                            basicOpiton: {
                                chartType: 'axis',
                                lineDeploy: [
                                    {
                                        name: 'MsgTotalExecTimeNs',
                                        type: 'line',
                                        tooltip: {
                                            valueFormatter: nsConvertTime
                                        }
                                    },
                                    {
                                        name: 'MsgBizExecTimeNs',
                                        type: 'line',
                                        tooltip: {
                                            valueFormatter: nsConvertTime
                                        }
                                    },
                                    {
                                        name: 'MsgLockBizExecTimeNs',
                                        type: 'line',
                                        tooltip: {
                                            valueFormatter: nsConvertTime
                                        }
                                    },
                                    {
                                        name: 'MsgFrameExecTimeNs',
                                        type: 'line',
                                        tooltip: {
                                            valueFormatter: nsConvertTime
                                        }
                                    }
                                ],
                                yAxiDeploy: [
                                    {
                                        name: '时延',
                                        axisLabel: {
                                            formatter: nsConvertTime
                                        }
                                    }
                                ]
                            },
                            additionalOpiton: {
                                backgroundColor: '#2d334c'
                            },
                            chartData: {
                                xData: [],
                                data: {
                                    MsgTotalExecTimeNs: [],
                                    MsgBizExecTimeNs: [],
                                    MsgLockBizExecTimeNs: [],
                                    MsgFrameExecTimeNs: []
                                }
                            }
                        }
                    }
                ]
            },
            selectTopValue: '30',
            offerSelectValue: 'Ave'
        };
    },
    methods: {
        async initData() {
            this.loading = true;
            this.currentRow = {};
            this.cleanOfferData();
            this.cleanOfferDetail();
            await this.getFileData();
            this.loading = false;
        },
        // 表格下拉框切换
        selectChange(value, key) {
            if (key === 'topSelect'){
                this.selectTopValue = value;
                this.tableData = [...this.AllTableData]?.sort(compareObjArr('AveMsgTotalExecTimeNs'))?.slice(0, this.selectTopValue) || [];
                const row = this.tableData.find(v => v?.OfferIndex === this.currentRow?.OfferIndex);
                if (!row){
                    this.cleanOfferData();
                    this.cleanOfferDetail();
                    this.getFileData();
                }
            } else if (key === 'offerSelect'){
                this.offerSelectValue = value;
                this.cleanOfferDetail();
                this.getFileData();
            }
        },
        // 表格切换选中行
        tableRowcheckedChange(currentRow) {
            if (this.currentRow?.OfferIndex !== currentRow?.OfferIndex){
                this.cleanOfferData();
                this.cleanOfferDetail();
            }
            this.currentRow = currentRow;
            this.getFileData();
        },
        // 表格选中行显示处理性能信息
        setOfferTitleData(currentRow, newTime){
            this.offerTitle.label.labelInfo.OfferName = currentRow?.OfferName;
            this.setReqDetail(newTime);
        },
        // 响应数据清理 -- 清理数据
        cleanOfferData(){
            this.offerTitle.label.labelInfo.OfferName = '-';
            this.offerDetail.details[0].info = {};
            this.offerDetail.details[1].info.chartData.xData = [];
            this.offerDetail.details[1].info.chartData.data.TotalMsgExecNum = [];
            this.offerDetail.details[1].info.chartData.data.OnMsgExecNum = [];
            this.offerDetail.details[1].info.chartData.data.OnLpcExecNum = [];
        },
        // redo处理性能
        setReqDetail(newTime){
            this.offerDetail.details[0].info = {
                TotalMsgExecNum: formatNumber(this.currentRow?.TotalMsgExecNum),
                OnMsgExecNum: formatNumber(this.currentRow?.OnMsgExecNum),
                OnLpcExecNum: formatNumber(this.currentRow?.OnLpcExecNum)
            };
            this.offerDetail.details[2].info = { ...this.currentRow };

            const yData = {
                TotalMsgExecNum: this.currentRow?.TotalMsgExecNum,
                OnMsgExecNum: this.currentRow?.OnMsgExecNum,
                OnLpcExecNum: this.currentRow?.OnLpcExecNum,
                MsgTotalExecTimeNs: this.currentRow?.[this.offerSelectValue + 'MsgTotalExecTimeNs'],
                MsgBizExecTimeNs: this.currentRow?.[this.offerSelectValue + 'MsgBizExecTimeNs'],
                MsgLockBizExecTimeNs: this.currentRow?.[this.offerSelectValue + 'MsgLockBizExecTimeNs'],
                MsgFrameExecTimeNs: this.currentRow?.[this.offerSelectValue + 'MsgFrameExecTimeNs']
            };
            const charDataList = [
                this.offerDetail.details[1].info.chartData,
                this.offerDetail.details[3].info.chartData
            ];
            this.setChartData(charDataList, yData, newTime);
        },
        // redo处理性能 -- 清理数据
        cleanOfferDetail(){
            this.offerDetail.details[2].infoDic = this.setOfferInfoDic(this.offerSelectValue);
            this.offerDetail.details[2].info = {};
            this.offerDetail.details[3].info.chartData.xData = [];
            this.offerDetail.details[3].info.chartData.data.MsgTotalExecTimeNs = [];
            this.offerDetail.details[3].info.chartData.data.MsgBizExecTimeNs = [];
            this.offerDetail.details[3].info.chartData.data.MsgLockBizExecTimeNs = [];
            this.offerDetail.details[3].info.chartData.data.MsgFrameExecTimeNs = [];
        },
        // infoDic值
        setOfferInfoDic(val){
            const infoDic = [
                { key: val + 'MsgTotalExecTimeNs', label: val + 'MsgTotalExecTimeNs' },
                { key: val + 'MsgBizExecTimeNs', label: val + 'MsgBizExecTimeNs' },
                { key: val + 'MsgLockBizExecTimeNs', label: val + 'MsgLockBizExecTimeNs' },
                { key: val + 'MsgFrameExecTimeNs', label: val + 'MsgFrameExecTimeNs' }
            ];
            return infoDic;
        },
        // chart时间轴
        setChartTime(){
            const charDataList = [
                this.offerDetail.details[1].info.chartData,
                this.offerDetail.details[3].info.chartData
            ];
            const newTime = this.$getCurrentLocalTime();
            charDataList.forEach(chartData => {
                if (chartData.xData.length > (5 * 60 / this.pollTime)) {
                    chartData.xData.shift();
                    Object.values(chartData.data).forEach(item => { item.shift(); });
                }
                chartData.xData.indexOf(newTime) === -1 && chartData.xData.push(newTime);
            });
            return newTime;
        },
        // chart数据
        setChartData(chartDataList, data, newTime){
            chartDataList.forEach(chartData => {
                const index = chartData.xData.indexOf(newTime);
                Object.keys(chartData.data).forEach(item => {
                    this.$set(chartData.data[item], index, data?.[item]);
                });
            });
        },
        // 构造页面数据
        async getFileData() {
            // chart时间轴
            const newTime = this.setChartTime();
            const { offerBaseInfo } = await this.getAPi();
            // 表格数据
            this.AllTableData = [...offerBaseInfo];
            this.tableData = [...this.AllTableData]?.sort(compareObjArr('AveMsgTotalExecTimeNs'))?.slice(0, this.selectTopValue);
            // redo处理性能  网络应答性能
            if (this.tableData?.length){
                const row = this.tableData.find(v => v?.OfferIndex === this.currentRow?.OfferIndex);
                this.currentRow = Object.keys(this.currentRow).length && row ? row : this.tableData?.[0];
                this.tableData.forEach(item => {
                    item._highlight = item?.OfferIndex === this.currentRow?.OfferIndex;
                });
                this.setOfferTitleData(this.currentRow, newTime);
            }
        },
        // 接口请求
        async getAPi() {
            const data = {
                offerBaseInfo: []
            };
            const param = [
                {
                    manageProxyIp: this.nodeData.ip,
                    manageProxyPort: this.nodeData.manageProxyPort,
                    pluginName: 'ldp_offerproc',
                    funcName: 'GetOfferBaseInfo'
                }
            ];
            try {
                const res = await getManagerProxy(JSON.stringify(param));
                if (res.code === '200') {
                    res.data?.[0]?.OfferBaseInfo?.length && (data.offerBaseInfo = res.data[0].OfferBaseInfo);
                }
                return data;
            } catch (err) {
                this.$emit('clear');
            }
        }
    }
};
</script>

<style lang="less" scoped>
.tab-box {
    .detail-grid-box {
        & > .obs-title {
            background: var(--wrapper-color);
            margin-top: 15px;
        }
    }

    .detail-box {
        /deep/ .obs-title {
            padding: 0 0 0 10px;

            .title-text {
                display: inline-block;
                color: var(--font-opacity-color);
                line-height: 42px;
                margin-left: 0;
                font-size: 12px;
                font-size: var(--font-size);
                padding: 0 5px;
                overflow: hidden;
                white-space: nowrap;
                text-overflow: ellipsis;
            }

            &::before {
                display: none;
            }
        }
    }
}
</style>
