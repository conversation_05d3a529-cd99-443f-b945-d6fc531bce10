<template>
    <div ref="tab-box" class="tab-box">
        <a-loading v-if="loading"></a-loading>
        <div v-else>
            <a-title title="性能分析开关" style="margin-top: 12px;">
                <slot>
                    <h-radio-group v-model="performanceStu" style="margin-left: 10px;" @on-click="handleSelectChange">
                        <h-radio :label="0">
                            <span>关闭</span>
                        </h-radio>
                        <h-radio :label="1">
                            <span>开启 (正常级别)</span>
                        </h-radio>
                        <h-radio :label="2">
                            <span>开启 (基本级别)</span>
                        </h-radio>
                    </h-radio-group>
                    <span style="position: absolute;">
                        <h-poptip customTransferClassName="apm-poptip" placement="bottom-end" :transfer="true" width="310">
                            <h-icon name="feedback_fill" size="24" color="#999" style="cursor: pointer; margin-left: 6px;"></h-icon>
                            <pre slot="content" style="color: #fff;">{{`1.性能分析会消耗平台性能，不建议长期开启，请在\n需要进行接口性能分析时打开开关，不用时请及时关闭\n2.正常级别：开启后会分析MDB执行数据操作时所有接\n口的执行性能\n3.基本级别：开启后仅分析MDB执行数据操作时API\n级别接口的执行性能`}}</pre>
                        </h-poptip>
                    </span>
                    <a-button type="dark" :loading="btnLoading" style="position: absolute; top: 5px; right: 6px;" @click="getFileData">刷新</a-button>
                </slot>
            </a-title>
            <br />
            <!-- 功能号处理详情 -->
            <obs-table ref="table" :title="title" :tableData="tableData" :columns="columns" notSetWidth autoHeadWidth highlightRow rowSelectOnly :height="tableHeight"
                @select-change="selectChange" />
        </div>
    </div>
</template>

<script>
import aTitle from '@/components/common/title/aTitle';
import aButton from '@/components/common/button/aButton';
import aLoading from '@/components/common/loading/aLoading';
import obsTable from '@/components/common/obsTable/obsTable';
import { getManagerProxy } from '@/api/mcApi';
export default {
    name: 'Performance',
    components: { aLoading, aButton, aTitle, obsTable },
    props: {
        nodeData: {
            type: Object,
            default: () => { }
        }
    },
    data() {
        return {
            loading: false,
            performanceStu: '',
            btnLoading: false,
            tableHeight: 0,
            methodData: {},
            title: {
                label: '执行详情',
                slots: [
                    {
                        key: 'methodSelect',
                        type: 'select',
                        defaultValue: '',
                        options: []
                    }
                ]
            },
            tableData: [],
            columns: [
                { key: 'Id', title: '事务控制器ID', width: 120 },
                { key: 'Count', title: '执行次数', sortable: true },
                { key: 'AveTime', title: '平均耗时', sortable: true },
                { key: 'Min', title: '最小耗时', sortable: true },
                { key: 'TopTimes', title: '前十最大耗时', width: 120 },
                { key: 'Stddev', title: '标准差', sortable: true },
                { key: 'p10', title: 'p10', sortable: true },
                { key: 'p50', title: 'p50', sortable: true },
                { key: 'p90', title: 'p90', sortable: true },
                { key: 'p95', title: 'p95', sortable: true },
                { key: 'p99', title: 'p99', sortable: true }
            ]
        };
    },
    mounted() {
        window.addEventListener('resize', this.fetTableHeight);
    },
    beforeDestroy() {
        window.removeEventListener('resize', this.fetTableHeight);
    },
    methods: {
        async initData() {
            this.loading = true;
            this.fetTableHeight();
            this.title.slots[0].options = [];
            this.title.slots[0].defaultValue = '';
            this.performanceStu = '';
            await this.getFileData();
            this.loading = false;
        },
        // 构造页面数据
        async getFileData() {
            this.btnLoading = true;
            try {
                const { getMdbInfo, getMdbTraceInfo } = await this.getAPi();
                if (Object.keys(getMdbInfo).length) {
                    this.performanceStu = getMdbInfo?.Trace;
                }
                this.methodData = {};
                this.title.slots[0].options = [];
                if (getMdbTraceInfo.Data) {
                    if (Array.isArray(getMdbTraceInfo.Data.tcs)) {
                        getMdbTraceInfo.Data.tcs.forEach(item => {
                            if (Array.isArray(item.trace)) {
                                item.trace.forEach(ele => {
                                    if (!this.methodData[ele.Method]) {
                                        this.methodData[ele.Method] = [];
                                    }
                                    ele.Id = item.Id;
                                    this.methodData[ele.Method].push(ele);
                                });
                            }
                        });
                    }
                    // 筛选所有接口
                    Object.keys(this.methodData).forEach(ele => {
                        this.title.slots[0].options.push({
                            label: ele,
                            value: ele
                        });
                    });
                    if (!this.title.slots[0].defaultValue) {
                        const val = this.title.slots[0].options[0]?.value;
                        this.title.slots[0].defaultValue = val;
                    }
                    this.$refs['table'] && this.$refs['table'].setSelectVal('methodSelect', this.title.slots[0].defaultValue);
                }
                this.handleTableData();
            } catch (error) {
                this.$emit('clear');
            }
            this.btnLoading = false;
        },
        // 设置table高度
        fetTableHeight() {
            this.$nextTick(() => {
                this.tableHeight = this.$refs['tab-box']?.offsetHeight - 130;
            });

        },
        // 接口请求
        async getAPi() {
            const data = {
                getMdbInfo: {},
                getMdbTraceInfo: {}
            };
            const param = [
                {
                    manageProxyIp: this.nodeData.ip,
                    manageProxyPort: this.nodeData.manageProxyPort,
                    pluginName: 'ldp_bizproc',
                    funcName: 'uftmdb_data_GetUftmdbCtrlInfo'
                },
                {
                    manageProxyIp: this.nodeData.manageProxyIp,
                    manageProxyPort: this.nodeData.manageProxyPort,
                    pluginName: 'ldp_bizproc',
                    funcName: 'uftmdb_data_GetMdbTraceInfo'
                }];
            const res = await getManagerProxy(JSON.stringify(param));
            if (res.code === '200') {
                !res.data?.[0]?.ErrorNo && Object.keys(res.data?.[0]).length && (data.getMdbInfo = res.data[0]);
                !res.data?.[1]?.ErrorNo && (data.getMdbTraceInfo = res.data[1]);
                return data;
            }
        },
        // 表格数据处理
        handleTableData() {
            const val = this.title.slots[0].defaultValue;
            this.tableData = this.methodData[val] || [];
        },
        // 表格下拉框切换
        selectChange(value) {
            this.tableData = this.methodData[value];
        },
        handleSelectChange() {
            setTimeout(() => {
                getManagerProxy(JSON.stringify([{
                    manageProxyIp: this.nodeData.ip,
                    manageProxyPort: this.nodeData.manageProxyPort,
                    pluginName: 'ldp_bizproc',
                    funcName: 'uftmdb_data_SetMdbTrace',
                    params: {
                        Trace: this.performanceStu.toString()
                    }
                }]));
            }, 100);
        }
    }
};
</script>

<style lang="less" scoped>
.tab-box {
    height: calc(100% - 20px);

    .obs-table {
        margin: 0;
    }
}
</style>
