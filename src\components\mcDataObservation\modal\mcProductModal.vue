<template>
    <div>
        <!-- 下发配置列表 -->
        <h-msg-box-safe v-model="modalData.status" :mask-closable="false" :title="modalData.title" width="700" maxHeight="350">
            <h-form ref="formValidate" :model="formValidate" :label-width="100">
                <h-form-item label="产品类型：" prop="type">
                    <span>消息中心3.0</span>
                </h-form-item>
                <h-form-item label="产品名称：" prop="name">
                    <span>{{ formValidate.name }}</span>
                </h-form-item>
                <h-form-item label="产品版本：" prop="version" :validRules="stringRule">
                    <h-input v-model.trim="formValidate.version" placeholder="请输入产品版本"></h-input>
                </h-form-item>
                <h-form-item label="服务管理地址: " prop="serveManageAddress" required :validRules="mcAddressRule">
                    <h-input v-model="formValidate.serveManageAddress" placeholder="请输入MC集群地址"></h-input>
                </h-form-item>
                <p class="form-tip">使用IP:PORT形式，多个地址使用英文逗号分隔</p>
            </h-form>

            <template v-slot:footer>
                <a-button @click="modalData.status = false">取消</a-button>
                <a-button type="primary" :loading="loading" @click="submitConfig">确认</a-button>
            </template>
        </h-msg-box-safe>
    </div>
</template>

<script>
import { stringLengthRule, multipleAddressRule } from '@/utils/validate';
import { setMcProductInstance } from '@/api/mcApi';
import aButton from '@/components/common/button/aButton';
export default {
    props: {
        modalInfo: {
            type: Object,
            default: null
        }
    },
    data() {
        const mcAddrRule = function (_rule, values, callback) {
            if (values.length) {
                const list = values.split(',');
                list.forEach((item, index) => {
                    const listBackups = [...list];
                    listBackups.splice(index, 1);
                    if (listBackups.indexOf(item) > -1) {
                        return callback(new Error(`${item}重复`));
                    }
                });
            }
            callback();
        };
        return {
            modalData: this.modalInfo,
            loading: false,
            stringRule: [{ test: stringLengthRule(30), trigger: 'change, blur' }],
            mcAddressRule: [{ test: multipleAddressRule, trigger: 'change, blur' }, { test: mcAddrRule, trigger: 'change, blur' }],
            loadingStatus: 1,
            formValidate: {
                type: '消息中心3.0',
                name: this.modalInfo.name,
                version: this.modalInfo.version,
                serveManageAddress: this.modalInfo.serveManageAddress
            }
        };
    },
    methods: {
        async submitConfig() {
            this.$refs['formValidate'].validate(async (valid) => {
                if (valid) {
                    this.loading = true;
                    try {
                        const param = {
                            id: this.modalInfo.id,
                            name: this.formValidate.name,
                            type: this.formValidate.type,
                            version: this.formValidate.version,
                            serveManageAddress: this.formValidate.serveManageAddress
                        };
                        const res = await setMcProductInstance(param);
                        if (res.code === '200') {
                            if (this.modalData.title === '创建产品节点'){
                                this.$emit('reload'); // 初始化产品节点
                                this.$hMessage.success('添加成功!');
                            } else if (this.modalData.title === '修改产品节点'){
                                this.$emit('reload', { ...param });
                                this.$hMessage.success('修改成功!');
                            }
                            this.modalInfo.status = false;
                        } else {
                            this.loading = false;
                        }
                    } catch (err) {
                        this.loading = false;
                    }
                }
            });
        }
    },
    components: {  aButton }
};
</script>

<style lang="less" scoped>
    .form-tip {
        color: var(--font-opacity-color);
        margin: -20px 0 20px 100px;
    }

</style>
