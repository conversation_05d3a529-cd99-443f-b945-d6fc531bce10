<template>
  <div :key="tkey" class="container">
    <div class="container-search">
        <h-input
            v-model.trim="searchText"
            placeholder="请输入"
            :maxlength="30"
            icon="android-search"
            style="display: inline-block;"
            clearable
            @on-enter="fuzzySearch"
            @on-blur="fuzzySearch"
            @on-click="fuzzySearch"
        ></h-input>
        <a-button
            type="dark"
            icon="plus-round"
            style="width: 32px; height: 32px; padding: 6px 10px 6px 9px;"
            @click="addItem"
        ></a-button>
    </div>
    <!-- 用例列表 -->
    <ul v-if="items.length">
        <li
            v-for="(item, index) in items"
            :key="item.id"
            :class="menuId === item.id ? 'menu-selected' : ''"
            @click="(e)=>checkMenu(e,item)">
            <div class="item">
                <template v-if="item.editing || item.adding">
                    <h-input
                        ref="input"
                        v-model="newItemName"
                        placeholder="请输入用例名"
                        class="new-item-input"
                    />
                    <h-icon
                        class="icon-setting"
                        name="checkmark-round"
                        size="14"
                        color="var(--link-color)"
                        @on-click="(e)=>saveItem(e, item)"
                    ></h-icon
                    >&nbsp;&nbsp;
                    <h-icon
                        class="icon-setting"
                        name="close-round"
                        size="14"
                        color="var(--error-color)"
                        @on-click="(e)=>cancelEdit(e,index)"
                    ></h-icon>
                </template>
                <template v-else>
                    <span :title="item.label">
                        {{ item.label }}
                    </span>
                    <h-icon
                        class="icon-setting"
                        name="brush"
                        size="14"
                        @on-click="(e)=>editItem(e,index)"
                    ></h-icon>&nbsp;&nbsp;
                    <h-icon
                        class="icon-setting"
                        name="t-b-delete"
                        color="var(--error-color)"
                        size="14"
                        @on-click="(e)=>deleteItem(e, item)"
                    ></h-icon>
                </template>
            </div>
        </li>
    </ul>
    <p v-else style="padding: 10px; text-align: center;">无内容</p>
  </div>
</template>

<script>
import aButton from '@/components/common/button/aButton';
export default {
    name: 'EditableList',
    components: { aButton },
    props: {
        menuList: {
            type: Array,
            default: () => []
        },
        menuId: {
            type: String,
            default: ''
        }
    },
    data() {
        return {
            tkey: 1,
            items: [],
            newItemName: '',
            searchText: ''
        };
    },
    mounted() {
        this.init();
    },
    methods: {
        init() {
            this.tkey++;
            this.searchText = '';
            this.fuzzySearch();
        },
        // 返回是否存在正在新增或编辑项
        hasOperateItem() {
            return this.items.some(item => item?.adding || item?.editing);
        },
        // 菜单模糊搜索
        fuzzySearch(_e) {
            this.items = this.searchText
                ? this.menuList.filter(
                    (item) => item.label.indexOf(this.searchText) !== -1
                )
                : [...this.menuList];
        },
        addItem() {
            if (this.hasOperateItem()) return;
            this.newItemName = '';
            this.items.unshift({
                label: '',
                adding: true
            });

            this.$nextTick(() => {
                if (this.$refs['input']?.[0]) {
                    this.$refs['input'][0].focus();
                }
            });
        },
        editItem(e, index) {
            if (this.hasOperateItem()) return;
            e.preventDefault();
            e.stopPropagation();
            this.newItemName = this.items[index].label;
            this.items[index].editing = true;
        },
        // 保存
        saveItem(e, item) {
            if (!this.newItemName.trim()) {
                return this.$hMessage.error('用例名不能为空');
            } else if (this.newItemName?.length > 64) {
                return this.$hMessage.error('用例名长度不能超过64');
            }
            e.preventDefault();
            e.stopPropagation();
            this.$emit('save', item, this.newItemName);
        },
        // 删除
        deleteItem(e, item) {
            e.preventDefault();
            e.stopPropagation();
            this.$emit('delete', item);
        },
        // 取消-编辑s
        cancelEdit(e, index) {
            e.preventDefault();
            e.stopPropagation();
            if (this.items[index].adding) {
                this.items.splice(index, 1);
            } else {
                this.items[index].editing = false;
            }
            this.newItemName = '';
        },
        // 切换
        checkMenu(e, item) {
            if (this.hasOperateItem()) return;
            e.preventDefault();
            e.stopPropagation();
            this.$emit('check-menu', item);
        }
    }
};
</script>

<style  lang="less" scoped>
@import url("@/assets/css/input.less");

.container {
    height: 100%;
    display: block;
    overflow: hidden;

    .container-search {
        display: flex;
        gap: 8px;
        margin: 10px 10px 0;

        .h-btn {
            padding: 6px 12px;
        }
    }

    ul {
        width: 100%;
        max-height: calc(100% - 52px);
        margin-top: 10px;
        overflow: auto;

        li {
            position: relative;
            overflow: hidden;
            width: 100%;
            height: 33px;
            color: var(--font-color);
            font-size: var(--font-size-base);
            line-height: 33px;
            padding: 0 5px 0 15px;
            white-space: nowrap;
            text-overflow: ellipsis;
            cursor: pointer;

            &:hover {
                background: var(--link-opacity-color);

                &::after {
                    position: absolute;
                    top: 0;
                    left: 0;
                    content: "";
                    width: 4px;
                    height: 33px;
                    background: var(--link-color);
                }
            }

            .item {
                display: flex;
                align-items: center;
                width: 100%;
                color: var(--font-color);

                span {
                    width: 80%;
                    overflow: hidden;
                    text-overflow: ellipsis;
                    white-space: nowrap;
                }

                .new-item-input {
                    width: 85%;
                    margin: 0 8px 0 0;
                }

                .icon-setting {
                    cursor: pointer;

                    &:hover {
                        color: var(--link-color);
                    }
                }
            }
        }

        .menu-selected {
            background: var(--link-opacity-color);

            &::after {
                position: absolute;
                left: 0;
                top: 0;
                content: "";
                width: 4px;
                height: 33px;
                background: var(--link-color);
            }
        }
    }
}

</style>
