<template>
    <div>
        <h-msg-box-safe v-model="modalData.status" :mask-closable="false" :title="modalData.title" width="600" maxHeight="550"
            @on-open="getCollections" >
            <h-spin v-if="dataLoading" fix>
                <h-icon name="load-c" size="18" class="demo-spin-icon-load"></h-icon>
                <div>加载中</div>
            </h-spin>
            <h-form ref="formValidate" :model="formValidate" :label-width="120" :rules="ruleValidator">
                <div>
                    <h-form-item label="已适配业务系统:" prop="adaptedBizSysType">
                        <h-select v-model="formValidate.adaptedBizSysType"  placeholder="请选择业务系统" :positionFixed="true"  :clearable="false" @on-change="handleSelectChange">
                            <h-option v-for="item in adaptedBizSysTypeDict" :key="item.code" :value="item.code">{{item.name}}</h-option>
                        </h-select>
                    </h-form-item>
                    <h-form-item label="KafKa地址:" prop="kafkaAddress" class='verify-tip-1'>
                        <h-input v-model="formValidate.kafkaAddress"></h-input>
                        <p>使用IP:PORT形式，多个地址使用英文逗号分隔</p>
                    </h-form-item>
                    <h-form-item label="Topic主题:" prop="kafkaTopics" class='verify-tip-1'>
                        <h-input v-model="formValidate.kafkaTopics" type="textarea" :rows="4"></h-input>
                        <p>一个主题占一行</p>
                    </h-form-item>
                    <h-form-item label="绑定产品节点:" prop="productId">
                        <h-select v-model="formValidate.productId"  placeholder="请选择产品节点"  transfer autoPlacement >
                            <h-option
                                v-for="item in productList"
                                :key="item.id"
                                :value="item.productInstNo"
                                >{{ item.productName }}</h-option
                            >
                        </h-select>
                    </h-form-item>
                </div>
            </h-form>
            <template v-slot:footer>
                <a-button @click="modalData.status = false">取消</a-button>
                <a-button type="primary" :loading="loading" @click="submitConfig">确定</a-button>
            </template>
        </h-msg-box-safe>
    </div>
</template>

<script>
import { mapState, mapActions } from 'vuex';
import aButton from '@/components/common/button/aButton';
import { multipleAddressRule } from '@/utils/validate';
export default {
    props: {
        modalInfo: {
            type: Object,
            default: null
        },
        adaptedBizSysTypeDict: {
            type: Array,
            default: () => []
        }
    },
    data() {
        const kafkaTopicsRule = function (_rule, values, callback) {
            if (values.length) {
                const list = values.trim().replaceAll('\n', '\r\n')?.split('\r\n');
                list.forEach((item, index) => {
                    const listBackups = [...list];
                    listBackups.splice(index, 1);
                    if (listBackups.indexOf(item) > -1) {
                        return callback(new Error(`主题名${item}重复`));
                    }
                });
                const res = values.trim().replaceAll('\n', '\r\n')?.split('\r\n')?.map(item => item.trim())?.filter(v => v === '');
                if (res.length) {
                    return callback(new Error('一个主题占一行'));
                }
            }
            callback();
        };
        return {
            dataLoading: false,
            serviceType: 'kafka',
            modalData: this.modalInfo,
            formValidate: {
                kafkaAddress: '',
                kafkaTopics: '',
                adaptedBizSysType: '',
                productId: ''
            },
            defaultConfigData: {
                'LDPSecu3.0': {
                    kafkaAddress: '127.0.0.1:9092',
                    kafkaTopics: 't_counter_order_tick_result,t_counter_tick_order_result,t_counter_insert_result,t_counter_insert_merge_result'
                },
                EFH: {
                    kafkaAddress: '127.0.0.1:9092',
                    kafkaTopics: 't_szse_mdgw_tcp_olap_delta,t_szse_fpga_tcp_olap_delta'
                }
            },
            loading: false,
            ruleValidator: {
                adaptedBizSysType: [{ required: true, message: '不能为空', trigger: 'change' }],
                kafkaAddress: [
                    { required: true, message: '不能为空', trigger: 'change' },
                    { validator: multipleAddressRule, trigger: 'change' }
                ],
                kafkaTopics: [
                    { required: true, message: '不能为空', trigger: 'change' },
                    { validator: kafkaTopicsRule, trigger: 'change' }
                ],
                productId: [{ required: true, message: '不能为空', trigger: 'change' }]
            },
            productList: [],
            isChange: true
        };
    },
    computed: {
        ...mapState({
            allProductList: state => {
                return state.product.productListLight || [];
            }
        })
    },
    methods: {
        ...mapActions({ getAllProductList: 'product/getProductListLight' }),
        async getCollections() {
            this.dataLoading = true;
            await this.getAllProductList();
            if (this.modalData.type === 'edit') {
                this.isChange = false;
                this.formValidate.adaptedBizSysType = this.modalData.adaptedBizSysType;
                this.getProductList(this.modalData.adaptedBizSysType);
                this.formValidate.kafkaAddress = this.modalData.kafkaAddress;
                this.formValidate.kafkaTopics = this.modalData.kafkaTopics?.replaceAll(',', '\r\n');
                this.formValidate.productId = this.modalData.adaptedBizSysType ? this.modalData.productId : '';
                this.$nextTick(() => {
                    this.isChange = true;
                });
            } else {
                this.formValidate.adaptedBizSysType =  this.adaptedBizSysTypeDict?.[0]?.code || '';
            }
            this.dataLoading = false;
        },
        getProductList(type){
            this.productList = this.allProductList.filter(o => (o?.bizSysTypes || []).includes(type)) || [];
        },
        handleSelectChange(val){
            if (!this.isChange) return;
            this.formValidate.kafkaAddress = this.defaultConfigData[val]?.kafkaAddress || '';
            this.formValidate.kafkaTopics = this.defaultConfigData[val]?.kafkaTopics?.replaceAll(',', '\r\n') || '';
            this.getProductList(val);
            this.formValidate.productId = val ? this.productList?.[0]?.productInstNo || '' : '';
        },
        submitConfig() {
            this.loading = true;
            this.$refs['formValidate'].validate(async (valid) => {
                if (valid) {
                    // kafkaTopics发送回去时转回逗号分隔,数组将每一项trim后转逗号分隔字符串
                    const kafkaTopicList = this.formValidate.kafkaTopics?.replaceAll('\n', '\r\n')?.split('\r\n')?.map(o => o?.trim())?.filter(item => item && item?.trim()) || [];
                    this.$emit('add-or-edit',
                        {
                            id: this.modalData?.id || '',
                            adaptedBizSysType: this.formValidate?.adaptedBizSysType,
                            kafkaAddress: this.formValidate?.kafkaAddress,
                            kafkaTopics: kafkaTopicList?.join(','),
                            productId: this.formValidate.productId
                        },
                        this.modalData.type
                    );
                    this.modalData.status = false;
                } else {
                    this.loading = false;
                }
            });
        }
    },
    components: {  aButton }
};
</script>

<style lang="less" scoped>
    p {
        color: var(--font-opacity-color);
    }

    /deep/ .verify-tip-1 .verify-tip.verify-bottom {
        margin-top: -34px !important;
    }

</style>
