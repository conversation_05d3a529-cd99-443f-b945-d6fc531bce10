<!-- /**
 * <AUTHOR>
 * @email <EMAIL>
 * @create date 2024-12-11 10:42:46
 * @modify date 2024-12-11 10:42:46
 * @desc 功能号显示设置
 */ -->

<template>
    <h-drawer
        v-model="drawerData.visible"
        :width="600"
        :title="drawerData.title"
        @on-close="handleClose"
    >
        <div class="setting-func-content">
            <div class="setting-func-content-top">
                <a-tips theme="dark" tipText="可通过开关选择你想要在此分类下展示的功能号。功能号过多可能导致界面拥挤，影响查看。建议选择不超过30个。"></a-tips>
                <div class="setting-func-content-top-category">{{ drawerData.categoryName }}</div>
                <div class="setting-func-content-top-info">
                    <div class="setting-func-content-top-info-count">
                        已展示功能号：<span v-if="!loading">{{ computedEnableTotalCount }}</span>
                    </div>
                    <div class="setting-func-content-top-info-search">
                        <h-input
                            v-model="functionNo"
                            :maxlength="100"
                            icon="search"
                            placeholder="输入功能号查询"
                            :disabled="loading"
                            @on-enter="onSearch"
                            @on-click="onSearch"
                        />
                    </div>
                </div>
            </div>
            <div class="setting-func-content-table">
                <div class="setting-func-content-table-view">
                    <h-table
                        :data="tableData"
                        :columns="columns"
                        :loading="loading"
                        customTrKey="key"
                    />
                </div>
                <div class="setting-func-content-table-page">
                    <h-page
                        :total="total"
                        showTotal
                        showSizer
                        :current="page"
                        :pageSize="pageSize"
                        placement="top"
                        @on-page-size-change="changePageSize"
                        @on-change="onChangePage"
                    />
                </div>
            </div>
        </div>
    </h-drawer>
</template>

<script>
import { queryCoreFuncs, setFuncEnable } from '@/api/coreFunctionApi';
import aTips from '@/components/common/apmTips/aTips';
import './displaySettingDrawer.less';
export default {
    name: 'DisplaySettingDrawer',
    components: { aTips },
    props: {
        drawerInfo: {
            title: String,
            productId: String,
            groupId: String,
            clusterList: Array,
            shardingList: Array,
            visible: Boolean
        }
    },
    data() {
        const renderCell = (h, params, key) => {
            const value = params.row[key] ?? '-';
            return h('span', { attrs: { title: value } }, value);
        };

        return {
            drawerData: this.drawerInfo,
            shardingNo: null,
            clusterId: null,
            functionNo: null,
            tableData: [],
            loading: false,
            pageSize: 20,
            page: 1,
            total: 0,
            enableTotalCount: '-',
            columns: [
                {
                    title: '功能号名称',
                    key: 'functionName',
                    ellipsis: true,
                    width: 300,
                    render: (h, params) => renderCell(h, params, 'functionName')
                },
                {
                    title: '功能号',
                    ellipsis: true,
                    key: 'functionNo',
                    render: (h, params) => renderCell(h, params, 'functionNo')
                },
                {
                    title: '是否展示',
                    key: 'enable',
                    render: (h, params) =>  h('h-switch',
                        {
                            key: params.row.enable,
                            props: {
                                size: 'small',
                                value: params.row.enable
                            },
                            on: {
                                'on-change': (val) => {
                                    this.setEnable(params.row, val);
                                }
                            }
                        }
                    )
                }
            ]
        };
    },
    watch: {
        drawerInfo() {
            this.drawerData = this.drawerInfo;
        },
        'drawerInfo.visible'() {
            if (this.drawerInfo.visible) {
                this.tableData = [];
                this.total = 0;
                this.page = 1;
                this.pageSize = 20;
                this.functionNo = null;
                this.queryFunctions();
            }
        }
    },
    computed: {
        computedEnableTotalCount() {
            if (this.enableTotalCount === '-') return '-';
            return `${this.enableTotalCount}个`;
        }
    },
    methods: {
        /**
         * 查询功能号列表
         */
        async queryFunctions(arg = {}, verifyCount = false) {
            if (!this.drawerData.productId) return;
            const param = {
                productId: this.drawerData.productId,
                serviceCode: this.drawerData.groupId,
                page: this.page,
                pageSize: this.pageSize,
                ...arg
            };
            if (this.shardingNo) {
                param.shardingNo = this.shardingNo;
            }
            if (this.clusterId) {
                param.clusterId = this.clusterId;
            }
            if (this.functionNo) {
                param.functionNo = this.functionNo;
            }
            try {
                // this.tableData = [];
                this.loading = true;
                const res = await queryCoreFuncs(param);
                if (res?.success && res?.data) {
                    const { list, totalCount, enableTotalCount } = res.data;
                    this.tableData = (list || []).map(row => ({ ...row, key: `${row.shardingNo}_${row.functionNo}_${row.clusterName}` }));
                    this.total = totalCount || 0;
                    this.enableTotalCount = enableTotalCount ?? '-';
                    this.$nextTick(() => {
                        if (verifyCount && enableTotalCount >= 30) {
                            this.$hMessage.warning({
                                content: '为保证查看体验，建议选择不超过30个。',
                                duration: 3
                            });
                        }
                    });
                }
            } catch (error) {
                console.log('查询功能号异常', error);
                this.tableData = [];
            } finally {
                this.loading = false;
            }
        },
        /**
         * 改变分页大小
         */
        changePageSize(size) {
            this.page = 1;
            this.pageSize = size;
            this.queryFunctions();
        },
        /**
         * 切换分页
         */
        onChangePage(page) {
            this.page = page;
            this.queryFunctions();
        },
        /**
         * 设置是否展示
         */
        async setEnable(row, enable) {
            try {
                this.loading = true;
                const res = await setFuncEnable({
                    ...row,
                    productId: this.drawerData.productId,
                    groupId: this.drawerData.groupId,
                    enable
                });
                let verify = false;
                if (res?.code === '200' && res.success) {
                    this.$hMessage.success('设置成功');
                    verify = true;
                } else {
                    this.$hMessage.error(res.message ?? '设置失败');
                }
                // this.page = 1;
                await this.queryFunctions({}, enable && verify);
            } catch (error) {
                console.log('设置是否展示失败', error);
            } finally {
                this.loading = false;
            }
        },
        /**
         * 重置还原enable
         */
        resetRowEnable(row, enable) {
            this.$nextTick(() => {
                const rowId = `${row.shardingNo}_${row.functionNo}_${row.clusterName}`;
                this.tableData = this.tableData.map(item => {
                    const id = `${item.shardingNo}_${item.functionNo}_${item.clusterName}`;
                    if (id === rowId) {
                        return {
                            ...item,
                            enable: !enable
                        };
                    }
                    return item;
                });
            });
        },
        /**
         * 触发搜索
         */
        onSearch() {
            if (this.loading) return;
            this.page = 1;
            this.$nextTick(() => {
                this.queryFunctions();
            });
        },
        handleClose() {
            this.drawerData = {
                visible: false
            };
        }
    }
};
</script>

<style scoped lang="less">
@import url("@/assets/css/drawer.less");
@import url("@/assets/css/table.less");
</style>
