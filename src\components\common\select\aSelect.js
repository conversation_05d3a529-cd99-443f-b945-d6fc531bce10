import './select.less';
import { typeOf } from '@/utils/utils';
import AsyncValidator from 'async-validator';
export default {
    name: 'a-select',
    props: {
        prop: {
            type: String // 必须
        },
        // select 宽度
        width: {
            type: String,
            default: '150'
        },
        options: {
            type: Array,
            default: []
        },
        value: {
            type: [String, Number, Array],
            default: ''
        },
        placeholder: {
            type: String,
            default: ''
        },
        disabled: {
            type: Boolean,
            default: false
        },
        // tooltip位置
        placement: {
            type: String,
            default: 'bottom'
        },
        // 是否显示tooltip
        showErrorMessage: {
            type: Boolean,
            default: true
        },
        positionFixed: {
            type: Boolean,
            default: false
        },
        required: {
            type: Boolean,
            default: false
        },
        requiredTrigger: {
            type: String,
            default: 'change'
        },
        // select 空校验
        validRules: {
            // 例如：
            // [
            //    { required: true, message: '输入不能为空', trigger: 'change'}
            // ]
            type: Array
        }
    },
    data() {
        return {
            selectValue: this.value || '',
            reqRules: [],
            transCustRules: [],
            mustShowErrorList: [],
            validateMessage: ''
        };
    },
    mounted() {
        if (this.validRules?.length) {
            this.customRules(); // 自定义规则 生成 transCustRules
        };
        if (this.required) {
            const reqRule = { required: true, message: '输入不能为空', trigger: this.requiredTrigger };
            this.reqRules.push(reqRule); // 必填规则增加
        };
        // 初始化的时候校验
        if (this.validRules?.length || this.required){
            this.$nextTick(() => {
                this.validate('change', () => {});
            });
        }
    },
    methods: {
        handleChange(val){
            this.$emit('on-change', val);
            this.selectValue = val;
            if (this.validRules?.length || this.required) {
                this.$nextTick(() => {
                    this.validate('change', () => {});
                });
            }
        },
        // 自定义规则 transCustRules
        customRules() {
            for (const rule of this.validRules) {
                this.custRuleValid(rule);
            }
        },
        // require规则是对象 转换规则参数格式
        custRuleValid(rule) {
            const isObj = typeOf('Object', rule);
            if (isObj && rule.required) {
                const reqRule = { required: true, message: '输入不能为空', trigger: this.requiredTrigger };
                this.reqRules.push(reqRule);
            };
            if (isObj && rule.validator) {
                const funcRule = { validator: rule.validator, trigger: rule.trigger };
                this.transCustRules.push(funcRule);
            }
        },
        // 根据trigger筛选
        getFilteredRule(trigger) {
            const rules = [];
            if (this.reqRules?.length){
                rules.push(...this.reqRules);
            }
            if (this.transCustRules?.length){
                rules.push(...this.transCustRules);
            }
            return rules.filter(
                (rule) => !rule.trigger || rule.trigger.indexOf(trigger) !== -1
            );
        },
        // 校验
        validate(trigger, callback = function() {}) {
            const rules = this.getFilteredRule(trigger);
            const descriptor = {};
            if (!rules || rules.length === 0) {
                callback();
                return true;
            }
            descriptor[this.prop] = rules;
            const model = {};
            model[this.prop] = this.selectValue;
            const validator = new AsyncValidator(descriptor);
            this.validateMessage = '';
            validator.validate(model, { firstFields: true }, (errors) => {
                this.validateMessage = errors ? errors[0].message : '';
            });
        }
    },
    render() {
        return <main class={ `a-select ${this.validateMessage ? 'a-select-error' : ''}`} style={{ display: 'inline-block' }}>
            {
                this.showErrorMessage && this.validateMessage ? <h-tooltip content={ this.validateMessage ? this.validateMessage : ''} placement={this.placement} >
                    <h-select
                        style={{ width: this.width + 'px' }}
                        value={this.selectValue}
                        disabled={this.disabled}
                        placeholder={this.placeholder || '请选择'}
                        positionFixed={this.positionFixed}
                        v-on:on-change={this.handleChange}
                    >
                        {
                            this.options.map(opt => { return <h-option key={opt.value} value={opt.value}>{opt.label}</h-option>; })
                        }
                    </h-select>
                </h-tooltip>
                    : <h-select
                        style={{ width: this.width + 'px' }}
                        value={this.selectValue}
                        disabled={this.disabled}
                        placeholder={this.placeholder || '请选择'}
                        positionFixed={this.positionFixed}
                        v-on:on-change={this.handleChange}
                    >
                        {
                            this.options.map(opt => { return <h-option key={opt.value} value={opt.value}>{opt.label}</h-option>; })
                        }
                    </h-select>
            }
        </main>;
    }
};
