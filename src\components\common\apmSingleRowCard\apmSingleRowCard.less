.apm-single-row-card {
    background: #2c334a;
    border-radius: 4px;

    &-title {
        display: flex;
        height: 32px;
        justify-content: space-between;
        align-items: center;
        color: #fff;
        font-size: 12px;
        border-bottom: 1px solid #444a60;
        padding: 0 15px;
        font-weight: 500;

        &-subtile {
            display: flex;
            color: #cacfd4;
            align-items: center;

            &-line {
                width: 1px;
                height: 12px;
                background-color: #cacfd4;
                margin: 0 5px;
            }
        }
    }

    &-content {
        padding: 15px;
        display: flex;

        &-item {
            flex: 1;

            &-title {
                color: #9296a1;
                font-weight: 400;
                line-height: 12px;
            }

            &-value {
                font-size: 18px;
                margin-top: 2px;
                color: #fff;

                &-surfix {
                    font-size: 12px;
                    color: #9296a1;
                    margin-left: 2px;
                }
            }
        }
    }
}
