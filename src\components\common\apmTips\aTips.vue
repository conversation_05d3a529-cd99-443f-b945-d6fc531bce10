<!-- 提示信息组件 -->
<template>
    <div
        v-if="isShow"
        :class="`tips-content tips-content-${theme}`"
        :style="{ backgroundColor: bgColor, border: borderColor ? `1px solid ${borderColor}` : 'none' }">
        <h-icon
            :name="iconName"
            :color="iconColor"
            size="12"
            class="icon">
        </h-icon>

        <div class="tip-text">
            <slot name="tipContent">
                {{ tipText }}
            </slot>
        </div>

        <h-icon
            v-if="closeable"
            class="close-icon"
            name="close"
            size="14"
            @on-click="handleCloseTip">
        </h-icon>
    </div>
</template>

<script>
const TIP_TYPE_ENUM_DARK = {
    info: {
        icon: 'prompt1',
        color: '#2D8DE5',
        bgColor: '#1F3759'
    },
    success: {
        icon: 'success1',
        color: '#52C41A',
        bgColor: '#26442D'
    },
    error: {
        icon: 'error',
        color: '#F5222D',
        bgColor: '#4C2132'
    },
    warning: {
        icon: 'prompt1',
        color: '#FF9901',
        bgColor: '#4E3B29'
    }
};
const TIP_TYPE_ENUM_DEFAULT = {
    info: {
        icon: 'prompt1',
        color: '#2D8DE5',
        bgColor: '#e6f7ff',
        borderColor: '#91cbff'
    },
    success: {
        icon: 'success1',
        color: '#52C41A',
        bgColor: '#f6ffed',
        borderColor: '#b7eb8f'
    },
    error: {
        icon: 'error',
        color: '#F5222D',
        bgColor: '#fff1f0',
        borderColor: '#ffa39e'
    },
    warning: {
        icon: 'prompt1',
        color: '#FF9901',
        bgColor: '#fffae6',
        borderColor: '#fdd496'
    }
};
export default {
    name: 'ApmTips',
    components: {},
    props: {
        theme: {
            type: String,
            default: 'default'
        },
        type: {
            type: String,
            default: 'info'
        },
        tipText: {
            type: String,
            default: ''
        },
        closeable: {
            type: Boolean,
            default: false
        }
    },
    data() {
        return {
            isShow: true
        };
    },
    computed: {
        TIP_TYPE_ENUM() {
            return this.theme === 'dark' ? TIP_TYPE_ENUM_DARK : TIP_TYPE_ENUM_DEFAULT;
        },
        iconName() {
            return this.TIP_TYPE_ENUM[this.type]?.icon;
        },
        iconColor() {
            return this.TIP_TYPE_ENUM[this.type]?.color;
        },
        bgColor() {
            return this.TIP_TYPE_ENUM[this.type]?.bgColor;
        },
        borderColor() {
            return this.TIP_TYPE_ENUM[this.type]?.borderColor;
        }
    },
    methods: {
        handleCloseTip() {
            this.isShow = false;
            this.$emit('close-tip');
        }
    }
};
</script>
<style lang="less" scoped>
.tips-content {
    width: 100%;
    min-height: 32px;
    border-radius: 4px;
    padding: 6px 12px;
    display: flex;
    align-items: baseline;

    &-dark {
        color: var(--font-color);
    }

    &-light {
        color: #495060;
        border: 1px solid #91cbff;
        background-color: #e6f7ff;
    }

    .tip-text {
        flex: 1;
        padding: 0 20px 0 8px;
    }

    .close-icon {
        color: #9296a1;

        &:hover {
            cursor: pointer;
            color: var(--link-color);
        }
    }
}
</style>
