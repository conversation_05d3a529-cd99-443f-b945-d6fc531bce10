<template>
    <div class="title">
        <a-title title="内存数据表管理">
            <slot>
                <div class="title-select">
                    <h-select
                        v-show="productList.length > 1"
                        v-model="productInstNo"
                        widthAdaption
                        placeholder="请选择"
                        class="select1"
                        :positionFixed="true"
                        :clearable="false"
                        :disabled="linkbtn"
                        @on-change="handleChangeProduct">
                        <h-option v-for="item in productList" :key="item.id" :value="item.productInstNo">
                            {{ item.productName}}
                        </h-option>
                    </h-select>
                    <h-select
                        v-model="endpointId"
                        widthAdaption
                        class="select2"
                        :positionFixed="true"
                        setDefSelect
                        :clearable="false"
                        :disabled="linkbtn"
                        @on-change="handleChangeEndpointt">
                        <h-option v-for="item in bizModelList" :key="item.id" :value="item.id">
                            {{ item.instanceNo }}
                        </h-option>
                    </h-select>
                    <a-button type="dark" :loading="loading" @click="changeLinkStatus">{{ linkbtn ? '断开' : '连接' }}</a-button>
                </div>
            </slot>
        </a-title>
    </div>
</template>

<script>
import _ from 'lodash';
import { mapState, mapActions } from 'vuex';
import aButton from '@/components/common/button/aButton';
import aTitle from '@/components/common/title/aTitle';
import { getUstTableEndpointConfigs } from '@/api/memoryApi';
export default {
    data() {
        return {
            linkbtn: false,
            loading: false,
            productInstNo: '',
            bizModelList: [],
            endpoint: null,
            endpointId: null,
            productInfo: {}
        };
    },
    async mounted() {
        // 查询产品节点
        await this.getProductListLight({ filter: 'supportMemoryTable' });
        this.checkInitSelectedNode();
        this.$emit('success-load');
    },
    computed: {
        ...mapState({
            productList: state => {
                return state.product.productListLight;
            }
        })
    },
    methods: {
        ...mapActions({
            getProductListLight: 'product/getProductListLight'
        }),
        /**
         * 查询默认接入点，兼容从别的页面跳转过来查看内存表，携带节点id
         */
        checkInitSelectedNode() {
            const productInstNo = localStorage.getItem('productInstNo');
            this.productInstNo = productInstNo && _.find(this.productList, ['productInstNo', productInstNo])
                ? productInstNo : this.productList?.[0]?.productInstNo;
            const endpointId = this.$route.query.endpointId ?? this.$route.query.instanceId;
            if (!!endpointId && this.bizModelList.length) {
                const endpointInstance = this.bizModelList.find(item => item.id === endpointId);
                if (endpointInstance) {
                    this.endpoint = `${endpointInstance.endpointIp}:${endpointInstance.endpointPort}`;
                    this.endpointId = endpointInstance.id;
                }
            }
        },
        /**
         * 改变接入点
         */
        handleChangeEndpointt(endpointId) {
            this.endpointId = endpointId;
            const endpointInstance = this.bizModelList.find(item => item.id === endpointId);
            if (endpointInstance) {
                this.endpoint = `${endpointInstance.endpointIp}:${endpointInstance.endpointPort}`;
            }
        },
        /**
         * 查询接入点
         */
        async queryEndpoint() {
            const productInstNo = localStorage.getItem('productInstNo');
            if (!productInstNo) return;
            this.bizModelList = [];
            this.endpoint = null;
            this.endpointId = null;
            const res = await getUstTableEndpointConfigs({ productId: productInstNo });
            if (res.data) {
                this.bizModelList = res.data?.configs || [];
                if (this.bizModelList?.[0]) {
                    const endpointInstance = this.bizModelList[0];
                    this.endpoint = `${endpointInstance.endpointIp}:${endpointInstance.endpointPort}`;
                    this.endpointId = endpointInstance.id;
                }
            }
        },
        // 切换产品节点
        handleChangeProduct(val) {
            if (!this.productList.length) return;
            this.productInfo = _.find(this.productList, ['productInstNo', val]) || this.productList[0];
            localStorage.setItem('productInstNo', this.productInfo.productInstNo);
            this.productInstNo = this.productInfo.productInstNo || '';
            this.queryEndpoint();
        },
        // 根据产品实例获取ip和前置或核心端口并连接节点获取内存表名列表
        routeConnection() {
            if (this.bizModelList.length) {
                const endpointInstance = this.bizModelList?.[0];
                this.endpointId = endpointInstance.id;
                this.endpoint = `${endpointInstance.endpointIp}:${endpointInstance.endpointPort}`;
                this.changeLinkStatus();
            }
        },
        // 改变节点连接状态
        changeLinkStatus() {
            this.loading = this.linkbtn ? false : true;
            this.linkbtn = !this.linkbtn;
            if (!this.linkbtn) return this.$emit('connect-database'); // 断开链接 清空表信息
            // 校验应用节点及节点类型是否存在（或连接的ip和端口号是否存在，不存在点击弹出错误提示
            if (this.endpoint) {
                const endpointInstance = this.bizModelList.find(item => item.id === this.endpointId);
                this.$emit('connect-database', {
                    endpoint: this.endpoint,
                    instanceId: endpointInstance?.appInstanceId
                });
            } else {
                this.breakLink();
                this.$hMessage.warning('请选择连接的产品及应用节点');
            }
        },
        breakLink() {
            this.linkbtn = false;
            this.loading = false;
        },
        successLink() {
            this.loading = false;
        }
    },
    components: { aButton, aTitle }
};
</script>

<style lang="less" scoped>
@import url("@/assets/css/input.less");

.main {
    p {
        display: inline-block;
        color: var(--font-color);
        font-size: var(--title-font-size);
        position: absolute;
        left: 50%;
        transform: translateX(-50%);
    }

    .title {
        min-width: 900px;
    }

    .title-select {
        float: right;
        margin-right: 4px;

        .h-select {
            width: auto;
            min-width: 200px;
        }
    }
}
</style>
