<template>
    <div>
        <h-msg-box-safe v-model="modalData.status" :mask-closable="false"
            title="修改配置" width="500" maxHeight="350" @on-open="getCollections">
            <h-form ref="formValidate" :model="formValidate" :rules="ruleValidate" :label-width="80">
                <h-form-item label="任务名" prop="replayName" required>
                    <h-input v-model.trim="formValidate.replayName" type="text" placeholder="请输入" clearable
                        :maxlength="20"></h-input>
                </h-form-item>
                <h-form-item label="备注" prop="notes">
                    <h-input v-model.trim="formValidate.notes" type="textarea" :rows="2" placeholder="请输入"
                        :maxlength="100" :canResize="false"></h-input>
                </h-form-item>
                <h-form-item label="发送间隔" prop="sendIntervalMs" required>
                    <h-input  v-model.number="formValidate.sendIntervalMs" type="int" style="width: 200px;" :maxlength="30" placeholder="请输入" >
                        <span slot="append">毫秒</span>
                    </h-input>
                </h-form-item>
            </h-form>
            <template v-slot:footer>
                <a-button @click="modalData.status = false">取消</a-button>
                <a-button type="primary" :loading="loading" @click="submitConfig">确认</a-button>
            </template>
        </h-msg-box-safe>
    </div>
</template>

<script>
import _ from 'lodash';
import aButton from '@/components/common/button/aButton';
export default {
    name: 'ModifyReplayModal',
    props: {
        modalInfo: {
            type: Object,
            default: null
        }
    },
    data() {
        return {
            modalData: this.modalInfo,
            formValidate: {},
            loading: false,
            ruleValidate: {
                sendIntervalMs: [{
                    required: true,
                    type: 'number',
                    min: 0,
                    max: 1000,
                    message: '请输入0~1000',
                    trigger: 'blur'
                }]
            }
        };
    },
    methods: {
        getCollections() {
            this.$refs['formValidate'].resetFields();
            this.formValidate = _.cloneDeep(this.modalData);
        },
        submitConfig() {
            this.$refs['formValidate'].validate((valid) => {
                if (valid) {
                    this.$emit('edit', this.formValidate);
                    this.modalData.status = false;
                }
            });
        }
    },
    components: { aButton }
};
</script>

<style lang="less" scoped>
/deep/.h-modal-body {
    padding: 16px;
}

/deep/ .h-input-icon {
    cursor: pointer;
}

.tips-content {
    margin-bottom: 15px;
}
</style>
