import fetch from './fetch';
import { objectToQueryString } from '@/utils/utils';

// const prefix = "/ldplt/api/v1";
const prefix = window['LOCAL_CONFIG']['API_HOME'] || '/';

// mdb用户登录接口
export function mdbLogin(param) {
    return fetch({ includeResponseHeaders: true }).post(`${prefix}/mdb/permission/user/login`, param);
}

// mdb用户登出
export function mdbLoginout() {
    return fetch({ includeResponseHeaders: true }).post(`${prefix}/mdb/permission/user/loginOut`);
}

// mdb用户修改密码
export function mdbUpdatePassword(param) {
    return fetch({ includeResponseHeaders: true }).post(`${prefix}/mdb/permission/user/password/update`, param);
}

// mdb用户重置密码
export function mdbResetPassword(param) {
    return fetch().post(`${prefix}/mdb/permission/user/password/reset`, param);
}

// 获取所有mdb用户列表
export function getMdbUsers(param) {
    return fetch().get(`${prefix}/mdb/permission/users?${objectToQueryString(param)}`);
}

// 创建mdb用户信息
export function createMdbUsers(param) {
    return fetch().post(`${prefix}/mdb/permission/user/create`, param);
}

// 修改mdb用户信息
export function updateMdbUsers(param) {
    return fetch().post(`${prefix}/mdb/permission/user/update`, param);
}

// 批量删除mdb用户信息
export function deleteMdbUsers(param) {
    return fetch().post(`${prefix}/mdb/permission/user/delete`, param);
}

// 获取所有mdb角色列表
export function getMdbRoles(param) {
    return fetch().get(`${prefix}/mdb/permission/roles?${objectToQueryString(param)}`);
}

// 创建mdb角色信息
export function createMdbRoles(param) {
    return fetch().post(`${prefix}/mdb/permission/role/create`, param);
}

// 修改mdb角色信息
export function updateMdbRoles(param) {
    return fetch().post(`${prefix}/mdb/permission/role/update`, param);
}

// 批量删除mdb角色信息
export function deleteMdbRoles(param) {
    return fetch().post(`${prefix}/mdb/permission/role/delete`, param);
}

// 获取产品下按集群类型分组的集群信息
export function getClusterList(param) {
    return fetch().get(`${prefix}/mdb/permission/clusters/group?${objectToQueryString(param)}`);
}

// 获取mdb表权限
export function getRolesPrivilege(param) {
    return fetch().get(`${prefix}/mdb/permission/privileges?${objectToQueryString(param)}`);
}

// 授权mdb表权限
export function setRolesAuthorization(param) {
    return fetch().post(`${prefix}/mdb/permission/privilege/authorization`, param);
}

// 获取集群下的所有表信息
export function getTablesByClusterId(param){
    return fetch().get(`${prefix}/mdb/permission/tables?${objectToQueryString(param)}`);
}

// 权限下发接口
export function setIssuePerInfo(param){
    return fetch().post(`${prefix}/mdb/permission/issued`, param);
}

// 获取APM与mdb权限的一致性
export function getConsistencyInfo(param){
    return fetch({ showErrorToast: false }).get(`${prefix}/mdb/permission/consistency?${objectToQueryString(param)}`);
}

// 最近一次权限状态结果
export function getHistoryIssuePerInfo(param){
    return fetch({ showErrorToast: false }).get(`${prefix}/mdb/permission/issued/his?${objectToQueryString(param)}`);
}

// 获取远程mdb用户与集群关系
export function getMdbPerUserCluster(param) {
    return fetch().get(`${prefix}/mdb/permission/remote/user-cluster?${objectToQueryString(param)}`);
}

// 获取远程mdb集群下的权限信息
export function getMdbPerUserClusterAuth(param) {
    return fetch().get(`${prefix}/mdb/permission/remote/cluster-auth?${objectToQueryString(param)}`);
}

// 获取APM中mdb权限版本信息
export function getMdbPerVersion(param) {
    return fetch().get(`${prefix}/mdb/permission/version?${objectToQueryString(param)}`);
}

// 查看用户ID及密码
export function getMdbPerAccess(param) {
    return fetch().get(`${prefix}/mdb/permission/access?${objectToQueryString(param)}`);
}

// 导出权限
export function downloadPermissionsFile(param){
    return fetch().get(`${prefix}/mdb/permission/auth/export?${objectToQueryString(param)}`);
}

// 导入权限
export function importPermissionsFile(param){
    return fetch().post(`${prefix}/mdb/permission/auth/import`, param);
}

// 获取权限配置
export function getMdbPerAuthConfig(param) {
    return fetch().get(`${prefix}/mdb/permission/auth/config?${objectToQueryString(param)}`);
}

// 权限配置修改
export function setMdbPerAuthConfig(param){
    return fetch().post(`${prefix}/mdb/permission/auth/config`, param);
}

// 权限文件内容
export function setMdbPerAuthDecrypt(param){
    return fetch().post(`${prefix}/mdb/permission/auth/decrypt`, param);
}

