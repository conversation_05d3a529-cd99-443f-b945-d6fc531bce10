<!--
 * @Description: 全链路时延监控
 * @Author: <PERSON><PERSON>
 * @Date: 2023-06-26 18:51:45
 * @LastEditTime: 2024-08-25 18:42:12
 * @LastEditors: yingzx38608 <EMAIL>
-->
<template>
    <div class="main">
        <div class="main-top">
            <span class="currenttime">今天是: {{ currentTime }}</span>
            <p>{{ $getProductType(productInfo.productType) }}时延监控</p>
            <h-select
                v-show="productList.length > 1"
                v-model="productInstNo"
                class="securities"
                placeholder="请选择"
                :positionFixed="true"
                :clearable="false"
                style="margin-right: 6px;"
                @on-change="checkProduct">
                <h-option v-for="item in productList" :key="item.id" :value="item.productInstNo">{{ item.productName }}</h-option>
            </h-select>
        </div>
        <div v-if="productInstNo" ref="wrapper" class="wrapper-flex">
            <router-view :productInstNo="productInstNo" :productInfo="productInfo" :date="currentTime"></router-view>
        </div>
        <a-loading v-if="loading"></a-loading>
    </div>
</template>
<script>
import _ from 'lodash';
import { mapState, mapActions } from 'vuex';
import { formatDate } from '@/utils/utils';
import aLoading from '@/components/common/loading/aLoading';
export default {
    data() {
        return {
            currentTime: formatDate(new Date()),
            productInstNo: '',
            productInfo: {},
            loading: false
        };
    },
    async mounted() {
        this.loading = true;
        await this.getProductList({ filter: 'supportEntrustLatency' });
        this.loading = false;
        const productInstNo = localStorage.getItem('productInstNo');
        this.productInstNo =  _.find(this.productList, ['productInstNo', productInstNo])?.productInstNo ||
            this.productList?.[0]?.productInstNo;
        this.checkProduct(this.productInstNo);
        this.currentTime = formatDate(new Date());
    },
    created() {
        const path = localStorage.getItem('monitorPath') || 'businessMonitor';
        this.$hCore.navigate(path);
    },
    computed: {
        ...mapState({
            productList: state => {
                return state.product.productListLight || [];

            }
        })
    },
    methods: {
        ...mapActions({ getProductList: 'product/getProductListLight' }),
        // 切换产品
        checkProduct(e) {
            if (this.productList.length) {
                this.productInfo = e ? _.find(this.productList, ['productInstNo', e]) : this.productList[0];
                localStorage.setItem('productInstNo', this.productInfo.productInstNo);
            }
        }
    },
    beforeRouteUpdate(to, from, next) {
        this.checkProduct(this.productInstNo);
        next();
    // 在当前路由改变，但是该组件被复用时调用
    // 举例来说，对于一个带有动态参数的路径 `/users/:id`，在 `/users/1` 和 `/users/2` 之间跳转的时候，
    // 由于会渲染同样的 `UserDetails` 组件，因此组件实例会被复用。而这个钩子就会在这个情况下被调用。
    // 因为在这种情况发生的时候，组件已经挂载好了，导航守卫可以访问组件实例 `this`
    },
    components: { aLoading }
};
</script>
<style lang="less" scoped>
@import url("@/assets/css/input.less");
@import url("@/assets/css/monitor.less");
</style>
