表分析插件支持通过sql语句查找、修改内存表中的数据。

表分析插件初始化时会创建虚表，每个表对应一个数据类型，其中数据成员则作为虚表的列。虚表作为承载数据的主体，会用到一些和实现相关的特性，为了对外呈现出统一且实现无关的界面，对于每一个虚表都会创建一个视图，用户通过操作视图实现对数据的访问，不能直接操作虚表。视图的命名规则为：

1. 视图名使用内存表的类型名后缀View的形式。例如，对于类型struct Score{};对应的视图名为ScoreView。

2. 列名使用数据成员名，有父子关系的类型，父子成员之间使用_进行分隔。例如，struct A{int id;};struct B{int id;A a;};struct Score{int id;B b;};Score自己的成员id对应的列名为id，成员b中的成员id对应的列名为b_id，成员b中的成员a中的成员id对应的列名为b_a_id。

表分析插件使用sqlite3引擎执行sql语句，理论上所有sqlite3支持的功能都是可用的，但由于内存表不同于普通的关联数据库，因此存在一部分限制，例如只支持查询和修改，无法插入和删除，无法实现关联查询等，以下列举了一些常用的操作：

查询表记录

1. 查询全量数据

   select * from ScoreView;

2. 查询指定列

   select id,b_id,b_a_id from ScoreView;

3. 条件查询

   select b_a_id from ScoreView where id=0;

4. 查询固定数量的记录

   select * from ScoreView limit 2;

5. 查询记录数量

   select count(*) from ScoreView;

修改表记录

1. 修改某一列

   update ScoreView set id=3;

2. 按条件修改某一列

   update ScoreView set id=4 where id=3;

访问内存表通过407功能号接入，用户按协议构造Ldp消息发送给核心，由核心代为调用表分析插件的功能，其中消息的BizFixed部分为json文本类型，执行SQL功能的OperationType为8，请求格式如下：

```json
{
	"FunctionID": 407,
	"MsgType": 0,
	"OperationType": 8,
	"LocalInfo": {
		"UserName": "admin",
		"Token": "xxxxxxxx"
	},
	"//": "数据库名",
	"DataBaseName": "xst_tableanalyse",
	"//": "表名",
	"TableName": "tagHeaderFileNode",
	"//": "sql语句",
	"SQLOption": "select * from ScoreView"
}
```