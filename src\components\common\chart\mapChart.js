import * as echarts from 'echarts';
import china from '@/components/common/chart/map/china.json';
import { mapTopoOption } from '@/components/common/chart/chartConfig';
export default {
    name: 'mapChart',
    props: {
        chartRef: {
            type: String,
            default: 'map'
        },
        chartData: {
            type: Object,
            default: () => {}
        }
    },
    data() {
        return {
            myChart: null,
            option: {}
        };
    },
    mounted() {
        window.roomTopoJump = (type, name) => {
            console.log(type, name);
        };
        if (this.$refs[this.chartRef]) {
            this.myChart = echarts.init(this.$refs[this.chartRef]);
            // 地图注册，第一个参数的名字必须和option.geo.map一致
            echarts.registerMap('china', china); // 第一个参数为配置设置的名称，第二个参数为引入的地图名称
            this.initOption();
            this.resizeObserver = new ResizeObserver(() => {
                this.resize();
            });
            this.resizeObserver.observe(this.$refs[this.chartRef]);
        }
    },
    beforeDestroy() {
        this.resizeObserver && this.resizeObserver.disconnect(this.$refs[this.chartRef]);
    },
    methods: {
        initOption() {
            this.option = mapTopoOption;
            this.refreshData(this.chartData);
        },
        refreshData(chartData) {
            this.option.series[0].data = chartData.roomCount || [];
            this.option.series[1].data = chartData.roomLocation || [];
            this.option.series[2].data = chartData.roomRelation || [];
            this.myChart && this.myChart.setOption(this.option);
        },
        resize() {
            this.myChart && this.myChart.resize();
        }
    },
    watch: {
        chartData: {
            handler(newVal) {
                if (newVal) {
                    this.refreshData(newVal);
                } else {
                    this.myChart.dispose();
                }
            },
            deep: true
        }
    },
    render() {
        return (
            <div ref={this.chartRef} style="width: 100%; height: 100%"></div>
        );
    }
};
