import './tag.less';
export default {
    name: 'a-tag',
    props: {
        closable: {
            type: Boolean,
            default: false
        },
        checkable: {
            type: Boolean,
            default: false
        },
        checked: {
            type: Boolean,
            default: true
        },
        color: {
            type: String,
            default: 'default'
        },
        type: {
            type: String,
            default: undefined  // 'border', 'dot'
        },
        id: {
            type: String,
            default: ''
        },
        name: {
            type: [String, Number]
        },
        size: {
            type: String,
            default: 'normal'
        }
    },
    data() {
        return {
        };
    },
    mounted() {
    },
    methods: {
        handleClicKTag(e) {
            e.preventDefault();
            e.stopPropagation();
            this.$emit('on-click', this.id);
        },
        close(e, id) {
            e.preventDefault();
            e.stopPropagation();
            this.$emit('on-close', id);
        }
    },
    render() {
        return <div onclick={this.handleClicKTag} class='a-tag'>
            <h-tag
                class={[this.size === 'normal' ? 'tag-size' : '']}
                color ={this.color}
                closable ={this.closable}
                key={this.id}
                name={this.id}
                v-on:on-close={this.close}
            >
                {this.name}
            </h-tag>
        </div>;
    }
};
