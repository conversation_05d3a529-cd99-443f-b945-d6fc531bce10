#!/usr/bin/env node

const fs = require('fs');
const path = require('path');

/**
 * 国际化文件验证和优化脚本
 * 检查生成的国际化文件的完整性，处理重复项，优化键值结构
 */

class I18nValidator {
    constructor() {
        this.finalDir = path.join(__dirname, '../src/locales/final');
        this.outputDir = path.join(__dirname, '../src/locales/optimized');
        
        // 加载最终的国际化文件
        this.zhCN = this.loadI18nFile('zh-CN.js');
        this.enUS = this.loadI18nFile('en-US.js');
        
        // 验证结果
        this.validationResults = {
            duplicates: [],
            invalidKeys: [],
            missingTranslations: [],
            emptyValues: [],
            suggestions: []
        };
    }

    /**
     * 加载国际化文件
     */
    loadI18nFile(filename) {
        try {
            const filePath = path.join(this.finalDir, filename);
            const content = fs.readFileSync(filePath, 'utf-8');
            const jsonStr = content.replace(/export\s+default\s+/, '').replace(/;?\s*$/, '');
            return JSON.parse(jsonStr);
        } catch (error) {
            console.error(`加载国际化文件失败 ${filename}:`, error.message);
            return {};
        }
    }

    /**
     * 验证键名规范
     */
    validateKeyNaming(obj, prefix = '') {
        for (const key in obj) {
            const fullKey = prefix ? `${prefix}.${key}` : key;
            
            if (typeof obj[key] === 'object' && obj[key] !== null) {
                this.validateKeyNaming(obj[key], fullKey);
            } else {
                // 检查键名是否符合小驼峰规范
                if (!/^[a-z][a-zA-Z0-9]*$/.test(key)) {
                    this.validationResults.invalidKeys.push({
                        key: fullKey,
                        issue: '键名不符合小驼峰规范',
                        suggestion: this.suggestBetterKey(key)
                    });
                }
                
                // 检查是否有空值
                if (!obj[key] || obj[key].trim() === '') {
                    this.validationResults.emptyValues.push({
                        key: fullKey,
                        issue: '值为空'
                    });
                }
            }
        }
    }

    /**
     * 建议更好的键名
     */
    suggestBetterKey(key) {
        return key
            .replace(/[^a-zA-Z0-9]/g, '')
            .replace(/^[A-Z]/, match => match.toLowerCase())
            .replace(/([A-Z])/g, (match, p1, offset) => offset > 0 ? p1.toLowerCase() : p1);
    }

    /**
     * 检查重复值
     */
    findDuplicateValues(obj, prefix = '', valueMap = new Map()) {
        for (const key in obj) {
            const fullKey = prefix ? `${prefix}.${key}` : key;
            
            if (typeof obj[key] === 'object' && obj[key] !== null) {
                this.findDuplicateValues(obj[key], fullKey, valueMap);
            } else {
                const value = obj[key];
                if (valueMap.has(value)) {
                    valueMap.get(value).push(fullKey);
                } else {
                    valueMap.set(value, [fullKey]);
                }
            }
        }
        
        // 找出重复的值
        for (const [value, keys] of valueMap.entries()) {
            if (keys.length > 1) {
                this.validationResults.duplicates.push({
                    value,
                    keys,
                    suggestion: '考虑合并为通用文案'
                });
            }
        }
        
        return valueMap;
    }

    /**
     * 检查中英文对应关系
     */
    validateTranslations(zhObj, enObj, prefix = '') {
        for (const key in zhObj) {
            const fullKey = prefix ? `${prefix}.${key}` : key;
            
            if (typeof zhObj[key] === 'object' && zhObj[key] !== null) {
                if (!enObj[key] || typeof enObj[key] !== 'object') {
                    this.validationResults.missingTranslations.push({
                        key: fullKey,
                        issue: '英文版本缺少对应的对象结构'
                    });
                } else {
                    this.validateTranslations(zhObj[key], enObj[key], fullKey);
                }
            } else {
                if (!enObj[key]) {
                    this.validationResults.missingTranslations.push({
                        key: fullKey,
                        issue: '英文版本缺少对应的翻译'
                    });
                }
            }
        }
    }

    /**
     * 生成优化建议
     */
    generateSuggestions() {
        // 建议提取通用文案
        const commonTexts = [
            '查询', '添加', '编辑', '删除', '保存', '取消', '确认', '提交',
            '成功', '失败', '错误', '警告', '加载中', '请选择', '请输入',
            '暂无数据', '操作成功', '操作失败'
        ];
        
        commonTexts.forEach(text => {
            const duplicates = this.validationResults.duplicates.filter(d => d.value === text);
            if (duplicates.length > 0) {
                this.validationResults.suggestions.push({
                    type: 'extract_common',
                    text,
                    suggestion: `"${text}" 出现在多个地方，建议提取到 common 分类中`,
                    keys: duplicates[0].keys
                });
            }
        });

        // 建议优化长文案
        this.findLongTexts(this.zhCN);
    }

    /**
     * 查找过长的文案
     */
    findLongTexts(obj, prefix = '') {
        for (const key in obj) {
            const fullKey = prefix ? `${prefix}.${key}` : key;
            
            if (typeof obj[key] === 'object' && obj[key] !== null) {
                this.findLongTexts(obj[key], fullKey);
            } else {
                const value = obj[key];
                if (value && value.length > 50) {
                    this.validationResults.suggestions.push({
                        type: 'long_text',
                        key: fullKey,
                        value,
                        suggestion: '文案过长，考虑拆分或简化'
                    });
                }
            }
        }
    }

    /**
     * 优化国际化结构
     */
    optimizeStructure() {
        const optimizedZhCN = JSON.parse(JSON.stringify(this.zhCN));
        const optimizedEnUS = JSON.parse(JSON.stringify(this.enUS));

        // 提取通用文案到 common
        const commonTexts = {
            // 操作类
            query: '查询',
            add: '添加',
            edit: '编辑',
            delete: '删除',
            save: '保存',
            cancel: '取消',
            confirm: '确认',
            submit: '提交',
            reset: '重置',
            refresh: '刷新',
            export: '导出',
            import: '导入',
            search: '搜索',
            clear: '清空',
            select: '选择',
            selectAll: '全选',

            // 状态类
            success: '成功',
            failed: '失败',
            error: '错误',
            warning: '警告',
            info: '信息',
            loading: '加载中',
            running: '运行中',
            stopped: '已停止',
            finished: '已完成',
            pending: '待处理',

            // 提示类
            pleaseSelect: '请选择',
            pleaseInput: '请输入',
            noData: '暂无数据',
            operationSuccess: '操作成功',
            operationFailed: '操作失败',
            confirmDelete: '确认删除吗？',
            deleteSuccess: '删除成功',
            saveSuccess: '保存成功',

            // 时间类
            startTime: '开始时间',
            endTime: '结束时间',
            createTime: '创建时间',
            updateTime: '更新时间',

            // 基础类
            name: '名称',
            title: '标题',
            description: '描述',
            remark: '备注',
            type: '类型',
            status: '状态',
            version: '版本'
        };

        // 将通用文案添加到 common 中
        optimizedZhCN.common = { ...optimizedZhCN.common, ...commonTexts };
        optimizedEnUS.common = { ...optimizedEnUS.common, ...commonTexts }; // 英文版本暂时使用中文

        return { optimizedZhCN, optimizedEnUS };
    }

    /**
     * 生成验证报告
     */
    generateValidationReport() {
        const report = {
            summary: {
                totalKeys: this.countKeys(this.zhCN),
                duplicateValues: this.validationResults.duplicates.length,
                invalidKeys: this.validationResults.invalidKeys.length,
                missingTranslations: this.validationResults.missingTranslations.length,
                emptyValues: this.validationResults.emptyValues.length,
                suggestions: this.validationResults.suggestions.length,
                validationTime: new Date().toISOString()
            },
            details: this.validationResults
        };

        return report;
    }

    /**
     * 计算键的总数
     */
    countKeys(obj) {
        let count = 0;
        for (const key in obj) {
            if (typeof obj[key] === 'object' && obj[key] !== null) {
                count += this.countKeys(obj[key]);
            } else {
                count++;
            }
        }
        return count;
    }

    /**
     * 写入文件
     */
    writeFile(filename, content) {
        if (!fs.existsSync(this.outputDir)) {
            fs.mkdirSync(this.outputDir, { recursive: true });
        }
        
        const filePath = path.join(this.outputDir, filename);
        fs.writeFileSync(filePath, content, 'utf-8');
        console.log(`✅ 生成文件: ${filePath}`);
    }

    /**
     * 执行验证和优化
     */
    run() {
        console.log('🚀 开始验证和优化国际化文件...');
        console.log(`输入目录: ${this.finalDir}`);
        console.log(`输出目录: ${this.outputDir}`);

        // 执行验证
        console.log('\n📋 执行验证...');
        this.validateKeyNaming(this.zhCN);
        this.findDuplicateValues(this.zhCN);
        this.validateTranslations(this.zhCN, this.enUS);
        this.generateSuggestions();

        // 生成验证报告
        const report = this.generateValidationReport();
        this.writeFile('validation-report.json', JSON.stringify(report, null, 2));

        // 生成优化后的文件
        console.log('\n🔧 生成优化文件...');
        const { optimizedZhCN, optimizedEnUS } = this.optimizeStructure();
        
        this.writeFile('zh-CN-optimized.js', `export default ${JSON.stringify(optimizedZhCN, null, 2)};`);
        this.writeFile('en-US-optimized.js', `export default ${JSON.stringify(optimizedEnUS, null, 2)};`);
        
        // 生成索引文件
        const indexContent = `import zhCN from './zh-CN-optimized';
import enUS from './en-US-optimized';

export default {
    'zh-CN': zhCN,
    'en-US': enUS
};`;
        this.writeFile('index.js', indexContent);

        // 输出验证结果
        console.log('\n📊 验证结果:');
        console.log(`总键数: ${report.summary.totalKeys}`);
        console.log(`重复值: ${report.summary.duplicateValues} 个`);
        console.log(`无效键名: ${report.summary.invalidKeys} 个`);
        console.log(`缺少翻译: ${report.summary.missingTranslations} 个`);
        console.log(`空值: ${report.summary.emptyValues} 个`);
        console.log(`优化建议: ${report.summary.suggestions} 条`);

        if (report.summary.duplicateValues > 0) {
            console.log('\n🔍 重复值示例:');
            report.details.duplicates.slice(0, 5).forEach(duplicate => {
                console.log(`  "${duplicate.value}" 出现在: ${duplicate.keys.join(', ')}`);
            });
        }

        if (report.summary.suggestions > 0) {
            console.log('\n💡 优化建议:');
            report.details.suggestions.slice(0, 5).forEach(suggestion => {
                console.log(`  ${suggestion.suggestion}`);
            });
        }

        console.log('\n✨ 验证和优化完成!');
        console.log('📝 查看详细报告: src/locales/optimized/validation-report.json');
    }
}

// 执行脚本
if (require.main === module) {
    const validator = new I18nValidator();
    validator.run();
}

module.exports = I18nValidator;
