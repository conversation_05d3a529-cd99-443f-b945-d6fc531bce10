import { ENDPOINT_TYPE } from '@/components/endpointConfig/constant';

/**
 * 菜单配置，
 * children为渲染的子菜单，
 * name为显示的中文名，
 * id为唯一值，
 * tabs为激活当前菜单时显示的tabs内容
 * tabs：component为显示的组件，id为唯一值，name为tab显示的名字
 */
export const MENUS = [
    {
        name: '应用时延度量',
        id: 'application',
        children: [
            {
                name: '链路模型配置',
                id: 'topoConfig',
                tabs: [
                    {
                        component: 'linkTopoConfig',
                        id: 'linkTopoConfig',
                        name: '模型配置'
                    },
                    {
                        component: 'linkTopoReview',
                        id: 'linkTopoReview',
                        name: '模型预览'
                    }
                ]
            },
            {
                name: '链路日志配置',
                id: 'logConfig',
                tabs: [
                    {
                        component: 'logSourceConfig',
                        id: 'logSourceConfig',
                        name: '链路日志配置'
                    }
                ]
            }
        ]
    },
    {
        name: '产品服务网关',
        id: 'productAccess',
        children: [
            {
                name: '管理功能API',
                id: 'manageAccess',
                tabs: [
                    {
                        component: 'endpointConfig',
                        id: 'managementEndpointConfig',
                        name: '产品服务网关',
                        endpointType: ENDPOINT_TYPE.MANAGEMENT
                    }
                ]
            },
            {
                name: 'MDB-SQL',
                id: 'mdbAccess',
                tabs: [
                    {
                        component: 'endpointConfig',
                        id: 'mdbEndpointConfig',
                        name: '产品服务网关',
                        endpointType: ENDPOINT_TYPE.MDB
                    }
                ]
            },
            {
                name: '内存表管理',
                id: 'ustTableAccess',
                tabs: [
                    {
                        component: 'endpointConfig',
                        id: 'ustTableEndpointConfig',
                        name: '产品服务网关',
                        endpointType: ENDPOINT_TYPE.UST_TABLE
                    }
                ]
            },
            {
                name: '应用抓包发包',
                id: 'sendPacketReceivedTableAccess',
                tabs: [
                    {
                        component: 'endpointConfig',
                        id: 'sendPacketReceivedEndpoint',
                        name: '产品服务网关',
                        endpointType: ENDPOINT_TYPE.SEND_PACKET_RECEIVED
                    }
                ]
            },
            {
                name: 'Locate配置管理',
                id: 'locateAccess',
                tabs: [
                    {
                        component: 'endpointConfig',
                        id: 'locateAccess',
                        name: '产品服务网关',
                        endpointType: ENDPOINT_TYPE.LOCATE
                    }
                ]
            }
        ]
    },
    {
        name: '应用运行监控',
        id: 'appMonitor',
        children: [
            {
                name: '监控规则',
                id: 'monitorRule',
                tabs: [
                    {
                        component: 'appMonitorRule',
                        id: 'appMonitorRule',
                        name: '监控规则'
                    }
                ]
            }
        ]
    },
    {
        name: '管理功能',
        id: 'manageFunction',
        children: [
            {
                name: '管理功能使用说明',
                id: 'manageFunctionMeta',
                tabs: []
            }
        ]
    }
];

