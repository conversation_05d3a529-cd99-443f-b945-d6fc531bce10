<template>
    <div class="query-table-box">
        <query-table
            ref="query-table"
            :border="false"
            showTitle
            :formItems="formItems"
            :multiLevel="multiLevel"
            :columns="columns"
            :tableData="tableData"
            :total="taskCount"
            :loading="loading"
            @selection="tableSelectChange"
            @query=handleQuery>
            <template #tableTitle>
                <div class="table-title">校验任务列表</div>
                <div class="table-describe">
                    校验任务总数:
                    <span>{{ taskCount }}</span>
                </div>
            </template>
            <template #operateBlock>
                <a-button type="primary"
                    style="margin-right: 5px;"
                    @click="handleCreateTask">创建
                </a-button>
                <a-button type="dark"
                    style="margin-right: 5px;"
                    @click="handleBetchStart(selectRowIds)">启动
                </a-button>
                <a-button type="dark"
                    style="margin-right: 5px;"
                    @click="handleBetchStop(selectRowIds)">停止
                </a-button>
                <a-button type="danger"
                    style="margin-right: 8px;"
                    @click="handleBetchDel(selectRowIds)">删除
                </a-button>
            </template>
        </query-table>

        <!-- 查看校验任务内容 -->
        <monitor-rule-detail-modal
            v-if="ruleDetail.status"
            :modalInfo="ruleDetail">
        </monitor-rule-detail-modal>
        <!-- 查看执行记录 -->
        <execution-record-drawer
            v-if="recordInfo.status"
            :productId="productId"
            :modalInfo="recordInfo">
        </execution-record-drawer>
        <!-- 查看直接结果详情 -->
        <verify-detail-modal
            v-if="resultInfo.status"
            :productId="productId"
            :modalInfo="resultInfo">
        </verify-detail-modal>
    </div>
</template>
<script>
import _ from 'lodash';
import { autoConvertTimeRender, cutZero } from '@/utils/utils';
import aButton from '@/components/common/button/aButton';
import queryTable from '@/components/common/bestTable/queryTable/queryTable';
import importOperateBtn from '@/components/ustTableVerification/column/importOperateBtn.vue';
import importStatusTableIcon from '@/components/secondAppearance/importStatusTableIcon.vue';
import executionRecordDrawer from '@/components/ustTableVerification/modal/executionRecordDrawer.vue';
import monitorRuleDetailModal from '@/components/productServiceConfig/modal/monitorRuleDetailModal.vue';
import verifyDetailModal from '@/components/ustTableVerification/modal/verifyDetailModal.vue';
import { VERIFE_RESULT_LIST, VERIFE_TYPE_LIST, MULTI_LEVEL } from '@/components/ustTableVerification/constant';
import { getVerificationTaskList, deleteCompareRule, setCompareOperate, downloadCompareResult } from '@/api/dataVerification';
export default {
    name: 'VerificationTaskList',
    components: { queryTable, aButton, executionRecordDrawer, monitorRuleDetailModal, verifyDetailModal },
    props: {
        productId: {
            type: String,
            default: ''
        }
    },
    data() {
        return {
            loading: false,
            formItems: [
                {
                    type: 'select',
                    key: 'compareResult',
                    label: '校验结果',
                    options: VERIFE_RESULT_LIST,
                    value: ''
                },
                {
                    type: 'select',
                    key: 'ruleType',
                    label: '校验类型',
                    options: VERIFE_TYPE_LIST,
                    value: ''
                }
            ],
            multiLevel: MULTI_LEVEL,
            columns: [
                {
                    type: 'selection',
                    width: 50,
                    align: 'center'
                },
                {
                    title: '任务名称',
                    key: 'ruleName',
                    ellipsis: true,
                    minWidth: 150,
                    render: (h, params) => {
                        return h('div', {
                            style: {
                                width: '100%'
                            }
                        }, [
                            h('Button',
                                {
                                    props: {
                                        size: 'small',
                                        type: 'text'
                                    },
                                    style: {
                                        width: '100%',
                                        padding: 0,
                                        'text-align': 'left',
                                        overflow: 'hidden', // 超出的文本隐藏
                                        'text-overflow': 'ellipsis', // 溢出用省略号显示
                                        'white-space': 'nowrap' // 溢出不换行
                                    },
                                    on: {
                                        click: () => {
                                            this.handleViewTaskContent(params.row);
                                        }
                                    }
                                },
                                params.row.ruleName || '-'
                            )
                        ]);
                    }
                },
                {
                    title: '源数据库',
                    key: 'sourceDatabaseName',
                    ellipsis: true
                },
                {
                    title: '目标数据库',
                    key: 'targetDatabasesName',
                    minWidth: 100,
                    ellipsis: true
                },
                {
                    title: '校验类型',
                    key: 'ruleType',
                    minWidth: 90,
                    ellipsis: true,
                    formatMethod: (row) => VERIFE_TYPE_LIST.find(e => e.value === row.ruleType)?.label ?? '-'
                },
                {
                    title: '错误阈值',
                    key: 'errorThreshold',
                    minWidth: 100,
                    ellipsis: true,
                    formatMethod: (row) => row.errorThreshold || '-'
                },
                {
                    title: '操作',
                    key: 'action',
                    width: 130,
                    render: (_, params) => {
                        return <importOperateBtn
                            row={params.row}
                            startLoading={this.startRuleList.includes(params.row.ruleId)}
                            v-on:row-operate={(operate) => this.handleRowClick(operate, params.row)}
                        />;
                    }
                },
                {
                    title: '执行开始时间',
                    key: 'executeTime',
                    minWidth: 150,
                    ellipsis: true,
                    formatMethod: (row) => row.executeTime || '-'
                },
                {
                    title: '当前执行进度',
                    key: 'ratio',
                    minWidth: 120,
                    render: (_, params) => {
                        if (!params.row?.ratio && params.row.ratio !== 0)  {
                            return <span>-</span>;
                        }
                        const ratio = Number(params.row?.ratio) * 100;
                        const status = this.getCompareProgressStatus(ratio, params.row.compareResult);
                        return <div title={cutZero(ratio.toFixed(1)) + '%'}>
                            <importStatusTableIcon type={status || ''}/>
                            {cutZero(ratio.toFixed(1)) + '%'}
                        </div>;
                    }
                },
                {
                    title: '执行耗时',
                    key: 'spendTime',
                    minWidth: 100,
                    render: (h, params) => {
                        return autoConvertTimeRender(h, params, 'spendTime', 's');
                    }
                },
                {
                    title: '校验结果',
                    key: 'compareResult',
                    minWidth: 110,
                    render: (h, params) => {
                        const resultObj = _.find(VERIFE_RESULT_LIST, ['value', params.row.compareResult]);
                        // 若图标为 warn 和 error，label文字需要支持点击
                        const canClick = ['warn', 'error'].includes(resultObj?.icon);
                        return <div title={resultObj?.label || '-'}>
                            <importStatusTableIcon type={resultObj?.icon || ''}/>
                            {
                                canClick
                                    ? <h-button type="text"
                                        style="padding: 0 0 2px 0; color: var(--link-color)"
                                        onClick={() => this.handlecompareResult(params.row)}>
                                        {resultObj?.label || '-'}
                                    </h-button>
                                    : <span>{resultObj?.label || '-'}</span>
                            }
                        </div>;
                    }
                }
            ],
            tableData: [],
            taskCount: 0,
            startRuleList: [],
            selectRowIds: [],

            ruleDetail: {
                status: false,
                contentList: []
            },
            recordInfo: {
                status: false
            },
            resultInfo: {
                status: false
            }
        };
    },
    methods: {
        async initData() {
            this.clearPolling();
            // 清理数据
            this.clearData();
            // 查询列表
            await this.$refs['query-table'].$_handleQuery();
            // 轮询列表更新状态
            this.setPolling();
        },
        clearData() {
            this.selectRowIds = [];
        },
        // 表格查询
        async handleQuery(val) {
            this.loading = true;
            // 参数查询
            try {
                const param = {
                    productId: this.productId,
                    ruleType: val?.ruleType || '',
                    compareResult: val?.compareResult || '',
                    page: val.page,
                    pageSize: val.pageSize
                };
                const res = await getVerificationTaskList(param);
                if (res.code === '200') {
                    this.tableData = res?.data?.list || [];
                    this.taskCount = res.data?.totalCount;
                } else {
                    this.tableData = [];
                    this.taskCount = 0;
                    this.clearPolling();
                }
            } catch (err) {
                console.error(err);
                this.tableData = [];
                this.taskCount = 0;
                this.clearPolling();
            } finally {
                this.loading = false;
            }
        },

        // 启动定时器查询
        setPolling() {
            this.clearPolling();
            this.timer = setInterval(async () => {
                this.tableData?.length && await this.getTableData();
            }, 5000);
        },
        clearPolling() {
            this.timer && clearInterval(this.timer);
        },
        // 获取表格数据状态更新
        async getTableData() {
            // 获取当前查询参数
            const param = {
                productId: this.productId,
                ruleType: '',
                compareResult: '',
                page: 1,
                pageSize: 2 ** 31 - 1
            };
            let allData = [];
            try {
                const res = await getVerificationTaskList(param);
                if (res.code === '200') {
                    allData = res?.data?.list || [];
                } else {
                    this.clearPolling();
                }
            } catch (err) {
                console.error(err);
                this.clearPolling();
            }

            // 轮询刷新时 - 恢复表格勾选状态
            const tableDataUpdate = this.tableData.map(item => {
                return {
                    ...item,
                    ..._.find(allData, ['ruleId', item?.ruleId]),
                    ...{ _checked: this.selectRowIds.includes(item.ruleId) }
                };
            });
            this.tableData = tableDataUpdate;
        },

        /**
         * 处理表格行操作点击事件
         * @param operate 操作类型:
         *      start:启动、stop:停止、delete:删除、record:执行记录、view:查看校验任务
         * @param row 表格行信息
         */
        handleRowClick(operate, row) {
            switch (operate) {
                case 'start':
                    this.handleBetchStart([row.ruleId]);
                    break;
                case 'stop':
                    this.handleBetchStop([row.ruleId]);
                    break;
                case 'delete':
                    this.handleBetchDel([row.ruleId]);
                    break;
                case 'record':
                    this.handleViewRecord(row);
                    break;
                case 'view':
                    this.handleViewTaskContent(row);
                    break;
            }
        },
        // 新增执行任务
        handleCreateTask() {
            this.clearPolling();
            this.$emit('to-create-task', 'create');
        },
        // 批量启动任务
        async handleBetchStart(selectRowIds) {
            if (!selectRowIds?.length) return this.$hMessage.info('请选择一条或多条规则');

            this.startRuleList = this.startRuleList.concat(selectRowIds);
            // 调用勾选项启动对比接口
            try {
                const res = await setCompareOperate({
                    action: 'start',
                    productId: this.productId,
                    ruleIds: selectRowIds
                });
                if (res.code === '200') {
                    this.$hMessage.success('启动成功！');
                } else if (res.code.length === 8) {
                    this.$hMessage.error({
                        content: res.message,
                        duration: 3
                    });
                }
            } finally {
                this.startRuleList = this.startRuleList.filter(item => !selectRowIds.includes(item));
                this.initData();
            }
        },
        // 批量停止任务
        async handleBetchStop(selectRowIds) {
            if (!selectRowIds?.length) return this.$hMessage.info('请选择一条或多条规则');

            // 调用勾选项启动对比接口
            try {
                const res = await setCompareOperate({
                    action: 'stop',
                    productId: this.productId,
                    ruleIds: selectRowIds
                });
                if (res.code === '200') {
                    this.$hMessage.success('停止校验中！');
                } else if (res.code.length === 8) {
                    this.$hMessage.error({
                        content: res.message,
                        duration: 3
                    });
                }
            } finally {
                this.initData();
            }
        },
        // 批量删除任务
        async handleBetchDel(selectRowIds) {
            if (!selectRowIds?.length) return this.$hMessage.info('请选择一条或多条规则');

            const obj = selectRowIds?.length > 1 ? '所选' : '此';
            // 删除二次确认
            this.$hMsgBoxSafe.confirm({
                title: `确定要删除${obj}校验任务`,
                content: `删除校验任务后，任务将从列表移除，同时任务所产生的执行历史不可追溯。请谨慎操作。`,
                onOk: async () => {
                    try {
                        const res = await deleteCompareRule({
                            productId: this.productId,
                            ruleIds: selectRowIds
                        });
                        if (res.code === '200') {
                            this.$hMessage.success('任务删除成功！');
                        } else if (res.code.length === 8) {
                            this.$hNotice.error({
                                title: '删除失败',
                                desc: res.message
                            });
                        }
                    } finally {
                        this.initData();
                    }
                }
            });

        },
        // 查看校验任务内容
        handleViewTaskContent(row) {
            const contentList = [
                {
                    label: '任务名称:',
                    text: row.ruleName
                },
                {
                    label: '源数据库:',
                    text: row.sourceDatabaseName
                },
                {
                    label: '目标数据库:',
                    text: row.targetDatabasesName
                },
                {
                    label: '校验类型:',
                    text: VERIFE_TYPE_LIST.find(e => e.value === row.ruleType)?.label ?? '-'
                },
                {
                    label: '校验内容:',
                    text: row?.verificationContent
                }
            ];
            this.ruleDetail = {
                status: true,
                title: '查看校验任务内容',
                contentList: contentList
            };
        },
        // 查看执行记录
        handleViewRecord(row) {
            this.recordInfo = {
                status: true,
                ...row
            };
        },
        // 处理校验结果交互 - 异常\不通过：查看结果
        handlecompareResult(row) {
            this.resultInfo = {
                status: true,
                ...row
            };
        },
        // 查看表记录总数比对结果
        // 下载对比详情
        async downloadCompareResult(row) {
            const fileName = '';
            try {
                const res = await downloadCompareResult({ execId: row?.execId });
                const objUrl = window.URL.createObjectURL(new Blob([res], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' }));
                // 通过创建a标签实现
                const link = document.createElement('a');
                link.href = objUrl;
                // 对下载的文件命名
                link.setAttribute('download', `${fileName}-${row.startTime}-比对详情.xls`);
                document.body.appendChild(link);
                link.click();
                document.body.removeChild(link);
                window.URL.revokeObjectURL(objUrl);
            } catch (err) {
                console.error(err);
            }
        },

        // 根据执行进度判断展示图标
        getCompareProgressStatus(progress, compareResult) {
            if (compareResult === 'running') {
                return 'loading';
            } else if (compareResult === 'succeeded' && progress == 100) {
                return 'successGray';
            } else {
                return 'offline';
            }
        },
        // 表格勾选项变化
        tableSelectChange(selection) {
            this.selectRowIds = selection.map(e => { return e?.ruleId; });
        }

    }
};
</script>

<style lang="less" scoped>
.query-table-box {
    width: 100%;
    height: 100%;

    .table-title {
        font-size: 14px;
        color: var(--font-color);
        padding-right: 10px;
    }

    .table-describe {
        font-size: 12px;
        color: var(--font-opacity-color);

        span {
            font-weight: 600;
        }
    }
}
</style>
