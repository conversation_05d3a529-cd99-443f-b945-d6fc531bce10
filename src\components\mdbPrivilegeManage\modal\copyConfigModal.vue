<!-- 复制配置 -->
<template>
    <div>
        <h-msg-box-safe v-model="modalData.status" :mask-closable="false" title="复制配置" width="600"
        @on-open="getCollections">
            <h-form ref="formValidate" :model="formValidate" :label-width="150">
                <h-form-item :label="`${modalData.clusterName} 配置复制至` " prop="clusterName">
                    <h-select v-model="formValidate.clusterIds" placeholder="请选择" :positionFixed="true"  :transfer="true"  :clearable="false" multiple>
                        <h-option v-for="item in modalData.clusterList" :key="item.id" :value="item.id">{{item.title}}</h-option>
                    </h-select>
                </h-form-item>
            </h-form>
            <template v-slot:footer>
                <a-button @click="modalData.status = false">取消</a-button>
                <a-button type="primary" :loading="loading" @click="submitConfig">确认</a-button>
            </template>
        </h-msg-box-safe>
    </div>
</template>

<script>
import aButton from '@/components/common/button/aButton';
export default {
    name: 'AddOrEditUser',
    props: {
        modalInfo: {
            type: Object,
            default: null
        }
    },
    data() {
        return {
            modalData: this.modalInfo,
            formValidate: {
                clusterIds: []
            },
            loading: false,
            showPassWord: false
        };
    },
    methods: {
        getCollections() {
            this.$refs['formValidate'].resetFields();
        },
        submitConfig() {
            this.$refs['formValidate'].validate((valid) => {
                if (valid) {
                    this.$emit('copy-config', this.formValidate.clusterIds);
                    this.modalData.status = false;
                }
            });
        }
    },
    components: { aButton }
};
</script>
