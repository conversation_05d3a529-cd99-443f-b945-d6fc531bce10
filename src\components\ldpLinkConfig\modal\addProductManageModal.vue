<!-- 对接产品配置中心弹窗 -->
<template>
    <div>
        <h-msg-box-safe
            v-model="modalData.status"
            :mask-closable="false"
            title="对接产品配置中心"
            width="950"
            top="10"
            :closable="false"
            :height="formItem.configInfo.switch ? 404 : 318"
            class-name="add-modal"
            >
            <h-form
                v-if="!nextStatus"
                ref="formItem"
                :model="formItem"
                :label-width="120"
                :rules="ruleValidate"
                >
                <h-form-item
                    label="产品类型："
                    prop="productType"
                    >
                    <h-radio-group
                        v-model="formItem.productType"
                        type="button"
                        @on-change="changeProductType">
                        <h-radio
                            v-for="item in productTypeList" :key="item.key"
                            :label="item.key"
                            :text="item.name">
                        </h-radio>
                    </h-radio-group>
                </h-form-item>

                <h-form-item
                    v-if="formItem.productType && (formItem.productType !== 'ldpRcm')"
                    label="业务系统："
                    prop="bizSysTypes"
                    style="padding-top: 10px;">
                    <h-checkbox-group
                        v-model="formItem.bizSysTypes"
                        type="button">
                        <h-checkbtn
                            v-for="item in bizSysTypesList" :key="item.key"
                            :value="item.key"
                            :label="item.name">
                        </h-checkbtn>
                    </h-checkbox-group>
                </h-form-item>

                <h-form-item
                    v-show="formItem.productType !== 'thirdService'"
                    label="配置中心类型："
                    prop="configSourceType" required>
                    <h-select
                        v-model="formItem.configSourceType"
                        setDefSelect
                        :clearable="false">
                        <h-option value="zookeeper">zookeeper</h-option>
                    </h-select>
                </h-form-item>

                <div v-show="formItem.productType !== 'thirdService'">
                    <h-form-item
                        label="配置中心地址："
                        prop="configInfo.zkAddr"
                        required>
                        <h-input
                            v-model.trim="formItem.configInfo.zkAddr"
                            placeholder="请输入服务集群地址"
                            @on-blur="blurZkInput">
                        </h-input>
                    </h-form-item>
                    <p class="form-tip">使用IP:PORT或域名:PORT形式，多个地址使用英文逗号分隔</p>

                    <h-form-item
                        label="配置身份信息："
                        style="margin-bottom: 8px;"
                        prop="configInfo.switch"
                        >
                        <h-switch
                            v-model.trim="formItem.configInfo.switch"
                        >
                        </h-switch>
                    </h-form-item>
                    <div v-if="formItem.configInfo.switch">
                        <h-form-item
                            label="用户名："
                            prop="configInfo.zkUserName"
                            required
                        >
                            <h-input
                                v-model.trim="formItem.configInfo.zkUserName"
                                placeholder="请输入用户名"
                                @on-blur="blurZkInput"
                            >
                            </h-input>
                        </h-form-item>
                        <h-form-item
                            label="密码："
                            prop="configInfo.zkPassword"
                            required
                        >
                            <h-input
                                v-model.trim="formItem.configInfo.zkPassword"
                                placeholder="请输入密码"
                                :type="showPwd ? 'text':'password'"
                                :icon="showPwd ? 'browse_fill' : 'eye-disabled'"
                                @on-click="toggleShowPwd"
                                @on-blur="blurZkInput"
                            >
                            </h-input>
                        </h-form-item>
                    </div>
                </div>
                <h-form-item
                    v-show="formItem.productType === 'ldpRcm'"
                    label="配置节点对接："
                    prop="createConfigPath"
                    required>
                    <h-radio-group
                        v-model="formItem.createConfigPath"
                        @on-change="changeConnectMethod">
                        <h-radio text="创建配置节点" label="create"></h-radio>
                        <h-radio text="对接配置节点" label="connect"></h-radio>
                    </h-radio-group>
                </h-form-item>

                <div v-show="formItem.productType !== 'thirdService'">
                    <h-form-item v-if="formItem.productType === 'ldpRcm' && formItem.createConfigPath === 'create'"
                        label="配置节点路径：" prop="configInfo.path" required :validRules="zkPathRule">
                        <div class="path-name-content">
                            <span class="first-content">/ldp/</span>
                            <h-input v-model.trim="formItem.configInfo.path" class="input-content"></h-input>
                            <span class="last-content">/config/{{ formItem.configInfo.path }}</span>
                        </div>
                    </h-form-item>
                    <h-form-item v-else label="配置节点路径：" prop="configInfo.paths" required>
                        <h-select
                            v-model="formItem.configInfo.paths"
                            multiple
                            placement="top"
                            :loading="selectLoading"
                            :clearable="false">
                            <h-option
                                v-for="item in pathList"
                                :key="item.value"
                                :value="item.value"
                                >{{ item.label }}
                            </h-option>
                        </h-select>
                    </h-form-item>
                </div>

                <div v-if="formItem.productType === 'ldpRcm' && formItem.createConfigPath === 'create'">
                    <h-form-item label="上传配置文件：">
                        <h-upload type="drag" action="" :before-upload="handleUpload" accept=".json">
                            <div class="upload-text">
                                <h-icon name="upload" size="40"></h-icon>
                                <p>点击或将文件拖拽到这里上传</p>
                            </div>
                            <div v-if="file !== null" class="file-load">
                                <div :title="file.name" class="file-name">上传文件：{{ file.name }}</div>
                                <h-button v-if="loadStatus === 0" type="text" :loading="!loadStatus">上传中</h-button>
                                <div v-if="loadStatus === 2" style="color: var(--success-color); margin: 0 10px;">上传成功</div>
                                <div v-if="loadStatus === 3" style="color: var(--error-color); margin: 0 10px;">上传失败</div>
                            </div>
                        </h-upload>
                    </h-form-item>
                    <p class="form-tip">如果已存在配置好的本地配置文件，可以选择上传，否则系统默认创建空的配置文件</p>
                </div>

                <!-- <h-form-item label="产品运行环境："
                    prop="productRuntimeEnvironment"
                    required>
                    <h-select
                        v-model="formItem.productRuntimeEnvironment"
                        placeholder="请选择"
                        :clearable="false"
                        :setDefSelect="true"
                        :positionFixed="true" >
                        <h-option
                            v-for="item in runtimeEnvironmentList" :key="item.key"
                            :value="item.key">
                            {{ item.name }}
                        </h-option>
                    </h-select>
                </h-form-item>
                <p>测试环境下可支持该产品节点的性能测试特性，生产环境该功能处于关闭状态。</p> -->
            </h-form>

            <template v-slot:footer>
                <a-button :disabled="loading" @click="modalData.status = false">取消</a-button>
                <a-button type="primary" :loading="loading" @click="submitConfig">确定</a-button>
            </template>
        </h-msg-box-safe>
        <check-add-prodcut-manage-modal
            v-if="checkProductModalData.status"
            :modalInfo="checkProductModalData"
            :height="formItem.configInfo.switch ? 403 : 317"
            @selectMenuChange="selectMenuChange"
            @close="closeAllModal"
         />
    </div>
</template>

<script>
import { isJSON } from '@/utils/utils';
import { addOrUpdateProduct } from '@/api/httpApi';
import { getZkProductPaths, checkProductInstance } from '@/api/productApi';
import { stringLengthRule } from '@/utils/validate';
import aButton from '@/components/common/button/aButton';
import checkAddProdcutManageModal from './checkAddProductManageModal.vue';

const zkPathRule = function (_rule, values, callback) {
    if (values.match(/^[ ]*$/)) return callback(new Error('输入不能为空'));
    if (values.length) {
        const regx = /^[a-zA-Z0-9_-]{1,255}$/;
        if (!regx.test(values)) {
            return callback(new Error('允许输入a-z、A-Z、0-9、_、-；长度不超过255'));
        }
    }
    callback();
};
export default {
    props: {
        modalInfo: {
            type: Object,
            default: null
        }
    },
    data() {
        return {
            nextStatus: false,
            modalData: this.modalInfo,
            loading: false,
            stringRule: [
                { test: stringLengthRule(50), trigger: 'change, blur' }
            ],
            zkPathRule: [{ test: zkPathRule, trigger: 'change, blur' }],
            pathList: [],
            selectLoading: false,
            ruleValidate: {
                productType: [{ required: true, message: '请选择产品类型', trigger: 'change' }],
                bizSysTypes: [{ required: true, type: 'array', message: '至少选择一个业务系统', trigger: 'change' }]
            },
            formItem: {
                productType: '',
                bizSysTypes: [],
                configSourceType: 'zookeeper',
                createConfigPath: 'connect',
                configInfo: {
                    zkAddr: '',
                    path: '',
                    paths: []
                }
            },
            configFileString: '',
            loadStatus: 1,
            file: null,
            checkProductModalData: {
                status: false
            },
            showPwd: false
        };
    },
    computed: {
        runtimeEnvironmentList() {
            const list = [];
            Object.keys(this.$store?.state?.apmDirDesc?.runtimeEnvironmentDict || {}).forEach(ele => {
                list.push({
                    key: ele,
                    name: this.$store?.state?.apmDirDesc?.runtimeEnvironmentDict[ele]
                });
            });
            return list;
        },
        productTypeList() {
            const list = [];
            Object.keys(this.$store?.state?.apmDirDesc?.bizProductTypeDict || {}).forEach(ele => {
                if (ele !== 'ldpApm') {
                    list.push({
                        key: ele,
                        name: this.$store?.state?.apmDirDesc?.bizProductTypeDict[ele]
                    });
                }
            });
            return list;
        },
        bizSysTypesList() {
            const list = [];
            const dicName = `${this.formItem.productType}BizSysTypeDict`;
            Object.keys(this.$store?.state?.apmDirDesc?.[dicName] || {}).forEach(ele => {
                list.push({
                    key: ele,
                    name: this.$store?.state?.apmDirDesc?.[dicName][ele]
                });
            });
            return list;
        }
    },
    mounted() {
    },
    methods: {
        // 切换产品类型
        changeProductType(val) {
            this.formItem.productType = '';
            this.formItem.bizSysTypes = [];
            this.$nextTick(() => {
                this.formItem.productType = val;
                this.$refs['formItem'].resetValidate();
            });
        },
        toggleShowPwd() {
            this.showPwd = !this.showPwd;
        },
        // rcm类型 - 切换对接方式（创建、对接）
        changeConnectMethod() {
            // 清除配置路径校验报错
            this.$nextTick(() => {
                if (this.formItem.createConfigPath === 'create') {
                    this.$refs['formItem'].resetValidateField('configInfo.path');
                } else {
                    this.$refs['formItem'].resetValidateField('configInfo.paths');
                }
            });
        },

        // 修改zk地址后刷新路径列表
        blurZkInput() {
            this.$refs['formItem'].validateField('configInfo.zkAddr', (err) => {
                if (!err) {
                    if (!this.formItem.configInfo.switch) {
                        this.getZkProductPaths();
                        return;
                    }
                    this.$refs['formItem'].validateField('configInfo.zkUserName', nameErr => {
                        if (!nameErr) {
                            this.$refs['formItem'].validateField('configInfo.zkPassword', pwdErr => {
                                if (!pwdErr) {
                                    this.getZkProductPaths();
                                }
                            });
                        }
                    });
                }
            });
        },
        // 获取zookeeper LDP产品配置路径
        async getZkProductPaths() {
            this.selectLoading = true;
            this.pathList = [];
            try {
                const params = {
                    zkAddr: this.formItem.configInfo.zkAddr
                };
                if (this.formItem.configInfo.switch && params.productType !== 'thirdService') {
                    params.zkUserName = this.formItem.configInfo.zkUserName;
                    params.zkPassword = this.formItem.configInfo.zkPassword;
                }
                const res = await getZkProductPaths(params);
                if (res.code === '200' && params.zkAddr === this.formItem.configInfo.zkAddr) {
                    Array.isArray(res.data) && res.data.forEach(item => {
                        this.pathList.push({
                            value: item,
                            label: item
                        });
                    });
                } else if (res.code.length === 8) {
                    this.$hMessage.error(res.message);
                }
            } finally {
                this.selectLoading = false;
            }
        },

        // 文件上传处理
        handleUpload(file) {
            this.file = file;
            this.loadStatus = 0;
            this.getActionData(file);
            return false;
        },
        // 解析获取文件内容
        getActionData (file) {
            // 文件大小校验 最大16MB
            if (file.size > 16 * 1024 * 1024) {
                this.loadStatus = 3;
                this.configFileString = '';
                this.$hMessage.error('文件大小超出16MB');
                return;
            }
            const that = this;
            const reader = new FileReader();// 新建一个FileReader
            reader.readAsText(file, 'UTF-8');// 读取文件
            reader.onload = function (evt) { // 读取完文件之后会回来这里
                const fileString = evt.target.result; // 读取文件内容
                if (isJSON(fileString)) {
                    that.configFileString = fileString;
                    that.loadStatus = 2;
                } else {
                    that.loadStatus = 3;
                    that.configFileString = '';
                    that.$hMessage.error('文件格式校验不通过,请校验文件格式并重新拖动或点击文件上传');
                }
            };
        },

        // 产品创建-确认
        submitConfig() {
            if (this.formItem.productType === 'thirdService') {
                this.$refs['formItem'].validateField('bizSysTypes', (err) => {
                    if (!err) {
                        this.addOrUpdateProduct();
                    }
                });
            } else {
                this.$refs['formItem'].validate((valid) => {
                    if (valid) {
                        this.addOrUpdateProduct();
                    }
                });
            }
        },
        async addOrUpdateProduct() {
            this.loading = true;
            const addParams = {};
            const pathName = this.formItem?.configInfo?.path;
            if (this.formItem.productType === 'ldpRcm' && this.formItem.createConfigPath === 'create') {
                addParams.configInfo = {
                    zkAddr: this.formItem?.configInfo?.zkAddr,
                    paths: [`/ldp/${pathName}/config/${pathName}`]
                };
                addParams.config = this.configFileString;
            }
            try {
                let params = {
                    ...this.formItem,
                    createConfigPath: this.formItem.productType === 'ldpRcm' && this.formItem.createConfigPath === 'create',
                    ...addParams
                };
                // 过滤掉不需要的参数
                if (this.formItem.productType === 'thirdService') {
                    params = {
                        productType: this.formItem.productType,
                        bizSysTypes: this.formItem.bizSysTypes
                    };
                }

                const createCallback = async () => {
                    this.loading = true;
                    const res = await addOrUpdateProduct(params).finally(() => {
                        this.loading = false;
                    });
                    if (res.success) {
                        localStorage.removeItem('productInstNo');
                        // 产品类型为三方服务 跳转三方服务配置页面
                        if (params.productType === 'thirdService') {
                            this.$hCore.navigate(`/tripartiteServiceList`);
                        } else {
                            this.$emit('reload', params.productType);
                            this.$hMessage.success('添加成功!');
                            this.modalInfo.status = false;
                        }
                    }
                };

                const existProducts = await checkProductInstance(params).finally(() => {
                    this.loading = false;
                });
                if (existProducts?.data?.length != 0) {
                    this.checkProductModalData = {
                        status: true,
                        data: existProducts.data.map(item => ({ ...item, configInfo: JSON.parse(item.configInfo) })),
                        configInfo: params.configInfo,
                        createCallback
                    };
                    return;
                }
                await createCallback();

            } finally {
                this.loading = false;
            }
        },
        /**
         * 关闭弹窗
         */
        closeAllModal() {
            this.modalInfo.status = false;
            this.checkProductModalData.status = false;
        },
        /**
         * 触发菜单点击
         */
        selectMenuChange(productInstNo) {
            this.$emit('selectMenuChange', productInstNo);
        }
    },
    components: {  aButton, checkAddProdcutManageModal }
};
</script>

<style lang="less" scoped>
.form-tip {
    margin: -20px 0 20px 120px;
    color: var(--font-opacity-color);
}

.file-load {
    display: flex;
    justify-content: center;
    margin: 0 10px;

    .file-name {
        max-width: 665px;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
    }
}

.path-name-content {
    display: flex;

    .first-content {
        min-width: 26px;
    }

    .input-content {
        width: 300px;
        min-width: 300px;
    }

    .last-content {
        margin-left: 5px;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
    }
}

.upload-text {
    width: 100%;
    min-width: 500px;
    padding: 10px 20px 0;
    color: var(--link-color);
}

/deep/ .h-form-item {
    .h-select-dropdown {
        margin-top: 0;
    }
}

/deep/ .add-modal {
    .h-modal-body {
        padding-top: 16px;
    }
}
</style>
