<template>
    <div class="main">
        <div class="title">
            <a-title
                :title="titleText"
                :class="boxContent === 'create' ? 'apm-title-create' : 'apm-title'">
                <template v-slot>
                    <div
                        v-show="boxContent === 'create'"
                        class="create-title">
                        <h-icon
                            name="arrow-left-c"
                            @on-click="handleCreateCancel">
                        </h-icon>
                        <span>创建数据校验任务</span>
                    </div>
                    <div
                        v-show="productList.length > 1"
                        class="product-select">
                        <h-select
                            v-model="productInstNo"
                            placeholder="请选择"
                            placement="bottom"
                            :disabled="boxContent === 'create'"
                            :positionFixed="true"
                            :clearable="false"
                            @on-change="checkProduct">
                            <h-option
                                v-for="item in productList"
                                :key="item.id"
                                :value="item.productInstNo">
                                {{ item.productName }}
                            </h-option>
                        </h-select>
                    </div>
                </template>
            </a-title>
        </div>
        <a-loading v-if="loading" style="z-index: 9;"></a-loading>

        <div class="product-box">
            <!-- 校验任务列表 -->
            <verification-task-list
                v-if="boxContent === 'table'"
                ref="task-list"
                :productId="productInstNo"
                @to-create-task="handleContentChange">
            </verification-task-list>

            <!-- 新增检验任务组件 -->
            <create-verification-task
                v-if="boxContent === 'create'"
                ref="task-create"
                :productId="productInstNo"
                @to-task-list="handleContentChange">
            </create-verification-task>
        </div>
    </div>
</template>

<script>
import _ from 'lodash';
import { mapState, mapActions } from 'vuex';
import aTitle from '@/components/common/title/aTitle';
import aLoading from '@/components/common/loading/aLoading';
import verificationTaskList from '@/components/ustTableVerification/verificationTaskList.vue';
import createVerificationTask from '@/components/ustTableVerification/createVerificationTask.vue';
export default {
    name: 'UstTableVerification',
    components: { aTitle, aLoading, verificationTaskList, createVerificationTask },
    data() {
        return {
            productInstNo: '', // 选中的产品
            tabName: 'countCompare', // tab默认选择
            loading: false,
            titleText: '数据校验',
            boxContent: 'table' // 内容区：表格|新增页面
        };
    },
    mounted() {
        this.init();
    },
    computed: {
        ...mapState({
            productList: state => {
                return state.product.productListLight || [];
            }
        }),
        productName() {
            return this.productList.find(o => o.id === this.productInstNo)?.productName;
        }
    },
    methods: {
        ...mapActions({ getProductList: 'product/getProductListLight' }),
        // 初始化页面
        async init() {
            try {
                this.loading = true;
                await this.getProductList({ filter: 'excludeLdpApm' });
                const productInstNo = localStorage.getItem('productInstNo');
                this.productInstNo = _.find(this.productList, ['productInstNo', productInstNo])?.productInstNo || this.productList?.[0]?.productInstNo;
            } catch (e) {
                console.error(e);
            } finally {
                this.loading = false;
            }
        },
        // 切换导航产品
        async checkProduct(item) {
            this.loading = true;
            localStorage.setItem('productInstNo', item);

            // 切换产品，回到列表页
            this.handleContentChange('table');
            setTimeout(() => {
                this.loading = false;
            }, 500);
        },

        // 切换展示 新增任务页|表格页
        handleContentChange(content) {
            this.boxContent = content;
            this.$nextTick(() => {
                if (content === 'create') {
                    this.titleText = '';
                    this.$refs['task-create'] && this.$refs['task-create'].initData();
                } else if (content === 'table') {
                    this.titleText = '数据校验';
                    this.$refs['task-list'] && this.$refs['task-list'].initData();
                }
            });
        },
        // 取消创建
        handleCreateCancel() {
            this.$refs['task-create'] && this.$refs['task-create'].handleCancel();
        }
    }
};
</script>

<style lang="less" scoped>

.main {
    overflow-x: auto;

    .title {
        min-width: 800px;

        .product-select {
            float: right;
            margin-right: 15px;
            top: 5px;
            min-width: 200px;
            width: auto;
        }

        .apm-title-create {
            &::before {
                display: none;
            }
        }
    }

    .create-title {
        float: left;

        .h-icon {
            cursor: pointer;
            margin-right: 8px;

            &:hover {
                color: var(--link-color);
            }
        }
    }

    .product-box {
        min-width: 800px;
        height: calc(100% - 53px);
        margin-top: 10px;
        border-radius: 4px;
        background: var(--wrapper-color);
    }
}
</style>
