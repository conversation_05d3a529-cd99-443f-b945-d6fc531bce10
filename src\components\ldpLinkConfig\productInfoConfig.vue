<template>
    <div ref="tab-box" class="tab-box">
        <a-loading v-if="loading"></a-loading>
        <div v-else>
            <a-title title="产品信息">
                <slot>
                    <p class="slot-text"><span>产品实例：</span>{{ productInfo.id }}</p>
                </slot>
            </a-title>
            <div class="product-info">
                <p>
                    <span>产品名称：</span>{{ overviewInfo.productName }}
                    <span><h-icon name="t-b-modify" @on-click="updateNameModal(0)"></h-icon></span>
                </p>
                <p>
                    <span>产品类型：</span>{{ productTypeDict[productInfo.productType] || '-' }}
                </p>
                <p>
                    <span>关联业务系统：</span>{{ bizSysTypesConvert || '-' }}
                    <span><h-icon name="t-b-modify" @on-click="updateNameModal(3)"></h-icon></span>
                </p>
                <p>
                    <span>产品配置中心：</span>{{ overviewInfo.configSourceType || '-' }}
                </p>
            </div>
            <!-- 已托管配置信息 -->
            <obs-table
                :title="trusteeshipDeploy.title"
                :tableData="trusteeshipDeploy.tableData"
                :columns="trusteeshipDeploy.columns">
            </obs-table>

            <!-- 汇总总览信息 - RCM产品除外 -->
            <div v-if="productInfo.productType !== 'ldpRcm'" class="info-group">
                <div v-for="(item, index) in tableGroupInfo" :key="index" class="info">
                    <obs-table
                        :title="item.title"
                        :tableData="item.tableData"
                        :columns="item.columns"
                        :maxHeight="210"
                        :width="boxWidth / 3" />
                </div>
            </div>
            <!-- RCM 产品汇总信息 -->
            <div v-else class="rcm-info-group">
                <div v-if="rcmInfo.topicStatistics" class="rcm-info">
                    <a-title title="主题"></a-title>
                    <p><span>主题数：</span>
                        {{rcmInfo.topicStatistics.topicNum}}
                    </p>
                    <p><span>主题引用模板：</span>
                        {{rcmInfo.topicStatistics.topicTemplateNum}}
                    </p>
                </div>
                <div v-if="rcmInfo.singletonContextStatistics" class="rcm-info">
                    <a-title title="单例上下文"></a-title>
                    <p><span>单例上下文数：</span>
                        {{rcmInfo.singletonContextStatistics.contextNum}}
                    </p>
                    <p><span>单例上下文引用模板：</span>
                        {{rcmInfo.singletonContextStatistics.contextTemplateNum}}
                    </p>
                </div>
                <div v-if="rcmInfo.clusterContextStatistics" class="rcm-info">
                    <a-title title="集群上下文"></a-title>
                    <p><span>集群上下文数：</span>
                        {{rcmInfo.clusterContextStatistics.contextNum}}
                    </p>
                    <p><span>集群上下文引用模板：</span>
                        {{rcmInfo.clusterContextStatistics.contextTemplateNum}}
                    </p>
                </div>
                <div v-if="rcmInfo.rcmTemplateStatistics" class="rcm-info">
                    <a-title title="模板"></a-title>
                    <p><span>主题模板数：</span>
                        {{rcmInfo.rcmTemplateStatistics.transportTemplateNum}}
                    </p>
                    <p><span>通用上下文模板：</span>
                        {{rcmInfo.rcmTemplateStatistics.singleTenContextTemplateNum}}
                    </p>
                    <p><span>集群上下文模板：</span>
                        {{rcmInfo.rcmTemplateStatistics.clusterContextTemplateNum}}
                    </p>
                </div>
            </div>
        </div>

        <!-- 修改产品信息、仲裁信息弹窗 -->
        <product-manage-name-modal v-if="manageNameInfo.status" :modalInfo="manageNameInfo" @update="reload($event)" />
    </div>
</template>

<script>
import { mapState } from 'vuex';
import { getProductOverview, getProductInstances } from '@/api/productApi';
import { getRcmOverview, getRcmInstanceList } from '@/api/rcmApi';
import aTitle from '@/components/common/title/aTitle';
import obsTable from '@/components/common/obsTable/obsTable';
import aLoading from '@/components/common/loading/aLoading';
import productManageNameModal from '@/components/ldpLinkConfig/modal/productManageNameModal.vue';
export default {
    name: 'ProductInfoConfig',
    components: { aTitle, obsTable, aLoading, productManageNameModal },
    props: {
        productInfo: {
            type: Object,
            default: () => {}
        }
    },
    data() {
        return {
            boxWidth: 100,
            overviewInfo: {},
            manageNameInfo: {
                status: false,
                productName: '',
                id: '',
                type: 0,
                configSourceType: 'zookeeper',
                configInfo: {
                    zkAddr: '',
                    paths: []
                }
            },
            tableGroupInfo: [
                {
                    title: {
                        label: '应用'
                    },
                    columns: [
                        {
                            title: '应用类型',
                            key: 'instanceType',
                            render: (h, params) => {
                                return h(
                                    'div',
                                    this.apmDirDesc?.appTypeDictDesc?.[params.row.instanceType] || params.row.instanceType
                                );
                            }
                        },
                        {
                            title: '关联应用',
                            key: 'instanceNumber',
                            minWidth: 90
                        }
                    ],
                    tableData: this.productInfo?.instanceTypes || []
                },
                {
                    title: {
                        label: '集群'
                    },
                    columns: [
                        {
                            title: '集群名',
                            key: 'clusterName'
                        },
                        {
                            title: '集群类型',
                            key: 'clusterType',
                            render: (h, params) => {
                                return h(
                                    'div',
                                    this.apmDirDesc?.clusterTypeDict?.[params.row.clusterType] || params.row.clusterType
                                );
                            }
                        },
                        {
                            title: '关联应用',
                            key: 'instanceNumber',
                            minWidth: 90
                        }
                    ],
                    tableData: this.productInfo?.appClusters || []
                },
                {
                    title: {
                        label: '服务'
                    },
                    columns: [
                        {
                            title: '服务类型',
                            key: 'serviceName'
                        },
                        {
                            title: '关联应用',
                            key: 'instanceNumber',
                            minWidth: 90
                        }
                    ],
                    tableData: this.productInfo?.services || []
                },
                {
                    title: {
                        label: '服务器'
                    },
                    columns: [
                        {
                            title: '主机名',
                            key: 'hostName',
                            minWidth: 110
                        },
                        {
                            title: '主机别名',
                            key: 'hostNameAlias',
                            minWidth: 110,
                            formatMethod: (row) => row?.hostNameAlias || '-'
                        },
                        {
                            title: 'IP地址/域名',
                            key: 'ips',
                            minWidth: 110,
                            render: (h, { row }) => {
                                const ips = row.ips ? row.ips?.join(',') : '-';
                                return h('div', ips);
                            }
                        },
                        {
                            title: '关联应用',
                            key: 'instanceNumber'
                        }
                    ],
                    tableData: this.productInfo?.hosts || []
                },

                {
                    title: {
                        label: '机房'
                    },
                    columns: [
                        {
                            title: '机房名',
                            key: 'roomName'
                        },
                        {
                            title: '机房别名',
                            key: 'roomNameAlias',
                            formatMethod: (row) => row?.roomNameAlias || '-'
                        },
                        {
                            title: '关联应用',
                            key: 'instanceNumber',
                            minWidth: 90
                        }
                    ],
                    tableData: this.productInfo?.rooms || []
                },
                {
                    title: {
                        label: '应用分片'
                    },
                    columns: [
                        {
                            title: '分片号',
                            key: 'shardingNo'
                        },
                        {
                            title: '分片名',
                            key: 'shardingName'
                        },
                        {
                            title: '关联应用',
                            key: 'instanceNumber',
                            minWidth: 90
                        }
                    ],
                    tableData: this.productInfo?.shardings || []
                }
            ],
            trusteeshipDeploy: {
                title: {
                    label: '已托管配置'
                },
                columns: [
                    { title: '配置节点名称', key: 'name' },
                    { title: '配置根目录', key: 'path', minWidth: 300 },
                    { title: '配置提供者', key: 'sourceType' },
                    { title: '配置服务地址', key: 'serviceAddress',
                        render: (h, params) => {
                            return h('div', [
                                h('Poptip', {
                                    class: 'apm-poptip',
                                    props: {
                                        title: '配置服务地址',
                                        placement: 'left-end',
                                        positionFixed: true
                                    }
                                }, [
                                    h('Button',
                                        {
                                            props: {
                                                size: 'small',
                                                type: 'text'
                                            }
                                        },
                                        '查看'
                                    ),
                                    h('div', {
                                        slot: 'content'
                                    }, [
                                        params.row.serviceAddress?.split(',')?.map(item => {
                                            return h('p', {
                                                style: {
                                                    padding: '4px 0'
                                                }
                                            }, [
                                                item
                                            ]);
                                        })
                                    ])
                                ])
                            ]);
                        }
                    },
                    {
                        title: '操作',
                        key: 'action',
                        render: (h, params) => {
                            return h('div', [
                                h('Button',
                                    {
                                        props: {
                                            size: 'small',
                                            type: 'text'
                                        },
                                        on: {
                                            click: () => {
                                                this.handleConfigJump(params.row);
                                            }
                                        }
                                    },
                                    '配置'
                                )
                            ]);
                        }
                    }
                ],
                tableData: []
            },
            rcmInfo: {
                topicStatistics: {},
                singletonContextStatistics: {},
                clusterContextStatistics: {},
                rcmTemplateStatistics: {}
            },
            loading: false
        };
    },
    mounted() {
        this.resizeObserver = new ResizeObserver(entries => {
            this.fetTableHeight();
        });
        this.resizeObserver.observe(this.$refs['tab-box']);
    },
    beforeDestroy() {
        this.resizeObserver.disconnect(this.$refs['tab-box']);
    },
    methods: {
        // 设置table高度
        fetTableHeight() {
            this.boxWidth = this.$refs['tab-box']?.offsetWidth - 90;
        },
        // 清理总览数据
        cleanOverviewData() {
            this.handlePageData();
        },
        async initData() {
            this.clearPageData();
            this.loading = true;
            try {
                await this.getProductInstances();
                await this.getProductOverview();
                this.productInfo.productType === 'ldpRcm' && await this.getRcmCountStatistics();
            } finally {
                this.loading = false;
            }
        },
        // 清空页面数据
        clearPageData() {
            this.instances = [];
            this.handlePageData();
        },
        // 获取产品总览信息
        async getProductOverview() {
            this.cleanOverviewData();
            const res = await getProductOverview({ productId: this.productInfo.id });
            if (res.code === '200') {
                this.handlePageData(res.data);
            }
        },
        // 获取应用节点列表
        async getProductInstances() {
            const res = await getProductInstances({ productId: this.productInfo.id });
            if (res.code === '200') {
                this.instances = res?.data?.instances;
            }
        },
        // 整理渲染页面数据
        handlePageData(data) {
            this.overviewInfo = {
                productName: data?.productName,
                bizSysTypes: data?.bizSysTypes,
                configSourceType: data?.configSourceType,
                configInfo: data?.configInfo
            };
            this.trusteeshipDeploy.tableData = data?.configs || [];
            this.tableGroupInfo[0].tableData = data?.instanceTypes || [];
            this.tableGroupInfo[1].tableData = data?.appClusters || [];
            this.tableGroupInfo[2].tableData = data?.services || [];
            this.tableGroupInfo[3].tableData = data?.hosts || [];
            this.tableGroupInfo[4].tableData = data?.rooms || [];
            this.tableGroupInfo[5].tableData = data?.shardings || [];
        },
        // 展示修改产品节点名称弹窗
        updateNameModal(type) {
            const configInfo = this.overviewInfo?.configInfo || {
                zkAddr: '',
                paths: []
            };
            this.manageNameInfo = {
                status: true,
                id: this.productInfo.id,
                productName: this.overviewInfo.productName,
                productType: this.productInfo.productType,
                type: type,
                bizSysTypes: this.overviewInfo.bizSysTypes,
                configSourceType: this.overviewInfo.configSourceType,
                configInfo: configInfo
            };
        },
        // 获取rcm配置列表
        async getRcmIdList() {
            let rcmIdList = [];
            try {
                const res = await getRcmInstanceList({
                    productId: this.productInfo?.id
                });
                if (res.code === '200') {
                    rcmIdList = res?.data || [];
                }
            } catch (err) {
                console.error(err);
            }
            return rcmIdList;
        },
        // 获取rcm主题、上下文、模板统计
        async getRcmCountStatistics() {
            // 构造请求
            const rcmList = await this.getRcmIdList();
            const promiseArr = [];
            Array.isArray(rcmList) && rcmList.forEach(element => {
                const param = {
                    id: element.id
                };
                const promise = getRcmOverview(param);
                promiseArr.push(promise);
            });

            this.rcmInfo = {
                topicStatistics: {},
                singletonContextStatistics: {},
                clusterContextStatistics: {},
                rcmTemplateStatistics: {}
            };
            // 获取配置请求rcm配置总览接口
            try {
                const values = await Promise.all(promiseArr);
                values.forEach(value => {
                    if (value.code === '200' && value.data) {
                        const {
                            topicStatistics,
                            singletonContextStatistics,
                            clusterContextStatistics,
                            rcmTemplateStatistics
                        } = value.data;

                        // 累加 topicStatistics
                        Object.keys(topicStatistics || {}).forEach(key => {
                            if (!this.rcmInfo.topicStatistics[key]) {
                                this.rcmInfo.topicStatistics[key] = 0;
                            }
                            this.rcmInfo.topicStatistics[key] += topicStatistics[key];
                        });

                        // 累加 singletonContextStatistics
                        Object.keys(singletonContextStatistics || {}).forEach(key => {
                            if (!this.rcmInfo.singletonContextStatistics[key]) {
                                this.rcmInfo.singletonContextStatistics[key] = 0;
                            }
                            this.rcmInfo.singletonContextStatistics[key] += singletonContextStatistics[key];
                        });

                        // 累加 clusterContextStatistics
                        Object.keys(clusterContextStatistics || {}).forEach(key => {
                            if (!this.rcmInfo.clusterContextStatistics[key]) {
                                this.rcmInfo.clusterContextStatistics[key] = 0;
                            }
                            this.rcmInfo.clusterContextStatistics[key] += clusterContextStatistics[key];
                        });

                        // 累加 rcmTemplateStatistics
                        Object.keys(rcmTemplateStatistics || {}).forEach(key => {
                            if (!this.rcmInfo.rcmTemplateStatistics[key]) {
                                this.rcmInfo.rcmTemplateStatistics[key] = 0;
                            }
                            this.rcmInfo.rcmTemplateStatistics[key] += rcmTemplateStatistics[key];
                        });
                    } else {
                        console.warn('Unexpected response format or error in response', value);
                    }
                });
            } catch (err) {
                console.error(err);
            }
        },
        // 已托管配置跳转rcm配置
        handleConfigJump(row) {
            this.$hCore.navigate('/rcmDeploy', { history: true }, {
                rcmId: row?.id
            });
        },
        reload(id) {
            this.$emit('reload', id);
        }
    },
    computed: {
        ...mapState({
            apmDirDesc: state => {
                return state.apmDirDesc || {};
            }
        }),
        productTypeDict() {
            return this.$store?.state?.apmDirDesc?.productTypeDict || {};
        },
        bizSysTypesConvert() {
            const dicName = `${this.productInfo.productType}BizSysTypeDict`;
            const bizSysEnum = this.$store?.state?.apmDirDesc?.[dicName] || {};
            const bizSysTypes = (this.overviewInfo.bizSysTypes || []).map(item => {
                return bizSysEnum[item];
            });
            return bizSysTypes.join(', ');
        }
    }
};
</script>

<style lang="less" scoped>
@import url("@/assets/css/poptip-1.less");

.tab-box {
    position: relative;
    width: 100%;
    height: calc(100% - 5px);
    cursor: pointer;

    .apm-title {
        margin-top: 5px;
        background: var(--wrapper-color);
    }

    .btn-add {
        position: absolute;
        right: 6px;
        top: -1px;
    }

    .slot-text {
        position: absolute;
        right: 10px;
        top: 0;
        font-size: 12px;
    }

    .product-info {
        background: var(--wrapper-color);
        display: flex;
        flex-wrap: wrap;
        align-content: flex-start;
        width: 100%;
        padding: 0 10px 10px;
        word-wrap: break-word;
        overflow: auto;

        .iconfont {
            color: var(--icon-color);
        }

        .iconfont:hover {
            color: var(--icon-hover);
        }

        .iconfont:active {
            color: var(--icon-press-down);
        }

        & > p {
            color: var(--font-color);
            padding-top: 10px;
            padding-right: 30px;
            line-height: 15px;
            white-space: nowrap;
            word-wrap: break-word;
            overflow: hidden;
            text-overflow: ellipsis;

            & > span {
                padding-left: 10px;
                color: var(--font-opacity-color);
            }
        }
    }

    .rcm-info-group {
        display: flex;
        flex-direction: row;

        .rcm-info {
            flex: 1;
            margin: 5px;
            padding: 0 0 20px;

            & > p {
                color: var(--font-color);
                padding-top: 20px;
                line-height: 15px;

                & > span {
                    padding-left: 13px;
                    color: var(--font-opacity-color);
                }
            }

            & > .apm-title {
                white-space: nowrap;
                overflow: hidden;
                text-overflow: ellipsis;
            }
        }
    }

    .info-group {
        width: 100%;
        display: grid;
        grid-template-columns: repeat(3, 1fr);  //同上

        .info {
            height: 280px;
            margin-left: 15px;
            padding: 0 0 10px;
            border-radius: 5px;
        }

        .info:first-child,
        .info:nth-child(4) {
            margin-left: 0;
        }
    }

    /deep/ .h-poptip-body-content {
        max-height: 150px;
        padding-right: 5px;
    }

    /deep/ .h-poptip-inner {
        background-color: var(--poptip-bg-color);
    }

    /deep/ .h-poptip-title-inner {
        color: #fff;
    }
}
</style>
