<template>
    <div class="tab-box">
        <a-loading v-if="loading"></a-loading>
        <div v-else>
            <!-- 吞吐信息 -->
            <obs-table ref="table" :title="title" :tableData="tableData" showTitle :columns="columns" notSetWidth autoHeadWidth
                highlightRow rowSelectOnly :maxHeight="220" @select-change="selectChange" @on-current-change="tableRowcheckedChange"/>
             <!-- 吞吐详情 -->
            <div v-if="tableData.length && Object.keys(currentRow).length" class="detail-grid-box">
                <obs-title :title="ThreadTitle" />
                <div class="detail-box">
                    <info-grid ref="redoDetail" :gridData="redoDetail" @select-change="selectChange"></info-grid>
                    <info-grid ref="netDetail" :gridData="netDetail" @select-change="selectChange"></info-grid>
                </div>
            </div>
        </div>
    </div>
</template>

<script>
import aLoading from '@/components/common/loading/aLoading';
import obsTitle from '@/components/common/title/obsTitle';
import infoGrid from '@/components/common/infoBar/infoGrid';
import obsTable from '@/components/common/obsTable/obsTable';
import { nsConvertTime, formatChartNumber } from '@/utils/utils';
import { getManagerProxy } from '@/api/mcApi';
export default {
    props: {
        nodeData: {
            type: Object,
            default: () => { }
        },
        pollTime: {
            type: Number,
            default: 0
        }
    },
    components: { aLoading, obsTitle, obsTable, infoGrid },
    data() {
        const threadColumns = [
            { title: '线程名', key: 'ThreadName', ellipsis: true },
            { title: '集群名', key: 'ClusterName', ellipsis: true },
            { title: '请求处理数量', key: 'ReqDealCount', ellipsis: true },
            { title: '请求处理的平均值', key: 'ReqTotalAvgNs', ellipsis: true },
            { title: '应答处理数量', key: 'RspDealCount', ellipsis: true },
            { title: '应答处理的平均值', key: 'RspTotalAvgNs', ellipsis: true },
            { title: '处理最小事务号', key: 'DealMinTransNo', ellipsis: true },
            { title: '处理最大事务号', key: 'DealMaxTransNo', ellipsis: true }
        ];
        return {
            loading: true,
            // 吞吐详情
            title: {
                label: '线程处理吞吐详情',
                slots: [
                    {
                        key: 'topSelect',
                        type: 'select',
                        defaultValue: '30',
                        options: [
                            {
                                value: '10',
                                label: 'Top10'
                            },
                            {
                                value: '30',
                                label: 'Top30'
                            },
                            {
                                value: '50',
                                label: 'Top50'
                            }
                        ]
                    }
                ]
            },
            AllTableData: [],
            tableData: [],
            columns: threadColumns,
            currentRow: {},
            ThreadTitle: {
                label: {
                    labelDic: [
                        {
                            key: 'Thread',
                            label: '线程'
                        }
                    ],
                    labelInfo: {
                        Thread: '-'
                    }
                },
                slots: [
                    {
                        type: 'text',
                        label: '超时检测处理耗时信息',
                        value: '-',
                        poptipInfo: {
                            placement: 'top-end',
                            content: {
                                CheckCount: '-',
                                CheckMaxNs: '-',
                                CheckMinNs: '-',
                                CheckAvgNs: '-'
                            }
                        }
                    }
                ]
            },
            // 网络应答处理
            netDetail: {
                layout: [
                    { x: 0, y: 0, w: 4, h: 13, i: '1' },
                    { x: 4, y: 0, w: 8, h: 13, i: '2' },
                    { x: 0, y: 1, w: 4, h: 13, i: '3' },
                    { x: 4, y: 1, w: 8, h: 13, i: '4' }
                ],
                details: [
                    {
                        type: 'obj',
                        title: '网络应答性能',
                        infoDic: [
                            {
                                label: 'DealCount',
                                key: 'DealCount'
                            },
                            {
                                label: 'TotalAvgNs',
                                key: 'TotalAvgNs'
                            }
                        ],
                        info: {
                            DealCount: '-',
                            TotalAvgNs: '-'
                        }
                    },
                    {
                        type: 'chart',
                        info: {
                            basicOpiton: {
                                chartType: 'axis',
                                lineDeploy: [
                                    {
                                        name: 'DealCount',
                                        type: 'line'
                                    },
                                    {
                                        name: 'TotalAvgNs',
                                        type: 'line',
                                        yAxisIndex: 1,
                                        tooltip: {
                                            valueFormatter: nsConvertTime
                                        }
                                    }
                                ],
                                yAxiDeploy: [
                                    {
                                        name: '个',
                                        alignTicks: true,
                                        axisLabel: {
                                            formatter: formatChartNumber
                                        }
                                    },
                                    {
                                        name: '时延',
                                        show: true,
                                        alignTicks: true,
                                        axisLabel: {
                                            formatter: nsConvertTime
                                        }
                                    }
                                ]
                            },
                            additionalOpiton: {
                                backgroundColor: '#2d334c'
                            },
                            chartData: {
                                xData: [],
                                data: {
                                    DealCount: [],
                                    TotalAvgNs: []
                                }
                            }
                        }
                    },
                    {
                        type: 'obj',
                        title: {
                            label: '网络应答时延分布',
                            slots: [
                                {
                                    key: 'netSelect',
                                    type: 'select',
                                    defaultValue: 'Avg',
                                    minWidth: '120px',
                                    options: [
                                        {
                                            value: 'Avg',
                                            label: 'Avg'
                                        },
                                        {
                                            value: 'Min',
                                            label: 'Min'
                                        },
                                        {
                                            value: 'Max',
                                            label: 'Max'
                                        }
                                    ]
                                }
                            ]
                        },
                        infoDic: [],
                        info: {}
                    },
                    {
                        type: 'chart',
                        info: {
                            basicOpiton: {
                                chartType: 'axis',
                                lineDeploy: [
                                    {
                                        name: 'Queue',
                                        type: 'line',
                                        tooltip: {
                                            valueFormatter: nsConvertTime
                                        }
                                    },
                                    {
                                        name: 'Process',
                                        type: 'line',
                                        tooltip: {
                                            valueFormatter: nsConvertTime
                                        }
                                    },
                                    {
                                        name: 'AsyncProcess',
                                        type: 'line',
                                        tooltip: {
                                            valueFormatter: nsConvertTime
                                        }
                                    }
                                ],
                                yAxiDeploy: [
                                    {
                                        name: '时延',
                                        axisLabel: {
                                            formatter: nsConvertTime
                                        }
                                    }
                                ]
                            },
                            additionalOpiton: {
                                backgroundColor: '#2d334c'
                            },
                            chartData: {
                                xData: [],
                                data: {
                                    Queue: [],
                                    Process: [],
                                    AsyncProcess: []
                                }
                            }
                        }
                    }
                ]
            },
            // Redo处理性能
            redoDetail: {
                layout: [
                    { x: 0, y: 0, w: 4, h: 13, i: '1' },
                    { x: 4, y: 0, w: 8, h: 13, i: '2' },
                    { x: 0, y: 1, w: 4, h: 13, i: '3' },
                    { x: 4, y: 1, w: 8, h: 13, i: '4' }
                ],
                details: [
                    {
                        type: 'obj',
                        title: 'Redo处理性能',
                        infoDic: [
                            {
                                label: 'DealCount',
                                key: 'DealCount'
                            },
                            {
                                label: 'TotalAvgNs',
                                key: 'TotalAvgNs'
                            }
                        ],
                        info: {
                            DealCount: '-',
                            TotalAvgNs: '-'
                        }
                    },
                    {
                        type: 'chart',
                        info: {
                            basicOpiton: {
                                chartType: 'axis',
                                lineDeploy: [
                                    {
                                        name: 'DealCount',
                                        type: 'line'
                                    },
                                    {
                                        name: 'TotalAvgNs',
                                        type: 'line',
                                        yAxisIndex: 1,
                                        tooltip: {
                                            valueFormatter: nsConvertTime
                                        }
                                    }
                                ],
                                yAxiDeploy: [
                                    {
                                        name: '个',
                                        alignTicks: true,
                                        axisLabel: {
                                            formatter: formatChartNumber
                                        }
                                    },
                                    {
                                        name: '时延',
                                        show: true,
                                        alignTicks: true,
                                        axisLabel: {
                                            formatter: nsConvertTime
                                        }
                                    }
                                ]
                            },
                            additionalOpiton: {
                                backgroundColor: '#2d334c'
                            },
                            chartData: {
                                xData: [],
                                data: {
                                    DealCount: [],
                                    TotalAvgNs: []
                                }
                            }
                        }
                    },
                    {
                        type: 'obj',
                        title: {
                            label: 'redo处理时延分布',
                            slots: [
                                {
                                    key: 'redoSelect',
                                    type: 'select',
                                    minWidth: '120px',
                                    defaultValue: 'Avg',
                                    options: [
                                        {
                                            value: 'Avg',
                                            label: 'Avg'
                                        },
                                        {
                                            value: 'Min',
                                            label: 'Min'
                                        },
                                        {
                                            value: 'Max',
                                            label: 'Max'
                                        }
                                    ]
                                }
                            ]
                        },
                        infoDic: [],
                        info: {}
                    },
                    {
                        type: 'chart',
                        info: {
                            basicOpiton: {
                                chartType: 'axis',
                                lineDeploy: [
                                    {
                                        name: 'Filter1',
                                        type: 'line',
                                        tooltip: {
                                            valueFormatter: nsConvertTime
                                        }
                                    },
                                    {
                                        name: 'Locate',
                                        type: 'line',
                                        tooltip: {
                                            valueFormatter: nsConvertTime
                                        }
                                    },
                                    {
                                        name: 'Queue',
                                        type: 'line',
                                        tooltip: {
                                            valueFormatter: nsConvertTime
                                        }
                                    },
                                    {
                                        name: 'Filter2',
                                        type: 'line',
                                        tooltip: {
                                            valueFormatter: nsConvertTime
                                        }
                                    },
                                    {
                                        name: 'Process',
                                        type: 'line',
                                        tooltip: {
                                            valueFormatter: nsConvertTime
                                        }
                                    }
                                ],
                                yAxiDeploy: [
                                    {
                                        name: '时延',
                                        axisLabel: {
                                            formatter: nsConvertTime
                                        }
                                    }
                                ]
                            },
                            additionalOpiton: {
                                backgroundColor: '#2d334c'
                            },
                            chartData: {
                                xData: [],
                                data: {
                                    Filter1: [],
                                    Locate: [],
                                    Queue: [],
                                    Filter2: [],
                                    Process: []
                                }
                            }
                        }
                    }
                ]
            },
            selectTopValue: '30',
            redoSelectValue: 'Avg',
            netSelectValue: 'Avg'
        };
    },
    methods: {
        async initData() {
            this.loading = true;
            this.currentRow = {};
            this.cleanRedoNetData();
            this.cleanReqDetail();
            this.cleanRspDetail();
            await this.getFileData();
            this.loading = false;
        },
        // 表格下拉框切换
        selectChange(value, key) {
            if (key === 'topSelect'){
                this.selectTopValue = value;
                this.tableData = [...this.AllTableData]?.slice(0, this.selectTopValue) || [];
                const row = this.tableData.find(v => (v?.ThreadName + v?.ClusterName) === (this.currentRow?.ThreadName + this.currentRow?.ClusterName));
                if (!row){
                    this.cleanRedoNetData();
                    this.cleanReqDetail();
                    this.cleanRspDetail();
                    this.getFileData();
                }
            }

            if (key === 'redoSelect'){
                this.redoSelectValue = value;
                this.cleanReqDetail();
                this.getFileData();
            }

            if (key === 'netSelect'){
                this.netSelectValue = value;
                this.cleanRspDetail();
                this.getFileData();
            }
        },
        // 表格切换选中行
        tableRowcheckedChange(currentRow) {
            if ((this.currentRow?.ThreadName + this.currentRow?.ClusterName) !== (currentRow?.ThreadName + currentRow?.ClusterName)){
                this.cleanRedoNetData();
                this.cleanReqDetail();
                this.cleanRspDetail();
            }
            this.currentRow = currentRow;
            this.getFileData();
        },
        // 表格选中行显示处理性能信息
        setThreadIdTitleData(currentRow, newTime){
            this.ThreadTitle.label.labelInfo.Thread = currentRow?.ThreadName + ' - ' + currentRow?.ClusterName;
            this.ThreadTitle.slots[0].value = currentRow?.TimeoutCheck?.CheckCount;
            this.ThreadTitle.slots[0].poptipInfo.content = { ...this.ThreadTitle.slots[0].poptipInfo.content, ...currentRow?.TimeoutCheck };
            this.setReqDetail(newTime);
            this.setRspDetail(newTime);
        },
        // 响应数据清理 -- 清理数据
        cleanRedoNetData(){
            this.ThreadTitle.label.labelInfo.Thread = '-';
            this.ThreadTitle.slots[0].value = '-';
            this.ThreadTitle.slots[0].poptipInfo.content = {
                CheckCount: '-',
                CheckMaxNs: '-',
                CheckMinNs: '-',
                CheckAvgNs: '-'
            };
            this.redoDetail.details[0].info = {};
            this.redoDetail.details[1].info.chartData.xData = [];
            this.redoDetail.details[1].info.chartData.data.DealCount = [];
            this.redoDetail.details[1].info.chartData.data.TotalAvgNs = [];
            this.netDetail.details[0].info = {};
            this.netDetail.details[1].info.chartData.xData = [];
            this.netDetail.details[1].info.chartData.data.DealCount = [];
            this.netDetail.details[1].info.chartData.data.TotalAvgNs = [];
        },
        // redo处理性能
        setReqDetail(newTime){
            const Req = this.currentRow?.Req;
            this.redoDetail.details[0].info = {
                DealCount: Req?.DealCount,
                TotalAvgNs: Req?.TotalAvgNs
            };
            this.redoDetail.details[2].info = { ...Req };

            const yData = {
                DealCount: Req?.DealCount,
                TotalAvgNs: Req?.TotalAvgNs,
                Filter1: Req?.['Filter1' + this.redoSelectValue + 'Ns'],
                Locate: Req?.['Locate' + this.redoSelectValue + 'Ns'],
                Queue: Req?.['Queue' + this.redoSelectValue + 'Ns'],
                Filter2: Req?.['Filter2' + this.redoSelectValue + 'Ns'],
                Process: Req?.['Process' + this.redoSelectValue + 'Ns']
            };
            const charDataList = [
                this.redoDetail.details[1].info.chartData,
                this.redoDetail.details[3].info.chartData
            ];
            this.setChartData(charDataList, yData, newTime);
        },
        // redo处理性能 -- 清理数据
        cleanReqDetail(){
            this.redoDetail.details[2].infoDic = this.setRepInfoDic(this.redoSelectValue);
            this.redoDetail.details[2].info = {};
            this.redoDetail.details[3].info.chartData.xData = [];
            this.redoDetail.details[3].info.chartData.data.Filter1 = [];
            this.redoDetail.details[3].info.chartData.data.Locate = [];
            this.redoDetail.details[3].info.chartData.data.Queue = [];
            this.redoDetail.details[3].info.chartData.data.Filter2 = [];
            this.redoDetail.details[3].info.chartData.data.Process = [];
        },
        // 设置 reqInfoDic
        setRepInfoDic(val){
            return [
                { label: 'Filter1' + val + 'Ns', key: 'Filter1' + val + 'Ns' },
                { label: 'Locate' + val + 'Ns', key: 'Locate' + val + 'Ns' },
                { label: 'Queue' + val + 'Ns', key: 'Queue' + val + 'Ns' },
                { label: 'Filter2' + val + 'Ns', key: 'Filter2' + val + 'Ns' },
                { label: 'Process' + val + 'Ns', key: 'Process' + val + 'Ns' }
            ];
        },
        // 网关响应性能
        setRspDetail(newTime){
            const Rsp = this.currentRow?.Rsp;
            this.netDetail.details[0].info = {
                DealCount: Rsp?.DealCount,
                TotalAvgNs: Rsp?.TotalAvgNs
            };
            this.netDetail.details[2].info = { ...Rsp };
            const yData = {
                DealCount: Rsp?.DealCount,
                TotalAvgNs: Rsp?.TotalAvgNs,
                AsyncProcess: Rsp?.['AsyncProcess' + this.redoSelectValue + 'Ns'],
                Queue: Rsp?.['Queue' + this.redoSelectValue + 'Ns'],
                Process: Rsp?.['Process' + this.redoSelectValue + 'Ns']
            };
            const charDataList = [
                this.netDetail.details[1].info.chartData,
                this.netDetail.details[3].info.chartData
            ];
            this.setChartData(charDataList, yData, newTime);
        },
        // 网关响应性能 -- 清理数据
        cleanRspDetail(){
            this.netDetail.details[2].infoDic = this.setRspInfoDic(this.netSelectValue);
            this.netDetail.details[2].info = {};
            this.netDetail.details[3].info.chartData.xData = [];
            this.netDetail.details[3].info.chartData.data.Queue = [];
            this.netDetail.details[3].info.chartData.data.Process = [];
            this.netDetail.details[3].info.chartData.data.AsyncProcess = [];
        },
        // 设置 reqInfoDic
        setRspInfoDic(val){
            return [
                { label: 'AsyncProcess' + val + 'Ns', key: 'AsyncProcess' + val + 'Ns' },
                { label: 'Queue' + val + 'Ns', key: 'Queue' + val + 'Ns' },
                { label: 'Process' + val + 'Ns', key: 'Process' + val + 'Ns' }
            ];
        },
        // chart时间轴
        setChartTime(){
            const charDataList = [
                this.redoDetail.details[1].info.chartData,
                this.redoDetail.details[3].info.chartData,
                this.netDetail.details[1].info.chartData,
                this.netDetail.details[3].info.chartData
            ];
            const newTime = this.$getCurrentLocalTime();
            charDataList.forEach(chartData => {
                if (chartData.xData.length > (5 * 60 / this.pollTime)) {
                    chartData.xData.shift();
                    Object.values(chartData.data).forEach(item => { item.shift(); });
                }
                chartData.xData.indexOf(newTime) === -1 && chartData.xData.push(newTime);
            });
            return newTime;
        },
        // chart数据
        setChartData(chartDataList, data, newTime){
            chartDataList.forEach(chartData => {
                const index = chartData.xData.indexOf(newTime);
                Object.keys(chartData.data).forEach(item => {
                    this.$set(chartData.data[item], index, data?.[item]);
                });
            });
        },
        // 解耦表格数据
        setThreadTableData(getRedoDealInfo){
            const tableData = [];
            for (const [threadKey, thread] of Object.entries(getRedoDealInfo)){
                const TimeoutCheck = thread?.TimeoutCheck;
                for (const [clusterKey, cluster] of Object.entries(thread)){
                    if (!cluster?.Req && !cluster?.Rsp) continue;
                    tableData.push({
                        ThreadName: threadKey,
                        ClusterName: clusterKey,
                        ReqDealCount: cluster?.Req?.DealCount,
                        ReqTotalAvgNs: cluster?.Req?.TotalAvgNs,
                        RspDealCount: cluster?.Rsp?.DealCount,
                        RspTotalAvgNs: cluster?.Rsp?.TotalAvgNs,
                        DealMinTransNo: cluster?.Req?.DealMinTransNo,
                        DealMaxTransNo: cluster?.Req?.DealMaxTransNo,
                        Rsp: cluster?.Rsp,
                        Req: cluster?.Req,
                        TimeoutCheck: TimeoutCheck
                    });
                }
            }
            return tableData;
        },
        // 构造页面数据
        async getFileData() {
            // chart时间轴
            const newTime = this.setChartTime();
            const { getRedoDealInfo } = await this.getAPi();
            // 表格数据
            this.AllTableData = this.setThreadTableData(getRedoDealInfo);
            this.tableData = [...this.AllTableData]?.slice(0, this.selectTopValue);

            // redo处理性能  网络应答性能
            if (this.tableData.length){
                const row = this.tableData.find(v => (v?.ThreadName + v?.ClusterName) === (this.currentRow?.ThreadName + this.currentRow?.ClusterName));
                this.currentRow = Object.keys(this.currentRow).length && row ? row : this.tableData?.[0];
                this.tableData.forEach(item => {
                    item._highlight = (item?.ThreadName + item?.ClusterName) === (this.currentRow?.ThreadName + this.currentRow?.ClusterName); // 表格刷新后默认选中
                });
                this.setThreadIdTitleData(this.currentRow, newTime);
            }
        },
        // 接口请求
        async getAPi() {
            const data = {
                getRedoDealInfo: {}
            };
            const param = [
                {
                    manageProxyIp: this.nodeData.ip,
                    manageProxyPort: this.nodeData.manageProxyPort,
                    pluginName: 'ldp_todb',
                    funcName: 'GetRedoDealInfo'
                }
            ];
            try {
                const res = await getManagerProxy(JSON.stringify(param));
                if (res.code === '200') {
                    res.data?.[0]?.ThreadDealInfo && (data.getRedoDealInfo = res.data[0].ThreadDealInfo);
                }
                return data;
            } catch (err) {
                this.$emit('clear');
            }
        }
    }
};
</script>

<style lang="less" scoped>
.tab-box {
    .detail-grid-box {
        & > .obs-title {
            background: var(--wrapper-color);
            margin-top: 15px;
        }
    }

    .detail-box {
        /deep/ .obs-title {
            padding: 0 0 0 10px;

            .title-text {
                display: inline-block;
                color: var(--font-opacity-color);
                line-height: 42px;
                margin-left: 0;
                font-size: 12px;
                font-size: var(--font-size);
                padding: 0 5px;
                overflow: hidden;
                white-space: nowrap;
                text-overflow: ellipsis;
            }

            &::before {
                display: none;
            }
        }
    }
}
</style>
