/*
 * @Description: 可切换的多表tab集合组件
 * @Author: <PERSON><PERSON>
 * @Date: 2022-11-29 14:53:36
 * @LastEditTime: 2023-03-20 17:57:24
 * @LastEditors: <PERSON><PERSON>
 */
import aForm from '@/components/common/form/aForm';
import aTable from '@/components/common/table/aTable';
import aButton from '@/components/common/button/aButton';
import _ from 'lodash';
import './bestTable.less';
export default {
    name: 'tabTitleTable',
    props: {
        tabTableData: {
            type: Array,
            default: []
        },
        tableLoading: {
            type: Boolean,
            default: false
        },
        hasSetTableColumns: {
            type: Boolean,
            default: true
        },
        hasButton: {
            type: Boolean,
            default: true
        },
        showTitle: {
            type: Boolean,
            default: false
        },
        // 表格自动适应高度
        autoHeight: {
            type: Boolean,
            default: true
        },
        showSizer: {
            type: Boolean,
            default: true
        },
        showElevator: {
            type: Boolean,
            default: true
        }
    },
    data() {
        return {
            tabName: '',
            formStatus: true,
            visible: false,
            styles: {
                height: 'calc(100% - 55px)',
                paddingBottom: '53px'
            },
            checkData: {},
            currentCheckedKeys: {},
            columnData: {},
            tableHeight: 0
        };
    },
    computed: {
        tabIndex: function() {
            return _.findIndex(this.tabTableData, {
                name: this.tabName
            });
        }
    },
    mounted() {
        this.$_init();
        this.tabName = this.tabTableData[0].name;
        window.addEventListener('resize', this.fetTableHeight);
        this.$nextTick(() => {
            this.fetTableHeight();
        });
    },
    beforeDestroy() {
        window.removeEventListener('resize', this.fetTableHeight);
    },
    methods: {
        resetHeight() {
            return new Promise((resolve, reject) => {
                this.tableHeight = 0;
                resolve();
            });
        },
        // 设置table高度
        fetTableHeight() {
            this.resetHeight().then(res => {
                if (this.autoHeight){
                    const height = this.formStatus ? Math.ceil(this.tabTableData?.[this.tabIndex]?.formItems?.length / 3) * 45 || 0 : 0;
                    this.tableHeight = this.$refs['table-box' + this.tabIndex].getBoundingClientRect().height - height - 110;
                } else {
                    this.tableHeight =  220;
                }
            });
        },
        changeFormStatus(){
            this.formStatus = !this.formStatus;
            this.fetTableHeight();
        },
        $_clearCurrentCheckedKeys(){
            this.tabTableData.forEach(element => {
                this.$set(this.currentCheckedKeys, element.name, []);
            });
        },
        $_init() {
            this.tabTableData.forEach(element => {
                this.$set(this.checkData, element.name, []);
                this.$set(this.columnData, element.name, []);

                if (this.currentCheckedKeys?.[element.name]?.length){
                    this.checkData[element.name] = this.currentCheckedKeys?.[element.name] || [];
                    element.columns.forEach(ele => {
                        if (this.currentCheckedKeys?.[element.name].includes(ele.key)){
                            this.columnData[element.name].push(ele);
                        }
                    });
                } else {
                    element.columns.forEach(ele => {
                        this.checkData[element.name].push(ele.key);
                        this.columnData[element.name].push(ele);
                    });
                }
            });
        },
        setTableColumns() {
            this.visible = true;
        },
        $_handleCheckbox(value) {
            this.$set(this.currentCheckedKeys, this.tabName, [...value]);
            this.columnData[this.tabName] = [];
            this.tabTableData[this.tabIndex].columns.forEach(ele => {
                if (value.includes(ele.key)){
                    this.columnData[this.tabName].push(ele);
                }
            });
            this.columnData[this.tabName].forEach(ele => {
                ele.hiddenCol = !value.includes(ele.key);
            });
        },
        // 获取查询参数
        getQueryParam() {
            const param = this.$refs['forms' + this.tabIndex]?.query() || {};
            if (!param) return;
            const pageParam = this.tabTableData[this.tabIndex].hasPage ? this.$refs['table' + this.tabIndex]?.getPageData() : {};
            return { ...param, ...pageParam, tabName: this.tabName };
        },
        // 点击查询
        $_handleQuery() {
            const params = this.getQueryParam();
            if (!params) return;
            this.$emit('query', params);
        },
        $_handleReset() {
            this.$refs['forms' + this.tabIndex].reset();
        },
        handleClickQuery() {
            this.$refs['table' + this.tabIndex].resetPage();
            this.$_handleQuery();
        },
        $_handleResetPageDataAndQuery() {
            this.$refs['table' + this.tabIndex].resetPage();
            this.$refs['table' + this.tabIndex].resetPageSize();
            this.$refs['table' + this.tabIndex].resetSortData();
            this.$_handleQuery();
        }
    },
    components: { aForm, aTable, aButton },
    render() {
        return <div class="best-table">
            <h-tabs v-model={this.tabName}  v-on:on-click={this.handleClickQuery} animated={false}>
                {
                    this.tabTableData.map((ele, index) => {
                        return <h-tab-pane label={ele.label} name={ele.name}>
                            {
                                ele.formItems?.length && <div class="form-box" style={{ height: this.formStatus ? Math.ceil(ele.formItems.length / 3) * 45 + 'px' : 0 }}>
                                    <a-form ref={'forms' + index} formItems={ele.formItems}/>
                                </div>
                            }
                            <div ref={'table-box' + index} class='table-box'>
                                <a-table
                                    ref={'table' + index}
                                    tableData={ele.tableData}
                                    columns={this.columnData[this.tabName]}
                                    showTitle={this.showTitle}
                                    hasPage={ele.hasPage}
                                    total={ele.total}
                                    showSizer={this.showSizer}
                                    showElevator={this.showElevator}
                                    height={this.tableHeight}
                                    loading={this.tableLoading}
                                    v-on:query={this.$_handleQuery}/>
                            </div>
                        </h-tab-pane>;
                    })
                }
            </h-tabs>
            {
                this.hasButton && <div class="btn-list">
                    <a-button type="primary" onClick={this.handleClickQuery}>查询</a-button>
                    {this.$slots.default}
                    <a-button type="dark" onClick={this.$_handleReset}>重置</a-button>
                    {this.hasSetTableColumns && <a-button type="dark" onClick={this.setTableColumns}>配置表格</a-button>}
                    <a-button type="dark" onClick={this.changeFormStatus}>
                        {this.formStatus ? '收起搜索' : '展开搜索'}
                        <h-icon name={this.formStatus ? 'packup' : 'unfold'}></h-icon></a-button>
                </div>
            }
            <h-drawer
                v-model={this.visible}
                width="280"
                title="表格配置"
                styles={this.styles}>
                <h-checkbox-group
                    v-model={this.checkData[this.tabName]}
                    onInput={this.$_handleCheckbox}
                    vertical>
                    {
                        this.tabTableData[this.tabIndex]?.columns?.map(item => {
                            return <h-checkbox key={item.key}
                                label={item.key}
                                disabled={item.disabled || (this.checkData[this.tabName].length <= 1 && this.checkData[this.tabName].includes(item.key))} style="display: flex; align-items: center;">
                                <span style="white-space:nowrap;">{item.title}</span>
                            </h-checkbox>;
                        })
                    }
                </h-checkbox-group>
            </h-drawer>
            <style jsx>
                {
                    `
                        .form-box {
                            margin-top: 4px;
                            overflow-y: hidden;
                            transition: height 1s;
                        }
                    `
                }
            </style>
        </div>;
    }
};
