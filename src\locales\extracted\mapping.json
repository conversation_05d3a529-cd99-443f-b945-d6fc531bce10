{"views\\index\\accordMonitor.vue": [{"key": "pleaseSelect", "chinese": "请选择", "english": "请选择", "file": "views\\index\\accordMonitor.vue"}, {"key": "config", "chinese": "配置", "english": "配置", "file": "views\\index\\accordMonitor.vue"}, {"key": "texttexttexttexttextTotalcount", "chinese": "总上场次数：{{ totalCount }}", "english": "总上场次数：{{ totalCount }}", "file": "views\\index\\accordMonitor.vue"}, {"key": "texttexttexttextdatatexttexttexttexttexttext", "chinese": "主备核心数据同步差量告警：", "english": "主备核心数据同步差量告警：", "file": "views\\index\\accordMonitor.vue"}, {"key": "text", "chinese": "条", "english": "条", "file": "views\\index\\accordMonitor.vue"}, {"key": "datatexttextrefreshtexttext", "chinese": "数据自动刷新频率：", "english": "数据自动刷新频率：", "file": "views\\index\\accordMonitor.vue"}, {"key": "text", "chinese": "秒", "english": "秒", "file": "views\\index\\accordMonitor.vue"}], "views\\index\\accordObservation.vue": [{"key": "texttextdatatexttext", "chinese": "核心数据同步", "english": "核心数据同步", "file": "views\\index\\accordObservation.vue"}, {"key": "texttext", "chinese": "总览", "english": "总览", "file": "views\\index\\accordObservation.vue"}, {"key": "texttext", "chinese": "未知", "english": "未知", "file": "views\\index\\accordObservation.vue"}], "views\\index\\analyseConfig.vue": [{"key": "texttexttexttextAtitleTitle", "chinese": ">\r\n        <!-- 头部标题 -->\r\n        <a-title :title=", "english": ">\r\n        <!-- 头部标题 -->\r\n        <a-title :title=", "file": "views\\index\\analyseConfig.vue"}, {"key": "texttextinfotexttexttexttexttexttextimporttexttexttexttext", "chinese": "场景信息为空,请新建或导入场景信息", "english": "场景信息为空,请新建或导入场景信息", "file": "views\\index\\analyseConfig.vue"}, {"key": "texttextidScenename", "chinese": ",            // 场景ID\r\n            sceneName:", "english": ",            // 场景ID\r\n            sceneName:", "file": "views\\index\\analyseConfig.vue"}, {"key": "texttextnameApidemoinfo", "chinese": ",          // 场景Name\r\n            apiDemoInfo:", "english": ",          // 场景Name\r\n            apiDemoInfo:", "file": "views\\index\\analyseConfig.vue"}, {"key": "exporttexttextdatasuccess", "chinese": "导出场景数据成功!", "english": "导出场景数据成功!", "file": "views\\index\\analyseConfig.vue"}, {"key": "exporttexttextfailed", "chinese": "导出场景失败!", "english": "导出场景失败!", "file": "views\\index\\analyseConfig.vue"}, {"key": "texttextscenename", "chinese": "场景：${sceneName}", "english": "场景：${sceneName}", "file": "views\\index\\analyseConfig.vue"}, {"key": "exporttexttext", "chinese": "导出场景", "english": "导出场景", "file": "views\\index\\analyseConfig.vue"}, {"key": "importtexttext", "chinese": "导入场景", "english": "导入场景", "file": "views\\index\\analyseConfig.vue"}, {"key": "texttexttexttext", "chinese": "新建场景", "english": "新建场景", "file": "views\\index\\analyseConfig.vue"}], "views\\index\\analyseData.vue": [{"key": "texttexttexttextHiconVif", "chinese": ">测试实例\r\n                    <h-icon v-if=", "english": ">测试实例\r\n                    <h-icon v-if=", "file": "views\\index\\analyseData.vue"}, {"key": "texttexttexttextdivDivClass", "chinese": ">来源用例</div>\r\n                <div class=", "english": ">来源用例</div>\r\n                <div class=", "file": "views\\index\\analyseData.vue"}, {"key": "texttexttexttextdivDivClass", "chinese": ">归属场景</div>\r\n                <div class=", "english": ">归属场景</div>\r\n                <div class=", "file": "views\\index\\analyseData.vue"}, {"key": "starttimedivDivClass", "chinese": ">开始时间</div>\r\n                <div class=", "english": ">开始时间</div>\r\n                <div class=", "file": "views\\index\\analyseData.vue"}, {"key": "endtimedivDivClass", "chinese": ">结束时间</div>\r\n                <div class=", "english": ">结束时间</div>\r\n                <div class=", "file": "views\\index\\analyseData.vue"}, {"key": "texttexttexttextdivDivClass", "chinese": ">产品节点</div>\r\n                <div class=", "english": ">产品节点</div>\r\n                <div class=", "file": "views\\index\\analyseData.vue"}, {"key": "texttextstatusdivDivClass", "chinese": ">测试状态</div>\r\n                <div class=", "english": ">测试状态</div>\r\n                <div class=", "file": "views\\index\\analyseData.vue"}, {"key": "texttextstatusdivDivClass", "chinese": ">报表状态</div>\r\n                <div class=", "english": ">报表状态</div>\r\n                <div class=", "file": "views\\index\\analyseData.vue"}, {"key": "texttexttexttextdivDivClass", "chinese": ">测试参数</div>\r\n                <div class=", "english": ">测试参数</div>\r\n                <div class=", "file": "views\\index\\analyseData.vue"}, {"key": "texttextHiconVif", "chinese": ">备注\r\n                    <h-icon v-if=", "english": ">备注\r\n                    <h-icon v-if=", "file": "views\\index\\analyseData.vue"}, {"key": "texttexttexttextDivVif", "chinese": "/>\r\n        <!-- 报表视图 -->\r\n        <div v-if=", "english": "/>\r\n        <!-- 报表视图 -->\r\n        <div v-if=", "file": "views\\index\\analyseData.vue"}, {"key": "texttexttexttext", "chinese": "暂无图表", "english": "暂无图表", "file": "views\\index\\analyseData.vue"}, {"key": "texttextidLooptype", "chinese": ",  // 场景id\r\n            loopType:", "english": ",  // 场景id\r\n            loopType:", "file": "views\\index\\analyseData.vue"}, {"key": "texttexttexttext", "chinese": "跨度/指标", "english": "跨度/指标", "file": "views\\index\\analyseData.vue"}, {"key": "texttextmin", "chinese": "最小（min）", "english": "最小（min）", "file": "views\\index\\analyseData.vue"}, {"key": "texttexttextp50", "chinese": "中位数（p50）", "english": "中位数（p50）", "file": "views\\index\\analyseData.vue"}, {"key": "texttextavg", "chinese": "平均（avg）", "english": "平均（avg）", "file": "views\\index\\analyseData.vue"}, {"key": "95texttextp95", "chinese": "95分位（p95）", "english": "95分位（p95）", "file": "views\\index\\analyseData.vue"}, {"key": "99texttextp99", "chinese": "99分位（p99）", "english": "99分位（p99）", "file": "views\\index\\analyseData.vue"}, {"key": "texttextmax", "chinese": "最大（max）", "english": "最大（max）", "file": "views\\index\\analyseData.vue"}, {"key": "texttexttextstddeviation", "chinese": "标准差（stdDeviation）", "english": "标准差（stdDeviation）", "file": "views\\index\\analyseData.vue"}, {"key": "texttextdetail", "chinese": "指标详情", "english": "指标详情", "file": "views\\index\\analyseData.vue"}, {"key": "texttexttexttext", "chinese": "资金账号", "english": "资金账号", "file": "views\\index\\analyseData.vue"}, {"key": "texttexttexttexttexttexttext", "chinese": "交易所申报编号", "english": "交易所申报编号", "file": "views\\index\\analyseData.vue"}, {"key": "texttexttexttexttext", "chinese": "柜台委托号", "english": "柜台委托号", "file": "views\\index\\analyseData.vue"}, {"key": "texttextdata", "chinese": "时延数据", "english": "时延数据", "file": "views\\index\\analyseData.vue"}, {"key": "text", "chinese": "无", "english": "无", "file": "views\\index\\analyseData.vue"}, {"key": "texttext", "chinese": "纳秒", "english": "纳秒", "file": "views\\index\\analyseData.vue"}, {"key": "texttext", "chinese": "微秒", "english": "微秒", "file": "views\\index\\analyseData.vue"}, {"key": "texttext", "chinese": "毫秒", "english": "毫秒", "file": "views\\index\\analyseData.vue"}, {"key": "texttextdetailtexttextunitname", "chinese": "报表详情（单位：${unitName}）", "english": "报表详情（单位：${unitName}）", "file": "views\\index\\analyseData.vue"}, {"key": "texttextthisunitname", "chinese": "单位（${this.unitName}）", "english": "单位（${this.unitName}）", "file": "views\\index\\analyseData.vue"}, {"key": "thisspanlatencydictdescspanTexttextdetail", "chinese": "${this.spanLatencyDictDesc[span] || ''} 指标详情", "english": "${this.spanLatencyDictDesc[span] || ''} 指标详情", "file": "views\\index\\analyseData.vue"}, {"key": "texttexttexttext", "chinese": "选择实例", "english": "选择实例", "file": "views\\index\\analyseData.vue"}, {"key": "texttexttexttext", "chinese": "测试实例", "english": "测试实例", "file": "views\\index\\analyseData.vue"}, {"key": "texttexttexttext", "chinese": "来源用例", "english": "来源用例", "file": "views\\index\\analyseData.vue"}, {"key": "texttexttexttext", "chinese": "归属场景", "english": "归属场景", "file": "views\\index\\analyseData.vue"}, {"key": "starttime", "chinese": "开始时间", "english": "开始时间", "file": "views\\index\\analyseData.vue"}, {"key": "endtime", "chinese": "结束时间", "english": "结束时间", "file": "views\\index\\analyseData.vue"}, {"key": "texttexttexttext", "chinese": "产品节点", "english": "产品节点", "file": "views\\index\\analyseData.vue"}, {"key": "texttextstatus", "chinese": "测试状态", "english": "测试状态", "file": "views\\index\\analyseData.vue"}, {"key": "texttextstatus", "chinese": "报表状态", "english": "报表状态", "file": "views\\index\\analyseData.vue"}, {"key": "texttexttexttext", "chinese": "测试参数", "english": "测试参数", "file": "views\\index\\analyseData.vue"}, {"key": "texttext", "chinese": "备注", "english": "备注", "file": "views\\index\\analyseData.vue"}, {"key": "configtexttext", "chinese": "配置报表", "english": "配置报表", "file": "views\\index\\analyseData.vue"}], "views\\index\\apmMonitorConfig.vue": [{"key": "texttexttexttexttexttexttextobservationtexttexttexttexttexttexttext", "chinese": "当前节点不支持观测！请重新选择节点", "english": "当前节点不支持观测！请重新选择节点", "file": "views\\index\\apmMonitorConfig.vue"}, {"key": "texttexttexttexttexttexttext", "chinese": "已托管应用节点", "english": "已托管应用节点", "file": "views\\index\\apmMonitorConfig.vue"}], "views\\index\\appRunningState.vue": [{"key": "texttextstatustext", "chinese": "应用状态墙", "english": "应用状态墙", "file": "views\\index\\appRunningState.vue"}, {"key": "texttextconfigpDivStyle", "chinese": ">基础配置</p>\r\n            <div style=", "english": ">基础配置</p>\r\n            <div style=", "file": "views\\index\\appRunningState.vue"}, {"key": "texttexttexttextconfigpDivClass", "chinese": ">告警规则配置</p>\r\n\r\n            <div class=", "english": ">告警规则配置</p>\r\n\r\n            <div class=", "file": "views\\index\\appRunningState.vue"}, {"key": "texttexthcolHcolSpan", "chinese": ">序号</h-col>\r\n                <h-col span=", "english": ">序号</h-col>\r\n                <h-col span=", "file": "views\\index\\appRunningState.vue"}, {"key": "starttimehcolHcolSpan", "chinese": ">开始时间</h-col>\r\n                <h-col span=", "english": ">开始时间</h-col>\r\n                <h-col span=", "file": "views\\index\\appRunningState.vue"}, {"key": "endtimehcolHcolSpan", "chinese": ">结束时间</h-col>\r\n                <h-col span=", "english": ">结束时间</h-col>\r\n                <h-col span=", "file": "views\\index\\appRunningState.vue"}, {"key": "savesuccess", "chinese": "保存成功", "english": "保存成功", "file": "views\\index\\appRunningState.vue"}, {"key": "savefailed", "chinese": "保存失败", "english": "保存失败", "file": "views\\index\\appRunningState.vue"}, {"key": "texttexttextsavetexttexttexttextconfigtext", "chinese": "您确定保存当前所有配置吗？", "english": "您确定保存当前所有配置吗？", "file": "views\\index\\appRunningState.vue"}, {"key": "texttexttexttext", "chinese": "服务视图", "english": "服务视图", "file": "views\\index\\appRunningState.vue"}, {"key": "texttexttexttext", "chinese": "部署视图", "english": "部署视图", "file": "views\\index\\appRunningState.vue"}, {"key": "texttextconfig", "chinese": "基础配置", "english": "基础配置", "file": "views\\index\\appRunningState.vue"}, {"key": "texttexttexttexttexttext", "chinese": "显示仲裁节点", "english": "显示仲裁节点", "file": "views\\index\\appRunningState.vue"}, {"key": "texttexttexttextconfig", "chinese": "告警规则配置", "english": "告警规则配置", "file": "views\\index\\appRunningState.vue"}, {"key": "texttext", "chinese": "序号", "english": "序号", "file": "views\\index\\appRunningState.vue"}, {"key": "texttextstatus", "chinese": "节点状态", "english": "节点状态", "file": "views\\index\\appRunningState.vue"}, {"key": "addtexttext", "chinese": "新增规则", "english": "新增规则", "file": "views\\index\\appRunningState.vue"}, {"key": "saveconfig", "chinese": "保存配置", "english": "保存配置", "file": "views\\index\\appRunningState.vue"}, {"key": "texttext", "chinese": "关闭", "english": "关闭", "file": "views\\index\\appRunningState.vue"}], "views\\index\\brokerDataLimit.vue": [{"key": "texttexttexttextconfig", "chinese": "降级熔断配置", "english": "降级熔断配置", "file": "views\\index\\brokerDataLimit.vue"}, {"key": "texttexttexttextlist", "chinese": "发布名单列表", "english": "发布名单列表", "file": "views\\index\\brokerDataLimit.vue"}, {"key": "texttexttextconfig", "chinese": "黑名单配置", "english": "黑名单配置", "file": "views\\index\\brokerDataLimit.vue"}, {"key": "texttexttextconfig", "chinese": "白名单配置", "english": "白名单配置", "file": "views\\index\\brokerDataLimit.vue"}, {"key": "texttexttexttextconfig", "chinese": "限流名单配置", "english": "限流名单配置", "file": "views\\index\\brokerDataLimit.vue"}, {"key": "textconfig", "chinese": "组配置", "english": "组配置", "file": "views\\index\\brokerDataLimit.vue"}, {"key": "texttexttext", "chinese": "发布中", "english": "发布中", "file": "views\\index\\brokerDataLimit.vue"}, {"key": "texttext", "chinese": "发布", "english": "发布", "file": "views\\index\\brokerDataLimit.vue"}, {"key": "texttexttexttexttextTabname", "chinese": ", // 选中的产品\r\n            tabName:", "english": ", // 选中的产品\r\n            tabName:", "file": "views\\index\\brokerDataLimit.vue"}, {"key": "texttexttexttexttextlisttexttexttexttexttexttexttexttexttexttexttexttexttexttexttexttext", "chinese": "确定要发布列表中已启用的黑名单、白名单、限流名单吗？", "english": "确定要发布列表中已启用的黑名单、白名单、限流名单吗？", "file": "views\\index\\brokerDataLimit.vue"}, {"key": "cancel", "chinese": "取消", "english": "取消", "file": "views\\index\\brokerDataLimit.vue"}, {"key": "publishloadingTexttexttextTexttext", "chinese": "{{publishLoading ? '发布中' : '发布'}}", "english": "{{publishLoading ? '发布中' : '发布'}}", "file": "views\\index\\brokerDataLimit.vue"}], "views\\index\\coreFuncHandleObservation\\displaySettingDrawer.vue": [{"key": "texttexttextfunctiontextspanVif", "chinese": ">\r\n                        已展示功能号：<span v-if=", "english": ">\r\n                        已展示功能号：<span v-if=", "file": "views\\index\\coreFuncHandleObservation\\displaySettingDrawer.vue"}, {"key": "texttextfunctiontextquery", "chinese": "输入功能号查询", "english": "输入功能号查询", "file": "views\\index\\coreFuncHandleObservation\\displaySettingDrawer.vue"}, {"key": "functiontextname", "chinese": "功能号名称", "english": "功能号名称", "file": "views\\index\\coreFuncHandleObservation\\displaySettingDrawer.vue"}, {"key": "functiontext", "chinese": "功能号", "english": "功能号", "file": "views\\index\\coreFuncHandleObservation\\displaySettingDrawer.vue"}, {"key": "texttexttexttext", "chinese": "是否展示", "english": "是否展示", "file": "views\\index\\coreFuncHandleObservation\\displaySettingDrawer.vue"}, {"key": "texttexttexttexttexttexttexttexttexttexttexttexttexttext30text", "chinese": "为保证查看体验，建议选择不超过30个。", "english": "为保证查看体验，建议选择不超过30个。", "file": "views\\index\\coreFuncHandleObservation\\displaySettingDrawer.vue"}, {"key": "queryfunctiontexttexttext", "chinese": "查询功能号异常", "english": "查询功能号异常", "file": "views\\index\\coreFuncHandleObservation\\displaySettingDrawer.vue"}, {"key": "settingsuccess", "chinese": "设置成功", "english": "设置成功", "file": "views\\index\\coreFuncHandleObservation\\displaySettingDrawer.vue"}, {"key": "settingfailed", "chinese": "设置失败", "english": "设置失败", "file": "views\\index\\coreFuncHandleObservation\\displaySettingDrawer.vue"}, {"key": "settingtexttexttexttextfailed", "chinese": "设置是否展示失败", "english": "设置是否展示失败", "file": "views\\index\\coreFuncHandleObservation\\displaySettingDrawer.vue"}, {"key": "thisenabletotalcounttext", "chinese": "${this.enableTotalCount}个", "english": "${this.enableTotalCount}个", "file": "views\\index\\coreFuncHandleObservation\\displaySettingDrawer.vue"}, {"key": "texttexttextfunctiontext", "chinese": "已展示功能号：", "english": "已展示功能号：", "file": "views\\index\\coreFuncHandleObservation\\displaySettingDrawer.vue"}], "views\\index\\coreFuncHandleObservation\\index.vue": [{"key": "texttextfunctiontexttexttext", "chinese": "核心功能号处理", "english": "核心功能号处理", "file": "views\\index\\coreFuncHandleObservation\\index.vue"}, {"key": "pleaseselecttexttext", "chinese": "请选择分片", "english": "请选择分片", "file": "views\\index\\coreFuncHandleObservation\\index.vue"}, {"key": "pleaseselecttexttext", "chinese": "请选择集群", "english": "请选择集群", "file": "views\\index\\coreFuncHandleObservation\\index.vue"}, {"key": "texttext", "chinese": "分片", "english": "分片", "file": "views\\index\\coreFuncHandleObservation\\index.vue"}, {"key": "texttext", "chinese": "集群", "english": "集群", "file": "views\\index\\coreFuncHandleObservation\\index.vue"}, {"key": "texttexttexttexttps", "chinese": "执行吞吐(tps)", "english": "执行吞吐(tps)", "file": "views\\index\\coreFuncHandleObservation\\index.vue"}, {"key": "texttexttexttexttexttextns", "chinese": "平均执行耗时(ns)", "english": "平均执行耗时(ns)", "file": "views\\index\\coreFuncHandleObservation\\index.vue"}, {"key": "errortexttexttext", "chinese": "错误次数(次)", "english": "错误次数(次)", "file": "views\\index\\coreFuncHandleObservation\\index.vue"}, {"key": "errortext", "chinese": "错误率(%)", "english": "错误率(%)", "file": "views\\index\\coreFuncHandleObservation\\index.vue"}, {"key": "texttexttexttext", "chinese": "队列积压", "english": "队列积压", "file": "views\\index\\coreFuncHandleObservation\\index.vue"}, {"key": "querytexttextfailed", "chinese": "查询分类失败", "english": "查询分类失败", "file": "views\\index\\coreFuncHandleObservation\\index.vue"}, {"key": "querytexttextinfotexttext", "chinese": "查询分片信息异常", "english": "查询分片信息异常", "file": "views\\index\\coreFuncHandleObservation\\index.vue"}, {"key": "querytexttextinfo", "chinese": "查询集群信息", "english": "查询集群信息", "file": "views\\index\\coreFuncHandleObservation\\index.vue"}, {"key": "texttexttexttextloading", "chinese": "分片、集群加载中", "english": "分片、集群加载中", "file": "views\\index\\coreFuncHandleObservation\\index.vue"}, {"key": "functiontexttexttextsetting", "chinese": "功能号显示设置", "english": "功能号显示设置", "file": "views\\index\\coreFuncHandleObservation\\index.vue"}], "views\\index\\createRule\\createRule.vue": [{"key": "texttextmonitortexttext", "chinese": "获取监控指标", "english": "获取监控指标", "file": "views\\index\\createRule\\createRule.vue"}, {"key": "configtexttexttexttext", "chinese": "配置规则内容", "english": "配置规则内容", "file": "views\\index\\createRule\\createRule.vue"}, {"key": "texttexttexttexttext", "chinese": "预执行内容", "english": "预执行内容", "file": "views\\index\\createRule\\createRule.vue"}, {"key": "sqltexttexttexttextmd", "chinese": "SQL语法说明.md", "english": "SQL语法说明.md", "file": "views\\index\\createRule\\createRule.vue"}, {"key": "texttext", "chinese": "核心", "english": "核心", "file": "views\\index\\createRule\\createRule.vue"}, {"key": "pleaseselecttexttext", "chinese": "请选择核心", "english": "请选择核心", "file": "views\\index\\createRule\\createRule.vue"}, {"key": "pleaseinputtexttexttext", "chinese": "请输入指标名", "english": "请输入指标名", "file": "views\\index\\createRule\\createRule.vue"}, {"key": "texttextsql", "chinese": "执行SQL", "english": "执行SQL", "file": "views\\index\\createRule\\createRule.vue"}, {"key": "texttexttexttext", "chinese": "测试结果", "english": "测试结果", "file": "views\\index\\createRule\\createRule.vue"}, {"key": "texttexttexttext", "chinese": "暂无结果", "english": "暂无结果", "file": "views\\index\\createRule\\createRule.vue"}, {"key": "texttextname", "chinese": "规则名称", "english": "规则名称", "file": "views\\index\\createRule\\createRule.vue"}, {"key": "pleaseinputtexttexttexttexttexttext20texttext", "chinese": "请输入规则名（不超过20字符）", "english": "请输入规则名（不超过20字符）", "file": "views\\index\\createRule\\createRule.vue"}, {"key": "texttexttexttext", "chinese": "规则说明", "english": "规则说明", "file": "views\\index\\createRule\\createRule.vue"}, {"key": "pleaseinputtexttexttexttexttexttexttext200texttext", "chinese": "请输入规则说明（不超过200字符）", "english": "请输入规则说明（不超过200字符）", "file": "views\\index\\createRule\\createRule.vue"}, {"key": "texttexttextabuttonSpanVif", "chinese": ">上一步</a-button>\r\n            <span v-if=", "english": ">上一步</a-button>\r\n            <span v-if=", "file": "views\\index\\createRule\\createRule.vue"}, {"key": "texttextmonitortexttextabuttonAbuttonType", "chinese": ">创建监控规则</a-button>\r\n            <a-button type=", "english": ">创建监控规则</a-button>\r\n            <a-button type=", "file": "views\\index\\createRule\\createRule.vue"}, {"key": "texttext1", "chinese": "指标1", "english": "指标1", "file": "views\\index\\createRule\\createRule.vue"}, {"key": "texttext2", "chinese": "指标2", "english": "指标2", "file": "views\\index\\createRule\\createRule.vue"}, {"key": "texttexttexttexttexttext", "chinese": "输入不能为空", "english": "输入不能为空", "file": "views\\index\\createRule\\createRule.vue"}, {"key": "texttexttexttexttext", "chinese": "指标名重复", "english": "指标名重复", "file": "views\\index\\createRule\\createRule.vue"}, {"key": "texttexttexttextsuccess", "chinese": "获取变量成功", "english": "获取变量成功", "file": "views\\index\\createRule\\createRule.vue"}, {"key": "texttexttexttextfailedtexttexttext", "chinese": "变量获取失败，请重试！", "english": "变量获取失败，请重试！", "file": "views\\index\\createRule\\createRule.vue"}, {"key": "texttexttexttextfailed", "chinese": "获取变量失败,", "english": "获取变量失败,", "file": "views\\index\\createRule\\createRule.vue"}, {"key": "texttextfailedtexttexttext", "chinese": "测试失败，请重试", "english": "测试失败，请重试", "file": "views\\index\\createRule\\createRule.vue"}, {"key": "texttextsqlfailed", "chinese": "测试sql失败", "english": "测试sql失败", "file": "views\\index\\createRule\\createRule.vue"}, {"key": "texttextsuccess", "chinese": "创建成功", "english": "创建成功", "file": "views\\index\\createRule\\createRule.vue"}, {"key": "texttexttexttextfailed", "chinese": "规则创建失败", "english": "规则创建失败", "file": "views\\index\\createRule\\createRule.vue"}, {"key": "querytexttextfailed", "chinese": "查询核心失败", "english": "查询核心失败", "file": "views\\index\\createRule\\createRule.vue"}, {"key": "texttexttexttexttexttext500texttexttext", "chinese": "最大输入长度500个字符", "english": "最大输入长度500个字符", "file": "views\\index\\createRule\\createRule.vue"}, {"key": "texttexttexttexttexttextselecttexttext", "chinese": "只允许写一条select语句", "english": "只允许写一条select语句", "file": "views\\index\\createRule\\createRule.vue"}, {"key": "texttext", "chinese": "留下", "english": "留下", "file": "views\\index\\createRule\\createRule.vue"}, {"key": "texttexttexttexttextoperationtexttexttextsavedatatexttexttexttexttexttexttexttext", "chinese": "离开后当前操作将不会保存，数据会丢失，请谨慎操作！", "english": "离开后当前操作将不会保存，数据会丢失，请谨慎操作！", "file": "views\\index\\createRule\\createRule.vue"}, {"key": "texttextmonitortexttexttexttexttextsql", "chinese": "创建监控规则-自定义SQL", "english": "创建监控规则-自定义SQL", "file": "views\\index\\createRule\\createRule.vue"}, {"key": "texttexttextsql", "chinese": "预执行SQL", "english": "预执行SQL", "file": "views\\index\\createRule\\createRule.vue"}, {"key": "texttexttextsqltexttexttexttexttexttexttexttexttexttexttexttexttexttexttexttexttexttextsqltexttexttexttexttext", "chinese": "“预执行SQL”执行后可得到对应变量，变量可在下方“执行SQL”被引用。点击", "english": "“预执行SQL”执行后可得到对应变量，变量可在下方“执行SQL”被引用。点击", "file": "views\\index\\createRule\\createRule.vue"}, {"key": "texttextsqltexttexttexttext", "chinese": "下载SQL语法指南", "english": "下载SQL语法指南", "file": "views\\index\\createRule\\createRule.vue"}, {"key": "texttexttexttext", "chinese": "获取变量", "english": "获取变量", "file": "views\\index\\createRule\\createRule.vue"}, {"key": "texttexttexttexttexttexttexttexttexttexttexttexttexttexttexttexttexttexttexttext", "chinese": "点击“获取变量”，生成对应可引用变量，在下方显示", "english": "点击“获取变量”，生成对应可引用变量，在下方显示", "file": "views\\index\\createRule\\createRule.vue"}, {"key": "texttexttexttexttext", "chinese": "可引用变量", "english": "可引用变量", "file": "views\\index\\createRule\\createRule.vue"}, {"key": "texttexttexttexttexttext", "chinese": "正式执行内容", "english": "正式执行内容", "file": "views\\index\\createRule\\createRule.vue"}, {"key": "sqltexttexttexttext", "chinese": "SQL语法指南", "english": "SQL语法指南", "file": "views\\index\\createRule\\createRule.vue"}, {"key": "texttexttexttexttexttexttexttexttexttexttexttexttexttexttexttexttexttexttexttexttexttexttext", "chinese": "统一策略：取结果集的第一行第一列，结果集必须是数字", "english": "统一策略：取结果集的第一行第一列，结果集必须是数字", "file": "views\\index\\createRule\\createRule.vue"}, {"key": "sqltexttext", "chinese": "SQL结果", "english": "SQL结果", "file": "views\\index\\createRule\\createRule.vue"}, {"key": "texttext", "chinese": "测试", "english": "测试", "file": "views\\index\\createRule\\createRule.vue"}, {"key": "texttexttexttexttexttexttexttexttexttextdatatexttexttexttexttexttexttexttextsettingtexttexttexttexttexttexttext", "chinese": "针对上一步获取的比对数据所形成的告警指标，设置对应的告警阈值。", "english": "针对上一步获取的比对数据所形成的告警指标，设置对应的告警阈值。", "file": "views\\index\\createRule\\createRule.vue"}, {"key": "texttexttext", "chinese": "上一步", "english": "上一步", "file": "views\\index\\createRule\\createRule.vue"}, {"key": "texttexttextconfigtexttexttexttext", "chinese": "下一步：配置规则内容", "english": "下一步：配置规则内容", "file": "views\\index\\createRule\\createRule.vue"}, {"key": "texttextmonitortexttext", "chinese": "创建监控规则", "english": "创建监控规则", "file": "views\\index\\createRule\\createRule.vue"}], "views\\index\\dataSecondAppearance.vue": [{"key": "pagenameDetailDatatexttexttexttext", "chinese": "pageName === 'detail' ? '数据二次上场' : ''", "english": "pageName === 'detail' ? '数据二次上场' : ''", "file": "views\\index\\dataSecondAppearance.vue"}, {"key": "datatexttexttexttext", "chinese": "数据二次上场", "english": "数据二次上场", "file": "views\\index\\dataSecondAppearance.vue"}, {"key": "texttexttexttexttextPagename", "chinese": ", // 选中的产品\r\n            pageName:", "english": ", // 选中的产品\r\n            pageName:", "file": "views\\index\\dataSecondAppearance.vue"}, {"key": "tabtexttexttexttextTitletext", "chinese": ", // tab默认选择\r\n            titleText:", "english": ", // tab默认选择\r\n            titleText:", "file": "views\\index\\dataSecondAppearance.vue"}, {"key": "texttexttexttexttexttexttexttext", "chinese": "创建二次上场任务", "english": "创建二次上场任务", "file": "views\\index\\dataSecondAppearance.vue"}], "views\\index\\emergencyManagementConfig\\addModal.vue": [{"key": "addtexttext", "chinese": "添加路由", "english": "添加路由", "file": "views\\index\\emergencyManagementConfig\\addModal.vue"}, {"key": "addtexttextfailed", "chinese": "添加路由失败", "english": "添加路由失败", "file": "views\\index\\emergencyManagementConfig\\addModal.vue"}, {"key": "texttext", "chinese": "确定", "english": "确定", "file": "views\\index\\emergencyManagementConfig\\addModal.vue"}], "views\\index\\emergencyManagementConfig\\helpModal.vue": [{"key": "configtexttext", "chinese": "配置说明", "english": "配置说明", "file": "views\\index\\emergencyManagementConfig\\helpModal.vue"}], "views\\index\\emergencyManagementConfig\\index.vue": [{"key": "configtexttextedit", "chinese": "配置应急修改", "english": "配置应急修改", "file": "views\\index\\emergencyManagementConfig\\index.vue"}, {"key": "texttexttexttextname", "chinese": "输入节点名称", "english": "输入节点名称", "file": "views\\index\\emergencyManagementConfig\\index.vue"}, {"key": "texttexttextconfigtexttextfailed", "chinese": "初始化配置应急失败", "english": "初始化配置应急失败", "file": "views\\index\\emergencyManagementConfig\\index.vue"}], "views\\index\\emergencyManagementConfig\\routeConfig.vue": [{"key": "texttextconfig", "chinese": "路由配置", "english": "路由配置", "file": "views\\index\\emergencyManagementConfig\\routeConfig.vue"}, {"key": "texttexttexttextVersion", "chinese": ">\r\n                当前版本：{{ version ||", "english": ">\r\n                当前版本：{{ version ||", "file": "views\\index\\emergencyManagementConfig\\routeConfig.vue"}, {"key": "texttextfunctiontext", "chinese": "输入功能号", "english": "输入功能号", "file": "views\\index\\emergencyManagementConfig\\routeConfig.vue"}, {"key": "editconfig", "chinese": "修改配置", "english": "修改配置", "file": "views\\index\\emergencyManagementConfig\\routeConfig.vue"}, {"key": "configjsontexttext", "chinese": "配置json预览", "english": "配置json预览", "file": "views\\index\\emergencyManagementConfig\\routeConfig.vue"}, {"key": "texttexttexttexttext", "chinese": "目标系统号", "english": "目标系统号", "file": "views\\index\\emergencyManagementConfig\\routeConfig.vue"}, {"key": "texttext", "chinese": "节点", "english": "节点", "file": "views\\index\\emergencyManagementConfig\\routeConfig.vue"}, {"key": "texttexttextid", "chinese": "目标端ID", "english": "目标端ID", "file": "views\\index\\emergencyManagementConfig\\routeConfig.vue"}, {"key": "operation", "chinese": "操作", "english": "操作", "file": "views\\index\\emergencyManagementConfig\\routeConfig.vue"}, {"key": "delete", "chinese": "删除", "english": "删除", "file": "views\\index\\emergencyManagementConfig\\routeConfig.vue"}, {"key": "texttextfailed", "chinese": "更新失败", "english": "更新失败", "file": "views\\index\\emergencyManagementConfig\\routeConfig.vue"}, {"key": "texttextsuccess", "chinese": "更新成功", "english": "更新成功", "file": "views\\index\\emergencyManagementConfig\\routeConfig.vue"}, {"key": "texttexttexttextconfigfailed", "chinese": "获取路由配置失败", "english": "获取路由配置失败", "file": "views\\index\\emergencyManagementConfig\\routeConfig.vue"}, {"key": "texttexttexttexttextconfigtext", "chinese": "确定要更新配置吗？", "english": "确定要更新配置吗？", "file": "views\\index\\emergencyManagementConfig\\routeConfig.vue"}, {"key": "texttextconfigtexttexttexttexttexttexttexttexttexttexttexttext", "chinese": "参数配置更新后实时生效，重启后失效。", "english": "参数配置更新后实时生效，重启后失效。", "file": "views\\index\\emergencyManagementConfig\\routeConfig.vue"}, {"key": "texttexttextdeletetexttexttext", "chinese": "确定要删除该路由？", "english": "确定要删除该路由？", "file": "views\\index\\emergencyManagementConfig\\routeConfig.vue"}, {"key": "texttextconfigtextversioninfotexttextadddeleteedittexttext", "chinese": "路由配置无version信息，不可添加、删除、修改配置。", "english": "路由配置无version信息，不可添加、删除、修改配置。", "file": "views\\index\\emergencyManagementConfig\\routeConfig.vue"}, {"key": "texttexttexttextVersion", "chinese": "当前版本：{{ version || \"-\" }}", "english": "当前版本：{{ version || \"-\" }}", "file": "views\\index\\emergencyManagementConfig\\routeConfig.vue"}, {"key": "texttextconfig", "chinese": "更新配置", "english": "更新配置", "file": "views\\index\\emergencyManagementConfig\\routeConfig.vue"}], "views\\index\\emergencyManagementConfig\\routeInfoForm.vue": [{"key": "pleaseselecttextedittexttexttext", "chinese": "请选择要修改的路由", "english": "请选择要修改的路由", "file": "views\\index\\emergencyManagementConfig\\routeInfoForm.vue"}, {"key": "pleaseInput", "chinese": "请输入", "english": "请输入", "file": "views\\index\\emergencyManagementConfig\\routeInfoForm.vue"}, {"key": "texttexttexttexttextidfailed", "chinese": "获取目标端id失败", "english": "获取目标端id失败", "file": "views\\index\\emergencyManagementConfig\\routeInfoForm.vue"}, {"key": "texttexttexttexttexttexttexttexttexttextfunctiontexttexttexttexttexttexttexttexttext", "chinese": "支持*、?、数字(不为负值)。多个功能号使用英文分号分隔", "english": "支持*、?、数字(不为负值)。多个功能号使用英文分号分隔", "file": "views\\index\\emergencyManagementConfig\\routeInfoForm.vue"}, {"key": "texttexttexttexttexttexttexttexttexttext255texttextfunctiontexttexttexttexttexttexttexttexttext", "chinese": "支持*、数字(不为负值、最大255)。多个功能号使用英文分号分隔", "english": "支持*、数字(不为负值、最大255)。多个功能号使用英文分号分隔", "file": "views\\index\\emergencyManagementConfig\\routeInfoForm.vue"}, {"key": "texttexttexttexttexttexttexttexttexttext65535", "chinese": "支持*、数字(不为负值、最大65535)", "english": "支持*、数字(不为负值、最大65535)", "file": "views\\index\\emergencyManagementConfig\\routeInfoForm.vue"}], "views\\index\\latencyTrendAnalysis.vue": [{"key": "texttexttexttexttexttexttexttext", "chinese": "链路时延趋势分析", "english": "链路时延趋势分析", "file": "views\\index\\latencyTrendAnalysis.vue"}, {"key": "texttexttexttext", "chinese": "选择日期", "english": "选择日期", "file": "views\\index\\latencyTrendAnalysis.vue"}, {"key": "texttexttexttext", "chinese": "业务类型", "english": "业务类型", "file": "views\\index\\latencyTrendAnalysis.vue"}, {"key": "texttextstatus", "chinese": "链路状态", "english": "链路状态", "file": "views\\index\\latencyTrendAnalysis.vue"}, {"key": "texttexttexttext", "chinese": "选择跨度", "english": "选择跨度", "file": "views\\index\\latencyTrendAnalysis.vue"}, {"key": "noData", "chinese": "暂无数据", "english": "暂无数据", "file": "views\\index\\latencyTrendAnalysis.vue"}, {"key": "texttexttexttexttexttexttexttext", "chinese": "应用分段时延趋势", "english": "应用分段时延趋势", "file": "views\\index\\latencyTrendAnalysis.vue"}, {"key": "texttexttexttexttexttexttexttext", "chinese": "应用分段时延统计", "english": "应用分段时延统计", "file": "views\\index\\latencyTrendAnalysis.vue"}, {"key": "90texttextp90", "chinese": "90分位（p90）", "english": "90分位（p90）", "file": "views\\index\\latencyTrendAnalysis.vue"}, {"key": "texttexttexttexttexttext", "chinese": "查看时延分布", "english": "查看时延分布", "file": "views\\index\\latencyTrendAnalysis.vue"}, {"key": "texttexttexttexttexttexttexttext", "chinese": "应用分段时延分布", "english": "应用分段时延分布", "file": "views\\index\\latencyTrendAnalysis.vue"}, {"key": "texttexttexttexttexttexttexttext", "chinese": "应用逐笔时延统计", "english": "应用逐笔时延统计", "file": "views\\index\\latencyTrendAnalysis.vue"}, {"key": "pleaseselectquerytexttext", "chinese": "请选择查询分页", "english": "请选择查询分页", "file": "views\\index\\latencyTrendAnalysis.vue"}, {"key": "texttexttext", "chinese": "中位数", "english": "中位数", "file": "views\\index\\latencyTrendAnalysis.vue"}, {"key": "texttexttext", "chinese": "平均值", "english": "平均值", "file": "views\\index\\latencyTrendAnalysis.vue"}, {"key": "texttexttext", "chinese": "最大值", "english": "最大值", "file": "views\\index\\latencyTrendAnalysis.vue"}, {"key": "texttexttext", "chinese": "最小值", "english": "最小值", "file": "views\\index\\latencyTrendAnalysis.vue"}, {"key": "90texttext", "chinese": "90分位", "english": "90分位", "file": "views\\index\\latencyTrendAnalysis.vue"}, {"key": "95texttext", "chinese": "95分位", "english": "95分位", "file": "views\\index\\latencyTrendAnalysis.vue"}, {"key": "99texttext", "chinese": "99分位", "english": "99分位", "file": "views\\index\\latencyTrendAnalysis.vue"}, {"key": "xtexttextnameHtmlstr", "chinese": ";// x轴的名称\r\n\r\n                htmlStr +=", "english": ";// x轴的名称\r\n\r\n                htmlStr +=", "file": "views\\index\\latencyTrendAnalysis.vue"}, {"key": "texttexttexttext", "chinese": "委托笔数:", "english": "委托笔数:", "file": "views\\index\\latencyTrendAnalysis.vue"}, {"key": "texttext", "chinese": "时延:", "english": "时延:", "file": "views\\index\\latencyTrendAnalysis.vue"}, {"key": "timetexttextstimeEtimeTexttexttexttexttextcount0text", "chinese": "时间范围：${sTime} - ${eTime} 总记录笔数：${count || 0}笔", "english": "时间范围：${sTime} - ${eTime} 总记录笔数：${count || 0}笔", "file": "views\\index\\latencyTrendAnalysis.vue"}, {"key": "texttexttexttexttexttextconfigtexttexttexttexttexttexttexttexttexttexttexttexttexttexttexttexttext", "chinese": "当前产品尚未配置模型，请前往“产品服务配置”进行模型配置", "english": "当前产品尚未配置模型，请前往“产品服务配置”进行模型配置", "file": "views\\index\\latencyTrendAnalysis.vue"}, {"key": "texttextconfig", "chinese": "前往配置", "english": "前往配置", "file": "views\\index\\latencyTrendAnalysis.vue"}, {"key": "xtexttextnameHtmlstr", "chinese": "';// x轴的名称\r\n\r\n                htmlStr += '", "english": "';// x轴的名称\r\n\r\n                htmlStr += '", "file": "views\\index\\latencyTrendAnalysis.vue"}, {"key": "texttext", "chinese": "查看", "english": "查看", "file": "views\\index\\latencyTrendAnalysis.vue"}], "views\\index\\ldpDataObservation.vue": [{"key": "texttexttexttext", "chinese": "集合竞价", "english": "集合竞价", "file": "views\\index\\ldpDataObservation.vue"}, {"key": "texttexttexttexttexttext", "chinese": "早盘竞价交易", "english": "早盘竞价交易", "file": "views\\index\\ldpDataObservation.vue"}, {"key": "texttext", "chinese": "盘休", "english": "盘休", "file": "views\\index\\ldpDataObservation.vue"}, {"key": "texttexttexttexttexttext", "chinese": "午盘竞价交易", "english": "午盘竞价交易", "file": "views\\index\\ldpDataObservation.vue"}, {"key": "texttexttexttext", "chinese": "盘后定价", "english": "盘后定价", "file": "views\\index\\ldpDataObservation.vue"}, {"key": "texttexttexttexttexttexttexttexttextobservationtexttexttexttexttexttexttext", "chinese": "当前应用节点不支持观测！请重新选择节点", "english": "当前应用节点不支持观测！请重新选择节点", "file": "views\\index\\ldpDataObservation.vue"}, {"key": "texttexttextCurrenttime", "chinese": "今天是: {{ currentTime }}", "english": "今天是: {{ currentTime }}", "file": "views\\index\\ldpDataObservation.vue"}], "views\\index\\ldpLinkConfig.vue": [{"key": "texttexttexttextconfigmanage", "chinese": "产品节点配置管理", "english": "产品节点配置管理", "file": "views\\index\\ldpLinkConfig.vue"}, {"key": "deletesuccess", "chinese": "删除成功", "english": "删除成功", "file": "views\\index\\ldpLinkConfig.vue"}, {"key": "deletefailed", "chinese": "删除失败", "english": "删除失败", "file": "views\\index\\ldpLinkConfig.vue"}, {"key": "texttexttext", "chinese": ") {\r\n                // 如果是", "english": ") {\r\n                // 如果是", "file": "views\\index\\ldpLinkConfig.vue"}, {"key": "texttexttextdeletetexttextthisproductinfoproductnametexttexttexttexttext", "chinese": "您确定删除名为\"${this.productInfo.productName}\"产品节点吗？", "english": "您确定删除名为\"${this.productInfo.productName}\"产品节点吗？", "file": "views\\index\\ldpLinkConfig.vue"}, {"key": "texttexttexttexttexttext", "chinese": "对接产品节点", "english": "对接产品节点", "file": "views\\index\\ldpLinkConfig.vue"}, {"key": "texttexttexttexttexttexttext", "chinese": "已注册产品节点", "english": "已注册产品节点", "file": "views\\index\\ldpLinkConfig.vue"}, {"key": "deletetexttexttexttext", "chinese": "删除产品节点", "english": "删除产品节点", "file": "views\\index\\ldpLinkConfig.vue"}, {"key": "texttexttexttextinfo", "chinese": "同步节点信息", "english": "同步节点信息", "file": "views\\index\\ldpLinkConfig.vue"}], "views\\index\\ldpLogCenter.vue": [{"key": "texttexterrortexttexttexttext", "chinese": "回库错误重试运维", "english": "回库错误重试运维", "file": "views\\index\\ldpLogCenter.vue"}, {"key": "texttexterrortexttext", "chinese": "回库错误重试", "english": "回库错误重试", "file": "views\\index\\ldpLogCenter.vue"}, {"key": "texttexterrortexttext", "chinese": "回库错误日志", "english": "回库错误日志", "file": "views\\index\\ldpLogCenter.vue"}, {"key": "texttextCurrentdate", "chinese": "日期：{{ currentDate }}", "english": "日期：{{ currentDate }}", "file": "views\\index\\ldpLogCenter.vue"}, {"key": "texttextquerytexttexttime", "chinese": "日志查询超时时间：", "english": "日志查询超时时间：", "file": "views\\index\\ldpLogCenter.vue"}], "views\\index\\ldpMonitor\\clusterMonitor.vue": [{"key": "texttextstatus", "chinese": "集群状态", "english": "集群状态", "file": "views\\index\\ldpMonitor\\clusterMonitor.vue"}, {"key": "texttextstatus", "chinese": "应用状态", "english": "应用状态", "file": "views\\index\\ldpMonitor\\clusterMonitor.vue"}], "views\\index\\ldpMonitor\\index.vue": [{"key": "texttext", "chinese": "盘前", "english": "盘前", "file": "views\\index\\ldpMonitor\\index.vue"}, {"key": "texttext", "chinese": "盘中", "english": "盘中", "file": "views\\index\\ldpMonitor\\index.vue"}, {"key": "texttext", "chinese": "盘后", "english": "盘后", "file": "views\\index\\ldpMonitor\\index.vue"}, {"key": "text", "chinese": "，在", "english": "，在", "file": "views\\index\\ldpMonitor\\index.vue"}, {"key": "text", "chinese": "和", "english": "和", "file": "views\\index\\ldpMonitor\\index.vue"}, {"key": "texttexttexttexttexttexttextTexttexttexttexttexttexttexttext", "chinese": "之间跳转的时候，\r\n    // 由于会渲染同样的", "english": "之间跳转的时候，\r\n    // 由于会渲染同样的", "file": "views\\index\\ldpMonitor\\index.vue"}], "views\\index\\ldpMonitor\\ldpAppMonitor.vue": [{"key": "texttexttexttext", "chinese": "核心性能", "english": "核心性能", "file": "views\\index\\ldpMonitor\\ldpAppMonitor.vue"}], "views\\index\\ldpTable.vue": [{"key": "querytexttexttext", "chinese": "查询内存表", "english": "查询内存表", "file": "views\\index\\ldpTable.vue"}, {"key": "dataquery", "chinese": "数据查询", "english": "数据查询", "file": "views\\index\\ldpTable.vue"}, {"key": "dataedit", "chinese": "数据修改", "english": "数据修改", "file": "views\\index\\ldpTable.vue"}, {"key": "texttexttexttexttext", "chinese": "内存表结构", "english": "内存表结构", "file": "views\\index\\ldpTable.vue"}, {"key": "texttexttexttexttexttexttexttexttexttexttexttexttexttexttext", "chinese": "请从左侧菜单选择内存表进行查看！", "english": "请从左侧菜单选择内存表进行查看！", "file": "views\\index\\ldpTable.vue"}, {"key": "pleaseselecttexttexttexttexttexttexttext", "chinese": "请选择并连接应用节点！", "english": "请选择并连接应用节点！", "file": "views\\index\\ldpTable.vue"}], "views\\index\\locateConfig.vue": [{"key": "locateconfigtexttexttexttexttext", "chinese": "Locate配置一致性校验", "english": "Locate配置一致性校验", "file": "views\\index\\locateConfig.vue"}, {"key": "texttextconfigmanage", "chinese": "节点配置管理", "english": "节点配置管理", "file": "views\\index\\locateConfig.vue"}, {"key": "texttextconfigtexttext", "chinese": "节点配置校验", "english": "节点配置校验", "file": "views\\index\\locateConfig.vue"}], "views\\index\\managementQuery.vue": [{"key": "instancelistlengthPleaseselecttexttextTexttexttexttexttexttexttexttext", "chinese": "instanceList.length ? '请选择节点': '当前暂无活跃节点'", "english": "instanceList.length ? '请选择节点': '当前暂无活跃节点'", "file": "views\\index\\managementQuery.vue"}, {"key": "texttexttexttexttextquery", "chinese": "输入节点名查询", "english": "输入节点名查询", "file": "views\\index\\managementQuery.vue"}, {"key": "searchmanagefunction", "chinese": "搜索管理功能", "english": "搜索管理功能", "file": "views\\index\\managementQuery.vue"}, {"key": "texttext", "chinese": ">显示", "english": ">显示", "file": "views\\index\\managementQuery.vue"}, {"key": "pleaseselectmanagefunctiontexttexttexttexttexttext", "chinese": "请选择管理功能手动发起请求", "english": "请选择管理功能手动发起请求", "file": "views\\index\\managementQuery.vue"}, {"key": "pleaseselectmanagefunction", "chinese": "请选择管理功能", "english": "请选择管理功能", "file": "views\\index\\managementQuery.vue"}, {"key": "pleaseselecttexttext", "chinese": "请选择节点", "english": "请选择节点", "file": "views\\index\\managementQuery.vue"}, {"key": "texttexttexttexttexttexttexttext", "chinese": "当前暂无活跃节点", "english": "当前暂无活跃节点", "file": "views\\index\\managementQuery.vue"}, {"key": "texttext", "chinese": "暂无", "english": "暂无", "file": "views\\index\\managementQuery.vue"}, {"key": "texttextdata", "chinese": "其他数据", "english": "其他数据", "file": "views\\index\\managementQuery.vue"}, {"key": "jsontexttexttexttexttexttexttextjavascripttexttexttexttextImport", "chinese": ";\r\n// JSON代码高亮需要由JavaScript插件支持\r\nimport", "english": ";\r\n// JSON代码高亮需要由JavaScript插件支持\r\nimport", "file": "views\\index\\managementQuery.vue"}, {"key": "managefunctiontexttexttexttextApmtime", "chinese": ", // 管理功能执行耗时\r\n            apmTime:", "english": ", // 管理功能执行耗时\r\n            apmTime:", "file": "views\\index\\managementQuery.vue"}, {"key": "texttexttexttexttextjsonpathconfigfailed", "chinese": "初始化默认jsonPath配置失败:", "english": "初始化默认jsonPath配置失败:", "file": "views\\index\\managementQuery.vue"}, {"key": "texttextsavetexttexttexttextfunctiontexttexttexttexttexttexttexttexttexttexttexttextquerytexttext", "chinese": "如需保存当前选中功能号输入参数，请手动触发一次查询请求！", "english": "如需保存当前选中功能号输入参数，请手动触发一次查询请求！", "file": "views\\index\\managementQuery.vue"}, {"key": "texttext", "chinese": "下载", "english": "下载", "file": "views\\index\\managementQuery.vue"}, {"key": "texttextjsonpathconfigfailed", "chinese": "获取jsonPath配置失败:", "english": "获取jsonPath配置失败:", "file": "views\\index\\managementQuery.vue"}, {"key": "functiontexttexttexttextdata", "chinese": "功能号无返回数据！", "english": "功能号无返回数据！", "file": "views\\index\\managementQuery.vue"}, {"key": "export", "chinese": "导出", "english": "导出", "file": "views\\index\\managementQuery.vue"}, {"key": "texttextexport", "chinese": "批量导出", "english": "批量导出", "file": "views\\index\\managementQuery.vue"}, {"key": "texttexttexttextexporttexttexttexttexttexttexttexttextmanagefunctiondata", "chinese": "支持批量导出当前产品下的所有管理功能数据", "english": "支持批量导出当前产品下的所有管理功能数据", "file": "views\\index\\managementQuery.vue"}, {"key": "texttextexport", "chinese": "快捷导出", "english": "快捷导出", "file": "views\\index\\managementQuery.vue"}, {"key": "texttexttexttextexporttexttexttexttextmanagetexttexttexttexttextgetfuncdetailinfotexttextfunction", "chinese": "点击一键导出所选产品管理所有核心的「GetFuncDetailInfo」管理功能", "english": "点击一键导出所选产品管理所有核心的「GetFuncDetailInfo」管理功能", "file": "views\\index\\managementQuery.vue"}, {"key": "childchinesenameTexttext", "chinese": "{{  child.chineseName || '暂无'}}", "english": "{{  child.chineseName || '暂无'}}", "file": "views\\index\\managementQuery.vue"}, {"key": "textquerytextmanagefunction", "chinese": "未查询到管理功能", "english": "未查询到管理功能", "file": "views\\index\\managementQuery.vue"}, {"key": "texttexttexttexttexttext", "chinese": "显示\"中文译名\"", "english": "显示\"中文译名\"", "file": "views\\index\\managementQuery.vue"}, {"key": "texttexttexttext", "chinese": "整体耗时：", "english": "整体耗时：", "file": "views\\index\\managementQuery.vue"}, {"key": "apmtexttext", "chinese": "APM耗时：", "english": "APM耗时：", "file": "views\\index\\managementQuery.vue"}, {"key": "texttexttexttexttexttext", "chinese": "应用节点耗时：", "english": "应用节点耗时：", "file": "views\\index\\managementQuery.vue"}, {"key": "configtable", "chinese": "配置表格", "english": "配置表格", "file": "views\\index\\managementQuery.vue"}, {"key": "texttexttexttext", "chinese": "关闭全部", "english": "关闭全部", "file": "views\\index\\managementQuery.vue"}], "views\\index\\marketAllLink.vue": [{"key": "texttexttexttexttexttexttexttexttexttexttext", "chinese": "行情全链路应用节点关系", "english": "行情全链路应用节点关系", "file": "views\\index\\marketAllLink.vue"}, {"key": "texttexttexttexttexttexttexttexttextmonitor", "chinese": "终端客户全链路质量监控", "english": "终端客户全链路质量监控", "file": "views\\index\\marketAllLink.vue"}, {"key": "texttextdatatexttexttexttextmonitor", "chinese": "行情数据时延趋势监控", "english": "行情数据时延趋势监控", "file": "views\\index\\marketAllLink.vue"}, {"key": "nsqtexttext", "chinese": "NSQ链路", "english": "NSQ链路", "file": "views\\index\\marketAllLink.vue"}, {"key": "fpgatexttext", "chinese": "FPGA链路", "english": "FPGA链路", "file": "views\\index\\marketAllLink.vue"}, {"key": "fpgatexttexttext", "chinese": "FPGA全链路", "english": "FPGA全链路", "file": "views\\index\\marketAllLink.vue"}, {"key": "texttexttexttextstatus", "chinese": "应用节点状态", "english": "应用节点状态", "file": "views\\index\\marketAllLink.vue"}, {"key": "texttexttexttexttext", "chinese": "时延正常率", "english": "时延正常率", "file": "views\\index\\marketAllLink.vue"}, {"key": "nsqtexttexttext", "chinese": "NSQ全链路", "english": "NSQ全链路", "file": "views\\index\\marketAllLink.vue"}], "views\\index\\marketMonitor.vue": [{"key": "texttexttexttexttexttexttexttexttexttexttextinfo", "chinese": "当前实例无采集时延走势信息", "english": "当前实例无采集时延走势信息", "file": "views\\index\\marketMonitor.vue"}, {"key": "texttexttexttexttexttexttexttexttexttexttext", "chinese": "该模型文件暂无拓扑结构", "english": "该模型文件暂无拓扑结构", "file": "views\\index\\marketMonitor.vue"}, {"key": "texttexttexttexttext", "chinese": "最近五分钟", "english": "最近五分钟", "file": "views\\index\\marketMonitor.vue"}, {"key": "texttexttexttexttexttext", "chinese": "最近十五分钟", "english": "最近十五分钟", "file": "views\\index\\marketMonitor.vue"}, {"key": "texttexttexttexttexttext", "chinese": "最近三十分钟", "english": "最近三十分钟", "file": "views\\index\\marketMonitor.vue"}, {"key": "texttexttexttexts", "chinese": "快照行情(μs)", "english": "快照行情(μs)", "file": "views\\index\\marketMonitor.vue"}, {"key": "texttexttexttexts", "chinese": "指数行情(μs)", "english": "指数行情(μs)", "file": "views\\index\\marketMonitor.vue"}, {"key": "texttexttexttexts", "chinese": "逐笔委托(μs)", "english": "逐笔委托(μs)", "file": "views\\index\\marketMonitor.vue"}, {"key": "texttexttexttexts", "chinese": "逐笔成交(μs)", "english": "逐笔成交(μs)", "file": "views\\index\\marketMonitor.vue"}, {"key": "getproducttypeproductinfoproducttypeTexttextmonitor", "chinese": "{{ $getProductType(productInfo.productType) }}时延监控", "english": "{{ $getProductType(productInfo.productType) }}时延监控", "file": "views\\index\\marketMonitor.vue"}, {"key": "texttexttexttext", "chinese": "显示拓扑", "english": "显示拓扑", "file": "views\\index\\marketMonitor.vue"}], "views\\index\\marketNodeDelayList.vue": [{"key": "texttexttexttexttexttexttexttexttexttexttexttext", "chinese": "行情产品节点穿透时延分析", "english": "行情产品节点穿透时延分析", "file": "views\\index\\marketNodeDelayList.vue"}, {"key": "texttextquery", "chinese": "汇总查询", "english": "汇总查询", "file": "views\\index\\marketNodeDelayList.vue"}, {"key": "texttexttext", "chinese": "交易日", "english": "交易日", "file": "views\\index\\marketNodeDelayList.vue"}, {"key": "texttextmin", "chinese": "最小(min)", "english": "最小(min)", "file": "views\\index\\marketNodeDelayList.vue"}, {"key": "1texttexttextp1", "chinese": "1分位数(p1)", "english": "1分位数(p1)", "file": "views\\index\\marketNodeDelayList.vue"}, {"key": "texttexttextp50", "chinese": "中位数(p50)", "english": "中位数(p50)", "file": "views\\index\\marketNodeDelayList.vue"}, {"key": "texttexttextavg", "chinese": "平均数(avg)", "english": "平均数(avg)", "file": "views\\index\\marketNodeDelayList.vue"}, {"key": "95texttexttextp95", "chinese": "95分位数(p95)", "english": "95分位数(p95)", "file": "views\\index\\marketNodeDelayList.vue"}, {"key": "99texttexttextp99", "chinese": "99分位数(p99)", "english": "99分位数(p99)", "file": "views\\index\\marketNodeDelayList.vue"}, {"key": "texttextmax", "chinese": "最大(max)", "english": "最大(max)", "file": "views\\index\\marketNodeDelayList.vue"}, {"key": "texttexttexttext", "chinese": "统计总数", "english": "统计总数", "file": "views\\index\\marketNodeDelayList.vue"}, {"key": "texttexttexttext", "chinese": "交易日期", "english": "交易日期", "file": "views\\index\\marketNodeDelayList.vue"}, {"key": "texttexttime", "chinese": "交易时间", "english": "交易时间", "file": "views\\index\\marketNodeDelayList.vue"}, {"key": "queryfailed", "chinese": "查询失败!", "english": "查询失败!", "file": "views\\index\\marketNodeDelayList.vue"}], "views\\index\\marketPenetrateList.vue": [{"key": "texttexttext", "chinese": "下单数", "english": "下单数", "file": "views\\index\\marketPenetrateList.vue"}, {"key": "texttexttexttexttext", "chinese": "全链路时延", "english": "全链路时延", "file": "views\\index\\marketPenetrateList.vue"}, {"key": "texttexttexttexttexttext", "chinese": "柜台上行时延", "english": "柜台上行时延", "file": "views\\index\\marketPenetrateList.vue"}, {"key": "texttexttexttexttexttext", "chinese": "柜台下行时延", "english": "柜台下行时延", "file": "views\\index\\marketPenetrateList.vue"}, {"key": "tgwtexttexttexttext", "chinese": "TGW上行时延", "english": "TGW上行时延", "file": "views\\index\\marketPenetrateList.vue"}, {"key": "tgwtexttexttexttext", "chinese": "TGW下行时延", "english": "TGW下行时延", "file": "views\\index\\marketPenetrateList.vue"}, {"key": "tgwtexttext", "chinese": "TGW时延", "english": "TGW时延", "file": "views\\index\\marketPenetrateList.vue"}, {"key": "texttexttexttexttext", "chinese": "交易所时延", "english": "交易所时延", "file": "views\\index\\marketPenetrateList.vue"}, {"key": "texttexttexttexttexttext", "chinese": "行情-交易时延", "english": "行情-交易时延", "file": "views\\index\\marketPenetrateList.vue"}, {"key": "texttexttexttexttexttexttexttext", "chinese": "交易-收到行情时延", "english": "交易-收到行情时延", "file": "views\\index\\marketPenetrateList.vue"}, {"key": "texttexttexttexttexttexttext", "chinese": "防火墙上行时延", "english": "防火墙上行时延", "file": "views\\index\\marketPenetrateList.vue"}, {"key": "texttexttexttexttexttexttext", "chinese": "防火墙下行时延", "english": "防火墙下行时延", "file": "views\\index\\marketPenetrateList.vue"}, {"key": "texttexttexttext", "chinese": "拓扑结构", "english": "拓扑结构", "file": "views\\index\\marketPenetrateList.vue"}, {"key": "texttexttexttext", "chinese": "交易市场", "english": "交易市场", "file": "views\\index\\marketPenetrateList.vue"}, {"key": "texttexttexttexttext", "chinese": "深圳交易所", "english": "深圳交易所", "file": "views\\index\\marketPenetrateList.vue"}, {"key": "texttexttexttexttext", "chinese": "上海交易所", "english": "上海交易所", "file": "views\\index\\marketPenetrateList.vue"}, {"key": "texttexttexttext", "chinese": "交易核心", "english": "交易核心", "file": "views\\index\\marketPenetrateList.vue"}, {"key": "texttexttexttext", "chinese": "报盘网关", "english": "报盘网关", "file": "views\\index\\marketPenetrateList.vue"}, {"key": "texttexttexttext", "chinese": "行情网关", "english": "行情网关", "file": "views\\index\\marketPenetrateList.vue"}, {"key": "texttexttexttext", "chinese": "资金账户", "english": "资金账户", "file": "views\\index\\marketPenetrateList.vue"}, {"key": "texttexttexttext", "chinese": "证券代码", "english": "证券代码", "file": "views\\index\\marketPenetrateList.vue"}, {"key": "texttexttext", "chinese": "席位号", "english": "席位号", "file": "views\\index\\marketPenetrateList.vue"}, {"key": "texttexttexttext", "chinese": "汇总间隔", "english": "汇总间隔", "file": "views\\index\\marketPenetrateList.vue"}, {"key": "text", "chinese": "天", "english": "天", "file": "views\\index\\marketPenetrateList.vue"}, {"key": "texttexttexttext", "chinese": "汇总方式", "english": "汇总方式", "file": "views\\index\\marketPenetrateList.vue"}, {"key": "detailquery", "chinese": "详情查询", "english": "详情查询", "file": "views\\index\\marketPenetrateList.vue"}, {"key": "texttext", "chinese": "日期", "english": "日期", "file": "views\\index\\marketPenetrateList.vue"}, {"key": "texttexttexttext", "chinese": "业务字段", "english": "业务字段", "file": "views\\index\\marketPenetrateList.vue"}], "views\\index\\marketTimeDelay.vue": [{"key": "texttexttime", "chinese": "选择时间", "english": "选择时间", "file": "views\\index\\marketTimeDelay.vue"}, {"key": "texttexttexttexttext", "chinese": "选择交易所", "english": "选择交易所", "file": "views\\index\\marketTimeDelay.vue"}, {"key": "texttextdatatexttext", "chinese": "选择数据类型", "english": "选择数据类型", "file": "views\\index\\marketTimeDelay.vue"}, {"key": "texttext", "chinese": "平均", "english": "平均", "file": "views\\index\\marketTimeDelay.vue"}, {"key": "texttexttexttext", "chinese": "每秒最大", "english": "每秒最大", "file": "views\\index\\marketTimeDelay.vue"}, {"key": "texttexttexttext", "chinese": "每秒最小", "english": "每秒最小", "file": "views\\index\\marketTimeDelay.vue"}, {"key": "texttexttexttexttexttexttexttexttexttexttexts", "chinese": "本地全系统时延走势分析(μs)", "english": "本地全系统时延走势分析(μs)", "file": "views\\index\\marketTimeDelay.vue"}, {"key": "texttexttexttexttexttexttexttexttexttextms", "chinese": "交易所到消费端总时延(ms)", "english": "交易所到消费端总时延(ms)", "file": "views\\index\\marketTimeDelay.vue"}, {"key": "querytextdatatexttexttext", "chinese": "查询的数据不存在", "english": "查询的数据不存在", "file": "views\\index\\marketTimeDelay.vue"}, {"key": "confirm", "chinese": "确认", "english": "确认", "file": "views\\index\\marketTimeDelay.vue"}], "views\\index\\mcDataObservation.vue": [{"key": "texttexttextmc30texttext", "chinese": "已托管MC3.0集群", "english": "已托管MC3.0集群", "file": "views\\index\\mcDataObservation.vue"}], "views\\index\\mcDeploy.vue": [{"key": "mc30configmanage", "chinese": "MC3.0配置管理", "english": "MC3.0配置管理", "file": "views\\index\\mcDeploy.vue"}, {"key": "texttext", "chinese": "主题", "english": "主题", "file": "views\\index\\mcDeploy.vue"}], "views\\index\\mdbDataExport\\detailDrawer.vue": [{"key": "detail", "chinese": "详情", "english": "详情", "file": "views\\index\\mdbDataExport\\detailDrawer.vue"}, {"key": "texttexttext", "chinese": "服务器", "english": "服务器", "file": "views\\index\\mdbDataExport\\detailDrawer.vue"}, {"key": "texttext", "chinese": "表名", "english": "表名", "file": "views\\index\\mdbDataExport\\detailDrawer.vue"}, {"key": "texttexttexttext", "chinese": "远程路径", "english": "远程路径", "file": "views\\index\\mdbDataExport\\detailDrawer.vue"}, {"key": "exportstatustexttexttext", "chinese": "导出状态和结果", "english": "导出状态和结果", "file": "views\\index\\mdbDataExport\\detailDrawer.vue"}, {"key": "exporttext", "chinese": "导出中", "english": "导出中", "file": "views\\index\\mdbDataExport\\detailDrawer.vue"}, {"key": "exportsuccess", "chinese": "导出成功", "english": "导出成功", "file": "views\\index\\mdbDataExport\\detailDrawer.vue"}, {"key": "exportfailed", "chinese": "导出失败", "english": "导出失败", "file": "views\\index\\mdbDataExport\\detailDrawer.vue"}, {"key": "textexport", "chinese": "待导出", "english": "待导出", "file": "views\\index\\mdbDataExport\\detailDrawer.vue"}, {"key": "texttexttexttextdetailfailed", "chinese": "查看历史详情失败", "english": "查看历史详情失败", "file": "views\\index\\mdbDataExport\\detailDrawer.vue"}, {"key": "errorinfo", "chinese": "错误信息", "english": "错误信息", "file": "views\\index\\mdbDataExport\\detailDrawer.vue"}], "views\\index\\mdbDataExport\\exportHistory.vue": [{"key": "texttextid", "chinese": "任务ID", "english": "任务ID", "file": "views\\index\\mdbDataExport\\exportHistory.vue"}, {"key": "exporttime", "chinese": "导出时间", "english": "导出时间", "file": "views\\index\\mdbDataExport\\exportHistory.vue"}, {"key": "exportstatus", "chinese": "导出状态", "english": "导出状态", "file": "views\\index\\mdbDataExport\\exportHistory.vue"}, {"key": "exporttexttexttexttexttext", "chinese": "导出内存表数量", "english": "导出内存表数量", "file": "views\\index\\mdbDataExport\\exportHistory.vue"}, {"key": "deletetexttexttexttexttextdata", "chinese": "删除远程服务器数据", "english": "删除远程服务器数据", "file": "views\\index\\mdbDataExport\\exportHistory.vue"}, {"key": "deleteapmtexttexttextdata", "chinese": "删除APM服务器数据", "english": "删除APM服务器数据", "file": "views\\index\\mdbDataExport\\exportHistory.vue"}, {"key": "textdelete", "chinese": "已删除", "english": "已删除", "file": "views\\index\\mdbDataExport\\exportHistory.vue"}, {"key": "deletetexttexttexttextfailed", "chinese": "删除历史任务失败,", "english": "删除历史任务失败,", "file": "views\\index\\mdbDataExport\\exportHistory.vue"}, {"key": "texttextsuccess", "chinese": "下载成功!", "english": "下载成功!", "file": "views\\index\\mdbDataExport\\exportHistory.vue"}, {"key": "texttextfailed", "chinese": "下载失败", "english": "下载失败", "file": "views\\index\\mdbDataExport\\exportHistory.vue"}, {"key": "texttexttexttexttextfailed", "chinese": "下载单张表失败,", "english": "下载单张表失败,", "file": "views\\index\\mdbDataExport\\exportHistory.vue"}, {"key": "deleteexporttexttexttexttexttexttexttexttexttexttexttexttexttexttexttexttexttexttexttexttexttexttexttextdatatexttexttexttexttexttexttexttexttexttext", "chinese": "删除导出任务的同时，可同时删除该任务导出在服务器的内存表数据。请勾选需要删除的内容。", "english": "删除导出任务的同时，可同时删除该任务导出在服务器的内存表数据。请勾选需要删除的内容。", "file": "views\\index\\mdbDataExport\\exportHistory.vue"}, {"key": "deleteexporttexttexttexttexttexttexttexttexttexttexttexttexttexttexttexttexttexttexttexttextdatatexttexttexttexttexttexttexttexttexttext", "chinese": "删除导出任务，可同时删除该任务导出在服务器的内存表数据。请勾选需要删除的内容。", "english": "删除导出任务，可同时删除该任务导出在服务器的内存表数据。请勾选需要删除的内容。", "file": "views\\index\\mdbDataExport\\exportHistory.vue"}, {"key": "texttexttexttext", "chinese": "查看原因", "english": "查看原因", "file": "views\\index\\mdbDataExport\\exportHistory.vue"}], "views\\index\\mdbDataExport\\exportTable.vue": [{"key": "texttexttexttextexportspanSpanClass", "chinese": ">最近一次导出</span>\r\n              <span class=", "english": ">最近一次导出</span>\r\n              <span class=", "file": "views\\index\\mdbDataExport\\exportTable.vue"}, {"key": "texttexttexttextexport", "chinese": "最近一次导出", "english": "最近一次导出", "file": "views\\index\\mdbDataExport\\exportTable.vue"}, {"key": "nbspnbspfailedtexttext", "chinese": "&nbsp;&nbsp;失败原因", "english": "&nbsp;&nbsp;失败原因", "file": "views\\index\\mdbDataExport\\exportTable.vue"}, {"key": "texttexttexttext", "chinese": "创建任务", "english": "创建任务", "file": "views\\index\\mdbDataExport\\exportTable.vue"}], "views\\index\\mdbDataExport\\index.vue": [{"key": "mdbdataexport", "chinese": "MDB数据导出", "english": "MDB数据导出", "file": "views\\index\\mdbDataExport\\index.vue"}, {"key": "exporttexttext", "chinese": "导出历史", "english": "导出历史", "file": "views\\index\\mdbDataExport\\index.vue"}, {"key": "exportmdbdata", "chinese": "导出MDB数据", "english": "导出MDB数据", "file": "views\\index\\mdbDataExport\\index.vue"}], "views\\index\\mdbDataObservation.vue": [], "views\\index\\mdbPrivilegeManage.vue": [{"key": "mdbtexttextmanage", "chinese": "MDB权限管理", "english": "MDB权限管理", "file": "views\\index\\mdbPrivilegeManage.vue"}, {"key": "texttexttexttexttexttexttexttexttexttextmdb", "chinese": "将本地用户权限下发至MDB", "english": "将本地用户权限下发至MDB", "file": "views\\index\\mdbPrivilegeManage.vue"}, {"key": "texttexttexteditNbspnbspnbspnbsphswitchVmodel", "chinese": ">权限可编辑  &nbsp;&nbsp;&nbsp;&nbsp;<h-switch v-model=", "english": ">权限可编辑  &nbsp;&nbsp;&nbsp;&nbsp;<h-switch v-model=", "file": "views\\index\\mdbPrivilegeManage.vue"}, {"key": "text", "chinese": "与", "english": "与", "file": "views\\index\\mdbPrivilegeManage.vue"}, {"key": "texttexttexttexttexttexttexttext", "chinese": "）。若当前权限内容为", "english": "）。若当前权限内容为", "file": "views\\index\\mdbPrivilegeManage.vue"}, {"key": "texttexttexttexttexttextedittexttexttexttexttexttexttexttext", "chinese": "权限内容。如需编辑，请至当前页面顶部", "english": "权限内容。如需编辑，请至当前页面顶部", "file": "views\\index\\mdbPrivilegeManage.vue"}, {"key": "texttexttexttext", "chinese": "(最新下发：", "english": "(最新下发：", "file": "views\\index\\mdbPrivilegeManage.vue"}, {"key": "texttexttexttexthispermissionsinfoheadertime", "chinese": "}}(最新下发：{{hisPermissionsInfo.headerTime ||", "english": "}}(最新下发：{{hisPermissionsInfo.headerTime ||", "file": "views\\index\\mdbPrivilegeManage.vue"}, {"key": "texttextfailed", "chinese": "下发失败", "english": "下发失败", "file": "views\\index\\mdbPrivilegeManage.vue"}, {"key": "texttextsuccess", "chinese": "下发成功", "english": "下发成功", "file": "views\\index\\mdbPrivilegeManage.vue"}, {"key": "texttextmanage", "chinese": "用户管理", "english": "用户管理", "file": "views\\index\\mdbPrivilegeManage.vue"}, {"key": "texttextmanage", "chinese": "角色管理", "english": "角色管理", "file": "views\\index\\mdbPrivilegeManage.vue"}, {"key": "texttexttexttext", "chinese": "下发结果", "english": "下发结果", "file": "views\\index\\mdbPrivilegeManage.vue"}, {"key": "mdbtexttexttexttexttexttexttexttexttext", "chinese": "MDB权限与本地权限一致。", "english": "MDB权限与本地权限一致。", "file": "views\\index\\mdbPrivilegeManage.vue"}, {"key": "mdbtexttexttexttexttexttexttexttexttexttext", "chinese": "MDB权限与本地权限不一致。", "english": "MDB权限与本地权限不一致。", "file": "views\\index\\mdbPrivilegeManage.vue"}, {"key": "texttexttexttexttexttextmdbtexttexttexttexttexttexttext", "chinese": "暂时无法获取MDB权限，请稍后查看。", "english": "暂时无法获取MDB权限，请稍后查看。", "file": "views\\index\\mdbPrivilegeManage.vue"}, {"key": "texttexttexttextsuccess", "chinese": "权限下发成功！", "english": "权限下发成功！", "file": "views\\index\\mdbPrivilegeManage.vue"}, {"key": "texttexttexttexttexttext", "chinese": "权限下发异常！", "english": "权限下发异常！", "file": "views\\index\\mdbPrivilegeManage.vue"}, {"key": "texttexttexttextfailed", "chinese": "权限下发失败！", "english": "权限下发失败！", "file": "views\\index\\mdbPrivilegeManage.vue"}, {"key": "exportsuccess", "chinese": "导出成功!", "english": "导出成功!", "file": "views\\index\\mdbPrivilegeManage.vue"}, {"key": "importtexttextsuccess", "chinese": "导入权限成功", "english": "导入权限成功", "file": "views\\index\\mdbPrivilegeManage.vue"}, {"key": "importtexttextfailed", "chinese": "导入权限失败", "english": "导入权限失败", "file": "views\\index\\mdbPrivilegeManage.vue"}, {"key": "texttexttexttexttexttexttexttextfailedtexttexttexttexttexttexttexttexttexttexttexttexttexttexttext", "chinese": "用户权限部分下发失败，请稍后重新尝试。本次下发详细如下：", "english": "用户权限部分下发失败，请稍后重新尝试。本次下发详细如下：", "file": "views\\index\\mdbPrivilegeManage.vue"}, {"key": "texttexttexttexttexttextfailedtexttexttexttexttexttexttexttexttexttexttexttexttexttexttext", "chinese": "用户权限下发失败，请稍后重新尝试。本次下发详细如下：", "english": "用户权限下发失败，请稍后重新尝试。本次下发详细如下：", "file": "views\\index\\mdbPrivilegeManage.vue"}, {"key": "texttexttexteditstatuseditsuccess", "chinese": "权限可编辑状态修改成功!", "english": "权限可编辑状态修改成功!", "file": "views\\index\\mdbPrivilegeManage.vue"}, {"key": "texttextoperation", "chinese": "权限操作", "english": "权限操作", "file": "views\\index\\mdbPrivilegeManage.vue"}, {"key": "texttexttexttext", "chinese": "同步权限", "english": "同步权限", "file": "views\\index\\mdbPrivilegeManage.vue"}, {"key": "texttextmdbtexttext", "chinese": "查看MDB权限", "english": "查看MDB权限", "file": "views\\index\\mdbPrivilegeManage.vue"}, {"key": "importtexttext", "chinese": "导入权限", "english": "导入权限", "file": "views\\index\\mdbPrivilegeManage.vue"}, {"key": "exporttexttext", "chinese": "导出权限", "english": "导出权限", "file": "views\\index\\mdbPrivilegeManage.vue"}, {"key": "texttexttexteditNbspnbspnbspnbsp", "chinese": "权限可编辑  &nbsp;&nbsp;&nbsp;&nbsp;", "english": "权限可编辑  &nbsp;&nbsp;&nbsp;&nbsp;", "file": "views\\index\\mdbPrivilegeManage.vue"}], "views\\index\\monitor\\businessMonitor.vue": [{"key": "texttexttexttexttexttext", "chinese": "展示链路拓扑", "english": "展示链路拓扑", "file": "views\\index\\monitor\\businessMonitor.vue"}, {"key": "texttexttexttext", "chinese": "时延趋势", "english": "时延趋势", "file": "views\\index\\monitor\\businessMonitor.vue"}, {"key": "10text", "chinese": "10秒", "english": "10秒", "file": "views\\index\\monitor\\businessMonitor.vue"}, {"key": "30text", "chinese": "30秒", "english": "30秒", "file": "views\\index\\monitor\\businessMonitor.vue"}, {"key": "texttexttexttextmonitortexttextdatafailed", "chinese": "获取图表监控时延数据失败！", "english": "获取图表监控时延数据失败！", "file": "views\\index\\monitor\\businessMonitor.vue"}, {"key": "texttexttexttextdatafailed", "chinese": "获取聚合数据失败！", "english": "获取聚合数据失败！", "file": "views\\index\\monitor\\businessMonitor.vue"}, {"key": "t3texttexttext", "chinese": "T3监视器", "english": "T3监视器", "file": "views\\index\\monitor\\businessMonitor.vue"}], "views\\index\\monitor\\index.vue": [], "views\\index\\networkSendAndRecevied.vue": [{"key": "texttexttexttext", "chinese": "应用抓包", "english": "应用抓包", "file": "views\\index\\networkSendAndRecevied.vue"}, {"key": "texttexttexttext", "chinese": "抓包回放", "english": "抓包回放", "file": "views\\index\\networkSendAndRecevied.vue"}, {"key": "texttexttexttext", "chinese": "应用发包", "english": "应用发包", "file": "views\\index\\networkSendAndRecevied.vue"}, {"key": "texttexttexttext", "chinese": "发包用例", "english": "发包用例", "file": "views\\index\\networkSendAndRecevied.vue"}], "views\\index\\noticeManagerList.vue": [{"key": "texttexttexttextmanage", "chinese": "告警人员管理", "english": "告警人员管理", "file": "views\\index\\noticeManagerList.vue"}, {"key": "texttexttexttext", "chinese": "告警人员", "english": "告警人员", "file": "views\\index\\noticeManagerList.vue"}, {"key": "texttexttextname", "chinese": "干系人名称", "english": "干系人名称", "file": "views\\index\\noticeManagerList.vue"}, {"key": "texttexttext", "chinese": "手机号", "english": "手机号", "file": "views\\index\\noticeManagerList.vue"}, {"key": "texttext", "chinese": "邮箱", "english": "邮箱", "file": "views\\index\\noticeManagerList.vue"}, {"key": "texttexttexttexttext", "chinese": "通知干系人", "english": "通知干系人", "file": "views\\index\\noticeManagerList.vue"}, {"key": "edit", "chinese": "修改", "english": "修改", "file": "views\\index\\noticeManagerList.vue"}, {"key": "texttexttextdeletetexttextparamaddresseenametexttexttexttextinfo", "chinese": "您确定删除名为\"${param.addresseeName}\"的干系人信息？", "english": "您确定删除名为\"${param.addresseeName}\"的干系人信息？", "file": "views\\index\\noticeManagerList.vue"}, {"key": "add", "chinese": "添加", "english": "添加", "file": "views\\index\\noticeManagerList.vue"}], "views\\index\\productDataStorage.vue": [{"key": "monitordatamanage", "chinese": "监控数据管理", "english": "监控数据管理", "file": "views\\index\\productDataStorage.vue"}, {"key": "texttexttext", "chinese": "索引名", "english": "索引名", "file": "views\\index\\productDataStorage.vue"}, {"key": "texttexttexttext", "chinese": "主分片数", "english": "主分片数", "file": "views\\index\\productDataStorage.vue"}, {"key": "texttexttexttext", "chinese": "从分片数", "english": "从分片数", "file": "views\\index\\productDataStorage.vue"}, {"key": "texttexttexttext", "chinese": "文档条数", "english": "文档条数", "file": "views\\index\\productDataStorage.vue"}, {"key": "texttexttexttext", "chinese": "空间占用", "english": "空间占用", "file": "views\\index\\productDataStorage.vue"}, {"key": "texttexttexttexttexttext", "chinese": "委托交易时延", "english": "委托交易时延", "file": "views\\index\\productDataStorage.vue"}, {"key": "texttextquerytexttextdata", "chinese": "委托查询时延数据", "english": "委托查询时延数据", "file": "views\\index\\productDataStorage.vue"}, {"key": "texttexttexttext", "chinese": "时延跨度", "english": "时延跨度", "file": "views\\index\\productDataStorage.vue"}, {"key": "monitortexttext", "chinese": "监控指标", "english": "监控指标", "file": "views\\index\\productDataStorage.vue"}, {"key": "texttexttexttexttexttexttextparamsrowindextexttexttextdatatext", "chinese": "您确定清空名为\"${params.row.index}\"的索引数据吗？", "english": "您确定清空名为\"${params.row.index}\"的索引数据吗？", "file": "views\\index\\productDataStorage.vue"}, {"key": "texttexttextdeletetexttextparamsrowindextexttexttextdatatext", "chinese": "您确定删除名为\"${params.row.index}\"的索引数据吗？", "english": "您确定删除名为\"${params.row.index}\"的索引数据吗？", "file": "views\\index\\productDataStorage.vue"}, {"key": "texttexttextelasticsearchEsurl", "chinese": "已连接ElasticSearch: {{ esUrl }}", "english": "已连接ElasticSearch: {{ esUrl }}", "file": "views\\index\\productDataStorage.vue"}, {"key": "texttexttexttexttext", "chinese": "按条件清理", "english": "按条件清理", "file": "views\\index\\productDataStorage.vue"}, {"key": "texttexttexttext", "chinese": "定时清理", "english": "定时清理", "file": "views\\index\\productDataStorage.vue"}], "views\\index\\productServiceConfig\\productServiceList.vue": [{"key": "texttexttexttextconfig", "chinese": "产品服务配置", "english": "产品服务配置", "file": "views\\index\\productServiceConfig\\productServiceList.vue"}, {"key": "texttexttexttextlistdivHmenuVif", "chinese": ">产品服务列表</div>\r\n                    <h-menu v-if=", "english": ">产品服务列表</div>\r\n                    <h-menu v-if=", "file": "views\\index\\productServiceConfig\\productServiceList.vue"}, {"key": "texttexttexttextlist", "chinese": "产品服务列表", "english": "产品服务列表", "file": "views\\index\\productServiceConfig\\productServiceList.vue"}], "views\\index\\productTimeDetail.vue": [{"key": "texttexttexttexttexttexttexttexttexttext", "chinese": "交易节点委托时延明细", "english": "交易节点委托时延明细", "file": "views\\index\\productTimeDetail.vue"}, {"key": "texttexttextabuttonAbuttonType", "chinese": ">上一条</a-button>\r\n        <a-button type=", "english": ">上一条</a-button>\r\n        <a-button type=", "file": "views\\index\\productTimeDetail.vue"}, {"key": "texttexttexttext", "chinese": "链路类型", "english": "链路类型", "file": "views\\index\\productTimeDetail.vue"}, {"key": "querytexttext", "chinese": "查询日期", "english": "查询日期", "file": "views\\index\\productTimeDetail.vue"}, {"key": "querytime", "chinese": "查询时间", "english": "查询时间", "file": "views\\index\\productTimeDetail.vue"}, {"key": "texttexttexttext", "chinese": "时延筛选", "english": "时延筛选", "file": "views\\index\\productTimeDetail.vue"}, {"key": "querytexttext", "chinese": "查询结果", "english": "查询结果", "file": "views\\index\\productTimeDetail.vue"}, {"key": "texttexttime", "chinese": "委托时间", "english": "委托时间", "file": "views\\index\\productTimeDetail.vue"}, {"key": "texttexttexttextqueryfailed", "chinese": "获取快捷查询失败!", "english": "获取快捷查询失败!", "file": "views\\index\\productTimeDetail.vue"}, {"key": "savetexttextqueryfailed", "chinese": "保存快捷查询失败!", "english": "保存快捷查询失败!", "file": "views\\index\\productTimeDetail.vue"}, {"key": "deletetexttextqueryfailed", "chinese": "删除快捷查询失败!", "english": "删除快捷查询失败!", "file": "views\\index\\productTimeDetail.vue"}, {"key": "texttextdatatexttexttexttexttext", "chinese": "当前数据为最后一条", "english": "当前数据为最后一条", "file": "views\\index\\productTimeDetail.vue"}, {"key": "texttexttextdeleteNamequerytexttexttext", "chinese": "您确定删除 '${name}'查询标签吗？", "english": "您确定删除 '${name}'查询标签吗？", "file": "views\\index\\productTimeDetail.vue"}, {"key": "texttextdataentrustnotexttexttexttexttext", "chinese": "委托${data.entrustNo}全链路拓扑", "english": "委托${data.entrustNo}全链路拓扑", "file": "views\\index\\productTimeDetail.vue"}, {"key": "texttexttext", "chinese": "上一条", "english": "上一条", "file": "views\\index\\productTimeDetail.vue"}, {"key": "texttexttext", "chinese": "下一条", "english": "下一条", "file": "views\\index\\productTimeDetail.vue"}], "views\\index\\productTimeSummary.vue": [{"key": "texttexttexttexttexttextquery", "chinese": "链路时延汇总查询", "english": "链路时延汇总查询", "file": "views\\index\\productTimeSummary.vue"}, {"key": "texttexttext", "chinese": "平均数", "english": "平均数", "file": "views\\index\\productTimeSummary.vue"}, {"key": "text", "chinese": "周", "english": "周", "file": "views\\index\\productTimeSummary.vue"}, {"key": "text", "chinese": "月", "english": "月", "file": "views\\index\\productTimeSummary.vue"}, {"key": "texttexttexttext", "chinese": "总委托数", "english": "总委托数", "file": "views\\index\\productTimeSummary.vue"}], "views\\index\\rcmBacklogMonitor.vue": [{"key": "texttexttexttexttextmonitor", "chinese": "上下文积压监控", "english": "上下文积压监控", "file": "views\\index\\rcmBacklogMonitor.vue"}, {"key": "rcmconfiglist", "chinese": "RCM配置列表", "english": "RCM配置列表", "file": "views\\index\\rcmBacklogMonitor.vue"}, {"key": "rcmtexttextidProductinstno", "chinese": ", // rcm文档id\r\n            productInstNo:", "english": ", // rcm文档id\r\n            productInstNo:", "file": "views\\index\\rcmBacklogMonitor.vue"}], "views\\index\\rcmDeploy.vue": [{"key": "rcmconfigmanage", "chinese": "RCM配置管理", "english": "RCM配置管理", "file": "views\\index\\rcmDeploy.vue"}, {"key": "deleteconfig", "chinese": "删除配置", "english": "删除配置", "file": "views\\index\\rcmDeploy.vue"}, {"key": "texttexttexttexttexttextconfigtexttexttexttexttexttexttexttexttip", "chinese": "请查看待发布配置和已发布配置告警提示", "english": "请查看待发布配置和已发布配置告警提示", "file": "views\\index\\rcmDeploy.vue"}, {"key": "texttexttextdeletetexttextthisinstancedatanametexttexttexttexttext", "chinese": "您确定删除名为\"${this.instanceData.name}\"的节点实例？", "english": "您确定删除名为\"${this.instanceData.name}\"的节点实例？", "file": "views\\index\\rcmDeploy.vue"}], "views\\index\\rcmObservation.vue": [{"key": "texttexttexttexttextobservation", "chinese": "上下文运行观测", "english": "上下文运行观测", "file": "views\\index\\rcmObservation.vue"}, {"key": "searchtexttexttext", "chinese": "搜索上下文", "english": "搜索上下文", "file": "views\\index\\rcmObservation.vue"}, {"key": "texttexttexttexttexttexttext", "chinese": "请先去建立产品", "english": "请先去建立产品", "file": "views\\index\\rcmObservation.vue"}, {"key": "texttextsuccess", "chinese": "复制成功", "english": "复制成功", "file": "views\\index\\rcmObservation.vue"}, {"key": "texttext", "chinese": "复制", "english": "复制", "file": "views\\index\\rcmObservation.vue"}], "views\\index\\secondAppearance\\index.vue": [{"key": "texttextDivClass", "chinese": ">\r\n        <!-- 头部 -->\r\n        <div class=", "english": ">\r\n        <!-- 头部 -->\r\n        <div class=", "file": "views\\index\\secondAppearance\\index.vue"}, {"key": "textsqltexttext", "chinese": "按SQL条件", "english": "按SQL条件", "file": "views\\index\\secondAppearance\\index.vue"}, {"key": "texttexttexttexttext", "chinese": "按资金账户", "english": "按资金账户", "file": "views\\index\\secondAppearance\\index.vue"}, {"key": "texttexttexttext", "chinese": "上场记录", "english": "上场记录", "file": "views\\index\\secondAppearance\\index.vue"}], "views\\index\\secondAppearance\\publishStatusDetail.vue": [{"key": "texttexttexttexttexttexttexttexttextspanSpanClass", "chinese": ">当前无上场任务执行</span>\r\n            <span class=", "english": ">当前无上场任务执行</span>\r\n            <span class=", "file": "views\\index\\secondAppearance\\publishStatusDetail.vue"}, {"key": "detailhbuttonDivSlot", "chinese": ">详情</h-button>\r\n            <div slot=", "english": ">详情</h-button>\r\n            <div slot=", "file": "views\\index\\secondAppearance\\publishStatusDetail.vue"}, {"key": "texttextinfodivDivClass", "chinese": ">上场信息</div>\r\n                    <div class=", "english": ">上场信息</div>\r\n                    <div class=", "file": "views\\index\\secondAppearance\\publishStatusDetail.vue"}, {"key": "taskdetailtitletexttext", "chinese": "${taskDetail.title}上场", "english": "${taskDetail.title}上场", "file": "views\\index\\secondAppearance\\publishStatusDetail.vue"}, {"key": "taskdetailtitleTexttexttexttexttext", "chinese": "{{taskDetail.title || '-'}}执行上场中", "english": "{{taskDetail.title || '-'}}执行上场中", "file": "views\\index\\secondAppearance\\publishStatusDetail.vue"}, {"key": "texttexttexttexttexttexttexttexttext", "chinese": "当前无上场任务执行", "english": "当前无上场任务执行", "file": "views\\index\\secondAppearance\\publishStatusDetail.vue"}, {"key": "texttextinfo", "chinese": "上场信息", "english": "上场信息", "file": "views\\index\\secondAppearance\\publishStatusDetail.vue"}, {"key": "starttime", "chinese": "开始时间:", "english": "开始时间:", "file": "views\\index\\secondAppearance\\publishStatusDetail.vue"}, {"key": "endtime", "chinese": "结束时间:", "english": "结束时间:", "file": "views\\index\\secondAppearance\\publishStatusDetail.vue"}, {"key": "texttexttexttexttexttext", "chinese": "需上场表数量:", "english": "需上场表数量:", "file": "views\\index\\secondAppearance\\publishStatusDetail.vue"}, {"key": "texttextsuccesstexttexttext", "chinese": "上场成功表数量:", "english": "上场成功表数量:", "file": "views\\index\\secondAppearance\\publishStatusDetail.vue"}, {"key": "texttextfailedtexttexttext", "chinese": "上场失败表数量:", "english": "上场失败表数量:", "file": "views\\index\\secondAppearance\\publishStatusDetail.vue"}], "views\\index\\smsList.vue": [{"key": "texttexttexttextmanage", "chinese": "告警通知管理", "english": "告警通知管理", "file": "views\\index\\smsList.vue"}, {"key": "texttexttexttexttexttext", "chinese": "告警通知参数", "english": "告警通知参数", "file": "views\\index\\smsList.vue"}, {"key": "texttextstatus", "chinese": "通知状态", "english": "通知状态", "file": "views\\index\\smsList.vue"}, {"key": "texttexttexttext", "chinese": "通知内容", "english": "通知内容", "file": "views\\index\\smsList.vue"}, {"key": "texttexttexttext", "chinese": "告警通知", "english": "告警通知", "file": "views\\index\\smsList.vue"}, {"key": "texttexttexttexttime", "chinese": "告警通知时间", "english": "告警通知时间", "file": "views\\index\\smsList.vue"}, {"key": "texttexttexttexttexttext", "chinese": "匹配通知模板", "english": "匹配通知模板", "file": "views\\index\\smsList.vue"}, {"key": "text", "chinese": "人", "english": "人", "file": "views\\index\\smsList.vue"}, {"key": "texttexttexttext", "chinese": "自动通知", "english": "自动通知", "file": "views\\index\\smsList.vue"}, {"key": "texttext", "chinese": "开启", "english": "开启", "file": "views\\index\\smsList.vue"}, {"key": "texttextstatus", "chinese": "匹配状态", "english": "匹配状态", "file": "views\\index\\smsList.vue"}, {"key": "success", "chinese": "成功", "english": "成功", "file": "views\\index\\smsList.vue"}, {"key": "failed", "chinese": "失败", "english": "失败", "file": "views\\index\\smsList.vue"}, {"key": "texttexttext", "chinese": "已通知", "english": "已通知", "file": "views\\index\\smsList.vue"}, {"key": "texttexttext", "chinese": "未通知", "english": "未通知", "file": "views\\index\\smsList.vue"}, {"key": "texttexttime", "chinese": "通知时间", "english": "通知时间", "file": "views\\index\\smsList.vue"}, {"key": "texttexttexttext", "chinese": "通知模板", "english": "通知模板", "file": "views\\index\\smsList.vue"}, {"key": "pleaseselecttexttexttexttext", "chinese": "请选择通知模板", "english": "请选择通知模板", "file": "views\\index\\smsList.vue"}, {"key": "texttext", "chinese": "全部", "english": "全部", "file": "views\\index\\smsList.vue"}, {"key": "texttext", "chinese": "匹配", "english": "匹配", "file": "views\\index\\smsList.vue"}, {"key": "texttexttext", "chinese": "未匹配", "english": "未匹配", "file": "views\\index\\smsList.vue"}, {"key": "pleaseselecttexttextstatus", "chinese": "请选择匹配状态", "english": "请选择匹配状态", "file": "views\\index\\smsList.vue"}, {"key": "pleaseselecttexttextstatus", "chinese": "请选择通知状态", "english": "请选择通知状态", "file": "views\\index\\smsList.vue"}, {"key": "texttexttexttexttexttext", "chinese": "通知外发历史", "english": "通知外发历史", "file": "views\\index\\smsList.vue"}, {"key": "texttexttime", "chinese": "创建时间", "english": "创建时间", "file": "views\\index\\smsList.vue"}, {"key": "texttexttexttext", "chinese": "通知方式", "english": "通知方式", "file": "views\\index\\smsList.vue"}, {"key": "texttext", "chinese": "短信", "english": "短信", "file": "views\\index\\smsList.vue"}, {"key": "texttexttexttext", "chinese": "电子邮件", "english": "电子邮件", "file": "views\\index\\smsList.vue"}, {"key": "texttexttexttext", "chinese": "通知地址", "english": "通知地址", "file": "views\\index\\smsList.vue"}, {"key": "texttexttexttexttext", "chinese": "通知提供方", "english": "通知提供方", "file": "views\\index\\smsList.vue"}, {"key": "texttexttext", "chinese": "干系人", "english": "干系人", "file": "views\\index\\smsList.vue"}, {"key": "pleaseselecttexttexttexttext", "chinese": "请选择通知方式", "english": "请选择通知方式", "file": "views\\index\\smsList.vue"}, {"key": "pleaseinputtexttexttexttext", "chinese": "请输入通知内容", "english": "请输入通知内容", "file": "views\\index\\smsList.vue"}, {"key": "texttexttime", "chinese": "发送时间", "english": "发送时间", "file": "views\\index\\smsList.vue"}, {"key": "texttextsuccess", "chinese": "发送成功", "english": "发送成功", "file": "views\\index\\smsList.vue"}, {"key": "texttextfailed", "chinese": "发送失败", "english": "发送失败", "file": "views\\index\\smsList.vue"}, {"key": "pleaseinputtexttexttexttext", "chinese": "请输入通知地址", "english": "请输入通知地址", "file": "views\\index\\smsList.vue"}, {"key": "managetexttexttexttext", "chinese": "管理通知模版", "english": "管理通知模版", "file": "views\\index\\smsList.vue"}], "views\\index\\sqlCores.vue": [{"key": "texttextspanSpanSlot", "chinese": ">超时:</span>\r\n                        <span slot=", "english": ">超时:</span>\r\n                        <span slot=", "file": "views\\index\\sqlCores.vue"}, {"key": "texttexttexttext", "chinese": "帮助手册", "english": "帮助手册", "file": "views\\index\\sqlCores.vue"}, {"key": "texttexttexttexttexttexttext", "chinese": "标签名不得为空", "english": "标签名不得为空", "file": "views\\index\\sqlCores.vue"}, {"key": "texttexttexttexttexttexttexttext", "chinese": "当前标签名已存在", "english": "当前标签名已存在", "file": "views\\index\\sqlCores.vue"}, {"key": "pleaseinputtexttexttext", "chinese": "请输入标签名", "english": "请输入标签名", "file": "views\\index\\sqlCores.vue"}, {"key": "edittexttexttexttexttext1mtexttexttexttexttext", "chinese": "编辑器内容超过1M不支持暂存!", "english": "编辑器内容超过1M不支持暂存!", "file": "views\\index\\sqlCores.vue"}, {"key": "pleaseselecttexttexttexttexttexttext", "chinese": "请选择要运行的节点！", "english": "请选择要运行的节点！", "file": "views\\index\\sqlCores.vue"}, {"key": "sqltexttexttexttexttexttext", "chinese": "SQL输入不能为空！", "english": "SQL输入不能为空！", "file": "views\\index\\sqlCores.vue"}, {"key": "pleaseselecttexttexttexttextsqltexttexttexttexttexttext1text", "chinese": "请选择要执行的sql语句，不得超过1条！", "english": "请选择要执行的sql语句，不得超过1条！", "file": "views\\index\\sqlCores.vue"}, {"key": "texttexttimetexttextconfigtexttexttext0", "chinese": "超时时间不能配置为空或0！", "english": "超时时间不能配置为空或0！", "file": "views\\index\\sqlCores.vue"}, {"key": "texttexttimetexttexttexttext10texttext", "chinese": "超时时间不得超过10分钟！", "english": "超时时间不得超过10分钟！", "file": "views\\index\\sqlCores.vue"}, {"key": "texttextdatatexttexttexttexttexttexttexttext", "chinese": "该页数据不存在，请重新执行", "english": "该页数据不存在，请重新执行", "file": "views\\index\\sqlCores.vue"}, {"key": "texttext", "chinese": "执行", "english": "执行", "file": "views\\index\\sqlCores.vue"}, {"key": "texttextsql", "chinese": "历史SQL", "english": "历史SQL", "file": "views\\index\\sqlCores.vue"}, {"key": "texttext", "chinese": "超时:", "english": "超时:", "file": "views\\index\\sqlCores.vue"}], "views\\index\\sqlTable.vue": [{"key": "texttexttexttexttexttext", "chinese": "输入表名过滤", "english": "输入表名过滤", "file": "views\\index\\sqlTable.vue"}, {"key": "texttexttext", "chinese": ">仅显示", "english": ">仅显示", "file": "views\\index\\sqlTable.vue"}, {"key": "texttext", "chinese": "索引", "english": "索引", "file": "views\\index\\sqlTable.vue"}, {"key": "texttexttexttext", "chinese": "关联关系", "english": "关联关系", "file": "views\\index\\sqlTable.vue"}, {"key": "texttexttexttext", "chinese": "使用说明", "english": "使用说明", "file": "views\\index\\sqlTable.vue"}, {"key": "texttexttexttext", "chinese": "索引类型", "english": "索引类型", "file": "views\\index\\sqlTable.vue"}, {"key": "texttexttexttext", "chinese": "索引字段", "english": "索引字段", "file": "views\\index\\sqlTable.vue"}, {"key": "texttexttexttexttexttext", "chinese": "关联对象身份", "english": "关联对象身份", "file": "views\\index\\sqlTable.vue"}, {"key": "texttexttexttext", "chinese": "关联类型", "english": "关联类型", "file": "views\\index\\sqlTable.vue"}, {"key": "texttext", "chinese": "包含", "english": "包含", "file": "views\\index\\sqlTable.vue"}, {"key": "texttext", "chinese": "关联", "english": "关联", "file": "views\\index\\sqlTable.vue"}, {"key": "texttexttexttext", "chinese": "关联字段", "english": "关联字段", "file": "views\\index\\sqlTable.vue"}, {"key": "texttexttexttext", "chinese": "关联索引", "english": "关联索引", "file": "views\\index\\sqlTable.vue"}, {"key": "texttexttexttexttexttexttexttexttexttexttexttexttexttexttexttexttexttexttexttexttexttext", "chinese": "当前服务在此路由策略下无可执行节点，请重新选择", "english": "当前服务在此路由策略下无可执行节点，请重新选择", "file": "views\\index\\sqlTable.vue"}, {"key": "texttexttexttexttexttexttexttexttexttexttexttexttext", "chinese": "此分片表在以下主节点中存在", "english": "此分片表在以下主节点中存在", "file": "views\\index\\sqlTable.vue"}, {"key": "texttexttexttexttexttexttexttexttexttexttexttexttexttexttexttexttexttexttexttexttexttexttext", "chinese": "当前模式下无可执行的核心节点，请调整路由策略重试", "english": "当前模式下无可执行的核心节点，请调整路由策略重试", "file": "views\\index\\sqlTable.vue"}, {"key": "texttext", "chinese": "结果", "english": "结果", "file": "views\\index\\sqlTable.vue"}, {"key": "texttexttexttextdatatexttexttextsqltexttexttexttexttexttexttexttexttexttexttexttext", "chinese": "仅表示行数据对应的SQL执行的节点，与执行结果无关", "english": "仅表示行数据对应的SQL执行的节点，与执行结果无关", "file": "views\\index\\sqlTable.vue"}, {"key": "texttext", "chinese": "终止", "english": "终止", "file": "views\\index\\sqlTable.vue"}, {"key": "sqltexttexttexttext", "chinese": "SQL执行异常", "english": "SQL执行异常", "file": "views\\index\\sqlTable.vue"}, {"key": "sqlparamsqlTexttexttexttexttexttexttexttexttexttexttexttexttexttextsqltext", "chinese": "'SQL：${param.sql} 执行异常，您确定要继续执行剩余SQL吗？", "english": "'SQL：${param.sql} 执行异常，您确定要继续执行剩余SQL吗？", "file": "views\\index\\sqlTable.vue"}, {"key": "texttexttexttexttexttexttexttexttexttexttext", "chinese": "仅显示\"不支持主备同步\"表", "english": "仅显示\"不支持主备同步\"表", "file": "views\\index\\sqlTable.vue"}, {"key": "importsql", "chinese": "导入SQL", "english": "导入SQL", "file": "views\\index\\sqlTable.vue"}], "views\\index\\threadInfoOverview.vue": [{"key": "texttexttexttextconfig", "chinese": "应用检查配置", "english": "应用检查配置", "file": "views\\index\\threadInfoOverview.vue"}], "views\\index\\topoMonitor.vue": [{"key": "texttexttexttexttexttexttexttexttexttexttext", "chinese": "当前产品不支持拓扑展示", "english": "当前产品不支持拓扑展示", "file": "views\\index\\topoMonitor.vue"}, {"key": "texttexttexttexttexttext", "chinese": "应用拓扑结构", "english": "应用拓扑结构", "file": "views\\index\\topoMonitor.vue"}, {"key": "rcmtexttexttexttext", "chinese": "RCM拓扑结构", "english": "RCM拓扑结构", "file": "views\\index\\topoMonitor.vue"}], "views\\index\\transaction.vue": [{"key": "texttexttexttext", "chinese": "选择产品", "english": "选择产品", "file": "views\\index\\transaction.vue"}, {"key": "xtexttextnameHtmlstr", "chinese": ";// x轴的名称\r\n\r\n                    htmlStr +=", "english": ";// x轴的名称\r\n\r\n                    htmlStr +=", "file": "views\\index\\transaction.vue"}, {"key": "querytexttexttexttexttext", "chinese": "查询订单不存在", "english": "查询订单不存在", "file": "views\\index\\transaction.vue"}, {"key": "texttexttexttexttexttextdatafailed", "chinese": "获取同比分析数据失败！", "english": "获取同比分析数据失败！", "file": "views\\index\\transaction.vue"}, {"key": "texttexttexttext", "chinese": "自由分析", "english": "自由分析", "file": "views\\index\\transaction.vue"}, {"key": "texttexttexttexttexttext", "chinese": "应用时延分析", "english": "应用时延分析", "file": "views\\index\\transaction.vue"}, {"key": "texttextresdatainstancenametexttextsuccess", "chinese": "报表${res.data.instanceName}创建成功！", "english": "报表${res.data.instanceName}创建成功！", "file": "views\\index\\transaction.vue"}, {"key": "query", "chinese": "查询", "english": "查询", "file": "views\\index\\transaction.vue"}, {"key": "texttexttexttexttexttext", "chinese": "创建分析报表", "english": "创建分析报表", "file": "views\\index\\transaction.vue"}, {"key": "xtexttextnameHtmlstr", "chinese": "';// x轴的名称\r\n\r\n                    htmlStr += '", "english": "';// x轴的名称\r\n\r\n                    htmlStr += '", "file": "views\\index\\transaction.vue"}], "views\\index\\tripartiteServiceConfig\\tripartiteServiceList.vue": [{"key": "texttexttexttexttexttextmanage", "chinese": "三方服务集成管理", "english": "三方服务集成管理", "file": "views\\index\\tripartiteServiceConfig\\tripartiteServiceList.vue"}, {"key": "texttexttexttextlistdivHmenuVif", "chinese": ">三方服务列表</div>\r\n                    <h-menu v-if=", "english": ">三方服务列表</div>\r\n                    <h-menu v-if=", "file": "views\\index\\tripartiteServiceConfig\\tripartiteServiceList.vue"}, {"key": "texttexttexttextlist", "chinese": "三方服务列表", "english": "三方服务列表", "file": "views\\index\\tripartiteServiceConfig\\tripartiteServiceList.vue"}], "views\\index\\ustTableVerification.vue": [{"key": "datatexttext", "chinese": "数据校验", "english": "数据校验", "file": "views\\index\\ustTableVerification.vue"}, {"key": "texttextdatatexttexttexttext", "chinese": "创建数据校验任务", "english": "创建数据校验任务", "file": "views\\index\\ustTableVerification.vue"}], "views\\testLXY.vue": [{"key": "texttextinfotext", "chinese": "用户信息表", "english": "用户信息表", "file": "views\\testLXY.vue"}, {"key": "texttextdetailtext", "chinese": "订单详情表", "english": "订单详情表", "file": "views\\testLXY.vue"}], "components\\accordObservation\\dataAccordDrawer.vue": [{"key": "texttexttexttexttexttexttexttext", "chinese": "主备核心事务差量", "english": "主备核心事务差量", "file": "components\\accordObservation\\dataAccordDrawer.vue"}, {"key": "texttexttexttexttexttext", "chinese": "回库事务差量", "english": "回库事务差量", "file": "components\\accordObservation\\dataAccordDrawer.vue"}, {"key": "texttext", "chinese": "角色", "english": "角色", "file": "components\\accordObservation\\dataAccordDrawer.vue"}, {"key": "texttexttext", "chinese": "事务号", "english": "事务号", "file": "components\\accordObservation\\dataAccordDrawer.vue"}, {"key": "texttexttexttexttexttexttexttext", "chinese": "与主核心事务差量", "english": "与主核心事务差量", "file": "components\\accordObservation\\dataAccordDrawer.vue"}, {"key": "texttexttexttexttext", "chinese": "事务量差(个)", "english": "事务量差(个)", "file": "components\\accordObservation\\dataAccordDrawer.vue"}, {"key": "datatitleTexttexttexttext", "chinese": "{{ dataTitle }}变化趋势", "english": "{{ dataTitle }}变化趋势", "file": "components\\accordObservation\\dataAccordDrawer.vue"}], "components\\accordObservation\\dataAccordObservation.vue": [{"key": "texttexttexttexttexttexttexttexttext", "chinese": "备核心事务最大差量", "english": "备核心事务最大差量", "file": "components\\accordObservation\\dataAccordObservation.vue"}, {"key": "texttexttexttexttexttexttexttext", "chinese": "回库事务最大差量", "english": "回库事务最大差量", "file": "components\\accordObservation\\dataAccordObservation.vue"}, {"key": "texttextIndicatorshardingno", "chinese": "分片 ${indicator.shardingNo || '-'}", "english": "分片 ${indicator.shardingNo || '-'}", "file": "components\\accordObservation\\dataAccordObservation.vue"}], "components\\analyse\\addCaseModal.vue": [{"key": "texttexttexttexttexttext", "chinese": "新建测试用例", "english": "新建测试用例", "file": "components\\analyse\\addCaseModal.vue"}, {"key": "texttextname", "chinese": "用例名称", "english": "用例名称", "file": "components\\analyse\\addCaseModal.vue"}, {"key": "pleaseinputtexttexttexttextname", "chinese": "请输入测试用例名称", "english": "请输入测试用例名称", "file": "components\\analyse\\addCaseModal.vue"}, {"key": "cancelabuttonAbuttonType", "chinese": ">取消</a-button>\r\n                <a-button type=", "english": ">取消</a-button>\r\n                <a-button type=", "file": "components\\analyse\\addCaseModal.vue"}, {"key": "texttexttexttexttexttexttexttexttext20", "chinese": "字符长度数不得超过20！", "english": "字符长度数不得超过20！", "file": "components\\analyse\\addCaseModal.vue"}, {"key": "texttexttexttextsuccess", "chinese": "用例创建成功!", "english": "用例创建成功!", "file": "components\\analyse\\addCaseModal.vue"}, {"key": "texttexttexttextfailed", "chinese": "用例创建失败!", "english": "用例创建失败!", "file": "components\\analyse\\addCaseModal.vue"}], "components\\analyse\\addSceneModal.vue": [{"key": "texttextname", "chinese": "场景名称", "english": "场景名称", "file": "components\\analyse\\addSceneModal.vue"}, {"key": "pleaseinputtexttextname", "chinese": "请输入场景名称", "english": "请输入场景名称", "file": "components\\analyse\\addSceneModal.vue"}, {"key": "texttexttexttextsuccess", "chinese": "场景创建成功!", "english": "场景创建成功!", "file": "components\\analyse\\addSceneModal.vue"}, {"key": "texttexttexttextfailed", "chinese": "场景创建失败!", "english": "场景创建失败!", "file": "components\\analyse\\addSceneModal.vue"}, {"key": "texttexttexttexttexttexttexttextexportsavetexttexttexttexttexttexttexttexttexttexttexttexttexttexttextinfo", "chinese": "请确保当前场景已导出保存，新建的场景将覆盖当前使用的场景信息！", "english": "请确保当前场景已导出保存，新建的场景将覆盖当前使用的场景信息！", "file": "components\\analyse\\addSceneModal.vue"}], "components\\analyse\\caseAccountModal.vue": [{"key": "texttextconfig", "chinese": "账号配置", "english": "账号配置", "file": "components\\analyse\\caseAccountModal.vue"}, {"key": "pleaseinputtexttexttexttext", "chinese": "请输入起始账号", "english": "请输入起始账号", "file": "components\\analyse\\caseAccountModal.vue"}, {"key": "texttext", "chinese": "账号...", "english": "账号...", "file": "components\\analyse\\caseAccountModal.vue"}, {"key": "textconfirmtexttexttexttexttexttexttext", "chinese": "请确认输入内容为数字！", "english": "请确认输入内容为数字！", "file": "components\\analyse\\caseAccountModal.vue"}, {"key": "texttexttexttexttexttextsuccess", "chinese": "用例账号更新成功!", "english": "用例账号更新成功!", "file": "components\\analyse\\caseAccountModal.vue"}, {"key": "texttexttexttexttexttext", "chinese": "用例账号更新!", "english": "用例账号更新!", "file": "components\\analyse\\caseAccountModal.vue"}, {"key": "import", "chinese": "导入", "english": "导入", "file": "components\\analyse\\caseAccountModal.vue"}, {"key": "texttexttexttext", "chinese": "起始账号", "english": "起始账号", "file": "components\\analyse\\caseAccountModal.vue"}, {"key": "texttexttexttext", "chinese": "生成个数", "english": "生成个数", "file": "components\\analyse\\caseAccountModal.vue"}, {"key": "texttexttexttext", "chinese": "自动生成", "english": "自动生成", "file": "components\\analyse\\caseAccountModal.vue"}, {"key": "texttexttexttext", "chinese": "股东账号", "english": "股东账号", "file": "components\\analyse\\caseAccountModal.vue"}, {"key": "texttexttext", "chinese": "上交所", "english": "上交所", "file": "components\\analyse\\caseAccountModal.vue"}, {"key": "texttexttext", "chinese": "深交所", "english": "深交所", "file": "components\\analyse\\caseAccountModal.vue"}], "components\\analyse\\caseConfigModal.vue": [{"key": "configtexttext", "chinese": "配置用例", "english": "配置用例", "file": "components\\analyse\\caseConfigModal.vue"}, {"key": "pleaseinputtexttextconfig", "chinese": "请输入用例配置...", "english": "请输入用例配置...", "file": "components\\analyse\\caseConfigModal.vue"}, {"key": "texttextconfigsavefailed", "chinese": "用例配置保存失败!", "english": "用例配置保存失败!", "file": "components\\analyse\\caseConfigModal.vue"}], "components\\analyse\\caseRemarkModal.vue": [{"key": "texttexttexttexttexttext", "chinese": "更新实例备注", "english": "更新实例备注", "file": "components\\analyse\\caseRemarkModal.vue"}, {"key": "texttext", "chinese": "标题", "english": "标题", "file": "components\\analyse\\caseRemarkModal.vue"}, {"key": "pleaseinputtexttextname", "chinese": "请输入标题名称", "english": "请输入标题名称", "file": "components\\analyse\\caseRemarkModal.vue"}, {"key": "texttext", "chinese": "内容", "english": "内容", "file": "components\\analyse\\caseRemarkModal.vue"}, {"key": "texttexttexttextsuccess", "chinese": "备注更新成功!", "english": "备注更新成功!", "file": "components\\analyse\\caseRemarkModal.vue"}, {"key": "texttexttexttextfailed", "chinese": "备注更新失败!", "english": "备注更新失败!", "file": "components\\analyse\\caseRemarkModal.vue"}], "components\\analyse\\eventListModal.vue": [{"key": "texttextlist", "chinese": "事件列表", "english": "事件列表", "file": "components\\analyse\\eventListModal.vue"}, {"key": "texttextdetail", "chinese": "事件详情", "english": "事件详情", "file": "components\\analyse\\eventListModal.vue"}, {"key": "tiptexttext", "chinese": "提示内容", "english": "提示内容", "file": "components\\analyse\\eventListModal.vue"}, {"key": "texttextid", "chinese": "复制id", "english": "复制id", "file": "components\\analyse\\eventListModal.vue"}, {"key": "texttexttexttexttexttexttexttexttext", "chinese": "该浏览器不支持复制", "english": "该浏览器不支持复制", "file": "components\\analyse\\eventListModal.vue"}, {"key": "copiedTexttextsuccessTexttextid", "chinese": "{{ copied ? '复制成功' : '复制id' }}", "english": "{{ copied ? '复制成功' : '复制id' }}", "file": "components\\analyse\\eventListModal.vue"}], "components\\analyse\\importAccountModal.vue": [{"key": "importtexttextinfo", "chinese": "导入账号信息", "english": "导入账号信息", "file": "components\\analyse\\importAccountModal.vue"}, {"key": "texttexttexttexttexttexttexttexttexttexttexttexttext", "chinese": "点击或将文件拖拽到这里上传", "english": "点击或将文件拖拽到这里上传", "file": "components\\analyse\\importAccountModal.vue"}], "components\\analyse\\importFileModal.vue": [{"key": "importtexttextconfigtexttext", "chinese": "导入场景配置文件", "english": "导入场景配置文件", "file": "components\\analyse\\importFileModal.vue"}, {"key": "texttexttexttexttexttexttexttextexportsavetextimporttexttexttexttexttexttexttexttexttexttexttexttexttextinfo", "chinese": "请确保当前场景已导出保存，新导入的场景将覆盖当前使用的场景信息！", "english": "请确保当前场景已导出保存，新导入的场景将覆盖当前使用的场景信息！", "file": "components\\analyse\\importFileModal.vue"}], "components\\analyse\\reportConfigModal.vue": [{"key": "texttexttexttext", "chinese": "委托回路", "english": "委托回路", "file": "components\\analyse\\reportConfigModal.vue"}, {"key": "texttexttexttext", "chinese": "可视跨度", "english": "可视跨度", "file": "components\\analyse\\reportConfigModal.vue"}, {"key": "texttexttexttext", "chinese": "可视指标", "english": "可视指标", "file": "components\\analyse\\reportConfigModal.vue"}, {"key": "texttexttexttext", "chinese": "度量单位", "english": "度量单位", "file": "components\\analyse\\reportConfigModal.vue"}, {"key": "texttextns", "chinese": "纳秒（ns）", "english": "纳秒（ns）", "file": "components\\analyse\\reportConfigModal.vue"}, {"key": "texttexts", "chinese": "微秒（μs）", "english": "微秒（μs）", "file": "components\\analyse\\reportConfigModal.vue"}, {"key": "texttextms", "chinese": "毫秒（ms）", "english": "毫秒（ms）", "file": "components\\analyse\\reportConfigModal.vue"}], "components\\analyse\\reportContrastModal.vue": [{"key": "texttexttexttext", "chinese": "报表对比", "english": "报表对比", "file": "components\\analyse\\reportContrastModal.vue"}, {"key": "texttexttexttexttexttext", "chinese": "选择对比实例", "english": "选择对比实例", "file": "components\\analyse\\reportContrastModal.vue"}], "components\\analyse\\secInstanceListModal.vue": [{"key": "texttexttexttexttexttext", "chinese": "选择测试实例", "english": "选择测试实例", "file": "components\\analyse\\secInstanceListModal.vue"}, {"key": "searchtexttexttexttext", "chinese": "搜索测试报告...", "english": "搜索测试报告...", "file": "components\\analyse\\secInstanceListModal.vue"}, {"key": "texttextname", "chinese": "实例名称", "english": "实例名称", "file": "components\\analyse\\secInstanceListModal.vue"}, {"key": "texttexttexttext", "chinese": "测试日期", "english": "测试日期", "file": "components\\analyse\\secInstanceListModal.vue"}, {"key": "pleaseselecttexttext", "chinese": "请选择实例！", "english": "请选择实例！", "file": "components\\analyse\\secInstanceListModal.vue"}], "components\\analyse\\updateCaseNameModal.vue": [{"key": "texttexttexttexttexttextname", "chinese": "更新测试实例名称", "english": "更新测试实例名称", "file": "components\\analyse\\updateCaseNameModal.vue"}, {"key": "pleaseinputtexttexttexttextname", "chinese": "请输入测试实例名称", "english": "请输入测试实例名称", "file": "components\\analyse\\updateCaseNameModal.vue"}, {"key": "texttexttexttexttexttexttexttexttext30", "chinese": "字符长度数不得超过30！", "english": "字符长度数不得超过30！", "file": "components\\analyse\\updateCaseNameModal.vue"}, {"key": "texttextnametexttextsuccess", "chinese": "实例名称更新成功!", "english": "实例名称更新成功!", "file": "components\\analyse\\updateCaseNameModal.vue"}, {"key": "texttexttexttextfailed", "chinese": "实例更新失败!", "english": "实例更新失败!", "file": "components\\analyse\\updateCaseNameModal.vue"}], "components\\analyseConfig\\analyseCaseInstance.vue": [{"key": "texttexttexttextabuttonHrowClass", "chinese": ">填写备注</a-button>\r\n            <h-row class=", "english": ">填写备注</a-button>\r\n            <h-row class=", "file": "components\\analyseConfig\\analyseCaseInstance.vue"}, {"key": "text", "chinese": "空", "english": "空", "file": "components\\analyseConfig\\analyseCaseInstance.vue"}, {"key": "texttexttexttext", "chinese": "最大时延", "english": "最大时延", "file": "components\\analyseConfig\\analyseCaseInstance.vue"}, {"key": "texttexttexttext", "chinese": "最小时延", "english": "最小时延", "file": "components\\analyseConfig\\analyseCaseInstance.vue"}, {"key": "texttexttexttext", "chinese": "平均时延", "english": "平均时延", "file": "components\\analyseConfig\\analyseCaseInstance.vue"}, {"key": "texttexttexttexttext", "chinese": "中位数时延", "english": "中位数时延", "file": "components\\analyseConfig\\analyseCaseInstance.vue"}, {"key": "99texttexttexttext", "chinese": "99分位时延", "english": "99分位时延", "file": "components\\analyseConfig\\analyseCaseInstance.vue"}, {"key": "querytexttextlistfailed", "chinese": "查询实例列表失败!", "english": "查询实例列表失败!", "file": "components\\analyseConfig\\analyseCaseInstance.vue"}, {"key": "contentTexttexttextdelete", "chinese": ",\r\n                content: `您确定删除", "english": ",\r\n                content: `您确定删除", "file": "components\\analyseConfig\\analyseCaseInstance.vue"}, {"key": "texttexttexttexttexttexttexttexttexttextdetailtexttexttexttexttext", "chinese": "您确定要跳转到该实例详情分析页面吗？", "english": "您确定要跳转到该实例详情分析页面吗？", "file": "components\\analyseConfig\\analyseCaseInstance.vue"}, {"key": "texttexttexttexttexttexttexttexttexttexttext", "chinese": "您确定停止当前测试实例？", "english": "您确定停止当前测试实例？", "file": "components\\analyseConfig\\analyseCaseInstance.vue"}, {"key": "texttexttexttexttexttextsuccess", "chinese": "测试实例停止成功!", "english": "测试实例停止成功!", "file": "components\\analyseConfig\\analyseCaseInstance.vue"}, {"key": "99texttexttext", "chinese": "99分位数", "english": "99分位数", "file": "components\\analyseConfig\\analyseCaseInstance.vue"}, {"key": "texttexttexttexttext", "chinese": "测试实例名", "english": "测试实例名", "file": "components\\analyseConfig\\analyseCaseInstance.vue"}, {"key": "texttextinfo", "chinese": "事件信息", "english": "事件信息", "file": "components\\analyseConfig\\analyseCaseInstance.vue"}, {"key": "texttexttexttext", "chinese": "停止测试", "english": "停止测试", "file": "components\\analyseConfig\\analyseCaseInstance.vue"}, {"key": "texttexttexttext", "chinese": "填写备注", "english": "填写备注", "file": "components\\analyseConfig\\analyseCaseInstance.vue"}, {"key": "tabdatareportstatusdescTexttext", "chinese": "{{ (tabData.reportStatusDesc) || '暂无' }}", "english": "{{ (tabData.reportStatusDesc) || '暂无' }}", "file": "components\\analyseConfig\\analyseCaseInstance.vue"}], "components\\analyseConfig\\analyseConfigShell.vue": [{"key": "texttexttexttext", "chinese": "发单工具", "english": "发单工具", "file": "components\\analyseConfig\\analyseConfigShell.vue"}, {"key": "pleaseinputtexttexttexttext", "chinese": "请输入发单工具", "english": "请输入发单工具", "file": "components\\analyseConfig\\analyseConfigShell.vue"}, {"key": "texttexttext", "chinese": "部署到", "english": "部署到", "file": "components\\analyseConfig\\analyseConfigShell.vue"}, {"key": "pleaseinputtexttexttexttext", "chinese": "请输入部署地址", "english": "请输入部署地址", "file": "components\\analyseConfig\\analyseConfigShell.vue"}, {"key": "texttexttexttexttext", "chinese": "部署实例数", "english": "部署实例数", "file": "components\\analyseConfig\\analyseConfigShell.vue"}, {"key": "pleaseinputtexttexttexttexttext", "chinese": "请输入部署示例数", "english": "请输入部署示例数", "file": "components\\analyseConfig\\analyseConfigShell.vue"}, {"key": "texttexttexttext", "chinese": "应用节点", "english": "应用节点", "file": "components\\analyseConfig\\analyseConfigShell.vue"}, {"key": "texttextip", "chinese": "接入IP", "english": "接入IP", "file": "components\\analyseConfig\\analyseConfigShell.vue"}, {"key": "texttexttexttext", "chinese": "接入端口", "english": "接入端口", "file": "components\\analyseConfig\\analyseConfigShell.vue"}, {"key": "texttexttexttext", "chinese": "测试用例", "english": "测试用例", "file": "components\\analyseConfig\\analyseConfigShell.vue"}, {"key": "texttexttexttexttext", "chinese": "命令行参数", "english": "命令行参数", "file": "components\\analyseConfig\\analyseConfigShell.vue"}, {"key": "pleaseinputtexttexttexttext", "chinese": "请输入执行命令", "english": "请输入执行命令", "file": "components\\analyseConfig\\analyseConfigShell.vue"}, {"key": "pleaseinput110texttexttext", "chinese": "请输入1-10正整数", "english": "请输入1-10正整数", "file": "components\\analyseConfig\\analyseConfigShell.vue"}, {"key": "pleaseinput1text65535texttexttexttexttexttext", "chinese": "请输入1到65535之间的端口号", "english": "请输入1到65535之间的端口号", "file": "components\\analyseConfig\\analyseConfigShell.vue"}, {"key": "texttexttexttextIp", "chinese": ",            // 业务类型\r\n                    ip:", "english": ",            // 业务类型\r\n                    ip:", "file": "components\\analyseConfig\\analyseConfigShell.vue"}, {"key": "textquerytexttexttextinfo", "chinese": "未查询到场景信息!", "english": "未查询到场景信息!", "file": "components\\analyseConfig\\analyseConfigShell.vue"}, {"key": "querytexttextinfotexttext", "chinese": "查询场景信息异常!", "english": "查询场景信息异常!", "file": "components\\analyseConfig\\analyseConfigShell.vue"}, {"key": "querytexttextinfofailed", "chinese": "查询场景信息失败!", "english": "查询场景信息失败!", "file": "components\\analyseConfig\\analyseConfigShell.vue"}, {"key": "texttexttexttextsuccess", "chinese": "测试执行成功!", "english": "测试执行成功!", "file": "components\\analyseConfig\\analyseConfigShell.vue"}, {"key": "texttexttextproductnametestcasenametexttexttexttext", "chinese": "确定对${productName}-${testCaseName}执行测试？", "english": "确定对${productName}-${testCaseName}执行测试？", "file": "components\\analyseConfig\\analyseConfigShell.vue"}, {"key": "texttexttexttexttexttext", "chinese": "选择发单工具", "english": "选择发单工具", "file": "components\\analyseConfig\\analyseConfigShell.vue"}, {"key": "texttexttexttextldptexttexttexttext", "chinese": "连接目标LDP业务系统", "english": "连接目标LDP业务系统", "file": "components\\analyseConfig\\analyseConfigShell.vue"}, {"key": "texttexttexttexttexttext", "chinese": "选择测试用例", "english": "选择测试用例", "file": "components\\analyseConfig\\analyseConfigShell.vue"}, {"key": "texttexttexttext", "chinese": "新建用例", "english": "新建用例", "file": "components\\analyseConfig\\analyseConfigShell.vue"}, {"key": "deletetexttext", "chinese": "删除用例", "english": "删除用例", "file": "components\\analyseConfig\\analyseConfigShell.vue"}, {"key": "configtexttexttext", "chinese": "配置多账号", "english": "配置多账号", "file": "components\\analyseConfig\\analyseConfigShell.vue"}, {"key": "nbspnbspnbsptexttexttexttext", "chinese": "④&nbsp;&nbsp;&nbsp;执行测试", "english": "④&nbsp;&nbsp;&nbsp;执行测试", "file": "components\\analyseConfig\\analyseConfigShell.vue"}, {"key": "texttexttexttexttexttext", "chinese": "打印时延日志", "english": "打印时延日志", "file": "components\\analyseConfig\\analyseConfigShell.vue"}, {"key": "texttexttexttext", "chinese": "执行测试", "english": "执行测试", "file": "components\\analyseConfig\\analyseConfigShell.vue"}], "components\\appBindingCore\\AppBindingCore.vue": [{"key": "texttexttexttext", "chinese": "筛选条件", "english": "筛选条件", "file": "components\\appBindingCore\\AppBindingCore.vue"}, {"key": "texttextinfo", "chinese": "线程信息", "english": "线程信息", "file": "components\\appBindingCore\\AppBindingCore.vue"}, {"key": "manageip", "chinese": "管理IP", "english": "管理IP", "file": "components\\appBindingCore\\AppBindingCore.vue"}, {"key": "pleaseselectmanageip", "chinese": "请选择管理IP", "english": "请选择管理IP", "file": "components\\appBindingCore\\AppBindingCore.vue"}, {"key": "texttexttexttexttext", "chinese": "应用节点名", "english": "应用节点名", "file": "components\\appBindingCore\\AppBindingCore.vue"}, {"key": "pleaseselecttexttexttexttexttext", "chinese": "请选择应用节点名", "english": "请选择应用节点名", "file": "components\\appBindingCore\\AppBindingCore.vue"}, {"key": "texttexttexttexttext", "chinese": "线程拥有者", "english": "线程拥有者", "file": "components\\appBindingCore\\AppBindingCore.vue"}, {"key": "pleaseinputtexttexttexttexttext", "chinese": "请输入线程拥有者", "english": "请输入线程拥有者", "file": "components\\appBindingCore\\AppBindingCore.vue"}, {"key": "texttexttexttext", "chinese": "是否绑核", "english": "是否绑核", "file": "components\\appBindingCore\\AppBindingCore.vue"}, {"key": "pleaseselecttexttext", "chinese": "请选择绑核", "english": "请选择绑核", "file": "components\\appBindingCore\\AppBindingCore.vue"}, {"key": "text", "chinese": "是", "english": "是", "file": "components\\appBindingCore\\AppBindingCore.vue"}, {"key": "text", "chinese": "否", "english": "否", "file": "components\\appBindingCore\\AppBindingCore.vue"}, {"key": "texttexttext", "chinese": "线程名", "english": "线程名", "file": "components\\appBindingCore\\AppBindingCore.vue"}, {"key": "texttexttexttext", "chinese": "线程类型", "english": "线程类型", "file": "components\\appBindingCore\\AppBindingCore.vue"}, {"key": "texttext", "chinese": "绑核", "english": "绑核", "file": "components\\appBindingCore\\AppBindingCore.vue"}], "components\\coreReplayObservation\\coreReplayDetail.vue": [{"key": "starttimeStarttime", "chinese": ">开始时间：{{ startTime ||", "english": ">开始时间：{{ startTime ||", "file": "components\\coreReplayObservation\\coreReplayDetail.vue"}, {"key": "texttexttexttexttexttexttexttexttexttexttexttexttexttexttexttexttexttext", "chinese": ">\r\n                    当前交易日重演完成时，将展示此交易日中", "english": ">\r\n                    当前交易日重演完成时，将展示此交易日中", "file": "components\\coreReplayObservation\\coreReplayDetail.vue"}, {"key": "texttexttexttexttexttexttexttexttexttexttexttextstatustexttexttexttexttexttexttexttexttext", "chinese": ">\r\n                    当前交易日消息收发的实时状态。应答比对：当前交易日", "english": ">\r\n                    当前交易日消息收发的实时状态。应答比对：当前交易日", "file": "components\\coreReplayObservation\\coreReplayDetail.vue"}, {"key": "texttexttexttexttexttexttexttexttextstarttexttexttext", "chinese": "当前交易日重演尚未开始，请稍候", "english": "当前交易日重演尚未开始，请稍候", "file": "components\\coreReplayObservation\\coreReplayDetail.vue"}, {"key": "texttexttexttexttexttexttexttexttextreplayfilepath", "chinese": "}；  重演交易日文件目录：${replayFilePath ||", "english": "}；  重演交易日文件目录：${replayFilePath ||", "file": "components\\coreReplayObservation\\coreReplayDetail.vue"}, {"key": "texttexttexttext", "chinese": "重演结果", "english": "重演结果", "file": "components\\coreReplayObservation\\coreReplayDetail.vue"}, {"key": "texttexttexttexttext", "chinese": "相同表数量", "english": "相同表数量", "file": "components\\coreReplayObservation\\coreReplayDetail.vue"}, {"key": "texttexttexttexttext", "chinese": "不同表数量", "english": "不同表数量", "file": "components\\coreReplayObservation\\coreReplayDetail.vue"}, {"key": "texttexttexttext", "chinese": "应答一致", "english": "应答一致", "file": "components\\coreReplayObservation\\coreReplayDetail.vue"}, {"key": "texttexttexttexttext", "chinese": "应答不一致", "english": "应答不一致", "file": "components\\coreReplayObservation\\coreReplayDetail.vue"}, {"key": "texttexttexttext", "chinese": "消息收发", "english": "消息收发", "file": "components\\coreReplayObservation\\coreReplayDetail.vue"}, {"key": "texttexttexttexttexttext", "chinese": "应答比对进度(%)", "english": "应答比对进度(%)", "file": "components\\coreReplayObservation\\coreReplayDetail.vue"}, {"key": "texttexttext", "chinese": "需比对：", "english": "需比对：", "file": "components\\coreReplayObservation\\coreReplayDetail.vue"}, {"key": "texttexttext", "chinese": "已比对：", "english": "已比对：", "file": "components\\coreReplayObservation\\coreReplayDetail.vue"}, {"key": "texttexttexttext", "chinese": "发送进度(%)", "english": "发送进度(%)", "file": "components\\coreReplayObservation\\coreReplayDetail.vue"}, {"key": "texttexttext", "chinese": "需发送：", "english": "需发送：", "file": "components\\coreReplayObservation\\coreReplayDetail.vue"}, {"key": "texttexttext", "chinese": "已发送：", "english": "已发送：", "file": "components\\coreReplayObservation\\coreReplayDetail.vue"}, {"key": "texttexttexttext", "chinese": "应答进度(%)", "english": "应答进度(%)", "file": "components\\coreReplayObservation\\coreReplayDetail.vue"}, {"key": "texttexttext", "chinese": "需应答：", "english": "需应答：", "file": "components\\coreReplayObservation\\coreReplayDetail.vue"}, {"key": "texttexttext", "chinese": "已应答：", "english": "已应答：", "file": "components\\coreReplayObservation\\coreReplayDetail.vue"}, {"key": "texttexttexttextmonitor", "chinese": "重演工具监控", "english": "重演工具监控", "file": "components\\coreReplayObservation\\coreReplayDetail.vue"}, {"key": "texttexttexttexttps", "chinese": "发送吞吐(tps)", "english": "发送吞吐(tps)", "file": "components\\coreReplayObservation\\coreReplayDetail.vue"}, {"key": "texttexttexttexttps", "chinese": "应答吞吐(tps)", "english": "应答吞吐(tps)", "file": "components\\coreReplayObservation\\coreReplayDetail.vue"}, {"key": "texttexttexttexttexttexttps", "chinese": "应答比对吞吐(tps)", "english": "应答比对吞吐(tps)", "file": "components\\coreReplayObservation\\coreReplayDetail.vue"}, {"key": "texttext", "chinese": "内存(%)", "english": "内存(%)", "file": "components\\coreReplayObservation\\coreReplayDetail.vue"}, {"key": "texttexttexttexttexttexttexttext", "chinese": "仅显示应答不一致", "english": "仅显示应答不一致", "file": "components\\coreReplayObservation\\coreReplayDetail.vue"}, {"key": "texttexttext", "chinese": "分片号", "english": "分片号", "file": "components\\coreReplayObservation\\coreReplayDetail.vue"}, {"key": "texttexttext", "chinese": "集群名", "english": "集群名", "file": "components\\coreReplayObservation\\coreReplayDetail.vue"}, {"key": "texttexttexttext", "chinese": "发送进度", "english": "发送进度", "file": "components\\coreReplayObservation\\coreReplayDetail.vue"}, {"key": "texttexttexttext", "chinese": "应答进度", "english": "应答进度", "file": "components\\coreReplayObservation\\coreReplayDetail.vue"}, {"key": "texttexttexttexttexttext", "chinese": "应答比对进度", "english": "应答比对进度", "file": "components\\coreReplayObservation\\coreReplayDetail.vue"}, {"key": "texttexttexttexttexttext", "chinese": "应答不一致数", "english": "应答不一致数", "file": "components\\coreReplayObservation\\coreReplayDetail.vue"}, {"key": "datatexttexttexttext", "chinese": "数据比对进度", "english": "数据比对进度", "file": "components\\coreReplayObservation\\coreReplayDetail.vue"}, {"key": "datatexttexttexttext", "chinese": "数据不一致数", "english": "数据不一致数", "file": "components\\coreReplayObservation\\coreReplayDetail.vue"}, {"key": "texttexttexttextstatusDatestatus", "chinese": ", // 重演任务状态\r\n            dateStatus:", "english": ", // 重演任务状态\r\n            dateStatus:", "file": "components\\coreReplayObservation\\coreReplayDetail.vue"}, {"key": "texttexttexttextstatusStarttime", "chinese": ", // 重演日期状态\r\n            startTime:", "english": ", // 重演日期状态\r\n            startTime:", "file": "components\\coreReplayObservation\\coreReplayDetail.vue"}, {"key": "starttimeReplayfilepath", "chinese": ", // 开始时间\r\n            replayFilePath:", "english": ", // 开始时间\r\n            replayFilePath:", "file": "components\\coreReplayObservation\\coreReplayDetail.vue"}, {"key": "texttextsuccess", "chinese": "终止成功", "english": "终止成功", "file": "components\\coreReplayObservation\\coreReplayDetail.vue"}, {"key": "texttextsuccess", "chinese": "暂停成功", "english": "暂停成功", "file": "components\\coreReplayObservation\\coreReplayDetail.vue"}, {"key": "texttextsuccess", "chinese": "恢复成功", "english": "恢复成功", "file": "components\\coreReplayObservation\\coreReplayDetail.vue"}, {"key": "texttexttexttexttexttext", "chinese": "具体任务细节", "english": "具体任务细节", "file": "components\\coreReplayObservation\\coreReplayDetail.vue"}, {"key": "starttimeStarttime", "chinese": "开始时间：{{ startTime || \"-\" }}", "english": "开始时间：{{ startTime || \"-\" }}", "file": "components\\coreReplayObservation\\coreReplayDetail.vue"}, {"key": "texttexttexttext", "chinese": "正在加载", "english": "正在加载", "file": "components\\coreReplayObservation\\coreReplayDetail.vue"}, {"key": "datatexttexttext", "chinese": "数据更新中", "english": "数据更新中", "file": "components\\coreReplayObservation\\coreReplayDetail.vue"}, {"key": "autoupdatetimeSDatatexttext", "chinese": "{{ autoUpdateTime }}s 数据更新", "english": "{{ autoUpdateTime }}s 数据更新", "file": "components\\coreReplayObservation\\coreReplayDetail.vue"}, {"key": "texttext", "chinese": "启动", "english": "启动", "file": "components\\coreReplayObservation\\coreReplayDetail.vue"}, {"key": "texttext", "chinese": "恢复", "english": "恢复", "file": "components\\coreReplayObservation\\coreReplayDetail.vue"}, {"key": "texttext", "chinese": "暂停", "english": "暂停", "file": "components\\coreReplayObservation\\coreReplayDetail.vue"}, {"key": "texttexttexttexttexttexttexttexttexttexttexttexttexttexttexttexttexttexttexttexttexttexttexttexttexttexttexttexttexttexttexttexttexttexttexttexttexttexttexttexttexttext", "chinese": "当前交易日重演完成时，将展示此交易日中\"重演\"与\"持久化\"中相同/不同表数量、应答的一致/不一致数量", "english": "当前交易日重演完成时，将展示此交易日中\"重演\"与\"持久化\"中相同/不同表数量、应答的一致/不一致数量", "file": "components\\coreReplayObservation\\coreReplayDetail.vue"}, {"key": "texttexttexttexttexttexttexttexttexttexttexttextstatustexttexttexttexttexttexttexttexttexttexttexttexttexttextTextTexttexttexttexttexttexttexttexttexttext", "chinese": "当前交易日消息收发的实时状态。应答比对：当前交易日\"重演的应答\" 与 \"生产已记录的应答\"比对。", "english": "当前交易日消息收发的实时状态。应答比对：当前交易日\"重演的应答\" 与 \"生产已记录的应答\"比对。", "file": "components\\coreReplayObservation\\coreReplayDetail.vue"}, {"key": "texttexttexttexttexttexttexttexttexttexttexttexttexttexttexttexttexttexttexttexttexttexttexttexttexttexttext", "chinese": "当前交易日中，重演工具本身的发送和应答速度及资源使用情况", "english": "当前交易日中，重演工具本身的发送和应答速度及资源使用情况", "file": "components\\coreReplayObservation\\coreReplayDetail.vue"}, {"key": "texttexttexttextInstancetotalcount", "chinese": "（ 核心总数：{{ instanceTotalCount }}", "english": "（ 核心总数：{{ instanceTotalCount }}", "file": "components\\coreReplayObservation\\coreReplayDetail.vue"}, {"key": "texttexttexttexttextInstancecompletedcount", "chinese": "已完成重演：{{ instanceCompletedCount }} ）", "english": "已完成重演：{{ instanceCompletedCount }} ）", "file": "components\\coreReplayObservation\\coreReplayDetail.vue"}, {"key": "errortexttext", "chinese": "错误原因", "english": "错误原因", "file": "components\\coreReplayObservation\\coreReplayDetail.vue"}], "components\\coreReplayObservation\\coreReplayTaskList.vue": [{"key": "texttextdatatexttext", "chinese": "核心数据重演", "english": "核心数据重演", "file": "components\\coreReplayObservation\\coreReplayTaskList.vue"}, {"key": "texttexttexttext", "chinese": "更多条件", "english": "更多条件", "file": "components\\coreReplayObservation\\coreReplayTaskList.vue"}, {"key": "texttexttexttextlistdivDivClass", "chinese": ">重演任务列表</div>\r\n          <div class=", "english": ">重演任务列表</div>\r\n          <div class=", "file": "components\\coreReplayObservation\\coreReplayTaskList.vue"}, {"key": "texttexttexttext", "chinese": "执行结果", "english": "执行结果", "file": "components\\coreReplayObservation\\coreReplayTaskList.vue"}, {"key": "texttexthoptionHoptionKey", "chinese": ">一致</h-option>\r\n            <h-option key=", "english": ">一致</h-option>\r\n            <h-option key=", "file": "components\\coreReplayObservation\\coreReplayTaskList.vue"}, {"key": "texttexttexttexttext", "chinese": "选择交易日", "english": "选择交易日", "file": "components\\coreReplayObservation\\coreReplayTaskList.vue"}, {"key": "texttexttexttextname", "chinese": "输入任务名称", "english": "输入任务名称", "file": "components\\coreReplayObservation\\coreReplayTaskList.vue"}, {"key": "texttexttexttext", "chinese": "任务来源", "english": "任务来源", "file": "components\\coreReplayObservation\\coreReplayTaskList.vue"}, {"key": "texttextstarttime", "chinese": "执行开始时间", "english": "执行开始时间", "file": "components\\coreReplayObservation\\coreReplayTaskList.vue"}, {"key": "pleaseselecttimetexttext", "chinese": "请选择时间范围", "english": "请选择时间范围", "file": "components\\coreReplayObservation\\coreReplayTaskList.vue"}, {"key": "texttextname", "chinese": "任务名称", "english": "任务名称", "file": "components\\coreReplayObservation\\coreReplayTaskList.vue"}, {"key": "texttexttexttext", "chinese": "任务标识：", "english": "任务标识：", "file": "components\\coreReplayObservation\\coreReplayTaskList.vue"}, {"key": "texttextstatus", "chinese": "执行状态", "english": "执行状态", "file": "components\\coreReplayObservation\\coreReplayTaskList.vue"}, {"key": "texttextendtime", "chinese": "执行结束时间", "english": "执行结束时间", "file": "components\\coreReplayObservation\\coreReplayTaskList.vue"}, {"key": "texttexttexttext", "chinese": "(不一致数：", "english": "(不一致数：", "file": "components\\coreReplayObservation\\coreReplayTaskList.vue"}, {"key": "texttexttexttext", "chinese": "不一致数", "english": "不一致数", "file": "components\\coreReplayObservation\\coreReplayTaskList.vue"}, {"key": "editsuccess", "chinese": "修改成功", "english": "修改成功", "file": "components\\coreReplayObservation\\coreReplayTaskList.vue"}, {"key": "texttextsuccess", "chinese": "启动成功", "english": "启动成功", "file": "components\\coreReplayObservation\\coreReplayTaskList.vue"}, {"key": "texttexttexttexttexttextsuccesstexttextresdatasuccesscount0", "chinese": "重演任务同步成功！数量：${res?.data?.successCount || 0}", "english": "重演任务同步成功！数量：${res?.data?.successCount || 0}", "file": "components\\coreReplayObservation\\coreReplayTaskList.vue"}, {"key": "texttexttextdeletetexttexttext", "chinese": "确定要删除此任务？", "english": "确定要删除此任务？", "file": "components\\coreReplayObservation\\coreReplayTaskList.vue"}, {"key": "deletetexttexttexttexttexttexttexttexttextlisttexttexttexttexttexttexttexttexttexttextdatatexttexttexttexttexttexttexttexttextoperation", "chinese": "删除重演任务后，任务将从列表移除，同时任务所产生的数据将一并被删除。请谨慎操作。", "english": "删除重演任务后，任务将从列表移除，同时任务所产生的数据将一并被删除。请谨慎操作。", "file": "components\\coreReplayObservation\\coreReplayTaskList.vue"}, {"key": "texttexttexttextlist", "chinese": "重演任务列表", "english": "重演任务列表", "file": "components\\coreReplayObservation\\coreReplayTaskList.vue"}, {"key": "texttexttexttext", "chinese": "任务总数:", "english": "任务总数:", "file": "components\\coreReplayObservation\\coreReplayTaskList.vue"}, {"key": "texttext", "chinese": "创建", "english": "创建", "file": "components\\coreReplayObservation\\coreReplayTaskList.vue"}, {"key": "texttexttexttext", "chinese": "同步任务", "english": "同步任务", "file": "components\\coreReplayObservation\\coreReplayTaskList.vue"}, {"key": "texttext", "chinese": "一致", "english": "一致", "file": "components\\coreReplayObservation\\coreReplayTaskList.vue"}, {"key": "texttexttext", "chinese": "不一致", "english": "不一致", "file": "components\\coreReplayObservation\\coreReplayTaskList.vue"}], "components\\coreReplayObservation\\createCoreReplayTask.vue": [{"key": "texttexttexttexttexttexttexttexttexttexttexttexttexttexttexttexttexttexttexttexttexttext", "chinese": "原重演任务中的重演对象可能发生变化，请仔细核对。", "english": "原重演任务中的重演对象可能发生变化，请仔细核对。", "file": "components\\coreReplayObservation\\createCoreReplayTask.vue"}, {"key": "texttexttexttext", "chinese": "发送间隔", "english": "发送间隔", "file": "components\\coreReplayObservation\\createCoreReplayTask.vue"}, {"key": "texttexttexttext", "chinese": "重演内容", "english": "重演内容", "file": "components\\coreReplayObservation\\createCoreReplayTask.vue"}, {"key": "texttexttexttext", "chinese": "任务参数", "english": "任务参数", "file": "components\\coreReplayObservation\\createCoreReplayTask.vue"}, {"key": "texttexttexttexttexttext", "chinese": "复制重演任务", "english": "复制重演任务", "file": "components\\coreReplayObservation\\createCoreReplayTask.vue"}, {"key": "texttexttexttexttexttext", "chinese": "创建重演任务", "english": "创建重演任务", "file": "components\\coreReplayObservation\\createCoreReplayTask.vue"}, {"key": "texttexttexttexttexttext", "chinese": "确定重演对象", "english": "确定重演对象", "file": "components\\coreReplayObservation\\createCoreReplayTask.vue"}, {"key": "configtexttexttexttext", "chinese": "配置任务参数", "english": "配置任务参数", "file": "components\\coreReplayObservation\\createCoreReplayTask.vue"}, {"key": "infotexttext", "chinese": "信息核对", "english": "信息核对", "file": "components\\coreReplayObservation\\createCoreReplayTask.vue"}, {"key": "texttexttexttexttexttexttexttexttexttexttexttexttexttexttexttexttext", "chinese": "按需选择需要重演“交易日”与对应的“核心”。", "english": "按需选择需要重演“交易日”与对应的“核心”。", "file": "components\\coreReplayObservation\\createCoreReplayTask.vue"}, {"key": "texttexttexttexttexttexttexttexttexttexttexttexttexttexttexttexttexttexttexttexttexttexttexttexttexttexttexttexttext", "chinese": "基于前两个步骤的选择，将对应产生的重演任务，请仔细核对任务内容。", "english": "基于前两个步骤的选择，将对应产生的重演任务，请仔细核对任务内容。", "file": "components\\coreReplayObservation\\createCoreReplayTask.vue"}, {"key": "pleaseinput01000", "chinese": "请输入0~1000", "english": "请输入0~1000", "file": "components\\coreReplayObservation\\createCoreReplayTask.vue"}, {"key": "texttexttexttext", "chinese": "重演任务", "english": "重演任务", "file": "components\\coreReplayObservation\\createCoreReplayTask.vue"}, {"key": "texttexttexttextType", "chinese": ",\r\n            // 复制、创建\r\n            type:", "english": ",\r\n            // 复制、创建\r\n            type:", "file": "components\\coreReplayObservation\\createCoreReplayTask.vue"}, {"key": "texttexttexttexttextsuccess", "chinese": "创建并启动成功", "english": "创建并启动成功", "file": "components\\coreReplayObservation\\createCoreReplayTask.vue"}, {"key": "texttexttexttexttexttexttexttexttext", "chinese": "请至少选择一个核心", "english": "请至少选择一个核心", "file": "components\\coreReplayObservation\\createCoreReplayTask.vue"}, {"key": "texttexttexttexttexttextconfigtexttexttexttexttexttexttexttext", "chinese": "根据实际情况配置重演任务相关参数。", "english": "根据实际情况配置重演任务相关参数。", "file": "components\\coreReplayObservation\\createCoreReplayTask.vue"}, {"key": "confirmtexttexttexttext", "chinese": "确认离开页面？", "english": "确认离开页面？", "file": "components\\coreReplayObservation\\createCoreReplayTask.vue"}, {"key": "typeCopyTexttexttexttexttexttextTexttexttexttexttexttext", "chinese": "{{ type === 'copy'? '复制重演任务' : '创建重演任务' }}", "english": "{{ type === 'copy'? '复制重演任务' : '创建重演任务' }}", "file": "components\\coreReplayObservation\\createCoreReplayTask.vue"}, {"key": "texttexttexttext", "chinese": "核心集群", "english": "核心集群", "file": "components\\coreReplayObservation\\createCoreReplayTask.vue"}, {"key": "texttexttexttext", "chinese": "发送间隔:", "english": "发送间隔:", "file": "components\\coreReplayObservation\\createCoreReplayTask.vue"}, {"key": "formitemssendintervalmsTexttext", "chinese": "{{ formItems.sendIntervalMs }} 毫秒", "english": "{{ formItems.sendIntervalMs }} 毫秒", "file": "components\\coreReplayObservation\\createCoreReplayTask.vue"}, {"key": "edittexttext", "chinese": "修改路径：", "english": "修改路径：", "file": "components\\coreReplayObservation\\createCoreReplayTask.vue"}, {"key": "texttexttextSteplistcurrentstep1", "chinese": "下一步：{{ stepList[currentStep + 1] }}", "english": "下一步：{{ stepList[currentStep + 1] }}", "file": "components\\coreReplayObservation\\createCoreReplayTask.vue"}, {"key": "texttexttexttexttexttexttext", "chinese": "创建并启动任务", "english": "创建并启动任务", "file": "components\\coreReplayObservation\\createCoreReplayTask.vue"}], "components\\endpointConfig\\endpointConfig.vue": [{"key": "addtexttexttexttext", "chinese": "添加网关节点", "english": "添加网关节点", "file": "components\\endpointConfig\\endpointConfig.vue"}, {"key": "texttexttexttext", "chinese": "接入点名", "english": "接入点名", "file": "components\\endpointConfig\\endpointConfig.vue"}, {"key": "texttexttexttexttexttext", "chinese": "接入应用节点", "english": "接入应用节点", "file": "components\\endpointConfig\\endpointConfig.vue"}, {"key": "texttexttextip", "chinese": "接入点IP", "english": "接入点IP", "file": "components\\endpointConfig\\endpointConfig.vue"}, {"key": "texttexttexttexttext", "chinese": "接入点端口", "english": "接入点端口", "file": "components\\endpointConfig\\endpointConfig.vue"}, {"key": "texttexttexttext", "chinese": "接入协议", "english": "接入协议", "file": "components\\endpointConfig\\endpointConfig.vue"}, {"key": "texttexttext", "chinese": "字符集", "english": "字符集", "file": "components\\endpointConfig\\endpointConfig.vue"}, {"key": "texttextconfig", "chinese": "批量配置", "english": "批量配置", "file": "components\\endpointConfig\\endpointConfig.vue"}, {"key": "texttexttexttexttext", "chinese": "连通性测试", "english": "连通性测试", "file": "components\\endpointConfig\\endpointConfig.vue"}, {"key": "addtexttext", "chinese": "添加节点", "english": "添加节点", "file": "components\\endpointConfig\\endpointConfig.vue"}, {"key": "texttexttexttexttexttext", "chinese": "应用节点类型", "english": "应用节点类型", "file": "components\\endpointConfig\\endpointConfig.vue"}, {"key": "texttexttexttext", "chinese": "应用身份", "english": "应用身份", "file": "components\\endpointConfig\\endpointConfig.vue"}, {"key": "managetexttext", "chinese": "管理端口", "english": "管理端口", "file": "components\\endpointConfig\\endpointConfig.vue"}, {"key": "texttexttexttext", "chinese": "应用集群", "english": "应用集群", "file": "components\\endpointConfig\\endpointConfig.vue"}, {"key": "texttexttext", "chinese": "系统号", "english": "系统号", "file": "components\\endpointConfig\\endpointConfig.vue"}, {"key": "texttexttexttext", "chinese": "支持抓包", "english": "支持抓包", "file": "components\\endpointConfig\\endpointConfig.vue"}, {"key": "texttexttexttext", "chinese": "支持发包", "english": "支持发包", "file": "components\\endpointConfig\\endpointConfig.vue"}, {"key": "configtexttext", "chinese": "配置文件", "english": "配置文件", "file": "components\\endpointConfig\\endpointConfig.vue"}, {"key": "texttexttexttexttexttexttext", "chinese": ", // 超出的文本隐藏", "english": ", // 超出的文本隐藏", "file": "components\\endpointConfig\\endpointConfig.vue"}, {"key": "texttexttexttexttexttexttexttext", "chinese": ", // 溢出用省略号显示", "english": ", // 溢出用省略号显示", "file": "components\\endpointConfig\\endpointConfig.vue"}, {"key": "texttexttexttext", "chinese": "开发平台", "english": "开发平台", "file": "components\\endpointConfig\\endpointConfig.vue"}, {"key": "texttexttexttextid", "chinese": "应用插件ID", "english": "应用插件ID", "file": "components\\endpointConfig\\endpointConfig.vue"}, {"key": "texttexttexttextconfig", "chinese": "接入网关配置", "english": "接入网关配置", "file": "components\\endpointConfig\\endpointConfig.vue"}, {"key": "texttexttexttextaccess_config_titlethisendpointtype", "chinese": "后端服务：${ACCESS_CONFIG_TITLE[this.endpointType]}", "english": "后端服务：${ACCESS_CONFIG_TITLE[this.endpointType]}", "file": "components\\endpointConfig\\endpointConfig.vue"}, {"key": "texttexttextdeletetexttextnametexttexttexttext", "chinese": "您确定删除名为\"${name}\"的节点吗？", "english": "您确定删除名为\"${name}\"的节点吗？", "file": "components\\endpointConfig\\endpointConfig.vue"}], "components\\latencyTrendAnalysis\\settingModal.vue": [{"key": "texttextconfig", "chinese": "链路配置", "english": "链路配置", "file": "components\\latencyTrendAnalysis\\settingModal.vue"}, {"key": "texttexttexttextdivHformitemLabel", "chinese": ">分析目标</div>\r\n                <h-form-item label=", "english": ">分析目标</div>\r\n                <h-form-item label=", "file": "components\\latencyTrendAnalysis\\settingModal.vue"}, {"key": "pleaseselecttexttext", "chinese": "请选择链路", "english": "请选择链路", "file": "components\\latencyTrendAnalysis\\settingModal.vue"}, {"key": "texttexttexttext", "chinese": "统计日期", "english": "统计日期", "file": "components\\latencyTrendAnalysis\\settingModal.vue"}, {"key": "texttexttime", "chinese": "统计时间", "english": "统计时间", "file": "components\\latencyTrendAnalysis\\settingModal.vue"}, {"key": "texttexttexttext", "chinese": "统计方式", "english": "统计方式", "file": "components\\latencyTrendAnalysis\\settingModal.vue"}, {"key": "pleaseselecttexttexttexttext", "chinese": "请选择统计方式", "english": "请选择统计方式", "file": "components\\latencyTrendAnalysis\\settingModal.vue"}, {"key": "texttexttexttext", "chinese": "统计口径", "english": "统计口径", "file": "components\\latencyTrendAnalysis\\settingModal.vue"}, {"key": "pleaseselecttexttexttexttext", "chinese": "请选择统计口径", "english": "请选择统计口径", "file": "components\\latencyTrendAnalysis\\settingModal.vue"}, {"key": "texttexttexttexttexttexttexttexttext18texttext", "chinese": "自定义范围不得超过18小时", "english": "自定义范围不得超过18小时", "file": "components\\latencyTrendAnalysis\\settingModal.vue"}, {"key": "texttexttexttext", "chinese": "全部市场", "english": "全部市场", "file": "components\\latencyTrendAnalysis\\settingModal.vue"}, {"key": "texttext", "chinese": "日盘", "english": "日盘", "file": "components\\latencyTrendAnalysis\\settingModal.vue"}, {"key": "texttext", "chinese": "早盘", "english": "早盘", "file": "components\\latencyTrendAnalysis\\settingModal.vue"}, {"key": "texttext", "chinese": "午盘", "english": "午盘", "file": "components\\latencyTrendAnalysis\\settingModal.vue"}, {"key": "texttexttexttext", "chinese": "分析目标", "english": "分析目标", "file": "components\\latencyTrendAnalysis\\settingModal.vue"}, {"key": "datatexttext", "chinese": "数据范围", "english": "数据范围", "file": "components\\latencyTrendAnalysis\\settingModal.vue"}, {"key": "datatexttext", "chinese": "数据过滤", "english": "数据过滤", "file": "components\\latencyTrendAnalysis\\settingModal.vue"}], "components\\ldpDataObservation\\nodeInfo.vue": [{"key": "texttextname", "chinese": "应用名称", "english": "应用名称", "file": "components\\ldpDataObservation\\nodeInfo.vue"}, {"key": "texttexttexttext", "chinese": "应用版本", "english": "应用版本", "file": "components\\ldpDataObservation\\nodeInfo.vue"}, {"key": "texttexttexttext", "chinese": "应用类型", "english": "应用类型", "file": "components\\ldpDataObservation\\nodeInfo.vue"}, {"key": "texttexttexttexttexttext", "chinese": "应用开发平台", "english": "应用开发平台", "file": "components\\ldpDataObservation\\nodeInfo.vue"}, {"key": "texttextname", "chinese": "产品名称", "english": "产品名称", "file": "components\\ldpDataObservation\\nodeInfo.vue"}, {"key": "texttexttexttext", "chinese": "应用简称", "english": "应用简称", "file": "components\\ldpDataObservation\\nodeInfo.vue"}, {"key": "texttexttexttext", "chinese": "应用编号", "english": "应用编号", "file": "components\\ldpDataObservation\\nodeInfo.vue"}, {"key": "texttexttexttext", "chinese": "应用实例", "english": "应用实例", "file": "components\\ldpDataObservation\\nodeInfo.vue"}, {"key": "pidtexttext", "chinese": "pid文件", "english": "pid文件", "file": "components\\ldpDataObservation\\nodeInfo.vue"}, {"key": "texttextname", "chinese": "插件名称", "english": "插件名称", "file": "components\\ldpDataObservation\\nodeInfo.vue"}, {"key": "texttexttext", "chinese": "实例号", "english": "实例号", "file": "components\\ldpDataObservation\\nodeInfo.vue"}, {"key": "texttexttexttext", "chinese": "插件版本", "english": "插件版本", "file": "components\\ldpDataObservation\\nodeInfo.vue"}, {"key": "texttexttexttext", "chinese": "发布日期", "english": "发布日期", "file": "components\\ldpDataObservation\\nodeInfo.vue"}, {"key": "managefunctiontexttext", "chinese": "管理功能数量", "english": "管理功能数量", "file": "components\\ldpDataObservation\\nodeInfo.vue"}, {"key": "managefunctionlist", "chinese": "管理功能列表", "english": "管理功能列表", "file": "components\\ldpDataObservation\\nodeInfo.vue"}, {"key": "texttext", "chinese": "目录", "english": "目录", "file": "components\\ldpDataObservation\\nodeInfo.vue"}, {"key": "texttexttexttext", "chinese": "目录地址", "english": "目录地址", "file": "components\\ldpDataObservation\\nodeInfo.vue"}, {"key": "configtexttext", "chinese": "配置地址", "english": "配置地址", "file": "components\\ldpDataObservation\\nodeInfo.vue"}, {"key": "zkconfigtexttext", "chinese": "zk配置地址", "english": "zk配置地址", "file": "components\\ldpDataObservation\\nodeInfo.vue"}], "components\\ldpLinkConfig\\appClusterConfig.vue": [{"key": "texttexttexttextinfo", "chinese": "仲裁服务信息", "english": "仲裁服务信息", "file": "components\\ldpLinkConfig\\appClusterConfig.vue"}, {"key": "texttexttexttexttext", "chinese": "服务提供者", "english": "服务提供者", "file": "components\\ldpLinkConfig\\appClusterConfig.vue"}, {"key": "texttexttexttexttexttext", "chinese": "服务集群地址", "english": "服务集群地址", "file": "components\\ldpLinkConfig\\appClusterConfig.vue"}, {"key": "texttexttexttexttexttexttexttext", "chinese": "集中仲裁服务路径", "english": "集中仲裁服务路径", "file": "components\\ldpLinkConfig\\appClusterConfig.vue"}, {"key": "texttexttexttext", "chinese": "集群类型", "english": "集群类型", "file": "components\\ldpLinkConfig\\appClusterConfig.vue"}, {"key": "texttexttexttexttexttexttexttext", "chinese": "集群应用节点类型", "english": "集群应用节点类型", "file": "components\\ldpLinkConfig\\appClusterConfig.vue"}, {"key": "texttexttexttexttexttexttext", "chinese": "集群内成员个数", "english": "集群内成员个数", "file": "components\\ldpLinkConfig\\appClusterConfig.vue"}, {"key": "texttexttexttextinfo", "chinese": "应用集群信息", "english": "应用集群信息", "file": "components\\ldpLinkConfig\\appClusterConfig.vue"}, {"key": "texttexttexttextinfothistabledataLength", "chinese": "应用集群信息：${(this.tableData || []).length}", "english": "应用集群信息：${(this.tableData || []).length}", "file": "components\\ldpLinkConfig\\appClusterConfig.vue"}], "components\\ldpLinkConfig\\appInstanceConfig.vue": [{"key": "texttexttexttexttexttext", "chinese": "应用注册中心", "english": "应用注册中心", "file": "components\\ldpLinkConfig\\appInstanceConfig.vue"}, {"key": "texttextinfotexttext", "chinese": "节点信息同步", "english": "节点信息同步", "file": "components\\ldpLinkConfig\\appInstanceConfig.vue"}, {"key": "texttexttexttexttexttexttexttexttexttext", "chinese": "应用节点注册中心路径", "english": "应用节点注册中心路径", "file": "components\\ldpLinkConfig\\appInstanceConfig.vue"}, {"key": "texttexttexttext", "chinese": "短应用名", "english": "短应用名", "file": "components\\ldpLinkConfig\\appInstanceConfig.vue"}, {"key": "texttexttexttext", "chinese": "节点编号", "english": "节点编号", "file": "components\\ldpLinkConfig\\appInstanceConfig.vue"}, {"key": "texttexttexttext", "chinese": "分片编号", "english": "分片编号", "file": "components\\ldpLinkConfig\\appInstanceConfig.vue"}, {"key": "texttexttexttexttexttext", "chinese": "应用节点身份", "english": "应用节点身份", "file": "components\\ldpLinkConfig\\appInstanceConfig.vue"}, {"key": "zkconfigtexttext", "chinese": "zk配置节点", "english": "zk配置节点", "file": "components\\ldpLinkConfig\\appInstanceConfig.vue"}, {"key": "texttexttexttextinfo", "chinese": "应用节点信息", "english": "应用节点信息", "file": "components\\ldpLinkConfig\\appInstanceConfig.vue"}, {"key": "texttexttexttextinfothistabledataLength", "chinese": "应用节点信息：${(this.tableData || []).length}", "english": "应用节点信息：${(this.tableData || []).length}", "file": "components\\ldpLinkConfig\\appInstanceConfig.vue"}, {"key": "texttexttextdeletetexttextnametexttexttexttexttexttexttexttext", "chinese": "您确定删除名为\"${name}\"的应用节点实例吗？", "english": "您确定删除名为\"${name}\"的应用节点实例吗？", "file": "components\\ldpLinkConfig\\appInstanceConfig.vue"}], "components\\ldpLinkConfig\\machineRoomConfig.vue": [{"key": "texttexttexttext", "chinese": "机房别名", "english": "机房别名", "file": "components\\ldpLinkConfig\\machineRoomConfig.vue"}, {"key": "texttexttexttexttexttext", "chinese": "关联应用个数", "english": "关联应用个数", "file": "components\\ldpLinkConfig\\machineRoomConfig.vue"}, {"key": "texttextinfothistabledataLength", "chinese": "机房信息：${(this.tableData || []).length}", "english": "机房信息：${(this.tableData || []).length}", "file": "components\\ldpLinkConfig\\machineRoomConfig.vue"}], "components\\ldpLinkConfig\\productInfoConfig.vue": [{"key": "texttextinfo", "chinese": "产品信息", "english": "产品信息", "file": "components\\ldpLinkConfig\\productInfoConfig.vue"}, {"key": "texttexttexttexttext", "chinese": "单例上下文", "english": "单例上下文", "file": "components\\ldpLinkConfig\\productInfoConfig.vue"}, {"key": "texttexttexttexttext", "chinese": "集群上下文", "english": "集群上下文", "file": "components\\ldpLinkConfig\\productInfoConfig.vue"}, {"key": "texttext", "chinese": "模板", "english": "模板", "file": "components\\ldpLinkConfig\\productInfoConfig.vue"}, {"key": "texttext", "chinese": "应用", "english": "应用", "file": "components\\ldpLinkConfig\\productInfoConfig.vue"}, {"key": "texttexttexttext", "chinese": "关联应用", "english": "关联应用", "file": "components\\ldpLinkConfig\\productInfoConfig.vue"}, {"key": "texttext", "chinese": "服务", "english": "服务", "file": "components\\ldpLinkConfig\\productInfoConfig.vue"}, {"key": "texttexttexttext", "chinese": "服务类型", "english": "服务类型", "file": "components\\ldpLinkConfig\\productInfoConfig.vue"}, {"key": "texttexttext", "chinese": "主机名", "english": "主机名", "file": "components\\ldpLinkConfig\\productInfoConfig.vue"}, {"key": "texttexttexttext", "chinese": "主机别名", "english": "主机别名", "file": "components\\ldpLinkConfig\\productInfoConfig.vue"}, {"key": "iptexttexttexttext", "chinese": "IP地址/域名", "english": "IP地址/域名", "file": "components\\ldpLinkConfig\\productInfoConfig.vue"}, {"key": "texttext", "chinese": "机房", "english": "机房", "file": "components\\ldpLinkConfig\\productInfoConfig.vue"}, {"key": "texttexttext", "chinese": "机房名", "english": "机房名", "file": "components\\ldpLinkConfig\\productInfoConfig.vue"}, {"key": "texttexttexttext", "chinese": "应用分片", "english": "应用分片", "file": "components\\ldpLinkConfig\\productInfoConfig.vue"}, {"key": "texttexttext", "chinese": "分片名", "english": "分片名", "file": "components\\ldpLinkConfig\\productInfoConfig.vue"}, {"key": "texttexttextconfig", "chinese": "已托管配置", "english": "已托管配置", "file": "components\\ldpLinkConfig\\productInfoConfig.vue"}, {"key": "configtexttextname", "chinese": "配置节点名称", "english": "配置节点名称", "file": "components\\ldpLinkConfig\\productInfoConfig.vue"}, {"key": "configtexttexttext", "chinese": "配置根目录", "english": "配置根目录", "file": "components\\ldpLinkConfig\\productInfoConfig.vue"}, {"key": "configtexttexttext", "chinese": "配置提供者", "english": "配置提供者", "file": "components\\ldpLinkConfig\\productInfoConfig.vue"}, {"key": "configtexttexttexttext", "chinese": "配置服务地址", "english": "配置服务地址", "file": "components\\ldpLinkConfig\\productInfoConfig.vue"}, {"key": "texttexttexttext", "chinese": "产品实例：", "english": "产品实例：", "file": "components\\ldpLinkConfig\\productInfoConfig.vue"}, {"key": "texttextname", "chinese": "产品名称：", "english": "产品名称：", "file": "components\\ldpLinkConfig\\productInfoConfig.vue"}, {"key": "texttexttexttext", "chinese": "产品类型：", "english": "产品类型：", "file": "components\\ldpLinkConfig\\productInfoConfig.vue"}, {"key": "texttexttexttexttexttext", "chinese": "关联业务系统：", "english": "关联业务系统：", "file": "components\\ldpLinkConfig\\productInfoConfig.vue"}, {"key": "texttextconfigtexttext", "chinese": "产品配置中心：", "english": "产品配置中心：", "file": "components\\ldpLinkConfig\\productInfoConfig.vue"}, {"key": "texttexttext", "chinese": "主题数：", "english": "主题数：", "file": "components\\ldpLinkConfig\\productInfoConfig.vue"}, {"key": "texttexttexttexttexttext", "chinese": "主题引用模板：", "english": "主题引用模板：", "file": "components\\ldpLinkConfig\\productInfoConfig.vue"}, {"key": "texttexttexttexttexttext", "chinese": "单例上下文数：", "english": "单例上下文数：", "file": "components\\ldpLinkConfig\\productInfoConfig.vue"}, {"key": "texttexttexttexttexttexttexttexttext", "chinese": "单例上下文引用模板：", "english": "单例上下文引用模板：", "file": "components\\ldpLinkConfig\\productInfoConfig.vue"}, {"key": "texttexttexttexttexttext", "chinese": "集群上下文数：", "english": "集群上下文数：", "file": "components\\ldpLinkConfig\\productInfoConfig.vue"}, {"key": "texttexttexttexttexttexttexttexttext", "chinese": "集群上下文引用模板：", "english": "集群上下文引用模板：", "file": "components\\ldpLinkConfig\\productInfoConfig.vue"}, {"key": "texttexttexttexttext", "chinese": "主题模板数：", "english": "主题模板数：", "file": "components\\ldpLinkConfig\\productInfoConfig.vue"}, {"key": "texttexttexttexttexttexttext", "chinese": "通用上下文模板：", "english": "通用上下文模板：", "file": "components\\ldpLinkConfig\\productInfoConfig.vue"}, {"key": "texttexttexttexttexttexttext", "chinese": "集群上下文模板：", "english": "集群上下文模板：", "file": "components\\ldpLinkConfig\\productInfoConfig.vue"}], "components\\ldpLinkConfig\\productMonitorConfig.vue": [{"key": "textfunctiontexttexttexttexttexttexttexttexttexttexttexttexttexttexttexttexttexttexttexttexttext", "chinese": "该功能为商业版特性,请咨询销售获取商业版本开通策略", "english": "该功能为商业版特性,请咨询销售获取商业版本开通策略", "file": "components\\ldpLinkConfig\\productMonitorConfig.vue"}, {"key": "texttexttexttexttexttextTextMonitortotalText", "chinese": "`告警规则执行 共 ${monitorTotal} 条`", "english": "`告警规则执行 共 ${monitorTotal} 条`", "file": "components\\ldpLinkConfig\\productMonitorConfig.vue"}, {"key": "texttexttexttexttexttextmonitortexttextconfig", "chinese": "当前产品暂无监控告警配置", "english": "当前产品暂无监控告警配置", "file": "components\\ldpLinkConfig\\productMonitorConfig.vue"}, {"key": "addtexttextsuccess", "chinese": "添加规则成功！", "english": "添加规则成功！", "file": "components\\ldpLinkConfig\\productMonitorConfig.vue"}, {"key": "texttexttexttexttexttextsuccess", "chinese": "告警规则清除成功！", "english": "告警规则清除成功！", "file": "components\\ldpLinkConfig\\productMonitorConfig.vue"}, {"key": "texttexttexttexttexttextTextMonitortotalText", "chinese": "告警规则执行 共 ${monitorTotal} 条", "english": "告警规则执行 共 ${monitorTotal} 条", "file": "components\\ldpLinkConfig\\productMonitorConfig.vue"}, {"key": "addtexttext", "chinese": "添加规则", "english": "添加规则", "file": "components\\ldpLinkConfig\\productMonitorConfig.vue"}, {"key": "importtexttext", "chinese": "导入规则", "english": "导入规则", "file": "components\\ldpLinkConfig\\productMonitorConfig.vue"}, {"key": "texttexttexttext", "chinese": "清空规则", "english": "清空规则", "file": "components\\ldpLinkConfig\\productMonitorConfig.vue"}], "components\\ldpLinkConfig\\productServiceConfig.vue": [{"key": "texttext", "chinese": "插件", "english": "插件", "file": "components\\ldpLinkConfig\\productServiceConfig.vue"}, {"key": "functiontexttext", "chinese": "功能说明", "english": "功能说明", "file": "components\\ldpLinkConfig\\productServiceConfig.vue"}, {"key": "functiontexttext", "chinese": "功能号名", "english": "功能号名", "file": "components\\ldpLinkConfig\\productServiceConfig.vue"}, {"key": "texttexttexttext", "chinese": "子系统号", "english": "子系统号", "file": "components\\ldpLinkConfig\\productServiceConfig.vue"}, {"key": "texttexttexttext", "chinese": "读写标识", "english": "读写标识", "file": "components\\ldpLinkConfig\\productServiceConfig.vue"}, {"key": "texttexttexttexttexttextstatus", "chinese": "可运行的业务状态", "english": "可运行的业务状态", "file": "components\\ldpLinkConfig\\productServiceConfig.vue"}, {"key": "texttexttexttext", "chinese": "定位模式", "english": "定位模式", "file": "components\\ldpLinkConfig\\productServiceConfig.vue"}, {"key": "datamanagefunctiontext", "chinese": "数据管理功能号", "english": "数据管理功能号", "file": "components\\ldpLinkConfig\\productServiceConfig.vue"}, {"key": "texttextfunctiontext", "chinese": "平台功能号", "english": "平台功能号", "file": "components\\ldpLinkConfig\\productServiceConfig.vue"}, {"key": "texttextfunctiontext", "chinese": "业务功能号", "english": "业务功能号", "file": "components\\ldpLinkConfig\\productServiceConfig.vue"}, {"key": "datatexttext", "chinese": "数据分片", "english": "数据分片", "file": "components\\ldpLinkConfig\\productServiceConfig.vue"}, {"key": "datatext", "chinese": "数据源", "english": "数据源", "file": "components\\ldpLinkConfig\\productServiceConfig.vue"}, {"key": "texttextkey", "chinese": "分片key", "english": "分片key", "file": "components\\ldpLinkConfig\\productServiceConfig.vue"}, {"key": "texttexttext", "chinese": "定位串", "english": "定位串", "file": "components\\ldpLinkConfig\\productServiceConfig.vue"}, {"key": "datatexttext", "chinese": "数据区间", "english": "数据区间", "file": "components\\ldpLinkConfig\\productServiceConfig.vue"}, {"key": "texttexttexttext", "chinese": "关联集群", "english": "关联集群", "file": "components\\ldpLinkConfig\\productServiceConfig.vue"}, {"key": "datatexttextfunctiontext", "chinese": "数据上场功能号", "english": "数据上场功能号", "file": "components\\ldpLinkConfig\\productServiceConfig.vue"}, {"key": "texttexttexttextfunctiontext", "chinese": "二次上场功能号", "english": "二次上场功能号", "file": "components\\ldpLinkConfig\\productServiceConfig.vue"}, {"key": "sqlfunctiontext", "chinese": "SQL功能号", "english": "SQL功能号", "file": "components\\ldpLinkConfig\\productServiceConfig.vue"}, {"key": "texttexttext", "chinese": "内存表", "english": "内存表", "file": "components\\ldpLinkConfig\\productServiceConfig.vue"}, {"key": "configinfotexttextsuccess", "chinese": "配置信息同步成功！", "english": "配置信息同步成功！", "file": "components\\ldpLinkConfig\\productServiceConfig.vue"}, {"key": "datatexttexttexttextmanagefunctiontextconfiginfotexttexttext", "chinese": "数据分片、数据管理功能号配置信息已同步！", "english": "数据分片、数据管理功能号配置信息已同步！", "file": "components\\ldpLinkConfig\\productServiceConfig.vue"}, {"key": "configinfotexttextfailed", "chinese": "配置信息同步失败！", "english": "配置信息同步失败！", "file": "components\\ldpLinkConfig\\productServiceConfig.vue"}, {"key": "textconfig", "chinese": "去配置", "english": "去配置", "file": "components\\ldpLinkConfig\\productServiceConfig.vue"}, {"key": "editsuccess", "chinese": "修改成功！", "english": "修改成功！", "file": "components\\ldpLinkConfig\\productServiceConfig.vue"}, {"key": "texttexttexttextdata", "chinese": "同步服务数据", "english": "同步服务数据", "file": "components\\ldpLinkConfig\\productServiceConfig.vue"}], "components\\ldpLinkConfig\\rcmContext.vue": [{"key": "texttexttextlist", "chinese": "上下文列表", "english": "上下文列表", "file": "components\\ldpLinkConfig\\rcmContext.vue"}, {"key": "texttexttextname", "chinese": "上下文名称", "english": "上下文名称", "file": "components\\ldpLinkConfig\\rcmContext.vue"}, {"key": "texttext", "chinese": "模式", "english": "模式", "file": "components\\ldpLinkConfig\\rcmContext.vue"}, {"key": "texttexttexttext", "chinese": "发送主题", "english": "发送主题", "file": "components\\ldpLinkConfig\\rcmContext.vue"}, {"key": "texttexttexttext", "chinese": "接收主题", "english": "接收主题", "file": "components\\ldpLinkConfig\\rcmContext.vue"}, {"key": "texttexttexttext", "chinese": "引用模板", "english": "引用模板", "file": "components\\ldpLinkConfig\\rcmContext.vue"}, {"key": "texttext", "chinese": "标签", "english": "标签", "file": "components\\ldpLinkConfig\\rcmContext.vue"}], "components\\ldpLinkConfig\\serverConfig.vue": [{"key": "texttexttexttexttext", "chinese": "测试连通性", "english": "测试连通性", "file": "components\\ldpLinkConfig\\serverConfig.vue"}, {"key": "sshconfig", "chinese": "SSH配置", "english": "SSH配置", "file": "components\\ldpLinkConfig\\serverConfig.vue"}, {"key": "texttextiptexttextReturnH", "chinese": "; // 渲染IP地址\r\n                        return h(", "english": "; // 渲染IP地址\r\n                        return h(", "file": "components\\ldpLinkConfig\\serverConfig.vue"}, {"key": "sshtexttexttext", "chinese": "SSH用户名", "english": "SSH用户名", "file": "components\\ldpLinkConfig\\serverConfig.vue"}, {"key": "texttextsshtexttexttextReturnH", "chinese": "; // 渲染SSH用户名\r\n                        return h(", "english": "; // 渲染SSH用户名\r\n                        return h(", "file": "components\\ldpLinkConfig\\serverConfig.vue"}, {"key": "texttexttexttext", "chinese": "关联机房", "english": "关联机房", "file": "components\\ldpLinkConfig\\serverConfig.vue"}, {"key": "text", "chinese": "或", "english": "或", "file": "components\\ldpLinkConfig\\serverConfig.vue"}, {"key": "texttexttextthistabledataLength", "chinese": "服务器：${(this.tableData || []).length}", "english": "服务器：${(this.tableData || []).length}", "file": "components\\ldpLinkConfig\\serverConfig.vue"}], "components\\ldpLogCenter\\ldpTodbErrorLog.vue": [{"key": "texttextstatusAloadingVif", "chinese": ">\r\n        <!-- 加载状态 -->\r\n        <a-loading v-if=", "english": ">\r\n        <!-- 加载状态 -->\r\n        <a-loading v-if=", "file": "components\\ldpLogCenter\\ldpTodbErrorLog.vue"}, {"key": "texttexterrortexttextdivDivClass", "chinese": ">回库错误日志</div>\r\n                    <div class=", "english": ">回库错误日志</div>\r\n                    <div class=", "file": "components\\ldpLogCenter\\ldpTodbErrorLog.vue"}, {"key": "texttextname", "chinese": "集群名称", "english": "集群名称", "file": "components\\ldpLogCenter\\ldpTodbErrorLog.vue"}, {"key": "texttexttexttext", "chinese": "账户类型", "english": "账户类型", "file": "components\\ldpLogCenter\\ldpTodbErrorLog.vue"}, {"key": "texttexttexttexttexttexttexttexttexttexttext", "chinese": "多个账号以英文逗号隔开", "english": "多个账号以英文逗号隔开", "file": "components\\ldpLogCenter\\ldpTodbErrorLog.vue"}, {"key": "texttexttexttexttexttext", "chinese": "选择不能为空", "english": "选择不能为空", "file": "components\\ldpLogCenter\\ldpTodbErrorLog.vue"}, {"key": "texttextstatus", "chinese": "回库状态", "english": "回库状态", "file": "components\\ldpLogCenter\\ldpTodbErrorLog.vue"}, {"key": "textsuccess", "chinese": "已成功", "english": "已成功", "file": "components\\ldpLogCenter\\ldpTodbErrorLog.vue"}, {"key": "texttexttime", "chinese": "事务时间", "english": "事务时间", "file": "components\\ldpLogCenter\\ldpTodbErrorLog.vue"}, {"key": "texttexttext", "chinese": "线程组", "english": "线程组", "file": "components\\ldpLogCenter\\ldpTodbErrorLog.vue"}, {"key": "texttexttexttexttexttexttexttexttexttexttexttexttexttexttext50text", "chinese": "支持多账号以英文逗号分隔且最多50个", "english": "支持多账号以英文逗号分隔且最多50个", "file": "components\\ldpLogCenter\\ldpTodbErrorLog.vue"}, {"key": "texttext", "chinese": "账户", "english": "账户", "file": "components\\ldpLogCenter\\ldpTodbErrorLog.vue"}, {"key": "texttexttext", "chinese": "线程号", "english": "线程号", "file": "components\\ldpLogCenter\\ldpTodbErrorLog.vue"}, {"key": "texttexttexttext", "chinese": "事务长度", "english": "事务长度", "file": "components\\ldpLogCenter\\ldpTodbErrorLog.vue"}, {"key": "errortext", "chinese": "错误号", "english": "错误号", "file": "components\\ldpLogCenter\\ldpTodbErrorLog.vue"}, {"key": "errortexttext", "chinese": "错误消息", "english": "错误消息", "file": "components\\ldpLogCenter\\ldpTodbErrorLog.vue"}, {"key": "texttextstatus", "chinese": "回库状态：", "english": "回库状态：", "file": "components\\ldpLogCenter\\ldpTodbErrorLog.vue"}, {"key": "texttexttexttexttexttexttexttexttextoperationtexttexttexttexttexttexttexterrortexttexttexttexttexttexttexttexttexttexttexttexttexttexttext", "chinese": "表示在用户进行\"重试\"操作之后，本地记录的错误事务，是否重新发送到网关，完成回库。", "english": "表示在用户进行\"重试\"操作之后，本地记录的错误事务，是否重新发送到网关，完成回库。", "file": "components\\ldpLogCenter\\ldpTodbErrorLog.vue"}, {"key": "textsuccess", "chinese": "未成功", "english": "未成功", "file": "components\\ldpLogCenter\\ldpTodbErrorLog.vue"}, {"key": "0Texttext", "chinese": ")?.[0]; // 获取", "english": ")?.[0]; // 获取", "file": "components\\ldpLogCenter\\ldpTodbErrorLog.vue"}, {"key": "queryfailed", "chinese": "查询失败", "english": "查询失败", "file": "components\\ldpLogCenter\\ldpTodbErrorLog.vue"}, {"key": "texttexttexttext", "chinese": "股东代码", "english": "股东代码", "file": "components\\ldpLogCenter\\ldpTodbErrorLog.vue"}, {"key": "texttexttexttexttexttexttexttexttexttexttextdetail", "chinese": "点击行查看对应日志记录详情", "english": "点击行查看对应日志记录详情", "file": "components\\ldpLogCenter\\ldpTodbErrorLog.vue"}, {"key": "textTotalcountTexttexttext", "chinese": "共 {{ totalCount }} 条记录", "english": "共 {{ totalCount }} 条记录", "file": "components\\ldpLogCenter\\ldpTodbErrorLog.vue"}, {"key": "texttexttext", "chinese": "上一页", "english": "上一页", "file": "components\\ldpLogCenter\\ldpTodbErrorLog.vue"}, {"key": "texttexttext", "chinese": "下一页", "english": "下一页", "file": "components\\ldpLogCenter\\ldpTodbErrorLog.vue"}], "components\\ldpLogCenter\\ldpTodbErrorRetry.vue": [{"key": "texttexttexttext", "chinese": "全部账户：", "english": "全部账户：", "file": "components\\ldpLogCenter\\ldpTodbErrorRetry.vue"}, {"key": "texttexttext", "chinese": "进行中：", "english": "进行中：", "file": "components\\ldpLogCenter\\ldpTodbErrorRetry.vue"}, {"key": "texttexttexttext", "chinese": "正常完成：", "english": "正常完成：", "file": "components\\ldpLogCenter\\ldpTodbErrorRetry.vue"}, {"key": "texttexttexttext", "chinese": "终止完成：", "english": "终止完成：", "file": "components\\ldpLogCenter\\ldpTodbErrorRetry.vue"}, {"key": "texttexttext", "chinese": "进行中", "english": "进行中", "file": "components\\ldpLogCenter\\ldpTodbErrorRetry.vue"}, {"key": "texttexttexttext", "chinese": "正常完成", "english": "正常完成", "file": "components\\ldpLogCenter\\ldpTodbErrorRetry.vue"}, {"key": "texttexttexttext", "chinese": "终止完成", "english": "终止完成", "file": "components\\ldpLogCenter\\ldpTodbErrorRetry.vue"}, {"key": "texttexterrortexttexttext", "chinese": "剩余错误账户数", "english": "剩余错误账户数", "file": "components\\ldpLogCenter\\ldpTodbErrorRetry.vue"}, {"key": "errortexttexttexttext", "chinese": "错误账户总数", "english": "错误账户总数", "file": "components\\ldpLogCenter\\ldpTodbErrorRetry.vue"}, {"key": "texttexttexttexterrortexttexttext", "chinese": "剩余回库错误事务数", "english": "剩余回库错误事务数", "file": "components\\ldpLogCenter\\ldpTodbErrorRetry.vue"}, {"key": "texttexterrortexttexttexttext", "chinese": "回库错误事务总数", "english": "回库错误事务总数", "file": "components\\ldpLogCenter\\ldpTodbErrorRetry.vue"}, {"key": "texttext", "chinese": "重试", "english": "重试", "file": "components\\ldpLogCenter\\ldpTodbErrorRetry.vue"}, {"key": "texttext", "chinese": "日志", "english": "日志", "file": "components\\ldpLogCenter\\ldpTodbErrorRetry.vue"}, {"key": "texttexttexttexttexttextstatus", "chinese": "最后重试执行状态", "english": "最后重试执行状态", "file": "components\\ldpLogCenter\\ldpTodbErrorRetry.vue"}, {"key": "texttexttexttext", "chinese": "全部重试", "english": "全部重试", "file": "components\\ldpLogCenter\\ldpTodbErrorRetry.vue"}, {"key": "texttexttexttext", "chinese": "全部终止", "english": "全部终止", "file": "components\\ldpLogCenter\\ldpTodbErrorRetry.vue"}, {"key": "texttexttexttext", "chinese": "全部账户", "english": "全部账户", "file": "components\\ldpLogCenter\\ldpTodbErrorRetry.vue"}, {"key": "texttexttexttexttexttexttexttext", "chinese": "需要重试的事务数", "english": "需要重试的事务数", "file": "components\\ldpLogCenter\\ldpTodbErrorRetry.vue"}, {"key": "texttexttextsuccesstexttexttext", "chinese": "重试已成功事务数", "english": "重试已成功事务数", "file": "components\\ldpLogCenter\\ldpTodbErrorRetry.vue"}, {"key": "texttexttexttext", "chinese": "执行进度", "english": "执行进度", "file": "components\\ldpLogCenter\\ldpTodbErrorRetry.vue"}, {"key": "texttexttexttexttext", "chinese": "执行总耗时", "english": "执行总耗时", "file": "components\\ldpLogCenter\\ldpTodbErrorRetry.vue"}, {"key": "starttexttexttexttexttexttext", "chinese": "开始重试消息序号", "english": "开始重试消息序号", "file": "components\\ldpLogCenter\\ldpTodbErrorRetry.vue"}, {"key": "starttexttexttexttexttext", "chinese": "开始重试事务号", "english": "开始重试事务号", "file": "components\\ldpLogCenter\\ldpTodbErrorRetry.vue"}, {"key": "endtexttexttexttexttext", "chinese": "结束重试消息号", "english": "结束重试消息号", "file": "components\\ldpLogCenter\\ldpTodbErrorRetry.vue"}, {"key": "endtexttexttexttext", "chinese": "结束的事务号", "english": "结束的事务号", "file": "components\\ldpLogCenter\\ldpTodbErrorRetry.vue"}, {"key": "texttexttexttexttexttexttext", "chinese": "最新重试消息号", "english": "最新重试消息号", "file": "components\\ldpLogCenter\\ldpTodbErrorRetry.vue"}, {"key": "texttexttexttexttexttexttexttext", "chinese": "最新重试的事务号", "english": "最新重试的事务号", "file": "components\\ldpLogCenter\\ldpTodbErrorRetry.vue"}, {"key": "texttexttexttexttexttexttexttexttexttexttext", "chinese": "已存在进行中的重试任务！", "english": "已存在进行中的重试任务！", "file": "components\\ldpLogCenter\\ldpTodbErrorRetry.vue"}, {"key": "texttexttexttext", "chinese": "终止任务", "english": "终止任务", "file": "components\\ldpLogCenter\\ldpTodbErrorRetry.vue"}, {"key": "textconfirmtexttexttexttexttexttexttexttexttexttexttexttexttexttexttexttext", "chinese": "您确认要终止当前正在执行中的重试任务吗？", "english": "您确认要终止当前正在执行中的重试任务吗？", "file": "components\\ldpLogCenter\\ldpTodbErrorRetry.vue"}, {"key": "texttexterrortexttexttexttext", "chinese": "创建错误重试任务", "english": "创建错误重试任务", "file": "components\\ldpLogCenter\\ldpTodbErrorRetry.vue"}, {"key": "textconfirmtexttexttexttexttexttexterrortexttexttexttexttexttexttexttexttext", "chinese": "您确认要对所有业务错误账户执行重试处理吗？", "english": "您确认要对所有业务错误账户执行重试处理吗？", "file": "components\\ldpLogCenter\\ldpTodbErrorRetry.vue"}, {"key": "texttexttexttexttexttexttexttext", "chinese": "暂无集群需要重试！", "english": "暂无集群需要重试！", "file": "components\\ldpLogCenter\\ldpTodbErrorRetry.vue"}, {"key": "texttexttexttexttexttexttext", "chinese": "暂无进行中任务！", "english": "暂无进行中任务！", "file": "components\\ldpLogCenter\\ldpTodbErrorRetry.vue"}, {"key": "texttexttexttexttexttextsuccess", "chinese": "创建重试任务成功!", "english": "创建重试任务成功!", "file": "components\\ldpLogCenter\\ldpTodbErrorRetry.vue"}, {"key": "texttexttexttexttexttextfailed", "chinese": "创建重试任务失败!", "english": "创建重试任务失败!", "file": "components\\ldpLogCenter\\ldpTodbErrorRetry.vue"}, {"key": "texttexttexttextsuccess", "chinese": "终止任务成功!", "english": "终止任务成功!", "file": "components\\ldpLogCenter\\ldpTodbErrorRetry.vue"}, {"key": "texttexttexttextfailed", "chinese": "终止任务失败!", "english": "终止任务失败!", "file": "components\\ldpLogCenter\\ldpTodbErrorRetry.vue"}, {"key": "texttexttexttexttexttexterrorquery", "chinese": "核心集群回库错误查询", "english": "核心集群回库错误查询", "file": "components\\ldpLogCenter\\ldpTodbErrorRetry.vue"}], "components\\ldpLogCenter\\recordMsgPoptip.vue": [{"key": "texttexttexttextinfo", "chinese": "记录字段信息", "english": "记录字段信息", "file": "components\\ldpLogCenter\\recordMsgPoptip.vue"}, {"key": "texttexttext", "chinese": "字段名", "english": "字段名", "file": "components\\ldpLogCenter\\recordMsgPoptip.vue"}, {"key": "texttexttext", "chinese": "字段值", "english": "字段值", "file": "components\\ldpLogCenter\\recordMsgPoptip.vue"}], "components\\ldpMonitor\\appTypeObserver.vue": [{"key": "texttexttexttextdivAloadingVif", "chinese": ">全局总览</div>\r\n            <a-loading v-if=", "english": ">全局总览</div>\r\n            <a-loading v-if=", "file": "components\\ldpMonitor\\appTypeObserver.vue"}, {"key": "texttexttexttext", "chinese": "全局总览", "english": "全局总览", "file": "components\\ldpMonitor\\appTypeObserver.vue"}], "components\\ldpMonitor\\businessMonitor.vue": [], "components\\ldpMonitor\\clusterAndGroupMonitor.vue": [{"key": "text", "chinese": "主", "english": "主", "file": "components\\ldpMonitor\\clusterAndGroupMonitor.vue"}, {"key": "texttext", "chinese": "同城", "english": "同城", "file": "components\\ldpMonitor\\clusterAndGroupMonitor.vue"}, {"key": "texttext", "chinese": "异地", "english": "异地", "file": "components\\ldpMonitor\\clusterAndGroupMonitor.vue"}, {"key": "texttexteleshardingno", "chinese": "分片${ele.shardingNo || '-'}", "english": "分片${ele.shardingNo || '-'}", "file": "components\\ldpMonitor\\clusterAndGroupMonitor.vue"}], "components\\ldpMonitor\\clusterProcessPerform.vue": [{"key": "functiontexttexttext", "chinese": "功能号处理", "english": "功能号处理", "file": "components\\ldpMonitor\\clusterProcessPerform.vue"}, {"key": "texttexttps", "chinese": "吞吐(tps)", "english": "吞吐(tps)", "file": "components\\ldpMonitor\\clusterProcessPerform.vue"}, {"key": "texttexttexttextns", "chinese": "平均时延(ns)", "english": "平均时延(ns)", "file": "components\\ldpMonitor\\clusterProcessPerform.vue"}, {"key": "texttexttexttext", "chinese": "委托交易", "english": "委托交易", "file": "components\\ldpMonitor\\clusterProcessPerform.vue"}, {"key": "texttexttext", "chinese": "吞吐率", "english": "吞吐率", "file": "components\\ldpMonitor\\clusterProcessPerform.vue"}, {"key": "errortext", "chinese": "错误率", "english": "错误率", "file": "components\\ldpMonitor\\clusterProcessPerform.vue"}, {"key": "texttextquery", "chinese": "委托查询", "english": "委托查询", "file": "components\\ldpMonitor\\clusterProcessPerform.vue"}, {"key": "texttexttexttext", "chinese": "其他业务", "english": "其他业务", "file": "components\\ldpMonitor\\clusterProcessPerform.vue"}], "components\\ldpMonitor\\dataAccordMonitor.vue": [{"key": "pleaseinputtexttext", "chinese": "请输入表名", "english": "请输入表名", "file": "components\\ldpMonitor\\dataAccordMonitor.vue"}, {"key": "texttexttext", "chinese": "未加载", "english": "未加载", "file": "components\\ldpMonitor\\dataAccordMonitor.vue"}, {"key": "loading", "chinese": "加载中", "english": "加载中", "file": "components\\ldpMonitor\\dataAccordMonitor.vue"}, {"key": "texttexttexttext", "chinese": "加载完成", "english": "加载完成", "file": "components\\ldpMonitor\\dataAccordMonitor.vue"}, {"key": "texttextfailed", "chinese": "加载失败", "english": "加载失败", "file": "components\\ldpMonitor\\dataAccordMonitor.vue"}, {"key": "texttexttexttexttext", "chinese": "内存记录数", "english": "内存记录数", "file": "components\\ldpMonitor\\dataAccordMonitor.vue"}, {"key": "texttexttexttexttexttext", "chinese": "持久化记录数", "english": "持久化记录数", "file": "components\\ldpMonitor\\dataAccordMonitor.vue"}, {"key": "datatexttext", "chinese": "数据差量", "english": "数据差量", "file": "components\\ldpMonitor\\dataAccordMonitor.vue"}, {"key": "texttextstatus", "chinese": "加载状态", "english": "加载状态", "file": "components\\ldpMonitor\\dataAccordMonitor.vue"}, {"key": "textdetail", "chinese": "表详情", "english": "表详情", "file": "components\\ldpMonitor\\dataAccordMonitor.vue"}, {"key": "texttexttexttext", "chinese": "上场次数:", "english": "上场次数:", "file": "components\\ldpMonitor\\dataAccordMonitor.vue"}, {"key": "texttexttextdetail", "chinese": "表加载详情", "english": "表加载详情", "file": "components\\ldpMonitor\\dataAccordMonitor.vue"}, {"key": "datatexttexttexttext0", "chinese": "数据差量不为0", "english": "数据差量不为0", "file": "components\\ldpMonitor\\dataAccordMonitor.vue"}], "components\\ldpMonitor\\ldpAppObserver.vue": [{"key": "texttexttexttextinfodivAloadingVif", "chinese": ">应用连接信息</div>\r\n            <a-loading v-if=", "english": ">应用连接信息</div>\r\n            <a-loading v-if=", "file": "components\\ldpMonitor\\ldpAppObserver.vue"}, {"key": "texttexttexttextinfo", "chinese": "应用连接信息", "english": "应用连接信息", "file": "components\\ldpMonitor\\ldpAppObserver.vue"}], "components\\ldpMonitor\\ldpAppObserverMenu.vue": [{"key": "texttexttexttexttexttexttextqueryDivVif", "chinese": ">\r\n        <!-- 上下游组合条件查询 -->\r\n        <div v-if=", "english": ">\r\n        <!-- 上下游组合条件查询 -->\r\n        <div v-if=", "file": "components\\ldpMonitor\\ldpAppObserverMenu.vue"}, {"key": "texttexttexttext", "chinese": "连接类型", "english": "连接类型", "file": "components\\ldpMonitor\\ldpAppObserverMenu.vue"}, {"key": "texttextstatus", "chinese": "连接状态", "english": "连接状态", "file": "components\\ldpMonitor\\ldpAppObserverMenu.vue"}, {"key": "texttexttexttext", "chinese": "仲裁节点", "english": "仲裁节点", "file": "components\\ldpMonitor\\ldpAppObserverMenu.vue"}, {"key": "searchtexttexttexttext", "chinese": "搜索应用节点", "english": "搜索应用节点", "file": "components\\ldpMonitor\\ldpAppObserverMenu.vue"}, {"key": "texttexttexttexttexthoptionHoptionValue", "chinese": ">上下游关系</h-option>\r\n            <h-option value=", "english": ">上下游关系</h-option>\r\n            <h-option value=", "file": "components\\ldpMonitor\\ldpAppObserverMenu.vue"}, {"key": "querytexttexttexttexttexttextlisterror", "chinese": "查询应用节点下拉列表-错误:", "english": "查询应用节点下拉列表-错误:", "file": "components\\ldpMonitor\\ldpAppObserverMenu.vue"}, {"key": "texttexttexttexttexttext", "chinese": "过滤仲裁节点", "english": "过滤仲裁节点", "file": "components\\ldpMonitor\\ldpAppObserverMenu.vue"}, {"key": "texttexttexttexttexttexttext", "chinese": "不过滤仲裁节点", "english": "不过滤仲裁节点", "file": "components\\ldpMonitor\\ldpAppObserverMenu.vue"}, {"key": "texttexttexttexttext", "chinese": "上下游关系", "english": "上下游关系", "file": "components\\ldpMonitor\\ldpAppObserverMenu.vue"}, {"key": "texttexttexttexttexttext", "chinese": "集群同步关系", "english": "集群同步关系", "file": "components\\ldpMonitor\\ldpAppObserverMenu.vue"}], "components\\ldpMonitor\\ldpClusterObserver.vue": [{"key": "texttexttexttextinfo", "chinese": "集群连接信息", "english": "集群连接信息", "file": "components\\ldpMonitor\\ldpClusterObserver.vue"}], "components\\ldpMonitor\\ldpClusterObserverMenu.vue": [{"key": "searchtexttext", "chinese": "搜索集群", "english": "搜索集群", "file": "components\\ldpMonitor\\ldpClusterObserverMenu.vue"}], "components\\ldpMonitor\\observerSetting.vue": [{"key": "texttexttexttextsetting", "chinese": "拓扑显示设置", "english": "拓扑显示设置", "file": "components\\ldpMonitor\\observerSetting.vue"}, {"key": "texttexttexttexttexttexttextdivDivClass", "chinese": ">显示未托管节点</div>\r\n                <div class=", "english": ">显示未托管节点</div>\r\n                <div class=", "file": "components\\ldpMonitor\\observerSetting.vue"}, {"key": "rcmconfigdivDivClass", "chinese": ">RCM配置</div>\r\n                <div class=", "english": ">RCM配置</div>\r\n                <div class=", "file": "components\\ldpMonitor\\observerSetting.vue"}, {"key": "texttexttexttextdivDivClass", "chinese": ">默认标签</div>\r\n                <div class=", "english": ">默认标签</div>\r\n                <div class=", "file": "components\\ldpMonitor\\observerSetting.vue"}, {"key": "queryrcmconfigfailed", "chinese": "查询rcm配置失败：", "english": "查询rcm配置失败：", "file": "components\\ldpMonitor\\observerSetting.vue"}, {"key": "querytexttextfailed", "chinese": "查询标签失败：", "english": "查询标签失败：", "file": "components\\ldpMonitor\\observerSetting.vue"}, {"key": "texttexttexttexttexttexttext", "chinese": "显示未托管节点", "english": "显示未托管节点", "file": "components\\ldpMonitor\\observerSetting.vue"}, {"key": "texttexttext", "chinese": "上下文", "english": "上下文", "file": "components\\ldpMonitor\\observerSetting.vue"}, {"key": "rcmconfig", "chinese": "RCM配置", "english": "RCM配置", "file": "components\\ldpMonitor\\observerSetting.vue"}, {"key": "texttexttexttext", "chinese": "默认标签", "english": "默认标签", "file": "components\\ldpMonitor\\observerSetting.vue"}], "components\\ldpMonitor\\operatePerform.vue": [{"key": "texttexttexttextInfosumbarData", "chinese": ">\r\n    <!-- 指标总览 -->\r\n    <info-sum-bar :data=", "english": ">\r\n    <!-- 指标总览 -->\r\n    <info-sum-bar :data=", "file": "components\\ldpMonitor\\operatePerform.vue"}, {"key": "texttexttexttexttexttexttexttext", "chinese": "委托业务性能总览", "english": "委托业务性能总览", "file": "components\\ldpMonitor\\operatePerform.vue"}, {"key": "texttext5texttext", "chinese": "最近5分钟", "english": "最近5分钟", "file": "components\\ldpMonitor\\operatePerform.vue"}, {"key": "texttext15texttext", "chinese": "最近15分钟", "english": "最近15分钟", "file": "components\\ldpMonitor\\operatePerform.vue"}, {"key": "texttext30texttext", "chinese": "最近30分钟", "english": "最近30分钟", "file": "components\\ldpMonitor\\operatePerform.vue"}, {"key": "texttext1texttext", "chinese": "最近1小时", "english": "最近1小时", "file": "components\\ldpMonitor\\operatePerform.vue"}, {"key": "5text", "chinese": "5秒", "english": "5秒", "file": "components\\ldpMonitor\\operatePerform.vue"}, {"key": "texttext", "chinese": "吞吐", "english": "吞吐", "file": "components\\ldpMonitor\\operatePerform.vue"}, {"key": "texttext", "chinese": "时延", "english": "时延", "file": "components\\ldpMonitor\\operatePerform.vue"}, {"key": "texttextqbs", "chinese": "吞吐(qbs)", "english": "吞吐(qbs)", "file": "components\\ldpMonitor\\operatePerform.vue"}, {"key": "texttextns", "chinese": "时延(ns)", "english": "时延(ns)", "file": "components\\ldpMonitor\\operatePerform.vue"}, {"key": "texttexttext", "chinese": "请求数", "english": "请求数", "file": "components\\ldpMonitor\\operatePerform.vue"}, {"key": "text", "chinese": "次", "english": "次", "file": "components\\ldpMonitor\\operatePerform.vue"}], "components\\ldpMonitor\\rcmObserver.vue": [{"key": "texttextdetail", "chinese": "查看详情", "english": "查看详情", "file": "components\\ldpMonitor\\rcmObserver.vue"}], "components\\ldpMonitor\\rcmObserverMenu.vue": [{"key": "searchtexttext", "chinese": "搜索主题", "english": "搜索主题", "file": "components\\ldpMonitor\\rcmObserverMenu.vue"}, {"key": "texttexttexttext", "chinese": "同步模式", "english": "同步模式", "file": "components\\ldpMonitor\\rcmObserverMenu.vue"}, {"key": "statustexttexttext", "chinese": "状态机复制", "english": "状态机复制", "file": "components\\ldpMonitor\\rcmObserverMenu.vue"}, {"key": "texttexttexttext", "chinese": "消息复制", "english": "消息复制", "file": "components\\ldpMonitor\\rcmObserverMenu.vue"}], "components\\ldpTable\\historyModifyList.vue": [{"key": "operationtexttext", "chinese": "操作内容", "english": "操作内容", "file": "components\\ldpTable\\historyModifyList.vue"}, {"key": "operationtexttext", "chinese": "操作用户", "english": "操作用户", "file": "components\\ldpTable\\historyModifyList.vue"}, {"key": "operationtexttext", "chinese": "操作执行", "english": "操作执行", "file": "components\\ldpTable\\historyModifyList.vue"}, {"key": "texttexttext", "chinese": "未执行", "english": "未执行", "file": "components\\ldpTable\\historyModifyList.vue"}, {"key": "texttext", "chinese": "失效", "english": "失效", "file": "components\\ldpTable\\historyModifyList.vue"}, {"key": "texttexttext", "chinese": "执行中", "english": "执行中", "file": "components\\ldpTable\\historyModifyList.vue"}, {"key": "texttexttexttext", "chinese": "执行完毕", "english": "执行完毕", "file": "components\\ldpTable\\historyModifyList.vue"}, {"key": "datatexttexttext", "chinese": "数据记录号", "english": "数据记录号", "file": "components\\ldpTable\\historyModifyList.vue"}, {"key": "texttexttexttext", "chinese": "字段类型", "english": "字段类型", "file": "components\\ldpTable\\historyModifyList.vue"}, {"key": "edittext", "chinese": "修改值", "english": "修改值", "file": "components\\ldpTable\\historyModifyList.vue"}, {"key": "texttexttexttext", "chinese": "原纪录值", "english": "原纪录值", "file": "components\\ldpTable\\historyModifyList.vue"}, {"key": "<PERSON><PERSON><PERSON>", "chinese": "修改状态", "english": "修改状态", "file": "components\\ldpTable\\historyModifyList.vue"}, {"key": "edittexttext", "chinese": "修改执行", "english": "修改执行", "file": "components\\ldpTable\\historyModifyList.vue"}, {"key": "textedittexttexttext", "chinese": "该修改未执行", "english": "该修改未执行", "file": "components\\ldpTable\\historyModifyList.vue"}, {"key": "texttexttexttextRequestprotocol", "chinese": ", // 请求路径\r\n                requestProtocol:", "english": ", // 请求路径\r\n                requestProtocol:", "file": "components\\ldpTable\\historyModifyList.vue"}, {"key": "texttexttexttextRequesttime", "chinese": ", // 请求协议\r\n                requestTime:", "english": ", // 请求协议\r\n                requestTime:", "file": "components\\ldpTable\\historyModifyList.vue"}, {"key": "texttexttimeRequestparams", "chinese": ", // 请求时间\r\n                requestParams:", "english": ", // 请求时间\r\n                requestParams:", "file": "components\\ldpTable\\historyModifyList.vue"}, {"key": "texttexttexttextResponse", "chinese": ", // 请求参数\r\n                response:", "english": ", // 请求参数\r\n                response:", "file": "components\\ldpTable\\historyModifyList.vue"}, {"key": "texttextdetailfailed", "chinese": "查看详情失败", "english": "查看详情失败", "file": "components\\ldpTable\\historyModifyList.vue"}, {"key": "texttexttext", "chinese": "目标表:", "english": "目标表:", "file": "components\\ldpTable\\historyModifyList.vue"}, {"key": "texttexteditoperation", "chinese": "批量修改操作:", "english": "批量修改操作:", "file": "components\\ldpTable\\historyModifyList.vue"}, {"key": "texttextdatatexttext", "chinese": "影响数据条数:", "english": "影响数据条数:", "file": "components\\ldpTable\\historyModifyList.vue"}, {"key": "texttexttexttexttexttext", "chinese": "影响字段个数:", "english": "影响字段个数:", "file": "components\\ldpTable\\historyModifyList.vue"}, {"key": "texttexttext", "chinese": "用户名:", "english": "用户名:", "file": "components\\ldpTable\\historyModifyList.vue"}, {"key": "texttexttexttext", "chinese": "用户角色:", "english": "用户角色:", "file": "components\\ldpTable\\historyModifyList.vue"}, {"key": "edittextsubmittime", "chinese": "修改单提交时间:", "english": "修改单提交时间:", "file": "components\\ldpTable\\historyModifyList.vue"}, {"key": "edittextstatus", "chinese": "修改单状态:", "english": "修改单状态:", "file": "components\\ldpTable\\historyModifyList.vue"}, {"key": "editsuccesstext", "chinese": "修改成功数:", "english": "修改成功数:", "file": "components\\ldpTable\\historyModifyList.vue"}, {"key": "editfailedtext", "chinese": "修改失败数:", "english": "修改失败数:", "file": "components\\ldpTable\\historyModifyList.vue"}, {"key": "editstarttime", "chinese": "修改开始时间:", "english": "修改开始时间:", "file": "components\\ldpTable\\historyModifyList.vue"}, {"key": "editendtime", "chinese": "修改结束时间:", "english": "修改结束时间:", "file": "components\\ldpTable\\historyModifyList.vue"}, {"key": "edittexttexttext", "chinese": "修改总耗时:", "english": "修改总耗时:", "file": "components\\ldpTable\\historyModifyList.vue"}], "components\\ldpTable\\selectBox.vue": [{"key": "texttexttexttexttext", "chinese": "内存表属性", "english": "内存表属性", "file": "components\\ldpTable\\selectBox.vue"}, {"key": "texttexttexttextfailed", "chinese": "表单验证失败!", "english": "表单验证失败!", "file": "components\\ldpTable\\selectBox.vue"}], "components\\ldpTable\\tableManageTop.vue": [{"key": "texttextdatatextmanage", "chinese": "内存数据表管理", "english": "内存数据表管理", "file": "components\\ldpTable\\tableManageTop.vue"}, {"key": "texttext", "chinese": "断开", "english": "断开", "file": "components\\ldpTable\\tableManageTop.vue"}, {"key": "texttext", "chinese": "连接", "english": "连接", "file": "components\\ldpTable\\tableManageTop.vue"}, {"key": "pleaseselecttexttexttexttexttexttexttexttexttexttext", "chinese": "请选择连接的产品及应用节点", "english": "请选择连接的产品及应用节点", "file": "components\\ldpTable\\tableManageTop.vue"}, {"key": "linkbtnTexttextTexttext", "chinese": "{{ linkbtn ? '断开' : '连接' }}", "english": "{{ linkbtn ? '断开' : '连接' }}", "file": "components\\ldpTable\\tableManageTop.vue"}], "components\\ldpTable\\tableModify.vue": [{"key": "texttexttexttextDivClass", "chinese": ">\r\n        <!-- 筛选部分 -->\r\n        <div class=", "english": ">\r\n        <!-- 筛选部分 -->\r\n        <div class=", "file": "components\\ldpTable\\tableModify.vue"}, {"key": "texttextedittext", "chinese": "历史修改单", "english": "历史修改单", "file": "components\\ldpTable\\tableModify.vue"}, {"key": "edittexttext", "chinese": "修改预览", "english": "修改预览", "file": "components\\ldpTable\\tableModify.vue"}, {"key": "boxinfotablenameTexteditoperationdetail", "chinese": "boxInfo.tableName+ ' 表修改操作详情'", "english": "boxInfo.tableName+ ' 表修改操作详情'", "file": "components\\ldpTable\\tableModify.vue"}, {"key": "texttexttexttext", "chinese": "原记录值", "english": "原记录值", "file": "components\\ldpTable\\tableModify.vue"}, {"key": "edittextlistqueryfailed", "chinese": "修改单列表查询失败!", "english": "修改单列表查询失败!", "file": "components\\ldpTable\\tableModify.vue"}, {"key": "texttextoperationfailed", "chinese": "清空操作失败!", "english": "清空操作失败!", "file": "components\\ldpTable\\tableModify.vue"}, {"key": "edittexttextqueryfailed", "chinese": "修改预览查询失败!", "english": "修改预览查询失败!", "file": "components\\ldpTable\\tableModify.vue"}, {"key": "texttextedittexttexttextdeletesuccess", "chinese": "预览修改单条目删除成功!", "english": "预览修改单条目删除成功!", "file": "components\\ldpTable\\tableModify.vue"}, {"key": "texttextedittexttexttextdeletefailed", "chinese": "预览修改单条目删除失败!", "english": "预览修改单条目删除失败!", "file": "components\\ldpTable\\tableModify.vue"}, {"key": "texttextedittexttexttexttexttexttext", "chinese": "预览修改单条目不存在!", "english": "预览修改单条目不存在!", "file": "components\\ldpTable\\tableModify.vue"}, {"key": "texttexteditfailed", "chinese": "执行修改失败!", "english": "执行修改失败!", "file": "components\\ldpTable\\tableModify.vue"}, {"key": "texttexttextdeleteparamsrowfieldnametexteditoperationtext", "chinese": "您确定删除\"${params.row.fieldName}\"的修改操作吗？", "english": "您确定删除\"${params.row.fieldName}\"的修改操作吗？", "file": "components\\ldpTable\\tableModify.vue"}, {"key": "texttexttexttexttexttexttextoperationtext", "chinese": "您确定执行清空操作吗？", "english": "您确定执行清空操作吗？", "file": "components\\ldpTable\\tableModify.vue"}, {"key": "texttextoperation", "chinese": "清空操作", "english": "清空操作", "file": "components\\ldpTable\\tableModify.vue"}, {"key": "submitedit", "chinese": "提交修改", "english": "提交修改", "file": "components\\ldpTable\\tableModify.vue"}], "components\\ldpTable\\tableQuery.vue": [{"key": "20texttext", "chinese": "20条/页", "english": "20条/页", "file": "components\\ldpTable\\tableQuery.vue"}, {"key": "30texttext", "chinese": "30条/页", "english": "30条/页", "file": "components\\ldpTable\\tableQuery.vue"}, {"key": "50texttext", "chinese": "50条/页", "english": "50条/页", "file": "components\\ldpTable\\tableQuery.vue"}], "components\\ldpTable\\tableStructure.vue": [{"key": "datatextinfo", "chinese": "数据表信息", "english": "数据表信息", "file": "components\\ldpTable\\tableStructure.vue"}, {"key": "datatexttexttext", "chinese": "数据表字段", "english": "数据表字段", "file": "components\\ldpTable\\tableStructure.vue"}, {"key": "datatexttext", "chinese": "数据类型", "english": "数据类型", "file": "components\\ldpTable\\tableStructure.vue"}, {"key": "texttexttextedit", "chinese": "是否可修改", "english": "是否可修改", "file": "components\\ldpTable\\tableStructure.vue"}, {"key": "querytexttexttexttext", "chinese": "查询结果可见", "english": "查询结果可见", "file": "components\\ldpTable\\tableStructure.vue"}, {"key": "texttexttexttext", "chinese": "全选字段", "english": "全选字段", "file": "components\\ldpTable\\tableStructure.vue"}, {"key": "texttexttextquerytexttext", "chinese": "可作为查询条件", "english": "可作为查询条件", "file": "components\\ldpTable\\tableStructure.vue"}, {"key": "datatexttext", "chinese": "数据表名：", "english": "数据表名：", "file": "components\\ldpTable\\tableStructure.vue"}, {"key": "datatexttexttexttexttext", "chinese": "数据表扩展属性：", "english": "数据表扩展属性：", "file": "components\\ldpTable\\tableStructure.vue"}, {"key": "datatexttexttext", "chinese": "数据表说明：", "english": "数据表说明：", "file": "components\\ldpTable\\tableStructure.vue"}, {"key": "texttextstructtexttexttexttexttexttextconfig", "chinese": "禁用struct字段可见属性配置", "english": "禁用struct字段可见属性配置", "file": "components\\ldpTable\\tableStructure.vue"}], "components\\locateConfig\\diffResult.vue": [{"key": "configtexttext", "chinese": "配置对比", "english": "配置对比", "file": "components\\locateConfig\\diffResult.vue"}, {"key": "configtexttexttexttext", "chinese": "配置文件类型", "english": "配置文件类型", "file": "components\\locateConfig\\diffResult.vue"}, {"key": "texttexttext", "chinese": "源节点", "english": "源节点", "file": "components\\locateConfig\\diffResult.vue"}, {"key": "texttexttexttext", "chinese": "目标节点", "english": "目标节点", "file": "components\\locateConfig\\diffResult.vue"}, {"key": "texttexttexttext", "chinese": "是否一致", "english": "是否一致", "file": "components\\locateConfig\\diffResult.vue"}, {"key": "texttexttexttext", "chinese": "一键修正", "english": "一键修正", "file": "components\\locateConfig\\diffResult.vue"}], "components\\locateConfig\\locateConfigManage.vue": [{"key": "texttexttexttextconfiglistDivClass", "chinese": ">\r\n        <!-- 定位节点配置列表 -->\r\n        <div class=", "english": ">\r\n        <!-- 定位节点配置列表 -->\r\n        <div class=", "file": "components\\locateConfig\\locateConfigManage.vue"}, {"key": "saveabuttonAbuttonVif", "chinese": ">保存</a-button>\r\n                <a-button v-if=", "english": ">保存</a-button>\r\n                <a-button v-if=", "file": "components\\locateConfig\\locateConfigManage.vue"}, {"key": "texttexttexttextconfiglist", "chinese": "定位节点配置列表", "english": "定位节点配置列表", "file": "components\\locateConfig\\locateConfigManage.vue"}, {"key": "texttexttexttexttexttextconfig", "chinese": "节点定位插件配置", "english": "节点定位插件配置", "file": "components\\locateConfig\\locateConfigManage.vue"}, {"key": "texttexttexttexttexttextconfig", "chinese": "节点定位规则配置", "english": "节点定位规则配置", "file": "components\\locateConfig\\locateConfigManage.vue"}, {"key": "configtexttexttexttext", "chinese": "配置对象类型", "english": "配置对象类型", "file": "components\\locateConfig\\locateConfigManage.vue"}, {"key": "configtexttext", "chinese": "配置对象", "english": "配置对象", "file": "components\\locateConfig\\locateConfigManage.vue"}, {"key": "texttext", "chinese": "在线", "english": "在线", "file": "components\\locateConfig\\locateConfigManage.vue"}, {"key": "texttext", "chinese": "离线", "english": "离线", "file": "components\\locateConfig\\locateConfigManage.vue"}, {"key": "texttextconfig", "chinese": "对比配置", "english": "对比配置", "file": "components\\locateConfig\\locateConfigManage.vue"}, {"key": "texttexttexttexttexttext16mb", "chinese": "文件大小超出16MB", "english": "文件大小超出16MB", "file": "components\\locateConfig\\locateConfigManage.vue"}, {"key": "configtexttexttexttextLocallanguage", "chinese": "配置文件格式：{{ localLanguage || '-' }}", "english": "配置文件格式：{{ localLanguage || '-' }}", "file": "components\\locateConfig\\locateConfigManage.vue"}, {"key": "save", "chinese": "保存", "english": "保存", "file": "components\\locateConfig\\locateConfigManage.vue"}, {"key": "texttext", "chinese": "同步", "english": "同步", "file": "components\\locateConfig\\locateConfigManage.vue"}], "components\\locateConfig\\nodeDiff.vue": [{"key": "texttexttexttexttextSourcediffsourcenode", "chinese": "`对比源节点: ${sourceDiff.sourceNode || '-'}`", "english": "`对比源节点: ${sourceDiff.sourceNode || '-'}`", "file": "components\\locateConfig\\nodeDiff.vue"}, {"key": "texttexttexttexttexttextTargetdifftargetnode", "chinese": "`对比目标节点: ${targetDiff.targetNode || '-'}`", "english": "`对比目标节点: ${targetDiff.targetNode || '-'}`", "file": "components\\locateConfig\\nodeDiff.vue"}, {"key": "texttexttexttexttextSourcediffsourcenode", "chinese": "对比源节点: ${sourceDiff.sourceNode || '-'}", "english": "对比源节点: ${sourceDiff.sourceNode || '-'}", "file": "components\\locateConfig\\nodeDiff.vue"}, {"key": "texttexttexttexttexttextTargetdifftargetnode", "chinese": "对比目标节点: ${targetDiff.targetNode || '-'}", "english": "对比目标节点: ${targetDiff.targetNode || '-'}", "file": "components\\locateConfig\\nodeDiff.vue"}, {"key": "configtexttexttexttextLanguage", "chinese": "配置文件格式：{{ language || '-' }}", "english": "配置文件格式：{{ language || '-' }}", "file": "components\\locateConfig\\nodeDiff.vue"}, {"key": "configtexttextSourcediffsourcepath", "chinese": "配置路径：{{ sourceDiff.sourcePath || '-' }}", "english": "配置路径：{{ sourceDiff.sourcePath || '-' }}", "file": "components\\locateConfig\\nodeDiff.vue"}, {"key": "configtexttextTargetdifftargetpath", "chinese": "配置路径：{{ targetDiff.targetPath || '-' }}", "english": "配置路径：{{ targetDiff.targetPath || '-' }}", "file": "components\\locateConfig\\nodeDiff.vue"}], "components\\locateConfig\\selectDiffModal.vue": [{"key": "texttexttexttextconfigtexttexttext", "chinese": "选择对比配置的节点", "english": "选择对比配置的节点", "file": "components\\locateConfig\\selectDiffModal.vue"}, {"key": "configtexttext", "chinese": "配置类型", "english": "配置类型", "file": "components\\locateConfig\\selectDiffModal.vue"}, {"key": "texttextconfigtexttext", "chinese": "原始配置来源", "english": "原始配置来源", "file": "components\\locateConfig\\selectDiffModal.vue"}, {"key": "texttexttexttexttext", "chinese": "对比源节点", "english": "对比源节点", "file": "components\\locateConfig\\selectDiffModal.vue"}, {"key": "texttexttexttexttexttext", "chinese": "对比目标节点", "english": "对比目标节点", "file": "components\\locateConfig\\selectDiffModal.vue"}, {"key": "texttexttexttextar", "chinese": "节点定位AR", "english": "节点定位AR", "file": "components\\locateConfig\\selectDiffModal.vue"}], "components\\managementQuery\\appConfigInfoDrawer.vue": [{"key": "texttextinfo", "chinese": "基本信息", "english": "基本信息", "file": "components\\managementQuery\\appConfigInfoDrawer.vue"}, {"key": "functionnamespanPClass", "chinese": ">功能名称：</span>\r\n            <p class=", "english": ">功能名称：</span>\r\n            <p class=", "file": "components\\managementQuery\\appConfigInfoDrawer.vue"}, {"key": "functiontexttextspanPClass", "chinese": ">功能备注：</span>\r\n            <p class=", "english": ">功能备注：</span>\r\n            <p class=", "file": "components\\managementQuery\\appConfigInfoDrawer.vue"}, {"key": "texttexttextspanPClass", "chinese": ">版本号：</span>\r\n            <p class=", "english": ">版本号：</span>\r\n            <p class=", "file": "components\\managementQuery\\appConfigInfoDrawer.vue"}, {"key": "texttexttimespanPClass", "chinese": ">更新时间：</span>\r\n            <p class=", "english": ">更新时间：</span>\r\n            <p class=", "file": "components\\managementQuery\\appConfigInfoDrawer.vue"}, {"key": "texttexttextspanPClass", "chinese": ">提供者：</span>\r\n            <p class=", "english": ">提供者：</span>\r\n            <p class=", "file": "components\\managementQuery\\appConfigInfoDrawer.vue"}, {"key": "functiontexttextspanPClass", "chinese": ">功能说明：</span>\r\n            <p class=", "english": ">功能说明：</span>\r\n            <p class=", "file": "components\\managementQuery\\appConfigInfoDrawer.vue"}, {"key": "texttexttexttext", "chinese": "入参说明", "english": "入参说明", "file": "components\\managementQuery\\appConfigInfoDrawer.vue"}, {"key": "texttexttexttext", "chinese": "出参说明", "english": "出参说明", "file": "components\\managementQuery\\appConfigInfoDrawer.vue"}, {"key": "texttexttexttext", "chinese": "案例说明", "english": "案例说明", "file": "components\\managementQuery\\appConfigInfoDrawer.vue"}, {"key": "texttexttexttextdivDivClass", "chinese": ">入参案例</div>\r\n        <div class=", "english": ">入参案例</div>\r\n        <div class=", "file": "components\\managementQuery\\appConfigInfoDrawer.vue"}, {"key": "texttexttexttextdivDivClass", "chinese": ">出参案例</div>\r\n        <div class=", "english": ">出参案例</div>\r\n        <div class=", "file": "components\\managementQuery\\appConfigInfoDrawer.vue"}, {"key": "texttextname", "chinese": "入参名称", "english": "入参名称", "file": "components\\managementQuery\\appConfigInfoDrawer.vue"}, {"key": "texttexttexttext", "chinese": "参数类型", "english": "参数类型", "file": "components\\managementQuery\\appConfigInfoDrawer.vue"}, {"key": "texttexttexttext", "chinese": "参数说明", "english": "参数说明", "file": "components\\managementQuery\\appConfigInfoDrawer.vue"}, {"key": "texttextname", "chinese": "出参名称", "english": "出参名称", "file": "components\\managementQuery\\appConfigInfoDrawer.vue"}, {"key": "functionname", "chinese": "功能名称：", "english": "功能名称：", "file": "components\\managementQuery\\appConfigInfoDrawer.vue"}, {"key": "functiontexttext", "chinese": "功能备注：", "english": "功能备注：", "file": "components\\managementQuery\\appConfigInfoDrawer.vue"}, {"key": "texttexttext", "chinese": "版本号：", "english": "版本号：", "file": "components\\managementQuery\\appConfigInfoDrawer.vue"}, {"key": "texttexttime", "chinese": "更新时间：", "english": "更新时间：", "file": "components\\managementQuery\\appConfigInfoDrawer.vue"}, {"key": "texttexttext", "chinese": "提供者：", "english": "提供者：", "file": "components\\managementQuery\\appConfigInfoDrawer.vue"}, {"key": "functiontexttext", "chinese": "功能说明：", "english": "功能说明：", "file": "components\\managementQuery\\appConfigInfoDrawer.vue"}, {"key": "texttexttexttext", "chinese": "入参案例", "english": "入参案例", "file": "components\\managementQuery\\appConfigInfoDrawer.vue"}, {"key": "texttexttexttext", "chinese": "出参案例", "english": "出参案例", "file": "components\\managementQuery\\appConfigInfoDrawer.vue"}], "components\\managementQuery\\batchExportModal.vue": [{"key": "pleaseinputmanagefunctionnamesearch", "chinese": "请输入管理功能名称搜索", "english": "请输入管理功能名称搜索", "file": "components\\managementQuery\\batchExportModal.vue"}, {"key": "managefunction", "chinese": "管理功能", "english": "管理功能", "file": "components\\managementQuery\\batchExportModal.vue"}, {"key": "texttext", "chinese": "移除", "english": "移除", "file": "components\\managementQuery\\batchExportModal.vue"}, {"key": "texttextexport", "chinese": "正在导出", "english": "正在导出", "file": "components\\managementQuery\\batchExportModal.vue"}, {"key": "exporttexttext", "chinese": "导出停止", "english": "导出停止", "file": "components\\managementQuery\\batchExportModal.vue"}, {"key": "texttextfailed", "chinese": "终止失败", "english": "终止失败", "file": "components\\managementQuery\\batchExportModal.vue"}, {"key": "exporttexttexttexttext", "chinese": "导出终止异常", "english": "导出终止异常", "file": "components\\managementQuery\\batchExportModal.vue"}, {"key": "exporttexttexttexttextsuccesstexttexttext", "chinese": "导出任务启动成功，请稍候", "english": "导出任务启动成功，请稍候", "file": "components\\managementQuery\\batchExportModal.vue"}, {"key": "exporttexttexttexttextfailed", "chinese": "导出任务启动失败", "english": "导出任务启动失败", "file": "components\\managementQuery\\batchExportModal.vue"}, {"key": "searchmanagefunctionLeftdatalength", "chinese": "搜索管理功能 ({{ leftData.length }})", "english": "搜索管理功能 ({{ leftData.length }})", "file": "components\\managementQuery\\batchExportModal.vue"}, {"key": "textexportlistRightdatalength", "chinese": "待导出列表 ({{ rightData.length }})", "english": "待导出列表 ({{ rightData.length }})", "file": "components\\managementQuery\\batchExportModal.vue"}, {"key": "texttext", "chinese": "原因", "english": "原因", "file": "components\\managementQuery\\batchExportModal.vue"}, {"key": "texttextlist", "chinese": "清空列表", "english": "清空列表", "file": "components\\managementQuery\\batchExportModal.vue"}], "components\\managementQuery\\configDataDrawer.vue": [{"key": "table", "chinese": "表格", "english": "表格", "file": "components\\managementQuery\\configDataDrawer.vue"}, {"key": "functionconfig", "chinese": "功能配置", "english": "功能配置", "file": "components\\managementQuery\\configDataDrawer.vue"}, {"key": "texttexttexttexttexttextNbsp", "chinese": "结果展示方式\r\n      &nbsp;", "english": "结果展示方式\r\n      &nbsp;", "file": "components\\managementQuery\\configDataDrawer.vue"}, {"key": "texttexttexttexttabtexttext", "chinese": "最大展示Tab个数", "english": "最大展示Tab个数", "file": "components\\managementQuery\\configDataDrawer.vue"}, {"key": "texttextconfig", "chinese": "交互配置", "english": "交互配置", "file": "components\\managementQuery\\configDataDrawer.vue"}, {"key": "texttexttexttexttexttexttextfunctionlisttexttext", "chinese": "只保留激活插件功能列表展开", "english": "只保留激活插件功能列表展开", "file": "components\\managementQuery\\configDataDrawer.vue"}, {"key": "texttexttexttexttexttexttexttexttexttext", "chinese": "记录最后一次输入参数", "english": "记录最后一次输入参数", "file": "components\\managementQuery\\configDataDrawer.vue"}, {"key": "texttexttexttextmanagefunctiontexttext", "chinese": "结果页与管理功能联动", "english": "结果页与管理功能联动", "file": "components\\managementQuery\\configDataDrawer.vue"}, {"key": "texttextgettexttextfunctiontexttexttexttexttexttexttexttexttext", "chinese": "单击Get类型功能菜单时自动发起请求", "english": "单击Get类型功能菜单时自动发起请求", "file": "components\\managementQuery\\configDataDrawer.vue"}], "components\\managementQuery\\exportDataModal.vue": [{"key": "managefunctionexport", "chinese": "管理功能导出", "english": "管理功能导出", "file": "components\\managementQuery\\exportDataModal.vue"}, {"key": "texttexttexttexttexttexttexttextexporttextmanagefunction", "chinese": "根据需要，选择想要导出的管理功能。", "english": "根据需要，选择想要导出的管理功能。", "file": "components\\managementQuery\\exportDataModal.vue"}, {"key": "texttext", "chinese": "入参", "english": "入参", "file": "components\\managementQuery\\exportDataModal.vue"}], "components\\managementQuery\\jsonPathDrawer.vue": [{"key": "configModaldatatitle", "chinese": "`配置（`+ modalData.title + ')'", "english": "`配置（`+ modalData.title + ')'", "file": "components\\managementQuery\\jsonPathDrawer.vue"}, {"key": "saveconfigabuttonAbuttonType", "chinese": ">保存配置</a-button\r\n      >\r\n      <a-button type=", "english": ">保存配置</a-button\r\n      >\r\n      <a-button type=", "file": "components\\managementQuery\\jsonPathDrawer.vue"}, {"key": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "chinese": "请输入jsonPath", "english": "请输入jsonPath", "file": "components\\managementQuery\\jsonPathDrawer.vue"}, {"key": "texttext", "chinese": "别名", "english": "别名", "file": "components\\managementQuery\\jsonPathDrawer.vue"}, {"key": "tabletexttextname", "chinese": "表格展示名称", "english": "表格展示名称", "file": "components\\managementQuery\\jsonPathDrawer.vue"}, {"key": "texttexttexttexttexttext", "chinese": "是否支持排序", "english": "是否支持排序", "file": "components\\managementQuery\\jsonPathDrawer.vue"}, {"key": "tabletexttexttexttexttexttexttexttexttexttexttexttexttexttexttexttexttexttexttext", "chinese": "表格字段支持行排序(仅当表格为一维数组时生效)", "english": "表格字段支持行排序(仅当表格为一维数组时生效)", "file": "components\\managementQuery\\jsonPathDrawer.vue"}, {"key": "config", "chinese": "配置（", "english": "配置（", "file": "components\\managementQuery\\jsonPathDrawer.vue"}], "components\\managementQuery\\JsonPathTable.vue": [{"key": "text", "chinese": "值", "english": "值", "file": "components\\managementQuery\\JsonPathTable.vue"}, {"key": "texttextdatatexttexttext", "chinese": "处理数据时出错:", "english": "处理数据时出错:", "file": "components\\managementQuery\\JsonPathTable.vue"}], "components\\managementQuery\\managementBox.vue": [{"key": "managefunctiontexttexttexttext", "chinese": "管理功能使用说明", "english": "管理功能使用说明", "file": "components\\managementQuery\\managementBox.vue"}, {"key": "texttext", "chinese": "前置", "english": "前置", "file": "components\\managementQuery\\managementBox.vue"}, {"key": "texttext", "chinese": "回库", "english": "回库", "file": "components\\managementQuery\\managementBox.vue"}, {"key": "placeholderpleaseinputDisabledClass", "chinese": "placeholder=\"请输入\" disabled :class=\"[", "english": "placeholder=\"请输入\" disabled :class=\"[", "file": "components\\managementQuery\\managementBox.vue"}], "components\\marketAllLink\\allLinkAnalyseConfig.vue": [{"key": "texttexttexttext", "chinese": "传输链路", "english": "传输链路", "file": "components\\marketAllLink\\allLinkAnalyseConfig.vue"}, {"key": "texttexttexttextdivHformitemLabel", "chinese": ">分析指标</div>\r\n                <h-form-item label=", "english": ">分析指标</div>\r\n                <h-form-item label=", "file": "components\\marketAllLink\\allLinkAnalyseConfig.vue"}, {"key": "datatexttextdivHformitemLabel", "chinese": ">数据范围</div>\r\n                <h-form-item label=", "english": ">数据范围</div>\r\n                <h-form-item label=", "file": "components\\marketAllLink\\allLinkAnalyseConfig.vue"}, {"key": "texttexttexttext", "chinese": "统计范围", "english": "统计范围", "file": "components\\marketAllLink\\allLinkAnalyseConfig.vue"}, {"key": "texttexttexttexttext", "chinese": "自定义范围", "english": "自定义范围", "file": "components\\marketAllLink\\allLinkAnalyseConfig.vue"}, {"key": "spansTexttexttexttext", "chinese": ",\r\n                spans: [ // 链路指标", "english": ",\r\n                spans: [ // 链路指标", "file": "components\\marketAllLink\\allLinkAnalyseConfig.vue"}, {"key": "95texttexttext", "chinese": "95%分位数", "english": "95%分位数", "file": "components\\marketAllLink\\allLinkAnalyseConfig.vue"}, {"key": "5texttexttext", "chinese": "5%分位数", "english": "5%分位数", "file": "components\\marketAllLink\\allLinkAnalyseConfig.vue"}, {"key": "texttexttexttext", "chinese": "分析指标", "english": "分析指标", "file": "components\\marketAllLink\\allLinkAnalyseConfig.vue"}, {"key": "submit", "chinese": "提交", "english": "提交", "file": "components\\marketAllLink\\allLinkAnalyseConfig.vue"}], "components\\marketAllLink\\marketChart.vue": [{"key": "texttexttexttexttexttexttexttexttexttexttexttext", "chinese": "当前行情系统暂无时延走势", "english": "当前行情系统暂无时延走势", "file": "components\\marketAllLink\\marketChart.vue"}, {"key": "texttexttexttexttexttexttexttexttexttexttext", "chinese": "秒级系统穿透时延标准差", "english": "秒级系统穿透时延标准差", "file": "components\\marketAllLink\\marketChart.vue"}, {"key": "texttexttexttexttexttextpPClass", "chinese": ">系统抖动范围</p>\r\n                            <p class=", "english": ">系统抖动范围</p>\r\n                            <p class=", "file": "components\\marketAllLink\\marketChart.vue"}, {"key": "texttexttexttexttextpPClass", "chinese": ">时延正常率</p>\r\n                        <p class=", "english": ">时延正常率</p>\r\n                        <p class=", "file": "components\\marketAllLink\\marketChart.vue"}, {"key": "95texttexttext", "chinese": "95分位数", "english": "95分位数", "file": "components\\marketAllLink\\marketChart.vue"}, {"key": "5texttexttext", "chinese": "5分位数", "english": "5分位数", "file": "components\\marketAllLink\\marketChart.vue"}, {"key": "texttexttexttexttexttext", "chinese": "系统抖动范围", "english": "系统抖动范围", "file": "components\\marketAllLink\\marketChart.vue"}, {"key": "texttexttexttext", "chinese": "快照行情", "english": "快照行情", "file": "components\\marketAllLink\\marketChart.vue"}, {"key": "texttexttexttext", "chinese": "指数行情", "english": "指数行情", "file": "components\\marketAllLink\\marketChart.vue"}, {"key": "texttexttexttext", "chinese": "逐笔委托", "english": "逐笔委托", "file": "components\\marketAllLink\\marketChart.vue"}, {"key": "texttexttexttext", "chinese": "逐笔成交", "english": "逐笔成交", "file": "components\\marketAllLink\\marketChart.vue"}, {"key": "texttextnameLinkname", "chinese": ",        // 链路名称\r\n                    linkName:", "english": ",        // 链路名称\r\n                    linkName:", "file": "components\\marketAllLink\\marketChart.vue"}, {"key": "texttexttexttext", "chinese": ", // 抖动范围", "english": ", // 抖动范围", "file": "components\\marketAllLink\\marketChart.vue"}, {"key": "thisspandatanamektext", "chinese": "${this.spanData.name}-K线", "english": "${this.spanData.name}-K线", "file": "components\\marketAllLink\\marketChart.vue"}, {"key": "nametexttextns", "chinese": "${name}走势(ns)", "english": "${name}走势(ns)", "file": "components\\marketAllLink\\marketChart.vue"}, {"key": "lasttimedatatimeTexttexttexttexttexttexttext", "chinese": "{{ lastTimeData.time }} 系统全链路时延", "english": "{{ lastTimeData.time }} 系统全链路时延", "file": "components\\marketAllLink\\marketChart.vue"}, {"key": "texttexttext", "chinese": "最大值:", "english": "最大值:", "file": "components\\marketAllLink\\marketChart.vue"}, {"key": "95texttexttext", "chinese": "95分位数:", "english": "95分位数:", "file": "components\\marketAllLink\\marketChart.vue"}, {"key": "5texttexttext", "chinese": "5分位数:", "english": "5分位数:", "file": "components\\marketAllLink\\marketChart.vue"}, {"key": "texttexttext", "chinese": "最小值:", "english": "最小值:", "file": "components\\marketAllLink\\marketChart.vue"}], "components\\marketAllLink\\suspend.vue": [{"key": "texttextstatusspanSpanClass", "chinese": ">应用状态：</span>\r\n                    <span class=", "english": ">应用状态：</span>\r\n                    <span class=", "file": "components\\marketAllLink\\suspend.vue"}, {"key": "texttexttexttexttextpPVfor", "chinese": ">时延正常率</p>\r\n            <p v-for=", "english": ">时延正常率</p>\r\n            <p v-for=", "file": "components\\marketAllLink\\suspend.vue"}, {"key": "texttextstatus", "chinese": "应用状态：", "english": "应用状态：", "file": "components\\marketAllLink\\suspend.vue"}], "components\\mcDataObservation\\backtrackQuery.vue": [{"key": "texttexttext", "chinese": "主题名", "english": "主题名", "file": "components\\mcDataObservation\\backtrackQuery.vue"}, {"key": "pleaseselecttexttexttext", "chinese": "请选择主题名", "english": "请选择主题名", "file": "components\\mcDataObservation\\backtrackQuery.vue"}, {"key": "texttext", "chinese": "分区", "english": "分区", "file": "components\\mcDataObservation\\backtrackQuery.vue"}, {"key": "pleaseselecttexttext", "chinese": "请选择分区", "english": "请选择分区", "file": "components\\mcDataObservation\\backtrackQuery.vue"}, {"key": "querytexttext", "chinese": "查询方式", "english": "查询方式", "file": "components\\mcDataObservation\\backtrackQuery.vue"}, {"key": "pleaseinput11000texttexttexttext", "chinese": "请输入1~1000的正整数", "english": "请输入1~1000的正整数", "file": "components\\mcDataObservation\\backtrackQuery.vue"}, {"key": "texttext", "chinese": "最近", "english": "最近", "file": "components\\mcDataObservation\\backtrackQuery.vue"}, {"key": "texttext", "chinese": "最早", "english": "最早", "file": "components\\mcDataObservation\\backtrackQuery.vue"}, {"key": "texttextidtexttext", "chinese": "消息ID范围", "english": "消息ID范围", "file": "components\\mcDataObservation\\backtrackQuery.vue"}, {"key": "texttexttext", "chinese": "分区号", "english": "分区号", "file": "components\\mcDataObservation\\backtrackQuery.vue"}, {"key": "texttextid", "chinese": "消息ID", "english": "消息ID", "file": "components\\mcDataObservation\\backtrackQuery.vue"}, {"key": "texttexttext", "chinese": "生产者", "english": "生产者", "file": "components\\mcDataObservation\\backtrackQuery.vue"}, {"key": "texttexttexttext", "chinese": "发布序号", "english": "发布序号", "file": "components\\mcDataObservation\\backtrackQuery.vue"}, {"key": "texttexttime", "chinese": "发布时间", "english": "发布时间", "file": "components\\mcDataObservation\\backtrackQuery.vue"}, {"key": "texttexttexttext", "chinese": "消息大小", "english": "消息大小", "file": "components\\mcDataObservation\\backtrackQuery.vue"}, {"key": "texttexttexttext", "chinese": "过滤条件", "english": "过滤条件", "file": "components\\mcDataObservation\\backtrackQuery.vue"}, {"key": "texttexttexttext", "chinese": "消息内容", "english": "消息内容", "file": "components\\mcDataObservation\\backtrackQuery.vue"}], "components\\mcDataObservation\\consumeBacklog.vue": [{"key": "texttext", "chinese": "或者", "english": "或者", "file": "components\\mcDataObservation\\consumeBacklog.vue"}, {"key": "texttexttexttexttexttexttexttexttext", "chinese": "消费者集群消息处理", "english": "消费者集群消息处理", "file": "components\\mcDataObservation\\consumeBacklog.vue"}, {"key": "texttexttexttexttext", "chinese": "消费者集群", "english": "消费者集群", "file": "components\\mcDataObservation\\consumeBacklog.vue"}, {"key": "texttexttext", "chinese": "实例名", "english": "实例名", "file": "components\\mcDataObservation\\consumeBacklog.vue"}, {"key": "texttexttexttexttext", "chinese": "消息积压数", "english": "消息积压数", "file": "components\\mcDataObservation\\consumeBacklog.vue"}, {"key": "texttexttexttexttexttext", "chinese": "消费消息序号", "english": "消费消息序号", "file": "components\\mcDataObservation\\consumeBacklog.vue"}, {"key": "texttexttexttexttexttext", "chinese": "最新消息序号", "english": "最新消息序号", "file": "components\\mcDataObservation\\consumeBacklog.vue"}, {"key": "texttexttexttext", "chinese": "调用次数", "english": "调用次数", "file": "components\\mcDataObservation\\consumeBacklog.vue"}, {"key": "texttexttexttexttexttexttexttexttexttexttext", "chinese": "当日客户端平均处理耗时", "english": "当日客户端平均处理耗时", "file": "components\\mcDataObservation\\consumeBacklog.vue"}, {"key": "texttexttexttexttexttexttexttexttexttexttext", "chinese": "当日客户端平均排队耗时", "english": "当日客户端平均排队耗时", "file": "components\\mcDataObservation\\consumeBacklog.vue"}, {"key": "texttexttexttexttexttexttexttexttexttexttext", "chinese": "当日客户端平均执行耗时", "english": "当日客户端平均执行耗时", "file": "components\\mcDataObservation\\consumeBacklog.vue"}, {"key": "texttexttexttexttexttexttexttexttexttexttext", "chinese": "当日客户端最大处理耗时", "english": "当日客户端最大处理耗时", "file": "components\\mcDataObservation\\consumeBacklog.vue"}, {"key": "texttexttexttexttexttexttexttexttexttexttext", "chinese": "当日客户端最大排队耗时", "english": "当日客户端最大排队耗时", "file": "components\\mcDataObservation\\consumeBacklog.vue"}, {"key": "texttexttexttexttexttexttexttexttexttexttext", "chinese": "当日客户端最大执行耗时", "english": "当日客户端最大执行耗时", "file": "components\\mcDataObservation\\consumeBacklog.vue"}, {"key": "texttexttexttexttexttexttexttexttexttexttext", "chinese": "当日客户端最小处理耗时", "english": "当日客户端最小处理耗时", "file": "components\\mcDataObservation\\consumeBacklog.vue"}, {"key": "texttexttexttexttexttexttexttexttexttexttext", "chinese": "当日客户端最小排队耗时", "english": "当日客户端最小排队耗时", "file": "components\\mcDataObservation\\consumeBacklog.vue"}, {"key": "texttexttexttexttexttexttexttexttexttexttext", "chinese": "当日客户端最小执行耗时", "english": "当日客户端最小执行耗时", "file": "components\\mcDataObservation\\consumeBacklog.vue"}, {"key": "texttexttexttexttexttexttexttext", "chinese": "消息处理变化趋势", "english": "消息处理变化趋势", "file": "components\\mcDataObservation\\consumeBacklog.vue"}, {"key": "texttext15texttext", "chinese": "最新15分钟", "english": "最新15分钟", "file": "components\\mcDataObservation\\consumeBacklog.vue"}, {"key": "texttext15texttext", "chinese": "历史15分钟", "english": "历史15分钟", "file": "components\\mcDataObservation\\consumeBacklog.vue"}, {"key": "text", "chinese": "个", "english": "个", "file": "components\\mcDataObservation\\consumeBacklog.vue"}, {"key": "texttexttext", "chinese": "数量(个)", "english": "数量(个)", "file": "components\\mcDataObservation\\consumeBacklog.vue"}, {"key": "texttext", "chinese": "最大", "english": "最大", "file": "components\\mcDataObservation\\consumeBacklog.vue"}, {"key": "texttext", "chinese": "最小", "english": "最小", "file": "components\\mcDataObservation\\consumeBacklog.vue"}, {"key": "texttexttexttexttexttexttexttexttext", "chinese": "当日客户端处理耗时", "english": "当日客户端处理耗时", "file": "components\\mcDataObservation\\consumeBacklog.vue"}, {"key": "texttexttexttexttexttexttexttexttext", "chinese": "当日客户端排队耗时", "english": "当日客户端排队耗时", "file": "components\\mcDataObservation\\consumeBacklog.vue"}, {"key": "texttexttexttexttexttexttexttexttext", "chinese": "当日客户端执行耗时", "english": "当日客户端执行耗时", "file": "components\\mcDataObservation\\consumeBacklog.vue"}, {"key": "texttext", "chinese": "耗时", "english": "耗时", "file": "components\\mcDataObservation\\consumeBacklog.vue"}], "components\\mcDataObservation\\deadLetterQueue.vue": [{"key": "texttexttexttext", "chinese": "死信队列", "english": "死信队列", "file": "components\\mcDataObservation\\deadLetterQueue.vue"}, {"key": "refresh", "chinese": "刷新", "english": "刷新", "file": "components\\mcDataObservation\\deadLetterQueue.vue"}, {"key": "texttexttexttext", "chinese": "死信主题", "english": "死信主题", "file": "components\\mcDataObservation\\deadLetterQueue.vue"}, {"key": "texttexttexttext", "chinese": "消息主题", "english": "消息主题", "file": "components\\mcDataObservation\\deadLetterQueue.vue"}, {"key": "texttexttexttext", "chinese": "消息分区", "english": "消息分区", "file": "components\\mcDataObservation\\deadLetterQueue.vue"}, {"key": "texttexttexttexttime", "chinese": "上次接收时间", "english": "上次接收时间", "file": "components\\mcDataObservation\\deadLetterQueue.vue"}, {"key": "texttexttexttexttext", "chinese": "死信消息数", "english": "死信消息数", "file": "components\\mcDataObservation\\deadLetterQueue.vue"}, {"key": "texttexttexttext", "chinese": "清空队列", "english": "清空队列", "file": "components\\mcDataObservation\\deadLetterQueue.vue"}], "components\\mcDataObservation\\mcCluster.vue": [{"key": "texttexttexttext", "chinese": "线程编号", "english": "线程编号", "file": "components\\mcDataObservation\\mcCluster.vue"}, {"key": "texttexttexttexttexttexttexttext", "chinese": "当前队列消息个数", "english": "当前队列消息个数", "file": "components\\mcDataObservation\\mcCluster.vue"}, {"key": "texttexttexttexttexttexttexttexttext", "chinese": "历史队列最大消息数", "english": "历史队列最大消息数", "file": "components\\mcDataObservation\\mcCluster.vue"}, {"key": "texttexttexttext", "chinese": "处理总数", "english": "处理总数", "file": "components\\mcDataObservation\\mcCluster.vue"}, {"key": "texttexttexttext", "chinese": "产品版本", "english": "产品版本", "file": "components\\mcDataObservation\\mcCluster.vue"}, {"key": "texttexttexttext", "chinese": "产品类型", "english": "产品类型", "file": "components\\mcDataObservation\\mcCluster.vue"}, {"key": "texttexttexttexttext", "chinese": "集群主机数", "english": "集群主机数", "file": "components\\mcDataObservation\\mcCluster.vue"}, {"key": "texttext", "chinese": "编号", "english": "编号", "file": "components\\mcDataObservation\\mcCluster.vue"}, {"key": "texttexttexttext", "chinese": "主机地址", "english": "主机地址", "file": "components\\mcDataObservation\\mcCluster.vue"}, {"key": "texttexttexttext", "chinese": "发布线程", "english": "发布线程", "file": "components\\mcDataObservation\\mcCluster.vue"}, {"key": "texttexttexttext", "chinese": "会话线程", "english": "会话线程", "file": "components\\mcDataObservation\\mcCluster.vue"}, {"key": "texttexttexttext", "chinese": "订阅线程", "english": "订阅线程", "file": "components\\mcDataObservation\\mcCluster.vue"}, {"key": "texttexttexttext", "chinese": "推送线程", "english": "推送线程", "file": "components\\mcDataObservation\\mcCluster.vue"}, {"key": "texttexttexttext", "chinese": "主题线程", "english": "主题线程", "file": "components\\mcDataObservation\\mcCluster.vue"}, {"key": "texttexttexttext", "chinese": "消息中心", "english": "消息中心", "file": "components\\mcDataObservation\\mcCluster.vue"}], "components\\mcDataObservation\\mcOverview.vue": [{"key": "texttexttexttexttext", "chinese": "全主题统计", "english": "全主题统计", "file": "components\\mcDataObservation\\mcOverview.vue"}, {"key": "texttexttexttexttext", "chinese": "单主题统计", "english": "单主题统计", "file": "components\\mcDataObservation\\mcOverview.vue"}, {"key": "texttexttext", "chinese": "节点数", "english": "节点数", "file": "components\\mcDataObservation\\mcOverview.vue"}, {"key": "texttexttext", "chinese": "会话数", "english": "会话数", "file": "components\\mcDataObservation\\mcOverview.vue"}, {"key": "texttexttexttext", "chinese": "主题总数", "english": "主题总数", "file": "components\\mcDataObservation\\mcOverview.vue"}, {"key": "texttexttexttexttexttext", "chinese": "持久化主题数", "english": "持久化主题数", "file": "components\\mcDataObservation\\mcOverview.vue"}, {"key": "texttexttexttext", "chinese": "分区总数", "english": "分区总数", "file": "components\\mcDataObservation\\mcOverview.vue"}, {"key": "texttexttexttexttexttext", "chinese": "分区副本总数", "english": "分区副本总数", "file": "components\\mcDataObservation\\mcOverview.vue"}, {"key": "texttexttexttexttext", "chinese": "生产者总数", "english": "生产者总数", "file": "components\\mcDataObservation\\mcOverview.vue"}, {"key": "texttexttexttexttexttext", "chinese": "生产消息总数", "english": "生产消息总数", "file": "components\\mcDataObservation\\mcOverview.vue"}, {"key": "texttexttext", "chinese": "消费者", "english": "消费者", "file": "components\\mcDataObservation\\mcOverview.vue"}, {"key": "texttexttexttexttext", "chinese": "消费者总数", "english": "消费者总数", "file": "components\\mcDataObservation\\mcOverview.vue"}, {"key": "texttexttexttexttext", "chinese": "订阅项总数", "english": "订阅项总数", "file": "components\\mcDataObservation\\mcOverview.vue"}, {"key": "texttexttexttexttexttext", "chinese": "推送消息总数", "english": "推送消息总数", "file": "components\\mcDataObservation\\mcOverview.vue"}, {"key": "texttexttexttexttexttexttexttexttexttexttexttext", "chinese": "集群线程队列消息变化趋势", "english": "集群线程队列消息变化趋势", "file": "components\\mcDataObservation\\mcOverview.vue"}, {"key": "texttexttexttexttexttexttexttexttexttexttexttext", "chinese": "主题生产消费消息变化趋势", "english": "主题生产消费消息变化趋势", "file": "components\\mcDataObservation\\mcOverview.vue"}, {"key": "texttexttexttexttexttexttexttext", "chinese": "发布消息变化差量", "english": "发布消息变化差量", "file": "components\\mcDataObservation\\mcOverview.vue"}, {"key": "texttexttexttexttexttexttexttext", "chinese": "订阅消息变化差量", "english": "订阅消息变化差量", "file": "components\\mcDataObservation\\mcOverview.vue"}, {"key": "texttexttexttext", "chinese": "消息数(个)", "english": "消息数(个)", "file": "components\\mcDataObservation\\mcOverview.vue"}, {"key": "pleaseselecttexttext", "chinese": "请选择主题", "english": "请选择主题", "file": "components\\mcDataObservation\\mcOverview.vue"}, {"key": "pleaseselecttexttexttext", "chinese": "请选择生产者", "english": "请选择生产者", "file": "components\\mcDataObservation\\mcOverview.vue"}, {"key": "pleaseselecttexttexttext", "chinese": "请选择消费者", "english": "请选择消费者", "file": "components\\mcDataObservation\\mcOverview.vue"}, {"key": "texttexttexttexttextBrokertype", "chinese": ", // 集群下拉框\r\n            brokerType:", "english": ", // 集群下拉框\r\n            brokerType:", "file": "components\\mcDataObservation\\mcOverview.vue"}], "components\\mcDataObservation\\mcPublish.vue": [{"key": "texttexttexttexttext", "chinese": "发布主题数", "english": "发布主题数", "file": "components\\mcDataObservation\\mcPublish.vue"}, {"key": "texttexttexttexttext", "chinese": "发布分区数", "english": "发布分区数", "file": "components\\mcDataObservation\\mcPublish.vue"}, {"key": "texttexttexttexttext", "chinese": "生产消息数", "english": "生产消息数", "file": "components\\mcDataObservation\\mcPublish.vue"}, {"key": "texttexttextinfo", "chinese": "生产者信息", "english": "生产者信息", "file": "components\\mcDataObservation\\mcPublish.vue"}], "components\\mcDataObservation\\mcSubscribe.vue": [{"key": "texttexttexttext", "chinese": "订阅项数", "english": "订阅项数", "file": "components\\mcDataObservation\\mcSubscribe.vue"}, {"key": "texttexttexttexttext", "chinese": "订阅主题数", "english": "订阅主题数", "file": "components\\mcDataObservation\\mcSubscribe.vue"}, {"key": "texttexttexttexttext", "chinese": "推送消息数", "english": "推送消息数", "file": "components\\mcDataObservation\\mcSubscribe.vue"}, {"key": "texttexttexttexttext", "chinese": "补缺消息数", "english": "补缺消息数", "file": "components\\mcDataObservation\\mcSubscribe.vue"}, {"key": "texttexttext", "chinese": "客户端", "english": "客户端", "file": "components\\mcDataObservation\\mcSubscribe.vue"}, {"key": "texttexttexttext", "chinese": "订阅编号", "english": "订阅编号", "file": "components\\mcDataObservation\\mcSubscribe.vue"}, {"key": "texttexttime", "chinese": "订阅时间", "english": "订阅时间", "file": "components\\mcDataObservation\\mcSubscribe.vue"}, {"key": "texttexttexttext", "chinese": "订阅方式", "english": "订阅方式", "file": "components\\mcDataObservation\\mcSubscribe.vue"}, {"key": "texttexttexttext", "chinese": "广播消费", "english": "广播消费", "file": "components\\mcDataObservation\\mcSubscribe.vue"}, {"key": "texttexttexttext", "chinese": "集群消费", "english": "集群消费", "file": "components\\mcDataObservation\\mcSubscribe.vue"}, {"key": "texttexttexttext", "chinese": "订阅主题", "english": "订阅主题", "file": "components\\mcDataObservation\\mcSubscribe.vue"}, {"key": "texttexttextinfo", "chinese": "消费者信息", "english": "消费者信息", "file": "components\\mcDataObservation\\mcSubscribe.vue"}], "components\\mcDataObservation\\mcTopic.vue": [{"key": "texttextinfo", "chinese": "分区信息", "english": "分区信息", "file": "components\\mcDataObservation\\mcTopic.vue"}, {"key": "texttexttexttext", "chinese": "主题描述", "english": "主题描述", "file": "components\\mcDataObservation\\mcTopic.vue"}, {"key": "texttexttext", "chinese": "分区数", "english": "分区数", "file": "components\\mcDataObservation\\mcTopic.vue"}, {"key": "texttexttexttexttext", "chinese": "分区副本数", "english": "分区副本数", "file": "components\\mcDataObservation\\mcTopic.vue"}, {"key": "texttexttexttexttexttext", "chinese": "是否全局主题", "english": "是否全局主题", "file": "components\\mcDataObservation\\mcTopic.vue"}, {"key": "texttexttexttext", "chinese": "有序级别", "english": "有序级别", "file": "components\\mcDataObservation\\mcTopic.vue"}, {"key": "texttexttexttext", "chinese": "业务校验", "english": "业务校验", "file": "components\\mcDataObservation\\mcTopic.vue"}, {"key": "texttexttexttext", "chinese": "可靠级别", "english": "可靠级别", "file": "components\\mcDataObservation\\mcTopic.vue"}, {"key": "texttexttexttexttext", "chinese": "消息有效期", "english": "消息有效期", "file": "components\\mcDataObservation\\mcTopic.vue"}, {"key": "texttexttexttexttextsavetexttexttexttexttext", "chinese": "服务端是否保存消费者偏移", "english": "服务端是否保存消费者偏移", "file": "components\\mcDataObservation\\mcTopic.vue"}, {"key": "texttexttexttext", "chinese": "生产者数", "english": "生产者数", "file": "components\\mcDataObservation\\mcTopic.vue"}, {"key": "texttexttexttext", "chinese": "消费者数", "english": "消费者数", "file": "components\\mcDataObservation\\mcTopic.vue"}, {"key": "texttexttexttexttexttexttext", "chinese": "主分区所在节点", "english": "主分区所在节点", "file": "components\\mcDataObservation\\mcTopic.vue"}, {"key": "texttexttexttexttexttexttexttext", "chinese": "副本分区所在节点", "english": "副本分区所在节点", "file": "components\\mcDataObservation\\mcTopic.vue"}, {"key": "texttexttexttexttexttext", "chinese": "持久化消息数", "english": "持久化消息数", "file": "components\\mcDataObservation\\mcTopic.vue"}, {"key": "texttexttexttexttextname", "chinese": "消费者实例名称", "english": "消费者实例名称", "file": "components\\mcDataObservation\\mcTopic.vue"}, {"key": "texttexttexttext", "chinese": "主题前缀", "english": "主题前缀", "file": "components\\mcDataObservation\\mcTopic.vue"}, {"key": "texttexttexttexttexttext", "chinese": "已推送消息数", "english": "已推送消息数", "file": "components\\mcDataObservation\\mcTopic.vue"}, {"key": "texttexttexttext", "chinese": "补缺个数", "english": "补缺个数", "file": "components\\mcDataObservation\\mcTopic.vue"}, {"key": "texttexttexttexttext", "chinese": "过滤条件值", "english": "过滤条件值", "file": "components\\mcDataObservation\\mcTopic.vue"}, {"key": "texttexttexttexttexttext", "chinese": "发布的消息数", "english": "发布的消息数", "file": "components\\mcDataObservation\\mcTopic.vue"}, {"key": "texttexttexttext", "chinese": "分区有序", "english": "分区有序", "file": "components\\mcDataObservation\\mcTopic.vue"}, {"key": "texttexttexttext", "chinese": "全局有序", "english": "全局有序", "file": "components\\mcDataObservation\\mcTopic.vue"}, {"key": "texttext", "chinese": "文件", "english": "文件", "file": "components\\mcDataObservation\\mcTopic.vue"}, {"key": "texttext", "chinese": "内存", "english": "内存", "file": "components\\mcDataObservation\\mcTopic.vue"}, {"key": "texttextinfo", "chinese": "主题信息", "english": "主题信息", "file": "components\\mcDataObservation\\mcTopic.vue"}], "components\\mcDeploy\\mcTopicDeploy.vue": [{"key": "pleaseinputtexttexttext", "chinese": "请输入主题名", "english": "请输入主题名", "file": "components\\mcDeploy\\mcTopicDeploy.vue"}, {"key": "texttexttexttexttexttext", "chinese": "上次更新日期", "english": "上次更新日期", "file": "components\\mcDeploy\\mcTopicDeploy.vue"}, {"key": "texttexttexttext", "chinese": "主题编号", "english": "主题编号", "file": "components\\mcDeploy\\mcTopicDeploy.vue"}, {"key": "texttexttext", "chinese": "副本数", "english": "副本数", "file": "components\\mcDeploy\\mcTopicDeploy.vue"}, {"key": "texttexttexttexttime", "chinese": "上次更新时间", "english": "上次更新时间", "file": "components\\mcDeploy\\mcTopicDeploy.vue"}, {"key": "texttexttexttexttexttext", "chinese": "动态主题同步", "english": "动态主题同步", "file": "components\\mcDeploy\\mcTopicDeploy.vue"}], "components\\mdbDataObservation\\generalView.vue": [{"key": "datatextname", "chinese": "数据库名称", "english": "数据库名称", "file": "components\\mdbDataObservation\\generalView.vue"}, {"key": "texttextLabel", "chinese": ", // 文本\r\n                            label:", "english": ", // 文本\r\n                            label:", "file": "components\\mdbDataObservation\\generalView.vue"}, {"key": "datatexttexttexttext", "chinese": "数据库表个数", "english": "数据库表个数", "file": "components\\mdbDataObservation\\generalView.vue"}, {"key": "texttexttexttexttext", "chinese": "主控进程号", "english": "主控进程号", "file": "components\\mdbDataObservation\\generalView.vue"}, {"key": "texttexttexttexttext", "chinese": "加锁进程号", "english": "加锁进程号", "file": "components\\mdbDataObservation\\generalView.vue"}, {"key": "texttexttexttexttexttext", "chinese": "已处理事务号", "english": "已处理事务号", "file": "components\\mdbDataObservation\\generalView.vue"}, {"key": "texttexttexttexttexttext", "chinese": "事务处理性能", "english": "事务处理性能", "file": "components\\mdbDataObservation\\generalView.vue"}, {"key": "texttexttexttexttexttext", "chinese": "事务处理总数", "english": "事务处理总数", "file": "components\\mdbDataObservation\\generalView.vue"}, {"key": "texttexttexttexttexttexttexttext", "chinese": "事务处理吞吐(笔/秒)", "english": "事务处理吞吐(笔/秒)", "file": "components\\mdbDataObservation\\generalView.vue"}, {"key": "texttexttexttexttexttext", "chinese": "事务处理吞吐", "english": "事务处理吞吐", "file": "components\\mdbDataObservation\\generalView.vue"}, {"key": "texttexttext", "chinese": "总数(笔)", "english": "总数(笔)", "file": "components\\mdbDataObservation\\generalView.vue"}, {"key": "texttexttexttext", "chinese": "吞吐(笔/秒)", "english": "吞吐(笔/秒)", "file": "components\\mdbDataObservation\\generalView.vue"}, {"key": "texttexttexttexttexttext", "chinese": "内存使用分布", "english": "内存使用分布", "file": "components\\mdbDataObservation\\generalView.vue"}, {"key": "texttextLabel", "chinese": ", // 文本\r\n                        label:", "english": ", // 文本\r\n                        label:", "file": "components\\mdbDataObservation\\generalView.vue"}, {"key": "texttextdatatexttexttextinfo", "chinese": "内存数据库版本信息", "english": "内存数据库版本信息", "file": "components\\mdbDataObservation\\generalView.vue"}, {"key": "texttexttexttextconfig", "chinese": "工作进程配置", "english": "工作进程配置", "file": "components\\mdbDataObservation\\generalView.vue"}, {"key": "adminfiletexttext", "chinese": "AdminFile文件", "english": "AdminFile文件", "file": "components\\mdbDataObservation\\generalView.vue"}, {"key": "texttexttexttextconfig", "chinese": "事务处理配置", "english": "事务处理配置", "file": "components\\mdbDataObservation\\generalView.vue"}, {"key": "texttexttexttextconfig", "chinese": "内存分配配置", "english": "内存分配配置", "file": "components\\mdbDataObservation\\generalView.vue"}], "components\\mdbDataObservation\\memory.vue": [{"key": "texttexttexttext", "chinese": "总记录数", "english": "总记录数", "file": "components\\mdbDataObservation\\memory.vue"}, {"key": "texttexttexttexttexttexttexttext", "chinese": "占已使用内存比率", "english": "占已使用内存比率", "file": "components\\mdbDataObservation\\memory.vue"}, {"key": "textsearchtexttexttext", "chinese": "请搜索内存表", "english": "请搜索内存表", "file": "components\\mdbDataObservation\\memory.vue"}, {"key": "texttexttexttexttexttexttexttext", "chinese": "表记录数增长统计", "english": "表记录数增长统计", "file": "components\\mdbDataObservation\\memory.vue"}, {"key": "texttexttexttexttexttexttexttexttext", "chinese": "表内存使用增长统计", "english": "表内存使用增长统计", "file": "components\\mdbDataObservation\\memory.vue"}, {"key": "texttexttexttexttexttexttext", "chinese": "表内存使用分布", "english": "表内存使用分布", "file": "components\\mdbDataObservation\\memory.vue"}, {"key": "textdatatexttexttexttext", "chinese": "表数据存储-文件", "english": "表数据存储-文件", "file": "components\\mdbDataObservation\\memory.vue"}, {"key": "texttexttextinfo", "chinese": "表索引信息", "english": "表索引信息", "file": "components\\mdbDataObservation\\memory.vue"}], "components\\mdbDataObservation\\performance.vue": [{"key": "texttexttexttexttexttext", "chinese": "性能分析开关", "english": "性能分析开关", "file": "components\\mdbDataObservation\\performance.vue"}, {"key": "texttextdetail", "chinese": "执行详情", "english": "执行详情", "file": "components\\mdbDataObservation\\performance.vue"}, {"key": "texttexttexttexttextid", "chinese": "事务控制器ID", "english": "事务控制器ID", "file": "components\\mdbDataObservation\\performance.vue"}, {"key": "texttexttexttext", "chinese": "执行次数", "english": "执行次数", "file": "components\\mdbDataObservation\\performance.vue"}, {"key": "texttexttexttext", "chinese": "平均耗时", "english": "平均耗时", "file": "components\\mdbDataObservation\\performance.vue"}, {"key": "texttexttexttext", "chinese": "最小耗时", "english": "最小耗时", "file": "components\\mdbDataObservation\\performance.vue"}, {"key": "texttexttexttexttexttext", "chinese": "前十最大耗时", "english": "前十最大耗时", "file": "components\\mdbDataObservation\\performance.vue"}, {"key": "texttexttext", "chinese": "标准差", "english": "标准差", "file": "components\\mdbDataObservation\\performance.vue"}, {"key": "texttextTexttexttexttext", "chinese": "开启 (正常级别)", "english": "开启 (正常级别)", "file": "components\\mdbDataObservation\\performance.vue"}, {"key": "texttextTexttexttexttext", "chinese": "开启 (基本级别)", "english": "开启 (基本级别)", "file": "components\\mdbDataObservation\\performance.vue"}], "components\\mdbDataObservation\\processor.vue": [{"key": "texttexttexttexttexttextTop10", "chinese": "事务处理吞吐 - Top10", "english": "事务处理吞吐 - Top10", "file": "components\\mdbDataObservation\\processor.vue"}, {"key": "texttexttexttext", "chinese": "死锁检测", "english": "死锁检测", "file": "components\\mdbDataObservation\\processor.vue"}, {"key": "texttexttexttexttexttexttexttext", "chinese": "只看被阻塞控制器", "english": "只看被阻塞控制器", "file": "components\\mdbDataObservation\\processor.vue"}, {"key": "texttexttexttexttexttexttext", "chinese": "只看工作控制器", "english": "只看工作控制器", "file": "components\\mdbDataObservation\\processor.vue"}, {"key": "undotexttextinfo", "chinese": "Undo文件信息", "english": "Undo文件信息", "file": "components\\mdbDataObservation\\processor.vue"}, {"key": "undotexttexttexttexttextinfo", "chinese": "Undo文件记录数信息", "english": "Undo文件记录数信息", "file": "components\\mdbDataObservation\\processor.vue"}, {"key": "undotexttexttext", "chinese": "Undo事务号: -", "english": "Undo事务号: -", "file": "components\\mdbDataObservation\\processor.vue"}, {"key": "undotexttexttextdatacommitsqn", "chinese": "Undo事务号:${data.CommitSqn || '-'}", "english": "Undo事务号:${data.CommitSqn || '-'}", "file": "components\\mdbDataObservation\\processor.vue"}], "components\\mdbDataObservation\\slowTransaction.vue": [{"key": "texttexttime", "chinese": "执行时间", "english": "执行时间", "file": "components\\mdbDataObservation\\slowTransaction.vue"}, {"key": "texttexttexttexttext", "chinese": "事务控制器", "english": "事务控制器", "file": "components\\mdbDataObservation\\slowTransaction.vue"}, {"key": "texttextms", "chinese": "耗时（ms）", "english": "耗时（ms）", "file": "components\\mdbDataObservation\\slowTransaction.vue"}, {"key": "texttexttext", "chinese": "处理表", "english": "处理表", "file": "components\\mdbDataObservation\\slowTransaction.vue"}, {"key": "texttexttexttexttext", "chinese": "处理表明细", "english": "处理表明细", "file": "components\\mdbDataObservation\\slowTransaction.vue"}], "components\\mdbPrivilegeManage\\roleManage.vue": [{"key": "addtexttextabuttonAbuttonType", "chinese": ">添加角色</a-button>\r\n                <a-button type=", "english": ">添加角色</a-button>\r\n                <a-button type=", "file": "components\\mdbPrivilegeManage\\roleManage.vue"}, {"key": "texttexttext", "chinese": "角色名：", "english": "角色名：", "file": "components\\mdbPrivilegeManage\\roleManage.vue"}, {"key": "pleaseinputtexttexttext", "chinese": "请输入角色名", "english": "请输入角色名", "file": "components\\mdbPrivilegeManage\\roleManage.vue"}, {"key": "texttexttext", "chinese": "角色名", "english": "角色名", "file": "components\\mdbPrivilegeManage\\roleManage.vue"}, {"key": "texttexttextconfigtexttext", "chinese": "角色未配置权限", "english": "角色未配置权限", "file": "components\\mdbPrivilegeManage\\roleManage.vue"}, {"key": "texttexttexttext", "chinese": "角色描述", "english": "角色描述", "file": "components\\mdbPrivilegeManage\\roleManage.vue"}, {"key": "edittime", "chinese": "修改时间", "english": "修改时间", "file": "components\\mdbPrivilegeManage\\roleManage.vue"}, {"key": "edit", "chinese": "编辑", "english": "编辑", "file": "components\\mdbPrivilegeManage\\roleManage.vue"}, {"key": "texttextconfig", "chinese": "权限配置", "english": "权限配置", "file": "components\\mdbPrivilegeManage\\roleManage.vue"}, {"key": "texttexttexttext", "chinese": "绑定用户", "english": "绑定用户", "file": "components\\mdbPrivilegeManage\\roleManage.vue"}, {"key": "texttexttexttextsuccess", "chinese": "创建角色成功!", "english": "创建角色成功!", "file": "components\\mdbPrivilegeManage\\roleManage.vue"}, {"key": "edittexttextsuccess", "chinese": "修改角色成功!", "english": "修改角色成功!", "file": "components\\mdbPrivilegeManage\\roleManage.vue"}, {"key": "deletesuccess", "chinese": "删除成功!", "english": "删除成功!", "file": "components\\mdbPrivilegeManage\\roleManage.vue"}, {"key": "readOnlytexttexttexttexttexttexttexttexttextdeletetexttexttexttext", "chinese": "read only为系统内置角色,不可删除,请勿勾选！", "english": "read only为系统内置角色,不可删除,请勿勾选！", "file": "components\\mdbPrivilegeManage\\roleManage.vue"}, {"key": "texttextdeletetexttext", "chinese": "批量删除角色", "english": "批量删除角色", "file": "components\\mdbPrivilegeManage\\roleManage.vue"}, {"key": "confirmtexttexttextdeletetexttexttexttexttexttexttext", "chinese": "确认要批量删除已选中的角色吗？", "english": "确认要批量删除已选中的角色吗？", "file": "components\\mdbPrivilegeManage\\roleManage.vue"}, {"key": "confirmtextdeletetexttextparamsrowrolenametext", "chinese": "确认要删除角色\"${params.row.roleName}\"吗？", "english": "确认要删除角色\"${params.row.roleName}\"吗？", "file": "components\\mdbPrivilegeManage\\roleManage.vue"}, {"key": "addtexttext", "chinese": "添加角色", "english": "添加角色", "file": "components\\mdbPrivilegeManage\\roleManage.vue"}, {"key": "deletetexttext", "chinese": "删除角色", "english": "删除角色", "file": "components\\mdbPrivilegeManage\\roleManage.vue"}], "components\\mdbPrivilegeManage\\userManage.vue": [{"key": "texttext", "chinese": "用户", "english": "用户", "file": "components\\mdbPrivilegeManage\\userManage.vue"}, {"key": "addtexttextabuttonAbuttonType", "chinese": ">添加用户</a-button>\r\n                <a-button type=", "english": ">添加用户</a-button>\r\n                <a-button type=", "file": "components\\mdbPrivilegeManage\\userManage.vue"}, {"key": "texttexttext", "chinese": "用户名：", "english": "用户名：", "file": "components\\mdbPrivilegeManage\\userManage.vue"}, {"key": "pleaseinputtexttexttext", "chinese": "请输入用户名", "english": "请输入用户名", "file": "components\\mdbPrivilegeManage\\userManage.vue"}, {"key": "texttexttext", "chinese": "用户名", "english": "用户名", "file": "components\\mdbPrivilegeManage\\userManage.vue"}, {"key": "texttexttexttext", "chinese": "用户描述", "english": "用户描述", "file": "components\\mdbPrivilegeManage\\userManage.vue"}, {"key": "texttexttexttext", "chinese": "关联角色", "english": "关联角色", "file": "components\\mdbPrivilegeManage\\userManage.vue"}, {"key": "resettexttext", "chinese": "重置密码", "english": "重置密码", "file": "components\\mdbPrivilegeManage\\userManage.vue"}, {"key": "texttextoperationfailed", "chinese": "用户操作失败:", "english": "用户操作失败:", "file": "components\\mdbPrivilegeManage\\userManage.vue"}, {"key": "texttexteditsuccess", "chinese": "用户修改成功！", "english": "用户修改成功！", "file": "components\\mdbPrivilegeManage\\userManage.vue"}, {"key": "texttexteditfailed", "chinese": "用户修改失败！", "english": "用户修改失败！", "file": "components\\mdbPrivilegeManage\\userManage.vue"}, {"key": "texttextdeletetexttext", "chinese": "批量删除用户", "english": "批量删除用户", "file": "components\\mdbPrivilegeManage\\userManage.vue"}, {"key": "confirmtexttexttextdeletetexttexttexttexttexttexttext", "chinese": "确认要批量删除已选中的用户吗？", "english": "确认要批量删除已选中的用户吗？", "file": "components\\mdbPrivilegeManage\\userManage.vue"}, {"key": "confirmtextdeletetexttextparamsrowusernametext", "chinese": "确认要删除用户\"${params.row.userName}\"吗？", "english": "确认要删除用户\"${params.row.userName}\"吗？", "file": "components\\mdbPrivilegeManage\\userManage.vue"}, {"key": "texttexttextresettexttexttext", "chinese": "确定要重置密码吗？", "english": "确定要重置密码吗？", "file": "components\\mdbPrivilegeManage\\userManage.vue"}, {"key": "addtexttext", "chinese": "添加用户", "english": "添加用户", "file": "components\\mdbPrivilegeManage\\userManage.vue"}, {"key": "deletetexttext", "chinese": "删除用户", "english": "删除用户", "file": "components\\mdbPrivilegeManage\\userManage.vue"}], "components\\networkSendAndRecevied\\netRecord.vue": [{"key": "texttexttexttext", "chinese": "用例内容", "english": "用例内容", "file": "components\\networkSendAndRecevied\\netRecord.vue"}, {"key": "texttexttexteditsuccess", "chinese": "用例名修改成功!", "english": "用例名修改成功!", "file": "components\\networkSendAndRecevied\\netRecord.vue"}, {"key": "texttextdeletesuccess", "chinese": "用例删除成功!", "english": "用例删除成功!", "file": "components\\networkSendAndRecevied\\netRecord.vue"}, {"key": "bodytexttexttexttexttexttexttexttexttexttext", "chinese": "Body内容为空或格式不正确", "english": "Body内容为空或格式不正确", "file": "components\\networkSendAndRecevied\\netRecord.vue"}, {"key": "texttextsavesuccess", "chinese": "用例保存成功!", "english": "用例保存成功!", "file": "components\\networkSendAndRecevied\\netRecord.vue"}, {"key": "texttexttexttexttexttext", "chinese": "请先选择用例", "english": "请先选择用例", "file": "components\\networkSendAndRecevied\\netRecord.vue"}, {"key": "texttextthismenulist0label", "chinese": "用例：${this.menuList?.[0]?.label}", "english": "用例：${this.menuList?.[0]?.label}", "file": "components\\networkSendAndRecevied\\netRecord.vue"}, {"key": "texttextitemlabel", "chinese": "用例：${item?.label}", "english": "用例：${item?.label}", "file": "components\\networkSendAndRecevied\\netRecord.vue"}, {"key": "texttexttextdeleteItemlabelTexttext", "chinese": "您确定删除 '${item.label}' 用例？", "english": "您确定删除 '${item.label}' 用例？", "file": "components\\networkSendAndRecevied\\netRecord.vue"}], "components\\networkSendAndRecevied\\netResource.vue": [{"key": "texttexttext", "chinese": "抓包中", "english": "抓包中", "file": "components\\networkSendAndRecevied\\netResource.vue"}, {"key": "functiontext", "chinese": "功能号：", "english": "功能号：", "file": "components\\networkSendAndRecevied\\netResource.vue"}, {"key": "texttexttext", "chinese": "消息体", "english": "消息体", "file": "components\\networkSendAndRecevied\\netResource.vue"}, {"key": "texttextlist", "chinese": "消息列表", "english": "消息列表", "file": "components\\networkSendAndRecevied\\netResource.vue"}, {"key": "texttext", "chinese": "清空", "english": "清空", "file": "components\\networkSendAndRecevied\\netResource.vue"}, {"key": "texttexttexttexttext", "chinese": "用户自定义", "english": "用户自定义", "file": "components\\networkSendAndRecevied\\netResource.vue"}, {"key": "texttexttime", "chinese": "抓包时间", "english": "抓包时间", "file": "components\\networkSendAndRecevied\\netResource.vue"}, {"key": "texttexttext", "chinese": "包类型", "english": "包类型", "file": "components\\networkSendAndRecevied\\netResource.vue"}, {"key": "texttextinfo", "chinese": "附加信息", "english": "附加信息", "file": "components\\networkSendAndRecevied\\netResource.vue"}, {"key": "texttexttexttextfailed", "chinese": "开启抓包失败", "english": "开启抓包失败", "file": "components\\networkSendAndRecevied\\netResource.vue"}, {"key": "texttexttexttextfailed", "chinese": "停止抓包失败", "english": "停止抓包失败", "file": "components\\networkSendAndRecevied\\netResource.vue"}, {"key": "texttextlisttextreset", "chinese": "消息列表已重置！", "english": "消息列表已重置！", "file": "components\\networkSendAndRecevied\\netResource.vue"}, {"key": "texttexttexttexttexttexttexttexttexttexttexttexttextrefreshlisttexttexttexttexttexttexttexttexttextquerytexttexttext", "chinese": "当前页为首页无法支持翻页，请刷新列表或用序号跳转至您想查询的位置", "english": "当前页为首页无法支持翻页，请刷新列表或用序号跳转至您想查询的位置", "file": "components\\networkSendAndRecevied\\netResource.vue"}, {"key": "texttexttexttexttexttexttexttexttexttexttexttexttexttexttextrefreshlisttexttexttexttexttexttexttexttexttextquerytexttexttext", "chinese": "当前页为最后一页无法支持翻页，请刷新列表或用序号跳转至您想查询的位置", "english": "当前页为最后一页无法支持翻页，请刷新列表或用序号跳转至您想查询的位置", "file": "components\\networkSendAndRecevied\\netResource.vue"}, {"key": "texttexttexttexttexttextdatatext", "chinese": "确定清空抓包数据吗?", "english": "确定清空抓包数据吗?", "file": "components\\networkSendAndRecevied\\netResource.vue"}, {"key": "texttexttexttexttexttexttext", "chinese": "无托管应用节点", "english": "无托管应用节点", "file": "components\\networkSendAndRecevied\\netResource.vue"}, {"key": "texttexttexttexttexttexttexttexttexttexttexttexttexttexttexttext", "chinese": "一个应用节点仅能开启一个抓包任务", "english": "一个应用节点仅能开启一个抓包任务", "file": "components\\networkSendAndRecevied\\netResource.vue"}, {"key": "starttexttext", "chinese": "开始抓包", "english": "开始抓包", "file": "components\\networkSendAndRecevied\\netResource.vue"}, {"key": "texttexttexttext", "chinese": "停止抓包", "english": "停止抓包", "file": "components\\networkSendAndRecevied\\netResource.vue"}, {"key": "textMsgtotalText", "chinese": "共 {{ msgTotal }} 条", "english": "共 {{ msgTotal }} 条", "file": "components\\networkSendAndRecevied\\netResource.vue"}, {"key": "texttexttexttext", "chinese": "跳转序号", "english": "跳转序号", "file": "components\\networkSendAndRecevied\\netResource.vue"}], "components\\networkSendAndRecevied\\netSend.vue": [{"key": "texttexttext", "chinese": "接入点", "english": "接入点", "file": "components\\networkSendAndRecevied\\netSend.vue"}, {"key": "texttexttexttexttexttext", "chinese": "图片无法显示", "english": "图片无法显示", "file": "components\\networkSendAndRecevied\\netSend.vue"}, {"key": "textdivDivSlot", "chinese": ">开</div>\r\n                            <div slot=", "english": ">开</div>\r\n                            <div slot=", "file": "components\\networkSendAndRecevied\\netSend.vue"}, {"key": "texttexttexttexttext", "chinese": "输入用例名", "english": "输入用例名", "file": "components\\networkSendAndRecevied\\netSend.vue"}, {"key": "2Texttexttimetexttexttextdelete40texttexttexttexttext", "chinese": ": {}\r\n        * 2. 关闭时间戳开关：删除40号域，即删除", "english": ": {}\r\n        * 2. 关闭时间戳开关：删除40号域，即删除", "file": "components\\networkSendAndRecevied\\netSend.vue"}, {"key": "texttext", "chinese": "请求", "english": "请求", "file": "components\\networkSendAndRecevied\\netSend.vue"}, {"key": "texttext", "chinese": "响应", "english": "响应", "file": "components\\networkSendAndRecevied\\netSend.vue"}, {"key": "texttextbodytexttexttexttexttexttexttexttexttexttext", "chinese": "请求Body内容为空或格式不正确", "english": "请求Body内容为空或格式不正确", "file": "components\\networkSendAndRecevied\\netSend.vue"}, {"key": "texttextsuccess", "chinese": "发包成功！", "english": "发包成功！", "file": "components\\networkSendAndRecevied\\netSend.vue"}, {"key": "texttexttexttexttexttext", "chinese": "无可用接入点", "english": "无可用接入点", "file": "components\\networkSendAndRecevied\\netSend.vue"}, {"key": "texttexttexttexttexttextconfigtexttexttexttexttexttext", "chinese": "请至“产品服务配置-产品服务网关”", "english": "请至“产品服务配置-产品服务网关”", "file": "components\\networkSendAndRecevied\\netSend.vue"}, {"key": "timetextNbsp", "chinese": "时间戳 &nbsp;", "english": "时间戳 &nbsp;", "file": "components\\networkSendAndRecevied\\netSend.vue"}, {"key": "text", "chinese": "开", "english": "开", "file": "components\\networkSendAndRecevied\\netSend.vue"}, {"key": "text", "chinese": "关", "english": "关", "file": "components\\networkSendAndRecevied\\netSend.vue"}, {"key": "texttext", "chinese": "发送", "english": "发送", "file": "components\\networkSendAndRecevied\\netSend.vue"}, {"key": "nbspnbspnbsptexttextlist", "chinese": "&nbsp;&nbsp;&nbsp;用例列表", "english": "&nbsp;&nbsp;&nbsp;用例列表", "file": "components\\networkSendAndRecevied\\netSend.vue"}, {"key": "texttext", "chinese": "引用", "english": "引用", "file": "components\\networkSendAndRecevied\\netSend.vue"}, {"key": "texttexttext", "chinese": "无内容", "english": "无内容", "file": "components\\networkSendAndRecevied\\netSend.vue"}, {"key": "savetexttexttext", "chinese": "保存为用例", "english": "保存为用例", "file": "components\\networkSendAndRecevied\\netSend.vue"}, {"key": "texttexttexttexttexttexttext", "chinese": "发包调用总耗时：", "english": "发包调用总耗时：", "file": "components\\networkSendAndRecevied\\netSend.vue"}, {"key": "texttexttexttexttexttext", "chinese": "发包调用耗时：", "english": "发包调用耗时：", "file": "components\\networkSendAndRecevied\\netSend.vue"}, {"key": "texttexttimetext", "chinese": "查看时间戳", "english": "查看时间戳", "file": "components\\networkSendAndRecevied\\netSend.vue"}], "components\\networkSendAndRecevied\\netThrouth.vue": [{"key": "texttextlist", "chinese": "日志列表", "english": "日志列表", "file": "components\\networkSendAndRecevied\\netThrouth.vue"}, {"key": "texttexttexttexttexttexttexttexttexttexttexttexttexttext", "chinese": "支持输入多个，采用英文分号区分", "english": "支持输入多个，采用英文分号区分", "file": "components\\networkSendAndRecevied\\netThrouth.vue"}, {"key": "pleaseinputfunctiontexttexttexttexttexttexttexttexttexttext", "chinese": "请输入功能号以英文分号形式间隔", "english": "请输入功能号以英文分号形式间隔", "file": "components\\networkSendAndRecevied\\netThrouth.vue"}, {"key": "texttextdata", "chinese": "获取数据", "english": "获取数据", "file": "components\\networkSendAndRecevied\\netThrouth.vue"}], "components\\productDataStorage\\archiveModal.vue": [{"key": "datatexttext", "chinese": "数据冷备", "english": "数据冷备", "file": "components\\productDataStorage\\archiveModal.vue"}, {"key": "texttexttexttexttext", "chinese": "归档服务器:", "english": "归档服务器:", "file": "components\\productDataStorage\\archiveModal.vue"}, {"key": "texttexttexttext", "chinese": "归档目录:", "english": "归档目录:", "file": "components\\productDataStorage\\archiveModal.vue"}, {"key": "texttexttextdata", "chinese": "产品元数据:", "english": "产品元数据:", "file": "components\\productDataStorage\\archiveModal.vue"}, {"key": "texttexttexttextdata", "chinese": "产品遥测数据:", "english": "产品遥测数据:", "file": "components\\productDataStorage\\archiveModal.vue"}, {"key": "datatexttexttexttext", "chinese": "数据日期范围:", "english": "数据日期范围:", "file": "components\\productDataStorage\\archiveModal.vue"}, {"key": "texttexttextabuttonAbuttonVif", "chinese": ">下一步</a-button>\r\n                <a-button v-if=", "english": ">下一步</a-button>\r\n                <a-button v-if=", "file": "components\\productDataStorage\\archiveModal.vue"}, {"key": "cancelabuttonAbuttonVif", "chinese": ">取消</a-button>\r\n                <a-button v-if=", "english": ">取消</a-button>\r\n                <a-button v-if=", "file": "components\\productDataStorage\\archiveModal.vue"}, {"key": "texttexttexttexttexttext", "chinese": "选择归档条件", "english": "选择归档条件", "file": "components\\productDataStorage\\archiveModal.vue"}, {"key": "texttexttexttextconfirm", "chinese": "归档条件确认", "english": "归档条件确认", "file": "components\\productDataStorage\\archiveModal.vue"}, {"key": "texttexttexttextdata", "chinese": "时延跟踪数据", "english": "时延跟踪数据", "file": "components\\productDataStorage\\archiveModal.vue"}, {"key": "monitortexttextdata", "chinese": "监控指标数据", "english": "监控指标数据", "file": "components\\productDataStorage\\archiveModal.vue"}, {"key": "texttexttexttexttexttextsuccess", "chinese": "归档指令发送成功！", "english": "归档指令发送成功！", "file": "components\\productDataStorage\\archiveModal.vue"}, {"key": "texttextfailed", "chinese": "归档失败!", "english": "归档失败!", "file": "components\\productDataStorage\\archiveModal.vue"}, {"key": "nextstatusTexttexttexttexttexttexttexttexttexttextconfirm", "chinese": "{{ !nextStatus ? '选择归档条件':'归档条件确认'}}", "english": "{{ !nextStatus ? '选择归档条件':'归档条件确认'}}", "file": "components\\productDataStorage\\archiveModal.vue"}, {"key": "texttexttexttexttext", "chinese": "归档服务器：", "english": "归档服务器：", "file": "components\\productDataStorage\\archiveModal.vue"}, {"key": "texttexttexttext", "chinese": "归档目录：", "english": "归档目录：", "file": "components\\productDataStorage\\archiveModal.vue"}, {"key": "texttexttextdata", "chinese": "产品元数据：", "english": "产品元数据：", "file": "components\\productDataStorage\\archiveModal.vue"}, {"key": "texttexttexttextdata", "chinese": "产品遥测数据：", "english": "产品遥测数据：", "file": "components\\productDataStorage\\archiveModal.vue"}, {"key": "datatexttexttexttext", "chinese": "数据日期范围：", "english": "数据日期范围：", "file": "components\\productDataStorage\\archiveModal.vue"}, {"key": "text", "chinese": "至", "english": "至", "file": "components\\productDataStorage\\archiveModal.vue"}, {"key": "texttexttext", "chinese": "下一步", "english": "下一步", "file": "components\\productDataStorage\\archiveModal.vue"}, {"key": "texttext", "chinese": "归档", "english": "归档", "file": "components\\productDataStorage\\archiveModal.vue"}], "components\\productDataStorage\\autoConditionModal.vue": [{"key": "texttexttexttexttexttext", "chinese": "每日定时清理", "english": "每日定时清理", "file": "components\\productDataStorage\\autoConditionModal.vue"}, {"key": "datatexttexttexttext", "chinese": "数据保留天数：", "english": "数据保留天数：", "file": "components\\productDataStorage\\autoConditionModal.vue"}, {"key": "texttext3600text", "chinese": "最大3600天", "english": "最大3600天", "file": "components\\productDataStorage\\autoConditionModal.vue"}, {"key": "texttexttexttexttime", "chinese": "选择清理时间", "english": "选择清理时间", "file": "components\\productDataStorage\\autoConditionModal.vue"}, {"key": "texttexttexttext", "chinese": "是否启用：", "english": "是否启用：", "file": "components\\productDataStorage\\autoConditionModal.vue"}, {"key": "queryabuttonAbuttonStyle", "chinese": ">查询</a-button>\r\n                <a-button style=", "english": ">查询</a-button>\r\n                <a-button style=", "file": "components\\productDataStorage\\autoConditionModal.vue"}, {"key": "texttexttexttext", "chinese": "清理日期：", "english": "清理日期：", "file": "components\\productDataStorage\\autoConditionModal.vue"}, {"key": "texttexttexttext", "chinese": "清理结果：", "english": "清理结果：", "file": "components\\productDataStorage\\autoConditionModal.vue"}, {"key": "datatexttexttexttext13600text", "chinese": "数据保留天数1~3600天", "english": "数据保留天数1~3600天", "file": "components\\productDataStorage\\autoConditionModal.vue"}, {"key": "texttexttime", "chinese": "清理时间", "english": "清理时间", "file": "components\\productDataStorage\\autoConditionModal.vue"}, {"key": "texttexttexttext", "chinese": "清理结果", "english": "清理结果", "file": "components\\productDataStorage\\autoConditionModal.vue"}, {"key": "failedinfo", "chinese": "失败信息", "english": "失败信息", "file": "components\\productDataStorage\\autoConditionModal.vue"}, {"key": "texttexttextdatatexttext", "chinese": "被清理数据范围", "english": "被清理数据范围", "file": "components\\productDataStorage\\autoConditionModal.vue"}, {"key": "texttexttext", "chinese": "索引号", "english": "索引号", "file": "components\\productDataStorage\\autoConditionModal.vue"}, {"key": "texttexttextdatatexttext", "chinese": "被清理数据大小", "english": "被清理数据大小", "file": "components\\productDataStorage\\autoConditionModal.vue"}, {"key": "texttexttexttextconfigsuccess", "chinese": "定时清理配置成功！", "english": "定时清理配置成功！", "file": "components\\productDataStorage\\autoConditionModal.vue"}, {"key": "texttextconfig", "chinese": "清理配置", "english": "清理配置", "file": "components\\productDataStorage\\autoConditionModal.vue"}, {"key": "texttexttexttexttexttexttexttexttexttexttexttexttextoperation", "chinese": "(启用后生效，每日定时执行清理操作)", "english": "(启用后生效，每日定时执行清理操作)", "file": "components\\productDataStorage\\autoConditionModal.vue"}, {"key": "texttexttexttexttexttext", "chinese": "定时清理记录", "english": "定时清理记录", "file": "components\\productDataStorage\\autoConditionModal.vue"}, {"key": "reset", "chinese": "重置", "english": "重置", "file": "components\\productDataStorage\\autoConditionModal.vue"}], "components\\productDataStorage\\conditionModal.vue": [{"key": "texttexttexttext", "chinese": "产品节点：", "english": "产品节点：", "file": "components\\productDataStorage\\conditionModal.vue"}, {"key": "datatexttext", "chinese": "数据日期：", "english": "数据日期：", "file": "components\\productDataStorage\\conditionModal.vue"}, {"key": "texttexttexttext", "chinese": "资金账号：", "english": "资金账号：", "file": "components\\productDataStorage\\conditionModal.vue"}, {"key": "texttexttexttexttexttexttexttexttext30text", "chinese": "请限制清理日期小于30天", "english": "请限制清理日期小于30天", "file": "components\\productDataStorage\\conditionModal.vue"}, {"key": "texttexttexttexttexttextsuccess", "chinese": "清理指令发送成功！", "english": "清理指令发送成功！", "file": "components\\productDataStorage\\conditionModal.vue"}, {"key": "texttext", "chinese": "清理", "english": "清理", "file": "components\\productDataStorage\\conditionModal.vue"}], "components\\productDataStorage\\conditionModal1.vue": [{"key": "datatexttextconfig", "chinese": "数据清理配置", "english": "数据清理配置", "file": "components\\productDataStorage\\conditionModal1.vue"}, {"key": "texttexttexttext", "chinese": "产品节点:", "english": "产品节点:", "file": "components\\productDataStorage\\conditionModal1.vue"}, {"key": "datatexttext", "chinese": "数据日期:", "english": "数据日期:", "file": "components\\productDataStorage\\conditionModal1.vue"}, {"key": "datatexttext", "chinese": "数据类型:", "english": "数据类型:", "file": "components\\productDataStorage\\conditionModal1.vue"}, {"key": "texttexttexttexttexttext", "chinese": "选择清理条件", "english": "选择清理条件", "file": "components\\productDataStorage\\conditionModal1.vue"}, {"key": "texttexttexttextconfirm", "chinese": "清理条件确认", "english": "清理条件确认", "file": "components\\productDataStorage\\conditionModal1.vue"}, {"key": "datatexttextfailed", "chinese": "数据清理失败!", "english": "数据清理失败!", "file": "components\\productDataStorage\\conditionModal1.vue"}, {"key": "nextstatusTexttexttexttexttexttexttexttexttexttextconfirm", "chinese": "{{ !nextStatus ? '选择清理条件':'清理条件确认'}}", "english": "{{ !nextStatus ? '选择清理条件':'清理条件确认'}}", "file": "components\\productDataStorage\\conditionModal1.vue"}, {"key": "datatexttext", "chinese": "数据类型：", "english": "数据类型：", "file": "components\\productDataStorage\\conditionModal1.vue"}, {"key": "warningTextoperationtexttexttextdeletetexttexttexttexttexttexttextdataTexttexttexttexttext", "chinese": "警告: 该操作将物理删除符合条件的所有数据, 操作不可逆!", "english": "警告: 该操作将物理删除符合条件的所有数据, 操作不可逆!", "file": "components\\productDataStorage\\conditionModal1.vue"}, {"key": "confirmtexttext", "chinese": "确认清理", "english": "确认清理", "file": "components\\productDataStorage\\conditionModal1.vue"}], "components\\productDataStorage\\fileListModal.vue": [{"key": "texttexttextdatatexttext", "chinese": "待归档数据清单", "english": "待归档数据清单", "file": "components\\productDataStorage\\fileListModal.vue"}, {"key": "texttexttexttext", "chinese": "文件条数", "english": "文件条数", "file": "components\\productDataStorage\\fileListModal.vue"}, {"key": "texttexttexttext", "chinese": "占用空间", "english": "占用空间", "file": "components\\productDataStorage\\fileListModal.vue"}, {"key": "texttexttexttexttexttext", "chinese": "总计:索引文件:", "english": "总计:索引文件:", "file": "components\\productDataStorage\\fileListModal.vue"}, {"key": "textTexttexttexttext", "chinese": "个; 文件条数:", "english": "个; 文件条数:", "file": "components\\productDataStorage\\fileListModal.vue"}, {"key": "textTexttexttexttext", "chinese": "条; 存储空间:", "english": "条; 存储空间:", "file": "components\\productDataStorage\\fileListModal.vue"}], "components\\productServiceConfig\\linkTopoConfig.vue": [{"key": "texttexttexttexttexttext", "chinese": "不支持该特性", "english": "不支持该特性", "file": "components\\productServiceConfig\\linkTopoConfig.vue"}, {"key": "texttexttexttexttexttexttext", "chinese": "是否支持该特性", "english": "是否支持该特性", "file": "components\\productServiceConfig\\linkTopoConfig.vue"}, {"key": "texttexttext", "chinese": "不支持", "english": "不支持", "file": "components\\productServiceConfig\\linkTopoConfig.vue"}, {"key": "texttexttexttexttexttexttexttexttextlist", "chinese": "已支持业务链路模型列表", "english": "已支持业务链路模型列表", "file": "components\\productServiceConfig\\linkTopoConfig.vue"}, {"key": "texttexttexttexttexttexttexttext", "chinese": "业务链路度量模型", "english": "业务链路度量模型", "file": "components\\productServiceConfig\\linkTopoConfig.vue"}, {"key": "texttexttexttext", "chinese": "选择模型", "english": "选择模型", "file": "components\\productServiceConfig\\linkTopoConfig.vue"}, {"key": "texttexttexttexttexttext", "chinese": "业务系统类型", "english": "业务系统类型", "file": "components\\productServiceConfig\\linkTopoConfig.vue"}, {"key": "texttexttexttext", "chinese": "模型预览", "english": "模型预览", "file": "components\\productServiceConfig\\linkTopoConfig.vue"}, {"key": "texttextconfigsuccess", "chinese": "模型配置成功", "english": "模型配置成功", "file": "components\\productServiceConfig\\linkTopoConfig.vue"}, {"key": "texttexttexttextfailed", "chinese": "切换模型失败", "english": "切换模型失败", "file": "components\\productServiceConfig\\linkTopoConfig.vue"}, {"key": "texttexttexttexttexttexttexttexttext", "chinese": "确定要切换度量模型？", "english": "确定要切换度量模型？", "file": "components\\productServiceConfig\\linkTopoConfig.vue"}, {"key": "texttexttexttexttexttexttexttexttexttexttexttexttexttexttexttexttexttexttextconfigtexttexttexttexttexttexttexttexttexttexttexttexttexttexttexttexttexttexttexttexttexttexttexttext", "chinese": "切换度量模型，本产品节点下对应的「链路日志配置」将自动同步更新。本次变动需要重启采集子系统才可生效。", "english": "切换度量模型，本产品节点下对应的「链路日志配置」将自动同步更新。本次变动需要重启采集子系统才可生效。", "file": "components\\productServiceConfig\\linkTopoConfig.vue"}], "components\\productServiceConfig\\linkTopoReview.vue": [{"key": "texttextdatatexttext", "chinese": "链路数据定义", "english": "链路数据定义", "file": "components\\productServiceConfig\\linkTopoReview.vue"}, {"key": "texttexttexttexttexttexttexttexttexttexttext", "chinese": "应用全链路时延度量模型", "english": "应用全链路时延度量模型", "file": "components\\productServiceConfig\\linkTopoReview.vue"}, {"key": "texttextname", "chinese": "模型名称", "english": "模型名称", "file": "components\\productServiceConfig\\linkTopoReview.vue"}, {"key": "texttexttexttext", "chinese": "业务系统", "english": "业务系统", "file": "components\\productServiceConfig\\linkTopoReview.vue"}, {"key": "texttexttexttexttexttext", "chinese": "业务系统版本", "english": "业务系统版本", "file": "components\\productServiceConfig\\linkTopoReview.vue"}, {"key": "texttexttexttext", "chinese": "模型版本", "english": "模型版本", "file": "components\\productServiceConfig\\linkTopoReview.vue"}, {"key": "texttext", "chinese": "简介", "english": "简介", "file": "components\\productServiceConfig\\linkTopoReview.vue"}, {"key": "texttexttexttext", "chinese": "跨度定义", "english": "跨度定义", "file": "components\\productServiceConfig\\linkTopoReview.vue"}, {"key": "texttextname", "chinese": "跨度名称", "english": "跨度名称", "file": "components\\productServiceConfig\\linkTopoReview.vue"}, {"key": "texttexttexttext", "chinese": "字段明细", "english": "字段明细", "file": "components\\productServiceConfig\\linkTopoReview.vue"}, {"key": "texttextdata", "chinese": "链路数据", "english": "链路数据", "file": "components\\productServiceConfig\\linkTopoReview.vue"}], "components\\productServiceConfig\\logSourceConfig.vue": [{"key": "texttexttexttext", "chinese": "链路模型", "english": "链路模型", "file": "components\\productServiceConfig\\logSourceConfig.vue"}, {"key": "texttexttexttext", "chinese": "日志类型", "english": "日志类型", "file": "components\\productServiceConfig\\logSourceConfig.vue"}, {"key": "texttexttexttexttexttexttexttext", "chinese": "时延日志输出目录", "english": "时延日志输出目录", "file": "components\\productServiceConfig\\logSourceConfig.vue"}, {"key": "texttexttexttexttexttexttext", "chinese": "时延日志关键字", "english": "时延日志关键字", "file": "components\\productServiceConfig\\logSourceConfig.vue"}, {"key": "texttextconfigsuccess", "chinese": "同步配置成功", "english": "同步配置成功", "file": "components\\productServiceConfig\\logSourceConfig.vue"}, {"key": "textconfirmtextdeleterowinstancenametexttexttexttexttextconfigtext", "chinese": "您确认要删除\"${row.instanceName}\"的链路日志配置吗？", "english": "您确认要删除\"${row.instanceName}\"的链路日志配置吗？", "file": "components\\productServiceConfig\\logSourceConfig.vue"}, {"key": "texttexttextconfigtexttexttexttext", "chinese": "当前已配置链路模型:", "english": "当前已配置链路模型:", "file": "components\\productServiceConfig\\logSourceConfig.vue"}, {"key": "texttextconfig", "chinese": "同步配置", "english": "同步配置", "file": "components\\productServiceConfig\\logSourceConfig.vue"}], "components\\productServiceConfig\\manageFunctionMeta.vue": [{"key": "texttexttexttextdivDivClass", "chinese": ">入参说明：</div>\r\n                    <div class=", "english": ">入参说明：</div>\r\n                    <div class=", "file": "components\\productServiceConfig\\manageFunctionMeta.vue"}, {"key": "texttexttexttextdivDivClass", "chinese": ">入参示例：</div>\r\n                    <div class=", "english": ">入参示例：</div>\r\n                    <div class=", "file": "components\\productServiceConfig\\manageFunctionMeta.vue"}, {"key": "texttexttexttextdivDivClass", "chinese": ">出参说明：</div>\r\n                    <div class=", "english": ">出参说明：</div>\r\n                    <div class=", "file": "components\\productServiceConfig\\manageFunctionMeta.vue"}, {"key": "texttexttexttextdivDivClass", "chinese": ">出参示例：</div>\r\n                    <div class=", "english": ">出参示例：</div>\r\n                    <div class=", "file": "components\\productServiceConfig\\manageFunctionMeta.vue"}, {"key": "texttexttexttext", "chinese": "更新日期：", "english": "更新日期：", "file": "components\\productServiceConfig\\manageFunctionMeta.vue"}, {"key": "childfuncnamecnTexttext", "chinese": "{{  child.funcNameCn || '暂无'}}", "english": "{{  child.funcNameCn || '暂无'}}", "file": "components\\productServiceConfig\\manageFunctionMeta.vue"}, {"key": "texttexttexttext", "chinese": "入参说明：", "english": "入参说明：", "file": "components\\productServiceConfig\\manageFunctionMeta.vue"}, {"key": "texttexttexttext", "chinese": "入参示例：", "english": "入参示例：", "file": "components\\productServiceConfig\\manageFunctionMeta.vue"}, {"key": "texttexttexttext", "chinese": "出参说明：", "english": "出参说明：", "file": "components\\productServiceConfig\\manageFunctionMeta.vue"}, {"key": "texttexttexttext", "chinese": "出参示例：", "english": "出参示例：", "file": "components\\productServiceConfig\\manageFunctionMeta.vue"}], "components\\productTimeAnalysis\\exportFileModal.vue": [{"key": "querytexttextexport", "chinese": "查询结果导出", "english": "查询结果导出", "file": "components\\productTimeAnalysis\\exportFileModal.vue"}, {"key": "exporttexttexttext", "chinese": "导出文件名:", "english": "导出文件名:", "file": "components\\productTimeAnalysis\\exportFileModal.vue"}, {"key": "pleaseinputqueryname", "chinese": "请输入查询名称", "english": "请输入查询名称", "file": "components\\productTimeAnalysis\\exportFileModal.vue"}, {"key": "exporttexttext", "chinese": "导出字段:", "english": "导出字段:", "file": "components\\productTimeAnalysis\\exportFileModal.vue"}, {"key": "savesuccess", "chinese": "保存成功!", "english": "保存成功!", "file": "components\\productTimeAnalysis\\exportFileModal.vue"}], "components\\productTimeAnalysis\\saveModal.vue": [{"key": "savequery", "chinese": "保存查询", "english": "保存查询", "file": "components\\productTimeAnalysis\\saveModal.vue"}, {"key": "queryn<PERSON>", "chinese": "查询名称", "english": "查询名称", "file": "components\\productTimeAnalysis\\saveModal.vue"}, {"key": "texttexttexttexttexttexttexttexttext15", "chinese": "字符长度数不得超过15!", "english": "字符长度数不得超过15!", "file": "components\\productTimeAnalysis\\saveModal.vue"}, {"key": "texttexttexttext", "chinese": "不能为空", "english": "不能为空", "file": "components\\productTimeAnalysis\\saveModal.vue"}, {"key": "texttext", "chinese": "每日", "english": "每日", "file": "components\\productTimeAnalysis\\saveModal.vue"}, {"key": "texttext", "chinese": "昨日", "english": "昨日", "file": "components\\productTimeAnalysis\\saveModal.vue"}, {"key": "texttext", "chinese": "本周", "english": "本周", "file": "components\\productTimeAnalysis\\saveModal.vue"}, {"key": "texttext", "chinese": "本月", "english": "本月", "file": "components\\productTimeAnalysis\\saveModal.vue"}], "components\\rcmBacklogMonitor\\rcmBacklog.vue": [{"key": "texttexttexttexttexttexttextlist", "chinese": "上下文消息积压列表", "english": "上下文消息积压列表", "file": "components\\rcmBacklogMonitor\\rcmBacklog.vue"}, {"key": "texttexttexttexttext0", "chinese": "积压数大于0", "english": "积压数大于0", "file": "components\\rcmBacklogMonitor\\rcmBacklog.vue"}], "components\\rcmDeploy\\rcmConfigModel.vue": [{"key": "texttexttexttextspanDivClass", "chinese": ">主题模板</span>\r\n            <div class=", "english": ">主题模板</span>\r\n            <div class=", "file": "components\\rcmDeploy\\rcmConfigModel.vue"}, {"key": "texttexttexttexttexttexttextspanDivClass", "chinese": ">通用上下文模板</span>\r\n            <div class=", "english": ">通用上下文模板</span>\r\n            <div class=", "file": "components\\rcmDeploy\\rcmConfigModel.vue"}, {"key": "texttexttexttexttexttexttextspanDivClass", "chinese": ">集群上下文模板</span>\r\n            <div class=", "english": ">集群上下文模板</span>\r\n            <div class=", "file": "components\\rcmDeploy\\rcmConfigModel.vue"}, {"key": "texttexttexttext", "chinese": "继承关系", "english": "继承关系", "file": "components\\rcmDeploy\\rcmConfigModel.vue"}, {"key": "texttexttexttexttext", "chinese": "未选中模板", "english": "未选中模板", "file": "components\\rcmDeploy\\rcmConfigModel.vue"}, {"key": "configdetail", "chinese": "配置详情", "english": "配置详情", "file": "components\\rcmDeploy\\rcmConfigModel.vue"}, {"key": "texttextname", "chinese": "模板名称:", "english": "模板名称:", "file": "components\\rcmDeploy\\rcmConfigModel.vue"}, {"key": "texttexttexttext", "chinese": "继承模板:", "english": "继承模板:", "file": "components\\rcmDeploy\\rcmConfigModel.vue"}, {"key": "texttextconfigdata", "chinese": "暂无配置数据", "english": "暂无配置数据", "file": "components\\rcmDeploy\\rcmConfigModel.vue"}, {"key": "texttexttexttexttextsaveType", "chinese": ",   // 模板名单独保存\r\n            type:", "english": ",   // 模板名单独保存\r\n            type:", "file": "components\\rcmDeploy\\rcmConfigModel.vue"}, {"key": "texttexttexttexttext", "chinese": "通用上下文", "english": "通用上下文", "file": "components\\rcmDeploy\\rcmConfigModel.vue"}, {"key": "texttexttexttexttexttextsuccess", "chinese": "主题模板更新成功", "english": "主题模板更新成功", "file": "components\\rcmDeploy\\rcmConfigModel.vue"}, {"key": "texttexttexttexttexttexttextsuccess", "chinese": "上下文模板更新成功", "english": "上下文模板更新成功", "file": "components\\rcmDeploy\\rcmConfigModel.vue"}, {"key": "texttexttextdeletetexttextKeynameTextobjtypetexttext", "chinese": "您确定删除名为 ${keyName} 的${obj[type]}模板？", "english": "您确定删除名为 ${keyName} 的${obj[type]}模板？", "file": "components\\rcmDeploy\\rcmConfigModel.vue"}, {"key": "texttexttexttext", "chinese": "主题模板", "english": "主题模板", "file": "components\\rcmDeploy\\rcmConfigModel.vue"}, {"key": "texttexttexttext", "chinese": "创建模板", "english": "创建模板", "file": "components\\rcmDeploy\\rcmConfigModel.vue"}, {"key": "texttexttexttexttexttexttext", "chinese": "通用上下文模板", "english": "通用上下文模板", "file": "components\\rcmDeploy\\rcmConfigModel.vue"}, {"key": "texttexttexttexttexttexttext", "chinese": "集群上下文模板", "english": "集群上下文模板", "file": "components\\rcmDeploy\\rcmConfigModel.vue"}], "components\\rcmDeploy\\rcmContextGroup.vue": [{"key": "texttexttexttexttexttexttext", "chinese": "上下文实例分组", "english": "上下文实例分组", "file": "components\\rcmDeploy\\rcmContextGroup.vue"}, {"key": "texttexttext", "chinese": "按主题", "english": "按主题", "file": "components\\rcmDeploy\\rcmContextGroup.vue"}, {"key": "texttexttext", "chinese": "按应用", "english": "按应用", "file": "components\\rcmDeploy\\rcmContextGroup.vue"}, {"key": "texttexttext", "chinese": "按标签", "english": "按标签", "file": "components\\rcmDeploy\\rcmContextGroup.vue"}, {"key": "texthcolhcolSpan", "chinese": ">总</h-col><h-col span=", "english": ">总</h-col><h-col span=", "file": "components\\rcmDeploy\\rcmContextGroup.vue"}, {"key": "texthcolhcolSpan", "chinese": ">收</h-col><h-col span=", "english": ">收</h-col><h-col span=", "file": "components\\rcmDeploy\\rcmContextGroup.vue"}, {"key": "texthrowHrowStyle", "chinese": ">总</h-row>\r\n                        <h-row style=", "english": ">总</h-row>\r\n                        <h-row style=", "file": "components\\rcmDeploy\\rcmContextGroup.vue"}, {"key": "texttextdeletetexttexttexttextnametexttexttexttexttexttexttext", "chinese": "确定删除分组名为${name}下的所有上下文？", "english": "确定删除分组名为${name}下的所有上下文？", "file": "components\\rcmDeploy\\rcmContextGroup.vue"}, {"key": "text", "chinese": "总", "english": "总", "file": "components\\rcmDeploy\\rcmContextGroup.vue"}, {"key": "text", "chinese": "收", "english": "收", "file": "components\\rcmDeploy\\rcmContextGroup.vue"}, {"key": "text", "chinese": "发", "english": "发", "file": "components\\rcmDeploy\\rcmContextGroup.vue"}, {"key": "texttexttexttext", "chinese": "查看更多", "english": "查看更多", "file": "components\\rcmDeploy\\rcmContextGroup.vue"}, {"key": "texttextdelete", "chinese": "批量删除", "english": "批量删除", "file": "components\\rcmDeploy\\rcmContextGroup.vue"}], "components\\rcmDeploy\\rcmDeployContext.vue": [{"key": "texttextname", "chinese": "模板名称", "english": "模板名称", "file": "components\\rcmDeploy\\rcmDeployContext.vue"}, {"key": "pleaseinputtexttexttextname", "chinese": "请输入上下文名称", "english": "请输入上下文名称", "file": "components\\rcmDeploy\\rcmDeployContext.vue"}, {"key": "texttexttexttexttext", "chinese": "上下文模式", "english": "上下文模式", "file": "components\\rcmDeploy\\rcmDeployContext.vue"}, {"key": "pleaseinputtexttextname", "chinese": "请输入应用名称", "english": "请输入应用名称", "file": "components\\rcmDeploy\\rcmDeployContext.vue"}, {"key": "querytexttext", "chinese": "查询标签", "english": "查询标签", "file": "components\\rcmDeploy\\rcmDeployContext.vue"}, {"key": "pleaseselectquerytexttext", "chinese": "请选择查询标签", "english": "请选择查询标签", "file": "components\\rcmDeploy\\rcmDeployContext.vue"}, {"key": "texttexttext", "chinese": "中心名", "english": "中心名", "file": "components\\rcmDeploy\\rcmDeployContext.vue"}, {"key": "texttextconfigtexttexttextsuccess", "chinese": "创建/配置上下文成功!", "english": "创建/配置上下文成功!", "file": "components\\rcmDeploy\\rcmDeployContext.vue"}, {"key": "textconfirmtexttexttextdeletetexttexttexttexttexttexttexttext", "chinese": "您确认要批量删除已选中的上下文吗", "english": "您确认要批量删除已选中的上下文吗", "file": "components\\rcmDeploy\\rcmDeployContext.vue"}, {"key": "texttexttexttexttexttexttexttext", "chinese": "所选项未绑定标签", "english": "所选项未绑定标签", "file": "components\\rcmDeploy\\rcmDeployContext.vue"}, {"key": "addtexttext", "chinese": "添加标签", "english": "添加标签", "file": "components\\rcmDeploy\\rcmDeployContext.vue"}, {"key": "deletetexttext", "chinese": "删除标签", "english": "删除标签", "file": "components\\rcmDeploy\\rcmDeployContext.vue"}, {"key": "addtexttextsuccess", "chinese": "添加标签成功!", "english": "添加标签成功!", "file": "components\\rcmDeploy\\rcmDeployContext.vue"}, {"key": "deletetexttextsuccess", "chinese": "删除标签成功!", "english": "删除标签成功!", "file": "components\\rcmDeploy\\rcmDeployContext.vue"}, {"key": "textconfirmtextdeletetexttextparamsrownametexttexttexttexttext", "chinese": "您确认要删除名为\"${params.row.name}\"的上下文吗？", "english": "您确认要删除名为\"${params.row.name}\"的上下文吗？", "file": "components\\rcmDeploy\\rcmDeployContext.vue"}, {"key": "texttextdeletetexttexttexttexttexttexttextthistotaltext", "chinese": "同时删除所有匹配记录（共${this.total}条）", "english": "同时删除所有匹配记录（共${this.total}条）", "file": "components\\rcmDeploy\\rcmDeployContext.vue"}], "components\\rcmDeploy\\rcmDeployTopic.vue": [{"key": "pleaseinputtext165535texttexttexttext", "chinese": "请输入在1-65535之间的值", "english": "请输入在1-65535之间的值", "file": "components\\rcmDeploy\\rcmDeployTopic.vue"}, {"key": "texttextname", "chinese": "主题名称", "english": "主题名称", "file": "components\\rcmDeploy\\rcmDeployTopic.vue"}, {"key": "pleaseinputtexttextname", "chinese": "请输入主题名称", "english": "请输入主题名称", "file": "components\\rcmDeploy\\rcmDeployTopic.vue"}, {"key": "pleaseinputtexttexttext", "chinese": "请输入分区号", "english": "请输入分区号", "file": "components\\rcmDeploy\\rcmDeployTopic.vue"}, {"key": "texttexttexttexttexttexttext", "chinese": "只支持输入数字", "english": "只支持输入数字", "file": "components\\rcmDeploy\\rcmDeployTopic.vue"}, {"key": "texttexttexttext", "chinese": "通讯地址", "english": "通讯地址", "file": "components\\rcmDeploy\\rcmDeployTopic.vue"}, {"key": "texttexttexttext", "chinese": "通讯端口", "english": "通讯端口", "file": "components\\rcmDeploy\\rcmDeployTopic.vue"}, {"key": "texttextconfigtexttextsuccess", "chinese": "创建/配置主题成功!", "english": "创建/配置主题成功!", "file": "components\\rcmDeploy\\rcmDeployTopic.vue"}, {"key": "pleaseselecttexttexttexttexttextdata", "chinese": "请选择一条或多条数据", "english": "请选择一条或多条数据", "file": "components\\rcmDeploy\\rcmDeployTopic.vue"}, {"key": "texttextdeletetexttext", "chinese": "批量删除主题", "english": "批量删除主题", "file": "components\\rcmDeploy\\rcmDeployTopic.vue"}, {"key": "textconfirmtexttexttextdeletetexttexttexttexttexttexttext", "chinese": "您确认要批量删除已选中的主题吗？", "english": "您确认要批量删除已选中的主题吗？", "file": "components\\rcmDeploy\\rcmDeployTopic.vue"}, {"key": "textconfirmtextdeletetexttextparamsrowtopictexttexttexttext", "chinese": "您确认要删除名为\"${params.row.topic}\"的主题吗？", "english": "您确认要删除名为\"${params.row.topic}\"的主题吗？", "file": "components\\rcmDeploy\\rcmDeployTopic.vue"}], "components\\rcmDeploy\\rcmOverview.vue": [{"key": "texttext", "chinese": "端口", "english": "端口", "file": "components\\rcmDeploy\\rcmOverview.vue"}, {"key": "textconfirmtextdeletetexttextconfigtext", "chinese": "您确认要删除该行配置吗？", "english": "您确认要删除该行配置吗？", "file": "components\\rcmDeploy\\rcmOverview.vue"}, {"key": "texttexttexttext", "chinese": "传输地址：", "english": "传输地址：", "file": "components\\rcmDeploy\\rcmOverview.vue"}, {"key": "texttexttexttext", "chinese": "传输端口：", "english": "传输端口：", "file": "components\\rcmDeploy\\rcmOverview.vue"}, {"key": "texttexttexttexttexttext", "chinese": "集群同步地址：", "english": "集群同步地址：", "file": "components\\rcmDeploy\\rcmOverview.vue"}, {"key": "texttexttexttexttexttext", "chinese": "集群同步端口：", "english": "集群同步端口：", "file": "components\\rcmDeploy\\rcmOverview.vue"}, {"key": "texttexttexttext", "chinese": "补缺端口：", "english": "补缺端口：", "file": "components\\rcmDeploy\\rcmOverview.vue"}, {"key": "texttexttextidtexttext", "chinese": "上下文ID范围：", "english": "上下文ID范围：", "file": "components\\rcmDeploy\\rcmOverview.vue"}, {"key": "texttextidtexttext", "chinese": "主题ID范围：", "english": "主题ID范围：", "file": "components\\rcmDeploy\\rcmOverview.vue"}, {"key": "texttexttexttext", "chinese": "分区范围：", "english": "分区范围：", "file": "components\\rcmDeploy\\rcmOverview.vue"}, {"key": "add", "chinese": "新增", "english": "新增", "file": "components\\rcmDeploy\\rcmOverview.vue"}], "components\\rcmDeploy\\rcmSourceFile.vue": [{"key": "texttexttextconfig", "chinese": "待发布配置", "english": "待发布配置", "file": "components\\rcmDeploy\\rcmSourceFile.vue"}, {"key": "texttexttextconfig", "chinese": "已发布配置", "english": "已发布配置", "file": "components\\rcmDeploy\\rcmSourceFile.vue"}, {"key": "texttexttextconfigtexttexttexttext", "chinese": "已发布配置告警说明", "english": "已发布配置告警说明", "file": "components\\rcmDeploy\\rcmSourceFile.vue"}, {"key": "texttextIClass", "chinese": ">远程：\r\n                    <i :class=", "english": ">远程：\r\n                    <i :class=", "file": "components\\rcmDeploy\\rcmSourceFile.vue"}, {"key": "texttextIClass", "chinese": ">历史：\r\n                    <i :class=", "english": ">历史：\r\n                    <i :class=", "file": "components\\rcmDeploy\\rcmSourceFile.vue"}, {"key": "texttexttexttexttexttextrcmconfigtexttexttexttexttexttexttexttexttexttexttexttexttexttext", "chinese": "无法在线预览RCM配置文件！请下载源文件进行本地查看。", "english": "无法在线预览RCM配置文件！请下载源文件进行本地查看。", "file": "components\\rcmDeploy\\rcmSourceFile.vue"}, {"key": "texttexttexttext", "chinese": "服务异常", "english": "服务异常", "file": "components\\rcmDeploy\\rcmSourceFile.vue"}, {"key": "configtexttext", "chinese": "配置发布", "english": "配置发布", "file": "components\\rcmDeploy\\rcmSourceFile.vue"}, {"key": "configtexttext", "chinese": "配置还原", "english": "配置还原", "file": "components\\rcmDeploy\\rcmSourceFile.vue"}, {"key": "texttexttexttexttexttextconfigtexttexttexttexttexttexttexttext", "chinese": "本地变更：本地配置版本已经发生变更。", "english": "本地变更：本地配置版本已经发生变更。", "file": "components\\rcmDeploy\\rcmSourceFile.vue"}, {"key": "texttexttexttexttexttextzkconfigtexttexttexttexttexttexttext", "chinese": "远程变更：远程zk配置版本已发生变更。", "english": "远程变更：远程zk配置版本已发生变更。", "file": "components\\rcmDeploy\\rcmSourceFile.vue"}, {"key": "texttext", "chinese": "还原", "english": "还原", "file": "components\\rcmDeploy\\rcmSourceFile.vue"}, {"key": "texttext", "chinese": "本地：", "english": "本地：", "file": "components\\rcmDeploy\\rcmSourceFile.vue"}, {"key": "texttexttexttexttexttext", "chinese": "最后更新版本：", "english": "最后更新版本：", "file": "components\\rcmDeploy\\rcmSourceFile.vue"}, {"key": "texttext", "chinese": "远程：", "english": "远程：", "file": "components\\rcmDeploy\\rcmSourceFile.vue"}, {"key": "texttext", "chinese": "历史：", "english": "历史：", "file": "components\\rcmDeploy\\rcmSourceFile.vue"}], "components\\rcmObservation\\deliver.vue": [{"key": "texttexttexttextcontextid", "chinese": "发送端的ContextId", "english": "发送端的ContextId", "file": "components\\rcmObservation\\deliver.vue"}, {"key": "texttexttexttextcontextid", "chinese": "接收端的ContextId", "english": "接收端的ContextId", "file": "components\\rcmObservation\\deliver.vue"}, {"key": "texttexttexttext", "chinese": "消息排队", "english": "消息排队", "file": "components\\rcmObservation\\deliver.vue"}, {"key": "texttexttexttext", "chinese": "消息投递", "english": "消息投递", "file": "components\\rcmObservation\\deliver.vue"}, {"key": "texttexttexttext", "chinese": "缓存积压", "english": "缓存积压", "file": "components\\rcmObservation\\deliver.vue"}, {"key": "texttexttexttexttext", "chinese": "消息持久化", "english": "消息持久化", "file": "components\\rcmObservation\\deliver.vue"}, {"key": "texttexttexttexttexttexttexttexttext", "chinese": "下一个排队消息序号", "english": "下一个排队消息序号", "file": "components\\rcmObservation\\deliver.vue"}, {"key": "submittexttexttexttexttext", "chinese": "提交的消息序号", "english": "提交的消息序号", "file": "components\\rcmObservation\\deliver.vue"}, {"key": "texttexttexttexttexttexttexttexttext", "chinese": "下一个处理消息序号", "english": "下一个处理消息序号", "file": "components\\rcmObservation\\deliver.vue"}, {"key": "texttexttexttexttexttexttexttexttext", "chinese": "已经处理的消息数量", "english": "已经处理的消息数量", "file": "components\\rcmObservation\\deliver.vue"}, {"key": "texttexttexttextfailedtexttexttexttext", "chinese": "消息回调失败次数统计", "english": "消息回调失败次数统计", "file": "components\\rcmObservation\\deliver.vue"}, {"key": "texttextconfirmtexttexttexttext", "chinese": "集群确认消息积压", "english": "集群确认消息积压", "file": "components\\rcmObservation\\deliver.vue"}, {"key": "texttexttexttexttexttexttexttext", "chinese": "应用投递消息积压", "english": "应用投递消息积压", "file": "components\\rcmObservation\\deliver.vue"}, {"key": "texttexttexttexttexttexttexttext", "chinese": "待持久化消息积压", "english": "待持久化消息积压", "file": "components\\rcmObservation\\deliver.vue"}, {"key": "texttexttexttexttexttexttexttexttexttext", "chinese": "下一个持久化消息序号", "english": "下一个持久化消息序号", "file": "components\\rcmObservation\\deliver.vue"}, {"key": "texttexttexttexttexttexttexttexttexttext", "chinese": "已经持久化的消息数量", "english": "已经持久化的消息数量", "file": "components\\rcmObservation\\deliver.vue"}], "components\\rcmObservation\\receiver.vue": [{"key": "texttextinfoInfogridRef", "chinese": ">\r\n    <!-- 会话信息 -->\r\n    <info-grid ref=", "english": ">\r\n    <!-- 会话信息 -->\r\n    <info-grid ref=", "file": "components\\rcmObservation\\receiver.vue"}, {"key": "texttextinfo", "chinese": "会话信息", "english": "会话信息", "file": "components\\rcmObservation\\receiver.vue"}, {"key": "texttexttexttext", "chinese": "主题分区", "english": "主题分区", "file": "components\\rcmObservation\\receiver.vue"}, {"key": "texttexttexttext", "chinese": "消息接收", "english": "消息接收", "file": "components\\rcmObservation\\receiver.vue"}, {"key": "texttexttexttext", "chinese": "消息应答", "english": "消息应答", "file": "components\\rcmObservation\\receiver.vue"}, {"key": "texttexttexttexttexttexttexttexttexttext", "chinese": "下一个待接收消息序号", "english": "下一个待接收消息序号", "file": "components\\rcmObservation\\receiver.vue"}, {"key": "texttexttexttexttexttexttexttexttext", "chinese": "收到的最大消息序号", "english": "收到的最大消息序号", "file": "components\\rcmObservation\\receiver.vue"}, {"key": "texttexttexttexttexttexttexttexttexttext", "chinese": "下一个待处理消息序号", "english": "下一个待处理消息序号", "file": "components\\rcmObservation\\receiver.vue"}, {"key": "texttextconfirmtexttexttexttexttext", "chinese": "已经确认的消息序号", "english": "已经确认的消息序号", "file": "components\\rcmObservation\\receiver.vue"}, {"key": "texttexttexttexttexttext", "chinese": "通讯分片处理", "english": "通讯分片处理", "file": "components\\rcmObservation\\receiver.vue"}, {"key": "texttexttexttext", "chinese": "分片接收", "english": "分片接收", "file": "components\\rcmObservation\\receiver.vue"}, {"key": "texttexttexttext", "chinese": "分片应答", "english": "分片应答", "file": "components\\rcmObservation\\receiver.vue"}, {"key": "texttexttexttext", "chinese": "分片补缺", "english": "分片补缺", "file": "components\\rcmObservation\\receiver.vue"}, {"key": "texttexttexttexttexttexttext", "chinese": "乱序和丢包检测", "english": "乱序和丢包检测", "file": "components\\rcmObservation\\receiver.vue"}, {"key": "texttexttexttexttexttexttexttexttexttext", "chinese": "下一个待接收分片序号", "english": "下一个待接收分片序号", "file": "components\\rcmObservation\\receiver.vue"}, {"key": "texttexttexttextcanceltexttexttexttexttext", "chinese": "下一个待取消息分片序号", "english": "下一个待取消息分片序号", "file": "components\\rcmObservation\\receiver.vue"}, {"key": "texttexttexttexttexttexttexttexttext", "chinese": "收到的最大分片序号", "english": "收到的最大分片序号", "file": "components\\rcmObservation\\receiver.vue"}, {"key": "texttextconfirmtexttexttexttext", "chinese": "已经确认的分片号", "english": "已经确认的分片号", "file": "components\\rcmObservation\\receiver.vue"}, {"key": "texttexttexttextacktexttexttext", "chinese": "最后发送ACK的原因", "english": "最后发送ACK的原因", "file": "components\\rcmObservation\\receiver.vue"}, {"key": "texttexttexttext", "chinese": "补缺执行", "english": "补缺执行", "file": "components\\rcmObservation\\receiver.vue"}, {"key": "texttexttexttext", "chinese": "补缺统计", "english": "补缺统计", "file": "components\\rcmObservation\\receiver.vue"}, {"key": "texttexttexttext", "chinese": "丢包检测", "english": "丢包检测", "file": "components\\rcmObservation\\receiver.vue"}, {"key": "texttexttexttext", "chinese": "丢包统计", "english": "丢包统计", "file": "components\\rcmObservation\\receiver.vue"}], "components\\rcmObservation\\transmitters.vue": [{"key": "texttextinfoInfogridRef", "chinese": ">\r\n        <!-- 会话信息 -->\r\n        <info-grid ref=", "english": ">\r\n        <!-- 会话信息 -->\r\n        <info-grid ref=", "file": "components\\rcmObservation\\transmitters.vue"}, {"key": "texttexttexttexttexttexttext", "chinese": "消息发送与应答", "english": "消息发送与应答", "file": "components\\rcmObservation\\transmitters.vue"}, {"key": "texttexttexttexttexttext", "chinese": "下个消息编号", "english": "下个消息编号", "file": "components\\rcmObservation\\transmitters.vue"}, {"key": "texttexttexttexttexttexttext", "chinese": "当前最大消息号", "english": "当前最大消息号", "file": "components\\rcmObservation\\transmitters.vue"}, {"key": "texttexttexttexttexttexttexttext", "chinese": "上次应答消息编号", "english": "上次应答消息编号", "file": "components\\rcmObservation\\transmitters.vue"}, {"key": "texttexttexttexttexttexttexttext", "chinese": "消息缓存与持久化", "english": "消息缓存与持久化", "file": "components\\rcmObservation\\transmitters.vue"}, {"key": "texttexttextsavetexttexttexttexttexttexttexttexttext", "chinese": "缓存中保存的消息的最小消息号", "english": "缓存中保存的消息的最小消息号", "file": "components\\rcmObservation\\transmitters.vue"}, {"key": "texttexttexttexttexttexttexttexttexttexttext", "chinese": "下一个待持久化的消息号", "english": "下一个待持久化的消息号", "file": "components\\rcmObservation\\transmitters.vue"}, {"key": "texttexttexttexttexttexttext", "chinese": "已持久化消息数", "english": "已持久化消息数", "file": "components\\rcmObservation\\transmitters.vue"}, {"key": "texttexttexttext", "chinese": "消息积压", "english": "消息积压", "file": "components\\rcmObservation\\transmitters.vue"}, {"key": "texttexttexttexttexttexttext", "chinese": "缓存积压消息数", "english": "缓存积压消息数", "file": "components\\rcmObservation\\transmitters.vue"}, {"key": "texttexttexttexttexttexttext", "chinese": "网络积压消息数", "english": "网络积压消息数", "file": "components\\rcmObservation\\transmitters.vue"}, {"key": "texttexttexttexttexttext", "chinese": "对端消息应答", "english": "对端消息应答", "file": "components\\rcmObservation\\transmitters.vue"}, {"key": "texttexttexttexttexttexttext", "chinese": "分片发送与应答", "english": "分片发送与应答", "file": "components\\rcmObservation\\transmitters.vue"}, {"key": "texttexttexttexttexttexttexttexttexttext", "chinese": "下一个待分配的分片号", "english": "下一个待分配的分片号", "file": "components\\rcmObservation\\transmitters.vue"}, {"key": "texttexttexttexttexttexttexttext", "chinese": "当前待发送分片号", "english": "当前待发送分片号", "file": "components\\rcmObservation\\transmitters.vue"}, {"key": "texttexttexttexttexttexttexttexttexttexttext", "chinese": "最后一次收到的应答序号", "english": "最后一次收到的应答序号", "file": "components\\rcmObservation\\transmitters.vue"}, {"key": "texttexttexttexttexttext", "chinese": "分片发送统计", "english": "分片发送统计", "file": "components\\rcmObservation\\transmitters.vue"}, {"key": "texttexttexttexttexttexttexttexttext", "chinese": "异步发送的分片数目", "english": "异步发送的分片数目", "file": "components\\rcmObservation\\transmitters.vue"}, {"key": "texttextfailedtexttexttexttexttext", "chinese": "底层失败的分片数目", "english": "底层失败的分片数目", "file": "components\\rcmObservation\\transmitters.vue"}, {"key": "texttexttexttexttexttext", "chinese": "对端分片应答", "english": "对端分片应答", "file": "components\\rcmObservation\\transmitters.vue"}], "components\\secondAppearance\\logDrawerContent.vue": [{"key": "texttexttexttexttexttext", "chinese": "上场记录总数：", "english": "上场记录总数：", "file": "components\\secondAppearance\\logDrawerContent.vue"}, {"key": "successtexttexttext", "chinese": "成功记录数：", "english": "成功记录数：", "file": "components\\secondAppearance\\logDrawerContent.vue"}, {"key": "failedtexttexttext", "chinese": "失败记录数：", "english": "失败记录数：", "file": "components\\secondAppearance\\logDrawerContent.vue"}, {"key": "errorinfo", "chinese": "错误信息：", "english": "错误信息：", "file": "components\\secondAppearance\\logDrawerContent.vue"}], "components\\sms\\addOrUpdateManagerModal.vue": [{"key": "modaldatatypeEdittexttexttextinfoAddtexttexttexttexttext", "chinese": "modalData.type ? '修改干系人信息' : '添加干系人信息'", "english": "modalData.type ? '修改干系人信息' : '添加干系人信息'", "file": "components\\sms\\addOrUpdateManagerModal.vue"}, {"key": "pleaseinput", "chinese": "请输入...", "english": "请输入...", "file": "components\\sms\\addOrUpdateManagerModal.vue"}, {"key": "edittexttexttextinfo", "chinese": "修改干系人信息", "english": "修改干系人信息", "file": "components\\sms\\addOrUpdateManagerModal.vue"}, {"key": "addtexttexttextinfo", "chinese": "添加干系人信息", "english": "添加干系人信息", "file": "components\\sms\\addOrUpdateManagerModal.vue"}, {"key": "texttexttexttexttexttexttexttext", "chinese": "手机号格式不正确", "english": "手机号格式不正确", "file": "components\\sms\\addOrUpdateManagerModal.vue"}, {"key": "texttexttexttexttexttext", "chinese": "邮箱不能为空", "english": "邮箱不能为空", "file": "components\\sms\\addOrUpdateManagerModal.vue"}, {"key": "texttexttexttexttexttexttext", "chinese": "邮箱格式不正确", "english": "邮箱格式不正确", "file": "components\\sms\\addOrUpdateManagerModal.vue"}, {"key": "addfailed", "chinese": "添加失败", "english": "添加失败", "file": "components\\sms\\addOrUpdateManagerModal.vue"}, {"key": "editfailed", "chinese": "修改失败", "english": "修改失败", "file": "components\\sms\\addOrUpdateManagerModal.vue"}], "components\\sms\\noticeTempModal.vue": [{"key": "texttexttexttexttexttextedit", "chinese": "告警通知模板编辑", "english": "告警通知模板编辑", "file": "components\\sms\\noticeTempModal.vue"}, {"key": "texttexttexttext", "chinese": "模板内容", "english": "模板内容", "file": "components\\sms\\noticeTempModal.vue"}, {"key": "texttexttexttexttext", "chinese": "外发投资者", "english": "外发投资者", "file": "components\\sms\\noticeTempModal.vue"}, {"key": "texttexttexttexttext", "chinese": "选择干系人", "english": "选择干系人", "file": "components\\sms\\noticeTempModal.vue"}, {"key": "texttexttexttexttext", "chinese": "通知到手机", "english": "通知到手机", "file": "components\\sms\\noticeTempModal.vue"}, {"key": "texttexttexttexttext", "chinese": "通知到邮箱", "english": "通知到邮箱", "file": "components\\sms\\noticeTempModal.vue"}, {"key": "operationsuccess", "chinese": "操作成功", "english": "操作成功", "file": "components\\sms\\noticeTempModal.vue"}, {"key": "operationfailed", "chinese": "操作失败", "english": "操作失败", "file": "components\\sms\\noticeTempModal.vue"}, {"key": "texttexttextdeletetexttextparamsrowaddresseenametexttexttexttexttext", "chinese": "您确定删除名为\"${params.row.addresseeName}\"的干系人吗？", "english": "您确定删除名为\"${params.row.addresseeName}\"的干系人吗？", "file": "components\\sms\\noticeTempModal.vue"}, {"key": "texttexttexttexttexttexttexttexttexttexttextphoneTexttexttexttexttexttexttexttexttexttexttext", "chinese": "告警通知参数存在手机号${phone}, 则发送模板短信至该手机", "english": "告警通知参数存在手机号${phone}, 则发送模板短信至该手机", "file": "components\\sms\\noticeTempModal.vue"}], "components\\sqlTable\\tableSqlTop.vue": [{"key": "iscoresPleaseselecttexttexttexttexttexttexttexttextTexttexttexttexttext", "chinese": "!isCores ? '请选择节点或集群或服务' : '请选择节点'", "english": "!isCores ? '请选择节点或集群或服务' : '请选择节点'", "file": "components\\sqlTable\\tableSqlTop.vue"}, {"key": "titleIscoresMdbsqltexttexttexttexttextmdbsql", "chinese": "title || (isCores ? 'MDB-SQL-多核心分发':'MDB-SQL')", "english": "title || (isCores ? 'MDB-SQL-多核心分发':'MDB-SQL')", "file": "components\\sqlTable\\tableSqlTop.vue"}, {"key": "pleaseselecttexttexttexttexttexttexttexttext", "chinese": "请选择节点或集群或服务", "english": "请选择节点或集群或服务", "file": "components\\sqlTable\\tableSqlTop.vue"}, {"key": "mdbsqltexttexttexttexttext", "chinese": "MDB-SQL-多核心分发", "english": "MDB-SQL-多核心分发", "file": "components\\sqlTable\\tableSqlTop.vue"}, {"key": "texttextsuccess", "chinese": "登录成功!", "english": "登录成功!", "file": "components\\sqlTable\\tableSqlTop.vue"}, {"key": "texttexttexttext", "chinese": "未知用户", "english": "未知用户", "file": "components\\sqlTable\\tableSqlTop.vue"}, {"key": "texttexttexttexttext", "chinese": "请重新登录!", "english": "请重新登录!", "file": "components\\sqlTable\\tableSqlTop.vue"}, {"key": "edittexttextsuccesstexttexttexttexttext", "chinese": "修改密码成功!请重新登录!", "english": "修改密码成功!请重新登录!", "file": "components\\sqlTable\\tableSqlTop.vue"}, {"key": "texttextCase", "chinese": "; // 服务\r\n                case", "english": "; // 服务\r\n                case", "file": "components\\sqlTable\\tableSqlTop.vue"}, {"key": "texttext", "chinese": "登录", "english": "登录", "file": "components\\sqlTable\\tableSqlTop.vue"}, {"key": "texttexttexttext", "chinese": "查看密钥", "english": "查看密钥", "file": "components\\sqlTable\\tableSqlTop.vue"}, {"key": "edittexttext", "chinese": "修改密码", "english": "修改密码", "file": "components\\sqlTable\\tableSqlTop.vue"}, {"key": "texttexttexttext", "chinese": "退出登录", "english": "退出登录", "file": "components\\sqlTable\\tableSqlTop.vue"}], "components\\transaction\\instanceTimeDelayModal.vue": [{"key": "modaldatakeytexttextdatalist", "chinese": "`${modalData.key}时延数据列表`", "english": "`${modalData.key}时延数据列表`", "file": "components\\transaction\\instanceTimeDelayModal.vue"}, {"key": "modaldatakeytexttextdatalist", "chinese": "${modalData.key}时延数据列表", "english": "${modalData.key}时延数据列表", "file": "components\\transaction\\instanceTimeDelayModal.vue"}], "components\\transaction\\reportConfirmModal.vue": [{"key": "texttexttexttext", "chinese": "创建报表", "english": "创建报表", "file": "components\\transaction\\reportConfirmModal.vue"}, {"key": "h3texttext", "chinese": ">\r\n                <h3>报表", "english": ">\r\n                <h3>报表", "file": "components\\transaction\\reportConfirmModal.vue"}, {"key": "texttexttexttextfailed", "chinese": "报表创建失败!", "english": "报表创建失败!", "file": "components\\transaction\\reportConfirmModal.vue"}, {"key": "texttextinstancenametexttextsuccess", "chinese": "报表\"{{instanceName}}\"创建成功!", "english": "报表\"{{instanceName}}\"创建成功!", "file": "components\\transaction\\reportConfirmModal.vue"}, {"key": "texttexttext", "chinese": "交易所：", "english": "交易所：", "file": "components\\transaction\\reportConfirmModal.vue"}, {"key": "texttexttext", "chinese": "席位号：", "english": "席位号：", "file": "components\\transaction\\reportConfirmModal.vue"}, {"key": "starttime", "chinese": "开始时间：", "english": "开始时间：", "file": "components\\transaction\\reportConfirmModal.vue"}, {"key": "endtime", "chinese": "结束时间：", "english": "结束时间：", "file": "components\\transaction\\reportConfirmModal.vue"}, {"key": "instanceidTexttextTexttext", "chinese": "{{instanceId ? '查看' :\r\n                    '创建'}}", "english": "{{instanceId ? '查看' :\r\n                    '创建'}}", "file": "components\\transaction\\reportConfirmModal.vue"}], "components\\transaction\\settingModal.vue": [{"key": "texttexttexttext", "chinese": "统计分析", "english": "统计分析", "file": "components\\transaction\\settingModal.vue"}, {"key": "texttexttexttext", "chinese": "选择市场", "english": "选择市场", "file": "components\\transaction\\settingModal.vue"}, {"key": "texttexttexttext", "chinese": "选择链路", "english": "选择链路", "file": "components\\transaction\\settingModal.vue"}, {"key": "texttexttexttext", "chinese": "选择指标", "english": "选择指标", "file": "components\\transaction\\settingModal.vue"}, {"key": "pleaseinputtexttexttext", "chinese": "请输入席位号", "english": "请输入席位号", "file": "components\\transaction\\settingModal.vue"}, {"key": "pleaseinputtexttexttexttext", "chinese": "请输入资金账号", "english": "请输入资金账号", "file": "components\\transaction\\settingModal.vue"}, {"key": "texttexttexttexttext", "chinese": "分位数指标", "english": "分位数指标", "file": "components\\transaction\\settingModal.vue"}, {"key": "texttexttexttext", "chinese": "同比分析", "english": "同比分析", "file": "components\\transaction\\settingModal.vue"}, {"key": "texttexttexttext", "chinese": "基线日期", "english": "基线日期", "file": "components\\transaction\\settingModal.vue"}, {"key": "texttexttime", "chinese": "基线时间", "english": "基线时间", "file": "components\\transaction\\settingModal.vue"}, {"key": "texttexttexttext", "chinese": "同比日期", "english": "同比日期", "file": "components\\transaction\\settingModal.vue"}, {"key": "texttexttime", "chinese": "同比时间", "english": "同比时间", "file": "components\\transaction\\settingModal.vue"}, {"key": "texttexttexttext", "chinese": "每秒平均", "english": "每秒平均", "file": "components\\transaction\\settingModal.vue"}, {"key": "texttexttexttexttexttexttexttexttexttexttexttexttexttextdata", "chinese": "分析指标（最多只能勾选三个指标数据）", "english": "分析指标（最多只能勾选三个指标数据）", "file": "components\\transaction\\settingModal.vue"}], "components\\transaction\\topListModal.vue": [{"key": "texttexttexttexttexttext", "chinese": "每秒时延订单", "english": "每秒时延订单", "file": "components\\transaction\\topListModal.vue"}, {"key": "texttextS", "chinese": "时延 (μs)", "english": "时延 (μs)", "file": "components\\transaction\\topListModal.vue"}, {"key": "texttexttexttext", "chinese": "时延订单", "english": "时延订单", "file": "components\\transaction\\topListModal.vue"}], "components\\tripartiteServiceConfig\\eccomServiceConfig.vue": [{"key": "texttextdatatexttext", "chinese": "华讯数据服务", "english": "华讯数据服务", "file": "components\\tripartiteServiceConfig\\eccomServiceConfig.vue"}, {"key": "configtexttexttexttexttexttext", "chinese": "配置服务接入代理", "english": "配置服务接入代理", "file": "components\\tripartiteServiceConfig\\eccomServiceConfig.vue"}, {"key": "texttexttexttexttext", "chinese": "采集器实例", "english": "采集器实例", "file": "components\\tripartiteServiceConfig\\eccomServiceConfig.vue"}, {"key": "texttexttexttexttexttext", "chinese": "接入服务类型", "english": "接入服务类型", "file": "components\\tripartiteServiceConfig\\eccomServiceConfig.vue"}, {"key": "texttexttexttexttexttext", "chinese": "接入服务地址", "english": "接入服务地址", "file": "components\\tripartiteServiceConfig\\eccomServiceConfig.vue"}, {"key": "topictexttext", "chinese": "Topic地址", "english": "Topic地址", "file": "components\\tripartiteServiceConfig\\eccomServiceConfig.vue"}, {"key": "texttexttexttexttexttexttexttext", "chinese": "已适配的业务系统", "english": "已适配的业务系统", "file": "components\\tripartiteServiceConfig\\eccomServiceConfig.vue"}, {"key": "texttexttexttexttexttext", "chinese": "绑定产品节点", "english": "绑定产品节点", "file": "components\\tripartiteServiceConfig\\eccomServiceConfig.vue"}, {"key": "texttextdatatexttexttexttext", "chinese": "创建数据采集代理", "english": "创建数据采集代理", "file": "components\\tripartiteServiceConfig\\eccomServiceConfig.vue"}, {"key": "editdatatexttexttexttext", "chinese": "修改数据采集代理", "english": "修改数据采集代理", "file": "components\\tripartiteServiceConfig\\eccomServiceConfig.vue"}, {"key": "addsuccess", "chinese": "新增成功!", "english": "新增成功!", "file": "components\\tripartiteServiceConfig\\eccomServiceConfig.vue"}, {"key": "editsuccess", "chinese": "修改成功!", "english": "修改成功!", "file": "components\\tripartiteServiceConfig\\eccomServiceConfig.vue"}, {"key": "texttexttextdeleteParamsrowinstancenameTexttext", "chinese": "您确定删除 '${params.row?.instanceName}' 实例？", "english": "您确定删除 '${params.row?.instanceName}' 实例？", "file": "components\\tripartiteServiceConfig\\eccomServiceConfig.vue"}], "components\\ustTableVerification\\createVerificationTask.vue": [{"key": "texttexttexttext", "chinese": "校验范围", "english": "校验范围", "file": "components\\ustTableVerification\\createVerificationTask.vue"}, {"key": "texttexttexttexttext", "chinese": "集群内校验", "english": "集群内校验", "file": "components\\ustTableVerification\\createVerificationTask.vue"}, {"key": "textdatatext", "chinese": "源数据库", "english": "源数据库", "file": "components\\ustTableVerification\\createVerificationTask.vue"}, {"key": "texttextdatatext", "chinese": "目标数据库", "english": "目标数据库", "file": "components\\ustTableVerification\\createVerificationTask.vue"}, {"key": "texttexttexttext", "chinese": "校验类型", "english": "校验类型", "file": "components\\ustTableVerification\\createVerificationTask.vue"}, {"key": "texttexttexttext", "chinese": "按表总量", "english": "按表总量", "file": "components\\ustTableVerification\\createVerificationTask.vue"}, {"key": "texttexttexttext", "chinese": "按表字段", "english": "按表字段", "file": "components\\ustTableVerification\\createVerificationTask.vue"}, {"key": "texttexttexttext", "chinese": "校验内容", "english": "校验内容", "file": "components\\ustTableVerification\\createVerificationTask.vue"}, {"key": "errortexttext", "chinese": "错误阈值", "english": "错误阈值", "file": "components\\ustTableVerification\\createVerificationTask.vue"}, {"key": "texttexttexttext", "chinese": "校验字段", "english": "校验字段", "file": "components\\ustTableVerification\\createVerificationTask.vue"}, {"key": "formitemsthresholdunitText", "chinese": "formItems.thresholdUnit === '个'", "english": "formItems.thresholdUnit === '个'", "file": "components\\ustTableVerification\\createVerificationTask.vue"}, {"key": "texttexttexttexttexttextdatatexttextaddTabledatalengthTexttexttexttexttexttexttexttext", "chinese": "`根据所选校验数据，将会新增${ tableData.length }条校验任务，请核对`", "english": "`根据所选校验数据，将会新增${ tableData.length }条校验任务，请核对`", "file": "components\\ustTableVerification\\createVerificationTask.vue"}, {"key": "texttexttexttexttexttexttexttext11000texttexttexttext", "chinese": "输入框输入范围为1~1000的正整数", "english": "输入框输入范围为1~1000的正整数", "file": "components\\ustTableVerification\\createVerificationTask.vue"}, {"key": "texttexttexttextdata", "chinese": "选择校验数据", "english": "选择校验数据", "file": "components\\ustTableVerification\\createVerificationTask.vue"}, {"key": "texttexttexttexttexttexttext", "chinese": "至少选择一张表", "english": "至少选择一张表", "file": "components\\ustTableVerification\\createVerificationTask.vue"}, {"key": "texttexttext", "chinese": "表总量", "english": "表总量", "file": "components\\ustTableVerification\\createVerificationTask.vue"}, {"key": "texttexttext", "chinese": "表字段", "english": "表字段", "file": "components\\ustTableVerification\\createVerificationTask.vue"}, {"key": "texttext", "chinese": "任务", "english": "任务", "file": "components\\ustTableVerification\\createVerificationTask.vue"}, {"key": "texttexttexttexttexttextdatatexttextaddTabledatalengthTexttexttexttexttexttexttexttext", "chinese": "根据所选校验数据，将会新增${ tableData.length }条校验任务，请核对", "english": "根据所选校验数据，将会新增${ tableData.length }条校验任务，请核对", "file": "components\\ustTableVerification\\createVerificationTask.vue"}, {"key": "textdatatexttexttexttexttexttexttexttexttextlabeltexttexttexttexttextedit", "chinese": "源数据库与目标数据库选择\"${label}\"节点重复，请修改", "english": "源数据库与目标数据库选择\"${label}\"节点重复，请修改", "file": "components\\ustTableVerification\\createVerificationTask.vue"}, {"key": "texttexttexttexttexttexttextsettingerrortexttexttexttexttexttexttexttexttexttexttexttexttexttexttexttexttexttextdatatexttext", "chinese": "“按表总量校验”不设置错误阈值。即不论校验过程是否出错，均完成全量数据校验", "english": "“按表总量校验”不设置错误阈值。即不论校验过程是否出错，均完成全量数据校验", "file": "components\\ustTableVerification\\createVerificationTask.vue"}, {"key": "texterrortexttext", "chinese": "按错误个数", "english": "按错误个数", "file": "components\\ustTableVerification\\createVerificationTask.vue"}, {"key": "texttexttexttexttexttexttexttexttexttexttexttexttexttexttexttexttexttexttexttexttexttexttexttexttexttexttext", "chinese": "当表字段不一致个数达到阈值时，校验任务自动停止，校验不通过", "english": "当表字段不一致个数达到阈值时，校验任务自动停止，校验不通过", "file": "components\\ustTableVerification\\createVerificationTask.vue"}], "components\\ustTableVerification\\verificationTaskList.vue": [{"key": "texttexttexttextlistdivDivClass", "chinese": ">校验任务列表</div>\r\n                <div class=", "english": ">校验任务列表</div>\r\n                <div class=", "file": "components\\ustTableVerification\\verificationTaskList.vue"}, {"key": "texttexttexttext", "chinese": "校验结果", "english": "校验结果", "file": "components\\ustTableVerification\\verificationTaskList.vue"}, {"key": "texttexttexttexttexttext", "chinese": "当前执行进度", "english": "当前执行进度", "file": "components\\ustTableVerification\\verificationTaskList.vue"}, {"key": "texttexttexttext", "chinese": "执行耗时", "english": "执行耗时", "file": "components\\ustTableVerification\\verificationTaskList.vue"}, {"key": "texttextsuccess", "chinese": "启动成功！", "english": "启动成功！", "file": "components\\ustTableVerification\\verificationTaskList.vue"}, {"key": "pleaseselecttexttexttexttexttexttexttext", "chinese": "请选择一条或多条规则", "english": "请选择一条或多条规则", "file": "components\\ustTableVerification\\verificationTaskList.vue"}, {"key": "texttexttexttexttext", "chinese": "停止校验中！", "english": "停止校验中！", "file": "components\\ustTableVerification\\verificationTaskList.vue"}, {"key": "texttext", "chinese": "所选", "english": "所选", "file": "components\\ustTableVerification\\verificationTaskList.vue"}, {"key": "text", "chinese": "此", "english": "此", "file": "components\\ustTableVerification\\verificationTaskList.vue"}, {"key": "texttextdeletesuccess", "chinese": "任务删除成功！", "english": "任务删除成功！", "file": "components\\ustTableVerification\\verificationTaskList.vue"}, {"key": "texttextname", "chinese": "任务名称:", "english": "任务名称:", "file": "components\\ustTableVerification\\verificationTaskList.vue"}, {"key": "textdatatext", "chinese": "源数据库:", "english": "源数据库:", "file": "components\\ustTableVerification\\verificationTaskList.vue"}, {"key": "texttextdatatext", "chinese": "目标数据库:", "english": "目标数据库:", "file": "components\\ustTableVerification\\verificationTaskList.vue"}, {"key": "texttexttexttext", "chinese": "校验类型:", "english": "校验类型:", "file": "components\\ustTableVerification\\verificationTaskList.vue"}, {"key": "texttexttexttext", "chinese": "校验内容:", "english": "校验内容:", "file": "components\\ustTableVerification\\verificationTaskList.vue"}, {"key": "texttexttexttexttexttexttexttext", "chinese": "查看校验任务内容", "english": "查看校验任务内容", "file": "components\\ustTableVerification\\verificationTaskList.vue"}, {"key": "texttexttextdeleteobjtexttexttexttext", "chinese": "确定要删除${obj}校验任务", "english": "确定要删除${obj}校验任务", "file": "components\\ustTableVerification\\verificationTaskList.vue"}, {"key": "deletetexttexttexttexttexttexttexttexttextlisttexttexttexttexttexttexttexttexttexttexttexttexttexttexttexttexttexttexttexttexttextoperation", "chinese": "删除校验任务后，任务将从列表移除，同时任务所产生的执行历史不可追溯。请谨慎操作。", "english": "删除校验任务后，任务将从列表移除，同时任务所产生的执行历史不可追溯。请谨慎操作。", "file": "components\\ustTableVerification\\verificationTaskList.vue"}, {"key": "filenamerowstarttimetexttextdetailxls", "chinese": "${fileName}-${row.startTime}-比对详情.xls", "english": "${fileName}-${row.startTime}-比对详情.xls", "file": "components\\ustTableVerification\\verificationTaskList.vue"}, {"key": "texttexttexttextlist", "chinese": "校验任务列表", "english": "校验任务列表", "file": "components\\ustTableVerification\\verificationTaskList.vue"}, {"key": "texttexttexttexttexttext", "chinese": "校验任务总数:", "english": "校验任务总数:", "file": "components\\ustTableVerification\\verificationTaskList.vue"}, {"key": "texttext", "chinese": "停止", "english": "停止", "file": "components\\ustTableVerification\\verificationTaskList.vue"}]}