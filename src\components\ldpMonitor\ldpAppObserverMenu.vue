<template>
    <div class="app-menu">
        <!-- 上下游组合条件查询 -->
        <div v-if="formValid.topologyView === 'UpstreamDownstream'" class="app-menu-type">
            <!--连接类型选择-->
            <h-select v-model="formValid.connectType" placeholder="连接类型" :clearable="true" @on-change="handleExchange">
                <h-option v-for="item in communicationTypeList" :key="item.value" :value="item.value">
                    {{ item.label }}
                </h-option>
            </h-select>
            <!--应用状态选择-->
            <h-select v-model="formValid.nodeStatus" placeholder="应用状态" :clearable="true" @on-change="handleExchange">
                <h-option v-for="item in appWorkStatusList" :key="item.value" :value="item.value">
                    {{ item.label }}
                </h-option>
            </h-select>
            <!--连接状态选择-->
            <h-select v-model="formValid.connectStatus" placeholder="连接状态" :clearable="true"
                @on-change="handleExchange">
                <h-option v-for="item in connectStatusList" :key="item.value" :value="item.value">
                    {{ item.label }}
                </h-option>
            </h-select>
        </div>

        <!-- 集群查询 -->
        <h-select v-if="formValid.topologyView === 'inCluster'" ref="cluster" v-model="formValid.clusterId"
            style="width: 120px;" class="app-menu-cluster" :clearable="true" :isString="true" placeholder="集群名称"
            @on-change="onChangeCluster">
            <h-option v-for="item in appClustersList" :key="item.id" :value="item.id">
                {{ item.clusterName }}
            </h-option>
        </h-select>
        <h-select v-if="formValid.topologyView === 'inCluster'" ref="cluster" v-model="formValid.excludeArbNode"
            style="width: 140px; margin-left: 4px;" class="app-menu-cluster" :clearable="false" placeholder="仲裁节点"
            @on-change="handleExchange">
            <h-option value="true">
                过滤仲裁节点
            </h-option>
            <h-option value="false">
                不过滤仲裁节点
            </h-option>
        </h-select>

        <!-- 搜索应用节点 -->
        <h-select
            v-if="formValid.topologyView === 'UpstreamDownstream'"
            ref="context"
            v-model="formValid.upStreamNodeIds"
            style="width: 200px;"
            class="app-menu-node"
            filterable
            placeholder="搜索应用节点"
            remoteIcon="search"
            showBottom
            showTitle
            widthAdaption
            isCheckall
            multiple
             @on-change="onChangeApp">
            <h-option v-for="item in searchAppList" :key="item.id" :value="item.id">
                {{ item.name }}
            </h-option>
        </h-select>
        <!-- 搜索应用节点 -->
        <h-select v-if="formValid.topologyView === 'inCluster'"
            ref="context"
            v-model="formValid.inClusterIds"
            style="width: 200px;" class="app-menu-node" filterable placeholder="搜索应用节点" remoteIcon="search" showBottom
            widthAdaption isCheckall multiple @on-change="onChangeApp">
            <h-option v-for="item in searchAppList" :key="item.id" :value="item.id">
                {{ item.name }}
            </h-option>
        </h-select>

        <div class="app-menu-line"></div>

        <!-- 上下游关系 & 集群同步关系 -->
        <h-select v-model="formValid.topologyView" :clearable="false" class="app-menu-relation"
            @on-change="handleTypeChange">
            <h-option value="UpstreamDownstream">上下游关系</h-option>
            <h-option value="inCluster">集群同步关系</h-option>
        </h-select>
    </div>
</template>

<script>
import { getObservableAppTypeTopology } from '@/api/topoApi';
import { getProductClusters } from '@/api/productApi';

export default {
    name: 'LdpAppObserverMenu',
    props: {
        productId: {
            type: String,
            default: ''
        }
    },
    data() {
        return {
            formValid: {
                nodeIds: [],
                upStreamNodeIds: [],
                inClusterIds: [],
                clusterId: '',
                nodeStatus: '',
                connectType: '',
                excludeArbNode: 'false',
                connectStatus: '',
                topologyView: 'UpstreamDownstream'
            },
            appClustersList: [],
            upStreamNodeIds: [],
            searchAppList: [],
            edgesList: [],
            timer: null,
            timer1: null,
            loading: false,
            timeLoading: false
        };
    },
    computed: {
        // 应用状态字典
        appWorkStatusList() {
            const appWorkStatusList = [];
            const appWorkStatusDict = this.$store?.state?.apmDirDesc?.appWorkStatusDict || {};
            Object.keys(appWorkStatusDict).forEach(ele => {
                appWorkStatusList.push({
                    label: appWorkStatusDict[ele],
                    value: ele
                });
            });
            return appWorkStatusList;
        },
        // 边连接类型字典
        communicationTypeList() {
            const communicationTypeList = [];
            const communicationTypeDict = this.$store?.state?.apmDirDesc?.communicationTypeDict || {};
            Object.keys(communicationTypeDict).forEach(ele => {
                communicationTypeList.push({
                    label: communicationTypeDict[ele],
                    value: ele
                });
            });
            return communicationTypeList;
        },
        // 边连接状态字典
        connectStatusList() {
            const connectStatusList = [];
            const connectStatusDict = this.$store?.state?.apmDirDesc?.connectStatusDict || {};
            Object.keys(connectStatusDict).forEach(ele => {
                connectStatusList.push({
                    label: connectStatusDict[ele],
                    value: ele
                });
            });
            return connectStatusList;
        }
    },
    mounted() {
        this.getObservableClusters();
        this.queryAppList();
    },
    methods: {
        /**
         * 查询应用节点下拉列表
         */
        async queryAppList(arg = {}) {
            const param = {
                productId: this.productId,
                type: 'instance',
                ...arg
            };
            try {
                const res = await getObservableAppTypeTopology(param);
                if (this.productId !== param.productId) return;
                if (res.success) {
                    const searchAppList = (res?.data?.nodes || []).reduce((pre, item) => {
                        if (!pre.find(p => p.id === item.id) && item.id) {
                            return [...pre, item];
                        }
                        return pre;
                    }, []);
                    this.searchAppList = searchAppList;
                }
            } catch (error) {
                this.searchAppList = [];
                console.error('查询应用节点下拉列表-错误:', error);
            }
        },
        /**
         * 检查查询参数
         */
        validQueryParam() {
            if (this.formValid.topologyView === 'UpstreamDownstream') {
                // 上下游模式
                if (this.formValid.upStreamNodeIds.length) {
                    // 检查应用节点是否存在
                    this.formValid.upStreamNodeIds = this.formValid.upStreamNodeIds.filter(item => this.searchAppList.find(app => app.id === item));
                    this.formValid.nodeIds = [...this.formValid.upStreamNodeIds];
                }
            }
        },
        /**
         * 切换下拉应用列表
         */
        onChangeApp(value) {
            this.formValid.nodeIds = value;
            this.handleExchange();
        },
        /**
         * 重置查询参数
         */
        resetParam() {
            this.formValid = {
                nodeIds: [],
                upStreamNodeIds: [],
                inClusterIds: [],
                clusterId: this.formValid.clusterId,
                nodeStatus: '',
                connectType: '',
                excludeArbNode: 'false',
                connectStatus: '',
                topologyView: this.formValid.topologyView
            };
        },
        /**
         * 切换topo类型
         */
        async handleTypeChange(value) {
            if (value === 'UpstreamDownstream') {
                // 切换为上下游时，清空集群选择
                this.formValid.clusterId = '';
                const arg = {
                    connectType: this.formValid.connectType,
                    nodeStatus: this.formValid.nodeStatus,
                    connectStatus: this.formValid.connectStatus
                };
                await this.queryAppList(arg);
                if (this.formValid.upStreamNodeIds.length) {
                    // 恢复应用节点时，判断是否存在
                    this.formValid.upStreamNodeIds = this.formValid.upStreamNodeIds.filter(item => this.searchAppList.find(app => app.id === item));
                }
            } else {
                this.searchAppList = [];
            }
            this.handleExchange();
        },
        /**
         * 切换集群
         */
        async onChangeCluster(value) {
            this.formValid.clusterId = value;
            this.searchAppList = [];
            this.formValid.inClusterIds = [];
            value && this.$nextTick(() => {
                this.queryAppList(this.formValid);
            });
            this.handleExchange();
        },
        /**
         * 改变搜索条件后，向外传递搜索参数
         */
        handleExchange() {
            const isUpStream = this.formValid.topologyView === 'UpstreamDownstream';
            const query = {
                ...this.formValid,
                nodeIds: isUpStream ? this.formValid.upStreamNodeIds : this.formValid.inClusterIds
            };
            if (!isUpStream) {
                // 集群同步模式
                query.connectStatus = '';
                query.connectType = '';
                query.upStreamNodeIds = '';
                query.nodeStatus = '';
            }

            this.$emit('changeParam', query);
        },
        /**
         * 设置查询参数
         */
        setQueryParam(arg = {}) {
            if (!arg.topologyView) {
                // 兜底保留拓扑类型
                arg.topologyView = this.formValid.topologyView;
            }
            Object.keys(arg).forEach(key => {
                if (key in this.formValid) {
                    // 更新字段
                    this.formValid[key] = arg[key];
                }
            });
            const isUpstream = this.formValid.topologyView === 'UpstreamDownstream';
            if ('nodeIds' in arg) {
                this.formValid[isUpstream ? 'upStreamNodeIds' : 'inClusterIds'] = arg.nodeIds;
            }
            this.handleExchange();
        },
        // 获取应用集群列表
        async getObservableClusters() {
            const res = await getProductClusters({
                productId: this.productId
            });
            this.appClustersList = res?.data?.appClusters || [];
        }
    }
};
</script>
<style lang="less" scoped>
@import url("@/assets/css/input.less");

.app-menu {
    display: flex;
    align-items: center;
    justify-content: space-between;

    &-type {
        display: flex;
        align-items: center;

        /deep/ .h-select {
            width: 120px;
            margin: 0 4px;
        }
    }

    &-node {
        margin-left: 4px;
    }

    &-cluster {
        margin-right: 4px;
    }

    &-line {
        background: #474e6f;
        width: 1px;
        height: 26px;
        margin: 0 8px;
    }

    &-relation {
        width: 150px;
    }

    /deep/ .h-select {
        height: 32px;
    }

    /deep/ .h-select-dropdown {
        input {
            color: #afafaf;
        }
    }
}
</style>
