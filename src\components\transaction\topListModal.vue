<template>
  <div>
    <h-msg-box
      v-model="modalData.status"
      :escClose="true"
      :title="title"
      allowCopy
      footerHide
      width="500"
      height="400"
      @on-open="handleOpen"
    >
       <div class="search-box">
        <h-input
            v-model="accountId"
            class="search-input"
            icon="search"
            placeholder="请输入资金账号"
            @on-enter="getTopList"
            @on-click="getTopList"
        >
        </h-input>
    </div>
    <a-table
        ref="table"
        maxHeight="290"
        :tableData="tableData"
        :columns="columns1"
        :loading="loading"
        :total="total"
        :hasPage="true"
        :highlightRow="true"
        :hasDarkClass="false"
        :immediateRowClick="true"
        :simple="true"
        :showSizer="false"
        :showElevator="false"
        @onCurrentChange="handleRowData"
        @query='getTopList'
        />
    </h-msg-box>
  </div>
</template>
<script>
import { postTopHits } from '@/api/httpApi';
import aTable from '@/components/common/table/aTable';
export default {
    props: {
        modalInfo: {
            type: Object,
            default: null
        }
    },
    data() {
        return {
            title: '每秒时延订单',
            modalData: this.modalInfo,
            total: 0,
            accountId: '',
            loading: false,
            columns1: [
                {
                    title: '资金账号',
                    key: 'accoundId'
                },
                {
                    title: '交易所申报编号',
                    key: 'orderId'
                },
                {
                    title: '时延 (μs)',
                    key: 'duration'
                }
            ],
            tableData: []
        };
    },
    methods: {
        handleOpen() {
            this.title = this.modalData.time + ' 时延订单';
            this.getTopList();
        },
        // 获取每秒最大十笔延时订单
        getTopList() {
            this.loading = true;
            const param = {
                ...this.modalData,
                ...this.$refs['table']?.getPageData()
            };
            param.startTime = this.modalData.time;
            if (this.accountId) param.accountId = this.accountId;
            postTopHits(param).then(res => {
                this.loading = false;
                if (res.success) {
                    this.total = res.data.totalCount;
                    this.tableData = res.data.list;
                }
            });
        },
        // 双击表格行数据
        handleRowData(row) {
            this.$emit('getLinkData', row);
        }
    },
    components: { aTable }
};
</script>

<style lang="less" scoped>
/deep/ .h-modal-body {
    padding: 10px 32px;
}

.search-box {
    overflow: hidden;

    .search-input {
        float: right;
        width: 200px;
        margin-bottom: 10px;

        /deep/ .h-icon {
            cursor: pointer;
        }
    }
}

/deep/ .h-page {
    margin: 10px 0 0;
    text-align: right;
    float: right;
}

/deep/ .h-modal-mask {
    display: none;
}
</style>
