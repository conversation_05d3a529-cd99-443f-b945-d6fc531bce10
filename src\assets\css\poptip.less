.apm-poptip {
    // poptip
    /deep/ .h-poptip-inner {
        background-color: var(--poptip-bg-color);
    }

    /deep/ .h-poptip-title {
        padding: 8px;
        border-bottom: 1px solid var(--font-opacity-color);
    }

    /deep/ .h-poptip-title::after {
        display: none;
    }

    /deep/ .h-poptip-title-inner {
        color: var(--font-color);
    }

    /deep/ .h-poptip-body {
        padding: 8px;
    }

    /deep/ .h-poptip-popper[x-placement^="left"] {
        .h-poptip-arrow {
            border-left-color: var(--poptip-bg-color) !important;

            &::after {
                border-left-color: var(--poptip-bg-color) !important;
            }
        }
    }

    /deep/ .h-poptip-popper[x-placement^="right"] {
        .h-poptip-arrow {
            border-right-color: var(--poptip-bg-color) !important;

            &::after {
                border-right-color: var(--poptip-bg-color) !important;
            }
        }
    }

    /deep/ .h-poptip-popper[x-placement^="top"] {
        .h-poptip-arrow {
            border-top-color: var(--poptip-bg-color) !important;

            &::after {
                border-top-color: var(--poptip-bg-color) !important;
            }
        }
    }

    /deep/ .h-poptip-popper[x-placement^="bottom"] {
        .h-poptip-arrow {
            border-bottom-color: var(--poptip-bg-color) !important;

            &::after {
                border-bottom-color: var(--poptip-bg-color) !important;
            }
        }
    }
}

