/* stylelint-disable property-no-unknown */
/* stylelint-disable declaration-block-no-redundant-longhand-properties */
.select-search {
    position: relative;
    transition-delay: 0s, 0s, 0s, 0s;
    transition-duration: 0.2s, 0.2s, 0.2s, 0.2s;
    transition-property: width;
    transition-timing-function: ease-in-out, ease-in-out, ease-in-out, ease-in-out;

    .h-input,
    .h-input-group-append {
        background-color: transparent !important;
        border-color: transparent !important;
        cursor: pointer;

        &:focus-visible,
        &:focus-within,
        &:focus,
        &:hover {
            outline: none !important;
            box-shadow: none !important;
            border-radius: 0 !important;
            border-color: transparent !important;
        }
    }

    .h-input {
        padding-right: 12px;
    }

    .h-input-group-append {
        padding-right: 13px;
        padding-left: 0;

        .h-icon {
            color: #fff;
        }

        span {
            display: inline-block;
            width: 14px;
            height: 14px;
            cursor: pointer;
        }

        .clear-icon {
            width: 14px;
            height: 14px;
            margin-right: 5px;
        }
    }

    .h-input-wrapper {
        background-color: #2c324d;
        border: 1px solid transparent;
        transition-behavior: normal, normal, normal, normal;
        transition-delay: 0s, 0s, 0s, 0s;
        border-radius: 4px;
        display: block;
        transition-duration: 0.2s, 0.2s, 0.2s, 0.2s;
        transition-property: border, background, box-shadow, -webkit-box-shadow;
        transition-timing-function: ease-in-out, ease-in-out, ease-in-out, ease-in-out;
        cursor: pointer;

        &:hover {
            background: #383f59;
        }
    }

    &[data-border="true"] {
        .h-input-wrapper {
            border: 1px solid #485565;
        }
    }

    &[data-focus="true"] {
        .h-input-wrapper {
            border-radius: 4px 4px 0 0;
            background-color: #262d43;
            border-color: #485565;
        }
    }

    .select-search-append {
        display: flex;
        align-items: center;
    }

    &-wrap {
        position: absolute;
        z-index: 100;
        width: 100%;
        top: 33px;
        left: 0;
        background-color: #262d43;
        border: 1px solid #485565;
        border-radius: 0 0 4px 4px;
        box-shadow: 0 1px 6px rgba(0, 0, 0, 0.2);

        .no-data {
            width: 100%;
            height: 169px;
            text-align: center;

            .text {
                color: #999;
                font-size: 12px;
                padding: 0 10px;
            }
        }

        .footer {
            color: #9296a1;
            padding: 0 12px;
            text-align: left;
            background-color: #202637;
            border-radius: 0 0 4px 4px;
            font-size: 12px;
            height: 30px;
            display: flex;
            align-items: center;

            &-select {
                padding-right: 8px;
                position: relative;
                display: flex;
                align-items: center;

                .icon {
                    height: 12px;
                    width: 12px;
                    margin-right: 4px;
                }
            }

            &-line {
                background-color: #9296a1;
                width: 1px;
                height: 10px;
            }

            &-esc {
                padding-left: 8px;
                display: flex;
                align-items: center;

                .icon {
                    height: 24px;
                    width: 24px;
                    position: relative;
                    top: 1px;
                }
            }
        }

        div[role="group"] {
            // width: max-content;
        }
    }

    .option-text {
        font-size: 12px;
        white-space: nowrap;

        &[data-match="true"] {
            color: #2d8de5;
        }
    }

    .h-spin-fix {
        background-color: rgba(0, 0, 0, 0.5);
    }

    .spin-icon-load {
        animation: ani-demo-spin 1s linear infinite;
        display: inline-block;
    }

    @keyframes ani-demo-spin {
        from {
            transform: rotate(0deg);
        }

        50% {
            transform: rotate(180deg);
        }

        to {
            transform: rotate(360deg);
        }
    }
}
