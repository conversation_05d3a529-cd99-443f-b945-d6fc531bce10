/*
 * @Description: 上下文主题拓扑关系
 * @Author: <PERSON><PERSON>
 * @Date: 2023-06-19 19:34:22
 * @LastEditTime: 2023-06-29 10:56:33
 * @LastEditors: <PERSON><PERSON>
 */
import Vis from 'vis';
export default {
    name: 'rcmTopicTopo',
    data() {
        return {
            options: {
                edges: {
                    chosen: false,
                    width: 1,
                    arrows: 'to',
                    shadow: false,
                    smooth: {
                        enabled: false // 设置连线是直线还是湾线还是贝塞尔
                    },
                    arrowStrikethrough: false,
                    color: {
                        color: '#ccc',
                        highlight: '#ccc',
                        hover: '#ccc',
                        inherit: 'from',
                        opacity: 1.0
                    },
                    font: {
                        color: '#fff',
                        size: 20,
                        multi: false,
                        strokeWidth: 2,
                        strokeColor: 'rgb(112, 184, 240)'
                    }
                },
                nodes: {
                    chosen: false,
                    size: 16,
                    font: {
                        size: 12,
                        color: '#333'
                    }
                },
                physics: {
                    enabled: true
                },
                interaction: {
                    dragNodes: false, // 是否能拖动节点
                    dragView: true, // 是否能拖动画布
                    hover: true, // 鼠标移过后加粗该节点和连接线
                    multiselect: false, // 按 ctrl 多选
                    selectable: false, // 是否可以点击选择
                    selectConnectedEdges: false, // 选择节点后是否显示连接线
                    hoverConnectedEdges: false, // 鼠标滑动节点后是否显示连接线
                    zoomView: true // 是否能缩放画布
                },
                layout: {
                    randomSeed: 1,
                    improvedLayout: true,
                    hierarchical: {
                        enabled: true,
                        levelSeparation: 100,
                        nodeSpacing: 100,
                        treeSpacing: 200,
                        blockShifting: true,
                        edgeMinimization: true,
                        parentCentralization: true,
                        direction: 'UD',        // UD, DU, LR, RL
                        sortMethod: 'directed'  // hubsize, directed
                    }
                }
            }
        };
    },
    methods: {
        init(temp, selectedName) {
            const { nodes, edges } = temp;
            nodes.forEach(ele => {
                ele.label = ele.label || ele.name;
                const img = ele.skinType === '1'
                    ? ele.label === selectedName ? './static/ctx-select.png' : './static/ctx-normal.png' : './static/topic-normal.png';
                ele.shape = 'image';
                ele.image = img;
            });
            this.$options.network = new Vis.Network(
                this.$refs['model-box'],
                { nodes, edges },
                this.options
            );
        }
    },
    render() {
        return <div ref="model-box" style="width: 100%; height: 100%; cursor: pointer;"></div>;
    },
    network: null // 放在这，避免响应式化
};
