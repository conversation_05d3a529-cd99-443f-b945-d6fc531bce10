<template>
    <div class="tab-box">
        <a-loading v-if="loading"></a-loading>
        <div v-else>
            <info-sum-bar :data="infoData"></info-sum-bar>
            <info-grid :gridData="readWorkingThread"></info-grid>
            <div v-if="MultiThreadsMode">
                <info-grid :gridData="mutiThread"></info-grid>
                <tab-title-table :tabTableData="tabTableData" :autoHeight="false" :hasSetTableColumns='false' :showTitle="true" :hasButton="false"></tab-title-table>
            </div>
            <div v-else>
                <info-grid :gridData="sendWorkingThread"></info-grid>
                <info-grid :gridData="onTimerThread"></info-grid>
            </div>
        </div>
    </div>
</template>

<script>
import aLoading from '@/components/common/loading/aLoading';
import infoGrid from '@/components/common/infoBar/infoGrid';
import infoSumBar from '@/components/common/infoBar/infoSumBar';
import tabTitleTable from '@/components/common/bestTable/tabTitleTable';
import { getManagerProxy } from '@/api/mcApi';
import { getPreviousTimesWithSeconds, formatChartNumber } from '@/utils/utils';
export default {
    props: {
        nodeData: {
            type: Object,
            default: () => { }
        },
        pollTime: {
            type: Number,
            default: 0
        }
    },
    components: { aLoading, infoGrid, infoSumBar, tabTitleTable },
    data() {
        const backendColumns = [
            {
                title: 'FunctionNo',
                key: 'FunctionNo',
                ellipsis: true
            },
            {
                title: 'SendCount',
                key: 'SendCount',
                ellipsis: true
            },
            {
                title: 'BizErrCount',
                key: 'BizErrCount',
                ellipsis: true
            },
            {
                title: 'PlatErrCount',
                key: 'PlatErrCount',
                ellipsis: true
            },
            {
                title: 'SuccessCount',
                key: 'SuccessCount',
                ellipsis: true
            },
            {
                title: 'whiteListMatchCount',
                key: 'whiteListMatchCount',
                ellipsis: true
            }
        ];
        const processDetailColumns = [
            {
                title: 'ThreadNo',
                key: 'ThreadNo',
                minWidth: 80,
                ellipsis: true
            },
            {
                title: 'IsReSendThread',
                key: 'IsReSendThread',
                minWidth: 120,
                ellipsis: true
            },
            {
                title: 'ManagedCount',
                key: 'ManagedCount',
                minWidth: 110,
                ellipsis: true
            },
            {
                title: 'BizFailedCount',
                key: 'BizFailedCount',
                minWidth: 110,
                ellipsis: true
            },
            {
                title: 'FailedCount',
                key: 'FailedCount',
                minWidth: 100,
                ellipsis: true
            },
            {
                title: 'TimeoutCount',
                key: 'TimeoutCount',
                minWidth: 110,
                ellipsis: true
            },
            {
                title: 'ReplayCount',
                key: 'ReplayCount',
                minWidth: 100,
                ellipsis: true
            },
            {
                title: 'CacheCount',
                key: 'CacheCount',
                minWidth: 100,
                ellipsis: true
            },
            {
                title: 'SendCount',
                key: 'SendCount',
                minWidth: 100,
                ellipsis: true
            },
            {
                title: 'ExpectingRspTransNo',
                key: 'ExpectingRspTransNo',
                minWidth: 160,
                ellipsis: true
            },
            {
                title: 'HandledTransNo',
                key: 'HandledTransNo',
                minWidth: 130,
                ellipsis: true
            }
        ];
        const dealyInfoColumns = [
            {
                title: 'ThreadNo',
                key: 'ThreadNo',
                ellipsis: true
            },
            {
                title: 'IsReSendThread',
                key: 'IsReSendThread',
                ellipsis: true
            },
            {
                title: 'AveragSendMilli',
                key: 'AveragSendMilli',
                ellipsis: true
            },
            {
                title: 'MaxSendMilli',
                key: 'MaxSendMilli',
                ellipsis: true
            },
            {
                title: 'MaxSendTime',
                key: 'MaxSendTime',
                ellipsis: true
            },
            {
                title: 'AveragRspMilli',
                key: 'AveragRspMilli',
                ellipsis: true
            },
            {
                title: 'MaxRspMilli',
                key: 'MaxRspMilli',
                ellipsis: true
            },
            {
                title: 'MaxRspTime',
                key: 'MaxRspTime',
                ellipsis: true
            }
        ];
        return {
            loading: true,
            MultiThreadsMode: false,
            // 回库执行进度
            infoData: {
                title: {
                    label: '回库执行进度',
                    slots: [
                        {
                            type: 'text', // 文本
                            label: '执行模式',
                            value: '-'
                        }
                    ]
                },
                direction: 'row',
                details: [
                    {
                        type: 'text',
                        title: '下一个写redo事务号',
                        info: {
                            key: 'WriterTransactionNo',
                            value: '-'
                        }
                    },
                    {
                        type: 'text',
                        title: '最大事务号',
                        info: {
                            key: 'MaxReadTransNo',
                            value: '-'
                        }
                    },
                    {
                        type: 'text',
                        title: '当前已同步最大事务号',
                        info: {
                            key: 'MaxTransNo',
                            value: '-'
                        }
                    },
                    {
                        type: 'text',
                        title: '同步流速',
                        info: {
                            key: 'SynchronousVelocity',
                            value: '-'
                        }
                    },
                    {
                        type: 'text',
                        title: '暂未处理事务号个数',
                        info: {
                            key: 'PendTransactionNum',
                            value: '-'
                        }
                    },
                    {
                        type: 'text',
                        title: '预计同步完成时间',
                        info: {
                            key: 'CompletionTime',
                            value: '-'
                        }
                    }
                ]
            },
            // readWorkingThread
            readWorkingThread: {
                title: {
                    label: 'Read Phase - WorkingThread',
                    slots: [
                        {
                            type: 'text', // 文本
                            label: 'WorkingThreadLoops',
                            value: '-'
                        }
                    ]
                },
                layout: [
                    { x: 0, y: 0, w: 6, h: 9, i: '1' },
                    { x: 6, y: 0, w: 3, h: 9, i: '2' },
                    { x: 9, y: 0, w: 3, h: 9, i: '3' },
                    { x: 0, y: 1, w: 9, h: 16, i: '4' },
                    { x: 9, y: 1, w: 3, h: 9, i: '5' },
                    { x: 9, y: 2, w: 3, h: 9, i: '6' }
                ],
                details: [
                    {
                        type: 'obj',
                        title: '配置',
                        infoDic: [
                            {
                                label: 'RedoPath',
                                key: 'RedoPath'
                            },
                            {
                                label: 'ShmName',
                                key: 'ShmName'
                            },
                            {
                                label: 'ZkPath',
                                key: 'ZkPath'
                            },
                            {
                                label: 'TodbZKPath',
                                key: 'TodbZKPath'
                            }
                        ],
                        info: {
                            RedoPath: '-',
                            ShmName: '-',
                            ZkPath: '-',
                            TodbZKPath2: '-'
                        }
                    },
                    {
                        type: 'obj',
                        title: '初始值',
                        infoDic: [
                            {
                                label: 'IsReaderInited',
                                key: 'IsReaderInited'
                            }
                        ],
                        info: {
                            IsReaderInited: '-'
                        }
                    },
                    {
                        type: 'obj',
                        title: '线程',
                        infoDic: [
                            {
                                label: 'RedoTypes',
                                key: 'RedoTypes'
                            },
                            {
                                label: 'OrigTransNo',
                                key: 'OrigTransNo'
                            },
                            {
                                label: 'ReadTransCount',
                                key: 'ReadTransCount'
                            }
                        ],
                        info: {
                            RedoTypes: '-',
                            OrigTransNo: '-',
                            ReadTransCount: '-'
                        }
                    },
                    {
                        type: 'chart',
                        title: '5min内每秒读redo吞吐量',
                        info: {
                            basicOpiton: {
                                chartType: 'axis',
                                lineDeploy: [
                                    {
                                        name: '读',
                                        type: 'line'
                                    }
                                ],
                                yAxiDeploy: [
                                    {
                                        name: '吞吐量'
                                    }
                                ]
                            },
                            additionalOpiton: {
                                backgroundColor: '#2d334c'
                            },
                            chartData: {
                                xData: [],
                                data: {
                                    读: []
                                }
                            }
                        }
                    },
                    {
                        type: 'obj',
                        title: '耗时',
                        infoDic: [
                            {
                                label: 'AveragReadMilli',
                                key: 'AveragReadMilli'
                            },
                            {
                                label: 'MaxReadMilli',
                                key: 'MaxReadMilli'
                            },
                            {
                                label: 'MaxReadTime',
                                key: 'MaxReadTime'
                            }
                        ],
                        info: {
                            AveragReadMilli: '-',
                            MaxReadMilli: '-',
                            MaxReadTime: '-'
                        }
                    },
                    {
                        type: 'obj',
                        title: '吞吐量',
                        infoDic: [
                            {
                                label: 'ReadRedoTps',
                                key: 'ReadRedoTps'
                            },
                            {
                                label: 'MaxReadRedoTps',
                                key: 'MaxReadRedoTps'
                            },
                            {
                                label: 'MinReadRedoTps',
                                key: 'MinReadRedoTps'
                            },
                            {
                                label: 'Per5minReadRedoTps',
                                key: 'Per5minReadRedoTps'
                            }
                        ],
                        info: {
                            ReadRedoTps: '-',
                            MaxReadRedoTps: '-',
                            MinReadRedoTps: '-',
                            Per5minReadRedoTps: '-'
                        }
                    }
                ]
            },
            // mutiThread
            mutiThread: {
                title: {
                    label: 'Send/ReSend Phase - MutiThread Model',
                    slots: [
                        {
                            type: 'text', // 文本
                            label: 'ThreadsCount',
                            value: '-'
                        }
                    ]
                },
                layout: [
                    { x: 0, y: 0, w: 4, h: 12, i: '1' },
                    { x: 4, y: 0, w: 8, h: 12, i: '2' },
                    { x: 0, y: 1, w: 9, h: 16, i: '4' },
                    { x: 9, y: 1, w: 3, h: 9, i: '5' },
                    { x: 9, y: 2, w: 3, h: 9, i: '6' }
                ],
                details: [
                    {
                        type: 'obj',
                        title: '线程',
                        infoDic: [
                            {
                                label: 'SendCount',
                                key: 'SendCount'
                            },
                            {
                                label: 'BizErrCount',
                                key: 'BizErrCount'
                            },
                            {
                                label: 'PlatErrCount',
                                key: 'PlatErrCount'
                            },
                            {
                                label: 'SuccessCount',
                                key: 'SuccessCount'
                            },
                            {
                                label: 'WhiteListMatchCount',
                                key: 'WhiteListMatchCount'
                            },
                            {
                                label: 'InvalidRspCount',
                                key: 'InvalidRspCount'
                            }
                        ],
                        info: {
                            SendCount: '-',
                            BizErrCount: '-',
                            PlatErrCount: '-',
                            SuccessCount: '-',
                            WhiteListMatchCount: '-',
                            InvalidRspCount: '-'
                        }
                    },
                    {
                        type: 'table',
                        title: '功能号统计',
                        info: {
                            tableData: [],
                            columns: backendColumns,
                            height: '180'
                        }
                    },
                    {
                        type: 'chart',
                        title: '5min内每秒发送/应答吞吐量',
                        info: {
                            basicOpiton: {
                                chartType: 'axis',
                                lineDeploy: [
                                    {
                                        name: '发送',
                                        type: 'line'
                                    },
                                    {
                                        name: '应答',
                                        type: 'line'
                                    }
                                ],
                                yAxiDeploy: [
                                    {
                                        name: '吞吐量',
                                        axisLabel: {
                                            formatter: formatChartNumber
                                        }
                                    }
                                ]
                            },
                            additionalOpiton: {
                                backgroundColor: '#2d334c'
                            },
                            chartData: {
                                xData: [],
                                data: {
                                    发送: [],
                                    应答: []
                                }
                            }
                        }
                    },
                    {
                        type: 'obj',
                        title: '发送吞吐量',
                        infoDic: [
                            {
                                label: 'SendTps',
                                key: 'SendTps'
                            },
                            {
                                label: 'Per5minSendTps',
                                key: 'Per5minSendTps'
                            },
                            {
                                label: 'MinSendTps',
                                key: 'MinSendTps'
                            },
                            {
                                label: 'MaxSendTps',
                                key: 'MaxSendTps'
                            }
                        ],
                        info: {
                            SendTps: '-',
                            Per5minSendTps: '-',
                            MinSendTps: '-',
                            MaxSendTps: '-'
                        }
                    },
                    {
                        type: 'obj',
                        title: '应答吞吐量',
                        infoDic: [
                            {
                                label: 'RspTps',
                                key: 'RspTps'
                            },
                            {
                                label: 'Per5minRspTps',
                                key: 'Per5minRspTps'
                            },
                            {
                                label: 'MinRspTps',
                                key: 'MinRspTps'
                            },
                            {
                                label: 'MaxRspTps',
                                key: 'MaxRspTps'
                            }
                        ],
                        info: {
                            RspTps: '-',
                            Per5minRspTps: '-',
                            MinRspTps: '-',
                            MaxRspTps: '-'
                        }
                    }
                ]
            },
            // tabTable
            tabTableData: [
                {
                    label: '线程信息',
                    name: 'ProcessDetail',
                    tableData: [],
                    columns: processDetailColumns,
                    hasPage: false,
                    total: 0
                },
                {
                    label: '耗时信息',
                    name: 'DealyInfo',
                    tableData: [],
                    columns: dealyInfoColumns,
                    hasPage: false,
                    total: 0
                }
            ],
            // sendWorkingThread
            sendWorkingThread: {
                title: {
                    label: 'Send Phase - WorkingThread',
                    slots: [
                        {
                            type: 'text', // 文本
                            label: 'WorkingThreadLoops',
                            value: '-'
                        }
                    ]
                },
                layout: [
                    { x: 0, y: 0, w: 4, h: 12, i: '1' },
                    { x: 4, y: 0, w: 8, h: 12, i: '2' }
                ],
                details: [
                    {
                        type: 'obj',
                        title: '线程',
                        infoDic: [
                            {
                                label: 'SendCount',
                                key: 'SendCount'
                            },
                            {
                                label: 'BizErrCount',
                                key: 'BizErrCount'
                            },
                            {
                                label: 'PlatErrCount',
                                key: 'PlatErrCount'
                            },
                            {
                                label: 'SuccessCount',
                                key: 'SuccessCount'
                            },
                            {
                                label: 'WhiteListMatchCount',
                                key: 'WhiteListMatchCount'
                            },
                            {
                                label: 'InvalidRspCount',
                                key: 'InvalidRspCount'
                            }
                        ],
                        info: {
                            SendCount: '-',
                            BizErrCount: '-',
                            PlatErrCount: '-',
                            SuccessCount: '-',
                            WhiteListMatchCount: '-',
                            InvalidRspCount: '-'
                        }
                    },
                    {
                        type: 'table',
                        title: '功能号统计',
                        info: {
                            tableData: [],
                            columns: backendColumns,
                            height: '180'
                        }
                    }
                ]
            },
            // onTimerThread
            onTimerThread: {
                title: {
                    label: 'ReSend Phase - OnTimerThread',
                    slots: [
                        {
                            type: 'text', // 文本
                            label: 'OnTimerThreadLoops',
                            value: '-'
                        }
                    ]
                },
                layout: [
                    { x: 0, y: 0, w: 4, h: 6, i: '1' },
                    { x: 4, y: 0, w: 4, h: 6, i: '2' },
                    { x: 8, y: 0, w: 4, h: 6, i: '3' }
                ],
                details: [
                    {
                        type: 'obj',
                        title: '初始值',
                        infoDic: [
                            {
                                label: 'ResendIntervalSec',
                                key: 'ResendIntervalSec'
                            },
                            {
                                label: 'MaxResendCount',
                                key: 'MaxResendCount'
                            }
                        ],
                        info: {
                            ResendIntervalSec: '-',
                            MaxResendCount: '-'
                        }
                    },
                    {
                        type: 'obj',
                        title: '线程',
                        infoDic: [
                            {
                                label: 'ErrResponseCount',
                                key: 'ErrResponseCount'
                            },
                            {
                                label: 'ResendCount',
                                key: 'ResendCount'
                            }
                        ],
                        info: {
                            ErrResponseCount: '-',
                            ResendCount: '-'
                        }
                    },
                    {
                        type: 'obj',
                        title: '结果',
                        infoDic: [
                            {
                                label: 'FailedTodbRecordCount',
                                key: 'FailedTodbRecordCount'
                            }
                        ],
                        info: {
                            FailedTodbRecordCount: '-'
                        }
                    }
                ]
            },
            // 初始数据   上一次的执行记录
            lastData: {}
        };
    },
    methods: {
        async initData() {
            this.loading = true;
            this.lastData = {};
            await this.getFileData();
            this.loading = false;
        },
        // 回库执行进度
        setInfoData(getMonitor, getTransactionNo, time){
            this.infoData.title.slots[0].value = getMonitor.MultiThreadsMode ? '多线程' : '单线程';
            const MaxTransNo = (getMonitor.ZkMinTransNo - 1) >= 0 ? (getMonitor.ZkMinTransNo - 1) : 0 ;
            const WriterTransactionNo = (getTransactionNo.ReplayTransactionNo || getTransactionNo.WriterTransactionNo) - 1 >= 0 ? (getTransactionNo.ReplayTransactionNo || getTransactionNo.WriterTransactionNo) - 1 : 0 ;
            const SynchronousVelocity = (this.lastData.LastTime) ? Number(((MaxTransNo - this.lastData.MaxTransNo) * 1000 / (time - this.lastData.LastTime)).toFixed(3)) : 0;
            const PendTransactionNum = WriterTransactionNo - MaxTransNo;
            const CompletionTime = (Number(SynchronousVelocity) ? (PendTransactionNum / SynchronousVelocity).toFixed(3) : 0) + ' s';
            this.lastData = {
                MaxTransNo: MaxTransNo,
                LastTime: time
            };
            this.infoData.details[0].info.value = getTransactionNo.ReplayTransactionNo || getTransactionNo.WriterTransactionNo || 0;
            this.infoData.details[1].info.value = getMonitor?.MaxReadTransNo || 0;
            this.infoData.details[2].info.value = MaxTransNo;
            this.infoData.details[3].info.value = SynchronousVelocity;
            this.infoData.details[4].info.value = PendTransactionNum;
            this.infoData.details[5].info.value = CompletionTime;
        },
        // Read Phase - WorkingThread
        setReadWorkingThread(getMonitor, timeList, perSecondReadCount){
            this.readWorkingThread.title.slots[0].value = getMonitor.WorkthreadLoops;
            this.readWorkingThread.details[0].info = { ...getMonitor };
            this.readWorkingThread.details[1].info = { ...getMonitor };
            this.readWorkingThread.details[2].info = { ...getMonitor };
            this.readWorkingThread.details[4].info = { ...getMonitor };
            this.readWorkingThread.details[5].info = { ...getMonitor };
            // 折线图
            this.readWorkingThread.details[3].info.chartData.xData = [];
            this.readWorkingThread.details[3].info.chartData.data.读 = [];
            this.readWorkingThread.details[3].info.chartData.xData = [...timeList];
            this.readWorkingThread.details[3].info.chartData.data.读 = [...perSecondReadCount];
        },
        // 单线程 - readWorkingThread
        setSimpleThread(getMonitor, getFuncNoDealInfo){
            this.sendWorkingThread.title.slots[0].value = getMonitor.WorkthreadLoops;
            const threadCounts = this.setThreadCount(getFuncNoDealInfo);
            this.sendWorkingThread.details[0].info = {
                ...threadCounts,
                InvalidRspCount: getMonitor?.InvalidRspCount || 0
            };
            this.sendWorkingThread.details[1].info.tableData = [...getFuncNoDealInfo];

            // onTimerThread
            this.onTimerThread.title.slots[0].value = getMonitor.OnTimerThreadLoops;
            this.onTimerThread.details[0].info = { ...getMonitor };
            this.onTimerThread.details[1].info = { ...getMonitor };
            this.onTimerThread.details[2].info = { ...getMonitor };
        },
        // 累加数值
        setThreadCount(data) {
            return data.reduce((accumulator, currentValue) => {
                accumulator.SendCount += currentValue.SendCount;
                accumulator.BizErrCount += currentValue.BizErrCount;
                accumulator.PlatErrCount += currentValue.PlatErrCount;
                accumulator.SuccessCount += currentValue.SuccessCount;
                accumulator.WhiteListMatchCount += currentValue.whiteListMatchCount;
                return accumulator;
            }, {
                SendCount: 0,
                BizErrCount: 0,
                PlatErrCount: 0,
                SuccessCount: 0,
                WhiteListMatchCount: 0
            });
        },
        // 多线程 mutiThread tabTable
        // eslint-disable-next-line max-params
        setMutiThread(getMonitor, getFuncNoDealInfo, timeList, perSecondSendCount, perSecondRspCount){
            // mutiThread
            this.mutiThread.title.slots[0].value = getMonitor.ThreadsCount;
            const threadCounts = this.setThreadCount(getFuncNoDealInfo);
            this.mutiThread.details[0].info = {
                ...threadCounts,
                InvalidRspCount: getMonitor?.InvalidRspCount || 0
            };
            this.mutiThread.details[1].info.tableData = [...getFuncNoDealInfo];
            this.mutiThread.details[3].info = { ...getMonitor };
            this.mutiThread.details[4].info = { ...getMonitor };
            // 折线图
            this.mutiThread.details[2].info.chartData.xData = [];
            this.mutiThread.details[2].info.chartData.data.发送 = [];
            this.mutiThread.details[2].info.chartData.data.应答 = [];
            this.mutiThread.details[2].info.chartData.xData = [...timeList];
            this.mutiThread.details[2].info.chartData.data.发送 = [...perSecondSendCount];
            this.mutiThread.details[2].info.chartData.data.应答 = [...perSecondRspCount];

            // tabTable
            this.tabTableData[0].tableData = [...(getMonitor?.ThreadsSendStatus || [])];
            this.tabTableData[1].tableData = [...(getMonitor?.ThreadsSendStatus || [])];
        },
        // 构造页面数据
        async getFileData() {
            const { getMonitor, getTransactionNo, perSecondReadCount, getFuncNoDealInfo, perSecondSendCount, perSecondRspCount } = await this.getAPi();
            this.MultiThreadsMode = getMonitor.MultiThreadsMode;
            const time = new Date();
            const timeList = [...getPreviousTimesWithSeconds(300)];
            // 回库进度
            this.setInfoData(getMonitor, getTransactionNo, time);
            // Read Phase - WorkingThread
            this.setReadWorkingThread(getMonitor, timeList, perSecondReadCount);

            if (!this.MultiThreadsMode){
                // 单线程 readWorkingThread  onTimerThread
                this.setSimpleThread(getMonitor, getFuncNoDealInfo);
            } else {
                // 多线程  mutiThread tabTable
                this.setMutiThread(getMonitor, getFuncNoDealInfo, timeList, perSecondSendCount, perSecondRspCount);
            }
        },
        // 获取核心ip
        getCoreIp(){
            let port = '';
            this.$emit('get-core-ip', (val) => {
                port = val;
            });
            return port;
        },
        // 接口请求
        async getAPi() {
            const data = {
                getMonitor: {},
                getTransactionNo: {},
                perSecondReadCount: [],
                getFuncNoDealInfo: [],
                perSecondSendCount: [],
                perSecondRspCount: []
            };
            const corePort = this.getCoreIp();
            const param = [
                {
                    manageProxyIp: this.nodeData.ip,
                    manageProxyPort: this.nodeData.manageProxyPort,
                    pluginName: 'ldp_todb',
                    funcName: 'GetMonitor'
                },
                {
                    manageProxyIp: this.nodeData.ip,
                    manageProxyPort: corePort,
                    pluginName: 'ldp_bizproc',
                    funcName: 'GetTransactionNo'
                },
                {
                    manageProxyIp: this.nodeData.ip,
                    manageProxyPort: this.nodeData.manageProxyPort,
                    pluginName: 'ldp_todb',
                    funcName: 'PerSecondReadCount'
                },
                {
                    manageProxyIp: this.nodeData.ip,
                    manageProxyPort: this.nodeData.manageProxyPort,
                    pluginName: 'ldp_todb',
                    funcName: 'GetFuncNoDealInfo'
                },
                {
                    manageProxyIp: this.nodeData.ip,
                    manageProxyPort: this.nodeData.manageProxyPort,
                    pluginName: 'ldp_todb',
                    funcName: 'PerSecondSendCount'
                },
                {
                    manageProxyIp: this.nodeData.ip,
                    manageProxyPort: this.nodeData.manageProxyPort,
                    pluginName: 'ldp_todb',
                    funcName: 'PerSecondRspCount'
                }
            ];
            try {
                const res = await getManagerProxy(JSON.stringify(param));
                if (res.code === '200') {
                    !res.data?.[0]?.ErrorNo && Object.keys(res.data?.[0]).length  && (data.getMonitor = res.data[0]);
                    !res.data?.[1]?.ErrorNo && Object.keys(res.data?.[1]).length  && (data.getTransactionNo = res.data[1]);
                    res.data?.[2]?.['5minPerSecondReadCount']?.length && (data.perSecondReadCount = res.data[2]['5minPerSecondReadCount']);
                    res.data?.[3]?.FuncNoDealInfo?.length  && (data.getFuncNoDealInfo = res.data[3].FuncNoDealInfo);
                    res.data?.[4]?.['5minPerSecondSendCount']?.length  && (data.perSecondSendCount = res.data[4]['5minPerSecondSendCount']);
                    res.data?.[5]?.['5minPerSecondRspCount']?.length  && (data.perSecondRspCount = res.data[5]['5minPerSecondRspCount']);
                }
                return data;
            } catch (err) {
                this.$emit('clear');
            }
        }
    }
};
</script>

<style lang="less" scoped>
.tab-box {
    /deep/ .info-bar {
        .apm-chart {
            background-color: var(--primary-color);
        }
    }

    .best-table {
        padding: 0;
        margin-top: 15px;
        background: var(--primary-color);

        /deep/ .h-table-tiptext {
            background: var(--wrapper-color);
        }

        /deep/ .a-table {
            margin: 0 10px;
        }
    }
}
</style>
