<template>
    <div>
        <h-msg-box v-model="transportInfo.status" :escClose="true" :mask-closable="false"
            :title="`${transportInfo.type === 'add' ? '创建' : '配置'}` + `${saveValidate.mode === 'singleton' ? '单例' : '集群'}上下文`"
            width="60" height="70" class="wrap-msgbox" @on-open="getCollections" @on-close="cancelMethod">
            <h-form ref="saveValidate" :model="saveValidate" :label-width="100">
                <h-form-item v-if="transportInfo.type === 'add'" label="模式" prop="mode" required>
                    <h-radio-group v-model="saveValidate.mode" @on-change="changeMode">
                        <h-radio label="singleton">
                            <span>单例</span>
                        </h-radio>
                        <h-radio label="cluster">
                            <span>集群</span>
                        </h-radio>
                    </h-radio-group>
                </h-form-item>
                <h-form-item label="名称" prop="contextName" :required="transportInfo.type === 'add' ? true :false">
                    <h-input v-model.trim="saveValidate.contextName" type="text" style="width: 300px;"
                        :disabled="transportInfo.type === 'update'" :maxlength="50"></h-input>
                </h-form-item>
                <h-form-item v-if="transportInfo.type === 'update'" label="模式" prop="mode">
                    <p>{{ saveValidate.mode }}</p>
                </h-form-item>
                <h-form-item label="发送主题">
                    <div style="max-height: 240px; overflow: scroll;">
                        <a-button type="dashed" size="small" icon="plus-round" color="#b0b4ba"
                            @click="handleAdd('send')">添加</a-button>
                        <h-tag v-for="item in saveValidate.txTopics" :key="item.topic + ':' + item.partition" :name="item.topic + ':' + item.partition" closable
                            @on-close="(event)=>handleClose(event,item, 'send')">
                            {{ item.topic + ' [' + item.partition + ']'}}
                        </h-tag>
                    </div>
                </h-form-item>
                <h-form-item label="接收主题">
                    <div style="max-height: 240px; overflow: scroll;">
                        <a-button type="dashed" size="small" icon="plus-round" color="#b0b4ba"
                            @click="handleAdd('receive')">添加</a-button>
                        <h-tag v-for="item in saveValidate.rxTopics" :key="item.topic + ':' + item.partition" :name="item.topic + ':' + item.partition" closable
                            @on-close="(event)=>handleClose(event, item, 'receive')">
                            {{ item.topic + ' [' + item.partition + ']'}}
                        </h-tag>
                    </div>
                </h-form-item>
                <h-form-item label="查询标签">
                    <div style="max-height: 240px; overflow: scroll;">
                        <a-button type="dashed" size="small" icon="plus-round" color="#b0b4ba"
                            @click="handleAdd('context-tag')">添加</a-button>
                        <h-tag v-for="item in saveValidate.tags" :key="item" :name="item" closable
                            @on-close="(event)=>handleClose(event, item, 'context-tag')">
                            {{ item }}
                        </h-tag>
                    </div>
                </h-form-item>
            </h-form>
            <singleton-context-setting v-if="saveValidate.mode === 'singleton'" ref="singleton"
                :singletonTemplateNames="singletonTemplateNames" :singletonSetting="singletonSetting"
                :rcmId="transportInfo.id" :type="transportInfo.type"></singleton-context-setting>
            <cluster-context-setting v-if="saveValidate.mode === 'cluster'" ref="cluster"
                :clusterTemplateNames="clusterTemplateNames" :clusterSetting="clusterSetting" :type="transportInfo.type"
                :rcmId="transportInfo.id" :contextName="saveValidate.contextName"></cluster-context-setting>
            <template v-slot:footer>
                <a-button @click="cancelMethod">取消</a-button>
                <a-button :loading="btnLoading" type="primary" @click="saveSubmit">确定</a-button>
            </template>
        </h-msg-box>
        <create-theme-modal v-if="formInfo.status" :modalInfo="formInfo" @add="addTagOrTheme" />
        <create-tag-modal v-if="inputInfo.status" :modalInfo="inputInfo" @add="addTagOrTheme" />
    </div>
</template>

<script>
import aButton from '@/components/common/button/aButton';
import { clusterTempDefault } from '@/config/rcmDefaultConfig';
import singletonContextSetting from '@/components/rcmDeploy/modal/context/singletonContextSetting';
import clusterContextSetting from '@/components/rcmDeploy/modal/context/clusterContextSetting';
import { formatDates } from '@/utils/utils';
import createThemeModal from '@/components/rcmDeploy/modal/context/createThemeModal';
import createTagModal from '@/components/rcmDeploy/modal/topic/createTagModal';
export default {
    props: {
        modalInfo: {
            type: Object,
            default: null
        }
    },
    components: { aButton, singletonContextSetting, clusterContextSetting, createThemeModal, createTagModal },
    data() {
        return {
            transportInfo: this.modalInfo,
            saveValidate: {
                contextName: 'topic',
                mode: 'singleton',
                tags: [],
                txTopics: [],
                rxTopics: []
            },
            modeTypes: [
                {
                    value: 'singleton',
                    label: '单例'
                },
                {
                    value: 'cluster',
                    label: '集群'
                }
            ],
            btnLoading: false,
            singletonTemplateNames: [],
            clusterTemplateNames: [],
            singletonSetting: {},
            clusterSetting: {},
            formInfo: {
                status: false
            },
            inputInfo: {
                status: false
            }
        };
    },
    mounted() {
    },
    methods: {
        // mode切换
        changeMode(value) {
            if (value === 'singleton') {
                this.saveValidate.contextName = 'context_' + formatDates(new Date()).replace(/[^\d]/gi, '') + '_single';
                return;
            }
            if (value === 'cluster') {
                this.saveValidate.contextName = 'context_' + formatDates(new Date()).replace(/[^\d]/gi, '') + '_tier';
                this.$nextTick(() => {
                    this.$refs['cluster'].init();
                });
                return;
            }
            this.getCollections();
        },
        // 初始化数据
        // eslint-disable-next-line complexity
        getCollections() {
            this.singletonTemplateNames = [];
            this.clusterTemplateNames = [];

            this.transportInfo.singleCtxList.forEach(item => {
                this.singletonTemplateNames.push({
                    value: item.name,
                    label: item.name
                });
            });

            this.transportInfo.clusterCtxList.forEach(item => {
                this.clusterTemplateNames.push({
                    value: item.name,
                    label: item.name
                });
            });

            this.saveValidate = {
                contextName: this.transportInfo.data.mode === 'singleton' ? this.transportInfo.data.name : this.transportInfo.data.tierName,
                mode: this.transportInfo.data.mode,
                tags: [...this.transportInfo.data.tags || []],
                txTopics: [...this.transportInfo.data.txTopics || []],
                rxTopics: [...this.transportInfo.data.rxTopics || []]
            };

            if (this.transportInfo.data.mode === 'singleton') {
                this.singletonSetting.contexts = [...this.transportInfo.data?.contexts || []];
                this.singletonSetting.ref = this.transportInfo.data?.ref || '';
                this.clusterSetting = {};
                this.$nextTick(() => {
                    this.$refs['singleton'].init();
                });
                return;
            }
            if (this.transportInfo.data.mode === 'cluster') {
                this.singletonSetting = {};
                this.clusterSetting.clusterParam = {
                    syncIp: this.transportInfo.data.syncIp || clusterTempDefault.syncIp,
                    syncAddr: this.transportInfo.data.syncAddr || clusterTempDefault.syncAddr,
                    syncPort: this.transportInfo.data.syncPort || clusterTempDefault.syncPort,
                    syncHeartbeatIntervalMilli: this.transportInfo.data.syncHeartbeatIntervalMilli || clusterTempDefault.syncHeartbeatIntervalMilli,
                    syncHeartbeatTimeoutMilli: this.transportInfo.data.syncHeartbeatTimeoutMilli || clusterTempDefault.syncHeartbeatTimeoutMilli,
                    syncAckIntervalMilli: this.transportInfo.data.syncAckIntervalMilli || clusterTempDefault.syncAckIntervalMilli,
                    syncAckTimeoutMilli: this.transportInfo.data.syncAckTimeoutMilli || clusterTempDefault.syncAckTimeoutMilli,
                    syncSendWindowSize: this.transportInfo.data.syncSendWindowSize || clusterTempDefault.syncSendWindowSize,
                    syncMtu: this.transportInfo.data.syncMtu || clusterTempDefault.syncMtu,
                    syncMode: this.transportInfo.data.syncMode || clusterTempDefault.syncMode
                };
                this.clusterSetting.contexts = [...this.transportInfo.data.contexts];
                this.clusterSetting.ref = this.transportInfo.data?.ref || '';
                this.clusterSetting.id = this.transportInfo.data?.id || '';
                this.$nextTick(() => {
                    this.$refs['cluster'].init();
                });
                return;
            }
        },
        // 删除标签
        handleClose(_event, val, type) {
            if (type === 'context-tag') {
                const index = this.saveValidate.tags.indexOf(val);
                this.saveValidate.tags.splice(index, 1);
                return;
            }
            if (type === 'send') {
                this.saveValidate.txTopics.forEach((v, index) => {
                    if (v.id === val.id) {
                        this.saveValidate.txTopics.splice(index, 1);
                    }
                });
                return;
            }
            if (type === 'receive') {
                this.saveValidate.rxTopics.forEach((v, index) => {
                    if (v.id === val.id) {
                        this.saveValidate.rxTopics.splice(index, 1);
                    }
                });
                return;
            }
        },
        // 添加标签
        handleAdd(type) {
            const setInfo = (obj, title, extraProps) => {
                obj.status = true;
                obj.id = this.transportInfo.id;
                obj.type = type;
                obj.title = title;
                Object.assign(obj, extraProps);
            };

            switch (type) {
                case 'context-tag':
                    setInfo(this.inputInfo, '添加标签', {
                        context: this.saveValidate.contextName,
                        tags: this.saveValidate.tags
                    });
                    break;
                case 'send':
                    setInfo(this.formInfo, '添加发送主题', {
                        selectedNodes: this.saveValidate.txTopics
                    });
                    break;
                case 'receive':
                    setInfo(this.formInfo, '添加接收主题', {
                        selectedNodes: this.saveValidate.rxTopics
                    });
                    break;
                default:
                    break;
            }
        },
        addTagOrTheme(val) {
            switch (val.type) {
                case 'context-tag':
                    this.saveValidate.tags = [...val.selection];
                    break;
                case 'send':
                    this.saveValidate.txTopics = [...val.selectedNodes];
                    break;
                case 'receive':
                    this.saveValidate.rxTopics = [...val.selectedNodes];
                    break;
                default:
                    break;
            }
        },
        // 点击取消按钮
        cancelMethod() {
            this.transportInfo.status = false;
            this.saveValidate = {
                contextName: 'topic',
                mode: 'singleton',
                tags: [],
                txTopics: [],
                rxTopics: []
            };
            this.singletonSetting = {};
            this.clusterSetting = {};
        },
        // 点击确定
        saveSubmit() {
            this.$refs['saveValidate'].validate((valid) => {
                const res = this.$refs[this.saveValidate.mode]?.getFileData();
                if (valid && res) {
                    this.$emit('create-or-update-context', {
                        ...this.saveValidate,
                        ...res });
                    this.transportInfo.status = false;
                }
            });
        }

    }
};
</script>
<style lang="less" scoped>
@import url("@/assets/css/tag.less");

.wrap-msgbox {
    .h-form-item {
        margin-bottom: 20px;
    }
}
</style>
