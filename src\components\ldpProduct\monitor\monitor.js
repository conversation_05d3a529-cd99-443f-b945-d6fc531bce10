import * as echarts from 'echarts';
import './monitor.less';
import aTable from '@/components/common/table/aTable';
export default {
    name: 'ldpMonitor',
    props: {
        monitorList: {
            type: Array,
            default: () => []
        },
        unitType: {
            type: String,
            default: ''
        }
    },
    data() {
        return {
            myCharts: [],
            option: {
                title: {
                    text: '时延走势(μs)',
                    textStyle: {
                        color: '#fff',
                        fontSize: 11,
                        fontWeight: 500
                    },
                    top: 10
                },
                grid: {
                    left: 60,
                    right: 30,
                    bottom: 30,
                    top: 50
                },
                tooltip: {
                    trigger: 'axis',
                    backgroundColor: 'rgba(88,94,106,0.40)',
                    borderColor: 'rgba(88,94,106,0.40)',
                    textStyle: {
                        color: '#fff'
                    },
                    // eslint-disable-next-line max-params
                    position: function (point, params, dom, rect, size) {
                        // 鼠标坐标和提示框位置的参考坐标系是：以外层div的左上角那一点为原点，x轴向右，y轴向下
                        // 提示框位置
                        let x = 0; // x坐标位置
                        let y = 0; // y坐标位置

                        // 当前鼠标位置
                        const pointX = point[0];
                        const pointY = point[1];

                        // 外层div大小
                        // var viewWidth = size.viewSize[0];
                        // var viewHeight = size.viewSize[1];

                        // 提示框大小
                        const boxWidth = size.contentSize[0];
                        const boxHeight = size.contentSize[1];

                        // boxWidth > pointX 说明鼠标左边放不下提示框
                        if (boxWidth > pointX) {
                            x = 5;
                        } else {
                            // 左边放的下
                            x = pointX - boxWidth;
                        }

                        // boxHeight > pointY 说明鼠标上边放不下提示框
                        if (boxHeight > pointY) {
                            y = 5;
                        } else {
                            // 上边放得下
                            y = pointY - boxHeight;
                        }

                        return [x, y];
                    }
                },
                legend: {
                    textStyle: {
                        color: '#fff',
                        fontSize: 11,
                        padding: [0, 0, 0, 10]
                    },
                    itemHeight: 1,
                    itemWidth: 16,
                    top: 10,
                    selectedMode: false
                },
                xAxis: {
                    type: 'category',
                    boundaryGap: false,
                    axisLine: {
                        show: true,
                        lineStyle: {
                            color: '#31364a'
                        }
                    },
                    axisTick: {
                        show: false
                    },
                    axisLabel: {
                        color: '#fff',
                        textStyle: {
                            width: '0'
                        },
                        rich: {
                            a: {
                                align: 'right'

                            },
                            b: {
                                align: 'left'
                            }
                        }
                    },
                    data: []
                },
                yAxis: [
                    {
                        type: 'value',
                        axisLine: {
                            show: true,
                            lineStyle: {
                                color: '#31364a'
                            }
                        },
                        axisLabel: {
                            color: '#fff'
                        },
                        splitLine: {
                            lineStyle: {
                                color: '#31364a'
                            }
                        }
                    },
                    {
                        axisLine: {
                            show: true,
                            lineStyle: {
                                color: '#31364a'
                            }
                        }
                    }
                ],
                toolbox: {
                    right: 10
                },
                series: []
            },
            colors: ['#5470c6', '#91cc75', '#fac858', '#ee6666', '#73c0de', '#3ba272', '#fc8452', '#9a60b4', '#ea7ccc'],
            maxHeight: 0
        };
    },
    mounted() {
        this.maxHeight = this.$refs['monitorBox'] ? this.$refs['monitorBox'].offsetHeight : 0;
        setTimeout(() => {
            this.init();
            window.addEventListener('resize', () => {
                this.resize();
            });
        }, 500);
    },
    methods: {
        init() {
            this.myCharts = [];
            this.$nextTick(() => {
                this.monitorList.forEach((ele, idx) => {
                    this.$refs[`chart${idx}`] && this.myCharts.push(echarts.init(this.$refs[`chart${idx}`], '#262B40'));
                    this.option.xAxis.data = ele.xaxis;
                    this.option.series = [{
                        name: ele.name,
                        type: 'line',
                        showSymbol: false,
                        data: ele.yaxis,
                        itemStyle: {
                            color: this.colors[idx]
                        },
                        lineStyle: {
                            width: 1,
                            color: this.colors[idx]
                        }
                    }];
                    this.myCharts[idx] && this.myCharts[idx].setOption(this.option, true);
                });
            });
        },
        resize() {
            this.maxHeight = this.$refs['monitorBox'] ? this.$refs['monitorBox'].offsetHeight - 10 : 0;
            this.$nextTick(() => {
                this.myCharts.forEach(ele => {
                    ele && ele.resize();
                });
            });
        }
    },
    watch: {
        monitorList(newVal) {
            if (newVal) {
                this.init();
                this.resize();
            }
        }
    },
    components: { aTable },
    render() {
        return (
            <div ref="monitorBox" class="box-monitor">
                {
                    this.monitorList.map((item, idx) => {
                        return <div ref={`chart${idx}`} class="chart" style={ { height: this.maxHeight / Math.ceil(this.monitorList.length / 2) - 5 + 'px' } }></div>;
                    })
                }
            </div>
        );
    }
};
