<template>
    <div class="tab-box">
        <!-- 筛选部分 -->
        <div class="select-box">
            <select-box
                ref="selectRef"
                :loading="loading"
                :boxInfo="boxInfo"
                @query="queryTable">
            </select-box>
        </div>
        <!-- 查询结果 -->
        <div ref="table-box" class="result-box">
            <h-tabs value="result">
                <template v-slot:extra>
                    <span class="record-count">
                        <span style="margin: 0 5px;">
                            内存表记录总数：{{ recordCount > 10000 ? recordCount / 10000 + ' w ' : recordCount }}
                        </span>
                    </span>
                </template>
                <h-tab-pane label="查询结果集" name="result">
                    <a-table
                        :hasPage="false"
                        :columns="columns"
                        :height="tableHeight"
                        :tableData="flatTableData"
                        canDrag
                        showTitle
                        border
                        :loading="loading"
                        highlightRow
                        @query="handleQuery"
                        @rowClick="rowClick">
                    </a-table>
                    <div class="page-box">
                        <a-button type="dark" :disabled="disUpturn" @click="turnUp">
                            上一页
                        </a-button>
                        &ensp;<span>{{ recordPage }}</span>&ensp;
                        <a-button type="dark" :disabled="disDownturn" @click="turnDown">
                            下一页
                        </a-button>
                        <h-select
                            v-model="pageSize"
                            autoPlacement
                            :transfer="true"
                            setDefSelect
                            :clearable="false"
                            @on-change="pageSizeChange">
                            <h-option
                                v-for="item in pageList"
                                :key="item.value"
                                :value="item.value">
                                {{ item.label }}
                            </h-option>
                        </h-select>
                    </div>
                </h-tab-pane>
            </h-tabs>
        </div>

        <!-- 表记录结构弹窗 -->
        <modify-detail-modal
            v-if="detialBoxInfo.status"
            ref="modify-detial"
            :detialBoxInfo="detialBoxInfo"
            :tableInfo="tableInfo"
            :memoryStruct="boxInfo.fields"
            :tableData="tableData">
        </modify-detail-modal>
    </div>
</template>

<script>
import { flatStruct, loopAllData, getSpanWidth } from '@/utils/utils';
import selectBox from '@/components/ldpTable/selectBox.vue';
import { getRecords } from '@/api/httpApi';
import aButton from '@/components/common/button/aButton';
import aTable from '@/components/common/table/aTable';
import modifyDetailModal from '@/components/ldpTable/modal/modifyDetailModal.vue';
export default {
    name: 'TableQuery',
    props: {
        databaseName: {
            type: String,
            default: ''
        },
        endpointInfo: {
            type: Object,
            default: () => {}
        },
        boxInfo: {
            tableName: {
                type: String,
                default: ''
            },
            expandAttributes: {
                type: Array,
                default: []
            },
            fields: {
                type: Array,
                default: []
            },
            describe: {
                type: String,
                default: ''
            }
        }
    },
    data() {
        return {
            columns: [],
            tableData: [],
            flatTableData: [],
            conditionParam: {}, // 当前条件筛选参数
            disUpturn: true, // 表记录上翻
            disDownturn: false, // 表记录下翻
            recordPage: 1, // 当前记录页数
            totalCount: 0, // 表格总记录数
            recordCount: 0, // 内存表总记录数
            recordSign: [{ threadId: 0, tableId: -1, recordId: 0 }], // 起始表记录标识
            saveRecord: null, // 保存 一条记录
            loading: false,
            pageSize: 10,
            pageList: [
                { value: 10, label: '10条/页' },
                { value: 20, label: '20条/页' },
                { value: 30, label: '30条/页' },
                { value: 50, label: '50条/页' }
            ],
            tableRecordes: {
                ThreadId: 0,
                TableId: -1,
                RecordId: 0
            },
            detialBoxInfo: {
                status: false,
                viewMode: 0, // 记录展示模式 0：查询模式，1：编辑模式
                tableIndex: 0
            },
            tableHeight: 0
        };
    },
    computed: {
        tableInfo() {
            return {
                databaseName: this.databaseName,
                tableName: this.boxInfo.tableName,
                endpointInfo: this.endpointInfo
            };
        }
    },
    mounted() {
        window.addEventListener('resize', this.fetTableHeight);
    },
    beforeDestroy() {
        window.removeEventListener('resize', this.fetTableHeight);
    },
    methods: {
        resetHeight() {
            return new Promise((resolve, reject) => {
                this.tableHeight = 0;
                resolve();
            });
        },
        // 设置table高度
        fetTableHeight() {
            this.resetHeight().then(res => {
                this.tableHeight = this.$refs['table-box'].getBoundingClientRect().height - 120;
            });
        },
        async initData() {
            // 查询无筛选条件下数据
            const param = {
                selectFields: '',
                whereCondition: ''
            };
            this.renderTable(); // 构建表头
            // 页码重置
            this.recordPage = 1;
            this.recordSign = [{ threadId: 0, tableId: -1, recordId: 0 }];
            await this.handleQuery(param); // 查询表数据
            this.saveRecord = this.tableData?.[0]; // 初始化时保存一条记录
            this.recordCount = this.totalCount;
        },
        // 切换tab情况下数据带筛选条件查询
        async carryConditionsQuery() {
            // 获取当前表单筛选条件
            const { filterCondition, validFailed } = this.$refs['selectRef'].getQueryCondition();
            if (validFailed) {
                return this.$hMessage.error('表单验证失败!');;
            } else if (filterCondition?.selectFields || filterCondition?.whereCondition) {
                await this.$refs['selectRef'].handleQuery();
            } else {
                await this.initData();
            }
        },
        renderTable() {
            const columns = [{
                title: '数据记录号',
                width: 100,
                align: 'center',
                render: (h, params) => {
                    return h(
                        'Poptip', {
                            props: {
                                trigger: 'hover',
                                content: `表序号：${params.row.TableId} 线程号：${params.row.ThreadId} 记录号：${params.row.RecordId}`,
                                placement: 'bottom-start'
                            }
                        }, [
                            h('span', [params.row.RecordId])
                        ]
                    );
                }
            }];
            // 结构信息中可见的字段表格展示
            loopAllData(this.boxInfo.fields, (item) => {
                const fieldWidth = getSpanWidth(item.path, '14px') + 30;
                if (!item.visible) return;
                if (item.type === 'array') {
                    columns.push({
                        title: item.path,
                        key: item.path.replaceAll('.', '：'),
                        minWidth: fieldWidth,
                        render: (h, params) => {
                            return h('span', `array[${item.dimensional}]`);
                        }
                    });
                } else if (item.type === 'struct') {
                    columns.push({
                        title: item.path,
                        key: item.path.replaceAll('.', '：'),
                        minWidth: fieldWidth,
                        render: (h, params) => {
                            return h('span', '<struct>');
                        }
                    });
                } else {
                    columns.push({
                        title: item.path,
                        key: item.path.replaceAll('.', '：'),
                        ellipsis: true,
                        minWidth: fieldWidth
                    });
                }

            });
            columns.push({
                title: '操作',
                key: 'action',
                width: 80,
                fixed: 'right',
                render: (h, params) => {
                    return h('div', [
                        h(
                            'Button',
                            {
                                props: {
                                    type: 'text',
                                    size: 'small'
                                },
                                on: {
                                    click: () => {
                                        this.detialBoxInfo.status = true;
                                        this.detialBoxInfo.tableIndex = params.row._index;
                                        this.detialBoxInfo.viewMode = 1;
                                    }
                                }
                            },
                            '修改'
                        )
                    ]);
                }
            });
            this.columns = columns;
        },
        // 表记录翻页-上一页
        turnUp() {
            this.disUpturn = true;
            this.disDownturn = true;
            this.recordPage--;
            const pageSignObj = this.recordSign[this.recordPage - 1];
            this.handleQuery(this.conditionParam, pageSignObj);
            this.recordSign.pop();
        },
        // 表记录翻页-下一页
        turnDown() {
            this.disUpturn = true;
            this.disDownturn = true;
            this.recordSign.push(this.tableRecordes);
            this.recordPage++;
            this.handleQuery(this.conditionParam, this.tableRecordes);
        },
        // 切换每页条数
        pageSizeChange() {
            // 页码重置
            this.recordPage = 1;
            this.recordSign = [{ threadId: 0, tableId: -1, recordId: 0 }];
            this.$refs['selectRef'].handleQuery();
        },
        // 查询按钮
        queryTable(param) {
            // 页码重置
            this.recordPage = 1;
            this.recordSign = [{ threadId: 0, tableId: -1, recordId: 0 }];
            this.handleQuery(param);
        },
        // 查询表数据
        async handleQuery(params, { threadId = 0, tableId = -1, recordId = 0 } = {}) {
            this.conditionParam = params;
            this.loading = true;
            const data = {
                databaseName: this.databaseName,
                tableName: this.boxInfo.tableName,
                ...this.endpointInfo,
                tableId: tableId,
                threadId: threadId,
                recordId: recordId,
                expandAttribute: params.selectFields,
                whereCondition: params.whereCondition,
                page: this.recordPage,
                pageSize: this.pageSize,
                requestBody: this.saveRecord
            };
            try {
                const res = await getRecords(data);
                this.loading = false;
                if (res && res.tableName === this.boxInfo.tableName) {
                    const tableRecords = Array.isArray(res?.tableRecords) ? res.tableRecords : [];
                    const lastRecord = tableRecords[tableRecords.length - 1];
                    this.tableRecordes = {
                        threadId: lastRecord?.ThreadId,
                        tableId: lastRecord?.TableId,
                        recordId: lastRecord?.RecordId
                    };
                    this.totalCount = res.totalCount;
                    this.disDownturn = tableRecords.length < this.pageSize ? true : false;
                    this.disUpturn = this.recordPage === 1 ? true : false;
                    // 表格数据----平铺赋值
                    this.tableData = tableRecords;
                    this.flatTableData = tableRecords.map((item) => {
                        return flatStruct(item);
                    });
                    this.fetTableHeight();
                }
            } catch (err) {
                console.error(err);
                this.loading = false;
                this.tableData = [];
                this.flatTableData = [];
            }
        },
        // 选择表记录展示弹窗
        rowClick(row) {
            this.detialBoxInfo.status = true;
            this.detialBoxInfo.tableIndex = row[1];
            this.detialBoxInfo.viewMode = 0;
        }
    },
    components: { aTable, aButton, selectBox, modifyDetailModal }
};
</script>

<style lang="less" scoped>
@import url("@/assets/css/tab.less");

.tab-box {
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    flex-grow: 1;
    overflow: hidden;

    .select-box {
        height: calc(30% - 44px);
        overflow: auto;
    }

    .result-box {
        height: calc(70% + 34px);

        /deep/ .h-tabs-tabpane {
            overflow: hidden;
        }

        /deep/ .h-tabs-nav-wrap {
            float: none !important;
        }

        /deep/ .h-tabs-nav-right {
            position: absolute;
            right: 0;
            top: 15px;
            color: var(--font-color);
        }

        .page-box {
            position: fixed;
            bottom: 0;
            right: 0;
            margin: 0 5px 0 0;
            color: var(--font-color);
            font-size: var(--font-size-base);

            /deep/ .h-select {
                width: 100px;
            }
        }

        /deep/ .h-btn-disable {
            color: #878788;
            background-color: #222533;
            border-color: #5c636c;
        }

        /deep/ .h-poptip {
            width: 100%;
        }
    }
}
</style>
