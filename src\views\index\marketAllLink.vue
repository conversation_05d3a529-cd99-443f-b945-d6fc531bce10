<template>
    <div class="main">
        <div class="main-top">
            <span class="currenttime">今天是: {{ currentTime }}</span>
            <p v-if="exchangeList.length">{{ exchangeList[activeIndex].marketValue }}</p>
        </div>
        <div class="wrapper swiper-container">
            <div v-for="(ele, idx) in exchangeList" :key="idx" class="swiper-wrapper">
                <div class="swiper-slide swiper-scrollbar">
                    <div class="wrapper-top">
                        <div class="wrapper-top-canvas">
                            <a-title title="行情全链路应用节点关系"></a-title>
                            <networkTopo :id="exchangeList[idx].label" :ref="`network${idx}`" :idx="idx"
                                @update="updateTopoSpan" />
                        </div>
                        <div class="wrapper-top-data">
                            <a-title title="终端客户全链路质量监控"></a-title>
                            <div class="container">
                                <p class="c-title c-weight">FPGA全链路</p>
                                <div class="container-child container-fpga">
                                    <div @mouseleave="hiddenSuspend" @mousemove="moveSuspend($event)"
                                        @mouseenter="handleSuspendEnter($event, ele.FPGA.instanceStatusInfoList, 1, 'FPGA')">
                                        <p class="c-font c-explain">应用节点状态</p>
                                        <p class="c-weight">{{ ele.FPGA.aliveInstanceNum }} / {{
                                            ele.FPGA.instanceNum }}</p>
                                    </div>
                                    <div @mouseleave="hiddenSuspend" @mousemove="moveSuspend($event)"
                                        @mouseenter="handleSuspendEnter($event, ele.FPGA.delayNormalRatio, 2, 'FPGA')">
                                        <p class="c-font c-explain">时延正常率</p>
                                        <p class="c-weight">{{ maxNsqdelayNormalRatio(ele.FPGA.delayNormalRatio) }}</p>
                                    </div>
                                </div>
                                <p class="c-title c-weight">NSQ全链路</p>
                                <div class="container-child container-fpga">
                                    <div @mouseleave="hiddenSuspend" @mousemove="moveSuspend($event)"
                                        @mouseenter="handleSuspendEnter($event, ele.NSQ.instanceStatusInfoList, 1, 'NSQ')">
                                        <p class="c-font c-explain">应用节点状态</p>
                                        <p class="c-weight">{{ ele.NSQ.aliveInstanceNum }} / {{
                                            ele.NSQ.instanceNum }}</p>
                                    </div>
                                    <div @mouseleave="hiddenSuspend" @mousemove="moveSuspend($event)"
                                        @mouseenter="handleSuspendEnter($event, ele.NSQ.delayNormalRatio, 2, 'NSQ')">
                                        <p class="c-font c-explain">时延正常率</p>
                                        <p class="c-weight">{{ maxNsqdelayNormalRatio(ele.NSQ.delayNormalRatio) }}</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="wrapper-content">
                        <a-title title="行情数据时延趋势监控">
                            <slot>
                                <h-checkbox-group v-model="ele.exchangeInfo.indicators" style="display: inline-block;"
                                    @on-change="checkChange">
                                    <h-checkbox v-for="item in ele.exchangeInfo.checkList" :key="item.label"
                                        :label="item.label"
                                        :disabled="ele.exchangeInfo.indicators.length <= 1 && ele.exchangeInfo.indicators.includes(item.label)">
                                        <span>{{ item.name }}</span>
                                    </h-checkbox>
                                </h-checkbox-group>
                                <h-select v-model="ele.link" class="select-link" :clearable="false">
                                    <h-option v-for="item in ele.linkList" :key="item.value" :value="item.value">{{
                                        item.label
                                    }}</h-option>
                                </h-select>
                            </slot>
                        </a-title>
                        <div v-if="ele.spanData && idx === activeIndex" class="container" style="background: #262b40;">
                            <market-chart v-for="(item, index) in ele.exchangeInfo.indicators" :id="exchangeList[activeIndex].label" :key="index"
                                :ref="`chart${index}`" :name="item" :spanData="ele.spanData"
                                :link="exchangeList[activeIndex].link" />
                        </div>
                    </div>
                </div>
            </div>
            <!-- 是否显示分页器 -->
            <div class="swiper-pagination"></div>
        </div>
        <suspend v-if="suspendInfo.status" :suspendInfo="suspendInfo" />
    </div>
</template>

<script>
import Swiper from 'swiper';
// import 'swiper/css/swiper.min.css';
import { formatDate } from '@/utils/utils';
// import { exchangeList, checkList } from '@/config/exchangeConfig';
import { getMarketLinkQuality } from '@/api/httpApi';
import suspend from '@/components/marketAllLink/suspend';
import marketChart from '@/components/marketAllLink/marketChart';
import networkTopo from '@/components/marketAllLink/networkTopo';
import aTitle from '@/components/common/title/aTitle';
import _ from 'lodash';
export default {
    data() {
        return {
            currentTime: new Date(),
            exchangeList: [],
            suspendInfo: {
                status: false,
                top: 0,
                left: 0,
                data: [],
                model: 1,
                name: ''
            },
            activeIndex: 0,
            timer: null,
            linkData: {
                FPGA: {
                    instanceNum: 0,
                    aliveInstanceNum: 0,
                    delayNormalRatio: [],
                    instanceInfoList: []
                },
                NSQ: {
                    instanceNum: 0,
                    aliveInstanceNum: 0,
                    delayNormalRatio: [],
                    instanceInfoList: []
                }
            }
        };
    },
    async mounted() {
        await this.getConfigList();
        this.init();
    },
    methods: {
        init() {
            this.currentTime = formatDate(new Date());
            this.timer = setInterval(() => {
                that.getMarketLinkQuality(0);
            }, 3000);
            // 设置上下轮播效果，swiper配置
            const that = this;
            const  swiper = new Swiper('.swiper-container', {
                // 关闭swiper鼠标拖拽翻页
                noSwiping: false,
                noSwipingClass: 'stop-swiping',
                // 是否出现分页器
                pagination: {
                    el: '.swiper-pagination',
                    // 分页器是否可点击
                    clickable: true
                },
                observer: true, // 修改swiper自己或子元素时，自动初始化swiper
                observeParents: true, // 修改swiper的父元素时，自动初始化swiper
                // swiper方向
                direction: 'vertical',
                // 是否可以鼠标滚轮控制
                mousewheel: false,
                on: {
                    slideChangeTransitionStart: function () {
                        that.activeIndex = swiper.activeIndex;
                        that.timer && clearInterval(that.timer);
                        that.timer = setInterval(() => {
                            that.getMarketLinkQuality(that.activeIndex);
                        }, 3000);
                    }
                }
            });
        },
        // 显示悬浮层
        // eslint-disable-next-line max-params
        handleSuspendEnter(e, data, model, name) {
            this.suspendInfo.top = (e.clientY || 0) + 'px';
            this.suspendInfo.left = (e.clientX || 0) + 'px';
            this.suspendInfo.status = true;
            this.suspendInfo.data = data;
            this.suspendInfo.model = model;
            this.suspendInfo.name = name;
        },
        // 隐藏悬浮层
        hiddenSuspend() {
            this.suspendInfo.status = false;
        },
        // 移动悬浮层
        moveSuspend(e) {
            this.suspendInfo.top = (e.clientY - 60 || 0) + 'px';
            this.suspendInfo.left = (e.clientX + 20 || 0) + 'px';
        },
        checkChange(list) {
            this.exchangeList[this.activeIndex].exchangeInfo.indicators = [];
            const newList = [];
            list.forEach(element => {
                if (element === 'SM') {
                    newList[0] = element;
                } else if (element === 'IM') {
                    newList[1] = element;
                } else if (element === 'OBOE') {
                    newList[2] = element;
                } else if (element === 'TYT') {
                    newList[3] = element;
                }
            });

            setTimeout(() => {
                this.exchangeList[this.activeIndex].exchangeInfo.indicators = this.fileterNullEleOfArray(newList);
            }, 200);
        },
        // 计算时延正常率最大值
        maxNsqdelayNormalRatio(arr) {
            if (Array.isArray(arr) && arr.length) {
                return Math.max.apply(Math, arr) + '%';
            } else {
                return '-';
            }
        },
        // 去除数组中空元素
        fileterNullEleOfArray(arr) {
            return arr.filter(function (s) {
                return s && s.trim();
            });
        },
        // 获取交易所列表
        async getConfigList() {
            // const that = this;
            return new Promise((resolve, reject) => {
                // TODO：getConfigInfo废弃
                // getConfigInfo().then(res => {
                //     const _list = ['SSE', 'SZSE'];
                //     that.exchangeId = _list[0] || '';
                //     that.exchangeList.length = 0;
                //     for (const i in _list) {
                //         if (_list.hasOwnProperty(i)) {
                //             for (const t in exchangeList) {
                //                 if (exchangeList.hasOwnProperty(t)) {
                //                     if (exchangeList[t].label === _list[i]) {
                //                         that.exchangeList.push({
                //                             ...exchangeList[t],
                //                             spanData: '',
                //                             exchangeInfo: {
                //                                 indicators: ['SM', 'IM'],
                //                                 checkList
                //                             },
                //                             link: 'NSQ',
                //                             linkList: [
                //                                 {
                //                                     value: 'NSQ',
                //                                     label: 'NSQ链路'
                //                                 },
                //                                 {
                //                                     value: 'FPGA',
                //                                     label: 'FPGA链路'
                //                                 }
                //                             ],
                //                             ...this.linkData
                //                         });
                //                     }
                //                 }
                //             }
                //         }
                //     }
                //     resolve(true);
                // }).catch(err => {
                //     reject(err);
                // });
            });
        },
        // 更新SpanID
        updateTopoSpan(data, idx) {
            const obj = JSON.parse(JSON.stringify(this.exchangeList[idx]));
            obj.spanData = data;
            this.$set(this.exchangeList, idx, obj);
        },
        // 获取实时链路质量
        getMarketLinkQuality(id) {
            return new Promise((resolve, reject) => {
                const param = {
                    exchangeId: this.exchangeList[id].label,
                    linkNames: ['FPGA', 'NSQ']
                };
                getMarketLinkQuality(param).then(res => {
                    if (res.success && res.data?.length) {
                        res.data.forEach(ele => {
                            this.exchangeList[this.activeIndex][ele.linkName] = { ...ele };
                        });

                        if (this.suspendInfo.status) {
                            switch (this.suspendInfo.model) {
                                case 1:
                                    this.suspendInfo.data = _.find(res.data, o => { return o.linkName === this.suspendInfo.name; })?.instanceStatusInfoList;
                                    break;
                                case 2:
                                    this.suspendInfo.data = _.find(res.data, o => { return o.linkName === this.suspendInfo.name; })?.delayNormalRatio;
                                    break;
                                default:
                                    return;
                            }
                        }

                        resolve(true);
                        return;
                    }
                    this.suspendInfo.data = [];
                    resolve(false);
                }).catch(err => {
                    reject(err);
                });
            });
        }
    },
    components: { suspend, marketChart, networkTopo, aTitle }
};
</script>

<style lang="less" scoped>
@import url("@/assets/css/input.less");

.main {
    .wrapper {
        height: calc(100% - 73px);

        .swiper-wrapper {
            display: flex;
            flex-direction: column;
            width: 100%;
            height: 100%;
            box-sizing: border-box;
            padding: 15px 15px 0;
            overflow: hidden;
            overflow-y: hidden;
            overflow-x: hidden;

            &:hover {
                overflow-y: overlay;
            }

            & > div {
                background: var(--wrapper-color);
            }

            .wrapper-top {
                display: flex;
                width: 100%;
                height: 320px;
                background: var(--wrapper-color);

                .wrapper-top-canvas {
                    flex: 1;

                    #network-monitor {
                        height: 272px;
                        cursor: pointer;
                    }
                }

                .wrapper-top-data {
                    width: 35%;
                    margin-left: 20px;
                }

                .container {
                    width: 100%;
                    height: 272px;

                    .container-child {
                        width: 100%;
                        display: flex;
                        justify-content: space-between;

                        & > div {
                            width: 48.5%;
                            height: 72px;
                            border-radius: 4px;
                            background: var(--primary-color);
                            cursor: pointer;

                            p {
                                text-align: center;
                                margin-top: 6px;
                            }

                            .c-explain {
                                margin-top: 11px;
                            }

                            &:hover {
                                background: #aaf6ff1a;
                                border: 1px solid #aaf6ff;

                                & > p {
                                    color: #aaf6ff !important;
                                }
                            }
                        }
                    }

                    .c-title {
                        padding: 15px 4px;
                    }

                    .c-weight {
                        color: var(--font-color);
                        font-size: var(--font-size);
                        font-weight: 500;
                    }

                    .c-font {
                        color: var(--font-color);
                        font-size: var(--font-size-base);
                    }
                }
            }
        }
    }

    // 轮播图
    /deep/ .swiper-pagination-bullet {
        background: var(--font-color);
        opacity: 0.75;
    }

    /deep/ .swiper-pagination-bullet-active {
        border-color: var(--link-color) !important;
        background: var(--link-color) !important;
        opacity: 1;
    }

    /deep/ .swiper-container-vertical > .swiper-pagination-bullets {
        right: 1px;
    }

    .select-link {
        width: 120px;
        float: right;
        margin: 4px 10px 0 0;
    }
}
</style>
