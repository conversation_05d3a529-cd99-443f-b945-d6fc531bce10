<template>
    <div>
        <h-msg-box-safe
            v-model="modalData.status"
            :escClose="true"
            :mask-closable="false"
            title="重置密码"
            width="600"
            class="wrap-msgbox"
        >
            <h-form
                ref="formValidate"
                :model="formValidate"
                :label-width="80"
                :rules="ruleInline"
                >
                <h-form-item label="用户名" prop="userName">
                    <h-input
                        v-model.trim="formValidate.userName"
                        placeholder="用户名"
                        disabled
                    ></h-input>
                </h-form-item>
                <h-form-item label="密码" prop="password" required>
                    <h-input v-model.trim="formValidate.password" :type="showPassWord ? 'text':'password'" placeholder="请输入或自动生成密码" :maxlength="20"
                    :icon="showPassWord ? 'browse_fill' : 'eye-disabled'" autocomplete="new-password" style="width: 81%;" @on-click="isShowPassWord"></h-input>
                    <a-button @click="generatePassword">自动生成</a-button>
                    <p class='input-text'>长度8-20位，必须包含字母 (a~zA~Z)、数字(0~9)、特殊符号(!、$、#、@、*、_)3种</p>
                </h-form-item>
            </h-form>

            <template v-slot:footer>
                <a-button @click="modalData.status = false">取消</a-button>
                <a-button type="primary" :loading="loading" @click="submitConfig">确定</a-button>
            </template>
        </h-msg-box-safe>
    </div>
</template>

<script>
import aButton from '@/components/common/button/aButton';
import { validatePass } from '@/utils/validate';
export default {
    props: {
        modalInfo: {
            type: Object,
            default: null
        }
    },
    data() {
        return {
            modalData: this.modalInfo,
            loading: false,
            formValidate: {
                id: this.modalInfo.id,
                userName: this.modalInfo.userName,
                password: ''
            },
            ruleInline: {
                password: [
                    {
                        type: 'string',
                        min: 8,
                        max: 20,
                        message: '请输入密码8~20位',
                        trigger: 'blur'
                    },
                    { validator: validatePass, trigger: 'blur' }
                ]
            },
            showPassWord: false
        };
    },
    mounted(){
        this.$refs['formValidate'].resetFields();
    },
    methods: {
        submitConfig() {
            this.$refs['formValidate'].validate((valid) => {
                if (valid) {
                    this.$emit('reset-password', this.formValidate);
                    this.modalData.status = false;
                }
            });
        },
        isShowPassWord(){
            this.showPassWord = !this.showPassWord;
        },
        // 自动生成密码
        generatePassword() {
            const letters = 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ';
            const numbers = '0123456789';
            const specialChars = '!@#$*_';

            const minLength = 8;
            const maxLength = 20;
            const length = Math.floor(Math.random() * (maxLength - minLength + 1)) + minLength;

            let password = '';

            password += letters[Math.floor(Math.random() * letters.length)];
            password += numbers[Math.floor(Math.random() * numbers.length)];
            password += specialChars[Math.floor(Math.random() * specialChars.length)];

            const allChars = letters + numbers + specialChars;

            for (let i = password.length; i < length; i++) {
                const char = allChars[Math.floor(Math.random() * allChars.length)];
                password += char;
            }
            password = password.split('').sort(() => Math.random() - 0.5).join('');

            this.formValidate.password = password;
        }
    },
    components: { aButton }
};
</script>
<style lang="less" scoped>

.wrap-msgbox {
    .modal-text {
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
        cursor: pointer;
    }

    .input-text {
        font-size: 12px;
        color: #9296a1;
        line-height: 20px;
        font-weight: 400;
    }
}
</style>
