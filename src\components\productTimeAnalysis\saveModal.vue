<template>
    <div>
        <h-msg-box-safe v-model="modalData.status" :escClose="true" :mask-closable="false" title="保存查询" width="50">
            <h-form ref="formValidate" :model="formValidate" :label-width="100" :rules="ruleValidator">
                <h-form-item label="查询时间" prop="time" required>
                    <h-row class="row">
                        <h-col span="10">
                            <h-select v-model="formValidate.timeType" set-def-select :positionFixed="true" :clearable="false">
                                <h-option key="day" value="today">每日</h-option>
                                <h-option v-if="modalData.type ==='entrustDetail'" key="yesterday" value="yesterday">昨日</h-option>
                                <h-option v-if="modalData.type ==='entrustSummary'" key="week" value="week">本周</h-option>
                                <h-option v-if="modalData.type ==='entrustSummary'" key="month" value="month">本月</h-option>
                            </h-select>
                        </h-col>
                        <h-col span="1">&nbsp;</h-col>
                        <h-col span="13">
                            <h-time-picker v-model="formValidate.time" type="timerange" confirm :editable="false" :clearable="false"
                                placement='bottom-start'
                                placeholder="交易时间" :positionFixed="true"></h-time-picker>
                        </h-col>
                    </h-row>
                </h-form-item>
                <h-form-item label="查询名称" prop="name" required>
                    <h-row class="row">
                        <h-input v-model="formValidate.name" placeholder="请输入查询名称"></h-input>
                    </h-row>
                </h-form-item>
            </h-form>
            <template v-slot:footer>
                <a-button @click="modalData.status = false">取消</a-button>
                <a-button type="primary" :loading="loading" @click="submitConfig">确定</a-button>
            </template>
        </h-msg-box-safe>
    </div>
</template>

<script>
import aButton from '@/components/common/button/aButton';
export default {
    props: {
        modalInfo: {
            type: Object,
            default: null
        }
    },
    data() {
        const nameRule = function (_rule, value, callback) {
            if (value?.length > 15) {
                return callback(new Error('字符长度数不得超过15!'));
            }
            callback();
        };
        return {
            modalData: this.modalInfo,
            loading: false,
            ruleValidator: {
                name: [
                    { required: true, message: '不能为空', trigger: ['blur', 'change'] },
                    { validator: nameRule, trigger: ['blur', 'change'] }
                ]
            },
            formValidate: {
                name: '',
                timeType: 'today',
                time: this.modalInfo.time || ['09:15:00', '15:00:00']
            }
        };
    },
    methods: {
        submitConfig() {
            const that = this;
            this.$refs['formValidate'].validate(async (valid) => {
                if (valid) {
                    if (this.formValidate.name.length > 15 || !this.formValidate.time) {
                        return;
                    }
                    that.loading = true;
                    try {
                        this.$emit('update', {
                            name: this.formValidate.name,
                            timeType: this.formValidate.timeType,
                            startTime: this.formValidate.time?.[0] || '09:15:00',
                            endTime: this.formValidate.time?.[1] || '15:00:00',
                            queryBody: this.modalData.queryBody
                        });
                        that.$hMessage.success('保存成功!');
                        that.modalInfo.status = false;
                    } catch (err) {
                        that.loading = false;
                    }
                }
            });
        }
    },
    components: { aButton }
};
</script>
