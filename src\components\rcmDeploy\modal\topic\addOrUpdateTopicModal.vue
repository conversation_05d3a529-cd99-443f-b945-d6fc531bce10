<!-- eslint-disable vue/no-deprecated-slot-attribute -->
<template>
    <div>
        <h-msg-box-safe v-model="transportInfo.status" :escClose="true" :mask-closable="false"
            :title="`${transportInfo.type === 'add' ? '创建' : '配置'}主题`" width="60" class="wrap-msgbox"
            @on-open="getCollections" @on-close="cancelMethod">
            <h-form ref="saveValidate" :model="saveValidate" :label-width="160">
                <div class="title">
                    <span>基本信息</span>
                </div>
                <div class="border"></div>
                <h-row>
                    <h-col span="12">
                        <h-form-item label="主题:" prop="topic" required>
                            <h-input v-model.trim="saveValidate.topic" type="text" :maxlength="50"></h-input>
                        </h-form-item>
                    </h-col>
                    <h-col span="12">
                        <h-form-item label="分区号:" prop="partition" required>
                            <h-input v-model.trim="saveValidate.partition" :specialFilter="true" :specialDecimal="2"
                                :maxlength="50"></h-input>
                        </h-form-item>
                    </h-col>
                </h-row>
                <h-row>
                    <h-col span="12">
                        <h-form-item label="通讯地址:" prop="addr" :validRules="ip4Rule" required>
                            <h-input v-model="saveValidate.addr" type="text"></h-input>
                        </h-form-item>
                    </h-col>
                    <h-col span="12">
                        <h-form-item label="端口:" prop="port" :validRules="portRule" required>
                            <h-input v-model="saveValidate.port" type="int" :maxlength="5"></h-input>
                        </h-form-item>
                    </h-col>
                </h-row>
                <h-row>
                    <h-col span="24">
                        <h-form-item label="查询标签:">
                            <div style="max-height: 240px; overflow: scroll;">
                                <a-button type="dashed" size="small" icon="plus-round" color="#b0b4ba"
                                    @click="handleAdd">添加</a-button>
                                <h-tag v-for="item in saveValidate.tags" :key="item" :name="item" closable
                                    @on-close="handleClose">
                                    {{ item }}
                                </h-tag>
                            </div>
                        </h-form-item>
                    </h-col>
                </h-row>
                <div class="title" style="position: relative; margin-top: 10px;">
                    <span>高级配置</span>&nbsp;&nbsp;<h-icon :name="buttonStatus ? 'packup' : 'unfold'" size="16" style="cursor: pointer;"
                        @on-click='changebuttonStatus'></h-icon>
                    <span style="position: absolute; right: 140px;">引用模板</span>
                    <h-select v-model="saveValidate.ref" placeholder="请选择" :positionFixed="true"
                        style="width: 120px; position: absolute; right: 0; top: -6px;" :clearable="true"
                        @on-change="handleSelectChange">
                        <h-option v-for="item in templateNames" :key="item.value" :value="item.value">{{ item.label
                        }}</h-option>
                    </h-select>
                </div>
                <div class="border"></div>
            </h-form>
            <theme-setting v-show="buttonStatus" ref="theme-setting" :rcmId="rcmId" :saveValidateData="saveValidate" />
            <template v-slot:footer>
                <a-button @click="cancelMethod">取消</a-button>
                <a-button :loading="btnLoading" type="primary" @click="saveSubmit">确定</a-button>
            </template>
        </h-msg-box-safe>
        <create-tag-modal v-if="inputInfo.status" :modalInfo="inputInfo" @add="addTags" />
    </div>
</template>

<script>
import { topicTempDefault } from '@/config/rcmDefaultConfig';
import aButton from '@/components/common/button/aButton';
import themeSetting from '@/components/rcmDeploy/modal/themeSetting';
import createTagModal from '@/components/rcmDeploy/modal/topic/createTagModal';
import { getTemplateDetail } from '@/api/rcmApi';
import { validatePort } from '@/utils/validate';
export default {
    props: {
        modalInfo: {
            type: Object,
            default: null
        },
        rcmId: {
            type: String,
            default: null
        }
    },
    components: { aButton, themeSetting, createTagModal },
    data() {
        return {
            transportInfo: this.modalInfo,
            saveValidate: {
                topic: '',
                partition: 1,
                addr: '*********',
                port: 27000,
                tags: [],
                ref: '',
                ...topicTempDefault
            },
            ip4Rule: ['ip4'],
            portRule: [{ test: validatePort, trigger: 'blur' }],
            btnLoading: false,
            templateNames: [],
            inputInfo: {
                status: false
            },
            isChange: true,
            buttonStatus: true
        };
    },
    mounted() {

    },
    methods: {
        // 初始化数据
        getCollections() {
            this.isChange = false;
            // 模板
            this.templateNames = [];
            this.transportInfo.transportTempList.forEach(item => {
                this.templateNames.push({
                    value: item.name,
                    label: item.name
                });
            });
            this.saveValidate = {
                ...this.saveValidate,
                ...this.transportInfo.data,
                tags: [...this.transportInfo.data.tags || []],
                ref: this.isExistRef(this.transportInfo.data?.ref) ? this.transportInfo.data?.ref : ''
            };
            this.$nextTick(() => {
                this.$refs['theme-setting'].init();
            });
            setTimeout(() => {
                this.isChange = true;
            }, 1000);
        },
        // 判断模板是否存在
        isExistRef(ref){
            const refs = this.templateNames.filter(v => v.value === ref);
            return refs.length;
        },
        // select切换
        async handleSelectChange(val) {
            if (this.isChange) {
                if (!val) return;
                const param = {
                    id: this.transportInfo.id,
                    name: val,
                    templateType: 'transport'
                };
                const res = await getTemplateDetail(param);
                if (res.code === '200') {
                    const transport = res.data?.template?.transport || {};
                    this.saveValidate.heartbeatIntervalMilli = transport.heartbeatIntervalMilli;
                    this.saveValidate.heartbeatTimeoutMilli = transport.heartbeatTimeoutMilli;
                    this.saveValidate.ackIntervalMilli =  transport.ackIntervalMilli;
                    this.saveValidate.ackTimeoutMilli = transport.ackTimeoutMilli;
                    this.saveValidate.useSharedMemory = transport.useSharedMemory;
                    this.saveValidate.asynchronousRms = transport.asynchronousRms;
                    this.saveValidate.hasBypass = transport.hasBypass;
                }
                this.$nextTick(() => {
                    this.$refs['theme-setting'].init();
                });
            }
        },
        // 删除标签
        handleClose(event, name) {
            const index = this.saveValidate.tags.indexOf(name);
            this.saveValidate.tags.splice(index, 1);
        },
        // 添加标签
        handleAdd() {
            this.inputInfo.id = this.transportInfo.id;
            this.inputInfo.type = 'topic';
            this.inputInfo.context = this.saveValidate.topic;
            this.inputInfo.status = true;
            this.inputInfo.title = '添加标签';
            this.inputInfo.tags = this.saveValidate.tags;
        },
        addTags(val) {
            this.saveValidate.tags = [...val.selection];
        },
        // 点击取消按钮
        cancelMethod() {
            this.transportInfo.status = false;
            this.$refs['saveValidate'].resetFields();
            this.saveValidate = {
                topic: 'topic',
                partition: 1,
                addr: '*********',
                port: 27000,
                tags: [],
                ref: '',
                heartbeatIntervalMilli: 1000,
                heartbeatTimeoutMilli: 3000,
                ackIntervalMilli: 1000,
                ackTimeoutMilli: 3000,
                useSharedMemory: false,
                hasBypass: false,
                asynchronousRms: false
            };
        },
        // 点击确定
        saveSubmit() {
            this.$refs['saveValidate'].validate((valid) => {
                const themeRes = this.$refs['theme-setting'].getFileData();
                if (valid && themeRes) {
                    this.$emit('create-or-update-topic', {
                        ...this.saveValidate,
                        ...themeRes,
                        id: Number(this.saveValidate?.id) || null,
                        topic: this.saveValidate.topic,
                        partition: this.saveValidate.partition,
                        addr: this.saveValidate.addr,
                        port: Number(this.saveValidate.port),
                        tags: this.saveValidate.tags,
                        ref: this.saveValidate.ref,
                        heartbeatIntervalMilli: themeRes.heartbeatIntervalMilli,
                        heartbeatTimeoutMilli: themeRes.heartbeatTimeoutMilli,
                        ackIntervalMilli: themeRes.ackIntervalMilli,
                        ackTimeoutMilli: themeRes.ackTimeoutMilli,
                        useSharedMemory: themeRes.useSharedMemory
                    });
                    this.transportInfo.status = false;
                }
            });
        },
        changebuttonStatus() {
            this.buttonStatus = !this.buttonStatus;
        }
    }
};
</script>
<style lang="less" scoped>
@import url("@/assets/css/tag.less");

.wrap-msgbox {
    .title {
        padding-left: 12px;

        span {
            font-size: 14px;
            font-weight: 600;
        }
    }

    .border {
        border-top: 1px solid #b0b4ba;
        margin-bottom: 15px;
        margin-top: 10px;
    }

    .h-form-item {
        margin-bottom: 10px;
    }
}
</style>
