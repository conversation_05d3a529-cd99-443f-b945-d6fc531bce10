.info-bar {
    width: 100%;
    margin: 10px 5px;
    border-radius: 5px;
    background: var(--primary-color);

    &:hover {
        cursor: pointer;
    }

    .info-bar-title {
        width: 100%;
        color: var(--font-opacity-color);
        text-align: center;
        font-size: 12px;
        height: 42px;
        line-height: 42px;
        padding: 0 5px;
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
    }

    .info-bar-text {
        font-family: DINCond-Black;
        color: var(--font-color);
        font-weight: 500;
        padding: 0 10px 10px;
        font-size: 18px;
        height: calc(100% - 42px);
        display: flex;
        justify-content: center;
        align-items: center;

        span {
            width: 93%;
            text-align: center;
            overflow: hidden;
            white-space: nowrap;
            text-overflow: ellipsis;
        }
    }

    .info-process-bar {
        height: calc(100% - 42px);
        display: flex;
        justify-content: center;
        align-items: center;
        flex-direction: column;

        .process .process-bar-box {
            margin: 0;
            background-color: transparent;
        }
    }

    .info-bar-detail {
        color: var(--font-color);
        line-height: 30px;
        width: 100%;
        padding: 5px 15px;
        height: calc(100% - 42px);

        p {
            display: flex;

            span {
                width: 50%;
                text-align: left;
                overflow: hidden;
                white-space: nowrap;
                text-overflow: ellipsis;
            }
        }
    }

    & > .info-bar-combine {
        width: 100%;
    }

    & > .info-bar-table {
        padding: 0 10px 10px;

        .obs-table .h-table th {
            background-color: var(--monitor-color);
        }

        .obs-table {
            margin: 0;
            padding: 0;
        }

        .a-table {
            margin: 0;
            padding: 0;
        }

        .h-table {
            margin: 0;
            padding: 0;
        }

        .table-box {
            margin: 0;
        }
    }

    & > .info-bar-monitor {
        height: calc(100% - 42px);
        padding: 5px 5px 10px;
        display: flex;

        .info-bar-monitor-item,
        .info-bar-monitor-item-can-click {
            height: 100%;
            background-color: var(--monitor-color);
            flex: 1;
            width: 0;
            margin: 0 5px;
            border: 1px solid transparent;
            border-radius: 4px;
        }

        .info-bar-monitor-item:hover {
            cursor: not-allowed; /* 展示禁用光标 */
        }

        .info-bar-monitor-item-can-click:hover {
            cursor: pointer;
            border: 1px solid var(--icon-hover);
        }
    }

    & > .info-bar-monitor-grid {
        display: grid;
        grid-auto-rows: row dense;
        grid-gap: 10px 0;
        padding: 5px 5px 10px;
        border-radius: 4px;

        .info-bar-monitor-item,
        .info-bar-monitor-item-can-click {
            height: 100%;
            background-color: var(--monitor-color);
            margin: 0 5px;
            border: 1px solid transparent;
        }

        .info-bar-monitor-item:hover {
            cursor: not-allowed; /* 展示禁用光标 */
        }

        .info-bar-monitor-item-can-click:hover {
            cursor: pointer;
            border: 1px solid var(--icon-hover);
        }
    }

    .info-bar-monitor,
    .info-bar-monitor-grid {
        .h-poptip {
            display: block;
            flex: 1;
            overflow: hidden;
        }

        .h-poptip,
        .h-poptip-rel {
            display: block;
            height: 100%;
        }
    }

    .info-bar-perform-detail {
        padding: 0 5px;
        // background-color: var(--monitor-color);

        .info-bar-perform-tag {
            height: 20px;
            line-height: 20px;
            margin: 0 5px;
            text-align: right;
            overflow: hidden;
            white-space: nowrap;
            text-overflow: ellipsis;
            color: var(--font-color);

            span {
                padding: 5px;
                background: #444a61;
                border-radius: 2px;
            }
        }

        .info-bar-perform-text-detail {
            font-family: PingFangSC-Regular;
            padding: 7px 10px;
            max-width: 100%;
            overflow: hidden;
            display: flex;
            align-items: baseline;
            justify-content: center;

            span {
                color: var(--font-opacity-color);
                overflow: hidden;
                white-space: nowrap;
                text-overflow: ellipsis;
                display: inline-block;
            }

            .info-bar-perform-label {
                max-width: 20%;
            }

            .info-bar-perform-text {
                font-family: DINCond-Black;
                max-width: 60%;
                font-size: 42px;
                color: var(--font-color);
                height: 48px;
                line-height: 48px;
                font-weight: 900;
                padding: 0 5px;
            }
        }
    }

    .info-bar-perform-obj {
        display: grid;
        grid-template-columns: repeat(2, 1fr);
        grid-auto-rows: row dense;
        text-align: center;
    }

    .info-bar-perform-obj-detail {
        height: 28px;
        line-height: 28px;
        display: flex;
        color: var(--font-opacity-color);
        background: #292e44;
        padding: 0 8px;
        border: 1px solid var(--monitor-color);

        span {
            flex: 1;
            width: 0;
            text-align: left;
            overflow: hidden;
            white-space: nowrap;
            text-overflow: ellipsis;
        }
    }
}

.info-bar-no-color {
    background: none;

    .info-bar-table {
        padding: 0;
    }

    .obs-table .h-table-wrapper {
        border: 1px solid var(--primary-color);
    }
}

//info-sum-bar
.info-sum-bar {
    width: 100%;
    background: var(--wrapper-color);
    margin-top: 15px;

    .info-row {
        display: flex;
        flex-direction: row;
        padding: 0 5px;

        .info-bar {
            width: 0;
            flex: 1;
        }
    }

    .info-column {
        display: flex;
        flex-direction: column;
        padding: 5px 0;

        .info-bar {
            margin: 5px 10px;
        }
    }

    .info-grid {
        display: grid;
        grid-auto-rows: row dense;
        grid-gap: 10px;
        padding: 10px 15px 10px 5px;
        overflow: auto;

        .info-bar {
            margin: 0 5px;
            border: 2px solid transparent;
        }
    }

    .info-bar-can-click:hover {
        cursor: pointer;
        border: 1px solid var(--icon-hover);
    }

    .scroll-bar {
        max-height: 150px;
        overflow: auto;
    }

    /* 滚动条自定义 */
    .scroll-bar::-webkit-scrollbar {
        width: 0;
        height: 6px;
    }

    /* 定义滚动条轨道 */
    .scroll-bar::-webkit-scrollbar-track {
        display: none;
    }

    /* 滑块 */
    .scroll-bar::-webkit-scrollbar-thumb {
        border-radius: 6px;
        background-color: #54565d;
    }
}

//info-scroll-bar
.info-scroll-bar {
    background: var(--wrapper-color);
    margin-top: 15px;
}

.info-scroll-bar-row {
    background: var(--wrapper-color);
    margin-top: 15px;

    .content-scroll {
        white-space: nowrap;
    }

    .scroll-content {
        display: inline-block;
    }

    .info-bar {
        display: inline-block;
    }
}

// obj组件里插槽样式
.slot-button.h-btn-text {
    padding: 0;
    margin: 0 0 0 5px;
    color: var(--link-color);

    :hover {
        color: var(--link-color);
        text-decoration: underline;
    }
}

.info-bar-clickable {
    cursor: pointer;
}


// min版本样式
.info-bar-min {
    .info-bar-title {
        font-size: 12px;
        height: 36px;
        line-height: 36px;
    }

    .info-bar-text {
        padding: 0 10px 6px;
        height: calc(100% - 36px);
    }

    .obs-title {
        height: 36px;
        line-height: 36px;
        font-size: 14px;

        .title-box {
            top: 2;
            height: 36px;
        }
    }

    .obs-title.obs-title-default {
        height: 36px;
        line-height: 36px;
        font-size: 14px;
    }

    .info-process-bar {
        height: calc(100% - 36px);
    }

    .info-bar-detail {
        height: calc(100% - 36px);
    }


    & > .info-bar-monitor {
        height: calc(100% - 36px);
    }
}

.info-sum-bar-min,
.info-scroll-bar-min {
    .obs-title {
        height: 36px;
        line-height: 36px;
        font-size: 14px;

        .title-box {
            top: 2;
            height: 36px;
        }
    }

    .obs-title.obs-title-default {
        height: 36px;
        line-height: 36px;
        font-size: 14px;
    }
}


// 状态
.info-bar-status-success {
    background-color: #3f6b49 !important;

    &:hover {
        background-color: #3f6b49 !important;
    }
}

.info-bar-status-warn {
    background-color: #4e3b29 !important;

    &:hover {
        background-color: #755b42 !important;
    }
}

.info-bar-status-error {
    background-color: #4c2132 !important;

    &:hover {
        background-color: #73354d !important;
    }
}
