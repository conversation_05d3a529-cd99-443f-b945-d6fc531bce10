<!-- /**
 * <AUTHOR>
 * @email <EMAIL>
 * @create date 2024-09-10 11:15:07
 * @modify date 2024-09-10 11:15:07
 * @desc [rcm組播域]
 */ -->

<template>
  <div>
    <inner-zone ref="inner" :rcmId="id" @turnToContext="turnToContext" />
    <inter-zone ref="inter" :rcmId="id" />
  </div>
</template>
<script>
import { defineComponent, toRefs } from 'vue';
import InnerZone from './innerZone.vue';
import InterZone from './interZone.vue';
export default defineComponent({
    name: 'RcmMulticast',
    components: {
        InnerZone,
        InterZone
    },
    props: {
        rcmId: String
    },
    setup(props, context) {
        const turnToContext = (names) => context.emit('turnToContext', names);
        const { rcmId } = toRefs(props);
        return {
            id: rcmId,
            turnToContext
        };
    },
    methods: {
        initData() {
            this.$refs.inner.initData();
            this.$refs.inter.initData();
        }
    }
});
</script>
