<template>
    <div>
        <h-drawer
            ref="drawer-box"
            v-model="modalData.status"
            title="日志记录详情"
            width="50"
            @on-close="handleClose">
            <obs-table
                border
                :height="logTableHeight"
                :columns="logColumns"
                :tableData="logTableData"
                :loading="logLoading"
                showTitle
            />
            <br/>
            <div class="page-box">
                <a-button
                    type="dark"
                    size="small"
                    :disabled="logEndMsgNos.length <= 1 || buttonLoading"
                    @click="()=>handlePrev()">上一页</a-button>
                &nbsp;&nbsp;
                <a-button
                    type="dark"
                    size="small"
                    :disabled="logTableData.length < pageSize || buttonLoading"
                    @click="()=>handleNext()">下一页</a-button>
            </div>
        </h-drawer>
    </div>
</template>

<script>
import obsTable from '@/components/common/obsTable/obsTable';
import aButton from '@/components/common/button/aButton';
import recordMsgPoptip from '@/components/ldpLogCenter/recordMsgPoptip.vue'; // render中使用
import { getManagerProxy } from '@/api/mcApi';

export default {
    name: 'LogDetailDrawer',
    components: {
        obsTable,
        aButton
    },
    props: {
        modalInfo: {
            type: Object,
            default: () => {}
        },
        instanceData: {
            type: Object,
            default: () => {}
        },
        timeout: {
            type: Number,
            default: 3
        }
    },
    data() {
        return {
            modalData: this.modalInfo,
            logTableHeight: 0,
            logColumns: [
                {
                    title: '记录号',
                    key: 'RecordNo',
                    ellipsis: true,
                    width: 100
                },
                {
                    title: '表名',
                    key: 'TableName',
                    ellipsis: true,
                    width: 120
                },
                {
                    title: '操作记录',
                    key: 'Type',
                    ellipsis: true,
                    width: 120
                },
                {
                    title: '记录字段信息',
                    key: 'RecordInfo',
                    ellipsis: true,
                    minWidth: 150,
                    render: (h, params) => {
                        const data = JSON.stringify(params?.row?.RecordInfo || {}) || '';
                        const tableData = Object.entries(params?.row?.RecordInfo || {}).map(([key, value]) => ({ key, value }));
                        return <recordMsgPoptip data={data} tableData={tableData}></recordMsgPoptip>;
                    }
                }
            ],
            logTableData: [],
            logLoading: false,
            buttonLoading: false,
            logEndMsgNos: [], // 结束操作编号, 翻页后操作编号
            pageSize: 10
        };
    },
    computed: {

    },
    mounted() {
        this.handleDrawerOpen();
        window.addEventListener('resize', this.fetTableHeight);
    },
    beforeDestroy() {
        window.removeEventListener('resize', this.fetTableHeight);
    },
    methods: {
        handleDrawerOpen() {
            this.clearlogData();
            this.getMsgBodyInfo();
            this.fetTableHeight();
        },
        handleClose() {
            this.clearlogData();
            this.modalData.status = false;
        },
        // 上一页
        handlePrev() {
            this.getMsgBodyInfo('minus');
        },
        // 下一页
        handleNext() {
            this.getMsgBodyInfo('plus');
        },
        clearlogData() {
            this.logTableData = [];
            this.logEndMsgNos = [];
        },
        // 接口请求------具体行字段记录信息
        async getMsgBodyInfo(type) {
            this.logLoading = true;
            this.buttonLoading = true;
            const nextMsgNo = type === 'minus' ? this.logEndMsgNos?.[this.logEndMsgNos?.length  - 3] || 0
                :  this.logEndMsgNos?.[this.logEndMsgNos?.length - 1] || 0;
            const param = [
                {
                    manageProxyIp: this.instanceData?.ip,
                    manageProxyPort: this.instanceData?.manageProxyPort,
                    pluginName: 'ldp_todb',
                    funcName: 'GetMsgBodyInfo',
                    params: {
                        MsgNo: this.modalData?.MsgNo,
                        StartActionNo: nextMsgNo,
                        ActionCount: this.pageSize,
                        TimeoutMilli: (this.timeout ?? 3) * 1000
                    }
                }
            ];
            try {
                const res = await getManagerProxy(JSON.stringify(param), this.proxyTimeout * 1000);
                if (res.code === '200') {
                    const resData = res?.data?.[0] || {};
                    if (resData?.ErrorNo){
                        this.$hMessage.error({
                            content: resData?.ErrorMsg || '查询失败',
                            duration: 2.5
                        });
                        return;
                    }
                    if (type === 'minus') {
                        this.logEndMsgNos.pop();
                    } else {
                        this.logEndMsgNos.push(resData?.NextActionNo);
                    }
                    this.logTableData = resData?.Actions || [];
                }
            } catch (err) {
                console.log(err);
            } finally {
                this.logLoading = false;
                this.buttonLoading = false;
            }
        },
        fetTableHeight() {
            this.logTableHeight = this.$refs['drawer-box']?.$el?.offsetTop - (window.LOCAL_CONFIG.HEADER_VISIBLE === true ? 240 : 160);;
        }
    }
};
</script>

<style lang="less" scoped>
@import url("@/assets/css/drawer.less");

.page-box {
    margin-right: 15px;
    color: var(--font-color);
    text-align: right;
}

/deep/ .obs-table {
    height: auto;
}
</style>
