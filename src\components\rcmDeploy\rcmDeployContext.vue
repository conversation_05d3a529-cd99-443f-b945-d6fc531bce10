<template>
    <div class="context-box rcm-box">
        <!-- 占位符，用于解决从别的页面跳转到该tab时，切换浏览器窗口后无法定位到该tab的hui bug -->
        <input id="rcm-inpt-placeholder" />
        <rcm-normal-title-table
            ref="table"
            title="筛选条件"
            :formItems="formItems"
            :columns="columns"
            :loading="loading"
            :tableData="tableData"
            :total="total"
            :hasSetTableColumns="false"
            :showTitle="true"
            @query="handleQuery"
            @clear="handleClear"
            @add-tag="handleTag('add')"
            @del-tag="handleTag('del')"
            @selection="tableSelection"
            @create="createOrUpdateContextModal(false, 'add')"
        >
        </rcm-normal-title-table>
        <add-or-update-context-Modal
            :modalInfo="modalInfo"
            @create-or-update-context="createOrUpdateContext"/>
        <create-tag-modal
            v-if="inputInfo.status"
            :modalInfo="inputInfo"
            @add="addTags"
            @del="delTags" />
        <!--上下文配置信息弹窗-->
        <context-info-modal
            v-if="ctxConfigInfo.status"
            :modalInfo="ctxConfigInfo"
            :rcmId="rcmId" />
    </div>
</template>
<script>
import { mapState } from 'vuex';
import rcmNormalTitleTable from '@/components/common/bestTable/rcmNormalTitleTable';
import createTagModal from '@/components/rcmDeploy/modal/topic/createTagModal';
import addOrUpdateContextModal from '@/components/rcmDeploy/modal/context/addOrUpdateContextModal.vue';
import contextInfoModal from '@/components/rcmDeploy/modal/context/contextInfoModal.vue';
import { formatDates } from '@/utils/utils';
import {
    createContext,
    queryContext,
    deleteContext,
    addContextTags,
    delContextTags,
    queryContextTags,
    getContextDetial,
    betchDeleteContext,
    getZones
} from '@/api/rcmApi';
export default {
    name: 'RcmDeployContext',
    props: {
        rcmId: {
            type: String,
            default: ''
        }
    },
    data() {
        return {
            loading: false,
            zones: [
                {
                    value: 'all',
                    label: '全部'
                }
            ],
            formItems: [
                {
                    type: 'select',
                    label: '模板名称',
                    key: 'ref',
                    options: [
                        {
                            value: 'all',
                            label: '全部'
                        }
                    ],
                    value: 'all'
                },
                {
                    type: 'input',
                    key: 'name',
                    label: '上下文名称',
                    value: '',
                    placeholder: '请输入上下文名称',
                    clearable: true
                },
                {
                    type: 'select',
                    label: '上下文模式',
                    key: 'mode',
                    options: [
                        {
                            value: 'all',
                            label: '全部'
                        },
                        {
                            value: 'singleton',
                            label: 'singleton'
                        },
                        {
                            value: 'cluster',
                            label: 'cluster'
                        }
                    ],
                    value: 'all'
                },
                {
                    type: 'input',
                    key: 'appName',
                    label: '应用名称',
                    value: '',
                    placeholder: '请输入应用名称',
                    clearable: true
                },
                {
                    type: 'select',
                    key: 'tags',
                    label: '查询标签',
                    options: [],
                    placeholder: '请选择查询标签',
                    multiple: true,
                    value: []
                },
                {
                    type: 'select',
                    label: '中心名',
                    key: 'zone',
                    options: [{ label: '全部', value: 'all' }],
                    value: 'all'
                },
                {
                    type: 'select',
                    label: '同步模式',
                    key: 'syncMode',
                    options: [
                        {
                            value: 'all',
                            label: '全部'
                        },
                        {
                            value: 0,
                            label: '状态机复制'
                        },
                        {
                            value: 1,
                            label: '消息复制'
                        }
                    ],
                    value: 'all'
                }
            ],
            modalInfo: {
                status: false
            },
            inputInfo: {
                status: false
            },
            columns: [
                {
                    type: 'selection',
                    width: 60,
                    align: 'center'
                },
                {
                    title: 'ID',
                    key: 'id',
                    width: 100,
                    ellipsis: true
                },
                {
                    title: '上下文名称',
                    key: 'name',
                    ellipsis: true,
                    render: (h, params) => {
                        return h('span', [params.row.name]);
                    }
                },
                {
                    title: '模式',
                    key: 'mode',
                    ellipsis: true
                },
                {
                    title: '发送主题',
                    key: 'txTopics',
                    render: (h, params) => {
                        const txTopics = [];
                        for (const txTopic of params.row.txTopics) {
                            txTopics.push(txTopic.topic + ' [' + txTopic.partition + ']');
                        }
                        return h(
                            'div',
                            {
                                class: 'h-table-cell-ellipsis',
                                attrs: {
                                    title: txTopics?.join(',') || '-'
                                }
                            },
                            txTopics?.join(',') || '-'
                        );
                    }
                },
                {
                    title: '接收主题',
                    key: 'rxTopics',
                    render: (h, params) => {
                        const rxTopics = [];
                        for (const rxTopic of params.row.rxTopics) {
                            rxTopics.push(rxTopic.topic + ' [' + rxTopic.partition + ']');
                        }
                        return h(
                            'div',
                            {
                                class: 'h-table-cell-ellipsis',
                                attrs: {
                                    title: rxTopics?.join(',') || '-'
                                }
                            },
                            rxTopics?.join(',') || '-'
                        );
                    }
                },
                {
                    title: '引用模板',
                    key: 'ref',
                    ellipsis: true,
                    render: (h, params) => {
                        return h('span', [params.row.ref || '-']);
                    }
                },
                {
                    title: '标签',
                    key: 'tags',
                    ellipsis: true,
                    render: (h, params) => {
                        return h('span', [params.row.tags?.join(',') || '-']);
                    }
                },
                {
                    title: '操作',
                    key: 'action',
                    fixed: 'right',
                    width: 160,
                    render: (h, params) => {
                        return h('div', [
                            h(
                                'Button',
                                {
                                    props: {
                                        size: 'small',
                                        type: 'text'
                                    },
                                    on: {
                                        click: () => {
                                            this.showCtxTopo(params.row);
                                        }
                                    }
                                },
                                '查看'
                            ),
                            h(
                                'Button',
                                {
                                    props: {
                                        size: 'small',
                                        type: 'text'
                                    },
                                    on: {
                                        click: () => {
                                            this.createOrUpdateContextModal(params.row, 'update');
                                        }
                                    }
                                },
                                '配置'
                            ),
                            h(
                                'Button',
                                {
                                    props: {
                                        size: 'small',
                                        type: 'text'
                                    },
                                    on: {
                                        click: () => {
                                            this.$hMsgBoxSafe.confirm({
                                                title: `删除`,
                                                content: `您确认要删除名为"${params.row.name}"的上下文吗？`,
                                                onOk: () => {
                                                    this.handleDeleteContext(params.row.id);
                                                }
                                            });
                                        }
                                    }
                                },
                                '删除'
                            )
                        ]);
                    }
                }
            ],
            tableData: [],
            total: 0,
            selection: [],
            ctxConfigInfo: {
                status: false,
                ctxName: '',
                data: {}
            },
            deleteCheckboxStatus: false // 是否同时删除所有匹配记录
        };
    },
    mounted() { },
    computed: {
        ...mapState({
            singleCtxList: (state) => {
                return state.rcm.singleCtxList || [];
            },
            clusterCtxList: (state) => {
                return state.rcm.clusterCtxList || [];
            }
        })
    },
    methods: {
        // 初始化查询参数
        setDefaultQueryParam(query) {
            const { contextName, zone } = query || {};
            this.$refs['table'].$_echoFormData(contextName ? { name: contextName || '' } : { zone });
        },
        // 初始化数据
        async initData(query) {
            this.formItems[4].options = await this.queryContextTags();
            const options = [];
            const ctxTempList = [];
            for (const item of (this.singleCtxList || [])) {
                if (options.indexOf(item.name) === -1) {
                    ctxTempList.push({
                        value: item.name,
                        label: item.name
                    });
                    options.push(item.name);
                }
            }
            for (const item of (this.clusterCtxList || [])) {
                if (options.indexOf(item.name) === -1) {
                    ctxTempList.push({
                        value: item.name,
                        label: item.name
                    });
                    options.push(item.name);
                }
            }
            this.formItems[0].options = [{ label: '全部', value: 'all' }].concat(ctxTempList);
            try {
                const zoneResult = await getZones({ rcmId: this.rcmId });
                const zones = zoneResult?.data?.length ? zoneResult.data.map(item => ({ value: item.name, label: item.name })) : [];
                this.formItems[5].options = [{ label: '全部', value: 'all' }].concat(zones);
            } finally {
                // 表格重查询数据
                this.$nextTick(() => {
                    query && this.setDefaultQueryParam(query);
                    this.$refs['table'].$_handleResetPageDataAndQuery();
                });
            }
        },
        async handleQuery(val) {
            this.selection = [];
            try {
                const param = {
                    rcmId: this.rcmId,
                    ref: val.ref !== 'all' ? val.ref : '',
                    name: val.name,
                    mode: val.mode !== 'all' ? val.mode : '',
                    appName: val.appName,
                    tags: val.tags?.join(',') || '',
                    page: val.page,
                    pageSize: val.pageSize,
                    zone: val.zone === 'all' ? '' : val.zone,
                    syncMode: val.syncMode === 'all' ? '' : val.syncMode
                };
                this.loading = true;
                const res = await queryContext(param);
                this.loading = false;
                if (res.code === '200') {
                    this.tableData = res?.data?.list || [];
                    this.total = res.data.totalCount;
                } else if (res.code.length === 8) {
                    this.$hMessage.error(res.message);
                }
            } catch (err) {
                this.loading = false;
                this.tableData = [];
                this.total = 0;
            }
        },
        // 创建
        createOrUpdateContextModal(param, type) {
            this.modalInfo.status = true;
            this.modalInfo.id = this.rcmId;
            this.modalInfo.type = type;
            this.modalInfo.singleCtxList = this.singleCtxList;
            this.modalInfo.clusterCtxList = this.clusterCtxList;
            this.modalInfo.data = param
                ? {
                    ...param
                }
                : {
                    id: '',
                    tags: [],
                    txTopics: [],
                    rxTopics: [],
                    mode: 'singleton',
                    name:
                        'context_' +
                        formatDates(new Date()).replace(/[^\d]/gi, '') +
                        '_singleton',
                    ref: ''
                };
        },
        // 创建、配置上下文
        async createOrUpdateContext(val) {
            const param = {
                rcmId: this.rcmId,
                ...val
            };
            const res = await createContext(param);
            if (res.code === '200') {
                this.formItems[4].options = await this.queryContextTags();
                this.$nextTick(() => {
                    this.$refs['table'].$_handleQuery();
                });
                this.$hMessage.success('创建/配置上下文成功!');
            }
        },
        // 查看上下文topo详情
        async showCtxTopo(ctx) {
            const param = {
                rcmId: this.rcmId,
                id: ctx.id
            };
            const res = await getContextDetial(param);
            if (res.code === '200') {
                this.ctxConfigInfo.status = true;
                this.ctxConfigInfo.name = res?.name;
                this.ctxConfigInfo.data = res?.data;
            }
        },
        // 选中行
        tableSelection(selection) {
            this.selection = selection;
        },
        // 删除行
        async handleDeleteContext(id) {
            const ids = [id.toString()];
            await this.deleteContext(ids);
        },
        // 批量删除
        handleClear(formVal) {
            this.$hMessage.destroy();
            if (!this.selection.length) {
                this.$hMessage.info('请选择一条或多条数据');
                return;
            }
            this.$hMsgBoxSafe.confirm({
                title: '您确认要批量删除已选中的上下文吗',
                render: (h) => {
                    return h(
                        'div',
                        {
                            style: 'margin: 20px'
                        },
                        [
                            h(
                                'h-checkbox',
                                {
                                    on: {
                                        'on-change': (status) => {
                                            this.deleteCheckboxStatus = status;
                                        }
                                    }
                                },
                                [h('span', `同时删除所有匹配记录（共${this.total}条）`)]
                            )
                        ]
                    );
                },
                onOk: async () => {
                    if (this.deleteCheckboxStatus) {
                        await this.betchDeleteCtx(formVal);
                    } else {
                        const ids = this.selection.map((v) => v.id.toString());
                        await this.deleteContext(ids);
                    }
                }
            });
        },
        // 批量删除查询的所有上下文
        async betchDeleteCtx(val) {
            const param = {
                rcmId: this.rcmId,
                ref: val.ref !== 'all' ? val.ref : '',
                name: val.name,
                mode: val.mode !== 'all' ? val.mode : '',
                appName: val.appName,
                tags: val.tags?.join(',') || '',
                page: val.page,
                pageSize: this.total,
                zone: val.zone === 'all' ? '' : val.zone
            };
            const res = await betchDeleteContext(param);
            if (res.code === '200') {
                this.$hMessage.success('删除成功!');
                this.formItems[4].options = await this.queryContextTags();
                this.$nextTick(() => {
                    this.$refs['table'].$_handleQuery();
                });
                this.selection = [];
            }
        },
        // 删除
        async deleteContext(ids) {
            const param = {
                rcmId: this.rcmId,
                contextIds: ids
            };
            const res = await deleteContext(param);
            if (res.code === '200') {
                this.$hMessage.success('删除成功!');
                this.formItems[4].options = await this.queryContextTags();
                this.$nextTick(() => {
                    this.$refs['table'].$_handleQuery();
                });
                this.selection = [];
            }
        },
        // 添加、删除标签
        handleTag(operate) {
            this.$hMessage.destroy();
            if (!this.selection.length) {
                this.$hMessage.info('请选择一条或多条数据');
                return;
            }
            // 所勾选项标签列表-用于批量删除
            const taglist = this.selection.map((o) => o.tags || []);
            const selectTagList = taglist?.flat() || [];
            if (operate === 'del' && !selectTagList.length) {
                this.$hMessage.warning('所选项未绑定标签');
                return;
            }
            this.inputInfo.id = this.rcmId;
            this.inputInfo.type = 'context-tag';
            this.inputInfo.context = this.selection.map((v) => v.name)?.join(',');
            this.inputInfo.status = true;
            this.inputInfo.operate = operate;
            this.inputInfo.title = operate === 'add' ? '添加标签' : '删除标签';
            this.inputInfo.tags = [];
            // 所勾选项标签列表-用于批量删除
            this.inputInfo.tagList = selectTagList;
        },
        // 添加标签
        async addTags(val) {
            if (!val?.selection?.length) {
                return;
            }
            const param = {
                rcmId: this.rcmId,
                contextIds: this.selection.map((v) => v.id.toString()),
                tags: [...val.selection]
            };
            const res = await addContextTags(param);
            if (res.code === '200') {
                this.$hMessage.success('添加标签成功!');
                this.formItems[4].options = await this.queryContextTags();
                this.$nextTick(() => {
                    this.$refs['table'].$_handleQuery();
                });
                this.selection = [];
            }
        },
        // 删除标签
        async delTags(val) {
            if (!val?.selection?.length) {
                return;
            }
            const param = {
                rcmId: this.rcmId,
                contextIds: this.selection.map((v) => v.id.toString()),
                tags: [...val.selection]
            };
            const res = await delContextTags(param);
            if (res.code === '200') {
                this.$hMessage.success('删除标签成功!');
                this.formItems[4].options = await this.queryContextTags();
                this.$nextTick(() => {
                    this.$refs['table'].$_handleQuery();
                });
                this.selection = [];
            }
        },
        // 查询所有的标签
        async queryContextTags() {
            const tagList = [];
            const res = await queryContextTags({
                rcmId: this.rcmId
            });
            if (res.code === '200') {
                res.data?.tags?.length &&
                    res.data.tags.forEach((v) => {
                        tagList.push({
                            value: v,
                            label: v
                        });
                    });
            }
            return tagList || [];
        }
    },
    components: {
        addOrUpdateContextModal,
        createTagModal,
        rcmNormalTitleTable,
        contextInfoModal
    }
};
</script>
<style lang="less" scoped>
@import url("@/assets/css/input.less");

.context-box {
    width: 100%;
    height: 100%;

    .best-table {
        padding: 0;
    }

    /deep/ .h-select {
        .h-checkbox-inner {
            border: 1px solid var(--font-opacity-color);
            border-radius: 2px;
            background-color: var(--font-color);
        }
    }
}

#rcm-inpt-placeholder {
    position: absolute;
    width: 0;
    height: 0;
    display: block;
    top: 0;
    left: 0;
    border: none;
    opacity: 0;
    outline: none;
}
</style>
