<template>
    <div class="tab-box">
        <h-tabs v-model="tabName" @on-click="handleTabChange(tabName)">
            <h-tab-pane label="核心性能" name="clusterProcessPerform">
                <cluster-process-perform ref="clusterProcessPerform" :productInstNo="productInstNo" :productInfo="productInfo" :curTabDashBorad="curTabDashBorad"/>
            </h-tab-pane>
        </h-tabs>
    </div>
</template>
<script>
import clusterProcessPerform from '@/components/ldpMonitor/clusterProcessPerform';
export default {
    props: {
        productInfo: {
            type: Object,
            default: () => { return {}; }
        },
        productInstNo: {
            type: String,
            default: ''
        }
    },
    components: { clusterProcessPerform },
    data() {
        return {
            tabName: 'clusterProcessPerform',
            editableTabs: [],
            curTabDashBorad: {
                timerSwitch: true,
                timerInterval: 10
            }
        };
    },
    mounted(){
        this.handleTabChange('clusterProcessPerform');
    },
    beforeDestroy(){
        this.clearPolling();
    },
    methods: {
        handleTabChange(tabName) {
            this.tabName = tabName;
            this.clearPolling();
            this.$nextTick(async () => {
                await this.$refs[tabName].init();
            });
        },
        clearPolling() {
            const clusterProcessPerform = this.$refs['clusterProcessPerform'];
            clusterProcessPerform && clusterProcessPerform.clearPolling();
        }
    },
    watch: {
        productInstNo() {
            this.clearPolling();
            this.$nextTick(async () => {
                await this.$refs[this.tabName].init();
            });
        }
    }
};
</script>
