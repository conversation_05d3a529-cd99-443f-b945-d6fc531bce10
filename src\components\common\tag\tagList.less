.monitor-list {
    display: flex;
    justify-content: space-between;
    position: absolute;
    left: 0;
    top: 0;
    width: 100%;

    .icon {
        color: var(--link-color);
        // margin: 0 10px;
        cursor: pointer;
    }

    .disabled-icon {
        color: var(--base-color);
        // margin: 0 10px;
        cursor: not-allowed;
    }

    .list-box {
        width: calc(100% - 40px);
        overflow: hidden;

        .list {
            width: calc(100% - 100px);
            display: flex;
            transform: all 2s;
            position: relative;
            left: 0;
            transition: left 1s;
        }
    }
}

