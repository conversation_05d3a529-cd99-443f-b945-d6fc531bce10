import JSZip from 'jszip';
import { saveAs } from 'file-saver';
import { sanitizeFilename } from '@/utils/utils';

/**
 * 批量导出json文件并打包
 * @request
 * data: {zipName: '', fileList: [
 *  {
 *    name: '',
 *    jsonData: ''
 *  }
 * ]}
 */
export const exportJsonToZip = (data) => {
    return new Promise((resolve, reject) => {
        // 创建一个新的 JSZip 实例
        const zip = new JSZip();
        const list = data.fileList;
        if (Array.isArray(list)) {
            list.forEach(ele => {
            // 将 JSON 数据转成字符串并保存为文件
                zip.file(sanitizeFilename(ele.name + '.json'), JSON.stringify(ele.jsonData, null, 2));
            });

            // 生成 ZIP 文件，并触发浏览器下载
            zip.generateAsync({ type: 'blob' }).then((content) => {
                saveAs(content, sanitizeFilename(data.zipName)); // 下载 ZIP 文件
                resolve(true);
            });
        }
    });
};
