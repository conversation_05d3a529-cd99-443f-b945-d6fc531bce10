<template>
    <div ref="tab-box" class="tab-box">
        <a-loading v-if="loading"></a-loading>
        <div v-else>
            <div class="title">
                <p>消费者信息</p>
                <apm-select-search
                    ref="subcriberSearch"
                    minWidth="150px"
                    focusWidth="350px"
                    dropHeight="200px"
                    class="securities"
                    :list="subcriberList"
                    clearable
                    placeholder="请选择消费者"
                    :border="true"
                    @onChange="(val) => handleSelectChange(val, 'subscriber')"
                />
                <a-button type="dark" @click="handleButtonClick">刷新</a-button>
            </div>
            <!--基础信息-->
            <description-bar :data="description" @select-change="handleSelectChange"></description-bar>
            <!--发布详情 -->
            <obs-table ref="table" :height="tableHeight" :title="title" :tableData="tableData" :columns="columns" :showTitle="true" :hasPage="false"
                :loading="tableLoading"/>
        </div>
    </div>
</template>

<script>
import _ from 'lodash';
import aButton from '@/components/common/button/aButton';
import aLoading from '@/components/common/loading/aLoading';
import descriptionBar from '@/components/common/description/descriptionBar';
import obsTable from '@/components/common/obsTable/obsTable';
import apmSelectSearch from '@/components/common/apmSelectSearch/apmSelectSearch';
import { getMcAPi } from '@/api/mcApi';
export default {
    name: 'McSubscribe',
    props: {
        nodeData: {
            type: Object,
            default: () => {}
        }
    },
    components: { descriptionBar, aLoading, aButton, obsTable, apmSelectSearch },
    data() {
        return {
            loading: false,
            tableLoading: false,
            tableHeight: 0,
            // 基础信息
            description: {
                title: {
                    label: '基础信息'
                },
                details: [
                    {
                        dataDic: [
                            {
                                label: '消费者',
                                key: 'SubName',
                                span: '4'
                            },
                            {
                                label: '订阅项数',
                                key: 'SubcribeCount',
                                span: '4'
                            },
                            {
                                label: '订阅主题数',
                                key: 'TopicCount',
                                span: '4'
                            },
                            {
                                label: '推送消息数',
                                key: 'PushCount',
                                span: '4'
                            },
                            {
                                label: '补缺消息数',
                                key: 'RebuildCount',
                                span: '4'
                            },
                            {
                                label: '客户端',
                                key: 'Host',
                                span: '4'
                            }
                        ],
                        data: {
                            SubName: '-',
                            SubcribeCount: '-',
                            TopicCount: '-',
                            PushCount: '-',
                            RebuildCount: '-',
                            Host: '-'
                        }
                    }
                ]
            },
            // 发布详情
            title: {
                label: '订阅详请'
            },
            tableData: [],
            columns: [
                {
                    title: '订阅编号',
                    key: 'SubId',
                    ellipsis: true
                },
                {
                    title: '订阅时间',
                    key: 'SubTime',
                    ellipsis: true
                },
                {
                    title: '订阅方式',
                    key: 'PartitionNo',
                    ellipsis: true,
                    render: (h, params) => {
                        const partitionNo = params?.row?.PartitionNo?.toString() === '-1' ? '广播消费' : '集群消费';
                        return h(
                            'span',
                            {
                                attrs: {
                                    title: partitionNo
                                }
                            },
                            [partitionNo]
                        );
                    }
                },
                {
                    title: '订阅主题',
                    key: 'TopicName',
                    ellipsis: true
                },
                {
                    title: '推送消息数',
                    key: 'PushCount',
                    ellipsis: true
                },
                {
                    title: '补缺消息数',
                    key: 'RebuildCount',
                    ellipsis: true
                },
                {
                    title: '过滤条件',
                    key: 'Filter',
                    width: 300,
                    render: (h, params) => {
                        const res = [];
                        Object.keys(params.row.Filter).forEach(key => {
                            const str = '(' + key + ':' + params.row.Filter[key] + ')';
                            res.push(str);
                        });

                        return h(
                            'div',
                            {
                                class: 'h-table-cell-ellipsis',
                                attrs: {
                                    title: res?.join(',') || '-'
                                }
                            },
                            [res?.join(',') || '-']
                        );
                    }
                }
            ],
            // 参数
            fileData: {},
            subcriber: '',
            subcriberList: [],
            subId: ''
        };
    },
    mounted() {
        this.fetTableHeight();
        window.addEventListener('resize', this.fetTableHeight);
    },
    beforeDestroy() {
        window.removeEventListener('resize', this.fetTableHeight);
    },
    methods: {
        // 设置table高度
        fetTableHeight() {
            this.tableHeight = this.$refs['tab-box']?.getBoundingClientRect()?.height - 220;
        },
        // 手动清空数据
        clearData(){
            this.subcriber = '';
            this.subcriberList = [];
            this.fileData = {};
            this.subId = '';
            this.tableData = [];
            this.description.details[0].data = {};
        },
        // 初始化构建数据结构
        async initData(val) {
            this.loading = true;
            await this.getFileData();
            this.getSubscriberList();
            this.loading = false;
            this.$nextTick(() => {
                this.setDefaultSubcriber(val);
            });
        },
        // 刷新---重新构建数据结构, 默认选择第一位消费者。若之前的消费者存在，则页面更新为当前消费者信息
        async handleButtonClick() {
            await this.initData(this.subcriber);
        },
        // 判断消费者是否存在, 使用历史选中值用于刷新，否则默认选中第一个
        setDefaultSubcriber(val) {
            const hasSubcriber = this.subcriberList.filter(v => v.value === val)?.[0] || '';
            const searchSubcriber = hasSubcriber ? hasSubcriber : this.subcriberList?.[0] || '';
            if (!searchSubcriber){
                this.clearData();
                return;
            }
            this.$refs['subcriberSearch'].onChangeSelect(searchSubcriber);
        },
        // 消费者切换,subId切换
        handleSelectChange(val, key) {
            // 消费者
            if (key === 'subscriber') {
                this.subcriber = val || '';
                const data = this.fileData?.[val] || [];
                this.description.details[0].data = {
                    SubName: data.SubName,
                    SubcribeCount: data.SubcribeCount,
                    TopicCount: data.TopicCount,
                    PushCount: data.PushCount,
                    RebuildCount: data.RebuildCount,
                    Host: data.Host
                };

                this.tableLoading = true;
                this.tableData = Object.values(data?.SubData || {})?.flat() || [];
                setTimeout(() => {
                    this.tableLoading = false;
                }, 200);
                return;
            }
        },
        // 消费者select
        getSubscriberList() {
            this.subcriber = '';
            this.subcriberList = [];
            const subcriberList = [];
            Object.keys(this.fileData).forEach((v) => {
                subcriberList.push({
                    label: v,
                    value: v
                });
            });
            this.subcriberList = subcriberList?.sort((a, b) => a?.value?.localeCompare(b?.value));
        },
        // 构建页面数据
        async getFileData() {
            this.fileData = {};
            const { GetTopicInfo } = await this.getTopicAPi();
            const { GetAllSubscribers, GetAllSubscribeInfo, GetSessionInfo } = await this.getAPi();
            for (const v of Object.values(GetAllSubscribers)) {
                const key = v.SubName + '(' + v.SessionId.trim() + ')';
                this.fileData[key] = [];
                const subcribes = GetAllSubscribeInfo?.filter(info => info.SubName + '(' + info.SessionId + ')' === key) || [];
                const topicList = subcribes?.map(v => v.TopicName) || [];
                const pushCounts = subcribes?.map(v => v.PushCount)?.reduce((a, b) => a + b, 0);
                const rebuildCounts = subcribes?.map(v => v.RebuildCount)?.reduce((a, b) => a + b, 0);
                const host = GetSessionInfo?.filter(v => v.SubcribeName + '(' + v.SessionID + ')' === key)?.[0]?.Host || '';
                const subData = this.getSubData(GetTopicInfo, subcribes);

                // 消费者信息
                this.fileData[key] = {
                    SubName: v.SubName,
                    SubcribeCount: subcribes?.length,
                    TopicCount: [...new Set(topicList)]?.length,
                    PushCount: pushCounts,
                    RebuildCount: rebuildCounts,
                    Host: host,
                    SubData: subData
                };
            }
        },
        // 获取表格数据
        getSubData(getTopicInfo, subcribes) {
            const subData = {};
            for (const sub of Object.values(subcribes)) {
                const key = '#' + sub.SubId;
                subData[key] = this.subData?.[key] || [];
                const filterFields = _.find(getTopicInfo, ['Topic', sub.TopicName])?.FilterFields;
                const filters = {};
                Object.keys(sub).forEach(v => {
                    if (v.indexOf('Filter') !== -1) {
                        const idx = v.replace('Filter', '') || 0;
                        const name = idx && filterFields[idx - 1]?.Name;
                        name && (filters[name] = sub[v] || '-');
                    }
                });

                subData[key].push({
                    ...sub,
                    Filter: filters
                });
            }
            return subData;
        },
        // 接口请求
        async getTopicAPi() {
            const funcNameList = ['GetTopicInfo'];
            const data = await getMcAPi(this.nodeData?.productInstances || [], funcNameList, 'ldp_mc', false);
            return data;
        },
        // 接口请求
        async getAPi() {
            const funcNameList = ['GetAllSubscribers', 'GetAllSubscribeInfo', 'GetSessionInfo'];
            const data = await getMcAPi(this.nodeData?.productInstances || [], funcNameList, 'ldp_mc');
            return data;
        }
    }
};
</script>

<style lang="less" scoped>
.tab-box {
    height: 100%;

    .title {
        color: var(--font-color);
        font-size: 14px;
        width: 100%;
        height: 40px;
        line-height: 40px;
        margin: 0 10px;
        position: relative;

        .securities {
            position: absolute;
            width: auto;
            top: 5px;
            right: 80px;
            min-width: 200px;
        }

        button {
            position: absolute;
            top: 5px;
            right: 10px;
        }
    }
}
</style>
