<!-- 网关服务 内存表管理 批量配置弹窗 -->
<template>
    <div>
        <h-msg-box
            v-model="modalInfo.status"
            :mask-closable="false"
            title="配置内存表管理服务"
            width="60"
            maxHeight="420"
            @on-open="getCollections">
            <div>
                <h-edit-gird
                    ref="editGrid"
                    :canMove="false"
                    :columns="columns"
                    :data="data"
                    :height="250"
                    :loading="tableLoading"
                    size="small"
                    :showEditInput="true"
                    :disabled-hover="true"
                    :highlight-row="false"
                ></h-edit-gird>
            </div>
            <template v-slot:footer>
                <h-button @click="modalInfo.status = false">取消</h-button>
                <h-button type="primary" :loading="loading" @click="submitConfig">确定</h-button>
            </template>
        </h-msg-box>
    </div>
</template>

<script>
import _ from 'lodash';
import { updateUstTableConfig } from '@/api/memoryApi';
export default {
    name: 'UpdateUstTableConfigModal',
    components: {  },
    props: {
        modalInfo: {
            type: Object,
            default: null
        },
        instanceList: {
            type: Array,
            default: () => []
        }
    },
    data() {
        return {
            modalData: this.modalInfo,
            tableLoading: false,
            loading: false,
            columns: [
                {
                    title: '应用节点',
                    key: 'instanceNo',
                    render: (h, params) => {
                        return h('div', params.row.instanceNo || '-');
                    }
                },
                {
                    title: '应用集群',
                    key: 'clusterName',
                    render: (h, params) => {
                        return h('div', params.row.clusterName || '-');
                    }
                },
                {
                    title: '接入点IP',
                    type: 'text',
                    key: 'endpointIp',
                    rule: [{
                        test: /^(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])(\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])){3}$/,
                        message: '只能输入ip地址格式',
                        trigger: 'blur'
                    }]
                },
                {
                    title: '接入点端口',
                    type: 'text',
                    key: 'endpointPort',
                    rule: [
                        {
                            validator: function(rule, value, callback) {
                                if (value % 1 === 0 && value > 0 && value <= 65536) {
                                    callback();
                                } else {
                                    callback(new Error('请输入1-65536范围的整数'));
                                }
                            },
                            trigger: 'blur'
                        }
                    ]
                },
                {
                    title: '系统号',
                    type: 'text',
                    key: 'systemNo',
                    rule: [
                        {
                            validator: function(rule, value, callback) {
                                if (value % 1 === 0 && value > 0 && value <= 65536) {
                                    callback();
                                } else {
                                    callback(new Error('请输入1-65536范围的整数'));
                                }
                            },
                            trigger: 'blur'
                        }
                    ]
                },
                {
                    title: '功能号',
                    type: 'text',
                    key: 'sqlFuncNo',
                    rule: [
                        {
                            validator: function(rule, value, callback) {
                                if (value.length < 255) {
                                    callback();
                                } else {
                                    callback(new Error('字符长度数不得超过255!'));
                                }
                            },
                            trigger: 'blur'
                        }
                    ]
                }
            ],
            data: []
        };
    },
    methods: {
        getCollections() {
            this.data = _.cloneDeep(this.modalData.configData);
        },
        submitConfig() {
            this.$refs.editGrid.validate((valid) => {
                if (valid) {
                    const data = this.$refs.editGrid.getChangeData();
                    const newData = data.map(item => {
                        return {
                            id: item.id,
                            appInstanceId: item.appInstanceId,
                            endpointIp: item.endpointIp,
                            endpointPort: item.endpointPort ? Number(item.endpointPort) : null,
                            systemNo: item.systemNo ? Number(item.systemNo) : null,
                            sqlFuncNo: item.sqlFuncNo
                        };
                    });
                    this.updateUstTableConfig(newData);
                }
            });
        },
        async updateUstTableConfig(configs) {
            this.loading = true;
            try {
                const params = {
                    productId: this.modalData.productInstNo,
                    configs
                };
                const res = await updateUstTableConfig(params);
                if (res.success) {
                    this.$emit('update');
                    this.$hMessage.success('配置成功');
                    this.modalData.status = false;
                }
            } finally {
                this.loading = false;
            }
        }
    }
};
</script>

<style lang="less" scoped>
/deep/ .h-modal-body {
    padding: 16px;
}

/deep/ .h-btn-text {
    color: var(--link-color);
}

/deep/ .h-btn-text:hover {
    color: var(--link-color);
    text-decoration: underline;
}

</style>
