<template>
    <div>
        <h-msg-box-safe v-model="modalData.status" :escClose="true" :mask-closable="false" title="新建测试用例" width="50" maxHeight="300">
            <h-form ref="formDynamic" :model="formDynamic" :label-width="80" :showTipsOnlyFocus="true" >
                <h-form-item label="UPDATE:">
                    <h-row class="row">
                        <h-col span="11">
                            <h-input v-model="formDynamic.tableName" :disabled="true"></h-input>
                        </h-col>
                        <h-col span="1">&nbsp;</h-col>
                        <h-col span="11">
                            <h-input v-model="formDynamic.expandAttributes"></h-input>
                        </h-col>
                        <h-col span="1">
                            <h-poptip class="popo-icon" title="功能简介" placement="left">
                                <h-icon name="help-circled"></h-icon>
                                <template v-slot:content>
                                    <div style="width: 180px; white-space: normal;">
                                        XXXX
                                    </div>
                                </template>
                            </h-poptip>
                        </h-col>
                    </h-row>
                </h-form-item>
                <h-form-item key="SET" label="SET:">
                    <h-row v-for="(item, index) in formDynamic.setData" :key="'setData' + index" class="row">
                        <h-col span="8">
                            <h-form-item :prop="'setData.' + index + '.fieldName'" required>
                                <h-select v-model="item.fieldName" set-def-select :positionFixed="true" :clearable="false"
                                    @on-change="changeSetFile(index)">
                                    <h-option v-for="fieldName in fieldNames.split(',')" :key="fieldName.trim()"
                                        :value="fieldName.trim()">{{
                                            fieldName.trim() }}</h-option>
                                </h-select>
                            </h-form-item>
                        </h-col>
                        <h-col span="1">&nbsp;</h-col>
                        <h-col span="7">
                            <h-form-item :prop="'setData.' + index + '.sign'" required>
                                <h-select v-model="item.sign" set-def-select :positionFixed="true" :clearable="false">
                                    <h-option v-for="signItem in item.signList" :key="signItem.value"
                                        :value="signItem.value">{{ signItem.label }}</h-option>
                                </h-select>
                            </h-form-item>
                        </h-col>
                        <h-col span="1">&nbsp;</h-col>
                        <h-col span="7">
                            <h-form-item  :prop="'setData.' + index + '.value'" :valid-rules="item.validateFunc">
                                <h-input v-model.trim="item.value" placeholder="字段值"></h-input>
                            </h-form-item>
                        </h-col>
                    </h-row>
                </h-form-item>
               <h-form-item key="WHERE" label="WHERE">
                    <h-row v-for="(item, index) in formDynamic.items" :key="'items' + index" class="row">
                        <h-col span="6">
                            <h-form-item :prop="'items.' + index + '.fieldName'" required>
                                <h-select v-model="item.fieldName" set-def-select :positionFixed="true" :clearable="false"
                                    @on-change="changeFile(index)">
                                    <h-option v-for="fieldName in fieldNames.split(',')" :key="fieldName.trim()"
                                        :value="fieldName.trim()">{{
                                            fieldName.trim() }}</h-option>
                                </h-select>
                            </h-form-item>
                        </h-col>
                        <h-col span="1">&nbsp;</h-col>
                        <h-col span="4">
                            <h-form-item :prop="'items.' + index + '.sign'" required>
                                <h-select v-model="item.sign" set-def-select :positionFixed="true" :clearable="false">
                                    <h-option v-for="signItem in item.signList" :key="signItem.value"
                                        :value="signItem.value">{{ signItem.label }}</h-option>
                                </h-select>
                            </h-form-item>
                        </h-col>
                        <h-col span="1">&nbsp;</h-col>
                        <h-col span="5">
                            <h-form-item :prop="'items.' + index + '.value'" :rules="item.validateFunc">
                                <h-input v-model.trim="item.value" placeholder="字段值"></h-input>
                            </h-form-item>
                        </h-col>
                        <h-col span="1">&nbsp;</h-col>
                        <h-col span="4">
                            <h-select v-model="item.logical" set-def-select :positionFixed="true" :clearable="false"
                                :disabled="index !== 0" @on-change="changelogical">
                                <h-option value="and">AND</h-option>
                                <h-option value="or">OR</h-option>
                            </h-select>
                        </h-col>
                        <h-col span="1">&nbsp;</h-col>
                        <h-col span="1">
                            <h-form-item>
                                <a-button type="ghost" shape="circle" size="small" icon="minus-round"
                                    @click="handleRemove(index)"></a-button>
                            </h-form-item>
                        </h-col>
                    </h-row>
                    <h-row>
                        <h-col span="12"> <h-form-item class="row">
                                <a-button type="ghost" shape="circle" size="small" icon="plus-round"
                                    @click="handleAdd"></a-button>
                            </h-form-item></h-col>
                    </h-row>
                </h-form-item>
            </h-form>
            <template v-slot:footer>
                <a-button @click="createPreview">创建修改预览</a-button>
                <a-button @click="handleCancel">取消</a-button>
            </template>
        </h-msg-box-safe>
    </div>
</template>
<script>
import aButton from '@/components/common/button/aButton';
import { comparatorConfig } from '@/config/exchangeConfig';
import { matchValidate } from '@/utils/validate';
import { loopAllData } from '@/utils/utils';
export default {
    props: {
        modalInfo: {
            type: Object,
            default: null
        },
        boxInfo: {
            tableName: {
                type: String,
                default: ''
            },
            expandAttributes: {
                type: Array,
                default: []
            },
            fields: {
                type: Array,
                default: []
            },
            describe: {
                type: String,
                default: ''
            }
        }
    },
    components: { aButton },
    data() {
        return {
            modalData: this.modalInfo,
            formDynamic: {
                tableName: this.boxInfo.tableName,
                expandAttributes: this.boxInfo.expandAttributes.join(','),
                setData: [
                    {
                        fieldName: '',
                        value: '',
                        sign: '',
                        signList: [],
                        validateFunc: null
                    }
                ],
                items: [
                    {
                        fieldName: '',
                        logical: '',
                        value: '',
                        sign: '',
                        signList: [],
                        validateFunc: null
                    }
                ]
            }
        };
    },
    computed: {
        fieldNames: function () {
            const data = this.getfieldNames(this.boxInfo.fields);
            return data || [];
        }
    },
    methods: {
        // 遍历所有字段名
        getfieldNames(fields) {
            const fieldNames = [];
            loopAllData(fields, (data) => {
                if (data.hasCondition) {
                    fieldNames.push(data.path);
                }
            });
            return fieldNames;
        },
        // set 更换字段名
        changeSetFile(index) {
            const fieldName = this.formDynamic.setData[index]?.fieldName || '';
            const { type, size } = this.getTypeOrSize(fieldName);
            this.formDynamic.setData[index].signList = type === 'bool' ? comparatorConfig['bool'] : type.indexOf('char') !== -1 ? comparatorConfig['char'] : comparatorConfig['default'];
            this.formDynamic.setData[index].validateFunc = matchValidate(type, size);
        },
        // 切换字段名
        changeFile(index) {
            const fieldName = this.formDynamic.items[index]?.fieldName;
            const { type, size } = this.getTypeOrSize(fieldName);
            this.formDynamic.items[index].signList = type === 'bool' ? comparatorConfig['bool'] : type.indexOf('char') !== -1 ? comparatorConfig['char'] : comparatorConfig['default'];
            this.formDynamic.items[index].validateFunc = matchValidate(type, size);
        },
        // 判断字段名类型,判断字段名size
        getTypeOrSize(fieldName) {
            let type = '';
            let size = '';
            loopAllData(this.boxInfo.fields, (data) => {
                if (data.path === fieldName) {
                    type = data.type;
                    size = data.size;
                }
            });
            return { type, size };
        },
        // 切换比较符
        changelogical() {
            const logical = this.formDynamic.items[0]?.logical;
            this.formDynamic.items.map((item) => {
                item.logical = logical;
                return item;
            });
        },
        // 删除where条件
        handleRemove(index) {
            this.formDynamic.items.splice(index, 1);
        },
        // 增加where条件
        handleAdd() {
            this.formDynamic.items.push({
                fieldName: '',
                logical: '',
                value: '',
                sign: '',
                signList: [],
                validateFunc: null
            });
        },
        // 创建修改预览
        createPreview() {
            this.$refs['formDynamic'].validate((valid) => {
                if (valid) {
                    // 获取所有筛选条件
                    const { tableName, expandAttributes } = this.formDynamic;
                    const updateCondition = {
                        type: 'update',
                        tableName,
                        expandAttributes
                    };
                    this.$emit('createPreview', updateCondition);
                } else {
                    this.$hMessage.error('表单验证失败!');
                }
            });

        },
        handleCancel() {
            this.modalData.status = false;
        }
    }
};
</script>
