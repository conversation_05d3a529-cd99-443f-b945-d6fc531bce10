<template>
    <div>
        <h-msg-box
            v-model="modalData.status"
            width="70"
            height="70"
            :escClose="false"
            :mask-closable="false"
            title="链路配置"
            @on-open="handleOpen"
            @on-cancle="cancel"
            >
            <h-tabs v-model="modalData.tabValue" size="small">
                <h-tab-pane label="统计分析" name="statistical">
                    <h-form v-if="modalData.tabValue === 'statistical'" ref="formValid1" :model="modalData.formItem"
                        :rules="timeRule" :label-width="100">
                        <div class="title">分析目标</div>
                        <h-form-item label="选择产品节点" prop="productInstNo" required>
                            <h-select v-model="modalData.formItem.productInstNo" placeholder="产品节点" :positionFixed="true" :clearable="false" @on-change="handleChangeProduct">
                                <h-option v-for="item in modalData.productList" :key="item.id" :value="item.productInstNo">{{
                                item.productName }}
                                </h-option>
                            </h-select>
                        </h-form-item>
                        <h-form-item label="选择市场" prop="exchangeId" required>
                            <h-radio-group v-model="modalData.formItem.exchangeId">
                                <h-radio v-for="item in exchangeIdList" :key="item" :label="item">
                                    {{getName(item)}}
                                </h-radio>
                            </h-radio-group>
                        </h-form-item>
                        <h-form-item label="选择链路" prop="loopType" required>
                            <h-select v-model="modalData.formItem.loopType" placeholder="委托回路" :clearable="false" :positionFixed="true"  >
                                <h-option v-for="item in loopList" :key="item.value" :value="item.value">{{ item.label }}
                                </h-option>
                            </h-select>
                        </h-form-item>
                        <h-form-item label="选择指标" prop="span">
                            <h-select v-model="modalData.formItem.span" placeholder="时延跨度" :positionFixed="true"  >
                                <h-option v-for="item in modalData.spanList" :key="item" :value="item">{{ spanLatencyDictDesc[item] }}</h-option>
                            </h-select>
                        </h-form-item>
                        <h-form-item label="席位号" prop="seat">
                            <h-input v-model="modalData.formItem.seat" placeholder="请输入席位号"></h-input>
                        </h-form-item>
                        <h-form-item label="资金账号" prop="accountId">
                            <h-input v-model="modalData.formItem.accountId" placeholder="请输入资金账号"></h-input>
                        </h-form-item>

                        <div class="title">分析指标（最多只能勾选三个指标数据）</div>
                        <h-form-item label="线性指标" prop="indicators" required>
                            <h-checkbox-group v-model="modalData.formItem.indicators">
                                <h-checkbox v-for="item in lineNormList" :key="item.label" :label="item.label"
                                    :disabled="checkboxStatus(item.label)" style="padding: 0 5px;">
                                    {{item.value}}</h-checkbox>
                            </h-checkbox-group>
                        </h-form-item>
                        <h-form-item label="分位数指标" prop="percentiles">
                            <h-checkbox-group v-model="modalData.formItem.percentiles">
                                <h-checkbox v-for="item in quantileNormList" :key="item.label" :label="item.label"
                                    :disabled="checkboxStatus(item.label)" style="padding: 0 5px;">
                                    {{item.value}}</h-checkbox>
                            </h-checkbox-group>
                        </h-form-item>
                        <div class="title">数据范围</div>
                        <h-form-item label="选择日期" prop="date" required>
                            <h-date-picker v-model="modalData.formItem.date" type="date" placeholder="选择日期" placement="top-start" :positionFixed="true"  >
                            </h-date-picker>
                        </h-form-item>
                        <h-form-item label="统计范围">
                            <h-radio-group v-model="modalData.formItem.exchangeTime" @on-change="handleChangeRange">
                                <h-radio v-for="item in rangeList" :key="item.label" :label="item.label">{{item.value}}
                                </h-radio>
                            </h-radio-group>
                        </h-form-item>
                        <h-form-item label="自定义范围" prop="time" required>
                            <h-time-picker v-model="modalData.formItem.time" confirm type="timerange" placement="top-start" placeholder="选择时间" :positionFixed="true"  >
                            </h-time-picker>
                        </h-form-item>
                    </h-form>
                </h-tab-pane>
                <h-tab-pane label="同比分析" name="compare">
                    <h-form v-if="modalData.tabValue === 'compare'" ref="formValid2" :model="modalData.formItem"
                        :rules="timeRule" :label-width="100">
                        <div class="title">分析目标</div>
                        <h-form-item label="选择产品节点" prop="productInstNo" required>
                            <h-select v-model="modalData.formItem.productInstNo" placeholder="产品节点" :clearable="false" :positionFixed="true"   @on-change="handleChangeProduct">
                                <h-option v-for="item in modalData.productList" :key="item.id" :value="item.productInstNo">{{
                                item.productName }}
                                </h-option>
                            </h-select>
                        </h-form-item>
                        <h-form-item label="选择市场" prop="exchangeId" required>
                            <h-radio-group v-model="modalData.formItem.exchangeId">
                                <h-radio v-for="item in exchangeIdList" :key="item" :label="item">
                                    {{getName(item)}}
                                </h-radio>
                            </h-radio-group>
                        </h-form-item>
                        <h-form-item label="选择链路" prop="loopType" required>
                            <h-select v-model="modalData.formItem.loopType" placeholder="委托回路" :clearable="false" :positionFixed="true"  >
                                <h-option v-for="item in loopList" :key="item.value" :value="item.value">{{ item.label }}
                                </h-option>
                            </h-select>
                        </h-form-item>
                        <h-form-item label="选择指标" prop="span">
                            <h-select v-model="modalData.formItem.span" placeholder="时延跨度" :positionFixed="true"  >
                                <h-option v-for="item in modalData.spanList" :key="item" :value="item">{{ spanLatencyDictDesc[item] }}</h-option>
                            </h-select>
                        </h-form-item>
                        <h-form-item label="席位号" prop="seat">
                            <h-input v-model="modalData.formItem.seat" placeholder="请输入席位号"></h-input>
                        </h-form-item>
                        <h-form-item label="资金账号" prop="accountId">
                            <h-input v-model="modalData.formItem.accountId" placeholder="请输入资金账号"></h-input>
                        </h-form-item>

                        <div class="title">分析指标</div>
                        <h-form-item label="统计方式" prop="compareIndicator" required>
                            <h-select v-model="modalData.formItem.compareIndicator" placeholder="请选择统计方式" :positionFixed="true"  >
                                <h-option v-for="item in lineNormList" :key="item.label" :value="item.label">{{ item.value }}
                                </h-option>
                            </h-select>
                        </h-form-item>
                        <div class="title">数据范围</div>
                        <h-row>
                            <h-col span="12">
                                <h-form-item label="基线日期" prop="date" required>
                                    <h-date-picker v-model="modalData.formItem.date" type="date" placeholder="选择日期" :positionFixed="true"
                                    placement="top-start"></h-date-picker>
                                </h-form-item>
                            </h-col>
                            <h-col span="12">
                                <h-form-item label="基线时间" prop="time" required>
                                    <h-time-picker v-model="modalData.formItem.time" confirm type="timerange" :positionFixed="true"
                                    placement="top-start" placeholder="选择时间">
                                    </h-time-picker>
                                </h-form-item>
                            </h-col>
                        </h-row>
                        <h-row>
                            <h-col span="12">
                                <h-form-item label="同比日期" prop="compareDate" required>
                                    <h-date-picker v-model="modalData.formItem.compareDate" type="date" placeholder="选择日期" :positionFixed="true"
                                    placement="top-start">
                                    </h-date-picker>
                                </h-form-item>
                            </h-col>
                            <h-col span="12">
                                <h-form-item label="同比时间" prop="time" required>
                                    <h-time-picker v-model="modalData.formItem.time" disabled confirm type="timerange" :positionFixed="true"
                                    placement="top-start" placeholder="选择时间">
                                    </h-time-picker>
                                </h-form-item>
                            </h-col>
                        </h-row>
                    </h-form>
                </h-tab-pane>
            </h-tabs>

            <template v-slot:footer>
                <a-button type="ghost" @click="cancel">取消</a-button>
                <a-button type="primary" style="margin-left: 8px;" @click="submitForm">提交</a-button>
            </template>
        </h-msg-box>
    </div>

</template>

<script>
import { mapState } from 'vuex';
import aButton from '@/components/common/button/aButton';
import { getExchangeName } from '@/utils/utils';
import _ from 'lodash';
export default ({
    props: {
        modalInfo: {
            type: Object,
            default: null
        },
        loading: {
            type: Boolean,
            default: false
        },
        modelLoopList: {
            type: Array,
            default: []
        }
    },
    data() {
        const timeRule = function (rule, values, callback) {
            if (values.length) {
                let startTime, endTime;
                values.forEach((ele, index) => {
                    const _arr = ele.split(':');
                    if (index) {
                        endTime = (Number(_arr[0])) * 3600 + (Number(_arr[1])) * 60 + (Number(_arr[2]));
                    } else {
                        startTime = (Number(_arr[0])) * 3600 + (Number(_arr[1])) * 60 + (Number(_arr[2]));
                    }
                });
                if ((endTime - startTime) > 18 * 3600) {
                    return callback(new Error('自定义范围不得超过18小时'));
                }
            }
            callback();
        };
        return {
            initData: {},
            modalData: this.modalInfo,
            timeRule: {
                time: [{
                    validator: timeRule, trigger: 'blur'
                }]
            },
            loopList: this.modelLoopList,
            // 线性指标集合
            lineNormList: [
                {
                    label: 'avg',
                    value: '每秒平均'
                },
                {
                    label: 'max',
                    value: '每秒最大'
                },
                {
                    label: 'min',
                    value: '每秒最小'
                }
            ],
            // 分位数指标
            quantileNormList: [
                {
                    label: 50,
                    value: '50%'
                },
                {
                    label: 60,
                    value: '60%'
                },
                {
                    label: 70,
                    value: '70%'
                },
                {
                    label: 80,
                    value: '80%'
                },
                {
                    label: 90,
                    value: '90%'
                },
                {
                    label: 95,
                    value: '95%'
                },
                {
                    label: 96,
                    value: '96%'
                },
                {
                    label: 97,
                    value: '97%'
                },
                {
                    label: 98,
                    value: '98%'
                },
                {
                    label: 99,
                    value: '99%'
                }
            ],
            // 时间范围
            rangeList: [
                {
                    label: 'ALL_DAY',
                    value: '日盘'
                },
                {
                    label: 'BIDDING',
                    value: '集合竞价'
                },
                {
                    label: 'MORNING',
                    value: '早盘'
                },
                {
                    label: 'AFTERNOON',
                    value: '午盘'
                },
                {
                    label: 'CLOSING-QUOTATION',
                    value: '盘后定价'
                }
            ],
            getName: getExchangeName
        };
    },
    computed: {
        ...mapState({
            // TODO：参数已被删除，从/ldplt/api/v1/product/latency/trace-models接口，spans参数块，使用spanName转义spanAlias即可
            spanLatencyDictDesc: state => {
                return state?.apmDirDesc?.spanLatencyDictDesc || {};
            }
        }),
        exchangeIdList: function() {
            return _.find(this.modalData.productList, ['productInstNo', this.modalData.formItem.productInstNo])?.exchangeIdList;
        }
    },
    methods: {
        handleChangeProduct() {
            this.$nextTick(() => {
                this.modalData.formItem.span = this.modalData.spanList[0];
                this.modalData.formItem.exchangeId = this.exchangeIdList[0];
            });
        },
        handleOpen() {
            this.initData = _.cloneDeep(this.modalInfo);
            // this.handleChangeRange(this.modalData.formItem.exchangeTime);
        },
        submitForm() {
            const refName = this.modalData.tabValue === 'statistical' ? 'formValid1' : 'formValid2';
            this.$refs[refName].validate(valid => {
                if (valid) {
                    this.modalData.status = false;
                    this.$emit('setInfoData', this.modalData);
                }
            });
        },
        // 更新自定义时间栏
        handleChangeRange(e) {
            try {
                const timeObj = this.modalData.exchangeTimeMap[this.modalData.formItem.exchangeId][e];
                this.modalData.formItem.time = [timeObj.startTime, timeObj.endTime];
            } catch (err) {
                console.log(err);
                this.$hMessage.error('更新查询时间失败，请检查产品信息中是否存在市场列表');
            }
        },
        checkboxStatus(e) {
            let list = [];
            const params = ['percentiles', 'indicators'];
            params.forEach(ele => {
                list = list.concat(this.modalData.formItem[ele]);
            });
            return list.length >= 3 && list.indexOf(e) < 0;
        },
        cancel() {
            for (const i in this.modalData) {
                if (this.modalData.hasOwnProperty(i)){
                    this.$set(this.modalData, i, this.initData[i]);
                }
            }
            this.modalData.status = false;
        }
    },
    components: {  aButton }
});
</script>

<style lang="less" scoped>
/deep/ .h-modal-body {
    padding: 10px 32px 16px;
}

/deep/ .h-tabs-tabpane {
    padding-right: 4px;
}

.title {
    position: relative;
    padding: 0 0 16px 20px;

    &::before {
        content: "";
        display: inline-block;
        position: absolute;
        left: 0;
        width: 4px;
        height: 17px;
        background: var(--link-color);
    }
}
</style>
