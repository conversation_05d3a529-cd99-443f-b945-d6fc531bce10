<template>
    <div>
        <h-msg-box-safe
            v-model="modalData.status"
            :escClose="true"
            :mask-closable="false"
            title="修改操作确认"
            width="600"
            height="420"
            @on-open="handleOpen">
            <a-tips
                style="margin-top: 10px;"
                type='warning'
                :tipText='tipText'
            ></a-tips>

            <div class="sql-title">SQL</div>
            <h-input
                v-model="sql"
                type="textarea"
                disabled
                placeholder="请输入..."></h-input>

            <div class="sql-title">执行节点</div>
            <h-table
                :columns="columns1"
                :data="modalData.coreList"
                maxHeight="220"></h-table>
            <template v-slot:footer>
                <h-poptip placement="top" width="300" trigger="hover">
                    <h-checkbox v-model="verifyStatus" style="margin-right: 10px;">不再询问</h-checkbox>
                    <div slot="content" class="info">
                        <div class="info-line">
                            <span class="info-line-title">选中：</span>
                            <p>表示后续SQL执行“写”操作时可直接执行，不再进行二次确认；</p>
                        </div>
                        <div class="info-line">
                            <span class="info-line-title">不选中：</span>
                            <p>表示后续SQL执行“写”操作时仍需进行二次确认；</p>
                        </div>
                        <div class="info-line">
                            <span class="info-line-title">注：</span>
                            <p>修改操作确认配置可在页面“配置”中修改；</p>
                        </div>
                    </div>
                </h-poptip>
                <h-button @click="modalData.status = false">取消</h-button>
                <h-button type="primary" @click="handleSubmit">确认</h-button>
            </template>
        </h-msg-box-safe>
    </div>
</template>

<script>
import _ from 'lodash';
import aTips from '@/components/common/apmTips/aTips';
export default ({
    props: {
        modalInfo: {
            type: Object,
            default: null
        },
        intances: {
            type: Array,
            default: () => []
        }
    },
    data() {
        return {
            modalData: this.modalInfo,
            verifyStatus: false,
            sql: '',
            tipText: 'SQL将在以下列表所显示的所有节点中执行，请确认。',
            columns1: [
                {
                    title: '节点',
                    key: 'instanceNo'
                },
                {
                    title: '集群',
                    key: 'clusterName'
                },
                {
                    title: '角色',
                    key: 'clusterRole',
                    render: (h, params) => {
                        const appInstanceClusterRoleDict = this.$store.state.apmDirDesc?.appInstanceClusterRoleDict;
                        return h('div', appInstanceClusterRoleDict?.[params?.row?.clusterRole] || '-');
                    }
                },
                {
                    title: '类型',
                    key: 'instanceDesc'
                }
            ]
        };
    },
    mounted() {
    },
    methods: {
        handleOpen() {
            this.sql = this.modalData.sqlList?.[0];
        },
        handleSubmit() {
            if (this.verifyStatus) {
                localStorage.setItem('apm.mdbsql.sqlValid', 'never');
            }
            const result = _.map(this.modalData.coreList, (obj) => { return { label: obj.instanceNo, value: obj.instanceId }; });
            this.$emit('verifySubmit', this.modalInfo.sqlList, result);
            this.modalData.status = false;
        }
    },
    components: { aTips }
});
</script>

<style lang="less" scoped>
/deep/ .h-modal-body {
    padding: 15px;
}

.radio-line {
    margin: 15px 0 10px;
}

.sql-title {
    position: relative;
    line-height: 40px;
    padding-left: 10px;

    &::before {
        content: "";
        display: inline-block;
        position: absolute;
        width: 4px;
        height: 16px;
        background: #2d8de5;
        top: 11px;
        left: 0;
    }
}

.info {
    & > .info-line {
        display: flex;
        margin-bottom: 6px;

        & > span {
            text-align: left;
        }

        & > p {
            width: 280px;
            text-align: left;
            white-space: normal;
        }

        & > .info-line-title {
            width: 50px;
            flex: none;
        }
    }

    .info-line:last-child {
        margin: 0;
    }
}

.node-group-box {
    width: calc(100% - 20px);
    height: auto;
    padding: 10px 10px 2px;
    margin-left: 20px;
    background-color: #cacfd454;
    border-radius: 4px;
}
</style>

