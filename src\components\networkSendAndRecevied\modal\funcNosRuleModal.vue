<template>
  <div>
    <h-msg-box-safe
        v-model="modalInfo.status"
        :mask-closable="false"
        :escClose="true"
        title="功能号"
        width="40"
        maxHeight="400"
        :footerHide="true"
    >
        <h-form
            ref="forms"
            :model="formItem"
            style="height: 100%;">
            <h-row class="head" :gutter="20">
                <h-col span="12">
                    <h-form-item label="" prop="funcNum" :label-width="5">
                        <h-input v-model="formItem.funcNum" placeholder="请输入功能号"></h-input>
                    </h-form-item>
                </h-col>
                <h-col span="4">
                    <h-button type="primary" icon="plus" @click="addFuncNos">添加</h-button>
                </h-col>
            </h-row>
        </h-form>
        <h-table showTitle :columns="columns" :data="tableData" :height="280" :loading="loading"></h-table>
    </h-msg-box-safe>
  </div>
</template>
<script>
export default {
    props: {
        modalData: {
            type: Object,
            default: function() {
                return {};
            }
        }
    },
    data() {
        return {
            modalInfo: this.modalData,
            loading: false,
            btnLoading: false,
            columns: [
                {
                    title: '序号',
                    key: 'Index'
                },
                {
                    title: '功能号',
                    key: 'FunctionID'
                },
                {
                    title: '操作',
                    key: 'action',
                    fixed: 'right',
                    width: 80,
                    render: (h, params) => {
                        return h('div', [
                            h('Button',
                                {
                                    props: {
                                        size: 'small',
                                        type: 'text'
                                    },
                                    on: {
                                        click: () => {
                                            // this.$hMsgBoxSafe.confirm({
                                            //     title: `删除`,
                                            //     content: `您确认要删除名为"${params.row.ruleName}"的配置吗？`,
                                            //     onOk: async () => {
                                            //         this.handleDeleteRule(params.row.id);
                                            //     }
                                            // });
                                        }
                                    }
                                },
                                '删除'
                            )
                        ]);
                    }
                }
            ],
            tableData: [],
            formItem: {
                funcNum: ''
            }
        };
    },
    methods: {
        cancelMethod() {
            this.modalInfo.status = false;
        }
    }
};
</script>
