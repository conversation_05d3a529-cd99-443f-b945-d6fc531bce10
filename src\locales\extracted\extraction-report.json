{"summary": {"totalTexts": 2045, "totalRoutes": 180, "extractTime": "2025-07-31T05:47:54.971Z"}, "routes": {"views\\index\\accordMonitor.vue": {"count": 7, "texts": [{"text": "请选择", "file": "views\\index\\accordMonitor.vue"}, {"text": "配置", "file": "views\\index\\accordMonitor.vue"}, {"text": "总上场次数：{{ totalCount }}", "file": "views\\index\\accordMonitor.vue"}, {"text": "主备核心数据同步差量告警：", "file": "views\\index\\accordMonitor.vue"}, {"text": "条", "file": "views\\index\\accordMonitor.vue"}, {"text": "数据自动刷新频率：", "file": "views\\index\\accordMonitor.vue"}, {"text": "秒", "file": "views\\index\\accordMonitor.vue"}]}, "views\\index\\accordObservation.vue": {"count": 3, "texts": [{"text": "核心数据同步", "file": "views\\index\\accordObservation.vue"}, {"text": "总览", "file": "views\\index\\accordObservation.vue"}, {"text": "未知", "file": "views\\index\\accordObservation.vue"}]}, "views\\index\\analyseConfig.vue": {"count": 10, "texts": [{"text": ">\r\n        <!-- 头部标题 -->\r\n        <a-title :title=", "file": "views\\index\\analyseConfig.vue"}, {"text": "场景信息为空,请新建或导入场景信息", "file": "views\\index\\analyseConfig.vue"}, {"text": ",            // 场景ID\r\n            sceneName:", "file": "views\\index\\analyseConfig.vue"}, {"text": ",          // 场景Name\r\n            apiDemoInfo:", "file": "views\\index\\analyseConfig.vue"}, {"text": "导出场景数据成功!", "file": "views\\index\\analyseConfig.vue"}, {"text": "导出场景失败!", "file": "views\\index\\analyseConfig.vue"}, {"text": "场景：${sceneName}", "file": "views\\index\\analyseConfig.vue"}, {"text": "导出场景", "file": "views\\index\\analyseConfig.vue"}, {"text": "导入场景", "file": "views\\index\\analyseConfig.vue"}, {"text": "新建场景", "file": "views\\index\\analyseConfig.vue"}]}, "views\\index\\analyseData.vue": {"count": 45, "texts": [{"text": ">测试实例\r\n                    <h-icon v-if=", "file": "views\\index\\analyseData.vue"}, {"text": ">来源用例</div>\r\n                <div class=", "file": "views\\index\\analyseData.vue"}, {"text": ">归属场景</div>\r\n                <div class=", "file": "views\\index\\analyseData.vue"}, {"text": ">开始时间</div>\r\n                <div class=", "file": "views\\index\\analyseData.vue"}, {"text": ">结束时间</div>\r\n                <div class=", "file": "views\\index\\analyseData.vue"}, {"text": ">产品节点</div>\r\n                <div class=", "file": "views\\index\\analyseData.vue"}, {"text": ">测试状态</div>\r\n                <div class=", "file": "views\\index\\analyseData.vue"}, {"text": ">报表状态</div>\r\n                <div class=", "file": "views\\index\\analyseData.vue"}, {"text": ">测试参数</div>\r\n                <div class=", "file": "views\\index\\analyseData.vue"}, {"text": ">备注\r\n                    <h-icon v-if=", "file": "views\\index\\analyseData.vue"}, {"text": "/>\r\n        <!-- 报表视图 -->\r\n        <div v-if=", "file": "views\\index\\analyseData.vue"}, {"text": "暂无图表", "file": "views\\index\\analyseData.vue"}, {"text": ",  // 场景id\r\n            loopType:", "file": "views\\index\\analyseData.vue"}, {"text": "跨度/指标", "file": "views\\index\\analyseData.vue"}, {"text": "最小（min）", "file": "views\\index\\analyseData.vue"}, {"text": "中位数（p50）", "file": "views\\index\\analyseData.vue"}, {"text": "平均（avg）", "file": "views\\index\\analyseData.vue"}, {"text": "95分位（p95）", "file": "views\\index\\analyseData.vue"}, {"text": "99分位（p99）", "file": "views\\index\\analyseData.vue"}, {"text": "最大（max）", "file": "views\\index\\analyseData.vue"}, {"text": "标准差（stdDeviation）", "file": "views\\index\\analyseData.vue"}, {"text": "指标详情", "file": "views\\index\\analyseData.vue"}, {"text": "资金账号", "file": "views\\index\\analyseData.vue"}, {"text": "交易所申报编号", "file": "views\\index\\analyseData.vue"}, {"text": "柜台委托号", "file": "views\\index\\analyseData.vue"}, {"text": "时延数据", "file": "views\\index\\analyseData.vue"}, {"text": "无", "file": "views\\index\\analyseData.vue"}, {"text": "纳秒", "file": "views\\index\\analyseData.vue"}, {"text": "微秒", "file": "views\\index\\analyseData.vue"}, {"text": "毫秒", "file": "views\\index\\analyseData.vue"}, {"text": "报表详情（单位：${unitName}）", "file": "views\\index\\analyseData.vue"}, {"text": "单位（${this.unitName}）", "file": "views\\index\\analyseData.vue"}, {"text": "${this.spanLatencyDictDesc[span] || ''} 指标详情", "file": "views\\index\\analyseData.vue"}, {"text": "选择实例", "file": "views\\index\\analyseData.vue"}, {"text": "测试实例", "file": "views\\index\\analyseData.vue"}, {"text": "来源用例", "file": "views\\index\\analyseData.vue"}, {"text": "归属场景", "file": "views\\index\\analyseData.vue"}, {"text": "开始时间", "file": "views\\index\\analyseData.vue"}, {"text": "结束时间", "file": "views\\index\\analyseData.vue"}, {"text": "产品节点", "file": "views\\index\\analyseData.vue"}, {"text": "测试状态", "file": "views\\index\\analyseData.vue"}, {"text": "报表状态", "file": "views\\index\\analyseData.vue"}, {"text": "测试参数", "file": "views\\index\\analyseData.vue"}, {"text": "备注", "file": "views\\index\\analyseData.vue"}, {"text": "配置报表", "file": "views\\index\\analyseData.vue"}]}, "views\\index\\apmMonitorConfig.vue": {"count": 2, "texts": [{"text": "当前节点不支持观测！请重新选择节点", "file": "views\\index\\apmMonitorConfig.vue"}, {"text": "已托管应用节点", "file": "views\\index\\apmMonitorConfig.vue"}]}, "views\\index\\appRunningState.vue": {"count": 19, "texts": [{"text": "应用状态墙", "file": "views\\index\\appRunningState.vue"}, {"text": ">基础配置</p>\r\n            <div style=", "file": "views\\index\\appRunningState.vue"}, {"text": ">告警规则配置</p>\r\n\r\n            <div class=", "file": "views\\index\\appRunningState.vue"}, {"text": ">序号</h-col>\r\n                <h-col span=", "file": "views\\index\\appRunningState.vue"}, {"text": ">开始时间</h-col>\r\n                <h-col span=", "file": "views\\index\\appRunningState.vue"}, {"text": ">结束时间</h-col>\r\n                <h-col span=", "file": "views\\index\\appRunningState.vue"}, {"text": "保存成功", "file": "views\\index\\appRunningState.vue"}, {"text": "保存失败", "file": "views\\index\\appRunningState.vue"}, {"text": "您确定保存当前所有配置吗？", "file": "views\\index\\appRunningState.vue"}, {"text": "服务视图", "file": "views\\index\\appRunningState.vue"}, {"text": "部署视图", "file": "views\\index\\appRunningState.vue"}, {"text": "基础配置", "file": "views\\index\\appRunningState.vue"}, {"text": "显示仲裁节点", "file": "views\\index\\appRunningState.vue"}, {"text": "告警规则配置", "file": "views\\index\\appRunningState.vue"}, {"text": "序号", "file": "views\\index\\appRunningState.vue"}, {"text": "节点状态", "file": "views\\index\\appRunningState.vue"}, {"text": "新增规则", "file": "views\\index\\appRunningState.vue"}, {"text": "保存配置", "file": "views\\index\\appRunningState.vue"}, {"text": "关闭", "file": "views\\index\\appRunningState.vue"}]}, "views\\index\\brokerDataLimit.vue": {"count": 12, "texts": [{"text": "降级熔断配置", "file": "views\\index\\brokerDataLimit.vue"}, {"text": "发布名单列表", "file": "views\\index\\brokerDataLimit.vue"}, {"text": "黑名单配置", "file": "views\\index\\brokerDataLimit.vue"}, {"text": "白名单配置", "file": "views\\index\\brokerDataLimit.vue"}, {"text": "限流名单配置", "file": "views\\index\\brokerDataLimit.vue"}, {"text": "组配置", "file": "views\\index\\brokerDataLimit.vue"}, {"text": "发布中", "file": "views\\index\\brokerDataLimit.vue"}, {"text": "发布", "file": "views\\index\\brokerDataLimit.vue"}, {"text": ", // 选中的产品\r\n            tabName:", "file": "views\\index\\brokerDataLimit.vue"}, {"text": "确定要发布列表中已启用的黑名单、白名单、限流名单吗？", "file": "views\\index\\brokerDataLimit.vue"}, {"text": "取消", "file": "views\\index\\brokerDataLimit.vue"}, {"text": "{{publishLoading ? '发布中' : '发布'}}", "file": "views\\index\\brokerDataLimit.vue"}]}, "views\\index\\coreFuncHandleObservation\\displaySettingDrawer.vue": {"count": 12, "texts": [{"text": ">\r\n                        已展示功能号：<span v-if=", "file": "views\\index\\coreFuncHandleObservation\\displaySettingDrawer.vue"}, {"text": "输入功能号查询", "file": "views\\index\\coreFuncHandleObservation\\displaySettingDrawer.vue"}, {"text": "功能号名称", "file": "views\\index\\coreFuncHandleObservation\\displaySettingDrawer.vue"}, {"text": "功能号", "file": "views\\index\\coreFuncHandleObservation\\displaySettingDrawer.vue"}, {"text": "是否展示", "file": "views\\index\\coreFuncHandleObservation\\displaySettingDrawer.vue"}, {"text": "为保证查看体验，建议选择不超过30个。", "file": "views\\index\\coreFuncHandleObservation\\displaySettingDrawer.vue"}, {"text": "查询功能号异常", "file": "views\\index\\coreFuncHandleObservation\\displaySettingDrawer.vue"}, {"text": "设置成功", "file": "views\\index\\coreFuncHandleObservation\\displaySettingDrawer.vue"}, {"text": "设置失败", "file": "views\\index\\coreFuncHandleObservation\\displaySettingDrawer.vue"}, {"text": "设置是否展示失败", "file": "views\\index\\coreFuncHandleObservation\\displaySettingDrawer.vue"}, {"text": "${this.enableTotalCount}个", "file": "views\\index\\coreFuncHandleObservation\\displaySettingDrawer.vue"}, {"text": "已展示功能号：", "file": "views\\index\\coreFuncHandleObservation\\displaySettingDrawer.vue"}]}, "views\\index\\coreFuncHandleObservation\\index.vue": {"count": 15, "texts": [{"text": "核心功能号处理", "file": "views\\index\\coreFuncHandleObservation\\index.vue"}, {"text": "请选择分片", "file": "views\\index\\coreFuncHandleObservation\\index.vue"}, {"text": "请选择集群", "file": "views\\index\\coreFuncHandleObservation\\index.vue"}, {"text": "分片", "file": "views\\index\\coreFuncHandleObservation\\index.vue"}, {"text": "集群", "file": "views\\index\\coreFuncHandleObservation\\index.vue"}, {"text": "执行吞吐(tps)", "file": "views\\index\\coreFuncHandleObservation\\index.vue"}, {"text": "平均执行耗时(ns)", "file": "views\\index\\coreFuncHandleObservation\\index.vue"}, {"text": "错误次数(次)", "file": "views\\index\\coreFuncHandleObservation\\index.vue"}, {"text": "错误率(%)", "file": "views\\index\\coreFuncHandleObservation\\index.vue"}, {"text": "队列积压", "file": "views\\index\\coreFuncHandleObservation\\index.vue"}, {"text": "查询分类失败", "file": "views\\index\\coreFuncHandleObservation\\index.vue"}, {"text": "查询分片信息异常", "file": "views\\index\\coreFuncHandleObservation\\index.vue"}, {"text": "查询集群信息", "file": "views\\index\\coreFuncHandleObservation\\index.vue"}, {"text": "分片、集群加载中", "file": "views\\index\\coreFuncHandleObservation\\index.vue"}, {"text": "功能号显示设置", "file": "views\\index\\coreFuncHandleObservation\\index.vue"}]}, "views\\index\\createRule\\createRule.vue": {"count": 48, "texts": [{"text": "获取监控指标", "file": "views\\index\\createRule\\createRule.vue"}, {"text": "配置规则内容", "file": "views\\index\\createRule\\createRule.vue"}, {"text": "预执行内容", "file": "views\\index\\createRule\\createRule.vue"}, {"text": "SQL语法说明.md", "file": "views\\index\\createRule\\createRule.vue"}, {"text": "核心", "file": "views\\index\\createRule\\createRule.vue"}, {"text": "请选择核心", "file": "views\\index\\createRule\\createRule.vue"}, {"text": "请输入指标名", "file": "views\\index\\createRule\\createRule.vue"}, {"text": "执行SQL", "file": "views\\index\\createRule\\createRule.vue"}, {"text": "测试结果", "file": "views\\index\\createRule\\createRule.vue"}, {"text": "暂无结果", "file": "views\\index\\createRule\\createRule.vue"}, {"text": "规则名称", "file": "views\\index\\createRule\\createRule.vue"}, {"text": "请输入规则名（不超过20字符）", "file": "views\\index\\createRule\\createRule.vue"}, {"text": "规则说明", "file": "views\\index\\createRule\\createRule.vue"}, {"text": "请输入规则说明（不超过200字符）", "file": "views\\index\\createRule\\createRule.vue"}, {"text": ">上一步</a-button>\r\n            <span v-if=", "file": "views\\index\\createRule\\createRule.vue"}, {"text": ">创建监控规则</a-button>\r\n            <a-button type=", "file": "views\\index\\createRule\\createRule.vue"}, {"text": "指标1", "file": "views\\index\\createRule\\createRule.vue"}, {"text": "指标2", "file": "views\\index\\createRule\\createRule.vue"}, {"text": "输入不能为空", "file": "views\\index\\createRule\\createRule.vue"}, {"text": "指标名重复", "file": "views\\index\\createRule\\createRule.vue"}, {"text": "获取变量成功", "file": "views\\index\\createRule\\createRule.vue"}, {"text": "变量获取失败，请重试！", "file": "views\\index\\createRule\\createRule.vue"}, {"text": "获取变量失败,", "file": "views\\index\\createRule\\createRule.vue"}, {"text": "测试失败，请重试", "file": "views\\index\\createRule\\createRule.vue"}, {"text": "测试sql失败", "file": "views\\index\\createRule\\createRule.vue"}, {"text": "创建成功", "file": "views\\index\\createRule\\createRule.vue"}, {"text": "规则创建失败", "file": "views\\index\\createRule\\createRule.vue"}, {"text": "查询核心失败", "file": "views\\index\\createRule\\createRule.vue"}, {"text": "最大输入长度500个字符", "file": "views\\index\\createRule\\createRule.vue"}, {"text": "只允许写一条select语句", "file": "views\\index\\createRule\\createRule.vue"}, {"text": "留下", "file": "views\\index\\createRule\\createRule.vue"}, {"text": "离开后当前操作将不会保存，数据会丢失，请谨慎操作！", "file": "views\\index\\createRule\\createRule.vue"}, {"text": "创建监控规则-自定义SQL", "file": "views\\index\\createRule\\createRule.vue"}, {"text": "预执行SQL", "file": "views\\index\\createRule\\createRule.vue"}, {"text": "“预执行SQL”执行后可得到对应变量，变量可在下方“执行SQL”被引用。点击", "file": "views\\index\\createRule\\createRule.vue"}, {"text": "下载SQL语法指南", "file": "views\\index\\createRule\\createRule.vue"}, {"text": "获取变量", "file": "views\\index\\createRule\\createRule.vue"}, {"text": "点击“获取变量”，生成对应可引用变量，在下方显示", "file": "views\\index\\createRule\\createRule.vue"}, {"text": "可引用变量", "file": "views\\index\\createRule\\createRule.vue"}, {"text": "正式执行内容", "file": "views\\index\\createRule\\createRule.vue"}, {"text": "SQL语法指南", "file": "views\\index\\createRule\\createRule.vue"}, {"text": "统一策略：取结果集的第一行第一列，结果集必须是数字", "file": "views\\index\\createRule\\createRule.vue"}, {"text": "SQL结果", "file": "views\\index\\createRule\\createRule.vue"}, {"text": "测试", "file": "views\\index\\createRule\\createRule.vue"}, {"text": "针对上一步获取的比对数据所形成的告警指标，设置对应的告警阈值。", "file": "views\\index\\createRule\\createRule.vue"}, {"text": "上一步", "file": "views\\index\\createRule\\createRule.vue"}, {"text": "下一步：配置规则内容", "file": "views\\index\\createRule\\createRule.vue"}, {"text": "创建监控规则", "file": "views\\index\\createRule\\createRule.vue"}]}, "views\\index\\dataSecondAppearance.vue": {"count": 5, "texts": [{"text": "pageName === 'detail' ? '数据二次上场' : ''", "file": "views\\index\\dataSecondAppearance.vue"}, {"text": "数据二次上场", "file": "views\\index\\dataSecondAppearance.vue"}, {"text": ", // 选中的产品\r\n            pageName:", "file": "views\\index\\dataSecondAppearance.vue"}, {"text": ", // tab默认选择\r\n            titleText:", "file": "views\\index\\dataSecondAppearance.vue"}, {"text": "创建二次上场任务", "file": "views\\index\\dataSecondAppearance.vue"}]}, "views\\index\\emergencyManagementConfig\\addModal.vue": {"count": 3, "texts": [{"text": "添加路由", "file": "views\\index\\emergencyManagementConfig\\addModal.vue"}, {"text": "添加路由失败", "file": "views\\index\\emergencyManagementConfig\\addModal.vue"}, {"text": "确定", "file": "views\\index\\emergencyManagementConfig\\addModal.vue"}]}, "views\\index\\emergencyManagementConfig\\helpModal.vue": {"count": 1, "texts": [{"text": "配置说明", "file": "views\\index\\emergencyManagementConfig\\helpModal.vue"}]}, "views\\index\\emergencyManagementConfig\\index.vue": {"count": 3, "texts": [{"text": "配置应急修改", "file": "views\\index\\emergencyManagementConfig\\index.vue"}, {"text": "输入节点名称", "file": "views\\index\\emergencyManagementConfig\\index.vue"}, {"text": "初始化配置应急失败", "file": "views\\index\\emergencyManagementConfig\\index.vue"}]}, "views\\index\\emergencyManagementConfig\\routeConfig.vue": {"count": 19, "texts": [{"text": "路由配置", "file": "views\\index\\emergencyManagementConfig\\routeConfig.vue"}, {"text": ">\r\n                当前版本：{{ version ||", "file": "views\\index\\emergencyManagementConfig\\routeConfig.vue"}, {"text": "输入功能号", "file": "views\\index\\emergencyManagementConfig\\routeConfig.vue"}, {"text": "修改配置", "file": "views\\index\\emergencyManagementConfig\\routeConfig.vue"}, {"text": "配置json预览", "file": "views\\index\\emergencyManagementConfig\\routeConfig.vue"}, {"text": "目标系统号", "file": "views\\index\\emergencyManagementConfig\\routeConfig.vue"}, {"text": "节点", "file": "views\\index\\emergencyManagementConfig\\routeConfig.vue"}, {"text": "目标端ID", "file": "views\\index\\emergencyManagementConfig\\routeConfig.vue"}, {"text": "操作", "file": "views\\index\\emergencyManagementConfig\\routeConfig.vue"}, {"text": "删除", "file": "views\\index\\emergencyManagementConfig\\routeConfig.vue"}, {"text": "更新失败", "file": "views\\index\\emergencyManagementConfig\\routeConfig.vue"}, {"text": "更新成功", "file": "views\\index\\emergencyManagementConfig\\routeConfig.vue"}, {"text": "获取路由配置失败", "file": "views\\index\\emergencyManagementConfig\\routeConfig.vue"}, {"text": "确定要更新配置吗？", "file": "views\\index\\emergencyManagementConfig\\routeConfig.vue"}, {"text": "参数配置更新后实时生效，重启后失效。", "file": "views\\index\\emergencyManagementConfig\\routeConfig.vue"}, {"text": "确定要删除该路由？", "file": "views\\index\\emergencyManagementConfig\\routeConfig.vue"}, {"text": "路由配置无version信息，不可添加、删除、修改配置。", "file": "views\\index\\emergencyManagementConfig\\routeConfig.vue"}, {"text": "当前版本：{{ version || \"-\" }}", "file": "views\\index\\emergencyManagementConfig\\routeConfig.vue"}, {"text": "更新配置", "file": "views\\index\\emergencyManagementConfig\\routeConfig.vue"}]}, "views\\index\\emergencyManagementConfig\\routeInfoForm.vue": {"count": 6, "texts": [{"text": "请选择要修改的路由", "file": "views\\index\\emergencyManagementConfig\\routeInfoForm.vue"}, {"text": "请输入", "file": "views\\index\\emergencyManagementConfig\\routeInfoForm.vue"}, {"text": "获取目标端id失败", "file": "views\\index\\emergencyManagementConfig\\routeInfoForm.vue"}, {"text": "支持*、?、数字(不为负值)。多个功能号使用英文分号分隔", "file": "views\\index\\emergencyManagementConfig\\routeInfoForm.vue"}, {"text": "支持*、数字(不为负值、最大255)。多个功能号使用英文分号分隔", "file": "views\\index\\emergencyManagementConfig\\routeInfoForm.vue"}, {"text": "支持*、数字(不为负值、最大65535)", "file": "views\\index\\emergencyManagementConfig\\routeInfoForm.vue"}]}, "views\\index\\latencyTrendAnalysis.vue": {"count": 28, "texts": [{"text": "链路时延趋势分析", "file": "views\\index\\latencyTrendAnalysis.vue"}, {"text": "选择日期", "file": "views\\index\\latencyTrendAnalysis.vue"}, {"text": "业务类型", "file": "views\\index\\latencyTrendAnalysis.vue"}, {"text": "链路状态", "file": "views\\index\\latencyTrendAnalysis.vue"}, {"text": "选择跨度", "file": "views\\index\\latencyTrendAnalysis.vue"}, {"text": "暂无数据", "file": "views\\index\\latencyTrendAnalysis.vue"}, {"text": "应用分段时延趋势", "file": "views\\index\\latencyTrendAnalysis.vue"}, {"text": "应用分段时延统计", "file": "views\\index\\latencyTrendAnalysis.vue"}, {"text": "90分位（p90）", "file": "views\\index\\latencyTrendAnalysis.vue"}, {"text": "查看时延分布", "file": "views\\index\\latencyTrendAnalysis.vue"}, {"text": "应用分段时延分布", "file": "views\\index\\latencyTrendAnalysis.vue"}, {"text": "应用逐笔时延统计", "file": "views\\index\\latencyTrendAnalysis.vue"}, {"text": "请选择查询分页", "file": "views\\index\\latencyTrendAnalysis.vue"}, {"text": "中位数", "file": "views\\index\\latencyTrendAnalysis.vue"}, {"text": "平均值", "file": "views\\index\\latencyTrendAnalysis.vue"}, {"text": "最大值", "file": "views\\index\\latencyTrendAnalysis.vue"}, {"text": "最小值", "file": "views\\index\\latencyTrendAnalysis.vue"}, {"text": "90分位", "file": "views\\index\\latencyTrendAnalysis.vue"}, {"text": "95分位", "file": "views\\index\\latencyTrendAnalysis.vue"}, {"text": "99分位", "file": "views\\index\\latencyTrendAnalysis.vue"}, {"text": ";// x轴的名称\r\n\r\n                htmlStr +=", "file": "views\\index\\latencyTrendAnalysis.vue"}, {"text": "委托笔数:", "file": "views\\index\\latencyTrendAnalysis.vue"}, {"text": "时延:", "file": "views\\index\\latencyTrendAnalysis.vue"}, {"text": "时间范围：${sTime} - ${eTime} 总记录笔数：${count || 0}笔", "file": "views\\index\\latencyTrendAnalysis.vue"}, {"text": "当前产品尚未配置模型，请前往“产品服务配置”进行模型配置", "file": "views\\index\\latencyTrendAnalysis.vue"}, {"text": "前往配置", "file": "views\\index\\latencyTrendAnalysis.vue"}, {"text": "';// x轴的名称\r\n\r\n                htmlStr += '", "file": "views\\index\\latencyTrendAnalysis.vue"}, {"text": "查看", "file": "views\\index\\latencyTrendAnalysis.vue"}]}, "views\\index\\ldpDataObservation.vue": {"count": 7, "texts": [{"text": "集合竞价", "file": "views\\index\\ldpDataObservation.vue"}, {"text": "早盘竞价交易", "file": "views\\index\\ldpDataObservation.vue"}, {"text": "盘休", "file": "views\\index\\ldpDataObservation.vue"}, {"text": "午盘竞价交易", "file": "views\\index\\ldpDataObservation.vue"}, {"text": "盘后定价", "file": "views\\index\\ldpDataObservation.vue"}, {"text": "当前应用节点不支持观测！请重新选择节点", "file": "views\\index\\ldpDataObservation.vue"}, {"text": "今天是: {{ currentTime }}", "file": "views\\index\\ldpDataObservation.vue"}]}, "views\\index\\ldpLinkConfig.vue": {"count": 9, "texts": [{"text": "产品节点配置管理", "file": "views\\index\\ldpLinkConfig.vue"}, {"text": "删除成功", "file": "views\\index\\ldpLinkConfig.vue"}, {"text": "删除失败", "file": "views\\index\\ldpLinkConfig.vue"}, {"text": ") {\r\n                // 如果是", "file": "views\\index\\ldpLinkConfig.vue"}, {"text": "您确定删除名为\"${this.productInfo.productName}\"产品节点吗？", "file": "views\\index\\ldpLinkConfig.vue"}, {"text": "对接产品节点", "file": "views\\index\\ldpLinkConfig.vue"}, {"text": "已注册产品节点", "file": "views\\index\\ldpLinkConfig.vue"}, {"text": "删除产品节点", "file": "views\\index\\ldpLinkConfig.vue"}, {"text": "同步节点信息", "file": "views\\index\\ldpLinkConfig.vue"}]}, "views\\index\\ldpLogCenter.vue": {"count": 5, "texts": [{"text": "回库错误重试运维", "file": "views\\index\\ldpLogCenter.vue"}, {"text": "回库错误重试", "file": "views\\index\\ldpLogCenter.vue"}, {"text": "回库错误日志", "file": "views\\index\\ldpLogCenter.vue"}, {"text": "日期：{{ currentDate }}", "file": "views\\index\\ldpLogCenter.vue"}, {"text": "日志查询超时时间：", "file": "views\\index\\ldpLogCenter.vue"}]}, "views\\index\\ldpMonitor\\clusterMonitor.vue": {"count": 2, "texts": [{"text": "集群状态", "file": "views\\index\\ldpMonitor\\clusterMonitor.vue"}, {"text": "应用状态", "file": "views\\index\\ldpMonitor\\clusterMonitor.vue"}]}, "views\\index\\ldpMonitor\\index.vue": {"count": 6, "texts": [{"text": "盘前", "file": "views\\index\\ldpMonitor\\index.vue"}, {"text": "盘中", "file": "views\\index\\ldpMonitor\\index.vue"}, {"text": "盘后", "file": "views\\index\\ldpMonitor\\index.vue"}, {"text": "，在", "file": "views\\index\\ldpMonitor\\index.vue"}, {"text": "和", "file": "views\\index\\ldpMonitor\\index.vue"}, {"text": "之间跳转的时候，\r\n    // 由于会渲染同样的", "file": "views\\index\\ldpMonitor\\index.vue"}]}, "views\\index\\ldpMonitor\\ldpAppMonitor.vue": {"count": 1, "texts": [{"text": "核心性能", "file": "views\\index\\ldpMonitor\\ldpAppMonitor.vue"}]}, "views\\index\\ldpTable.vue": {"count": 6, "texts": [{"text": "查询内存表", "file": "views\\index\\ldpTable.vue"}, {"text": "数据查询", "file": "views\\index\\ldpTable.vue"}, {"text": "数据修改", "file": "views\\index\\ldpTable.vue"}, {"text": "内存表结构", "file": "views\\index\\ldpTable.vue"}, {"text": "请从左侧菜单选择内存表进行查看！", "file": "views\\index\\ldpTable.vue"}, {"text": "请选择并连接应用节点！", "file": "views\\index\\ldpTable.vue"}]}, "views\\index\\locateConfig.vue": {"count": 3, "texts": [{"text": "Locate配置一致性校验", "file": "views\\index\\locateConfig.vue"}, {"text": "节点配置管理", "file": "views\\index\\locateConfig.vue"}, {"text": "节点配置校验", "file": "views\\index\\locateConfig.vue"}]}, "views\\index\\managementQuery.vue": {"count": 30, "texts": [{"text": "instanceList.length ? '请选择节点': '当前暂无活跃节点'", "file": "views\\index\\managementQuery.vue"}, {"text": "输入节点名查询", "file": "views\\index\\managementQuery.vue"}, {"text": "搜索管理功能", "file": "views\\index\\managementQuery.vue"}, {"text": ">显示", "file": "views\\index\\managementQuery.vue"}, {"text": "请选择管理功能手动发起请求", "file": "views\\index\\managementQuery.vue"}, {"text": "请选择管理功能", "file": "views\\index\\managementQuery.vue"}, {"text": "请选择节点", "file": "views\\index\\managementQuery.vue"}, {"text": "当前暂无活跃节点", "file": "views\\index\\managementQuery.vue"}, {"text": "暂无", "file": "views\\index\\managementQuery.vue"}, {"text": "其他数据", "file": "views\\index\\managementQuery.vue"}, {"text": ";\r\n// JSON代码高亮需要由JavaScript插件支持\r\nimport", "file": "views\\index\\managementQuery.vue"}, {"text": ", // 管理功能执行耗时\r\n            apmTime:", "file": "views\\index\\managementQuery.vue"}, {"text": "初始化默认jsonPath配置失败:", "file": "views\\index\\managementQuery.vue"}, {"text": "如需保存当前选中功能号输入参数，请手动触发一次查询请求！", "file": "views\\index\\managementQuery.vue"}, {"text": "下载", "file": "views\\index\\managementQuery.vue"}, {"text": "获取jsonPath配置失败:", "file": "views\\index\\managementQuery.vue"}, {"text": "功能号无返回数据！", "file": "views\\index\\managementQuery.vue"}, {"text": "导出", "file": "views\\index\\managementQuery.vue"}, {"text": "批量导出", "file": "views\\index\\managementQuery.vue"}, {"text": "支持批量导出当前产品下的所有管理功能数据", "file": "views\\index\\managementQuery.vue"}, {"text": "快捷导出", "file": "views\\index\\managementQuery.vue"}, {"text": "点击一键导出所选产品管理所有核心的「GetFuncDetailInfo」管理功能", "file": "views\\index\\managementQuery.vue"}, {"text": "{{  child.chineseName || '暂无'}}", "file": "views\\index\\managementQuery.vue"}, {"text": "未查询到管理功能", "file": "views\\index\\managementQuery.vue"}, {"text": "显示\"中文译名\"", "file": "views\\index\\managementQuery.vue"}, {"text": "整体耗时：", "file": "views\\index\\managementQuery.vue"}, {"text": "APM耗时：", "file": "views\\index\\managementQuery.vue"}, {"text": "应用节点耗时：", "file": "views\\index\\managementQuery.vue"}, {"text": "配置表格", "file": "views\\index\\managementQuery.vue"}, {"text": "关闭全部", "file": "views\\index\\managementQuery.vue"}]}, "views\\index\\marketAllLink.vue": {"count": 9, "texts": [{"text": "行情全链路应用节点关系", "file": "views\\index\\marketAllLink.vue"}, {"text": "终端客户全链路质量监控", "file": "views\\index\\marketAllLink.vue"}, {"text": "行情数据时延趋势监控", "file": "views\\index\\marketAllLink.vue"}, {"text": "NSQ链路", "file": "views\\index\\marketAllLink.vue"}, {"text": "FPGA链路", "file": "views\\index\\marketAllLink.vue"}, {"text": "FPGA全链路", "file": "views\\index\\marketAllLink.vue"}, {"text": "应用节点状态", "file": "views\\index\\marketAllLink.vue"}, {"text": "时延正常率", "file": "views\\index\\marketAllLink.vue"}, {"text": "NSQ全链路", "file": "views\\index\\marketAllLink.vue"}]}, "views\\index\\marketMonitor.vue": {"count": 11, "texts": [{"text": "当前实例无采集时延走势信息", "file": "views\\index\\marketMonitor.vue"}, {"text": "该模型文件暂无拓扑结构", "file": "views\\index\\marketMonitor.vue"}, {"text": "最近五分钟", "file": "views\\index\\marketMonitor.vue"}, {"text": "最近十五分钟", "file": "views\\index\\marketMonitor.vue"}, {"text": "最近三十分钟", "file": "views\\index\\marketMonitor.vue"}, {"text": "快照行情(μs)", "file": "views\\index\\marketMonitor.vue"}, {"text": "指数行情(μs)", "file": "views\\index\\marketMonitor.vue"}, {"text": "逐笔委托(μs)", "file": "views\\index\\marketMonitor.vue"}, {"text": "逐笔成交(μs)", "file": "views\\index\\marketMonitor.vue"}, {"text": "{{ $getProductType(productInfo.productType) }}时延监控", "file": "views\\index\\marketMonitor.vue"}, {"text": "显示拓扑", "file": "views\\index\\marketMonitor.vue"}]}, "views\\index\\marketNodeDelayList.vue": {"count": 14, "texts": [{"text": "行情产品节点穿透时延分析", "file": "views\\index\\marketNodeDelayList.vue"}, {"text": "汇总查询", "file": "views\\index\\marketNodeDelayList.vue"}, {"text": "交易日", "file": "views\\index\\marketNodeDelayList.vue"}, {"text": "最小(min)", "file": "views\\index\\marketNodeDelayList.vue"}, {"text": "1分位数(p1)", "file": "views\\index\\marketNodeDelayList.vue"}, {"text": "中位数(p50)", "file": "views\\index\\marketNodeDelayList.vue"}, {"text": "平均数(avg)", "file": "views\\index\\marketNodeDelayList.vue"}, {"text": "95分位数(p95)", "file": "views\\index\\marketNodeDelayList.vue"}, {"text": "99分位数(p99)", "file": "views\\index\\marketNodeDelayList.vue"}, {"text": "最大(max)", "file": "views\\index\\marketNodeDelayList.vue"}, {"text": "统计总数", "file": "views\\index\\marketNodeDelayList.vue"}, {"text": "交易日期", "file": "views\\index\\marketNodeDelayList.vue"}, {"text": "交易时间", "file": "views\\index\\marketNodeDelayList.vue"}, {"text": "查询失败!", "file": "views\\index\\marketNodeDelayList.vue"}]}, "views\\index\\marketPenetrateList.vue": {"count": 28, "texts": [{"text": "下单数", "file": "views\\index\\marketPenetrateList.vue"}, {"text": "全链路时延", "file": "views\\index\\marketPenetrateList.vue"}, {"text": "柜台上行时延", "file": "views\\index\\marketPenetrateList.vue"}, {"text": "柜台下行时延", "file": "views\\index\\marketPenetrateList.vue"}, {"text": "TGW上行时延", "file": "views\\index\\marketPenetrateList.vue"}, {"text": "TGW下行时延", "file": "views\\index\\marketPenetrateList.vue"}, {"text": "TGW时延", "file": "views\\index\\marketPenetrateList.vue"}, {"text": "交易所时延", "file": "views\\index\\marketPenetrateList.vue"}, {"text": "行情-交易时延", "file": "views\\index\\marketPenetrateList.vue"}, {"text": "交易-收到行情时延", "file": "views\\index\\marketPenetrateList.vue"}, {"text": "防火墙上行时延", "file": "views\\index\\marketPenetrateList.vue"}, {"text": "防火墙下行时延", "file": "views\\index\\marketPenetrateList.vue"}, {"text": "拓扑结构", "file": "views\\index\\marketPenetrateList.vue"}, {"text": "交易市场", "file": "views\\index\\marketPenetrateList.vue"}, {"text": "深圳交易所", "file": "views\\index\\marketPenetrateList.vue"}, {"text": "上海交易所", "file": "views\\index\\marketPenetrateList.vue"}, {"text": "交易核心", "file": "views\\index\\marketPenetrateList.vue"}, {"text": "报盘网关", "file": "views\\index\\marketPenetrateList.vue"}, {"text": "行情网关", "file": "views\\index\\marketPenetrateList.vue"}, {"text": "资金账户", "file": "views\\index\\marketPenetrateList.vue"}, {"text": "证券代码", "file": "views\\index\\marketPenetrateList.vue"}, {"text": "席位号", "file": "views\\index\\marketPenetrateList.vue"}, {"text": "汇总间隔", "file": "views\\index\\marketPenetrateList.vue"}, {"text": "天", "file": "views\\index\\marketPenetrateList.vue"}, {"text": "汇总方式", "file": "views\\index\\marketPenetrateList.vue"}, {"text": "详情查询", "file": "views\\index\\marketPenetrateList.vue"}, {"text": "日期", "file": "views\\index\\marketPenetrateList.vue"}, {"text": "业务字段", "file": "views\\index\\marketPenetrateList.vue"}]}, "views\\index\\marketTimeDelay.vue": {"count": 10, "texts": [{"text": "选择时间", "file": "views\\index\\marketTimeDelay.vue"}, {"text": "选择交易所", "file": "views\\index\\marketTimeDelay.vue"}, {"text": "选择数据类型", "file": "views\\index\\marketTimeDelay.vue"}, {"text": "平均", "file": "views\\index\\marketTimeDelay.vue"}, {"text": "每秒最大", "file": "views\\index\\marketTimeDelay.vue"}, {"text": "每秒最小", "file": "views\\index\\marketTimeDelay.vue"}, {"text": "本地全系统时延走势分析(μs)", "file": "views\\index\\marketTimeDelay.vue"}, {"text": "交易所到消费端总时延(ms)", "file": "views\\index\\marketTimeDelay.vue"}, {"text": "查询的数据不存在", "file": "views\\index\\marketTimeDelay.vue"}, {"text": "确认", "file": "views\\index\\marketTimeDelay.vue"}]}, "views\\index\\mcDataObservation.vue": {"count": 1, "texts": [{"text": "已托管MC3.0集群", "file": "views\\index\\mcDataObservation.vue"}]}, "views\\index\\mcDeploy.vue": {"count": 2, "texts": [{"text": "MC3.0配置管理", "file": "views\\index\\mcDeploy.vue"}, {"text": "主题", "file": "views\\index\\mcDeploy.vue"}]}, "views\\index\\mdbDataExport\\detailDrawer.vue": {"count": 11, "texts": [{"text": "详情", "file": "views\\index\\mdbDataExport\\detailDrawer.vue"}, {"text": "服务器", "file": "views\\index\\mdbDataExport\\detailDrawer.vue"}, {"text": "表名", "file": "views\\index\\mdbDataExport\\detailDrawer.vue"}, {"text": "远程路径", "file": "views\\index\\mdbDataExport\\detailDrawer.vue"}, {"text": "导出状态和结果", "file": "views\\index\\mdbDataExport\\detailDrawer.vue"}, {"text": "导出中", "file": "views\\index\\mdbDataExport\\detailDrawer.vue"}, {"text": "导出成功", "file": "views\\index\\mdbDataExport\\detailDrawer.vue"}, {"text": "导出失败", "file": "views\\index\\mdbDataExport\\detailDrawer.vue"}, {"text": "待导出", "file": "views\\index\\mdbDataExport\\detailDrawer.vue"}, {"text": "查看历史详情失败", "file": "views\\index\\mdbDataExport\\detailDrawer.vue"}, {"text": "错误信息", "file": "views\\index\\mdbDataExport\\detailDrawer.vue"}]}, "views\\index\\mdbDataExport\\exportHistory.vue": {"count": 14, "texts": [{"text": "任务ID", "file": "views\\index\\mdbDataExport\\exportHistory.vue"}, {"text": "导出时间", "file": "views\\index\\mdbDataExport\\exportHistory.vue"}, {"text": "导出状态", "file": "views\\index\\mdbDataExport\\exportHistory.vue"}, {"text": "导出内存表数量", "file": "views\\index\\mdbDataExport\\exportHistory.vue"}, {"text": "删除远程服务器数据", "file": "views\\index\\mdbDataExport\\exportHistory.vue"}, {"text": "删除APM服务器数据", "file": "views\\index\\mdbDataExport\\exportHistory.vue"}, {"text": "已删除", "file": "views\\index\\mdbDataExport\\exportHistory.vue"}, {"text": "删除历史任务失败,", "file": "views\\index\\mdbDataExport\\exportHistory.vue"}, {"text": "下载成功!", "file": "views\\index\\mdbDataExport\\exportHistory.vue"}, {"text": "下载失败", "file": "views\\index\\mdbDataExport\\exportHistory.vue"}, {"text": "下载单张表失败,", "file": "views\\index\\mdbDataExport\\exportHistory.vue"}, {"text": "删除导出任务的同时，可同时删除该任务导出在服务器的内存表数据。请勾选需要删除的内容。", "file": "views\\index\\mdbDataExport\\exportHistory.vue"}, {"text": "删除导出任务，可同时删除该任务导出在服务器的内存表数据。请勾选需要删除的内容。", "file": "views\\index\\mdbDataExport\\exportHistory.vue"}, {"text": "查看原因", "file": "views\\index\\mdbDataExport\\exportHistory.vue"}]}, "views\\index\\mdbDataExport\\exportTable.vue": {"count": 4, "texts": [{"text": ">最近一次导出</span>\r\n              <span class=", "file": "views\\index\\mdbDataExport\\exportTable.vue"}, {"text": "最近一次导出", "file": "views\\index\\mdbDataExport\\exportTable.vue"}, {"text": "&nbsp;&nbsp;失败原因", "file": "views\\index\\mdbDataExport\\exportTable.vue"}, {"text": "创建任务", "file": "views\\index\\mdbDataExport\\exportTable.vue"}]}, "views\\index\\mdbDataExport\\index.vue": {"count": 3, "texts": [{"text": "MDB数据导出", "file": "views\\index\\mdbDataExport\\index.vue"}, {"text": "导出历史", "file": "views\\index\\mdbDataExport\\index.vue"}, {"text": "导出MDB数据", "file": "views\\index\\mdbDataExport\\index.vue"}]}, "views\\index\\mdbDataObservation.vue": {"count": 0, "texts": []}, "views\\index\\mdbPrivilegeManage.vue": {"count": 31, "texts": [{"text": "MDB权限管理", "file": "views\\index\\mdbPrivilegeManage.vue"}, {"text": "将本地用户权限下发至MDB", "file": "views\\index\\mdbPrivilegeManage.vue"}, {"text": ">权限可编辑  &nbsp;&nbsp;&nbsp;&nbsp;<h-switch v-model=", "file": "views\\index\\mdbPrivilegeManage.vue"}, {"text": "与", "file": "views\\index\\mdbPrivilegeManage.vue"}, {"text": "）。若当前权限内容为", "file": "views\\index\\mdbPrivilegeManage.vue"}, {"text": "权限内容。如需编辑，请至当前页面顶部", "file": "views\\index\\mdbPrivilegeManage.vue"}, {"text": "(最新下发：", "file": "views\\index\\mdbPrivilegeManage.vue"}, {"text": "}}(最新下发：{{hisPermissionsInfo.headerTime ||", "file": "views\\index\\mdbPrivilegeManage.vue"}, {"text": "下发失败", "file": "views\\index\\mdbPrivilegeManage.vue"}, {"text": "下发成功", "file": "views\\index\\mdbPrivilegeManage.vue"}, {"text": "用户管理", "file": "views\\index\\mdbPrivilegeManage.vue"}, {"text": "角色管理", "file": "views\\index\\mdbPrivilegeManage.vue"}, {"text": "下发结果", "file": "views\\index\\mdbPrivilegeManage.vue"}, {"text": "MDB权限与本地权限一致。", "file": "views\\index\\mdbPrivilegeManage.vue"}, {"text": "MDB权限与本地权限不一致。", "file": "views\\index\\mdbPrivilegeManage.vue"}, {"text": "暂时无法获取MDB权限，请稍后查看。", "file": "views\\index\\mdbPrivilegeManage.vue"}, {"text": "权限下发成功！", "file": "views\\index\\mdbPrivilegeManage.vue"}, {"text": "权限下发异常！", "file": "views\\index\\mdbPrivilegeManage.vue"}, {"text": "权限下发失败！", "file": "views\\index\\mdbPrivilegeManage.vue"}, {"text": "导出成功!", "file": "views\\index\\mdbPrivilegeManage.vue"}, {"text": "导入权限成功", "file": "views\\index\\mdbPrivilegeManage.vue"}, {"text": "导入权限失败", "file": "views\\index\\mdbPrivilegeManage.vue"}, {"text": "用户权限部分下发失败，请稍后重新尝试。本次下发详细如下：", "file": "views\\index\\mdbPrivilegeManage.vue"}, {"text": "用户权限下发失败，请稍后重新尝试。本次下发详细如下：", "file": "views\\index\\mdbPrivilegeManage.vue"}, {"text": "权限可编辑状态修改成功!", "file": "views\\index\\mdbPrivilegeManage.vue"}, {"text": "权限操作", "file": "views\\index\\mdbPrivilegeManage.vue"}, {"text": "同步权限", "file": "views\\index\\mdbPrivilegeManage.vue"}, {"text": "查看MDB权限", "file": "views\\index\\mdbPrivilegeManage.vue"}, {"text": "导入权限", "file": "views\\index\\mdbPrivilegeManage.vue"}, {"text": "导出权限", "file": "views\\index\\mdbPrivilegeManage.vue"}, {"text": "权限可编辑  &nbsp;&nbsp;&nbsp;&nbsp;", "file": "views\\index\\mdbPrivilegeManage.vue"}]}, "views\\index\\monitor\\businessMonitor.vue": {"count": 7, "texts": [{"text": "展示链路拓扑", "file": "views\\index\\monitor\\businessMonitor.vue"}, {"text": "时延趋势", "file": "views\\index\\monitor\\businessMonitor.vue"}, {"text": "10秒", "file": "views\\index\\monitor\\businessMonitor.vue"}, {"text": "30秒", "file": "views\\index\\monitor\\businessMonitor.vue"}, {"text": "获取图表监控时延数据失败！", "file": "views\\index\\monitor\\businessMonitor.vue"}, {"text": "获取聚合数据失败！", "file": "views\\index\\monitor\\businessMonitor.vue"}, {"text": "T3监视器", "file": "views\\index\\monitor\\businessMonitor.vue"}]}, "views\\index\\monitor\\index.vue": {"count": 0, "texts": []}, "views\\index\\networkSendAndRecevied.vue": {"count": 4, "texts": [{"text": "应用抓包", "file": "views\\index\\networkSendAndRecevied.vue"}, {"text": "抓包回放", "file": "views\\index\\networkSendAndRecevied.vue"}, {"text": "应用发包", "file": "views\\index\\networkSendAndRecevied.vue"}, {"text": "发包用例", "file": "views\\index\\networkSendAndRecevied.vue"}]}, "views\\index\\noticeManagerList.vue": {"count": 9, "texts": [{"text": "告警人员管理", "file": "views\\index\\noticeManagerList.vue"}, {"text": "告警人员", "file": "views\\index\\noticeManagerList.vue"}, {"text": "干系人名称", "file": "views\\index\\noticeManagerList.vue"}, {"text": "手机号", "file": "views\\index\\noticeManagerList.vue"}, {"text": "邮箱", "file": "views\\index\\noticeManagerList.vue"}, {"text": "通知干系人", "file": "views\\index\\noticeManagerList.vue"}, {"text": "修改", "file": "views\\index\\noticeManagerList.vue"}, {"text": "您确定删除名为\"${param.addresseeName}\"的干系人信息？", "file": "views\\index\\noticeManagerList.vue"}, {"text": "添加", "file": "views\\index\\noticeManagerList.vue"}]}, "views\\index\\productDataStorage.vue": {"count": 15, "texts": [{"text": "监控数据管理", "file": "views\\index\\productDataStorage.vue"}, {"text": "索引名", "file": "views\\index\\productDataStorage.vue"}, {"text": "主分片数", "file": "views\\index\\productDataStorage.vue"}, {"text": "从分片数", "file": "views\\index\\productDataStorage.vue"}, {"text": "文档条数", "file": "views\\index\\productDataStorage.vue"}, {"text": "空间占用", "file": "views\\index\\productDataStorage.vue"}, {"text": "委托交易时延", "file": "views\\index\\productDataStorage.vue"}, {"text": "委托查询时延数据", "file": "views\\index\\productDataStorage.vue"}, {"text": "时延跨度", "file": "views\\index\\productDataStorage.vue"}, {"text": "监控指标", "file": "views\\index\\productDataStorage.vue"}, {"text": "您确定清空名为\"${params.row.index}\"的索引数据吗？", "file": "views\\index\\productDataStorage.vue"}, {"text": "您确定删除名为\"${params.row.index}\"的索引数据吗？", "file": "views\\index\\productDataStorage.vue"}, {"text": "已连接ElasticSearch: {{ esUrl }}", "file": "views\\index\\productDataStorage.vue"}, {"text": "按条件清理", "file": "views\\index\\productDataStorage.vue"}, {"text": "定时清理", "file": "views\\index\\productDataStorage.vue"}]}, "views\\index\\productServiceConfig\\productServiceList.vue": {"count": 3, "texts": [{"text": "产品服务配置", "file": "views\\index\\productServiceConfig\\productServiceList.vue"}, {"text": ">产品服务列表</div>\r\n                    <h-menu v-if=", "file": "views\\index\\productServiceConfig\\productServiceList.vue"}, {"text": "产品服务列表", "file": "views\\index\\productServiceConfig\\productServiceList.vue"}]}, "views\\index\\productTimeDetail.vue": {"count": 16, "texts": [{"text": "交易节点委托时延明细", "file": "views\\index\\productTimeDetail.vue"}, {"text": ">上一条</a-button>\r\n        <a-button type=", "file": "views\\index\\productTimeDetail.vue"}, {"text": "链路类型", "file": "views\\index\\productTimeDetail.vue"}, {"text": "查询日期", "file": "views\\index\\productTimeDetail.vue"}, {"text": "查询时间", "file": "views\\index\\productTimeDetail.vue"}, {"text": "时延筛选", "file": "views\\index\\productTimeDetail.vue"}, {"text": "查询结果", "file": "views\\index\\productTimeDetail.vue"}, {"text": "委托时间", "file": "views\\index\\productTimeDetail.vue"}, {"text": "获取快捷查询失败!", "file": "views\\index\\productTimeDetail.vue"}, {"text": "保存快捷查询失败!", "file": "views\\index\\productTimeDetail.vue"}, {"text": "删除快捷查询失败!", "file": "views\\index\\productTimeDetail.vue"}, {"text": "当前数据为最后一条", "file": "views\\index\\productTimeDetail.vue"}, {"text": "您确定删除 '${name}'查询标签吗？", "file": "views\\index\\productTimeDetail.vue"}, {"text": "委托${data.entrustNo}全链路拓扑", "file": "views\\index\\productTimeDetail.vue"}, {"text": "上一条", "file": "views\\index\\productTimeDetail.vue"}, {"text": "下一条", "file": "views\\index\\productTimeDetail.vue"}]}, "views\\index\\productTimeSummary.vue": {"count": 5, "texts": [{"text": "链路时延汇总查询", "file": "views\\index\\productTimeSummary.vue"}, {"text": "平均数", "file": "views\\index\\productTimeSummary.vue"}, {"text": "周", "file": "views\\index\\productTimeSummary.vue"}, {"text": "月", "file": "views\\index\\productTimeSummary.vue"}, {"text": "总委托数", "file": "views\\index\\productTimeSummary.vue"}]}, "views\\index\\rcmBacklogMonitor.vue": {"count": 3, "texts": [{"text": "上下文积压监控", "file": "views\\index\\rcmBacklogMonitor.vue"}, {"text": "RCM配置列表", "file": "views\\index\\rcmBacklogMonitor.vue"}, {"text": ", // rcm文档id\r\n            productInstNo:", "file": "views\\index\\rcmBacklogMonitor.vue"}]}, "views\\index\\rcmDeploy.vue": {"count": 4, "texts": [{"text": "RCM配置管理", "file": "views\\index\\rcmDeploy.vue"}, {"text": "删除配置", "file": "views\\index\\rcmDeploy.vue"}, {"text": "请查看待发布配置和已发布配置告警提示", "file": "views\\index\\rcmDeploy.vue"}, {"text": "您确定删除名为\"${this.instanceData.name}\"的节点实例？", "file": "views\\index\\rcmDeploy.vue"}]}, "views\\index\\rcmObservation.vue": {"count": 5, "texts": [{"text": "上下文运行观测", "file": "views\\index\\rcmObservation.vue"}, {"text": "搜索上下文", "file": "views\\index\\rcmObservation.vue"}, {"text": "请先去建立产品", "file": "views\\index\\rcmObservation.vue"}, {"text": "复制成功", "file": "views\\index\\rcmObservation.vue"}, {"text": "复制", "file": "views\\index\\rcmObservation.vue"}]}, "views\\index\\secondAppearance\\index.vue": {"count": 4, "texts": [{"text": ">\r\n        <!-- 头部 -->\r\n        <div class=", "file": "views\\index\\secondAppearance\\index.vue"}, {"text": "按SQL条件", "file": "views\\index\\secondAppearance\\index.vue"}, {"text": "按资金账户", "file": "views\\index\\secondAppearance\\index.vue"}, {"text": "上场记录", "file": "views\\index\\secondAppearance\\index.vue"}]}, "views\\index\\secondAppearance\\publishStatusDetail.vue": {"count": 12, "texts": [{"text": ">当前无上场任务执行</span>\r\n            <span class=", "file": "views\\index\\secondAppearance\\publishStatusDetail.vue"}, {"text": ">详情</h-button>\r\n            <div slot=", "file": "views\\index\\secondAppearance\\publishStatusDetail.vue"}, {"text": ">上场信息</div>\r\n                    <div class=", "file": "views\\index\\secondAppearance\\publishStatusDetail.vue"}, {"text": "${taskDetail.title}上场", "file": "views\\index\\secondAppearance\\publishStatusDetail.vue"}, {"text": "{{taskDetail.title || '-'}}执行上场中", "file": "views\\index\\secondAppearance\\publishStatusDetail.vue"}, {"text": "当前无上场任务执行", "file": "views\\index\\secondAppearance\\publishStatusDetail.vue"}, {"text": "上场信息", "file": "views\\index\\secondAppearance\\publishStatusDetail.vue"}, {"text": "开始时间:", "file": "views\\index\\secondAppearance\\publishStatusDetail.vue"}, {"text": "结束时间:", "file": "views\\index\\secondAppearance\\publishStatusDetail.vue"}, {"text": "需上场表数量:", "file": "views\\index\\secondAppearance\\publishStatusDetail.vue"}, {"text": "上场成功表数量:", "file": "views\\index\\secondAppearance\\publishStatusDetail.vue"}, {"text": "上场失败表数量:", "file": "views\\index\\secondAppearance\\publishStatusDetail.vue"}]}, "views\\index\\smsList.vue": {"count": 38, "texts": [{"text": "告警通知管理", "file": "views\\index\\smsList.vue"}, {"text": "告警通知参数", "file": "views\\index\\smsList.vue"}, {"text": "通知状态", "file": "views\\index\\smsList.vue"}, {"text": "通知内容", "file": "views\\index\\smsList.vue"}, {"text": "告警通知", "file": "views\\index\\smsList.vue"}, {"text": "告警通知时间", "file": "views\\index\\smsList.vue"}, {"text": "匹配通知模板", "file": "views\\index\\smsList.vue"}, {"text": "人", "file": "views\\index\\smsList.vue"}, {"text": "自动通知", "file": "views\\index\\smsList.vue"}, {"text": "开启", "file": "views\\index\\smsList.vue"}, {"text": "匹配状态", "file": "views\\index\\smsList.vue"}, {"text": "成功", "file": "views\\index\\smsList.vue"}, {"text": "失败", "file": "views\\index\\smsList.vue"}, {"text": "已通知", "file": "views\\index\\smsList.vue"}, {"text": "未通知", "file": "views\\index\\smsList.vue"}, {"text": "通知时间", "file": "views\\index\\smsList.vue"}, {"text": "通知模板", "file": "views\\index\\smsList.vue"}, {"text": "请选择通知模板", "file": "views\\index\\smsList.vue"}, {"text": "全部", "file": "views\\index\\smsList.vue"}, {"text": "匹配", "file": "views\\index\\smsList.vue"}, {"text": "未匹配", "file": "views\\index\\smsList.vue"}, {"text": "请选择匹配状态", "file": "views\\index\\smsList.vue"}, {"text": "请选择通知状态", "file": "views\\index\\smsList.vue"}, {"text": "通知外发历史", "file": "views\\index\\smsList.vue"}, {"text": "创建时间", "file": "views\\index\\smsList.vue"}, {"text": "通知方式", "file": "views\\index\\smsList.vue"}, {"text": "短信", "file": "views\\index\\smsList.vue"}, {"text": "电子邮件", "file": "views\\index\\smsList.vue"}, {"text": "通知地址", "file": "views\\index\\smsList.vue"}, {"text": "通知提供方", "file": "views\\index\\smsList.vue"}, {"text": "干系人", "file": "views\\index\\smsList.vue"}, {"text": "请选择通知方式", "file": "views\\index\\smsList.vue"}, {"text": "请输入通知内容", "file": "views\\index\\smsList.vue"}, {"text": "发送时间", "file": "views\\index\\smsList.vue"}, {"text": "发送成功", "file": "views\\index\\smsList.vue"}, {"text": "发送失败", "file": "views\\index\\smsList.vue"}, {"text": "请输入通知地址", "file": "views\\index\\smsList.vue"}, {"text": "管理通知模版", "file": "views\\index\\smsList.vue"}]}, "views\\index\\sqlCores.vue": {"count": 15, "texts": [{"text": ">超时:</span>\r\n                        <span slot=", "file": "views\\index\\sqlCores.vue"}, {"text": "帮助手册", "file": "views\\index\\sqlCores.vue"}, {"text": "标签名不得为空", "file": "views\\index\\sqlCores.vue"}, {"text": "当前标签名已存在", "file": "views\\index\\sqlCores.vue"}, {"text": "请输入标签名", "file": "views\\index\\sqlCores.vue"}, {"text": "编辑器内容超过1M不支持暂存!", "file": "views\\index\\sqlCores.vue"}, {"text": "请选择要运行的节点！", "file": "views\\index\\sqlCores.vue"}, {"text": "SQL输入不能为空！", "file": "views\\index\\sqlCores.vue"}, {"text": "请选择要执行的sql语句，不得超过1条！", "file": "views\\index\\sqlCores.vue"}, {"text": "超时时间不能配置为空或0！", "file": "views\\index\\sqlCores.vue"}, {"text": "超时时间不得超过10分钟！", "file": "views\\index\\sqlCores.vue"}, {"text": "该页数据不存在，请重新执行", "file": "views\\index\\sqlCores.vue"}, {"text": "执行", "file": "views\\index\\sqlCores.vue"}, {"text": "历史SQL", "file": "views\\index\\sqlCores.vue"}, {"text": "超时:", "file": "views\\index\\sqlCores.vue"}]}, "views\\index\\sqlTable.vue": {"count": 23, "texts": [{"text": "输入表名过滤", "file": "views\\index\\sqlTable.vue"}, {"text": ">仅显示", "file": "views\\index\\sqlTable.vue"}, {"text": "索引", "file": "views\\index\\sqlTable.vue"}, {"text": "关联关系", "file": "views\\index\\sqlTable.vue"}, {"text": "使用说明", "file": "views\\index\\sqlTable.vue"}, {"text": "索引类型", "file": "views\\index\\sqlTable.vue"}, {"text": "索引字段", "file": "views\\index\\sqlTable.vue"}, {"text": "关联对象身份", "file": "views\\index\\sqlTable.vue"}, {"text": "关联类型", "file": "views\\index\\sqlTable.vue"}, {"text": "包含", "file": "views\\index\\sqlTable.vue"}, {"text": "关联", "file": "views\\index\\sqlTable.vue"}, {"text": "关联字段", "file": "views\\index\\sqlTable.vue"}, {"text": "关联索引", "file": "views\\index\\sqlTable.vue"}, {"text": "当前服务在此路由策略下无可执行节点，请重新选择", "file": "views\\index\\sqlTable.vue"}, {"text": "此分片表在以下主节点中存在", "file": "views\\index\\sqlTable.vue"}, {"text": "当前模式下无可执行的核心节点，请调整路由策略重试", "file": "views\\index\\sqlTable.vue"}, {"text": "结果", "file": "views\\index\\sqlTable.vue"}, {"text": "仅表示行数据对应的SQL执行的节点，与执行结果无关", "file": "views\\index\\sqlTable.vue"}, {"text": "终止", "file": "views\\index\\sqlTable.vue"}, {"text": "SQL执行异常", "file": "views\\index\\sqlTable.vue"}, {"text": "'SQL：${param.sql} 执行异常，您确定要继续执行剩余SQL吗？", "file": "views\\index\\sqlTable.vue"}, {"text": "仅显示\"不支持主备同步\"表", "file": "views\\index\\sqlTable.vue"}, {"text": "导入SQL", "file": "views\\index\\sqlTable.vue"}]}, "views\\index\\threadInfoOverview.vue": {"count": 1, "texts": [{"text": "应用检查配置", "file": "views\\index\\threadInfoOverview.vue"}]}, "views\\index\\topoMonitor.vue": {"count": 3, "texts": [{"text": "当前产品不支持拓扑展示", "file": "views\\index\\topoMonitor.vue"}, {"text": "应用拓扑结构", "file": "views\\index\\topoMonitor.vue"}, {"text": "RCM拓扑结构", "file": "views\\index\\topoMonitor.vue"}]}, "views\\index\\transaction.vue": {"count": 10, "texts": [{"text": "选择产品", "file": "views\\index\\transaction.vue"}, {"text": ";// x轴的名称\r\n\r\n                    htmlStr +=", "file": "views\\index\\transaction.vue"}, {"text": "查询订单不存在", "file": "views\\index\\transaction.vue"}, {"text": "获取同比分析数据失败！", "file": "views\\index\\transaction.vue"}, {"text": "自由分析", "file": "views\\index\\transaction.vue"}, {"text": "应用时延分析", "file": "views\\index\\transaction.vue"}, {"text": "报表${res.data.instanceName}创建成功！", "file": "views\\index\\transaction.vue"}, {"text": "查询", "file": "views\\index\\transaction.vue"}, {"text": "创建分析报表", "file": "views\\index\\transaction.vue"}, {"text": "';// x轴的名称\r\n\r\n                    htmlStr += '", "file": "views\\index\\transaction.vue"}]}, "views\\index\\tripartiteServiceConfig\\tripartiteServiceList.vue": {"count": 3, "texts": [{"text": "三方服务集成管理", "file": "views\\index\\tripartiteServiceConfig\\tripartiteServiceList.vue"}, {"text": ">三方服务列表</div>\r\n                    <h-menu v-if=", "file": "views\\index\\tripartiteServiceConfig\\tripartiteServiceList.vue"}, {"text": "三方服务列表", "file": "views\\index\\tripartiteServiceConfig\\tripartiteServiceList.vue"}]}, "views\\index\\ustTableVerification.vue": {"count": 2, "texts": [{"text": "数据校验", "file": "views\\index\\ustTableVerification.vue"}, {"text": "创建数据校验任务", "file": "views\\index\\ustTableVerification.vue"}]}, "views\\testLXY.vue": {"count": 2, "texts": [{"text": "用户信息表", "file": "views\\testLXY.vue"}, {"text": "订单详情表", "file": "views\\testLXY.vue"}]}, "components\\accordObservation\\dataAccordDrawer.vue": {"count": 7, "texts": [{"text": "主备核心事务差量", "file": "components\\accordObservation\\dataAccordDrawer.vue"}, {"text": "回库事务差量", "file": "components\\accordObservation\\dataAccordDrawer.vue"}, {"text": "角色", "file": "components\\accordObservation\\dataAccordDrawer.vue"}, {"text": "事务号", "file": "components\\accordObservation\\dataAccordDrawer.vue"}, {"text": "与主核心事务差量", "file": "components\\accordObservation\\dataAccordDrawer.vue"}, {"text": "事务量差(个)", "file": "components\\accordObservation\\dataAccordDrawer.vue"}, {"text": "{{ dataTitle }}变化趋势", "file": "components\\accordObservation\\dataAccordDrawer.vue"}]}, "components\\accordObservation\\dataAccordObservation.vue": {"count": 3, "texts": [{"text": "备核心事务最大差量", "file": "components\\accordObservation\\dataAccordObservation.vue"}, {"text": "回库事务最大差量", "file": "components\\accordObservation\\dataAccordObservation.vue"}, {"text": "分片 ${indicator.shardingNo || '-'}", "file": "components\\accordObservation\\dataAccordObservation.vue"}]}, "components\\analyse\\addCaseModal.vue": {"count": 7, "texts": [{"text": "新建测试用例", "file": "components\\analyse\\addCaseModal.vue"}, {"text": "用例名称", "file": "components\\analyse\\addCaseModal.vue"}, {"text": "请输入测试用例名称", "file": "components\\analyse\\addCaseModal.vue"}, {"text": ">取消</a-button>\r\n                <a-button type=", "file": "components\\analyse\\addCaseModal.vue"}, {"text": "字符长度数不得超过20！", "file": "components\\analyse\\addCaseModal.vue"}, {"text": "用例创建成功!", "file": "components\\analyse\\addCaseModal.vue"}, {"text": "用例创建失败!", "file": "components\\analyse\\addCaseModal.vue"}]}, "components\\analyse\\addSceneModal.vue": {"count": 5, "texts": [{"text": "场景名称", "file": "components\\analyse\\addSceneModal.vue"}, {"text": "请输入场景名称", "file": "components\\analyse\\addSceneModal.vue"}, {"text": "场景创建成功!", "file": "components\\analyse\\addSceneModal.vue"}, {"text": "场景创建失败!", "file": "components\\analyse\\addSceneModal.vue"}, {"text": "请确保当前场景已导出保存，新建的场景将覆盖当前使用的场景信息！", "file": "components\\analyse\\addSceneModal.vue"}]}, "components\\analyse\\caseAccountModal.vue": {"count": 13, "texts": [{"text": "账号配置", "file": "components\\analyse\\caseAccountModal.vue"}, {"text": "请输入起始账号", "file": "components\\analyse\\caseAccountModal.vue"}, {"text": "账号...", "file": "components\\analyse\\caseAccountModal.vue"}, {"text": "请确认输入内容为数字！", "file": "components\\analyse\\caseAccountModal.vue"}, {"text": "用例账号更新成功!", "file": "components\\analyse\\caseAccountModal.vue"}, {"text": "用例账号更新!", "file": "components\\analyse\\caseAccountModal.vue"}, {"text": "导入", "file": "components\\analyse\\caseAccountModal.vue"}, {"text": "起始账号", "file": "components\\analyse\\caseAccountModal.vue"}, {"text": "生成个数", "file": "components\\analyse\\caseAccountModal.vue"}, {"text": "自动生成", "file": "components\\analyse\\caseAccountModal.vue"}, {"text": "股东账号", "file": "components\\analyse\\caseAccountModal.vue"}, {"text": "上交所", "file": "components\\analyse\\caseAccountModal.vue"}, {"text": "深交所", "file": "components\\analyse\\caseAccountModal.vue"}]}, "components\\analyse\\caseConfigModal.vue": {"count": 3, "texts": [{"text": "配置用例", "file": "components\\analyse\\caseConfigModal.vue"}, {"text": "请输入用例配置...", "file": "components\\analyse\\caseConfigModal.vue"}, {"text": "用例配置保存失败!", "file": "components\\analyse\\caseConfigModal.vue"}]}, "components\\analyse\\caseRemarkModal.vue": {"count": 6, "texts": [{"text": "更新实例备注", "file": "components\\analyse\\caseRemarkModal.vue"}, {"text": "标题", "file": "components\\analyse\\caseRemarkModal.vue"}, {"text": "请输入标题名称", "file": "components\\analyse\\caseRemarkModal.vue"}, {"text": "内容", "file": "components\\analyse\\caseRemarkModal.vue"}, {"text": "备注更新成功!", "file": "components\\analyse\\caseRemarkModal.vue"}, {"text": "备注更新失败!", "file": "components\\analyse\\caseRemarkModal.vue"}]}, "components\\analyse\\eventListModal.vue": {"count": 6, "texts": [{"text": "事件列表", "file": "components\\analyse\\eventListModal.vue"}, {"text": "事件详情", "file": "components\\analyse\\eventListModal.vue"}, {"text": "提示内容", "file": "components\\analyse\\eventListModal.vue"}, {"text": "复制id", "file": "components\\analyse\\eventListModal.vue"}, {"text": "该浏览器不支持复制", "file": "components\\analyse\\eventListModal.vue"}, {"text": "{{ copied ? '复制成功' : '复制id' }}", "file": "components\\analyse\\eventListModal.vue"}]}, "components\\analyse\\importAccountModal.vue": {"count": 2, "texts": [{"text": "导入账号信息", "file": "components\\analyse\\importAccountModal.vue"}, {"text": "点击或将文件拖拽到这里上传", "file": "components\\analyse\\importAccountModal.vue"}]}, "components\\analyse\\importFileModal.vue": {"count": 2, "texts": [{"text": "导入场景配置文件", "file": "components\\analyse\\importFileModal.vue"}, {"text": "请确保当前场景已导出保存，新导入的场景将覆盖当前使用的场景信息！", "file": "components\\analyse\\importFileModal.vue"}]}, "components\\analyse\\reportConfigModal.vue": {"count": 7, "texts": [{"text": "委托回路", "file": "components\\analyse\\reportConfigModal.vue"}, {"text": "可视跨度", "file": "components\\analyse\\reportConfigModal.vue"}, {"text": "可视指标", "file": "components\\analyse\\reportConfigModal.vue"}, {"text": "度量单位", "file": "components\\analyse\\reportConfigModal.vue"}, {"text": "纳秒（ns）", "file": "components\\analyse\\reportConfigModal.vue"}, {"text": "微秒（μs）", "file": "components\\analyse\\reportConfigModal.vue"}, {"text": "毫秒（ms）", "file": "components\\analyse\\reportConfigModal.vue"}]}, "components\\analyse\\reportContrastModal.vue": {"count": 2, "texts": [{"text": "报表对比", "file": "components\\analyse\\reportContrastModal.vue"}, {"text": "选择对比实例", "file": "components\\analyse\\reportContrastModal.vue"}]}, "components\\analyse\\secInstanceListModal.vue": {"count": 5, "texts": [{"text": "选择测试实例", "file": "components\\analyse\\secInstanceListModal.vue"}, {"text": "搜索测试报告...", "file": "components\\analyse\\secInstanceListModal.vue"}, {"text": "实例名称", "file": "components\\analyse\\secInstanceListModal.vue"}, {"text": "测试日期", "file": "components\\analyse\\secInstanceListModal.vue"}, {"text": "请选择实例！", "file": "components\\analyse\\secInstanceListModal.vue"}]}, "components\\analyse\\updateCaseNameModal.vue": {"count": 5, "texts": [{"text": "更新测试实例名称", "file": "components\\analyse\\updateCaseNameModal.vue"}, {"text": "请输入测试实例名称", "file": "components\\analyse\\updateCaseNameModal.vue"}, {"text": "字符长度数不得超过30！", "file": "components\\analyse\\updateCaseNameModal.vue"}, {"text": "实例名称更新成功!", "file": "components\\analyse\\updateCaseNameModal.vue"}, {"text": "实例更新失败!", "file": "components\\analyse\\updateCaseNameModal.vue"}]}, "components\\analyseConfig\\analyseCaseInstance.vue": {"count": 18, "texts": [{"text": ">填写备注</a-button>\r\n            <h-row class=", "file": "components\\analyseConfig\\analyseCaseInstance.vue"}, {"text": "空", "file": "components\\analyseConfig\\analyseCaseInstance.vue"}, {"text": "最大时延", "file": "components\\analyseConfig\\analyseCaseInstance.vue"}, {"text": "最小时延", "file": "components\\analyseConfig\\analyseCaseInstance.vue"}, {"text": "平均时延", "file": "components\\analyseConfig\\analyseCaseInstance.vue"}, {"text": "中位数时延", "file": "components\\analyseConfig\\analyseCaseInstance.vue"}, {"text": "99分位时延", "file": "components\\analyseConfig\\analyseCaseInstance.vue"}, {"text": "查询实例列表失败!", "file": "components\\analyseConfig\\analyseCaseInstance.vue"}, {"text": ",\r\n                content: `您确定删除", "file": "components\\analyseConfig\\analyseCaseInstance.vue"}, {"text": "您确定要跳转到该实例详情分析页面吗？", "file": "components\\analyseConfig\\analyseCaseInstance.vue"}, {"text": "您确定停止当前测试实例？", "file": "components\\analyseConfig\\analyseCaseInstance.vue"}, {"text": "测试实例停止成功!", "file": "components\\analyseConfig\\analyseCaseInstance.vue"}, {"text": "99分位数", "file": "components\\analyseConfig\\analyseCaseInstance.vue"}, {"text": "测试实例名", "file": "components\\analyseConfig\\analyseCaseInstance.vue"}, {"text": "事件信息", "file": "components\\analyseConfig\\analyseCaseInstance.vue"}, {"text": "停止测试", "file": "components\\analyseConfig\\analyseCaseInstance.vue"}, {"text": "填写备注", "file": "components\\analyseConfig\\analyseCaseInstance.vue"}, {"text": "{{ (tabData.reportStatusDesc) || '暂无' }}", "file": "components\\analyseConfig\\analyseCaseInstance.vue"}]}, "components\\analyseConfig\\analyseConfigShell.vue": {"count": 29, "texts": [{"text": "发单工具", "file": "components\\analyseConfig\\analyseConfigShell.vue"}, {"text": "请输入发单工具", "file": "components\\analyseConfig\\analyseConfigShell.vue"}, {"text": "部署到", "file": "components\\analyseConfig\\analyseConfigShell.vue"}, {"text": "请输入部署地址", "file": "components\\analyseConfig\\analyseConfigShell.vue"}, {"text": "部署实例数", "file": "components\\analyseConfig\\analyseConfigShell.vue"}, {"text": "请输入部署示例数", "file": "components\\analyseConfig\\analyseConfigShell.vue"}, {"text": "应用节点", "file": "components\\analyseConfig\\analyseConfigShell.vue"}, {"text": "接入IP", "file": "components\\analyseConfig\\analyseConfigShell.vue"}, {"text": "接入端口", "file": "components\\analyseConfig\\analyseConfigShell.vue"}, {"text": "测试用例", "file": "components\\analyseConfig\\analyseConfigShell.vue"}, {"text": "命令行参数", "file": "components\\analyseConfig\\analyseConfigShell.vue"}, {"text": "请输入执行命令", "file": "components\\analyseConfig\\analyseConfigShell.vue"}, {"text": "请输入1-10正整数", "file": "components\\analyseConfig\\analyseConfigShell.vue"}, {"text": "请输入1到65535之间的端口号", "file": "components\\analyseConfig\\analyseConfigShell.vue"}, {"text": ",            // 业务类型\r\n                    ip:", "file": "components\\analyseConfig\\analyseConfigShell.vue"}, {"text": "未查询到场景信息!", "file": "components\\analyseConfig\\analyseConfigShell.vue"}, {"text": "查询场景信息异常!", "file": "components\\analyseConfig\\analyseConfigShell.vue"}, {"text": "查询场景信息失败!", "file": "components\\analyseConfig\\analyseConfigShell.vue"}, {"text": "测试执行成功!", "file": "components\\analyseConfig\\analyseConfigShell.vue"}, {"text": "确定对${productName}-${testCaseName}执行测试？", "file": "components\\analyseConfig\\analyseConfigShell.vue"}, {"text": "选择发单工具", "file": "components\\analyseConfig\\analyseConfigShell.vue"}, {"text": "连接目标LDP业务系统", "file": "components\\analyseConfig\\analyseConfigShell.vue"}, {"text": "选择测试用例", "file": "components\\analyseConfig\\analyseConfigShell.vue"}, {"text": "新建用例", "file": "components\\analyseConfig\\analyseConfigShell.vue"}, {"text": "删除用例", "file": "components\\analyseConfig\\analyseConfigShell.vue"}, {"text": "配置多账号", "file": "components\\analyseConfig\\analyseConfigShell.vue"}, {"text": "④&nbsp;&nbsp;&nbsp;执行测试", "file": "components\\analyseConfig\\analyseConfigShell.vue"}, {"text": "打印时延日志", "file": "components\\analyseConfig\\analyseConfigShell.vue"}, {"text": "执行测试", "file": "components\\analyseConfig\\analyseConfigShell.vue"}]}, "components\\appBindingCore\\AppBindingCore.vue": {"count": 15, "texts": [{"text": "筛选条件", "file": "components\\appBindingCore\\AppBindingCore.vue"}, {"text": "线程信息", "file": "components\\appBindingCore\\AppBindingCore.vue"}, {"text": "管理IP", "file": "components\\appBindingCore\\AppBindingCore.vue"}, {"text": "请选择管理IP", "file": "components\\appBindingCore\\AppBindingCore.vue"}, {"text": "应用节点名", "file": "components\\appBindingCore\\AppBindingCore.vue"}, {"text": "请选择应用节点名", "file": "components\\appBindingCore\\AppBindingCore.vue"}, {"text": "线程拥有者", "file": "components\\appBindingCore\\AppBindingCore.vue"}, {"text": "请输入线程拥有者", "file": "components\\appBindingCore\\AppBindingCore.vue"}, {"text": "是否绑核", "file": "components\\appBindingCore\\AppBindingCore.vue"}, {"text": "请选择绑核", "file": "components\\appBindingCore\\AppBindingCore.vue"}, {"text": "是", "file": "components\\appBindingCore\\AppBindingCore.vue"}, {"text": "否", "file": "components\\appBindingCore\\AppBindingCore.vue"}, {"text": "线程名", "file": "components\\appBindingCore\\AppBindingCore.vue"}, {"text": "线程类型", "file": "components\\appBindingCore\\AppBindingCore.vue"}, {"text": "绑核", "file": "components\\appBindingCore\\AppBindingCore.vue"}]}, "components\\coreReplayObservation\\coreReplayDetail.vue": {"count": 54, "texts": [{"text": ">开始时间：{{ startTime ||", "file": "components\\coreReplayObservation\\coreReplayDetail.vue"}, {"text": ">\r\n                    当前交易日重演完成时，将展示此交易日中", "file": "components\\coreReplayObservation\\coreReplayDetail.vue"}, {"text": ">\r\n                    当前交易日消息收发的实时状态。应答比对：当前交易日", "file": "components\\coreReplayObservation\\coreReplayDetail.vue"}, {"text": "当前交易日重演尚未开始，请稍候", "file": "components\\coreReplayObservation\\coreReplayDetail.vue"}, {"text": "}；  重演交易日文件目录：${replayFilePath ||", "file": "components\\coreReplayObservation\\coreReplayDetail.vue"}, {"text": "重演结果", "file": "components\\coreReplayObservation\\coreReplayDetail.vue"}, {"text": "相同表数量", "file": "components\\coreReplayObservation\\coreReplayDetail.vue"}, {"text": "不同表数量", "file": "components\\coreReplayObservation\\coreReplayDetail.vue"}, {"text": "应答一致", "file": "components\\coreReplayObservation\\coreReplayDetail.vue"}, {"text": "应答不一致", "file": "components\\coreReplayObservation\\coreReplayDetail.vue"}, {"text": "消息收发", "file": "components\\coreReplayObservation\\coreReplayDetail.vue"}, {"text": "应答比对进度(%)", "file": "components\\coreReplayObservation\\coreReplayDetail.vue"}, {"text": "需比对：", "file": "components\\coreReplayObservation\\coreReplayDetail.vue"}, {"text": "已比对：", "file": "components\\coreReplayObservation\\coreReplayDetail.vue"}, {"text": "发送进度(%)", "file": "components\\coreReplayObservation\\coreReplayDetail.vue"}, {"text": "需发送：", "file": "components\\coreReplayObservation\\coreReplayDetail.vue"}, {"text": "已发送：", "file": "components\\coreReplayObservation\\coreReplayDetail.vue"}, {"text": "应答进度(%)", "file": "components\\coreReplayObservation\\coreReplayDetail.vue"}, {"text": "需应答：", "file": "components\\coreReplayObservation\\coreReplayDetail.vue"}, {"text": "已应答：", "file": "components\\coreReplayObservation\\coreReplayDetail.vue"}, {"text": "重演工具监控", "file": "components\\coreReplayObservation\\coreReplayDetail.vue"}, {"text": "发送吞吐(tps)", "file": "components\\coreReplayObservation\\coreReplayDetail.vue"}, {"text": "应答吞吐(tps)", "file": "components\\coreReplayObservation\\coreReplayDetail.vue"}, {"text": "应答比对吞吐(tps)", "file": "components\\coreReplayObservation\\coreReplayDetail.vue"}, {"text": "内存(%)", "file": "components\\coreReplayObservation\\coreReplayDetail.vue"}, {"text": "仅显示应答不一致", "file": "components\\coreReplayObservation\\coreReplayDetail.vue"}, {"text": "分片号", "file": "components\\coreReplayObservation\\coreReplayDetail.vue"}, {"text": "集群名", "file": "components\\coreReplayObservation\\coreReplayDetail.vue"}, {"text": "发送进度", "file": "components\\coreReplayObservation\\coreReplayDetail.vue"}, {"text": "应答进度", "file": "components\\coreReplayObservation\\coreReplayDetail.vue"}, {"text": "应答比对进度", "file": "components\\coreReplayObservation\\coreReplayDetail.vue"}, {"text": "应答不一致数", "file": "components\\coreReplayObservation\\coreReplayDetail.vue"}, {"text": "数据比对进度", "file": "components\\coreReplayObservation\\coreReplayDetail.vue"}, {"text": "数据不一致数", "file": "components\\coreReplayObservation\\coreReplayDetail.vue"}, {"text": ", // 重演任务状态\r\n            dateStatus:", "file": "components\\coreReplayObservation\\coreReplayDetail.vue"}, {"text": ", // 重演日期状态\r\n            startTime:", "file": "components\\coreReplayObservation\\coreReplayDetail.vue"}, {"text": ", // 开始时间\r\n            replayFilePath:", "file": "components\\coreReplayObservation\\coreReplayDetail.vue"}, {"text": "终止成功", "file": "components\\coreReplayObservation\\coreReplayDetail.vue"}, {"text": "暂停成功", "file": "components\\coreReplayObservation\\coreReplayDetail.vue"}, {"text": "恢复成功", "file": "components\\coreReplayObservation\\coreReplayDetail.vue"}, {"text": "具体任务细节", "file": "components\\coreReplayObservation\\coreReplayDetail.vue"}, {"text": "开始时间：{{ startTime || \"-\" }}", "file": "components\\coreReplayObservation\\coreReplayDetail.vue"}, {"text": "正在加载", "file": "components\\coreReplayObservation\\coreReplayDetail.vue"}, {"text": "数据更新中", "file": "components\\coreReplayObservation\\coreReplayDetail.vue"}, {"text": "{{ autoUpdateTime }}s 数据更新", "file": "components\\coreReplayObservation\\coreReplayDetail.vue"}, {"text": "启动", "file": "components\\coreReplayObservation\\coreReplayDetail.vue"}, {"text": "恢复", "file": "components\\coreReplayObservation\\coreReplayDetail.vue"}, {"text": "暂停", "file": "components\\coreReplayObservation\\coreReplayDetail.vue"}, {"text": "当前交易日重演完成时，将展示此交易日中\"重演\"与\"持久化\"中相同/不同表数量、应答的一致/不一致数量", "file": "components\\coreReplayObservation\\coreReplayDetail.vue"}, {"text": "当前交易日消息收发的实时状态。应答比对：当前交易日\"重演的应答\" 与 \"生产已记录的应答\"比对。", "file": "components\\coreReplayObservation\\coreReplayDetail.vue"}, {"text": "当前交易日中，重演工具本身的发送和应答速度及资源使用情况", "file": "components\\coreReplayObservation\\coreReplayDetail.vue"}, {"text": "（ 核心总数：{{ instanceTotalCount }}", "file": "components\\coreReplayObservation\\coreReplayDetail.vue"}, {"text": "已完成重演：{{ instanceCompletedCount }} ）", "file": "components\\coreReplayObservation\\coreReplayDetail.vue"}, {"text": "错误原因", "file": "components\\coreReplayObservation\\coreReplayDetail.vue"}]}, "components\\coreReplayObservation\\coreReplayTaskList.vue": {"count": 27, "texts": [{"text": "核心数据重演", "file": "components\\coreReplayObservation\\coreReplayTaskList.vue"}, {"text": "更多条件", "file": "components\\coreReplayObservation\\coreReplayTaskList.vue"}, {"text": ">重演任务列表</div>\r\n          <div class=", "file": "components\\coreReplayObservation\\coreReplayTaskList.vue"}, {"text": "执行结果", "file": "components\\coreReplayObservation\\coreReplayTaskList.vue"}, {"text": ">一致</h-option>\r\n            <h-option key=", "file": "components\\coreReplayObservation\\coreReplayTaskList.vue"}, {"text": "选择交易日", "file": "components\\coreReplayObservation\\coreReplayTaskList.vue"}, {"text": "输入任务名称", "file": "components\\coreReplayObservation\\coreReplayTaskList.vue"}, {"text": "任务来源", "file": "components\\coreReplayObservation\\coreReplayTaskList.vue"}, {"text": "执行开始时间", "file": "components\\coreReplayObservation\\coreReplayTaskList.vue"}, {"text": "请选择时间范围", "file": "components\\coreReplayObservation\\coreReplayTaskList.vue"}, {"text": "任务名称", "file": "components\\coreReplayObservation\\coreReplayTaskList.vue"}, {"text": "任务标识：", "file": "components\\coreReplayObservation\\coreReplayTaskList.vue"}, {"text": "执行状态", "file": "components\\coreReplayObservation\\coreReplayTaskList.vue"}, {"text": "执行结束时间", "file": "components\\coreReplayObservation\\coreReplayTaskList.vue"}, {"text": "(不一致数：", "file": "components\\coreReplayObservation\\coreReplayTaskList.vue"}, {"text": "不一致数", "file": "components\\coreReplayObservation\\coreReplayTaskList.vue"}, {"text": "修改成功", "file": "components\\coreReplayObservation\\coreReplayTaskList.vue"}, {"text": "启动成功", "file": "components\\coreReplayObservation\\coreReplayTaskList.vue"}, {"text": "重演任务同步成功！数量：${res?.data?.successCount || 0}", "file": "components\\coreReplayObservation\\coreReplayTaskList.vue"}, {"text": "确定要删除此任务？", "file": "components\\coreReplayObservation\\coreReplayTaskList.vue"}, {"text": "删除重演任务后，任务将从列表移除，同时任务所产生的数据将一并被删除。请谨慎操作。", "file": "components\\coreReplayObservation\\coreReplayTaskList.vue"}, {"text": "重演任务列表", "file": "components\\coreReplayObservation\\coreReplayTaskList.vue"}, {"text": "任务总数:", "file": "components\\coreReplayObservation\\coreReplayTaskList.vue"}, {"text": "创建", "file": "components\\coreReplayObservation\\coreReplayTaskList.vue"}, {"text": "同步任务", "file": "components\\coreReplayObservation\\coreReplayTaskList.vue"}, {"text": "一致", "file": "components\\coreReplayObservation\\coreReplayTaskList.vue"}, {"text": "不一致", "file": "components\\coreReplayObservation\\coreReplayTaskList.vue"}]}, "components\\coreReplayObservation\\createCoreReplayTask.vue": {"count": 25, "texts": [{"text": "原重演任务中的重演对象可能发生变化，请仔细核对。", "file": "components\\coreReplayObservation\\createCoreReplayTask.vue"}, {"text": "发送间隔", "file": "components\\coreReplayObservation\\createCoreReplayTask.vue"}, {"text": "重演内容", "file": "components\\coreReplayObservation\\createCoreReplayTask.vue"}, {"text": "任务参数", "file": "components\\coreReplayObservation\\createCoreReplayTask.vue"}, {"text": "复制重演任务", "file": "components\\coreReplayObservation\\createCoreReplayTask.vue"}, {"text": "创建重演任务", "file": "components\\coreReplayObservation\\createCoreReplayTask.vue"}, {"text": "确定重演对象", "file": "components\\coreReplayObservation\\createCoreReplayTask.vue"}, {"text": "配置任务参数", "file": "components\\coreReplayObservation\\createCoreReplayTask.vue"}, {"text": "信息核对", "file": "components\\coreReplayObservation\\createCoreReplayTask.vue"}, {"text": "按需选择需要重演“交易日”与对应的“核心”。", "file": "components\\coreReplayObservation\\createCoreReplayTask.vue"}, {"text": "基于前两个步骤的选择，将对应产生的重演任务，请仔细核对任务内容。", "file": "components\\coreReplayObservation\\createCoreReplayTask.vue"}, {"text": "请输入0~1000", "file": "components\\coreReplayObservation\\createCoreReplayTask.vue"}, {"text": "重演任务", "file": "components\\coreReplayObservation\\createCoreReplayTask.vue"}, {"text": ",\r\n            // 复制、创建\r\n            type:", "file": "components\\coreReplayObservation\\createCoreReplayTask.vue"}, {"text": "创建并启动成功", "file": "components\\coreReplayObservation\\createCoreReplayTask.vue"}, {"text": "请至少选择一个核心", "file": "components\\coreReplayObservation\\createCoreReplayTask.vue"}, {"text": "根据实际情况配置重演任务相关参数。", "file": "components\\coreReplayObservation\\createCoreReplayTask.vue"}, {"text": "确认离开页面？", "file": "components\\coreReplayObservation\\createCoreReplayTask.vue"}, {"text": "{{ type === 'copy'? '复制重演任务' : '创建重演任务' }}", "file": "components\\coreReplayObservation\\createCoreReplayTask.vue"}, {"text": "核心集群", "file": "components\\coreReplayObservation\\createCoreReplayTask.vue"}, {"text": "发送间隔:", "file": "components\\coreReplayObservation\\createCoreReplayTask.vue"}, {"text": "{{ formItems.sendIntervalMs }} 毫秒", "file": "components\\coreReplayObservation\\createCoreReplayTask.vue"}, {"text": "修改路径：", "file": "components\\coreReplayObservation\\createCoreReplayTask.vue"}, {"text": "下一步：{{ stepList[currentStep + 1] }}", "file": "components\\coreReplayObservation\\createCoreReplayTask.vue"}, {"text": "创建并启动任务", "file": "components\\coreReplayObservation\\createCoreReplayTask.vue"}]}, "components\\endpointConfig\\endpointConfig.vue": {"count": 25, "texts": [{"text": "添加网关节点", "file": "components\\endpointConfig\\endpointConfig.vue"}, {"text": "接入点名", "file": "components\\endpointConfig\\endpointConfig.vue"}, {"text": "接入应用节点", "file": "components\\endpointConfig\\endpointConfig.vue"}, {"text": "接入点IP", "file": "components\\endpointConfig\\endpointConfig.vue"}, {"text": "接入点端口", "file": "components\\endpointConfig\\endpointConfig.vue"}, {"text": "接入协议", "file": "components\\endpointConfig\\endpointConfig.vue"}, {"text": "字符集", "file": "components\\endpointConfig\\endpointConfig.vue"}, {"text": "批量配置", "file": "components\\endpointConfig\\endpointConfig.vue"}, {"text": "连通性测试", "file": "components\\endpointConfig\\endpointConfig.vue"}, {"text": "添加节点", "file": "components\\endpointConfig\\endpointConfig.vue"}, {"text": "应用节点类型", "file": "components\\endpointConfig\\endpointConfig.vue"}, {"text": "应用身份", "file": "components\\endpointConfig\\endpointConfig.vue"}, {"text": "管理端口", "file": "components\\endpointConfig\\endpointConfig.vue"}, {"text": "应用集群", "file": "components\\endpointConfig\\endpointConfig.vue"}, {"text": "系统号", "file": "components\\endpointConfig\\endpointConfig.vue"}, {"text": "支持抓包", "file": "components\\endpointConfig\\endpointConfig.vue"}, {"text": "支持发包", "file": "components\\endpointConfig\\endpointConfig.vue"}, {"text": "配置文件", "file": "components\\endpointConfig\\endpointConfig.vue"}, {"text": ", // 超出的文本隐藏", "file": "components\\endpointConfig\\endpointConfig.vue"}, {"text": ", // 溢出用省略号显示", "file": "components\\endpointConfig\\endpointConfig.vue"}, {"text": "开发平台", "file": "components\\endpointConfig\\endpointConfig.vue"}, {"text": "应用插件ID", "file": "components\\endpointConfig\\endpointConfig.vue"}, {"text": "接入网关配置", "file": "components\\endpointConfig\\endpointConfig.vue"}, {"text": "后端服务：${ACCESS_CONFIG_TITLE[this.endpointType]}", "file": "components\\endpointConfig\\endpointConfig.vue"}, {"text": "您确定删除名为\"${name}\"的节点吗？", "file": "components\\endpointConfig\\endpointConfig.vue"}]}, "components\\latencyTrendAnalysis\\settingModal.vue": {"count": 17, "texts": [{"text": "链路配置", "file": "components\\latencyTrendAnalysis\\settingModal.vue"}, {"text": ">分析目标</div>\r\n                <h-form-item label=", "file": "components\\latencyTrendAnalysis\\settingModal.vue"}, {"text": "请选择链路", "file": "components\\latencyTrendAnalysis\\settingModal.vue"}, {"text": "统计日期", "file": "components\\latencyTrendAnalysis\\settingModal.vue"}, {"text": "统计时间", "file": "components\\latencyTrendAnalysis\\settingModal.vue"}, {"text": "统计方式", "file": "components\\latencyTrendAnalysis\\settingModal.vue"}, {"text": "请选择统计方式", "file": "components\\latencyTrendAnalysis\\settingModal.vue"}, {"text": "统计口径", "file": "components\\latencyTrendAnalysis\\settingModal.vue"}, {"text": "请选择统计口径", "file": "components\\latencyTrendAnalysis\\settingModal.vue"}, {"text": "自定义范围不得超过18小时", "file": "components\\latencyTrendAnalysis\\settingModal.vue"}, {"text": "全部市场", "file": "components\\latencyTrendAnalysis\\settingModal.vue"}, {"text": "日盘", "file": "components\\latencyTrendAnalysis\\settingModal.vue"}, {"text": "早盘", "file": "components\\latencyTrendAnalysis\\settingModal.vue"}, {"text": "午盘", "file": "components\\latencyTrendAnalysis\\settingModal.vue"}, {"text": "分析目标", "file": "components\\latencyTrendAnalysis\\settingModal.vue"}, {"text": "数据范围", "file": "components\\latencyTrendAnalysis\\settingModal.vue"}, {"text": "数据过滤", "file": "components\\latencyTrendAnalysis\\settingModal.vue"}]}, "components\\ldpDataObservation\\nodeInfo.vue": {"count": 19, "texts": [{"text": "应用名称", "file": "components\\ldpDataObservation\\nodeInfo.vue"}, {"text": "应用版本", "file": "components\\ldpDataObservation\\nodeInfo.vue"}, {"text": "应用类型", "file": "components\\ldpDataObservation\\nodeInfo.vue"}, {"text": "应用开发平台", "file": "components\\ldpDataObservation\\nodeInfo.vue"}, {"text": "产品名称", "file": "components\\ldpDataObservation\\nodeInfo.vue"}, {"text": "应用简称", "file": "components\\ldpDataObservation\\nodeInfo.vue"}, {"text": "应用编号", "file": "components\\ldpDataObservation\\nodeInfo.vue"}, {"text": "应用实例", "file": "components\\ldpDataObservation\\nodeInfo.vue"}, {"text": "pid文件", "file": "components\\ldpDataObservation\\nodeInfo.vue"}, {"text": "插件名称", "file": "components\\ldpDataObservation\\nodeInfo.vue"}, {"text": "实例号", "file": "components\\ldpDataObservation\\nodeInfo.vue"}, {"text": "插件版本", "file": "components\\ldpDataObservation\\nodeInfo.vue"}, {"text": "发布日期", "file": "components\\ldpDataObservation\\nodeInfo.vue"}, {"text": "管理功能数量", "file": "components\\ldpDataObservation\\nodeInfo.vue"}, {"text": "管理功能列表", "file": "components\\ldpDataObservation\\nodeInfo.vue"}, {"text": "目录", "file": "components\\ldpDataObservation\\nodeInfo.vue"}, {"text": "目录地址", "file": "components\\ldpDataObservation\\nodeInfo.vue"}, {"text": "配置地址", "file": "components\\ldpDataObservation\\nodeInfo.vue"}, {"text": "zk配置地址", "file": "components\\ldpDataObservation\\nodeInfo.vue"}]}, "components\\ldpLinkConfig\\appClusterConfig.vue": {"count": 9, "texts": [{"text": "仲裁服务信息", "file": "components\\ldpLinkConfig\\appClusterConfig.vue"}, {"text": "服务提供者", "file": "components\\ldpLinkConfig\\appClusterConfig.vue"}, {"text": "服务集群地址", "file": "components\\ldpLinkConfig\\appClusterConfig.vue"}, {"text": "集中仲裁服务路径", "file": "components\\ldpLinkConfig\\appClusterConfig.vue"}, {"text": "集群类型", "file": "components\\ldpLinkConfig\\appClusterConfig.vue"}, {"text": "集群应用节点类型", "file": "components\\ldpLinkConfig\\appClusterConfig.vue"}, {"text": "集群内成员个数", "file": "components\\ldpLinkConfig\\appClusterConfig.vue"}, {"text": "应用集群信息", "file": "components\\ldpLinkConfig\\appClusterConfig.vue"}, {"text": "应用集群信息：${(this.tableData || []).length}", "file": "components\\ldpLinkConfig\\appClusterConfig.vue"}]}, "components\\ldpLinkConfig\\appInstanceConfig.vue": {"count": 11, "texts": [{"text": "应用注册中心", "file": "components\\ldpLinkConfig\\appInstanceConfig.vue"}, {"text": "节点信息同步", "file": "components\\ldpLinkConfig\\appInstanceConfig.vue"}, {"text": "应用节点注册中心路径", "file": "components\\ldpLinkConfig\\appInstanceConfig.vue"}, {"text": "短应用名", "file": "components\\ldpLinkConfig\\appInstanceConfig.vue"}, {"text": "节点编号", "file": "components\\ldpLinkConfig\\appInstanceConfig.vue"}, {"text": "分片编号", "file": "components\\ldpLinkConfig\\appInstanceConfig.vue"}, {"text": "应用节点身份", "file": "components\\ldpLinkConfig\\appInstanceConfig.vue"}, {"text": "zk配置节点", "file": "components\\ldpLinkConfig\\appInstanceConfig.vue"}, {"text": "应用节点信息", "file": "components\\ldpLinkConfig\\appInstanceConfig.vue"}, {"text": "应用节点信息：${(this.tableData || []).length}", "file": "components\\ldpLinkConfig\\appInstanceConfig.vue"}, {"text": "您确定删除名为\"${name}\"的应用节点实例吗？", "file": "components\\ldpLinkConfig\\appInstanceConfig.vue"}]}, "components\\ldpLinkConfig\\machineRoomConfig.vue": {"count": 3, "texts": [{"text": "机房别名", "file": "components\\ldpLinkConfig\\machineRoomConfig.vue"}, {"text": "关联应用个数", "file": "components\\ldpLinkConfig\\machineRoomConfig.vue"}, {"text": "机房信息：${(this.tableData || []).length}", "file": "components\\ldpLinkConfig\\machineRoomConfig.vue"}]}, "components\\ldpLinkConfig\\productInfoConfig.vue": {"count": 34, "texts": [{"text": "产品信息", "file": "components\\ldpLinkConfig\\productInfoConfig.vue"}, {"text": "单例上下文", "file": "components\\ldpLinkConfig\\productInfoConfig.vue"}, {"text": "集群上下文", "file": "components\\ldpLinkConfig\\productInfoConfig.vue"}, {"text": "模板", "file": "components\\ldpLinkConfig\\productInfoConfig.vue"}, {"text": "应用", "file": "components\\ldpLinkConfig\\productInfoConfig.vue"}, {"text": "关联应用", "file": "components\\ldpLinkConfig\\productInfoConfig.vue"}, {"text": "服务", "file": "components\\ldpLinkConfig\\productInfoConfig.vue"}, {"text": "服务类型", "file": "components\\ldpLinkConfig\\productInfoConfig.vue"}, {"text": "主机名", "file": "components\\ldpLinkConfig\\productInfoConfig.vue"}, {"text": "主机别名", "file": "components\\ldpLinkConfig\\productInfoConfig.vue"}, {"text": "IP地址/域名", "file": "components\\ldpLinkConfig\\productInfoConfig.vue"}, {"text": "机房", "file": "components\\ldpLinkConfig\\productInfoConfig.vue"}, {"text": "机房名", "file": "components\\ldpLinkConfig\\productInfoConfig.vue"}, {"text": "应用分片", "file": "components\\ldpLinkConfig\\productInfoConfig.vue"}, {"text": "分片名", "file": "components\\ldpLinkConfig\\productInfoConfig.vue"}, {"text": "已托管配置", "file": "components\\ldpLinkConfig\\productInfoConfig.vue"}, {"text": "配置节点名称", "file": "components\\ldpLinkConfig\\productInfoConfig.vue"}, {"text": "配置根目录", "file": "components\\ldpLinkConfig\\productInfoConfig.vue"}, {"text": "配置提供者", "file": "components\\ldpLinkConfig\\productInfoConfig.vue"}, {"text": "配置服务地址", "file": "components\\ldpLinkConfig\\productInfoConfig.vue"}, {"text": "产品实例：", "file": "components\\ldpLinkConfig\\productInfoConfig.vue"}, {"text": "产品名称：", "file": "components\\ldpLinkConfig\\productInfoConfig.vue"}, {"text": "产品类型：", "file": "components\\ldpLinkConfig\\productInfoConfig.vue"}, {"text": "关联业务系统：", "file": "components\\ldpLinkConfig\\productInfoConfig.vue"}, {"text": "产品配置中心：", "file": "components\\ldpLinkConfig\\productInfoConfig.vue"}, {"text": "主题数：", "file": "components\\ldpLinkConfig\\productInfoConfig.vue"}, {"text": "主题引用模板：", "file": "components\\ldpLinkConfig\\productInfoConfig.vue"}, {"text": "单例上下文数：", "file": "components\\ldpLinkConfig\\productInfoConfig.vue"}, {"text": "单例上下文引用模板：", "file": "components\\ldpLinkConfig\\productInfoConfig.vue"}, {"text": "集群上下文数：", "file": "components\\ldpLinkConfig\\productInfoConfig.vue"}, {"text": "集群上下文引用模板：", "file": "components\\ldpLinkConfig\\productInfoConfig.vue"}, {"text": "主题模板数：", "file": "components\\ldpLinkConfig\\productInfoConfig.vue"}, {"text": "通用上下文模板：", "file": "components\\ldpLinkConfig\\productInfoConfig.vue"}, {"text": "集群上下文模板：", "file": "components\\ldpLinkConfig\\productInfoConfig.vue"}]}, "components\\ldpLinkConfig\\productMonitorConfig.vue": {"count": 9, "texts": [{"text": "该功能为商业版特性,请咨询销售获取商业版本开通策略", "file": "components\\ldpLinkConfig\\productMonitorConfig.vue"}, {"text": "`告警规则执行 共 ${monitorTotal} 条`", "file": "components\\ldpLinkConfig\\productMonitorConfig.vue"}, {"text": "当前产品暂无监控告警配置", "file": "components\\ldpLinkConfig\\productMonitorConfig.vue"}, {"text": "添加规则成功！", "file": "components\\ldpLinkConfig\\productMonitorConfig.vue"}, {"text": "告警规则清除成功！", "file": "components\\ldpLinkConfig\\productMonitorConfig.vue"}, {"text": "告警规则执行 共 ${monitorTotal} 条", "file": "components\\ldpLinkConfig\\productMonitorConfig.vue"}, {"text": "添加规则", "file": "components\\ldpLinkConfig\\productMonitorConfig.vue"}, {"text": "导入规则", "file": "components\\ldpLinkConfig\\productMonitorConfig.vue"}, {"text": "清空规则", "file": "components\\ldpLinkConfig\\productMonitorConfig.vue"}]}, "components\\ldpLinkConfig\\productServiceConfig.vue": {"count": 26, "texts": [{"text": "插件", "file": "components\\ldpLinkConfig\\productServiceConfig.vue"}, {"text": "功能说明", "file": "components\\ldpLinkConfig\\productServiceConfig.vue"}, {"text": "功能号名", "file": "components\\ldpLinkConfig\\productServiceConfig.vue"}, {"text": "子系统号", "file": "components\\ldpLinkConfig\\productServiceConfig.vue"}, {"text": "读写标识", "file": "components\\ldpLinkConfig\\productServiceConfig.vue"}, {"text": "可运行的业务状态", "file": "components\\ldpLinkConfig\\productServiceConfig.vue"}, {"text": "定位模式", "file": "components\\ldpLinkConfig\\productServiceConfig.vue"}, {"text": "数据管理功能号", "file": "components\\ldpLinkConfig\\productServiceConfig.vue"}, {"text": "平台功能号", "file": "components\\ldpLinkConfig\\productServiceConfig.vue"}, {"text": "业务功能号", "file": "components\\ldpLinkConfig\\productServiceConfig.vue"}, {"text": "数据分片", "file": "components\\ldpLinkConfig\\productServiceConfig.vue"}, {"text": "数据源", "file": "components\\ldpLinkConfig\\productServiceConfig.vue"}, {"text": "分片key", "file": "components\\ldpLinkConfig\\productServiceConfig.vue"}, {"text": "定位串", "file": "components\\ldpLinkConfig\\productServiceConfig.vue"}, {"text": "数据区间", "file": "components\\ldpLinkConfig\\productServiceConfig.vue"}, {"text": "关联集群", "file": "components\\ldpLinkConfig\\productServiceConfig.vue"}, {"text": "数据上场功能号", "file": "components\\ldpLinkConfig\\productServiceConfig.vue"}, {"text": "二次上场功能号", "file": "components\\ldpLinkConfig\\productServiceConfig.vue"}, {"text": "SQL功能号", "file": "components\\ldpLinkConfig\\productServiceConfig.vue"}, {"text": "内存表", "file": "components\\ldpLinkConfig\\productServiceConfig.vue"}, {"text": "配置信息同步成功！", "file": "components\\ldpLinkConfig\\productServiceConfig.vue"}, {"text": "数据分片、数据管理功能号配置信息已同步！", "file": "components\\ldpLinkConfig\\productServiceConfig.vue"}, {"text": "配置信息同步失败！", "file": "components\\ldpLinkConfig\\productServiceConfig.vue"}, {"text": "去配置", "file": "components\\ldpLinkConfig\\productServiceConfig.vue"}, {"text": "修改成功！", "file": "components\\ldpLinkConfig\\productServiceConfig.vue"}, {"text": "同步服务数据", "file": "components\\ldpLinkConfig\\productServiceConfig.vue"}]}, "components\\ldpLinkConfig\\rcmContext.vue": {"count": 7, "texts": [{"text": "上下文列表", "file": "components\\ldpLinkConfig\\rcmContext.vue"}, {"text": "上下文名称", "file": "components\\ldpLinkConfig\\rcmContext.vue"}, {"text": "模式", "file": "components\\ldpLinkConfig\\rcmContext.vue"}, {"text": "发送主题", "file": "components\\ldpLinkConfig\\rcmContext.vue"}, {"text": "接收主题", "file": "components\\ldpLinkConfig\\rcmContext.vue"}, {"text": "引用模板", "file": "components\\ldpLinkConfig\\rcmContext.vue"}, {"text": "标签", "file": "components\\ldpLinkConfig\\rcmContext.vue"}]}, "components\\ldpLinkConfig\\serverConfig.vue": {"count": 8, "texts": [{"text": "测试连通性", "file": "components\\ldpLinkConfig\\serverConfig.vue"}, {"text": "SSH配置", "file": "components\\ldpLinkConfig\\serverConfig.vue"}, {"text": "; // 渲染IP地址\r\n                        return h(", "file": "components\\ldpLinkConfig\\serverConfig.vue"}, {"text": "SSH用户名", "file": "components\\ldpLinkConfig\\serverConfig.vue"}, {"text": "; // 渲染SSH用户名\r\n                        return h(", "file": "components\\ldpLinkConfig\\serverConfig.vue"}, {"text": "关联机房", "file": "components\\ldpLinkConfig\\serverConfig.vue"}, {"text": "或", "file": "components\\ldpLinkConfig\\serverConfig.vue"}, {"text": "服务器：${(this.tableData || []).length}", "file": "components\\ldpLinkConfig\\serverConfig.vue"}]}, "components\\ldpLogCenter\\ldpTodbErrorLog.vue": {"count": 26, "texts": [{"text": ">\r\n        <!-- 加载状态 -->\r\n        <a-loading v-if=", "file": "components\\ldpLogCenter\\ldpTodbErrorLog.vue"}, {"text": ">回库错误日志</div>\r\n                    <div class=", "file": "components\\ldpLogCenter\\ldpTodbErrorLog.vue"}, {"text": "集群名称", "file": "components\\ldpLogCenter\\ldpTodbErrorLog.vue"}, {"text": "账户类型", "file": "components\\ldpLogCenter\\ldpTodbErrorLog.vue"}, {"text": "多个账号以英文逗号隔开", "file": "components\\ldpLogCenter\\ldpTodbErrorLog.vue"}, {"text": "选择不能为空", "file": "components\\ldpLogCenter\\ldpTodbErrorLog.vue"}, {"text": "回库状态", "file": "components\\ldpLogCenter\\ldpTodbErrorLog.vue"}, {"text": "已成功", "file": "components\\ldpLogCenter\\ldpTodbErrorLog.vue"}, {"text": "事务时间", "file": "components\\ldpLogCenter\\ldpTodbErrorLog.vue"}, {"text": "线程组", "file": "components\\ldpLogCenter\\ldpTodbErrorLog.vue"}, {"text": "支持多账号以英文逗号分隔且最多50个", "file": "components\\ldpLogCenter\\ldpTodbErrorLog.vue"}, {"text": "账户", "file": "components\\ldpLogCenter\\ldpTodbErrorLog.vue"}, {"text": "线程号", "file": "components\\ldpLogCenter\\ldpTodbErrorLog.vue"}, {"text": "事务长度", "file": "components\\ldpLogCenter\\ldpTodbErrorLog.vue"}, {"text": "错误号", "file": "components\\ldpLogCenter\\ldpTodbErrorLog.vue"}, {"text": "错误消息", "file": "components\\ldpLogCenter\\ldpTodbErrorLog.vue"}, {"text": "回库状态：", "file": "components\\ldpLogCenter\\ldpTodbErrorLog.vue"}, {"text": "表示在用户进行\"重试\"操作之后，本地记录的错误事务，是否重新发送到网关，完成回库。", "file": "components\\ldpLogCenter\\ldpTodbErrorLog.vue"}, {"text": "未成功", "file": "components\\ldpLogCenter\\ldpTodbErrorLog.vue"}, {"text": ")?.[0]; // 获取", "file": "components\\ldpLogCenter\\ldpTodbErrorLog.vue"}, {"text": "查询失败", "file": "components\\ldpLogCenter\\ldpTodbErrorLog.vue"}, {"text": "股东代码", "file": "components\\ldpLogCenter\\ldpTodbErrorLog.vue"}, {"text": "点击行查看对应日志记录详情", "file": "components\\ldpLogCenter\\ldpTodbErrorLog.vue"}, {"text": "共 {{ totalCount }} 条记录", "file": "components\\ldpLogCenter\\ldpTodbErrorLog.vue"}, {"text": "上一页", "file": "components\\ldpLogCenter\\ldpTodbErrorLog.vue"}, {"text": "下一页", "file": "components\\ldpLogCenter\\ldpTodbErrorLog.vue"}]}, "components\\ldpLogCenter\\ldpTodbErrorRetry.vue": {"count": 39, "texts": [{"text": "全部账户：", "file": "components\\ldpLogCenter\\ldpTodbErrorRetry.vue"}, {"text": "进行中：", "file": "components\\ldpLogCenter\\ldpTodbErrorRetry.vue"}, {"text": "正常完成：", "file": "components\\ldpLogCenter\\ldpTodbErrorRetry.vue"}, {"text": "终止完成：", "file": "components\\ldpLogCenter\\ldpTodbErrorRetry.vue"}, {"text": "进行中", "file": "components\\ldpLogCenter\\ldpTodbErrorRetry.vue"}, {"text": "正常完成", "file": "components\\ldpLogCenter\\ldpTodbErrorRetry.vue"}, {"text": "终止完成", "file": "components\\ldpLogCenter\\ldpTodbErrorRetry.vue"}, {"text": "剩余错误账户数", "file": "components\\ldpLogCenter\\ldpTodbErrorRetry.vue"}, {"text": "错误账户总数", "file": "components\\ldpLogCenter\\ldpTodbErrorRetry.vue"}, {"text": "剩余回库错误事务数", "file": "components\\ldpLogCenter\\ldpTodbErrorRetry.vue"}, {"text": "回库错误事务总数", "file": "components\\ldpLogCenter\\ldpTodbErrorRetry.vue"}, {"text": "重试", "file": "components\\ldpLogCenter\\ldpTodbErrorRetry.vue"}, {"text": "日志", "file": "components\\ldpLogCenter\\ldpTodbErrorRetry.vue"}, {"text": "最后重试执行状态", "file": "components\\ldpLogCenter\\ldpTodbErrorRetry.vue"}, {"text": "全部重试", "file": "components\\ldpLogCenter\\ldpTodbErrorRetry.vue"}, {"text": "全部终止", "file": "components\\ldpLogCenter\\ldpTodbErrorRetry.vue"}, {"text": "全部账户", "file": "components\\ldpLogCenter\\ldpTodbErrorRetry.vue"}, {"text": "需要重试的事务数", "file": "components\\ldpLogCenter\\ldpTodbErrorRetry.vue"}, {"text": "重试已成功事务数", "file": "components\\ldpLogCenter\\ldpTodbErrorRetry.vue"}, {"text": "执行进度", "file": "components\\ldpLogCenter\\ldpTodbErrorRetry.vue"}, {"text": "执行总耗时", "file": "components\\ldpLogCenter\\ldpTodbErrorRetry.vue"}, {"text": "开始重试消息序号", "file": "components\\ldpLogCenter\\ldpTodbErrorRetry.vue"}, {"text": "开始重试事务号", "file": "components\\ldpLogCenter\\ldpTodbErrorRetry.vue"}, {"text": "结束重试消息号", "file": "components\\ldpLogCenter\\ldpTodbErrorRetry.vue"}, {"text": "结束的事务号", "file": "components\\ldpLogCenter\\ldpTodbErrorRetry.vue"}, {"text": "最新重试消息号", "file": "components\\ldpLogCenter\\ldpTodbErrorRetry.vue"}, {"text": "最新重试的事务号", "file": "components\\ldpLogCenter\\ldpTodbErrorRetry.vue"}, {"text": "已存在进行中的重试任务！", "file": "components\\ldpLogCenter\\ldpTodbErrorRetry.vue"}, {"text": "终止任务", "file": "components\\ldpLogCenter\\ldpTodbErrorRetry.vue"}, {"text": "您确认要终止当前正在执行中的重试任务吗？", "file": "components\\ldpLogCenter\\ldpTodbErrorRetry.vue"}, {"text": "创建错误重试任务", "file": "components\\ldpLogCenter\\ldpTodbErrorRetry.vue"}, {"text": "您确认要对所有业务错误账户执行重试处理吗？", "file": "components\\ldpLogCenter\\ldpTodbErrorRetry.vue"}, {"text": "暂无集群需要重试！", "file": "components\\ldpLogCenter\\ldpTodbErrorRetry.vue"}, {"text": "暂无进行中任务！", "file": "components\\ldpLogCenter\\ldpTodbErrorRetry.vue"}, {"text": "创建重试任务成功!", "file": "components\\ldpLogCenter\\ldpTodbErrorRetry.vue"}, {"text": "创建重试任务失败!", "file": "components\\ldpLogCenter\\ldpTodbErrorRetry.vue"}, {"text": "终止任务成功!", "file": "components\\ldpLogCenter\\ldpTodbErrorRetry.vue"}, {"text": "终止任务失败!", "file": "components\\ldpLogCenter\\ldpTodbErrorRetry.vue"}, {"text": "核心集群回库错误查询", "file": "components\\ldpLogCenter\\ldpTodbErrorRetry.vue"}]}, "components\\ldpLogCenter\\recordMsgPoptip.vue": {"count": 3, "texts": [{"text": "记录字段信息", "file": "components\\ldpLogCenter\\recordMsgPoptip.vue"}, {"text": "字段名", "file": "components\\ldpLogCenter\\recordMsgPoptip.vue"}, {"text": "字段值", "file": "components\\ldpLogCenter\\recordMsgPoptip.vue"}]}, "components\\ldpMonitor\\appTypeObserver.vue": {"count": 2, "texts": [{"text": ">全局总览</div>\r\n            <a-loading v-if=", "file": "components\\ldpMonitor\\appTypeObserver.vue"}, {"text": "全局总览", "file": "components\\ldpMonitor\\appTypeObserver.vue"}]}, "components\\ldpMonitor\\businessMonitor.vue": {"count": 0, "texts": []}, "components\\ldpMonitor\\clusterAndGroupMonitor.vue": {"count": 4, "texts": [{"text": "主", "file": "components\\ldpMonitor\\clusterAndGroupMonitor.vue"}, {"text": "同城", "file": "components\\ldpMonitor\\clusterAndGroupMonitor.vue"}, {"text": "异地", "file": "components\\ldpMonitor\\clusterAndGroupMonitor.vue"}, {"text": "分片${ele.shardingNo || '-'}", "file": "components\\ldpMonitor\\clusterAndGroupMonitor.vue"}]}, "components\\ldpMonitor\\clusterProcessPerform.vue": {"count": 8, "texts": [{"text": "功能号处理", "file": "components\\ldpMonitor\\clusterProcessPerform.vue"}, {"text": "吞吐(tps)", "file": "components\\ldpMonitor\\clusterProcessPerform.vue"}, {"text": "平均时延(ns)", "file": "components\\ldpMonitor\\clusterProcessPerform.vue"}, {"text": "委托交易", "file": "components\\ldpMonitor\\clusterProcessPerform.vue"}, {"text": "吞吐率", "file": "components\\ldpMonitor\\clusterProcessPerform.vue"}, {"text": "错误率", "file": "components\\ldpMonitor\\clusterProcessPerform.vue"}, {"text": "委托查询", "file": "components\\ldpMonitor\\clusterProcessPerform.vue"}, {"text": "其他业务", "file": "components\\ldpMonitor\\clusterProcessPerform.vue"}]}, "components\\ldpMonitor\\dataAccordMonitor.vue": {"count": 13, "texts": [{"text": "请输入表名", "file": "components\\ldpMonitor\\dataAccordMonitor.vue"}, {"text": "未加载", "file": "components\\ldpMonitor\\dataAccordMonitor.vue"}, {"text": "加载中", "file": "components\\ldpMonitor\\dataAccordMonitor.vue"}, {"text": "加载完成", "file": "components\\ldpMonitor\\dataAccordMonitor.vue"}, {"text": "加载失败", "file": "components\\ldpMonitor\\dataAccordMonitor.vue"}, {"text": "内存记录数", "file": "components\\ldpMonitor\\dataAccordMonitor.vue"}, {"text": "持久化记录数", "file": "components\\ldpMonitor\\dataAccordMonitor.vue"}, {"text": "数据差量", "file": "components\\ldpMonitor\\dataAccordMonitor.vue"}, {"text": "加载状态", "file": "components\\ldpMonitor\\dataAccordMonitor.vue"}, {"text": "表详情", "file": "components\\ldpMonitor\\dataAccordMonitor.vue"}, {"text": "上场次数:", "file": "components\\ldpMonitor\\dataAccordMonitor.vue"}, {"text": "表加载详情", "file": "components\\ldpMonitor\\dataAccordMonitor.vue"}, {"text": "数据差量不为0", "file": "components\\ldpMonitor\\dataAccordMonitor.vue"}]}, "components\\ldpMonitor\\ldpAppObserver.vue": {"count": 2, "texts": [{"text": ">应用连接信息</div>\r\n            <a-loading v-if=", "file": "components\\ldpMonitor\\ldpAppObserver.vue"}, {"text": "应用连接信息", "file": "components\\ldpMonitor\\ldpAppObserver.vue"}]}, "components\\ldpMonitor\\ldpAppObserverMenu.vue": {"count": 11, "texts": [{"text": ">\r\n        <!-- 上下游组合条件查询 -->\r\n        <div v-if=", "file": "components\\ldpMonitor\\ldpAppObserverMenu.vue"}, {"text": "连接类型", "file": "components\\ldpMonitor\\ldpAppObserverMenu.vue"}, {"text": "连接状态", "file": "components\\ldpMonitor\\ldpAppObserverMenu.vue"}, {"text": "仲裁节点", "file": "components\\ldpMonitor\\ldpAppObserverMenu.vue"}, {"text": "搜索应用节点", "file": "components\\ldpMonitor\\ldpAppObserverMenu.vue"}, {"text": ">上下游关系</h-option>\r\n            <h-option value=", "file": "components\\ldpMonitor\\ldpAppObserverMenu.vue"}, {"text": "查询应用节点下拉列表-错误:", "file": "components\\ldpMonitor\\ldpAppObserverMenu.vue"}, {"text": "过滤仲裁节点", "file": "components\\ldpMonitor\\ldpAppObserverMenu.vue"}, {"text": "不过滤仲裁节点", "file": "components\\ldpMonitor\\ldpAppObserverMenu.vue"}, {"text": "上下游关系", "file": "components\\ldpMonitor\\ldpAppObserverMenu.vue"}, {"text": "集群同步关系", "file": "components\\ldpMonitor\\ldpAppObserverMenu.vue"}]}, "components\\ldpMonitor\\ldpClusterObserver.vue": {"count": 1, "texts": [{"text": "集群连接信息", "file": "components\\ldpMonitor\\ldpClusterObserver.vue"}]}, "components\\ldpMonitor\\ldpClusterObserverMenu.vue": {"count": 1, "texts": [{"text": "搜索集群", "file": "components\\ldpMonitor\\ldpClusterObserverMenu.vue"}]}, "components\\ldpMonitor\\observerSetting.vue": {"count": 10, "texts": [{"text": "拓扑显示设置", "file": "components\\ldpMonitor\\observerSetting.vue"}, {"text": ">显示未托管节点</div>\r\n                <div class=", "file": "components\\ldpMonitor\\observerSetting.vue"}, {"text": ">RCM配置</div>\r\n                <div class=", "file": "components\\ldpMonitor\\observerSetting.vue"}, {"text": ">默认标签</div>\r\n                <div class=", "file": "components\\ldpMonitor\\observerSetting.vue"}, {"text": "查询rcm配置失败：", "file": "components\\ldpMonitor\\observerSetting.vue"}, {"text": "查询标签失败：", "file": "components\\ldpMonitor\\observerSetting.vue"}, {"text": "显示未托管节点", "file": "components\\ldpMonitor\\observerSetting.vue"}, {"text": "上下文", "file": "components\\ldpMonitor\\observerSetting.vue"}, {"text": "RCM配置", "file": "components\\ldpMonitor\\observerSetting.vue"}, {"text": "默认标签", "file": "components\\ldpMonitor\\observerSetting.vue"}]}, "components\\ldpMonitor\\operatePerform.vue": {"count": 13, "texts": [{"text": ">\r\n    <!-- 指标总览 -->\r\n    <info-sum-bar :data=", "file": "components\\ldpMonitor\\operatePerform.vue"}, {"text": "委托业务性能总览", "file": "components\\ldpMonitor\\operatePerform.vue"}, {"text": "最近5分钟", "file": "components\\ldpMonitor\\operatePerform.vue"}, {"text": "最近15分钟", "file": "components\\ldpMonitor\\operatePerform.vue"}, {"text": "最近30分钟", "file": "components\\ldpMonitor\\operatePerform.vue"}, {"text": "最近1小时", "file": "components\\ldpMonitor\\operatePerform.vue"}, {"text": "5秒", "file": "components\\ldpMonitor\\operatePerform.vue"}, {"text": "吞吐", "file": "components\\ldpMonitor\\operatePerform.vue"}, {"text": "时延", "file": "components\\ldpMonitor\\operatePerform.vue"}, {"text": "吞吐(qbs)", "file": "components\\ldpMonitor\\operatePerform.vue"}, {"text": "时延(ns)", "file": "components\\ldpMonitor\\operatePerform.vue"}, {"text": "请求数", "file": "components\\ldpMonitor\\operatePerform.vue"}, {"text": "次", "file": "components\\ldpMonitor\\operatePerform.vue"}]}, "components\\ldpMonitor\\rcmObserver.vue": {"count": 1, "texts": [{"text": "查看详情", "file": "components\\ldpMonitor\\rcmObserver.vue"}]}, "components\\ldpMonitor\\rcmObserverMenu.vue": {"count": 4, "texts": [{"text": "搜索主题", "file": "components\\ldpMonitor\\rcmObserverMenu.vue"}, {"text": "同步模式", "file": "components\\ldpMonitor\\rcmObserverMenu.vue"}, {"text": "状态机复制", "file": "components\\ldpMonitor\\rcmObserverMenu.vue"}, {"text": "消息复制", "file": "components\\ldpMonitor\\rcmObserverMenu.vue"}]}, "components\\ldpTable\\historyModifyList.vue": {"count": 32, "texts": [{"text": "操作内容", "file": "components\\ldpTable\\historyModifyList.vue"}, {"text": "操作用户", "file": "components\\ldpTable\\historyModifyList.vue"}, {"text": "操作执行", "file": "components\\ldpTable\\historyModifyList.vue"}, {"text": "未执行", "file": "components\\ldpTable\\historyModifyList.vue"}, {"text": "失效", "file": "components\\ldpTable\\historyModifyList.vue"}, {"text": "执行中", "file": "components\\ldpTable\\historyModifyList.vue"}, {"text": "执行完毕", "file": "components\\ldpTable\\historyModifyList.vue"}, {"text": "数据记录号", "file": "components\\ldpTable\\historyModifyList.vue"}, {"text": "字段类型", "file": "components\\ldpTable\\historyModifyList.vue"}, {"text": "修改值", "file": "components\\ldpTable\\historyModifyList.vue"}, {"text": "原纪录值", "file": "components\\ldpTable\\historyModifyList.vue"}, {"text": "修改状态", "file": "components\\ldpTable\\historyModifyList.vue"}, {"text": "修改执行", "file": "components\\ldpTable\\historyModifyList.vue"}, {"text": "该修改未执行", "file": "components\\ldpTable\\historyModifyList.vue"}, {"text": ", // 请求路径\r\n                requestProtocol:", "file": "components\\ldpTable\\historyModifyList.vue"}, {"text": ", // 请求协议\r\n                requestTime:", "file": "components\\ldpTable\\historyModifyList.vue"}, {"text": ", // 请求时间\r\n                requestParams:", "file": "components\\ldpTable\\historyModifyList.vue"}, {"text": ", // 请求参数\r\n                response:", "file": "components\\ldpTable\\historyModifyList.vue"}, {"text": "查看详情失败", "file": "components\\ldpTable\\historyModifyList.vue"}, {"text": "目标表:", "file": "components\\ldpTable\\historyModifyList.vue"}, {"text": "批量修改操作:", "file": "components\\ldpTable\\historyModifyList.vue"}, {"text": "影响数据条数:", "file": "components\\ldpTable\\historyModifyList.vue"}, {"text": "影响字段个数:", "file": "components\\ldpTable\\historyModifyList.vue"}, {"text": "用户名:", "file": "components\\ldpTable\\historyModifyList.vue"}, {"text": "用户角色:", "file": "components\\ldpTable\\historyModifyList.vue"}, {"text": "修改单提交时间:", "file": "components\\ldpTable\\historyModifyList.vue"}, {"text": "修改单状态:", "file": "components\\ldpTable\\historyModifyList.vue"}, {"text": "修改成功数:", "file": "components\\ldpTable\\historyModifyList.vue"}, {"text": "修改失败数:", "file": "components\\ldpTable\\historyModifyList.vue"}, {"text": "修改开始时间:", "file": "components\\ldpTable\\historyModifyList.vue"}, {"text": "修改结束时间:", "file": "components\\ldpTable\\historyModifyList.vue"}, {"text": "修改总耗时:", "file": "components\\ldpTable\\historyModifyList.vue"}]}, "components\\ldpTable\\selectBox.vue": {"count": 2, "texts": [{"text": "内存表属性", "file": "components\\ldpTable\\selectBox.vue"}, {"text": "表单验证失败!", "file": "components\\ldpTable\\selectBox.vue"}]}, "components\\ldpTable\\tableManageTop.vue": {"count": 5, "texts": [{"text": "内存数据表管理", "file": "components\\ldpTable\\tableManageTop.vue"}, {"text": "断开", "file": "components\\ldpTable\\tableManageTop.vue"}, {"text": "连接", "file": "components\\ldpTable\\tableManageTop.vue"}, {"text": "请选择连接的产品及应用节点", "file": "components\\ldpTable\\tableManageTop.vue"}, {"text": "{{ linkbtn ? '断开' : '连接' }}", "file": "components\\ldpTable\\tableManageTop.vue"}]}, "components\\ldpTable\\tableModify.vue": {"count": 16, "texts": [{"text": ">\r\n        <!-- 筛选部分 -->\r\n        <div class=", "file": "components\\ldpTable\\tableModify.vue"}, {"text": "历史修改单", "file": "components\\ldpTable\\tableModify.vue"}, {"text": "修改预览", "file": "components\\ldpTable\\tableModify.vue"}, {"text": "boxInfo.tableName+ ' 表修改操作详情'", "file": "components\\ldpTable\\tableModify.vue"}, {"text": "原记录值", "file": "components\\ldpTable\\tableModify.vue"}, {"text": "修改单列表查询失败!", "file": "components\\ldpTable\\tableModify.vue"}, {"text": "清空操作失败!", "file": "components\\ldpTable\\tableModify.vue"}, {"text": "修改预览查询失败!", "file": "components\\ldpTable\\tableModify.vue"}, {"text": "预览修改单条目删除成功!", "file": "components\\ldpTable\\tableModify.vue"}, {"text": "预览修改单条目删除失败!", "file": "components\\ldpTable\\tableModify.vue"}, {"text": "预览修改单条目不存在!", "file": "components\\ldpTable\\tableModify.vue"}, {"text": "执行修改失败!", "file": "components\\ldpTable\\tableModify.vue"}, {"text": "您确定删除\"${params.row.fieldName}\"的修改操作吗？", "file": "components\\ldpTable\\tableModify.vue"}, {"text": "您确定执行清空操作吗？", "file": "components\\ldpTable\\tableModify.vue"}, {"text": "清空操作", "file": "components\\ldpTable\\tableModify.vue"}, {"text": "提交修改", "file": "components\\ldpTable\\tableModify.vue"}]}, "components\\ldpTable\\tableQuery.vue": {"count": 3, "texts": [{"text": "20条/页", "file": "components\\ldpTable\\tableQuery.vue"}, {"text": "30条/页", "file": "components\\ldpTable\\tableQuery.vue"}, {"text": "50条/页", "file": "components\\ldpTable\\tableQuery.vue"}]}, "components\\ldpTable\\tableStructure.vue": {"count": 11, "texts": [{"text": "数据表信息", "file": "components\\ldpTable\\tableStructure.vue"}, {"text": "数据表字段", "file": "components\\ldpTable\\tableStructure.vue"}, {"text": "数据类型", "file": "components\\ldpTable\\tableStructure.vue"}, {"text": "是否可修改", "file": "components\\ldpTable\\tableStructure.vue"}, {"text": "查询结果可见", "file": "components\\ldpTable\\tableStructure.vue"}, {"text": "全选字段", "file": "components\\ldpTable\\tableStructure.vue"}, {"text": "可作为查询条件", "file": "components\\ldpTable\\tableStructure.vue"}, {"text": "数据表名：", "file": "components\\ldpTable\\tableStructure.vue"}, {"text": "数据表扩展属性：", "file": "components\\ldpTable\\tableStructure.vue"}, {"text": "数据表说明：", "file": "components\\ldpTable\\tableStructure.vue"}, {"text": "禁用struct字段可见属性配置", "file": "components\\ldpTable\\tableStructure.vue"}]}, "components\\locateConfig\\diffResult.vue": {"count": 6, "texts": [{"text": "配置对比", "file": "components\\locateConfig\\diffResult.vue"}, {"text": "配置文件类型", "file": "components\\locateConfig\\diffResult.vue"}, {"text": "源节点", "file": "components\\locateConfig\\diffResult.vue"}, {"text": "目标节点", "file": "components\\locateConfig\\diffResult.vue"}, {"text": "是否一致", "file": "components\\locateConfig\\diffResult.vue"}, {"text": "一键修正", "file": "components\\locateConfig\\diffResult.vue"}]}, "components\\locateConfig\\locateConfigManage.vue": {"count": 14, "texts": [{"text": ">\r\n        <!-- 定位节点配置列表 -->\r\n        <div class=", "file": "components\\locateConfig\\locateConfigManage.vue"}, {"text": ">保存</a-button>\r\n                <a-button v-if=", "file": "components\\locateConfig\\locateConfigManage.vue"}, {"text": "定位节点配置列表", "file": "components\\locateConfig\\locateConfigManage.vue"}, {"text": "节点定位插件配置", "file": "components\\locateConfig\\locateConfigManage.vue"}, {"text": "节点定位规则配置", "file": "components\\locateConfig\\locateConfigManage.vue"}, {"text": "配置对象类型", "file": "components\\locateConfig\\locateConfigManage.vue"}, {"text": "配置对象", "file": "components\\locateConfig\\locateConfigManage.vue"}, {"text": "在线", "file": "components\\locateConfig\\locateConfigManage.vue"}, {"text": "离线", "file": "components\\locateConfig\\locateConfigManage.vue"}, {"text": "对比配置", "file": "components\\locateConfig\\locateConfigManage.vue"}, {"text": "文件大小超出16MB", "file": "components\\locateConfig\\locateConfigManage.vue"}, {"text": "配置文件格式：{{ localLanguage || '-' }}", "file": "components\\locateConfig\\locateConfigManage.vue"}, {"text": "保存", "file": "components\\locateConfig\\locateConfigManage.vue"}, {"text": "同步", "file": "components\\locateConfig\\locateConfigManage.vue"}]}, "components\\locateConfig\\nodeDiff.vue": {"count": 7, "texts": [{"text": "`对比源节点: ${sourceDiff.sourceNode || '-'}`", "file": "components\\locateConfig\\nodeDiff.vue"}, {"text": "`对比目标节点: ${targetDiff.targetNode || '-'}`", "file": "components\\locateConfig\\nodeDiff.vue"}, {"text": "对比源节点: ${sourceDiff.sourceNode || '-'}", "file": "components\\locateConfig\\nodeDiff.vue"}, {"text": "对比目标节点: ${targetDiff.targetNode || '-'}", "file": "components\\locateConfig\\nodeDiff.vue"}, {"text": "配置文件格式：{{ language || '-' }}", "file": "components\\locateConfig\\nodeDiff.vue"}, {"text": "配置路径：{{ sourceDiff.sourcePath || '-' }}", "file": "components\\locateConfig\\nodeDiff.vue"}, {"text": "配置路径：{{ targetDiff.targetPath || '-' }}", "file": "components\\locateConfig\\nodeDiff.vue"}]}, "components\\locateConfig\\selectDiffModal.vue": {"count": 6, "texts": [{"text": "选择对比配置的节点", "file": "components\\locateConfig\\selectDiffModal.vue"}, {"text": "配置类型", "file": "components\\locateConfig\\selectDiffModal.vue"}, {"text": "原始配置来源", "file": "components\\locateConfig\\selectDiffModal.vue"}, {"text": "对比源节点", "file": "components\\locateConfig\\selectDiffModal.vue"}, {"text": "对比目标节点", "file": "components\\locateConfig\\selectDiffModal.vue"}, {"text": "节点定位AR", "file": "components\\locateConfig\\selectDiffModal.vue"}]}, "components\\managementQuery\\appConfigInfoDrawer.vue": {"count": 24, "texts": [{"text": "基本信息", "file": "components\\managementQuery\\appConfigInfoDrawer.vue"}, {"text": ">功能名称：</span>\r\n            <p class=", "file": "components\\managementQuery\\appConfigInfoDrawer.vue"}, {"text": ">功能备注：</span>\r\n            <p class=", "file": "components\\managementQuery\\appConfigInfoDrawer.vue"}, {"text": ">版本号：</span>\r\n            <p class=", "file": "components\\managementQuery\\appConfigInfoDrawer.vue"}, {"text": ">更新时间：</span>\r\n            <p class=", "file": "components\\managementQuery\\appConfigInfoDrawer.vue"}, {"text": ">提供者：</span>\r\n            <p class=", "file": "components\\managementQuery\\appConfigInfoDrawer.vue"}, {"text": ">功能说明：</span>\r\n            <p class=", "file": "components\\managementQuery\\appConfigInfoDrawer.vue"}, {"text": "入参说明", "file": "components\\managementQuery\\appConfigInfoDrawer.vue"}, {"text": "出参说明", "file": "components\\managementQuery\\appConfigInfoDrawer.vue"}, {"text": "案例说明", "file": "components\\managementQuery\\appConfigInfoDrawer.vue"}, {"text": ">入参案例</div>\r\n        <div class=", "file": "components\\managementQuery\\appConfigInfoDrawer.vue"}, {"text": ">出参案例</div>\r\n        <div class=", "file": "components\\managementQuery\\appConfigInfoDrawer.vue"}, {"text": "入参名称", "file": "components\\managementQuery\\appConfigInfoDrawer.vue"}, {"text": "参数类型", "file": "components\\managementQuery\\appConfigInfoDrawer.vue"}, {"text": "参数说明", "file": "components\\managementQuery\\appConfigInfoDrawer.vue"}, {"text": "出参名称", "file": "components\\managementQuery\\appConfigInfoDrawer.vue"}, {"text": "功能名称：", "file": "components\\managementQuery\\appConfigInfoDrawer.vue"}, {"text": "功能备注：", "file": "components\\managementQuery\\appConfigInfoDrawer.vue"}, {"text": "版本号：", "file": "components\\managementQuery\\appConfigInfoDrawer.vue"}, {"text": "更新时间：", "file": "components\\managementQuery\\appConfigInfoDrawer.vue"}, {"text": "提供者：", "file": "components\\managementQuery\\appConfigInfoDrawer.vue"}, {"text": "功能说明：", "file": "components\\managementQuery\\appConfigInfoDrawer.vue"}, {"text": "入参案例", "file": "components\\managementQuery\\appConfigInfoDrawer.vue"}, {"text": "出参案例", "file": "components\\managementQuery\\appConfigInfoDrawer.vue"}]}, "components\\managementQuery\\batchExportModal.vue": {"count": 13, "texts": [{"text": "请输入管理功能名称搜索", "file": "components\\managementQuery\\batchExportModal.vue"}, {"text": "管理功能", "file": "components\\managementQuery\\batchExportModal.vue"}, {"text": "移除", "file": "components\\managementQuery\\batchExportModal.vue"}, {"text": "正在导出", "file": "components\\managementQuery\\batchExportModal.vue"}, {"text": "导出停止", "file": "components\\managementQuery\\batchExportModal.vue"}, {"text": "终止失败", "file": "components\\managementQuery\\batchExportModal.vue"}, {"text": "导出终止异常", "file": "components\\managementQuery\\batchExportModal.vue"}, {"text": "导出任务启动成功，请稍候", "file": "components\\managementQuery\\batchExportModal.vue"}, {"text": "导出任务启动失败", "file": "components\\managementQuery\\batchExportModal.vue"}, {"text": "搜索管理功能 ({{ leftData.length }})", "file": "components\\managementQuery\\batchExportModal.vue"}, {"text": "待导出列表 ({{ rightData.length }})", "file": "components\\managementQuery\\batchExportModal.vue"}, {"text": "原因", "file": "components\\managementQuery\\batchExportModal.vue"}, {"text": "清空列表", "file": "components\\managementQuery\\batchExportModal.vue"}]}, "components\\managementQuery\\configDataDrawer.vue": {"count": 9, "texts": [{"text": "表格", "file": "components\\managementQuery\\configDataDrawer.vue"}, {"text": "功能配置", "file": "components\\managementQuery\\configDataDrawer.vue"}, {"text": "结果展示方式\r\n      &nbsp;", "file": "components\\managementQuery\\configDataDrawer.vue"}, {"text": "最大展示Tab个数", "file": "components\\managementQuery\\configDataDrawer.vue"}, {"text": "交互配置", "file": "components\\managementQuery\\configDataDrawer.vue"}, {"text": "只保留激活插件功能列表展开", "file": "components\\managementQuery\\configDataDrawer.vue"}, {"text": "记录最后一次输入参数", "file": "components\\managementQuery\\configDataDrawer.vue"}, {"text": "结果页与管理功能联动", "file": "components\\managementQuery\\configDataDrawer.vue"}, {"text": "单击Get类型功能菜单时自动发起请求", "file": "components\\managementQuery\\configDataDrawer.vue"}]}, "components\\managementQuery\\exportDataModal.vue": {"count": 3, "texts": [{"text": "管理功能导出", "file": "components\\managementQuery\\exportDataModal.vue"}, {"text": "根据需要，选择想要导出的管理功能。", "file": "components\\managementQuery\\exportDataModal.vue"}, {"text": "入参", "file": "components\\managementQuery\\exportDataModal.vue"}]}, "components\\managementQuery\\jsonPathDrawer.vue": {"count": 8, "texts": [{"text": "`配置（`+ modalData.title + ')'", "file": "components\\managementQuery\\jsonPathDrawer.vue"}, {"text": ">保存配置</a-button\r\n      >\r\n      <a-button type=", "file": "components\\managementQuery\\jsonPathDrawer.vue"}, {"text": "请输入jsonPath", "file": "components\\managementQuery\\jsonPathDrawer.vue"}, {"text": "别名", "file": "components\\managementQuery\\jsonPathDrawer.vue"}, {"text": "表格展示名称", "file": "components\\managementQuery\\jsonPathDrawer.vue"}, {"text": "是否支持排序", "file": "components\\managementQuery\\jsonPathDrawer.vue"}, {"text": "表格字段支持行排序(仅当表格为一维数组时生效)", "file": "components\\managementQuery\\jsonPathDrawer.vue"}, {"text": "配置（", "file": "components\\managementQuery\\jsonPathDrawer.vue"}]}, "components\\managementQuery\\JsonPathTable.vue": {"count": 2, "texts": [{"text": "值", "file": "components\\managementQuery\\JsonPathTable.vue"}, {"text": "处理数据时出错:", "file": "components\\managementQuery\\JsonPathTable.vue"}]}, "components\\managementQuery\\managementBox.vue": {"count": 4, "texts": [{"text": "管理功能使用说明", "file": "components\\managementQuery\\managementBox.vue"}, {"text": "前置", "file": "components\\managementQuery\\managementBox.vue"}, {"text": "回库", "file": "components\\managementQuery\\managementBox.vue"}, {"text": "placeholder=\"请输入\" disabled :class=\"[", "file": "components\\managementQuery\\managementBox.vue"}]}, "components\\marketAllLink\\allLinkAnalyseConfig.vue": {"count": 10, "texts": [{"text": "传输链路", "file": "components\\marketAllLink\\allLinkAnalyseConfig.vue"}, {"text": ">分析指标</div>\r\n                <h-form-item label=", "file": "components\\marketAllLink\\allLinkAnalyseConfig.vue"}, {"text": ">数据范围</div>\r\n                <h-form-item label=", "file": "components\\marketAllLink\\allLinkAnalyseConfig.vue"}, {"text": "统计范围", "file": "components\\marketAllLink\\allLinkAnalyseConfig.vue"}, {"text": "自定义范围", "file": "components\\marketAllLink\\allLinkAnalyseConfig.vue"}, {"text": ",\r\n                spans: [ // 链路指标", "file": "components\\marketAllLink\\allLinkAnalyseConfig.vue"}, {"text": "95%分位数", "file": "components\\marketAllLink\\allLinkAnalyseConfig.vue"}, {"text": "5%分位数", "file": "components\\marketAllLink\\allLinkAnalyseConfig.vue"}, {"text": "分析指标", "file": "components\\marketAllLink\\allLinkAnalyseConfig.vue"}, {"text": "提交", "file": "components\\marketAllLink\\allLinkAnalyseConfig.vue"}]}, "components\\marketAllLink\\marketChart.vue": {"count": 20, "texts": [{"text": "当前行情系统暂无时延走势", "file": "components\\marketAllLink\\marketChart.vue"}, {"text": "秒级系统穿透时延标准差", "file": "components\\marketAllLink\\marketChart.vue"}, {"text": ">系统抖动范围</p>\r\n                            <p class=", "file": "components\\marketAllLink\\marketChart.vue"}, {"text": ">时延正常率</p>\r\n                        <p class=", "file": "components\\marketAllLink\\marketChart.vue"}, {"text": "95分位数", "file": "components\\marketAllLink\\marketChart.vue"}, {"text": "5分位数", "file": "components\\marketAllLink\\marketChart.vue"}, {"text": "系统抖动范围", "file": "components\\marketAllLink\\marketChart.vue"}, {"text": "快照行情", "file": "components\\marketAllLink\\marketChart.vue"}, {"text": "指数行情", "file": "components\\marketAllLink\\marketChart.vue"}, {"text": "逐笔委托", "file": "components\\marketAllLink\\marketChart.vue"}, {"text": "逐笔成交", "file": "components\\marketAllLink\\marketChart.vue"}, {"text": ",        // 链路名称\r\n                    linkName:", "file": "components\\marketAllLink\\marketChart.vue"}, {"text": ", // 抖动范围", "file": "components\\marketAllLink\\marketChart.vue"}, {"text": "${this.spanData.name}-K线", "file": "components\\marketAllLink\\marketChart.vue"}, {"text": "${name}走势(ns)", "file": "components\\marketAllLink\\marketChart.vue"}, {"text": "{{ lastTimeData.time }} 系统全链路时延", "file": "components\\marketAllLink\\marketChart.vue"}, {"text": "最大值:", "file": "components\\marketAllLink\\marketChart.vue"}, {"text": "95分位数:", "file": "components\\marketAllLink\\marketChart.vue"}, {"text": "5分位数:", "file": "components\\marketAllLink\\marketChart.vue"}, {"text": "最小值:", "file": "components\\marketAllLink\\marketChart.vue"}]}, "components\\marketAllLink\\suspend.vue": {"count": 3, "texts": [{"text": ">应用状态：</span>\r\n                    <span class=", "file": "components\\marketAllLink\\suspend.vue"}, {"text": ">时延正常率</p>\r\n            <p v-for=", "file": "components\\marketAllLink\\suspend.vue"}, {"text": "应用状态：", "file": "components\\marketAllLink\\suspend.vue"}]}, "components\\mcDataObservation\\backtrackQuery.vue": {"count": 17, "texts": [{"text": "主题名", "file": "components\\mcDataObservation\\backtrackQuery.vue"}, {"text": "请选择主题名", "file": "components\\mcDataObservation\\backtrackQuery.vue"}, {"text": "分区", "file": "components\\mcDataObservation\\backtrackQuery.vue"}, {"text": "请选择分区", "file": "components\\mcDataObservation\\backtrackQuery.vue"}, {"text": "查询方式", "file": "components\\mcDataObservation\\backtrackQuery.vue"}, {"text": "请输入1~1000的正整数", "file": "components\\mcDataObservation\\backtrackQuery.vue"}, {"text": "最近", "file": "components\\mcDataObservation\\backtrackQuery.vue"}, {"text": "最早", "file": "components\\mcDataObservation\\backtrackQuery.vue"}, {"text": "消息ID范围", "file": "components\\mcDataObservation\\backtrackQuery.vue"}, {"text": "分区号", "file": "components\\mcDataObservation\\backtrackQuery.vue"}, {"text": "消息ID", "file": "components\\mcDataObservation\\backtrackQuery.vue"}, {"text": "生产者", "file": "components\\mcDataObservation\\backtrackQuery.vue"}, {"text": "发布序号", "file": "components\\mcDataObservation\\backtrackQuery.vue"}, {"text": "发布时间", "file": "components\\mcDataObservation\\backtrackQuery.vue"}, {"text": "消息大小", "file": "components\\mcDataObservation\\backtrackQuery.vue"}, {"text": "过滤条件", "file": "components\\mcDataObservation\\backtrackQuery.vue"}, {"text": "消息内容", "file": "components\\mcDataObservation\\backtrackQuery.vue"}]}, "components\\mcDataObservation\\consumeBacklog.vue": {"count": 28, "texts": [{"text": "或者", "file": "components\\mcDataObservation\\consumeBacklog.vue"}, {"text": "消费者集群消息处理", "file": "components\\mcDataObservation\\consumeBacklog.vue"}, {"text": "消费者集群", "file": "components\\mcDataObservation\\consumeBacklog.vue"}, {"text": "实例名", "file": "components\\mcDataObservation\\consumeBacklog.vue"}, {"text": "消息积压数", "file": "components\\mcDataObservation\\consumeBacklog.vue"}, {"text": "消费消息序号", "file": "components\\mcDataObservation\\consumeBacklog.vue"}, {"text": "最新消息序号", "file": "components\\mcDataObservation\\consumeBacklog.vue"}, {"text": "调用次数", "file": "components\\mcDataObservation\\consumeBacklog.vue"}, {"text": "当日客户端平均处理耗时", "file": "components\\mcDataObservation\\consumeBacklog.vue"}, {"text": "当日客户端平均排队耗时", "file": "components\\mcDataObservation\\consumeBacklog.vue"}, {"text": "当日客户端平均执行耗时", "file": "components\\mcDataObservation\\consumeBacklog.vue"}, {"text": "当日客户端最大处理耗时", "file": "components\\mcDataObservation\\consumeBacklog.vue"}, {"text": "当日客户端最大排队耗时", "file": "components\\mcDataObservation\\consumeBacklog.vue"}, {"text": "当日客户端最大执行耗时", "file": "components\\mcDataObservation\\consumeBacklog.vue"}, {"text": "当日客户端最小处理耗时", "file": "components\\mcDataObservation\\consumeBacklog.vue"}, {"text": "当日客户端最小排队耗时", "file": "components\\mcDataObservation\\consumeBacklog.vue"}, {"text": "当日客户端最小执行耗时", "file": "components\\mcDataObservation\\consumeBacklog.vue"}, {"text": "消息处理变化趋势", "file": "components\\mcDataObservation\\consumeBacklog.vue"}, {"text": "最新15分钟", "file": "components\\mcDataObservation\\consumeBacklog.vue"}, {"text": "历史15分钟", "file": "components\\mcDataObservation\\consumeBacklog.vue"}, {"text": "个", "file": "components\\mcDataObservation\\consumeBacklog.vue"}, {"text": "数量(个)", "file": "components\\mcDataObservation\\consumeBacklog.vue"}, {"text": "最大", "file": "components\\mcDataObservation\\consumeBacklog.vue"}, {"text": "最小", "file": "components\\mcDataObservation\\consumeBacklog.vue"}, {"text": "当日客户端处理耗时", "file": "components\\mcDataObservation\\consumeBacklog.vue"}, {"text": "当日客户端排队耗时", "file": "components\\mcDataObservation\\consumeBacklog.vue"}, {"text": "当日客户端执行耗时", "file": "components\\mcDataObservation\\consumeBacklog.vue"}, {"text": "耗时", "file": "components\\mcDataObservation\\consumeBacklog.vue"}]}, "components\\mcDataObservation\\deadLetterQueue.vue": {"count": 8, "texts": [{"text": "死信队列", "file": "components\\mcDataObservation\\deadLetterQueue.vue"}, {"text": "刷新", "file": "components\\mcDataObservation\\deadLetterQueue.vue"}, {"text": "死信主题", "file": "components\\mcDataObservation\\deadLetterQueue.vue"}, {"text": "消息主题", "file": "components\\mcDataObservation\\deadLetterQueue.vue"}, {"text": "消息分区", "file": "components\\mcDataObservation\\deadLetterQueue.vue"}, {"text": "上次接收时间", "file": "components\\mcDataObservation\\deadLetterQueue.vue"}, {"text": "死信消息数", "file": "components\\mcDataObservation\\deadLetterQueue.vue"}, {"text": "清空队列", "file": "components\\mcDataObservation\\deadLetterQueue.vue"}]}, "components\\mcDataObservation\\mcCluster.vue": {"count": 15, "texts": [{"text": "线程编号", "file": "components\\mcDataObservation\\mcCluster.vue"}, {"text": "当前队列消息个数", "file": "components\\mcDataObservation\\mcCluster.vue"}, {"text": "历史队列最大消息数", "file": "components\\mcDataObservation\\mcCluster.vue"}, {"text": "处理总数", "file": "components\\mcDataObservation\\mcCluster.vue"}, {"text": "产品版本", "file": "components\\mcDataObservation\\mcCluster.vue"}, {"text": "产品类型", "file": "components\\mcDataObservation\\mcCluster.vue"}, {"text": "集群主机数", "file": "components\\mcDataObservation\\mcCluster.vue"}, {"text": "编号", "file": "components\\mcDataObservation\\mcCluster.vue"}, {"text": "主机地址", "file": "components\\mcDataObservation\\mcCluster.vue"}, {"text": "发布线程", "file": "components\\mcDataObservation\\mcCluster.vue"}, {"text": "会话线程", "file": "components\\mcDataObservation\\mcCluster.vue"}, {"text": "订阅线程", "file": "components\\mcDataObservation\\mcCluster.vue"}, {"text": "推送线程", "file": "components\\mcDataObservation\\mcCluster.vue"}, {"text": "主题线程", "file": "components\\mcDataObservation\\mcCluster.vue"}, {"text": "消息中心", "file": "components\\mcDataObservation\\mcCluster.vue"}]}, "components\\mcDataObservation\\mcOverview.vue": {"count": 23, "texts": [{"text": "全主题统计", "file": "components\\mcDataObservation\\mcOverview.vue"}, {"text": "单主题统计", "file": "components\\mcDataObservation\\mcOverview.vue"}, {"text": "节点数", "file": "components\\mcDataObservation\\mcOverview.vue"}, {"text": "会话数", "file": "components\\mcDataObservation\\mcOverview.vue"}, {"text": "主题总数", "file": "components\\mcDataObservation\\mcOverview.vue"}, {"text": "持久化主题数", "file": "components\\mcDataObservation\\mcOverview.vue"}, {"text": "分区总数", "file": "components\\mcDataObservation\\mcOverview.vue"}, {"text": "分区副本总数", "file": "components\\mcDataObservation\\mcOverview.vue"}, {"text": "生产者总数", "file": "components\\mcDataObservation\\mcOverview.vue"}, {"text": "生产消息总数", "file": "components\\mcDataObservation\\mcOverview.vue"}, {"text": "消费者", "file": "components\\mcDataObservation\\mcOverview.vue"}, {"text": "消费者总数", "file": "components\\mcDataObservation\\mcOverview.vue"}, {"text": "订阅项总数", "file": "components\\mcDataObservation\\mcOverview.vue"}, {"text": "推送消息总数", "file": "components\\mcDataObservation\\mcOverview.vue"}, {"text": "集群线程队列消息变化趋势", "file": "components\\mcDataObservation\\mcOverview.vue"}, {"text": "主题生产消费消息变化趋势", "file": "components\\mcDataObservation\\mcOverview.vue"}, {"text": "发布消息变化差量", "file": "components\\mcDataObservation\\mcOverview.vue"}, {"text": "订阅消息变化差量", "file": "components\\mcDataObservation\\mcOverview.vue"}, {"text": "消息数(个)", "file": "components\\mcDataObservation\\mcOverview.vue"}, {"text": "请选择主题", "file": "components\\mcDataObservation\\mcOverview.vue"}, {"text": "请选择生产者", "file": "components\\mcDataObservation\\mcOverview.vue"}, {"text": "请选择消费者", "file": "components\\mcDataObservation\\mcOverview.vue"}, {"text": ", // 集群下拉框\r\n            brokerType:", "file": "components\\mcDataObservation\\mcOverview.vue"}]}, "components\\mcDataObservation\\mcPublish.vue": {"count": 4, "texts": [{"text": "发布主题数", "file": "components\\mcDataObservation\\mcPublish.vue"}, {"text": "发布分区数", "file": "components\\mcDataObservation\\mcPublish.vue"}, {"text": "生产消息数", "file": "components\\mcDataObservation\\mcPublish.vue"}, {"text": "生产者信息", "file": "components\\mcDataObservation\\mcPublish.vue"}]}, "components\\mcDataObservation\\mcSubscribe.vue": {"count": 12, "texts": [{"text": "订阅项数", "file": "components\\mcDataObservation\\mcSubscribe.vue"}, {"text": "订阅主题数", "file": "components\\mcDataObservation\\mcSubscribe.vue"}, {"text": "推送消息数", "file": "components\\mcDataObservation\\mcSubscribe.vue"}, {"text": "补缺消息数", "file": "components\\mcDataObservation\\mcSubscribe.vue"}, {"text": "客户端", "file": "components\\mcDataObservation\\mcSubscribe.vue"}, {"text": "订阅编号", "file": "components\\mcDataObservation\\mcSubscribe.vue"}, {"text": "订阅时间", "file": "components\\mcDataObservation\\mcSubscribe.vue"}, {"text": "订阅方式", "file": "components\\mcDataObservation\\mcSubscribe.vue"}, {"text": "广播消费", "file": "components\\mcDataObservation\\mcSubscribe.vue"}, {"text": "集群消费", "file": "components\\mcDataObservation\\mcSubscribe.vue"}, {"text": "订阅主题", "file": "components\\mcDataObservation\\mcSubscribe.vue"}, {"text": "消费者信息", "file": "components\\mcDataObservation\\mcSubscribe.vue"}]}, "components\\mcDataObservation\\mcTopic.vue": {"count": 26, "texts": [{"text": "分区信息", "file": "components\\mcDataObservation\\mcTopic.vue"}, {"text": "主题描述", "file": "components\\mcDataObservation\\mcTopic.vue"}, {"text": "分区数", "file": "components\\mcDataObservation\\mcTopic.vue"}, {"text": "分区副本数", "file": "components\\mcDataObservation\\mcTopic.vue"}, {"text": "是否全局主题", "file": "components\\mcDataObservation\\mcTopic.vue"}, {"text": "有序级别", "file": "components\\mcDataObservation\\mcTopic.vue"}, {"text": "业务校验", "file": "components\\mcDataObservation\\mcTopic.vue"}, {"text": "可靠级别", "file": "components\\mcDataObservation\\mcTopic.vue"}, {"text": "消息有效期", "file": "components\\mcDataObservation\\mcTopic.vue"}, {"text": "服务端是否保存消费者偏移", "file": "components\\mcDataObservation\\mcTopic.vue"}, {"text": "生产者数", "file": "components\\mcDataObservation\\mcTopic.vue"}, {"text": "消费者数", "file": "components\\mcDataObservation\\mcTopic.vue"}, {"text": "主分区所在节点", "file": "components\\mcDataObservation\\mcTopic.vue"}, {"text": "副本分区所在节点", "file": "components\\mcDataObservation\\mcTopic.vue"}, {"text": "持久化消息数", "file": "components\\mcDataObservation\\mcTopic.vue"}, {"text": "消费者实例名称", "file": "components\\mcDataObservation\\mcTopic.vue"}, {"text": "主题前缀", "file": "components\\mcDataObservation\\mcTopic.vue"}, {"text": "已推送消息数", "file": "components\\mcDataObservation\\mcTopic.vue"}, {"text": "补缺个数", "file": "components\\mcDataObservation\\mcTopic.vue"}, {"text": "过滤条件值", "file": "components\\mcDataObservation\\mcTopic.vue"}, {"text": "发布的消息数", "file": "components\\mcDataObservation\\mcTopic.vue"}, {"text": "分区有序", "file": "components\\mcDataObservation\\mcTopic.vue"}, {"text": "全局有序", "file": "components\\mcDataObservation\\mcTopic.vue"}, {"text": "文件", "file": "components\\mcDataObservation\\mcTopic.vue"}, {"text": "内存", "file": "components\\mcDataObservation\\mcTopic.vue"}, {"text": "主题信息", "file": "components\\mcDataObservation\\mcTopic.vue"}]}, "components\\mcDeploy\\mcTopicDeploy.vue": {"count": 6, "texts": [{"text": "请输入主题名", "file": "components\\mcDeploy\\mcTopicDeploy.vue"}, {"text": "上次更新日期", "file": "components\\mcDeploy\\mcTopicDeploy.vue"}, {"text": "主题编号", "file": "components\\mcDeploy\\mcTopicDeploy.vue"}, {"text": "副本数", "file": "components\\mcDeploy\\mcTopicDeploy.vue"}, {"text": "上次更新时间", "file": "components\\mcDeploy\\mcTopicDeploy.vue"}, {"text": "动态主题同步", "file": "components\\mcDeploy\\mcTopicDeploy.vue"}]}, "components\\mdbDataObservation\\generalView.vue": {"count": 19, "texts": [{"text": "数据库名称", "file": "components\\mdbDataObservation\\generalView.vue"}, {"text": ", // 文本\r\n                            label:", "file": "components\\mdbDataObservation\\generalView.vue"}, {"text": "数据库表个数", "file": "components\\mdbDataObservation\\generalView.vue"}, {"text": "主控进程号", "file": "components\\mdbDataObservation\\generalView.vue"}, {"text": "加锁进程号", "file": "components\\mdbDataObservation\\generalView.vue"}, {"text": "已处理事务号", "file": "components\\mdbDataObservation\\generalView.vue"}, {"text": "事务处理性能", "file": "components\\mdbDataObservation\\generalView.vue"}, {"text": "事务处理总数", "file": "components\\mdbDataObservation\\generalView.vue"}, {"text": "事务处理吞吐(笔/秒)", "file": "components\\mdbDataObservation\\generalView.vue"}, {"text": "事务处理吞吐", "file": "components\\mdbDataObservation\\generalView.vue"}, {"text": "总数(笔)", "file": "components\\mdbDataObservation\\generalView.vue"}, {"text": "吞吐(笔/秒)", "file": "components\\mdbDataObservation\\generalView.vue"}, {"text": "内存使用分布", "file": "components\\mdbDataObservation\\generalView.vue"}, {"text": ", // 文本\r\n                        label:", "file": "components\\mdbDataObservation\\generalView.vue"}, {"text": "内存数据库版本信息", "file": "components\\mdbDataObservation\\generalView.vue"}, {"text": "工作进程配置", "file": "components\\mdbDataObservation\\generalView.vue"}, {"text": "AdminFile文件", "file": "components\\mdbDataObservation\\generalView.vue"}, {"text": "事务处理配置", "file": "components\\mdbDataObservation\\generalView.vue"}, {"text": "内存分配配置", "file": "components\\mdbDataObservation\\generalView.vue"}]}, "components\\mdbDataObservation\\memory.vue": {"count": 8, "texts": [{"text": "总记录数", "file": "components\\mdbDataObservation\\memory.vue"}, {"text": "占已使用内存比率", "file": "components\\mdbDataObservation\\memory.vue"}, {"text": "请搜索内存表", "file": "components\\mdbDataObservation\\memory.vue"}, {"text": "表记录数增长统计", "file": "components\\mdbDataObservation\\memory.vue"}, {"text": "表内存使用增长统计", "file": "components\\mdbDataObservation\\memory.vue"}, {"text": "表内存使用分布", "file": "components\\mdbDataObservation\\memory.vue"}, {"text": "表数据存储-文件", "file": "components\\mdbDataObservation\\memory.vue"}, {"text": "表索引信息", "file": "components\\mdbDataObservation\\memory.vue"}]}, "components\\mdbDataObservation\\performance.vue": {"count": 10, "texts": [{"text": "性能分析开关", "file": "components\\mdbDataObservation\\performance.vue"}, {"text": "执行详情", "file": "components\\mdbDataObservation\\performance.vue"}, {"text": "事务控制器ID", "file": "components\\mdbDataObservation\\performance.vue"}, {"text": "执行次数", "file": "components\\mdbDataObservation\\performance.vue"}, {"text": "平均耗时", "file": "components\\mdbDataObservation\\performance.vue"}, {"text": "最小耗时", "file": "components\\mdbDataObservation\\performance.vue"}, {"text": "前十最大耗时", "file": "components\\mdbDataObservation\\performance.vue"}, {"text": "标准差", "file": "components\\mdbDataObservation\\performance.vue"}, {"text": "开启 (正常级别)", "file": "components\\mdbDataObservation\\performance.vue"}, {"text": "开启 (基本级别)", "file": "components\\mdbDataObservation\\performance.vue"}]}, "components\\mdbDataObservation\\processor.vue": {"count": 8, "texts": [{"text": "事务处理吞吐 - Top10", "file": "components\\mdbDataObservation\\processor.vue"}, {"text": "死锁检测", "file": "components\\mdbDataObservation\\processor.vue"}, {"text": "只看被阻塞控制器", "file": "components\\mdbDataObservation\\processor.vue"}, {"text": "只看工作控制器", "file": "components\\mdbDataObservation\\processor.vue"}, {"text": "Undo文件信息", "file": "components\\mdbDataObservation\\processor.vue"}, {"text": "Undo文件记录数信息", "file": "components\\mdbDataObservation\\processor.vue"}, {"text": "Undo事务号: -", "file": "components\\mdbDataObservation\\processor.vue"}, {"text": "Undo事务号:${data.CommitSqn || '-'}", "file": "components\\mdbDataObservation\\processor.vue"}]}, "components\\mdbDataObservation\\slowTransaction.vue": {"count": 5, "texts": [{"text": "执行时间", "file": "components\\mdbDataObservation\\slowTransaction.vue"}, {"text": "事务控制器", "file": "components\\mdbDataObservation\\slowTransaction.vue"}, {"text": "耗时（ms）", "file": "components\\mdbDataObservation\\slowTransaction.vue"}, {"text": "处理表", "file": "components\\mdbDataObservation\\slowTransaction.vue"}, {"text": "处理表明细", "file": "components\\mdbDataObservation\\slowTransaction.vue"}]}, "components\\mdbPrivilegeManage\\roleManage.vue": {"count": 19, "texts": [{"text": ">添加角色</a-button>\r\n                <a-button type=", "file": "components\\mdbPrivilegeManage\\roleManage.vue"}, {"text": "角色名：", "file": "components\\mdbPrivilegeManage\\roleManage.vue"}, {"text": "请输入角色名", "file": "components\\mdbPrivilegeManage\\roleManage.vue"}, {"text": "角色名", "file": "components\\mdbPrivilegeManage\\roleManage.vue"}, {"text": "角色未配置权限", "file": "components\\mdbPrivilegeManage\\roleManage.vue"}, {"text": "角色描述", "file": "components\\mdbPrivilegeManage\\roleManage.vue"}, {"text": "修改时间", "file": "components\\mdbPrivilegeManage\\roleManage.vue"}, {"text": "编辑", "file": "components\\mdbPrivilegeManage\\roleManage.vue"}, {"text": "权限配置", "file": "components\\mdbPrivilegeManage\\roleManage.vue"}, {"text": "绑定用户", "file": "components\\mdbPrivilegeManage\\roleManage.vue"}, {"text": "创建角色成功!", "file": "components\\mdbPrivilegeManage\\roleManage.vue"}, {"text": "修改角色成功!", "file": "components\\mdbPrivilegeManage\\roleManage.vue"}, {"text": "删除成功!", "file": "components\\mdbPrivilegeManage\\roleManage.vue"}, {"text": "read only为系统内置角色,不可删除,请勿勾选！", "file": "components\\mdbPrivilegeManage\\roleManage.vue"}, {"text": "批量删除角色", "file": "components\\mdbPrivilegeManage\\roleManage.vue"}, {"text": "确认要批量删除已选中的角色吗？", "file": "components\\mdbPrivilegeManage\\roleManage.vue"}, {"text": "确认要删除角色\"${params.row.roleName}\"吗？", "file": "components\\mdbPrivilegeManage\\roleManage.vue"}, {"text": "添加角色", "file": "components\\mdbPrivilegeManage\\roleManage.vue"}, {"text": "删除角色", "file": "components\\mdbPrivilegeManage\\roleManage.vue"}]}, "components\\mdbPrivilegeManage\\userManage.vue": {"count": 17, "texts": [{"text": "用户", "file": "components\\mdbPrivilegeManage\\userManage.vue"}, {"text": ">添加用户</a-button>\r\n                <a-button type=", "file": "components\\mdbPrivilegeManage\\userManage.vue"}, {"text": "用户名：", "file": "components\\mdbPrivilegeManage\\userManage.vue"}, {"text": "请输入用户名", "file": "components\\mdbPrivilegeManage\\userManage.vue"}, {"text": "用户名", "file": "components\\mdbPrivilegeManage\\userManage.vue"}, {"text": "用户描述", "file": "components\\mdbPrivilegeManage\\userManage.vue"}, {"text": "关联角色", "file": "components\\mdbPrivilegeManage\\userManage.vue"}, {"text": "重置密码", "file": "components\\mdbPrivilegeManage\\userManage.vue"}, {"text": "用户操作失败:", "file": "components\\mdbPrivilegeManage\\userManage.vue"}, {"text": "用户修改成功！", "file": "components\\mdbPrivilegeManage\\userManage.vue"}, {"text": "用户修改失败！", "file": "components\\mdbPrivilegeManage\\userManage.vue"}, {"text": "批量删除用户", "file": "components\\mdbPrivilegeManage\\userManage.vue"}, {"text": "确认要批量删除已选中的用户吗？", "file": "components\\mdbPrivilegeManage\\userManage.vue"}, {"text": "确认要删除用户\"${params.row.userName}\"吗？", "file": "components\\mdbPrivilegeManage\\userManage.vue"}, {"text": "确定要重置密码吗？", "file": "components\\mdbPrivilegeManage\\userManage.vue"}, {"text": "添加用户", "file": "components\\mdbPrivilegeManage\\userManage.vue"}, {"text": "删除用户", "file": "components\\mdbPrivilegeManage\\userManage.vue"}]}, "components\\networkSendAndRecevied\\netRecord.vue": {"count": 9, "texts": [{"text": "用例内容", "file": "components\\networkSendAndRecevied\\netRecord.vue"}, {"text": "用例名修改成功!", "file": "components\\networkSendAndRecevied\\netRecord.vue"}, {"text": "用例删除成功!", "file": "components\\networkSendAndRecevied\\netRecord.vue"}, {"text": "Body内容为空或格式不正确", "file": "components\\networkSendAndRecevied\\netRecord.vue"}, {"text": "用例保存成功!", "file": "components\\networkSendAndRecevied\\netRecord.vue"}, {"text": "请先选择用例", "file": "components\\networkSendAndRecevied\\netRecord.vue"}, {"text": "用例：${this.menuList?.[0]?.label}", "file": "components\\networkSendAndRecevied\\netRecord.vue"}, {"text": "用例：${item?.label}", "file": "components\\networkSendAndRecevied\\netRecord.vue"}, {"text": "您确定删除 '${item.label}' 用例？", "file": "components\\networkSendAndRecevied\\netRecord.vue"}]}, "components\\networkSendAndRecevied\\netResource.vue": {"count": 21, "texts": [{"text": "抓包中", "file": "components\\networkSendAndRecevied\\netResource.vue"}, {"text": "功能号：", "file": "components\\networkSendAndRecevied\\netResource.vue"}, {"text": "消息体", "file": "components\\networkSendAndRecevied\\netResource.vue"}, {"text": "消息列表", "file": "components\\networkSendAndRecevied\\netResource.vue"}, {"text": "清空", "file": "components\\networkSendAndRecevied\\netResource.vue"}, {"text": "用户自定义", "file": "components\\networkSendAndRecevied\\netResource.vue"}, {"text": "抓包时间", "file": "components\\networkSendAndRecevied\\netResource.vue"}, {"text": "包类型", "file": "components\\networkSendAndRecevied\\netResource.vue"}, {"text": "附加信息", "file": "components\\networkSendAndRecevied\\netResource.vue"}, {"text": "开启抓包失败", "file": "components\\networkSendAndRecevied\\netResource.vue"}, {"text": "停止抓包失败", "file": "components\\networkSendAndRecevied\\netResource.vue"}, {"text": "消息列表已重置！", "file": "components\\networkSendAndRecevied\\netResource.vue"}, {"text": "当前页为首页无法支持翻页，请刷新列表或用序号跳转至您想查询的位置", "file": "components\\networkSendAndRecevied\\netResource.vue"}, {"text": "当前页为最后一页无法支持翻页，请刷新列表或用序号跳转至您想查询的位置", "file": "components\\networkSendAndRecevied\\netResource.vue"}, {"text": "确定清空抓包数据吗?", "file": "components\\networkSendAndRecevied\\netResource.vue"}, {"text": "无托管应用节点", "file": "components\\networkSendAndRecevied\\netResource.vue"}, {"text": "一个应用节点仅能开启一个抓包任务", "file": "components\\networkSendAndRecevied\\netResource.vue"}, {"text": "开始抓包", "file": "components\\networkSendAndRecevied\\netResource.vue"}, {"text": "停止抓包", "file": "components\\networkSendAndRecevied\\netResource.vue"}, {"text": "共 {{ msgTotal }} 条", "file": "components\\networkSendAndRecevied\\netResource.vue"}, {"text": "跳转序号", "file": "components\\networkSendAndRecevied\\netResource.vue"}]}, "components\\networkSendAndRecevied\\netSend.vue": {"count": 22, "texts": [{"text": "接入点", "file": "components\\networkSendAndRecevied\\netSend.vue"}, {"text": "图片无法显示", "file": "components\\networkSendAndRecevied\\netSend.vue"}, {"text": ">开</div>\r\n                            <div slot=", "file": "components\\networkSendAndRecevied\\netSend.vue"}, {"text": "输入用例名", "file": "components\\networkSendAndRecevied\\netSend.vue"}, {"text": ": {}\r\n        * 2. 关闭时间戳开关：删除40号域，即删除", "file": "components\\networkSendAndRecevied\\netSend.vue"}, {"text": "请求", "file": "components\\networkSendAndRecevied\\netSend.vue"}, {"text": "响应", "file": "components\\networkSendAndRecevied\\netSend.vue"}, {"text": "请求Body内容为空或格式不正确", "file": "components\\networkSendAndRecevied\\netSend.vue"}, {"text": "发包成功！", "file": "components\\networkSendAndRecevied\\netSend.vue"}, {"text": "无可用接入点", "file": "components\\networkSendAndRecevied\\netSend.vue"}, {"text": "请至“产品服务配置-产品服务网关”", "file": "components\\networkSendAndRecevied\\netSend.vue"}, {"text": "时间戳 &nbsp;", "file": "components\\networkSendAndRecevied\\netSend.vue"}, {"text": "开", "file": "components\\networkSendAndRecevied\\netSend.vue"}, {"text": "关", "file": "components\\networkSendAndRecevied\\netSend.vue"}, {"text": "发送", "file": "components\\networkSendAndRecevied\\netSend.vue"}, {"text": "&nbsp;&nbsp;&nbsp;用例列表", "file": "components\\networkSendAndRecevied\\netSend.vue"}, {"text": "引用", "file": "components\\networkSendAndRecevied\\netSend.vue"}, {"text": "无内容", "file": "components\\networkSendAndRecevied\\netSend.vue"}, {"text": "保存为用例", "file": "components\\networkSendAndRecevied\\netSend.vue"}, {"text": "发包调用总耗时：", "file": "components\\networkSendAndRecevied\\netSend.vue"}, {"text": "发包调用耗时：", "file": "components\\networkSendAndRecevied\\netSend.vue"}, {"text": "查看时间戳", "file": "components\\networkSendAndRecevied\\netSend.vue"}]}, "components\\networkSendAndRecevied\\netThrouth.vue": {"count": 4, "texts": [{"text": "日志列表", "file": "components\\networkSendAndRecevied\\netThrouth.vue"}, {"text": "支持输入多个，采用英文分号区分", "file": "components\\networkSendAndRecevied\\netThrouth.vue"}, {"text": "请输入功能号以英文分号形式间隔", "file": "components\\networkSendAndRecevied\\netThrouth.vue"}, {"text": "获取数据", "file": "components\\networkSendAndRecevied\\netThrouth.vue"}]}, "components\\productDataStorage\\archiveModal.vue": {"count": 23, "texts": [{"text": "数据冷备", "file": "components\\productDataStorage\\archiveModal.vue"}, {"text": "归档服务器:", "file": "components\\productDataStorage\\archiveModal.vue"}, {"text": "归档目录:", "file": "components\\productDataStorage\\archiveModal.vue"}, {"text": "产品元数据:", "file": "components\\productDataStorage\\archiveModal.vue"}, {"text": "产品遥测数据:", "file": "components\\productDataStorage\\archiveModal.vue"}, {"text": "数据日期范围:", "file": "components\\productDataStorage\\archiveModal.vue"}, {"text": ">下一步</a-button>\r\n                <a-button v-if=", "file": "components\\productDataStorage\\archiveModal.vue"}, {"text": ">取消</a-button>\r\n                <a-button v-if=", "file": "components\\productDataStorage\\archiveModal.vue"}, {"text": "选择归档条件", "file": "components\\productDataStorage\\archiveModal.vue"}, {"text": "归档条件确认", "file": "components\\productDataStorage\\archiveModal.vue"}, {"text": "时延跟踪数据", "file": "components\\productDataStorage\\archiveModal.vue"}, {"text": "监控指标数据", "file": "components\\productDataStorage\\archiveModal.vue"}, {"text": "归档指令发送成功！", "file": "components\\productDataStorage\\archiveModal.vue"}, {"text": "归档失败!", "file": "components\\productDataStorage\\archiveModal.vue"}, {"text": "{{ !nextStatus ? '选择归档条件':'归档条件确认'}}", "file": "components\\productDataStorage\\archiveModal.vue"}, {"text": "归档服务器：", "file": "components\\productDataStorage\\archiveModal.vue"}, {"text": "归档目录：", "file": "components\\productDataStorage\\archiveModal.vue"}, {"text": "产品元数据：", "file": "components\\productDataStorage\\archiveModal.vue"}, {"text": "产品遥测数据：", "file": "components\\productDataStorage\\archiveModal.vue"}, {"text": "数据日期范围：", "file": "components\\productDataStorage\\archiveModal.vue"}, {"text": "至", "file": "components\\productDataStorage\\archiveModal.vue"}, {"text": "下一步", "file": "components\\productDataStorage\\archiveModal.vue"}, {"text": "归档", "file": "components\\productDataStorage\\archiveModal.vue"}]}, "components\\productDataStorage\\autoConditionModal.vue": {"count": 20, "texts": [{"text": "每日定时清理", "file": "components\\productDataStorage\\autoConditionModal.vue"}, {"text": "数据保留天数：", "file": "components\\productDataStorage\\autoConditionModal.vue"}, {"text": "最大3600天", "file": "components\\productDataStorage\\autoConditionModal.vue"}, {"text": "选择清理时间", "file": "components\\productDataStorage\\autoConditionModal.vue"}, {"text": "是否启用：", "file": "components\\productDataStorage\\autoConditionModal.vue"}, {"text": ">查询</a-button>\r\n                <a-button style=", "file": "components\\productDataStorage\\autoConditionModal.vue"}, {"text": "清理日期：", "file": "components\\productDataStorage\\autoConditionModal.vue"}, {"text": "清理结果：", "file": "components\\productDataStorage\\autoConditionModal.vue"}, {"text": "数据保留天数1~3600天", "file": "components\\productDataStorage\\autoConditionModal.vue"}, {"text": "清理时间", "file": "components\\productDataStorage\\autoConditionModal.vue"}, {"text": "清理结果", "file": "components\\productDataStorage\\autoConditionModal.vue"}, {"text": "失败信息", "file": "components\\productDataStorage\\autoConditionModal.vue"}, {"text": "被清理数据范围", "file": "components\\productDataStorage\\autoConditionModal.vue"}, {"text": "索引号", "file": "components\\productDataStorage\\autoConditionModal.vue"}, {"text": "被清理数据大小", "file": "components\\productDataStorage\\autoConditionModal.vue"}, {"text": "定时清理配置成功！", "file": "components\\productDataStorage\\autoConditionModal.vue"}, {"text": "清理配置", "file": "components\\productDataStorage\\autoConditionModal.vue"}, {"text": "(启用后生效，每日定时执行清理操作)", "file": "components\\productDataStorage\\autoConditionModal.vue"}, {"text": "定时清理记录", "file": "components\\productDataStorage\\autoConditionModal.vue"}, {"text": "重置", "file": "components\\productDataStorage\\autoConditionModal.vue"}]}, "components\\productDataStorage\\conditionModal.vue": {"count": 6, "texts": [{"text": "产品节点：", "file": "components\\productDataStorage\\conditionModal.vue"}, {"text": "数据日期：", "file": "components\\productDataStorage\\conditionModal.vue"}, {"text": "资金账号：", "file": "components\\productDataStorage\\conditionModal.vue"}, {"text": "请限制清理日期小于30天", "file": "components\\productDataStorage\\conditionModal.vue"}, {"text": "清理指令发送成功！", "file": "components\\productDataStorage\\conditionModal.vue"}, {"text": "清理", "file": "components\\productDataStorage\\conditionModal.vue"}]}, "components\\productDataStorage\\conditionModal1.vue": {"count": 11, "texts": [{"text": "数据清理配置", "file": "components\\productDataStorage\\conditionModal1.vue"}, {"text": "产品节点:", "file": "components\\productDataStorage\\conditionModal1.vue"}, {"text": "数据日期:", "file": "components\\productDataStorage\\conditionModal1.vue"}, {"text": "数据类型:", "file": "components\\productDataStorage\\conditionModal1.vue"}, {"text": "选择清理条件", "file": "components\\productDataStorage\\conditionModal1.vue"}, {"text": "清理条件确认", "file": "components\\productDataStorage\\conditionModal1.vue"}, {"text": "数据清理失败!", "file": "components\\productDataStorage\\conditionModal1.vue"}, {"text": "{{ !nextStatus ? '选择清理条件':'清理条件确认'}}", "file": "components\\productDataStorage\\conditionModal1.vue"}, {"text": "数据类型：", "file": "components\\productDataStorage\\conditionModal1.vue"}, {"text": "警告: 该操作将物理删除符合条件的所有数据, 操作不可逆!", "file": "components\\productDataStorage\\conditionModal1.vue"}, {"text": "确认清理", "file": "components\\productDataStorage\\conditionModal1.vue"}]}, "components\\productDataStorage\\fileListModal.vue": {"count": 6, "texts": [{"text": "待归档数据清单", "file": "components\\productDataStorage\\fileListModal.vue"}, {"text": "文件条数", "file": "components\\productDataStorage\\fileListModal.vue"}, {"text": "占用空间", "file": "components\\productDataStorage\\fileListModal.vue"}, {"text": "总计:索引文件:", "file": "components\\productDataStorage\\fileListModal.vue"}, {"text": "个; 文件条数:", "file": "components\\productDataStorage\\fileListModal.vue"}, {"text": "条; 存储空间:", "file": "components\\productDataStorage\\fileListModal.vue"}]}, "components\\productServiceConfig\\linkTopoConfig.vue": {"count": 12, "texts": [{"text": "不支持该特性", "file": "components\\productServiceConfig\\linkTopoConfig.vue"}, {"text": "是否支持该特性", "file": "components\\productServiceConfig\\linkTopoConfig.vue"}, {"text": "不支持", "file": "components\\productServiceConfig\\linkTopoConfig.vue"}, {"text": "已支持业务链路模型列表", "file": "components\\productServiceConfig\\linkTopoConfig.vue"}, {"text": "业务链路度量模型", "file": "components\\productServiceConfig\\linkTopoConfig.vue"}, {"text": "选择模型", "file": "components\\productServiceConfig\\linkTopoConfig.vue"}, {"text": "业务系统类型", "file": "components\\productServiceConfig\\linkTopoConfig.vue"}, {"text": "模型预览", "file": "components\\productServiceConfig\\linkTopoConfig.vue"}, {"text": "模型配置成功", "file": "components\\productServiceConfig\\linkTopoConfig.vue"}, {"text": "切换模型失败", "file": "components\\productServiceConfig\\linkTopoConfig.vue"}, {"text": "确定要切换度量模型？", "file": "components\\productServiceConfig\\linkTopoConfig.vue"}, {"text": "切换度量模型，本产品节点下对应的「链路日志配置」将自动同步更新。本次变动需要重启采集子系统才可生效。", "file": "components\\productServiceConfig\\linkTopoConfig.vue"}]}, "components\\productServiceConfig\\linkTopoReview.vue": {"count": 11, "texts": [{"text": "链路数据定义", "file": "components\\productServiceConfig\\linkTopoReview.vue"}, {"text": "应用全链路时延度量模型", "file": "components\\productServiceConfig\\linkTopoReview.vue"}, {"text": "模型名称", "file": "components\\productServiceConfig\\linkTopoReview.vue"}, {"text": "业务系统", "file": "components\\productServiceConfig\\linkTopoReview.vue"}, {"text": "业务系统版本", "file": "components\\productServiceConfig\\linkTopoReview.vue"}, {"text": "模型版本", "file": "components\\productServiceConfig\\linkTopoReview.vue"}, {"text": "简介", "file": "components\\productServiceConfig\\linkTopoReview.vue"}, {"text": "跨度定义", "file": "components\\productServiceConfig\\linkTopoReview.vue"}, {"text": "跨度名称", "file": "components\\productServiceConfig\\linkTopoReview.vue"}, {"text": "字段明细", "file": "components\\productServiceConfig\\linkTopoReview.vue"}, {"text": "链路数据", "file": "components\\productServiceConfig\\linkTopoReview.vue"}]}, "components\\productServiceConfig\\logSourceConfig.vue": {"count": 8, "texts": [{"text": "链路模型", "file": "components\\productServiceConfig\\logSourceConfig.vue"}, {"text": "日志类型", "file": "components\\productServiceConfig\\logSourceConfig.vue"}, {"text": "时延日志输出目录", "file": "components\\productServiceConfig\\logSourceConfig.vue"}, {"text": "时延日志关键字", "file": "components\\productServiceConfig\\logSourceConfig.vue"}, {"text": "同步配置成功", "file": "components\\productServiceConfig\\logSourceConfig.vue"}, {"text": "您确认要删除\"${row.instanceName}\"的链路日志配置吗？", "file": "components\\productServiceConfig\\logSourceConfig.vue"}, {"text": "当前已配置链路模型:", "file": "components\\productServiceConfig\\logSourceConfig.vue"}, {"text": "同步配置", "file": "components\\productServiceConfig\\logSourceConfig.vue"}]}, "components\\productServiceConfig\\manageFunctionMeta.vue": {"count": 10, "texts": [{"text": ">入参说明：</div>\r\n                    <div class=", "file": "components\\productServiceConfig\\manageFunctionMeta.vue"}, {"text": ">入参示例：</div>\r\n                    <div class=", "file": "components\\productServiceConfig\\manageFunctionMeta.vue"}, {"text": ">出参说明：</div>\r\n                    <div class=", "file": "components\\productServiceConfig\\manageFunctionMeta.vue"}, {"text": ">出参示例：</div>\r\n                    <div class=", "file": "components\\productServiceConfig\\manageFunctionMeta.vue"}, {"text": "更新日期：", "file": "components\\productServiceConfig\\manageFunctionMeta.vue"}, {"text": "{{  child.funcNameCn || '暂无'}}", "file": "components\\productServiceConfig\\manageFunctionMeta.vue"}, {"text": "入参说明：", "file": "components\\productServiceConfig\\manageFunctionMeta.vue"}, {"text": "入参示例：", "file": "components\\productServiceConfig\\manageFunctionMeta.vue"}, {"text": "出参说明：", "file": "components\\productServiceConfig\\manageFunctionMeta.vue"}, {"text": "出参示例：", "file": "components\\productServiceConfig\\manageFunctionMeta.vue"}]}, "components\\productTimeAnalysis\\exportFileModal.vue": {"count": 5, "texts": [{"text": "查询结果导出", "file": "components\\productTimeAnalysis\\exportFileModal.vue"}, {"text": "导出文件名:", "file": "components\\productTimeAnalysis\\exportFileModal.vue"}, {"text": "请输入查询名称", "file": "components\\productTimeAnalysis\\exportFileModal.vue"}, {"text": "导出字段:", "file": "components\\productTimeAnalysis\\exportFileModal.vue"}, {"text": "保存成功!", "file": "components\\productTimeAnalysis\\exportFileModal.vue"}]}, "components\\productTimeAnalysis\\saveModal.vue": {"count": 8, "texts": [{"text": "保存查询", "file": "components\\productTimeAnalysis\\saveModal.vue"}, {"text": "查询名称", "file": "components\\productTimeAnalysis\\saveModal.vue"}, {"text": "字符长度数不得超过15!", "file": "components\\productTimeAnalysis\\saveModal.vue"}, {"text": "不能为空", "file": "components\\productTimeAnalysis\\saveModal.vue"}, {"text": "每日", "file": "components\\productTimeAnalysis\\saveModal.vue"}, {"text": "昨日", "file": "components\\productTimeAnalysis\\saveModal.vue"}, {"text": "本周", "file": "components\\productTimeAnalysis\\saveModal.vue"}, {"text": "本月", "file": "components\\productTimeAnalysis\\saveModal.vue"}]}, "components\\rcmBacklogMonitor\\rcmBacklog.vue": {"count": 2, "texts": [{"text": "上下文消息积压列表", "file": "components\\rcmBacklogMonitor\\rcmBacklog.vue"}, {"text": "积压数大于0", "file": "components\\rcmBacklogMonitor\\rcmBacklog.vue"}]}, "components\\rcmDeploy\\rcmConfigModel.vue": {"count": 18, "texts": [{"text": ">主题模板</span>\r\n            <div class=", "file": "components\\rcmDeploy\\rcmConfigModel.vue"}, {"text": ">通用上下文模板</span>\r\n            <div class=", "file": "components\\rcmDeploy\\rcmConfigModel.vue"}, {"text": ">集群上下文模板</span>\r\n            <div class=", "file": "components\\rcmDeploy\\rcmConfigModel.vue"}, {"text": "继承关系", "file": "components\\rcmDeploy\\rcmConfigModel.vue"}, {"text": "未选中模板", "file": "components\\rcmDeploy\\rcmConfigModel.vue"}, {"text": "配置详情", "file": "components\\rcmDeploy\\rcmConfigModel.vue"}, {"text": "模板名称:", "file": "components\\rcmDeploy\\rcmConfigModel.vue"}, {"text": "继承模板:", "file": "components\\rcmDeploy\\rcmConfigModel.vue"}, {"text": "暂无配置数据", "file": "components\\rcmDeploy\\rcmConfigModel.vue"}, {"text": ",   // 模板名单独保存\r\n            type:", "file": "components\\rcmDeploy\\rcmConfigModel.vue"}, {"text": "通用上下文", "file": "components\\rcmDeploy\\rcmConfigModel.vue"}, {"text": "主题模板更新成功", "file": "components\\rcmDeploy\\rcmConfigModel.vue"}, {"text": "上下文模板更新成功", "file": "components\\rcmDeploy\\rcmConfigModel.vue"}, {"text": "您确定删除名为 ${keyName} 的${obj[type]}模板？", "file": "components\\rcmDeploy\\rcmConfigModel.vue"}, {"text": "主题模板", "file": "components\\rcmDeploy\\rcmConfigModel.vue"}, {"text": "创建模板", "file": "components\\rcmDeploy\\rcmConfigModel.vue"}, {"text": "通用上下文模板", "file": "components\\rcmDeploy\\rcmConfigModel.vue"}, {"text": "集群上下文模板", "file": "components\\rcmDeploy\\rcmConfigModel.vue"}]}, "components\\rcmDeploy\\rcmContextGroup.vue": {"count": 13, "texts": [{"text": "上下文实例分组", "file": "components\\rcmDeploy\\rcmContextGroup.vue"}, {"text": "按主题", "file": "components\\rcmDeploy\\rcmContextGroup.vue"}, {"text": "按应用", "file": "components\\rcmDeploy\\rcmContextGroup.vue"}, {"text": "按标签", "file": "components\\rcmDeploy\\rcmContextGroup.vue"}, {"text": ">总</h-col><h-col span=", "file": "components\\rcmDeploy\\rcmContextGroup.vue"}, {"text": ">收</h-col><h-col span=", "file": "components\\rcmDeploy\\rcmContextGroup.vue"}, {"text": ">总</h-row>\r\n                        <h-row style=", "file": "components\\rcmDeploy\\rcmContextGroup.vue"}, {"text": "确定删除分组名为${name}下的所有上下文？", "file": "components\\rcmDeploy\\rcmContextGroup.vue"}, {"text": "总", "file": "components\\rcmDeploy\\rcmContextGroup.vue"}, {"text": "收", "file": "components\\rcmDeploy\\rcmContextGroup.vue"}, {"text": "发", "file": "components\\rcmDeploy\\rcmContextGroup.vue"}, {"text": "查看更多", "file": "components\\rcmDeploy\\rcmContextGroup.vue"}, {"text": "批量删除", "file": "components\\rcmDeploy\\rcmContextGroup.vue"}]}, "components\\rcmDeploy\\rcmDeployContext.vue": {"count": 16, "texts": [{"text": "模板名称", "file": "components\\rcmDeploy\\rcmDeployContext.vue"}, {"text": "请输入上下文名称", "file": "components\\rcmDeploy\\rcmDeployContext.vue"}, {"text": "上下文模式", "file": "components\\rcmDeploy\\rcmDeployContext.vue"}, {"text": "请输入应用名称", "file": "components\\rcmDeploy\\rcmDeployContext.vue"}, {"text": "查询标签", "file": "components\\rcmDeploy\\rcmDeployContext.vue"}, {"text": "请选择查询标签", "file": "components\\rcmDeploy\\rcmDeployContext.vue"}, {"text": "中心名", "file": "components\\rcmDeploy\\rcmDeployContext.vue"}, {"text": "创建/配置上下文成功!", "file": "components\\rcmDeploy\\rcmDeployContext.vue"}, {"text": "您确认要批量删除已选中的上下文吗", "file": "components\\rcmDeploy\\rcmDeployContext.vue"}, {"text": "所选项未绑定标签", "file": "components\\rcmDeploy\\rcmDeployContext.vue"}, {"text": "添加标签", "file": "components\\rcmDeploy\\rcmDeployContext.vue"}, {"text": "删除标签", "file": "components\\rcmDeploy\\rcmDeployContext.vue"}, {"text": "添加标签成功!", "file": "components\\rcmDeploy\\rcmDeployContext.vue"}, {"text": "删除标签成功!", "file": "components\\rcmDeploy\\rcmDeployContext.vue"}, {"text": "您确认要删除名为\"${params.row.name}\"的上下文吗？", "file": "components\\rcmDeploy\\rcmDeployContext.vue"}, {"text": "同时删除所有匹配记录（共${this.total}条）", "file": "components\\rcmDeploy\\rcmDeployContext.vue"}]}, "components\\rcmDeploy\\rcmDeployTopic.vue": {"count": 12, "texts": [{"text": "请输入在1-65535之间的值", "file": "components\\rcmDeploy\\rcmDeployTopic.vue"}, {"text": "主题名称", "file": "components\\rcmDeploy\\rcmDeployTopic.vue"}, {"text": "请输入主题名称", "file": "components\\rcmDeploy\\rcmDeployTopic.vue"}, {"text": "请输入分区号", "file": "components\\rcmDeploy\\rcmDeployTopic.vue"}, {"text": "只支持输入数字", "file": "components\\rcmDeploy\\rcmDeployTopic.vue"}, {"text": "通讯地址", "file": "components\\rcmDeploy\\rcmDeployTopic.vue"}, {"text": "通讯端口", "file": "components\\rcmDeploy\\rcmDeployTopic.vue"}, {"text": "创建/配置主题成功!", "file": "components\\rcmDeploy\\rcmDeployTopic.vue"}, {"text": "请选择一条或多条数据", "file": "components\\rcmDeploy\\rcmDeployTopic.vue"}, {"text": "批量删除主题", "file": "components\\rcmDeploy\\rcmDeployTopic.vue"}, {"text": "您确认要批量删除已选中的主题吗？", "file": "components\\rcmDeploy\\rcmDeployTopic.vue"}, {"text": "您确认要删除名为\"${params.row.topic}\"的主题吗？", "file": "components\\rcmDeploy\\rcmDeployTopic.vue"}]}, "components\\rcmDeploy\\rcmOverview.vue": {"count": 11, "texts": [{"text": "端口", "file": "components\\rcmDeploy\\rcmOverview.vue"}, {"text": "您确认要删除该行配置吗？", "file": "components\\rcmDeploy\\rcmOverview.vue"}, {"text": "传输地址：", "file": "components\\rcmDeploy\\rcmOverview.vue"}, {"text": "传输端口：", "file": "components\\rcmDeploy\\rcmOverview.vue"}, {"text": "集群同步地址：", "file": "components\\rcmDeploy\\rcmOverview.vue"}, {"text": "集群同步端口：", "file": "components\\rcmDeploy\\rcmOverview.vue"}, {"text": "补缺端口：", "file": "components\\rcmDeploy\\rcmOverview.vue"}, {"text": "上下文ID范围：", "file": "components\\rcmDeploy\\rcmOverview.vue"}, {"text": "主题ID范围：", "file": "components\\rcmDeploy\\rcmOverview.vue"}, {"text": "分区范围：", "file": "components\\rcmDeploy\\rcmOverview.vue"}, {"text": "新增", "file": "components\\rcmDeploy\\rcmOverview.vue"}]}, "components\\rcmDeploy\\rcmSourceFile.vue": {"count": 16, "texts": [{"text": "待发布配置", "file": "components\\rcmDeploy\\rcmSourceFile.vue"}, {"text": "已发布配置", "file": "components\\rcmDeploy\\rcmSourceFile.vue"}, {"text": "已发布配置告警说明", "file": "components\\rcmDeploy\\rcmSourceFile.vue"}, {"text": ">远程：\r\n                    <i :class=", "file": "components\\rcmDeploy\\rcmSourceFile.vue"}, {"text": ">历史：\r\n                    <i :class=", "file": "components\\rcmDeploy\\rcmSourceFile.vue"}, {"text": "无法在线预览RCM配置文件！请下载源文件进行本地查看。", "file": "components\\rcmDeploy\\rcmSourceFile.vue"}, {"text": "服务异常", "file": "components\\rcmDeploy\\rcmSourceFile.vue"}, {"text": "配置发布", "file": "components\\rcmDeploy\\rcmSourceFile.vue"}, {"text": "配置还原", "file": "components\\rcmDeploy\\rcmSourceFile.vue"}, {"text": "本地变更：本地配置版本已经发生变更。", "file": "components\\rcmDeploy\\rcmSourceFile.vue"}, {"text": "远程变更：远程zk配置版本已发生变更。", "file": "components\\rcmDeploy\\rcmSourceFile.vue"}, {"text": "还原", "file": "components\\rcmDeploy\\rcmSourceFile.vue"}, {"text": "本地：", "file": "components\\rcmDeploy\\rcmSourceFile.vue"}, {"text": "最后更新版本：", "file": "components\\rcmDeploy\\rcmSourceFile.vue"}, {"text": "远程：", "file": "components\\rcmDeploy\\rcmSourceFile.vue"}, {"text": "历史：", "file": "components\\rcmDeploy\\rcmSourceFile.vue"}]}, "components\\rcmObservation\\deliver.vue": {"count": 16, "texts": [{"text": "发送端的ContextId", "file": "components\\rcmObservation\\deliver.vue"}, {"text": "接收端的ContextId", "file": "components\\rcmObservation\\deliver.vue"}, {"text": "消息排队", "file": "components\\rcmObservation\\deliver.vue"}, {"text": "消息投递", "file": "components\\rcmObservation\\deliver.vue"}, {"text": "缓存积压", "file": "components\\rcmObservation\\deliver.vue"}, {"text": "消息持久化", "file": "components\\rcmObservation\\deliver.vue"}, {"text": "下一个排队消息序号", "file": "components\\rcmObservation\\deliver.vue"}, {"text": "提交的消息序号", "file": "components\\rcmObservation\\deliver.vue"}, {"text": "下一个处理消息序号", "file": "components\\rcmObservation\\deliver.vue"}, {"text": "已经处理的消息数量", "file": "components\\rcmObservation\\deliver.vue"}, {"text": "消息回调失败次数统计", "file": "components\\rcmObservation\\deliver.vue"}, {"text": "集群确认消息积压", "file": "components\\rcmObservation\\deliver.vue"}, {"text": "应用投递消息积压", "file": "components\\rcmObservation\\deliver.vue"}, {"text": "待持久化消息积压", "file": "components\\rcmObservation\\deliver.vue"}, {"text": "下一个持久化消息序号", "file": "components\\rcmObservation\\deliver.vue"}, {"text": "已经持久化的消息数量", "file": "components\\rcmObservation\\deliver.vue"}]}, "components\\rcmObservation\\receiver.vue": {"count": 23, "texts": [{"text": ">\r\n    <!-- 会话信息 -->\r\n    <info-grid ref=", "file": "components\\rcmObservation\\receiver.vue"}, {"text": "会话信息", "file": "components\\rcmObservation\\receiver.vue"}, {"text": "主题分区", "file": "components\\rcmObservation\\receiver.vue"}, {"text": "消息接收", "file": "components\\rcmObservation\\receiver.vue"}, {"text": "消息应答", "file": "components\\rcmObservation\\receiver.vue"}, {"text": "下一个待接收消息序号", "file": "components\\rcmObservation\\receiver.vue"}, {"text": "收到的最大消息序号", "file": "components\\rcmObservation\\receiver.vue"}, {"text": "下一个待处理消息序号", "file": "components\\rcmObservation\\receiver.vue"}, {"text": "已经确认的消息序号", "file": "components\\rcmObservation\\receiver.vue"}, {"text": "通讯分片处理", "file": "components\\rcmObservation\\receiver.vue"}, {"text": "分片接收", "file": "components\\rcmObservation\\receiver.vue"}, {"text": "分片应答", "file": "components\\rcmObservation\\receiver.vue"}, {"text": "分片补缺", "file": "components\\rcmObservation\\receiver.vue"}, {"text": "乱序和丢包检测", "file": "components\\rcmObservation\\receiver.vue"}, {"text": "下一个待接收分片序号", "file": "components\\rcmObservation\\receiver.vue"}, {"text": "下一个待取消息分片序号", "file": "components\\rcmObservation\\receiver.vue"}, {"text": "收到的最大分片序号", "file": "components\\rcmObservation\\receiver.vue"}, {"text": "已经确认的分片号", "file": "components\\rcmObservation\\receiver.vue"}, {"text": "最后发送ACK的原因", "file": "components\\rcmObservation\\receiver.vue"}, {"text": "补缺执行", "file": "components\\rcmObservation\\receiver.vue"}, {"text": "补缺统计", "file": "components\\rcmObservation\\receiver.vue"}, {"text": "丢包检测", "file": "components\\rcmObservation\\receiver.vue"}, {"text": "丢包统计", "file": "components\\rcmObservation\\receiver.vue"}]}, "components\\rcmObservation\\transmitters.vue": {"count": 21, "texts": [{"text": ">\r\n        <!-- 会话信息 -->\r\n        <info-grid ref=", "file": "components\\rcmObservation\\transmitters.vue"}, {"text": "消息发送与应答", "file": "components\\rcmObservation\\transmitters.vue"}, {"text": "下个消息编号", "file": "components\\rcmObservation\\transmitters.vue"}, {"text": "当前最大消息号", "file": "components\\rcmObservation\\transmitters.vue"}, {"text": "上次应答消息编号", "file": "components\\rcmObservation\\transmitters.vue"}, {"text": "消息缓存与持久化", "file": "components\\rcmObservation\\transmitters.vue"}, {"text": "缓存中保存的消息的最小消息号", "file": "components\\rcmObservation\\transmitters.vue"}, {"text": "下一个待持久化的消息号", "file": "components\\rcmObservation\\transmitters.vue"}, {"text": "已持久化消息数", "file": "components\\rcmObservation\\transmitters.vue"}, {"text": "消息积压", "file": "components\\rcmObservation\\transmitters.vue"}, {"text": "缓存积压消息数", "file": "components\\rcmObservation\\transmitters.vue"}, {"text": "网络积压消息数", "file": "components\\rcmObservation\\transmitters.vue"}, {"text": "对端消息应答", "file": "components\\rcmObservation\\transmitters.vue"}, {"text": "分片发送与应答", "file": "components\\rcmObservation\\transmitters.vue"}, {"text": "下一个待分配的分片号", "file": "components\\rcmObservation\\transmitters.vue"}, {"text": "当前待发送分片号", "file": "components\\rcmObservation\\transmitters.vue"}, {"text": "最后一次收到的应答序号", "file": "components\\rcmObservation\\transmitters.vue"}, {"text": "分片发送统计", "file": "components\\rcmObservation\\transmitters.vue"}, {"text": "异步发送的分片数目", "file": "components\\rcmObservation\\transmitters.vue"}, {"text": "底层失败的分片数目", "file": "components\\rcmObservation\\transmitters.vue"}, {"text": "对端分片应答", "file": "components\\rcmObservation\\transmitters.vue"}]}, "components\\secondAppearance\\logDrawerContent.vue": {"count": 4, "texts": [{"text": "上场记录总数：", "file": "components\\secondAppearance\\logDrawerContent.vue"}, {"text": "成功记录数：", "file": "components\\secondAppearance\\logDrawerContent.vue"}, {"text": "失败记录数：", "file": "components\\secondAppearance\\logDrawerContent.vue"}, {"text": "错误信息：", "file": "components\\secondAppearance\\logDrawerContent.vue"}]}, "components\\sms\\addOrUpdateManagerModal.vue": {"count": 9, "texts": [{"text": "modalData.type ? '修改干系人信息' : '添加干系人信息'", "file": "components\\sms\\addOrUpdateManagerModal.vue"}, {"text": "请输入...", "file": "components\\sms\\addOrUpdateManagerModal.vue"}, {"text": "修改干系人信息", "file": "components\\sms\\addOrUpdateManagerModal.vue"}, {"text": "添加干系人信息", "file": "components\\sms\\addOrUpdateManagerModal.vue"}, {"text": "手机号格式不正确", "file": "components\\sms\\addOrUpdateManagerModal.vue"}, {"text": "邮箱不能为空", "file": "components\\sms\\addOrUpdateManagerModal.vue"}, {"text": "邮箱格式不正确", "file": "components\\sms\\addOrUpdateManagerModal.vue"}, {"text": "添加失败", "file": "components\\sms\\addOrUpdateManagerModal.vue"}, {"text": "修改失败", "file": "components\\sms\\addOrUpdateManagerModal.vue"}]}, "components\\sms\\noticeTempModal.vue": {"count": 10, "texts": [{"text": "告警通知模板编辑", "file": "components\\sms\\noticeTempModal.vue"}, {"text": "模板内容", "file": "components\\sms\\noticeTempModal.vue"}, {"text": "外发投资者", "file": "components\\sms\\noticeTempModal.vue"}, {"text": "选择干系人", "file": "components\\sms\\noticeTempModal.vue"}, {"text": "通知到手机", "file": "components\\sms\\noticeTempModal.vue"}, {"text": "通知到邮箱", "file": "components\\sms\\noticeTempModal.vue"}, {"text": "操作成功", "file": "components\\sms\\noticeTempModal.vue"}, {"text": "操作失败", "file": "components\\sms\\noticeTempModal.vue"}, {"text": "您确定删除名为\"${params.row.addresseeName}\"的干系人吗？", "file": "components\\sms\\noticeTempModal.vue"}, {"text": "告警通知参数存在手机号${phone}, 则发送模板短信至该手机", "file": "components\\sms\\noticeTempModal.vue"}]}, "components\\sqlTable\\tableSqlTop.vue": {"count": 13, "texts": [{"text": "!isCores ? '请选择节点或集群或服务' : '请选择节点'", "file": "components\\sqlTable\\tableSqlTop.vue"}, {"text": "title || (isCores ? 'MDB-SQL-多核心分发':'MDB-SQL')", "file": "components\\sqlTable\\tableSqlTop.vue"}, {"text": "请选择节点或集群或服务", "file": "components\\sqlTable\\tableSqlTop.vue"}, {"text": "MDB-SQL-多核心分发", "file": "components\\sqlTable\\tableSqlTop.vue"}, {"text": "登录成功!", "file": "components\\sqlTable\\tableSqlTop.vue"}, {"text": "未知用户", "file": "components\\sqlTable\\tableSqlTop.vue"}, {"text": "请重新登录!", "file": "components\\sqlTable\\tableSqlTop.vue"}, {"text": "修改密码成功!请重新登录!", "file": "components\\sqlTable\\tableSqlTop.vue"}, {"text": "; // 服务\r\n                case", "file": "components\\sqlTable\\tableSqlTop.vue"}, {"text": "登录", "file": "components\\sqlTable\\tableSqlTop.vue"}, {"text": "查看密钥", "file": "components\\sqlTable\\tableSqlTop.vue"}, {"text": "修改密码", "file": "components\\sqlTable\\tableSqlTop.vue"}, {"text": "退出登录", "file": "components\\sqlTable\\tableSqlTop.vue"}]}, "components\\transaction\\instanceTimeDelayModal.vue": {"count": 2, "texts": [{"text": "`${modalData.key}时延数据列表`", "file": "components\\transaction\\instanceTimeDelayModal.vue"}, {"text": "${modalData.key}时延数据列表", "file": "components\\transaction\\instanceTimeDelayModal.vue"}]}, "components\\transaction\\reportConfirmModal.vue": {"count": 9, "texts": [{"text": "创建报表", "file": "components\\transaction\\reportConfirmModal.vue"}, {"text": ">\r\n                <h3>报表", "file": "components\\transaction\\reportConfirmModal.vue"}, {"text": "报表创建失败!", "file": "components\\transaction\\reportConfirmModal.vue"}, {"text": "报表\"{{instanceName}}\"创建成功!", "file": "components\\transaction\\reportConfirmModal.vue"}, {"text": "交易所：", "file": "components\\transaction\\reportConfirmModal.vue"}, {"text": "席位号：", "file": "components\\transaction\\reportConfirmModal.vue"}, {"text": "开始时间：", "file": "components\\transaction\\reportConfirmModal.vue"}, {"text": "结束时间：", "file": "components\\transaction\\reportConfirmModal.vue"}, {"text": "{{instanceId ? '查看' :\r\n                    '创建'}}", "file": "components\\transaction\\reportConfirmModal.vue"}]}, "components\\transaction\\settingModal.vue": {"count": 14, "texts": [{"text": "统计分析", "file": "components\\transaction\\settingModal.vue"}, {"text": "选择市场", "file": "components\\transaction\\settingModal.vue"}, {"text": "选择链路", "file": "components\\transaction\\settingModal.vue"}, {"text": "选择指标", "file": "components\\transaction\\settingModal.vue"}, {"text": "请输入席位号", "file": "components\\transaction\\settingModal.vue"}, {"text": "请输入资金账号", "file": "components\\transaction\\settingModal.vue"}, {"text": "分位数指标", "file": "components\\transaction\\settingModal.vue"}, {"text": "同比分析", "file": "components\\transaction\\settingModal.vue"}, {"text": "基线日期", "file": "components\\transaction\\settingModal.vue"}, {"text": "基线时间", "file": "components\\transaction\\settingModal.vue"}, {"text": "同比日期", "file": "components\\transaction\\settingModal.vue"}, {"text": "同比时间", "file": "components\\transaction\\settingModal.vue"}, {"text": "每秒平均", "file": "components\\transaction\\settingModal.vue"}, {"text": "分析指标（最多只能勾选三个指标数据）", "file": "components\\transaction\\settingModal.vue"}]}, "components\\transaction\\topListModal.vue": {"count": 3, "texts": [{"text": "每秒时延订单", "file": "components\\transaction\\topListModal.vue"}, {"text": "时延 (μs)", "file": "components\\transaction\\topListModal.vue"}, {"text": "时延订单", "file": "components\\transaction\\topListModal.vue"}]}, "components\\tripartiteServiceConfig\\eccomServiceConfig.vue": {"count": 13, "texts": [{"text": "华讯数据服务", "file": "components\\tripartiteServiceConfig\\eccomServiceConfig.vue"}, {"text": "配置服务接入代理", "file": "components\\tripartiteServiceConfig\\eccomServiceConfig.vue"}, {"text": "采集器实例", "file": "components\\tripartiteServiceConfig\\eccomServiceConfig.vue"}, {"text": "接入服务类型", "file": "components\\tripartiteServiceConfig\\eccomServiceConfig.vue"}, {"text": "接入服务地址", "file": "components\\tripartiteServiceConfig\\eccomServiceConfig.vue"}, {"text": "Topic地址", "file": "components\\tripartiteServiceConfig\\eccomServiceConfig.vue"}, {"text": "已适配的业务系统", "file": "components\\tripartiteServiceConfig\\eccomServiceConfig.vue"}, {"text": "绑定产品节点", "file": "components\\tripartiteServiceConfig\\eccomServiceConfig.vue"}, {"text": "创建数据采集代理", "file": "components\\tripartiteServiceConfig\\eccomServiceConfig.vue"}, {"text": "修改数据采集代理", "file": "components\\tripartiteServiceConfig\\eccomServiceConfig.vue"}, {"text": "新增成功!", "file": "components\\tripartiteServiceConfig\\eccomServiceConfig.vue"}, {"text": "修改成功!", "file": "components\\tripartiteServiceConfig\\eccomServiceConfig.vue"}, {"text": "您确定删除 '${params.row?.instanceName}' 实例？", "file": "components\\tripartiteServiceConfig\\eccomServiceConfig.vue"}]}, "components\\ustTableVerification\\createVerificationTask.vue": {"count": 23, "texts": [{"text": "校验范围", "file": "components\\ustTableVerification\\createVerificationTask.vue"}, {"text": "集群内校验", "file": "components\\ustTableVerification\\createVerificationTask.vue"}, {"text": "源数据库", "file": "components\\ustTableVerification\\createVerificationTask.vue"}, {"text": "目标数据库", "file": "components\\ustTableVerification\\createVerificationTask.vue"}, {"text": "校验类型", "file": "components\\ustTableVerification\\createVerificationTask.vue"}, {"text": "按表总量", "file": "components\\ustTableVerification\\createVerificationTask.vue"}, {"text": "按表字段", "file": "components\\ustTableVerification\\createVerificationTask.vue"}, {"text": "校验内容", "file": "components\\ustTableVerification\\createVerificationTask.vue"}, {"text": "错误阈值", "file": "components\\ustTableVerification\\createVerificationTask.vue"}, {"text": "校验字段", "file": "components\\ustTableVerification\\createVerificationTask.vue"}, {"text": "formItems.thresholdUnit === '个'", "file": "components\\ustTableVerification\\createVerificationTask.vue"}, {"text": "`根据所选校验数据，将会新增${ tableData.length }条校验任务，请核对`", "file": "components\\ustTableVerification\\createVerificationTask.vue"}, {"text": "输入框输入范围为1~1000的正整数", "file": "components\\ustTableVerification\\createVerificationTask.vue"}, {"text": "选择校验数据", "file": "components\\ustTableVerification\\createVerificationTask.vue"}, {"text": "至少选择一张表", "file": "components\\ustTableVerification\\createVerificationTask.vue"}, {"text": "表总量", "file": "components\\ustTableVerification\\createVerificationTask.vue"}, {"text": "表字段", "file": "components\\ustTableVerification\\createVerificationTask.vue"}, {"text": "任务", "file": "components\\ustTableVerification\\createVerificationTask.vue"}, {"text": "根据所选校验数据，将会新增${ tableData.length }条校验任务，请核对", "file": "components\\ustTableVerification\\createVerificationTask.vue"}, {"text": "源数据库与目标数据库选择\"${label}\"节点重复，请修改", "file": "components\\ustTableVerification\\createVerificationTask.vue"}, {"text": "“按表总量校验”不设置错误阈值。即不论校验过程是否出错，均完成全量数据校验", "file": "components\\ustTableVerification\\createVerificationTask.vue"}, {"text": "按错误个数", "file": "components\\ustTableVerification\\createVerificationTask.vue"}, {"text": "当表字段不一致个数达到阈值时，校验任务自动停止，校验不通过", "file": "components\\ustTableVerification\\createVerificationTask.vue"}]}, "components\\ustTableVerification\\verificationTaskList.vue": {"count": 22, "texts": [{"text": ">校验任务列表</div>\r\n                <div class=", "file": "components\\ustTableVerification\\verificationTaskList.vue"}, {"text": "校验结果", "file": "components\\ustTableVerification\\verificationTaskList.vue"}, {"text": "当前执行进度", "file": "components\\ustTableVerification\\verificationTaskList.vue"}, {"text": "执行耗时", "file": "components\\ustTableVerification\\verificationTaskList.vue"}, {"text": "启动成功！", "file": "components\\ustTableVerification\\verificationTaskList.vue"}, {"text": "请选择一条或多条规则", "file": "components\\ustTableVerification\\verificationTaskList.vue"}, {"text": "停止校验中！", "file": "components\\ustTableVerification\\verificationTaskList.vue"}, {"text": "所选", "file": "components\\ustTableVerification\\verificationTaskList.vue"}, {"text": "此", "file": "components\\ustTableVerification\\verificationTaskList.vue"}, {"text": "任务删除成功！", "file": "components\\ustTableVerification\\verificationTaskList.vue"}, {"text": "任务名称:", "file": "components\\ustTableVerification\\verificationTaskList.vue"}, {"text": "源数据库:", "file": "components\\ustTableVerification\\verificationTaskList.vue"}, {"text": "目标数据库:", "file": "components\\ustTableVerification\\verificationTaskList.vue"}, {"text": "校验类型:", "file": "components\\ustTableVerification\\verificationTaskList.vue"}, {"text": "校验内容:", "file": "components\\ustTableVerification\\verificationTaskList.vue"}, {"text": "查看校验任务内容", "file": "components\\ustTableVerification\\verificationTaskList.vue"}, {"text": "确定要删除${obj}校验任务", "file": "components\\ustTableVerification\\verificationTaskList.vue"}, {"text": "删除校验任务后，任务将从列表移除，同时任务所产生的执行历史不可追溯。请谨慎操作。", "file": "components\\ustTableVerification\\verificationTaskList.vue"}, {"text": "${fileName}-${row.startTime}-比对详情.xls", "file": "components\\ustTableVerification\\verificationTaskList.vue"}, {"text": "校验任务列表", "file": "components\\ustTableVerification\\verificationTaskList.vue"}, {"text": "校验任务总数:", "file": "components\\ustTableVerification\\verificationTaskList.vue"}, {"text": "停止", "file": "components\\ustTableVerification\\verificationTaskList.vue"}]}}}