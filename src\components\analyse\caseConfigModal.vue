<template>
    <div>
        <!-- 下发配置列表 -->
        <h-msg-box-safe
            v-model="modalData.status"
            :escClose="true"
            :mask-closable="false"
            title="配置用例"
            width="50"
            height="60"
            @on-open="getConfigInfo"
        >
            <h-tabs>
                <h-tab-pane v-for="item in caseConfigList" :key="item.key" :label="item.key" :name="item.key">
                    <h-input
                        v-model="item.value"
                        type="textarea"
                        :rows="17"
                        :disabled="modalData.disabled"
                        placeholder="请输入用例配置..."
                    ></h-input>
                </h-tab-pane>
            </h-tabs>

            <template v-slot:footer>
                <a-button @click="modalData.status = false">取消</a-button>
                <a-button type="primary" :loading="loading" @click="submitConfig">确定</a-button>
            </template>
        </h-msg-box-safe>
    </div>
</template>

<script>
import { saveCase } from '@/api/httpApi';
import aButton from '@/components/common/button/aButton';
export default {
    props: {
        modalInfo: {
            type: Object,
            default: null
        }
    },
    data() {
        return {
            modalData: this.modalInfo,
            loading: false,
            caseConfigList: []
        };
    },
    methods: {
        // 解析配置信息
        getConfigInfo() {
            Object.keys(this.modalData.config).forEach(ele => {
                this.caseConfigList.push({
                    key: ele,
                    value: this.modalData.config[ele]
                });
            });
        },
        async submitConfig() {
            if (!this.modalData.disabled) {
                try {
                    this.loading = true;
                    const param = {
                        sceneId: this.modalData.sceneId,
                        testCaseId: this.modalData.testCaseId,
                        apiDemoConfig: {}
                    };
                    this.caseConfigList.forEach(ele => {
                        param.apiDemoConfig[ele.key] = ele.value;
                    });
                    const res = await saveCase(param);
                    if (res.success) {
                        this.$hMessage.success('用例配置保存成功!');
                        this.$emit('update', param.testCaseId);
                        this.modalInfo.status = false;
                    } else {
                        this.loading = false;
                        this.$hMessage.error('用例配置保存失败!');
                    }
                } catch (err) {
                    this.loading = false;
                }
            } else {
                this.modalData.status = false;
            }
        }
    },
    components: { aButton }
};
</script>

<style lang="less" scoped>
/deep/ .h-modal-body {
    padding: 6px 16px 16px;
}
</style>
