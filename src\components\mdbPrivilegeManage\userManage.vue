<template>
    <div class="manage-box">
        <normal-table ref="table" formTitle="筛选条件" tableTitle="用户" :formItems="formItems" :columns="columns" :loading="loading"
            :tableData="tableData" showTitle :hasPage="false" :hasSetTableColumns="false" @query="handleQuery" @selection='tableSelection'>
            <div slot='btns' class='table-slot-box'>
                <a-button type="primary" :disabled="editable" @click="addOrEditUserModal(false,'add')">添加用户</a-button>
                <a-button type="dark" :disabled="editable" @click="handleClear">删除用户</a-button>
            </div>
        </normal-table>
        <!-- 查看用户 -->
        <view-user-modal v-if="userInfo.status" :modalInfo="userInfo" ></view-user-modal>
        <!-- 添加、编辑用户 -->
        <add-or-edit-user-modal v-if="addOrEditUserInfo.status" :modalInfo="addOrEditUserInfo" :productId="productId" @add-or-edit-user="addOrEditUser"></add-or-edit-user-modal>
         <!-- 重置密码 -->
         <reset-password-modal v-if="resetPasswordInfo.status" :modalInfo="resetPasswordInfo" @reset-password="handleResetPassword"></reset-password-modal>
         <!-- 创建用户成功失败时的弹窗 -->
         <success-error-modal v-if="createUserSuccErrorInfo.status" key="createUser" :modalInfo="createUserSuccErrorInfo" businessType="createUser"></success-error-modal>
         <!-- 重置密码成功与失败 -->
         <success-error-modal v-if="resetPasswordSuccErrorInfo.status" key="resetPassword" :modalInfo="resetPasswordSuccErrorInfo" businessType="resetPassword" />
    </div>
</template>

<script>
import { getMdbRoles, getMdbUsers, createMdbUsers, updateMdbUsers, deleteMdbUsers, mdbResetPassword } from '@/api/mdbPrivilegeApi';
import normalTable from '@/components/common/bestTable/normalTable/normalTable';
import viewUserModal from '@/components/mdbPrivilegeManage/modal/viewUserModal.vue';
import addOrEditUserModal from '@/components/mdbPrivilegeManage/modal/addOrEditUserModal';
import resetPasswordModal from '@/components/mdbPrivilegeManage/modal/resetPasswordModal';
import successErrorModal from '@/components/mdbPrivilegeManage/modal/successErrorModal';
import aButton from '@/components/common/button/aButton';
const { SM3 } = require('gm-crypto');
export default {
    name: 'UserManage',
    components: { aButton, normalTable, addOrEditUserModal, resetPasswordModal, viewUserModal, successErrorModal },
    props: {
        productId: {
            type: String,
            default: ''
        },
        editable: {
            type: Boolean,
            default: false
        }
    },
    data() {
        return {
            loading: false,
            formItems: [
                {
                    type: 'input',
                    key: 'userName',
                    label: '用户名：',
                    value: '',
                    placeholder: '请输入用户名'
                }
            ],
            columns: [
                {
                    type: 'selection',
                    width: 60,
                    align: 'center'
                },
                {
                    title: '用户名',
                    key: 'userName',
                    ellipsis: true
                },
                {
                    title: '用户描述',
                    key: 'userDescribe',
                    ellipsis: true
                },
                {
                    title: '关联角色',
                    key: 'roles',
                    ellipsis: true,
                    render: (h, params) => {
                        const roles = params.row?.roles?.map(o => o.roleName);
                        return h(
                            'span',
                            {
                                attrs: {
                                    title: roles?.join(',') || '-'
                                }
                            },
                            roles?.join(',') || '-'
                        );
                    }
                },
                {
                    title: '修改时间',
                    key: 'gmtModified',
                    ellipsis: true
                },
                {
                    title: '操作',
                    key: 'action',
                    fixed: 'right',
                    width: 220,
                    render: (h, params) => {
                        return h('div', [
                            h('Button',
                                {
                                    props: {
                                        size: 'small',
                                        type: 'text'
                                    },
                                    on: {
                                        click: () => { this.viewUserInfoModal(params.row); }
                                    }
                                },
                                '查看'
                            ),
                            h('Button',
                                {
                                    props: {
                                        size: 'small',
                                        type: 'text',
                                        disabled: this.editable
                                    },
                                    on: {
                                        click: () => { this.addOrEditUserModal(params.row, 'edit'); }
                                    }
                                },
                                '编辑'
                            ),
                            h('Button',
                                {
                                    props: {
                                        size: 'small',
                                        type: 'text',
                                        disabled: this.editable
                                    },
                                    on: {
                                        click: () => {
                                            this.$hMsgBoxSafe.confirm({
                                                title: `删除`,
                                                content: `确认要删除用户"${params.row.userName}"吗？`,
                                                onOk: () => {
                                                    this.handleDeleteUser(params.row.id);
                                                }
                                            });
                                        }
                                    }
                                },
                                '删除'
                            ),
                            h('Button',
                                {
                                    props: {
                                        size: 'small',
                                        type: 'text',
                                        disabled: this.editable
                                    },
                                    on: {
                                        click: () => {
                                            this.handleResetPasswordModal(params.row);
                                        }
                                    }
                                },
                                '重置密码'
                            )
                        ]);
                    }
                }
            ],
            tableData: [],
            selection: [],
            // 查看
            userInfo: {
                status: false
            },
            // 增加、编辑弹窗
            addOrEditUserInfo: {
                status: false
            },
            // 重置密码
            resetPasswordInfo: {
                status: false
            },
            // 创建用户成功
            createUserSuccErrorInfo: {
                status: false
            },
            // 重置密码成功
            resetPasswordSuccErrorInfo: {
                status: false
            }
        };
    },
    mounted() {
    },
    methods: {
        // 初始化数据
        initData(){
            this.$nextTick(() => {
                this.$refs['table'].$_handleResetPageDataAndQuery();
            });
        },
        // 查询
        async handleQuery(val){
            this.selection = [];
            try {
                this.loading = true;
                const param = {
                    productId: this.productId,
                    userName: val.userName
                };
                const res = await getMdbUsers(param);
                if (res.code === '200'){
                    this.tableData = res?.data || [];
                    Array.isArray(this.tableData) && this.tableData.forEach(item => {
                        item._disabled = this.editable;
                    });
                } else if (res.code.length === 8){
                    this.$hMessage.error(res.message);
                }
                this.$emit('check-status');
                this.loading = false;
            } catch (err) {
                this.loading = false;
                this.tableData = [];
            }
        },
        // 查看
        viewUserInfoModal(param){
            this.userInfo = {
                productId: this.productId,
                userName: param.userName,
                userDescribe: param.userDescribe,
                gmtModified: param.gmtModified,
                roles: param.roles,
                status: true
            };
        },
        // 获取所有角色
        async getMdbRoles(){
            let roleList = [];
            const param = {
                productId: this.productId,
                roleName: ''
            };
            const res = await getMdbRoles(param);
            if (res.code === '200'){
                roleList = res.data;
            } else if (res.code.length === 8){
                this.$hMessage.error(res.message);
            }
            return roleList;
        },
        // 新增、编辑
        async addOrEditUserModal(param, type) {
            const roleList = await this.getMdbRoles();
            this.addOrEditUserInfo = param ? {
                ...param,
                roles: param?.roles?.map(o => o.id) || [],
                type: type,
                status: true,
                roleList: roleList
            } : {
                userName: '',
                password: '',
                userDescribe: '',
                roles: [],
                type: type,
                status: true,
                roleList: roleList
            };
        },
        async addOrEditUser(val) {
            try {
                const { param, apiFunction, successMessage } = this.getRequestConfig(val);
                const res = await apiFunction(param);

                await this.handleResponse(res, val, successMessage);
            } catch (error) {
                console.error('用户操作失败:', error);
            } finally {
                this.addOrEditUserInfo.status = false;
            }
        },
        // 基本请求参数
        getRequestConfig(val) {
            const baseConfig = {
                productId: this.productId,
                userName: val.userName,
                userDescribe: val.userDescribe,
                roles: val.roles
            };

            if (val.type === 'add') {
                return {
                    param: {
                        ...baseConfig,
                        password: SM3.digest(val.password, 'utf8', 'hex')
                    },
                    apiFunction: createMdbUsers,
                    successMessage: ''
                };
            }

            return {
                param: {
                    ...baseConfig,
                    id: val.id
                },
                apiFunction: updateMdbUsers,
                successMessage: '用户修改成功！'
            };
        },
        // 请求结果
        async handleResponse(res, val, successMessage) {
            // 用户创建、修改成功
            if (res.code === '200') {
                await this.handleSuccessResponse(val, res, successMessage);
                this.$nextTick(() => {
                    this.$refs['table'].$_handleQuery();
                });
            } else if (res.code.length === 8) {
                // 用户创建、修改失败
                this.handleErrorResponse(val, res);
            }
        },
        // 用户创建、修改成功
        async handleSuccessResponse(val, res, successMessage) {
            if (val.type === 'add') {
                this.createUserSuccErrorInfo.type = 'success';
                this.createUserSuccErrorInfo.status = true;
                this.createUserSuccErrorInfo.userName = val?.userName;
                this.createUserSuccErrorInfo.password = val?.password;
                this.createUserSuccErrorInfo.roles = val?.roleNames?.join(',') || '';
                this.createUserSuccErrorInfo.tableData = res?.data?.details || [];
            } else {
                this.$hMessage.success(successMessage);
            }
        },
        // 用户创建、修改失败
        handleErrorResponse(val, res) {
            if (val.type === 'add') {
                this.createUserSuccErrorInfo.type = 'error';
                this.createUserSuccErrorInfo.status = true;
                this.createUserSuccErrorInfo.errmsg = res?.message || '';
            } else {
                this.$hMessage.error(res?.message || '用户修改失败！');
            }
        },
        // 选中行
        tableSelection(selection){
            this.selection = selection;
        },
        // 删除行
        async handleDeleteUser(id){
            const ids = [id.toString()];
            await this.deleteUser(ids);
        },
        // 删除
        async deleteUser(ids){
            const param = {
                productId: this.productId,
                ids: ids
            };
            const res = await deleteMdbUsers(param);
            if (res.code === '200'){
                // 删除的用户和本地存储的id一致则清除token
                const id = localStorage.getItem('mdbUserId');
                if (ids.includes(id)){
                    localStorage.removeItem('mdbToken');
                    localStorage.removeItem('mdbUserId');
                }
                this.$hMessage.success('删除成功!');
                this.$refs['table'].$_handleQuery();
            } else if (res.code.length === 8){
                this.$hMessage.error(res.message);
            }
            this.selection = [];
        },
        // 批量删除
        handleClear(){
            this.$hMessage.destroy();
            if (!this.selection.length) {
                this.$hMessage.info('请选择一条或多条数据');
                return;
            }
            this.$hMsgBoxSafe.confirm({
                title: '批量删除用户',
                content: '确认要批量删除已选中的用户吗？',
                onOk: async () => {
                    const ids = this.selection?.map(v => v?.id?.toString()) || [];
                    await this.deleteUser(ids);
                }
            });
        },
        // 重置密码
        handleResetPasswordModal(param){
            this.resetPasswordInfo = {
                id: param.id,
                userName: param.userName,
                status: true
            };
        },
        async handleResetPassword(val){
            this.$hMsgBoxSafe.confirm({
                title: `确定要重置密码吗？`,
                content: `确定要为用户「${val.userName}」重置密码吗？重置后，此用户将被强制登出，再次登录需使用新密码。`,
                onOk: async () => {
                    const param = {
                        id: val.id,
                        password: SM3.digest(val.password, 'utf8', 'hex')
                    };
                    const res = await mdbResetPassword(param);
                    if (res?.code === '200'){
                        // 重置的用户和本地存储的id一致则清除token
                        const id = localStorage.getItem('mdbUserId');
                        if (val.id === id){
                            localStorage.removeItem('mdbToken');
                            localStorage.removeItem('mdbUserId');
                        }
                        this.$refs['table'].$_handleQuery();

                        this.resetPasswordSuccErrorInfo.type = 'success';
                        this.resetPasswordSuccErrorInfo.status = true;
                        this.resetPasswordSuccErrorInfo.userName = val?.userName;
                        this.resetPasswordSuccErrorInfo.password = val?.password;

                    } else if (res?.code?.length === 8){
                        this.resetPasswordSuccErrorInfo.type = 'error';
                        this.resetPasswordSuccErrorInfo.status = true;
                        this.resetPasswordSuccErrorInfo.errmsg = res?.message || '';
                    }
                    this.selection = [];
                }
            });
        }
    }
};
</script>
<style lang="less" scoped>
.manage-box {
    width: 100%;
    height: 100%;

    .table-slot-box > .h-btn {
        margin-right: 10px;
    }

    .best-table {
        padding: 0;
    }

    /deep/.table-slot-box > .h-btn.h-btn-disable {
        background-color: #33394e;
        border-color: #33394e;
        color: #969797;
    }
}
</style>
