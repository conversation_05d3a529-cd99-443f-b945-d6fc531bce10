import {
    createMdbEndpoint,
    createSendPacketReceivedEndpoint,
    createUstTableEndpoint,
    getEndpoints,
    getManageFunctionEndpointConfigs,
    getManageFunctionEndpoints,
    getMdbEndpointConfigs,
    getMdbEndpoints,
    getSendPacketReceivedEndpointConfigs,
    getSendPacketReceivedEndpoints,
    getUstTableEndpointConfigs,
    getUstTableEndpoints,
    testManageFunctionConnect,
    testMdbConnect,
    testSendPacketReceivedConnect,
    testUstTableConnect,
    getLocateEndpoints,
    createLocateEndpoint,
    getLocateEndpointConfigs,
    testLocateConnect
} from '@/api/memoryApi';

/**
 * 接入协议
 */
export const PROTOCOL = {
    T2: 'T2',
    LDP_MSG: 'LDP_MSG',
    HTTP: 'HTTP'
};

/**
 * 接入点类型，与服务端接入点类型枚举对应
 */
export const ENDPOINT_TYPE = {
    MDB: 'MDB',
    UST_TABLE: 'UST_TABLE',
    MANAGEMENT: 'MANAGE_FUNCTION',
    SEND_PACKET_RECEIVED: 'SEND_PACKET_RECEIVED',
    LOCATE: 'LOCATE'
};

/**
 * 接入协议列表
 */
export const PROTOCOL_LIST_MAP = {
    [ENDPOINT_TYPE.MDB]: [PROTOCOL.T2],
    [ENDPOINT_TYPE.UST_TABLE]: [PROTOCOL.LDP_MSG],
    [ENDPOINT_TYPE.SEND_PACKET_RECEIVED]: [PROTOCOL.LDP_MSG],
    [ENDPOINT_TYPE.MANAGEMENT]: [PROTOCOL.HTTP],
    [ENDPOINT_TYPE.LOCATE]: [PROTOCOL.T2]
};

/**
 * 查询接入点api映射
 */
export const GET_ENDPOINTS_MAP = {
    [ENDPOINT_TYPE.MDB]: getMdbEndpoints,
    [ENDPOINT_TYPE.UST_TABLE]: getUstTableEndpoints,
    [ENDPOINT_TYPE.SEND_PACKET_RECEIVED]: getSendPacketReceivedEndpoints,
    [ENDPOINT_TYPE.MANAGEMENT]: getManageFunctionEndpoints,
    [ENDPOINT_TYPE.LOCATE]: getLocateEndpoints
};

/**
 * 创建、修改接入点api映射
 */
export const CREATE_ENDPOINTS_MAP = {
    [ENDPOINT_TYPE.MDB]: createMdbEndpoint,
    [ENDPOINT_TYPE.UST_TABLE]: createUstTableEndpoint,
    [ENDPOINT_TYPE.SEND_PACKET_RECEIVED]: createSendPacketReceivedEndpoint,
    [ENDPOINT_TYPE.LOCATE]: createLocateEndpoint
};

/**
 * 查询接入配置列表api映射
 */
export const GET_ENDPOINT_CONFIGS = {
    [ENDPOINT_TYPE.MDB]: getMdbEndpointConfigs,
    [ENDPOINT_TYPE.UST_TABLE]: getUstTableEndpointConfigs,
    [ENDPOINT_TYPE.SEND_PACKET_RECEIVED]: getSendPacketReceivedEndpointConfigs,
    [ENDPOINT_TYPE.MANAGEMENT]: getManageFunctionEndpointConfigs,
    [ENDPOINT_TYPE.LOCATE]: getLocateEndpointConfigs
};

/**
 * 测试连通性检测api映射
 */
export const TEST_CONNECT = {
    [ENDPOINT_TYPE.MDB]: testMdbConnect,
    [ENDPOINT_TYPE.UST_TABLE]: testUstTableConnect,
    [ENDPOINT_TYPE.SEND_PACKET_RECEIVED]: testSendPacketReceivedConnect,
    [ENDPOINT_TYPE.MANAGEMENT]: testManageFunctionConnect,
    [ENDPOINT_TYPE.LOCATE]: testLocateConnect
};

/**
 * 支持上传的文件后缀
 */
export const ACCEPT_FILE_EXTENDSTIONS = {
    [ENDPOINT_TYPE.MDB]: ['dat'],
    [ENDPOINT_TYPE.LOCATE]: ['dat'],
    [ENDPOINT_TYPE.UST_TABLE]: ['cfg'],
    [ENDPOINT_TYPE.SEND_PACKET_RECEIVED]: ['cfg']
};

/**
 * 接入配置标题
 */
export const ACCESS_CONFIG_TITLE = {
    [ENDPOINT_TYPE.MDB]: 'MDB-SQL',
    [ENDPOINT_TYPE.UST_TABLE]: '内存表管理',
    [ENDPOINT_TYPE.MANAGEMENT]: '管理功能API',
    [ENDPOINT_TYPE.SEND_PACKET_RECEIVED]: '应用抓包发包',
    [ENDPOINT_TYPE.LOCATE]: 'Locate配置管理'
};

/**
 * 连通性测试结果标题文案
 */
export const TEST_CONNECT_RESULT_TITLE = {
    [ENDPOINT_TYPE.MDB]: 'MDB访问测试结果',
    [ENDPOINT_TYPE.UST_TABLE]: 'USTTable访问测试结果',
    [ENDPOINT_TYPE.MANAGEMENT]: '管理功能访问测试结果',
    [ENDPOINT_TYPE.SEND_PACKET_RECEIVED]: '访问测试结果',
    [ENDPOINT_TYPE.LOCATE]: 'Locate访问测试结果'
};

/**
 * 测试连接状态
 */
export const CONNECT_STATUS = {
    success: {
        text: '连接成功',
        icon: 'success1',
        color: '#1abe6b'
    },
    failed: {
        text: '连接失败',
        icon: 'error',
        color: '#f5222d'
    }
};

/**
 * 测试连通性时，查询在线接入点的api
 */
export const QUERY_ONLINE_ENDPINTS_MAP = {
    [ENDPOINT_TYPE.MDB]: getEndpoints,
    [ENDPOINT_TYPE.MANAGEMENT]: getManageFunctionEndpoints,
    [ENDPOINT_TYPE.SEND_PACKET_RECEIVED]: getManageFunctionEndpoints,
    [ENDPOINT_TYPE.UST_TABLE]: getManageFunctionEndpoints,
    [ENDPOINT_TYPE.LOCATE]: getLocateEndpoints
};

/**
 * 默认连通性检测展示表头
 */
export const DEFAULT_TEST_CONNECT_COL = [
    {
        title: '节点',
        key: 'instanceNo'
    },
    {
        title: '集群',
        key: 'clusterName'
    },
    {
        title: '系统号',
        key: 'systemNo',
        formatMethod: (row) => row?.systemNo ?? '-'
    },
    {
        title: '功能号',
        key: 'sqlFuncNo',
        formatMethod: (row) => row?.sqlFuncNo ?? '-'
    },
    {
        title: '节点状态',
        key: 'status'
    }
];

/**
 * 抓包发包连通性检测表头
 */
export const SEND_PACKET_TEST_CONNECT_COL = [
    {
        title: '节点',
        key: 'instanceNo'
    },
    {
        title: '集群',
        key: 'clusterName'
    },
    {
        title: '系统号',
        key: 'systemNo',
        formatMethod: (row) => row?.systemNo ?? '-'
    },
    {
        title: '节点状态',
        key: 'status'
    }
];

/**
 * locate连通性测试表头
 */
export const LOCATE_TEST_CONNECT_COL = [
    {
        title: '应用节点名',
        key: 'instanceName'
    },
    {
        title: '功能号',
        key: 'funNo',
        formatMethod: (row) => row?.funNo ?? '-'
    },
    {
        title: '应用插件ID',
        key: 'pluginId'
    },
    {
        title: '节点状态',
        key: 'status'
    }
];
