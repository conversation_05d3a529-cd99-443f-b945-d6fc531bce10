<template>
      <div class="main-top">
        <span class="currenttime">今天是: {{ currentTime }}</span>
        <p>
          {{ $getProductType(productInfo.productType) }}
        </p>
        <h-select
          v-model="productInstNo"
          class="securities"
          placeholder="请选择"
          :positionFixed="true"
          :clearable="false"
          @on-change="checkProduct"
        >
          <h-option
            v-for="item in productList"
            :key="item.id"
            :value="item.productInstNo"
            >{{ item.productName }}</h-option
          ></h-select
        >
      </div>
  </template>
<script>
import { mapState, mapActions } from 'vuex';
import _ from 'lodash';
import { formatDate } from '@/utils/utils';

export default {
    data() {
        return {
            currentTime: formatDate(new Date()),
            productInfo: {},
            productInstNo: localStorage.getItem('productInstNo')
        };
    },
    mounted() {
        this.init();
    },
    methods: {
        ...mapActions({ getProductList: 'product/getProductListLight' }),
        async init() {
            try {
                await this.getProductList();
                this.productInstNo ??= this.productList?.[0]?.productInstNo;
                this.productInfo =  _.find(this.productList, ['productInstNo', localStorage.getItem('productInstNo')]);
            } catch (e) {
                console.error(e);
            }
        },
        // 切换产品
        async checkProduct(val) {
            if (this.productList.length) {
                this.productInfo = val
                    ? _.find(this.productList, ['productInstNo', val])
                    : this.productList[0];
                localStorage.setItem('productInstNo', this.productInfo.productInstNo);
                setTimeout(() => {
                    window.location.reload();
                }, 400);
            }
        }
    },
    computed: {
        ...mapState({
            productList: (state) => {
                return state.product.productListLight || [];
            }
        })
    }
};
</script>
  <style lang="less" scoped>
    @import url("@/assets/css/input.less");
  </style>
