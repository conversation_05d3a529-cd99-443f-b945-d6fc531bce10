<template>
    <div style="height: 100%;">
      <normal-table
        ref="table"
        :formCols="4"
        formTitle="筛选条件"
        tableTitle="线程信息"
        :formItems="formItems"
        :columns="threadColumns"
        :loading="tableLoading"
        isSimpleTable
        :total="threadTableTotalCount"
        :tableData="threadTableData"
        :hasPage="true"
        showTitle
        :hasSetTableColumns="false"
        @query="handleQuery"
      >
      </normal-table>
    </div>
  </template>
<script>
import { mapState, mapActions } from 'vuex';
import normalTable from '@/components/common/bestTable/normalTable/normalTable';
import { getProductInstances } from '@/api/productApi';
import { getThreadInfoCpuNo } from '@/api/threadInfoApi';
export default {
    props: {
        productInstNo: {
            type: String,
            default: ''
        }
    },
    components: { normalTable },
    data() {
        return {
            instanceList: [],
            tableLoading: false,
            // 线程信息
            formItems: [
                {
                    type: 'select',
                    label: '管理IP',
                    key: 'manageIps',
                    placeholder: '请选择管理IP',
                    options: [],
                    value: [],
                    multiple: true
                },
                {
                    type: 'select',
                    label: '应用节点名',
                    key: 'appInstanceNames',
                    options: [],
                    value: [],
                    placeholder: '请选择应用节点名',
                    multiple: true
                },
                {
                    type: 'input',
                    label: '线程拥有者',
                    key: 'threadOwner',
                    placeholder: '请输入线程拥有者',
                    value: '',
                    clearable: true
                },
                {
                    type: 'select',
                    label: '是否绑核',
                    key: 'bindingCpuNo',
                    placeholder: '请选择绑核',
                    options: [
                        {
                            value: '1',
                            label: '是'
                        },
                        {
                            value: '0',
                            label: '否'
                        }
                    ],
                    value: null,
                    clearable: true
                }
            ],
            threadColumns: [
                {
                    title: '管理IP',
                    key: 'manageIp',
                    ellipsis: true
                },
                {
                    title: '应用节点名',
                    key: 'appInstanceName',
                    ellipsis: true
                },
                {
                    title: '线程拥有者',
                    key: 'threadOwner',
                    ellipsis: true
                },
                {
                    title: '线程名',
                    key: 'threadName',
                    ellipsis: true
                },
                {
                    title: '线程类型',
                    key: 'threadPriority',
                    ellipsis: true
                },
                {
                    title: '绑核',
                    key: 'threadCpuNo',
                    ellipsis: true
                }
            ],
            threadTableData: [],
            threadTableTotalCount: 0
        };
    },
    mounted() {
    },
    beforeDestroy() {},
    computed: {
        ...mapState({
            productList: (state) => {
                return state.product.productListLight;
            }
        })
    },
    methods: {
        ...mapActions({ getProductList: 'product/getProductListLight' }),
        clearData(){
            this.threadTableTotalCount = 0;
            this.threadTableData = [];
        },
        // 初始化
        async initData() {
            this.clearData();
            await this.getInstanceList();
            // 表格重查询数据
            this.$nextTick(() => {
                this.$refs['table'] && this.$refs['table'].$_handleResetPageDataAndQuery();
            });
        },
        // 获取实例节点列表
        async getInstanceList() {
            try {
                const res = await getProductInstances({
                    productId: this.productInstNo
                });
                if (res.code === '200') {
                    const ips = [...new Set((res?.data?.instances || []).map(o => o?.manageProxyIp))]?.filter(v => v);
                    const instanceNames = [...new Set((res?.data?.instances || []).map(o => o?.instanceName))];
                    this.formItems[0].options = (ips || []).map(o => {
                        return {
                            value: o,
                            label: o
                        };
                    });
                    this.formItems[1].options = (instanceNames || []).map(o => {
                        return {
                            value: o,
                            label: o
                        };
                    });
                } else {
                    this.formItems[0].options = [];
                    this.formItems[1].options = [];
                }
            } catch (err) {
                this.formItems[0].options = [];
                this.formItems[1].options = [];
            } finally {
                this.loading = false;
            }
        },
        // 查询
        async handleQuery(val) {
            try {
                this.tableLoading = true;
                const param = {
                    productId: this.productInstNo,
                    appInstanceNames: val?.appInstanceNames?.join(',') || '',
                    manageIps: val?.manageIps?.join(',') || '',
                    threadOwner: val?.threadOwner || '',
                    bindingCpuNo: val?.bindingCpuNo === '1'
                        ? true : val?.bindingCpuNo === '0' ? false
                            : '',
                    page: val?.page || 1,
                    pageSize: val?.pageSize || 10
                };
                const res = await getThreadInfoCpuNo(param);
                if (this.productInstNo !== param.productId) return;
                if (res.code === '200') {
                    this.threadTableData = res?.data?.list || [];
                    this.threadTableTotalCount = res?.data?.totalCount || 0;
                } else if (res.code.length === 8) {
                    this.$hMessage.error(res.message);
                }
                this.tableLoading = false;
            } catch (err) {
                this.threadTableTotalCount = 0;
                this.threadTableData = [];
            } finally {
                this.tableLoading = false;
            }
        }
    }
};
</script>
<style lang="less" scoped>
@import url("@/assets/css/input.less");
</style>
