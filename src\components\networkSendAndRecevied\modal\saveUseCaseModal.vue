<template>
  <div>
    <!-- 保存用例弹窗 -->
    <h-msg-box-safe
        v-model="modalData.status"
        width="400">
        <p slot="header" style="height: 24px; display: flex; align-items: center;">
            <h-icon
                name="android-alert"
                size="24"
                color="var(--warning-color)"
                style="margin-right: 5px;"
            ></h-icon>
            <span>保存用例</span>
        </p>
        <p>用例「{{modalData.params ? modalData.params.name : '-'}}」内容发生变化，请确认是否直接更新原用例？或将新内容另存为新用例？</p>
        <p slot="footer">
            <h-button @click="cancelMethod">取消</h-button>
            <h-button @click="confirmMethod">更新</h-button>
            <h-button type="primary" @click="openSaveAs">另存为</h-button>
        </p>
    </h-msg-box-safe>

    <!-- 另存用例弹窗 -->
    <save-as-new-case-modal
        v-model="saveAsInfo.status"
        :modalInfo="saveAsInfo"
        @update="handleUseCaseUpdate" >
    </save-as-new-case-modal>
  </div>
</template>
<script>
import { saveUseCaseInfo } from '@/api/networkApi';
import saveAsNewCaseModal from './saveAsNewCaseModal.vue';
export default {
    components: { saveAsNewCaseModal },
    name: 'SaveUseCaseModal',
    props: {
        modalInfo: {
            type: Object,
            default: function() {
                return {};
            }
        }
    },
    data() {
        return {
            modalData: this.modalInfo,
            saveAsInfo: {
                status: false
            }
        };
    },
    methods: {
        cancelMethod() {
            this.modalData.status = false;
        },
        // 确定保存用例
        confirmMethod() {
            const params = this.modalData?.params;
            this.saveUseCaseInfo(params);
        },
        // 保存用例信息
        async saveUseCaseInfo(params) {
            try {
                const res = await saveUseCaseInfo(params);
                if (res.code === '200') {
                    this.$hMessage.success('保存用例成功。');
                    this.$emit('update', res.data?.id);
                    this.modalData.status = false;
                } else if (res.code?.length === 8) {
                    this.$hMessage.error('保存用例失败。');
                }
            } catch (err) {
                console.error(err);
            }
        },
        // 另存用例名
        openSaveAs() {
            this.saveAsInfo.status = true;
            this.saveAsInfo.params = this.modalData.params;
            this.modalData.status = false;
        },
        handleUseCaseUpdate(id) {
            this.$emit('update', id);
        }
    }
};
</script>
