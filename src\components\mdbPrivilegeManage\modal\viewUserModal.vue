<!-- 查看用户 -->
<template>
    <div>
        <h-msg-box-safe
            v-model="modalData.status"
            :mask-closable="false"
            title="查看用户"
            width="70"
            height="500"
            class="wrap-msgbox"
            allowCopy
            :footerHide="true"
            @on-close="modalData.status = false"
        >
            <div class="title">基础信息</div>
            <h-form
                ref="formValidate"
                :model="formValidate"
                :label-width="120"
                cols="2"
            >
                <h-form-item label="用户名：" prop="userName">
                    <p class="modal-text" :title="formValidate.userName">{{ formValidate.userName }}</p>
                </h-form-item>
                <h-form-item label="上次修改时间：" prop="gmtModified">
                    <p class="modal-text">{{ formValidate.gmtModified }}</p>
                </h-form-item>
                <h-form-item label="描述：" prop="roleDescribe" cols="2">
                    <p class="modal-text" :title="formValidate.userDescribe">{{ formValidate.userDescribe }}</p>
                </h-form-item>
            </h-form>
            <div class="title" style="margin-top: 5px;">关联角色</div>
            <no-data v-if="!roleList.length" isWhite style="height: 300px;" text="暂无关联角色"></no-data>
            <h-tabs v-if="roleList.length" v-model="roleId" showArrow lazy :animated="false" @on-click="handleRoleClick(roleId)">
                <h-tab-pane v-for="role in roleList" :key="role.id" :label="role.roleName" :name="role.id"></h-tab-pane>
            </h-tabs>
            <p style="margin-bottom: 10px;">
                <span style="color: #495060;">赋权方式：{{ permissionsTypeList[permissionsType] }}</span>
                <h-poptip autoPlacement trigger="click" customTransferClassName="apm-poptip monitor-poptip">
                    <h-icon name="feedback_fill" size="16" color="#999" style="cursor: pointer; position: relative; top: 1px;"></h-icon>
                    <div slot="content" style="white-space: normal; max-width: 410px;">
                        <div class="tip-row">
                            <span class="tip-title">按操作赋权：</span>
                            <span class="tip-text">角色拥有MDB集群对应权限（select/update/delete/insert），集群中新建表时，角色可控表同步增加。</span>
                        </div>
                        <div class="tip-row">
                            <span class="tip-title">按表赋权：</span>
                            <span class="tip-text">角色拥有详细表对应权限（select/update/delete/insert），集群中新建表时，角色可控表不变。</span>
                        </div>
                     </div>
                </h-poptip>
            </p>
            <cluster-role-permissions-info
                v-if="roleId && permissionsType === 'OPERATION'"
                ref="cluster-role-permissions-info"
                :key="roleId"
                :productId="modalData.productId"
                :roleId="roleId"
                type="view"
            ></cluster-role-permissions-info>
            <role-permissions-info
                v-if="roleId && permissionsType === 'TABLE'"
                :key="roleId"
                 ref="role-permissions-info"
                :productId="modalData.productId"
                :roleId="roleId"
                type="view"
            ></role-permissions-info>
        </h-msg-box-safe>
    </div>
</template>

<script>
import noData from '@/components/common/noData/noData';
import clusterRolePermissionsInfo from '@/components/mdbPrivilegeManage/modal/clusterRolePermissionsInfo';
import rolePermissionsInfo from '@/components/mdbPrivilegeManage/modal/rolePermissionsInfo';

export default {
    name: 'ViewUserModal',
    components: { rolePermissionsInfo, clusterRolePermissionsInfo, noData },
    props: {
        modalInfo: {
            type: Object,
            default: () => ({})
        }
    },
    data() {
        return {
            modalData: this.modalInfo,
            formValidate: {
                userName: this.modalInfo.userName || '',
                userDescribe: this.modalInfo.userDescribe || '',
                gmtModified: this.modalInfo.gmtModified || ''
            },
            loading: false,
            roleId: '',
            permissionsType: '',
            roleList: this.modalInfo.roles || [],
            permissionsTypeList: {
                OPERATION: '按操作赋权',
                TABLE: '按表赋权'
            }
        };
    },
    mounted() {
        this.roleId = '';
        this.permissionsType = '';
        if (this.roleList?.length) {
            this.handleRoleClick(this.roleList?.[0]?.id);
        }
    },
    methods: {
        handleRoleClick(id) {
            this.roleId = id || '';
            this.permissionsType = this.roleList.find(o => o.id === id)?.type || 'TABLE';
            this.$nextTick(() => {
                if (this.permissionsType === 'TABLE') {
                this.$refs?.['role-permissions-info'] && this.$refs['role-permissions-info'].initData();
                }
                if (this.permissionsType === 'OPERATION') {
                this.$refs?.['cluster-role-permissions-info'] && this.$refs['cluster-role-permissions-info'].initData();
                }
            });
        }
    }
};
</script>

<style lang="less" scoped>
/deep/ .h-modal-body {
    padding: 16px 20px;
}

.wrap-msgbox {
    .modal-text {
        cursor: pointer;
        max-height: 100px;
        overflow: scroll;
    }

    /deep/ .h-tabs-bar {
        margin-bottom: 10px;
    }

    .title {
        position: relative;
        padding: 5px 0 10px 20px;
        font-weight: 600;

        &::before {
            content: "";
            display: inline-block;
            position: absolute;
            left: 0;
            width: 4px;
            height: 17px;
            background: var(--link-color);
        }
    }

    .tip-row {
        display: flex;
        align-items: flex-start;
        flex-wrap: wrap;
    }

    .tip-title {
        color: #cacfd4;
        white-space: nowrap;
        margin-right: 4px;
        min-width: 72px;
        font-family: PingFangSC-Regular;
    }

    .tip-text {
        color: var(--font-color);
        word-break: break-all;
        flex: 1 1 0;
        white-space: pre-line;
        font-family: PingFangSC-Regular;
    }

    .border {
        border-top: 1px solid #b0b4ba;
        margin-bottom: 15px;
        margin-top: 10px;
    }

    .h-form .h-form-item-label {
        color: #777;
    }

    .h-form-item {
        margin-bottom: 0;
    }
}
</style>
