<template>
    <div class="transmitters">
        <!-- 会话信息 -->
        <info-grid ref="session" :gridData="sessionData" class="first-grid"
            @select-change="handleSelectChange"></info-grid>
        <!-- 应用消息处理 -->
        <info-grid :gridData="appMsgData"></info-grid>
        <!-- 通讯分片处理 -->
        <info-grid :gridData="netShardData"></info-grid>
    </div>
</template>

<script>
import infoGrid from '@/components/common/infoBar/infoGrid';
export default {
    name: 'Transmitters',
    components: {
        infoGrid
    },
    props: {
        rcmInfo: {
            type: Object,
            default: () => {}
        }
    },
    data() {
        return {
            // 会话信息
            sessionData: {
                title: {
                    label: '会话信息'
                },
                layout: [
                    { x: 0, y: 0, w: 4, h: 11.5, i: 'info1' },
                    { x: 4, y: 0, w: 4, h: 11.5, i: 'info2' },
                    { x: 8, y: 0, w: 4, h: 11.5, i: 'info3' }
                ],
                details: [
                    {
                        type: 'description',
                        title: {
                            label: 'Transmitter'
                        },
                        infoDic: [
                            {
                                label: 'ContextName',
                                key: 'ContextName',
                                span: '24'
                            },
                            {
                                label: 'TierName',
                                key: 'TierName',
                                span: '12'
                            },
                            {
                                label: 'Role',
                                key: 'Role',
                                span: '12'
                            },
                            {
                                label: 'Address',
                                key: 'Address',
                                span: '12'
                            },
                            {
                                label: 'TxArg',
                                key: 'TxArg',
                                span: '12'
                            }
                        ],
                        info: {}
                    },
                    {
                        type: 'description',
                        title: {
                            label: 'Transport: ',
                            slots: [
                                {
                                    key: 'topicPartitionNo',
                                    type: 'select',
                                    defaultValue: '',
                                    options: [],
                                    minWidth: '80px',
                                    maxWidth: '180px',
                                    placeholder: '主题分区'
                                }
                            ]
                        },
                        infoDic: [
                            {
                                label: 'TopicName',
                                key: 'TopicName',
                                span: '12'
                            },
                            {
                                label: 'PartitionNo',
                                key: 'PartitionNo',
                                span: '12'
                            },
                            {
                                label: 'Addr',
                                key: 'Addr',
                                span: '12'
                            },
                            {
                                label: 'Port',
                                key: 'Port',
                                span: '12'
                            }
                        ],
                        info: {}
                    },
                    {
                        type: 'table',
                        title: {
                            label: 'Ackers:3'
                        },
                        info: {
                            height: '170',
                            columns: [
                                {
                                    title: 'ContextName',
                                    key: 'ContextName',
                                    ellipsis: true,
                                    minWidth: 140
                                },
                                {
                                    title: 'ConnectionPhase',
                                    key: 'ConnectionPhase',
                                    ellipsis: true,
                                    minWidth: 140
                                },
                                {
                                    title: 'IsMandatoryAcker',
                                    key: 'IsMandatoryAcker',
                                    ellipsis: true,
                                    minWidth: 140
                                }
                            ],
                            tableData: []
                        }
                    }
                ]
            },
            // 应用消息处理
            appMsgData: {
                title: {
                    label: '应用消息处理'
                },
                layout: [
                    { x: 0, y: 0, w: 6, h: 6.5, i: 'info1' },
                    { x: 6, y: 0, w: 6, h: 6.5, i: 'info2' },
                    { x: 0, y: 1, w: 6, h: 6.5, i: 'info3' },
                    { x: 0, y: 2, w: 12, h: 11.5, i: 'info5' }
                ],
                details: [
                    {
                        title: {
                            label: '消息发送与应答',
                            noTagColor: true,
                            slots: [
                                {
                                    type: 'text', // 文本
                                    label: 'LastSendMilli',
                                    value: '-'
                                }
                            ]
                        },
                        type: 'monitor',
                        info: [
                            {
                                type: 'text',
                                span: '8',
                                label: 'NextMsgNo',
                                labelAlias: '下个消息编号',
                                value: '-'
                            },
                            {
                                type: 'text',
                                span: '8',
                                label: 'ToSendMsgNo',
                                labelAlias: '当前最大消息号',
                                value: '-'
                            },
                            {
                                type: 'text',
                                span: '8',
                                label: 'LastAckMsgNo',
                                labelAlias: '上次应答消息编号',
                                value: '-'
                            }
                        ]
                    },
                    {
                        title: {
                            label: '消息缓存与持久化',
                            noTagColor: true
                        },
                        type: 'monitor',
                        info: [
                            {
                                type: 'text',
                                span: '8',
                                label: 'MinCachedMsgNo',
                                labelAlias: '缓存中保存的消息的最小消息号',
                                value: '-'
                            },
                            {
                                type: 'text',
                                span: '8',
                                label: 'NextMsgNoRecord',
                                labelAlias: '下一个待持久化的消息号',
                                value: '-'
                            },
                            {
                                type: 'text',
                                span: '8',
                                label: 'MsgRecorded',
                                labelAlias: '已持久化消息数',
                                value: '-'
                            }
                        ]
                    },
                    {
                        title: {
                            label: '消息积压',
                            noTagColor: true
                        },
                        type: 'monitor',
                        info: [
                            {
                                type: 'text',
                                span: '8',
                                label: 'AppBackLog',
                                labelAlias: '缓存积压消息数',
                                value: '-'
                            },
                            {
                                type: 'text',
                                span: '8',
                                label: 'NetBackLog',
                                labelAlias: '网络积压消息数',
                                value: '-'
                            }
                        ]
                    },
                    {
                        title: {
                            label: '对端消息应答',
                            noTagColor: true
                        },
                        type: 'table',
                        info: {
                            height: '170',
                            columns: [
                                {
                                    title: 'ContextName',
                                    key: 'ContextName',
                                    ellipsis: true
                                },
                                {
                                    title: 'AckMsgNo',
                                    key: 'AckMsgNo',
                                    ellipsis: true
                                },
                                {
                                    title: 'IsMandatoryAcker',
                                    key: 'IsMandatoryAcker',
                                    ellipsis: true
                                },
                                {
                                    title: 'LastRecvMilli',
                                    key: 'LastRecvMilli',
                                    ellipsis: true
                                }
                            ],
                            tableData: []
                        }
                    }
                ]
            },
            // 通讯分片处理
            netShardData: {
                title: {
                    label: '通讯分片处理'
                },
                layout: [
                    { x: 0, y: 0, w: 6, h: 6.5, i: 'info1' },
                    { x: 6, y: 0, w: 6, h: 6.5, i: 'info2' },
                    { x: 0, y: 1, w: 12, h: 11.5, i: 'info3' }
                ],
                details: [
                    {
                        title: {
                            label: '分片发送与应答',
                            noTagColor: true
                        },
                        type: 'monitor',
                        info: [
                            {
                                type: 'text',
                                span: '8',
                                label: 'NextSqn',
                                labelAlias: '下一个待分配的分片号',
                                value: '-'
                            },
                            {
                                type: 'text',
                                span: '8',
                                label: 'ToSendSqn',
                                labelAlias: '当前待发送分片号',
                                value: '-'
                            },
                            {
                                type: 'text',
                                span: '8',
                                label: 'LastAckSqn',
                                labelAlias: '最后一次收到的应答序号',
                                value: '-'
                            }
                        ]
                    },
                    {
                        title: {
                            label: '分片发送统计',
                            noTagColor: true
                        },
                        type: 'monitor',
                        info: [
                            {
                                type: 'text',
                                span: '8',
                                label: 'AsyncSendSqnCount',
                                labelAlias: '异步发送的分片数目',
                                value: '-'
                            },
                            {
                                type: 'text',
                                span: '8',
                                label: 'SendFailedCount',
                                labelAlias: '底层失败的分片数目',
                                value: '-'
                            }
                        ]
                    },
                    {
                        title: {
                            label: '对端分片应答',
                            noTagColor: true
                        },
                        type: 'table',
                        info: {
                            height: '170',
                            columns: [
                                {
                                    title: 'ContextName',
                                    key: 'ContextName',
                                    ellipsis: true
                                },
                                {
                                    title: 'AckSqn',
                                    key: 'AckSqn',
                                    ellipsis: true
                                },
                                {
                                    title: 'AckCount',
                                    key: 'AckCount',
                                    ellipsis: true
                                },
                                {
                                    title: 'NakCount',
                                    key: 'NakCount',
                                    ellipsis: true
                                },
                                {
                                    title: 'NakCountFlowControl',
                                    key: 'NakCountFlowControl',
                                    ellipsis: true
                                },
                                {
                                    title: 'OnlineNakMergeCount',
                                    key: 'OnlineNakMergeCount',
                                    ellipsis: true
                                },
                                {
                                    title: 'OnlineNakFlowControlCount',
                                    key: 'OnlineNakFlowControlCount',
                                    ellipsis: true,
                                    minWidth: 140
                                },
                                {
                                    title: 'LastRecvMilli',
                                    key: 'LastRecvMilli',
                                    ellipsis: true
                                }
                            ],
                            tableData: []
                        }
                    }
                ]
            },
            selectTopic: ''
        };
    },
    methods: {
        initData() {
            this.getFileData();
        },
        // 构造页面数据
        getFileData()  {
            const options = (this.rcmInfo?.Transmitters || []).map(o => {
                return {
                    value: `${o?.TopicName}[${o?.PartitionNo}]`,
                    label: `${o?.TopicName}[${o?.PartitionNo}]`
                };
            });
            // 主题分区列表
            this.sessionData.details[1].title.slots[0].options = options;
            this.$nextTick(() => {
                const value = options.find(o => o.value === this.selectTopic)?.value || options?.[0]?.value;
                this.$refs['session'].$refs['info-bar'][1].setSelectVal('topicPartitionNo', value);
            });
        },
        setPageData(selectTopic) {
            const itemData = this.rcmInfo?.Transmitters?.find(o => {
                return `${o.TopicName}[${o.PartitionNo}]` === selectTopic;
            }) || {};
            // ------  会话信息  -----------
            this.setSessionData(itemData);

            // ------  应用消息处理  -----------
            this.setAppMsgData(itemData);

            // ------  通讯分片处理  -----------
            this.setNetShardData(itemData);
        },
        // 会话信息
        setSessionData(itemData) {
            this.sessionData.title.label = `会话信息：${itemData?.Session?.ConnectionId ?? ''}-${itemData?.Session?.SessionPhase ?? ''}`;
            // Transmitter
            this.sessionData.details[0].title.label = `Transmitter：${itemData?.TransmitterIndex ?? '-'}`;
            this.sessionData.details[0].info = {
                ...itemData,
                ContextName: this.rcmInfo?.ContextName,
                TierName: this.rcmInfo?.TierName,
                StartMsgNo: itemData?.Session?.StartMsgNo
            };
            // Transport
            this.sessionData.details[1].title.label = `Transport：${itemData?.TransportId ?? '-'}`;
            this.sessionData.details[1].info = itemData;
            // Ackers
            this.sessionData.details[2].title.label = `Ackers：${itemData?.Ack?.length ?? '-'}`;
            this.sessionData.details[2].info.tableData = itemData?.Ack;
        },
        // 应用消息处理
        setAppMsgData(itemData) {
            // 特殊取值
            itemData.LastSendMilli = itemData?.Session?.LastSendMilli;
            // 缓存积压消息数
            itemData.AppBackLog = itemData?.NextMsgNo - itemData?.ToSendMsgNo;
            // 网络积压消息数
            itemData.NetBackLog = itemData?.ToSendMsgNo - itemData?.LastAckMsgNo;

            this.appMsgData.details.forEach(item => {
                item.title.slots && (item.title.slots[0].value = itemData[item.title.slots[0].label]);
                if (item.type === 'table') {
                    // 对端消息应答
                    item.info.tableData = itemData?.Ack;
                } else if (item.type === 'monitor') {
                    // 网络消息发送与应答\消息缓存与持久化\消息积压
                    item.info.forEach(o => {
                        o.type === 'text' && (o.value = itemData?.[o.label]);
                    });
                }
            });
        },
        // 通讯分片处理
        setNetShardData(itemData) {
            this.netShardData.details.forEach(item => {
                item.title.slots && (item.title.slots[0].value = itemData[item.title.slots[0].label]);
                if (item.type === 'table') {
                    // 对端分片应答
                    item.info.tableData = itemData?.Ack;
                } else if (item.type === 'monitor') {
                    // 发送端分片发送与应答\发送端分片发送统计
                    item.info.forEach(o => {
                        o.type === 'text' && (o.value = itemData?.[o.label]);
                    });
                }
            });
        },
        // 切换主题分区
        handleSelectChange(val) {
            this.selectTopic = val;
            this.setPageData(val);
        }
    }
};
</script>

<style lang="less" scoped>
.transmitters {
    height: 100%;
    overflow: auto;

    // .first-grid {
    //     margin-top: 0;
    // }
}
</style>
