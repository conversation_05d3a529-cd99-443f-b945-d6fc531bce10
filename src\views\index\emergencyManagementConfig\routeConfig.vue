<template>
  <div class="container">
    <h-tabs>
      <h-tab-pane label="路由配置" name="routeConfig">
        <a-loading v-if="loading" />
        <no-data v-if="hasError && !loading" />
        <h-split
          v-if="node !== null && node.id !== null && !loading"
          ref="split"
          v-model="split1"
          min="85px"
          mode="vertical"
          @on-moving="onTopMoving"
        >
          <div slot="top" class="container-top">
            <div v-if="!version && !loading" class="alert">
                <h-alert type="warning" closable @on-close="onCloseAlert">
                    <h-icon name="prompt" />路由配置无version信息，不可添加、删除、修改配置。
                </h-alert>
            </div>
            <div class="container-top-info">
              <div class="container-top-info-version">
                当前版本：{{ version || "-" }}
              </div>
              <div class="container-top-info-search">
                <a-button type="dark" :disabled="disabled" @click="onAdd">添加路由</a-button>
                <h-input
                    v-model="searchVal"
                    icon="search"
                    clearable
                    placeholder="输入功能号"
                    @on-click="onSearch"
                    @on-enter="onSearch"
                />
                <div class="container-top-info-line" />
                <div class="container-top-info-aq" @click="() => {instructInfo.status = true;}">
                  <h-icon name="feedback" />
                </div>
              </div>
            </div>
            <div class="container-top-table">
              <a-table
                highlightRow
                :tableData="renderTableData"
                :height="tableHeight"
                showTitle
                :hasPage="false"
                :columns="columns"
                rowSelectOnly
                @on-current-change="tableRowcheckedChange"
              />
            </div>
          </div>
          <div slot="bottom" class="container-bottom">
            <h-split v-model="split2" class="split2">
              <div slot="left" class="container-bottom-left">
                <a-title title="修改配置" />
                <div class="container-bottom-left-form">
                  <router-info-form
                    ref="form"
                    :node="node"
                    :disabled="disabled"
                    @callUpdateTable="callUpdateTable"
                  />
                </div>
              </div>
              <div slot="right" class="container-bottom-left">
                <div class="json-box">
                  <a-title title="配置json预览" />
                  <json-viewer
                    :value="jsonData"
                    :expand-depth="10"
                    :expanded="true"
                  >
                  </json-viewer>
                </div>
              </div>
            </h-split>
          </div>
        </h-split>
        <!-- <no-data v-else /> -->
        <div class="footer">
          <a-button type="primary" :disabled="disabled" @click="beforeSave">更新配置</a-button>
        </div>
      </h-tab-pane>
    </h-tabs>
    <add-modal
      v-if="modalInfo.status"
      :node="node"
      :modalInfo="modalInfo"
      @inserTable="inserTable"
      @onClose="onClose"
    />
    <help-modal v-if="instructInfo.status" :modalInfo="instructInfo" />
  </div>
</template>
<script>
import aButton from '@/components/common/button/aButton';
import aTable from '@/components/common/obsTable/obsTable';
import aTitle from '@/components/common/title/aTitle';
import routerInfoForm from './routeInfoForm.vue';
import noData from '@/components/common/noData/noData';
import aLoading from '@/components/common/loading/aLoading';
import addModal from './addModal.vue';
import { v4 as uuidv4 } from 'uuid';
import jsonViewer from 'vue-json-viewer';
import { getManagerProxy } from '@/api/mcApi';
import { editManageInfo } from '@/api/httpApi';
import helpModal from './helpModal.vue';

export default {
    name: 'RouteConfig',
    components: {
        aButton,
        jsonViewer,
        addModal,
        noData,
        aLoading,
        aTitle,
        helpModal,
        aTable,
        routerInfoForm
    },
    props: {
        node: {
            type: Object,
            default: () => null
        }
    },
    data() {
        return {
            split1: window.innerHeight > 649 ? 0.3 : 0.4,
            searchVal: null,
            searchValMatch: null,
            split2: 0.5,
            instructInfo: {
                status: false
            },
            jsonTitle: {
                title: '配置json预览'
            },
            tableData: [],
            modalInfo: {
                status: false
            },
            version: null,
            hasError: false,
            hiddenAlert: false,
            loading: false,
            tableHeight: '200px',
            columns: [
                {
                    title: '功能号',
                    key: 'Functions',
                    ellipsis: true
                },
                {
                    title: '目标系统号',
                    key: 'SystemNo',
                    ellipsis: true
                },
                {
                    title: '节点',
                    key: 'NodeNo',
                    ellipsis: true
                },
                {
                    title: '目标端ID',
                    key: 'BackendID',
                    ellipsis: true
                },
                {
                    title: '操作',
                    key: 'action',
                    render: (h, params) => {
                        return h('div', [
                            h(
                                'Button',
                                {
                                    props: {
                                        type: 'text',
                                        size: 'small',
                                        disabled: this.version === undefined
                                    },
                                    on: {
                                        click: (e) => {
                                            if (!this.version) return;
                                            e.stopPropagation();
                                            this.onDeleteRow(params.row);
                                        }
                                    }
                                },
                                '删除'
                            )
                        ]);
                    }
                }
            ]
        };
    },
    computed: {
        jsonData() {
            return {
                Routes: this.tableData.map(item => ({
                    Functions: item.Functions,
                    SystemNo: item.SystemNo,
                    NodeNo: item.NodeNo,
                    BackendID: item.BackendID
                }))
            };
        },
        disabled() {
            return !this.version;
        },
        renderTableData() {
            if (!this.searchValMatch) return this.tableData;
            return this.tableData.filter(item => item.Functions && item.Functions.includes(this.searchValMatch));
        }
    },
    mounted() {
        this.init();
    },
    methods: {
        onSearch() {
            this.$nextTick(() => {
                this.searchValMatch = this.searchVal;
            });
        },
        onCloseAlert() {
            this.hiddenAlert = true;
            this.tableData.length && this.onTopMoving();
        },
        onTopMoving() {
            const splitRef = this.$refs['split'];
            const topPanel = splitRef?.$el && splitRef.$el.querySelector('.top-pane');
            if (splitRef && topPanel) {
                const height = topPanel.getBoundingClientRect().height;
                const heightVal = height - 48;
                this.tableHeight = `${heightVal < 100 ? 100 : (heightVal - (this.hiddenAlert ? 0 : 42))}px`;
            }
        },
        async beforeSave() {
            const form = this.$refs['form'];
            const res = this.tableData.length ? await form.validateAndGetAll() : {};
            this.$hMsgBoxSafe.confirm({
                title: `确定要更新配置吗？`,
                content: `参数配置更新后实时生效，重启后失效。`,
                onOk: async () => {
                    await this.onSubmit(res);
                }
            });
        },
        async onSubmit(dataObj) {
            try {
                const form = this.$refs['form'];
                const checkVisible = await form.getData();
                if (checkVisible?.visible) {
                    const { data, uuid } = dataObj;
                    if (uuid === null) {
                    // 新增
                        this.tableData.unshift({ ...data, uuid: uuidv4() });
                    } else {
                        this.tableData = this.tableData.map((item) => ({
                            ...item,
                            ...(uuid === item.uuid ? data : {})
                        }));
                    }
                }
                const routers = this.tableData.map((item) => ({
                    Functions: item.Functions,
                    SystemNo: item.SystemNo,
                    NodeNo: item.NodeNo,
                    BackendID: item.BackendID
                }));
                const res = await editManageInfo({
                    manageProxyIp: this.node.manageProxyIp,
                    manageProxyPort: this.node.manageProxyPort,
                    PluginName: 'ldp_front',
                    FuncName: 'SetFrontRoute',
                    Routes: routers,
                    Version: this.version,
                    instanceId: this.node.id,
                    productId: localStorage.getItem('productInstNo')
                });
                const body = JSON.parse(res?.data?.response?.body);
                if (body?.ErrorMsg) {
                    this.$hMsgBox.error({
                        title: '更新失败',
                        content: body?.ErrorMsg
                    });
                } else {
                    this.$hMessage.success({
                        content: '更新成功'
                    });
                    this.init();
                }
            } catch (error) {
                console.log('更新失败', error);
                // this.$hMsgBox.error({
                //     title: '更新失败',
                //     content: error?.toString?.()
                // });
            }
        },
        /**
         * 点击路由
         */
        tableRowcheckedChange(row) {
            this.$refs['form'].setData(row);
            this.tableData = this.tableData.map(item => ({ ...item, _highlight: row.uuid === item.uuid }));
        },
        /**
         * 实时更新
         */
        callUpdateTable({ data, uuid }) {
            if (!uuid) return;
            this.tableData = this.tableData.map((item) => ({
                ...item,
                ...(item.uuid === uuid ? data : item),
                _highlight: uuid === item.uuid
            }));
        },
        /**
         * 添加路由
         */
        inserTable({ data }) {
            this.tableData.unshift({ ...data, uuid: uuidv4() });
            this.onClose();
            this.tableRowcheckedChange(this.tableData[0]);
        },
        /**
         * 删除路由
         */
        onDeleteRow(row) {
            this.$hMsgBoxSafe.confirm({
                title: `确定要删除该路由？`,
                onOk: () => {
                    this.tableData = this.tableData.filter(item => item.uuid !== row.uuid);
                    const curEditRow = this.$refs['form'].getData();
                    if (this.tableData.length && (curEditRow?.uuid === row.uuid || !curEditRow.uuid)) {
                        this.tableRowcheckedChange(this.tableData[0]);
                        return;
                    }
                    if (this.tableData.length === 0) {
                        this.tableRowcheckedChange({ visible: false, uuid: null });
                    }
                }
            });
        },
        onAdd() {
            this.modalInfo.status = true;
        },
        onClose() {
            this.modalInfo.status = false;
        },
        async init() {
            this.tableData = [];
            this.version = null;
            this.loading = true;
            this.searchValMatch = null;
            this.hasError = false;
            if (!this.node?.manageProxyIp) return;
            const param = [
                {
                    manageProxyIp: this.node.manageProxyIp,
                    manageProxyPort: this.node.manageProxyPort,
                    pluginName: 'ldp_front',
                    funcName: 'RouteStat'
                }
            ];
            try {
                const res = await getManagerProxy(JSON.stringify(param));
                if (res.code === '200') {
                    if (res.data?.[0]?.ErrorNo) {
                        this.loading = true;
                        this.$hMsgBox.error({
                            title: '获取路由配置失败',
                            content: res.data?.[0]?.ErrorMsg
                        });
                    } else {
                        this.hasError = false;
                        const dataObj = res.data?.[0] || {};
                        this.tableData = (dataObj?.Routes || []).map((item) => ({
                            ...item,
                            uuid: uuidv4()
                        }));
                        this.version = dataObj?.Version;
                        this.hiddenAlert = !!this.version;
                        if (this.tableData[0]) {
                            this.tableData[0]._highlight = true;
                            this.$nextTick(() => {
                                this.$refs['form'].setData(this.tableData[0]);
                            });
                        }
                    }
                    this.$nextTick(() => {
                        const el = this.$refs['split']?.$el;
                        if (!el) return;
                        const totalHeight = el.getBoundingClientRect().height;
                        const topBarHeight = 42;
                        const tableHeaderHeight = 42;
                        const oneRowHeight = 40;
                        const versionHeight = this.version ? 0 : 47;
                        const ratio = (topBarHeight + tableHeaderHeight + versionHeight + oneRowHeight * 3) / totalHeight;
                        this.split1 = ratio;
                        this.$nextTick(this.onTopMoving);
                    });
                }
            } catch (err) {
                console.log('获取路由配置失败', err);
            } finally {
                this.loading = false;
            }
        }
    }
};
</script>
<style scoped lang="less">
@import url("@/assets/css/json-view.less");

.container {
    width: 100%;
    color: #fff;
    display: block;

    /deep/ .h-tabs {
        width: 100%;

        .h-split-trigger-horizontal,
        .h-split-trigger-vertical {
            // background-color: unset;
            background: #1b2130;
            border-color: #31364a;
        }

        .h-split-trigger-vertical .h-split-trigger-bar,
        .h-split-trigger-horizontal .h-split-trigger-bar {
            background: #444a60;
        }
    }

    /deep/ .h-tabs-tabpane {
        display: flex;
        flex-direction: column;
    }

    &-top {
        .alert .h-alert {
            height: 32px;
            background: #4e3b29;
            border-radius: 4px;
            border: none;
            padding: 0;
            line-height: 32px;
            color: #fff;
            margin-bottom: 15px;

            /deep/.h-alert-close {
                top: unset;
            }

            .icon-prompt {
                color: #ff9901;
                margin-left: 13px;
                margin-right: 9px;
            }
        }

        &-info {
            display: flex;
            justify-content: space-between;
            align-items: center;

            &-version {
                color: #fff;
            }

            &-search {
                display: flex;

                button {
                    margin-right: 8px;
                }
            }

            &-line {
                width: 1px;
                background: #31364a;
                margin: 0 8px;
            }

            &-aq {
                width: 32px;
                height: 32px;
                background: #262d43;
                border: 1px solid #485565;
                border-radius: 4px;
                // display: flex;
                justify-content: center;
                align-items: center;
                text-align: center;
                cursor: pointer;

                i {
                    color: #fff;
                    font-size: 22px;
                    width: 32px;
                    height: 32px;
                    display: inline-block;
                }
            }
        }

        /deep/ .table-box {
            margin: 0;
        }

        /deep/ .obs-table {
            background-color: unset;
        }

        /deep/ .h-table-tbody {
            td {
                background-color: unset !important;
            }

            .h-table-row-highlight td {
                background: #1f3759 !important;
            }
        }

        /deep/ .h-input-disabled {
            input::placeholder {
                color: #9296a1;
            }

            .h-input-suffix i {
                color: #9296a1;
                cursor: not-allowed;

                &:hover {
                    color: #9296a1;
                }
            }
        }
    }

    &-bottom {
        height: 100%;

        &-left,
        &-right {
            background: #262d43;
            height: 100%;
        }

        &-left {
            overflow-y: auto;

            &-form {
                padding: 15px;
            }
        }

        /deep/ .apm-title {
            background-color: unset;
            margin-top: 6px;
        }

        /deep/ .split2 {
            .h-split-trigger {
                position: relative;
                top: 5px;
                height: calc(100% - 5px);
            }
        }
    }
}
</style>
