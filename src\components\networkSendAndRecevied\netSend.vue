<template>
  <div class="tab-box">
    <div class="net-send">
        <h-form
            ref="formItems"
            :model="formItems"
            :label-width="80"
            :cols="4"
            style="background: var(--wrapper-color);">
            <h-form-item label="接入点" prop="endpoint" required>
                <h-select v-model="formItems.endpoint"
                    :clearable="false"
                    :setDefSelect="true"
                    :positionFixed="true">
                    <h-option
                        v-for="item in endpointList"
                        :key="item.id"
                        :value="item.id"
                        >{{ item.instanceNo }}
                    </h-option>
                    <div slot="nodata" class="nodata-dropdown">
                        <div class="nodata-img">
                            <img
                                width="120"
                                height="80"
                                :src="`${IMG_HOME}static/noDataWhite.png`"
                                alt="图片无法显示" />
                        </div>
                        <p>无可用接入点</p>
                        <p>
                            请至“产品服务配置-产品服务网关”
                            <a-button
                                type="text"
                                size="small"
                                style="color: var(--link-color);"
                                @click="$hCore.navigate(`/productServiceList`, {
                                    menuId: 'sendPacketReceivedTableAccess'
                                });">配置
                            </a-button>
                        </p>
                    </div>
                </h-select>
            </h-form-item>
            <h-form-item label="发包协议" prop="protocol" required>
                <h-select v-model="formItems.protocol"
                    :clearable="false"
                    :setDefSelect="true"
                    :positionFixed="true">
                    <h-option
                        v-for="item in protocolList" :key="item.value" :value="item.value"
                        >{{ item.label }}
                    </h-option>
                </h-select>
            </h-form-item>
            <h-form-item :labelWidth="20">
                <span class="time-box">
                    <div>时间戳 &nbsp;
                        <h-switch v-model="isTimeShow" @on-change="toggleTimeShow">
                            <div slot="open">开</div>
                            <div slot="close">关</div>
                        </h-switch>
                    </div>
                    <div class="split-solid"></div>
                    <a-button
                        type="primary"
                        :loading="rspLoading"
                        @click="handleSendPacket">发送
                    </a-button>
                </span>
            </h-form-item>
        </h-form>
        <!-- 用例展示 -->
        <div class="result-box">
            <!-- 用例列表 -->
            <menu-layout ref="menu" customMenu @menu-fold="(status) => menuFoldStatus = status">
                <template v-slot:menu>
                    <div class="menu">
                        <div class="header-menu">
                            &nbsp;&nbsp;&nbsp;用例列表
                        </div>
                        <h-input
                            v-model="searchText"
                            placeholder="输入用例名"
                            icon="android-search"
                            clearable
                            :style="{ display: menuFoldStatus ? 'none' : 'block' }"
                            @on-enter="fuzzySearch"
                            @on-blur="fuzzySearch"
                            @on-click="fuzzySearch"
                        ></h-input>
                        <ul v-if="menuList.length" class="menu-ul uselist-menu">
                            <li v-for="item in menuList" :key="item.id"
                                :class="selectUseCase === item.id ? 'menu-selected' : ''">
                                {{ item.name }}
                                <a-button
                                    type="text"
                                    class="uselist-menu-btn"
                                    @click="handleUseCaseChange(item.id)">引用
                                </a-button>
                            </li>
                        </ul>
                        <p v-if="!menuList.length && !menuFoldStatus" style="padding: 10px; text-align: center;">无内容</p>
                    </div>
                </template>
                <template v-slot:right>
                    <h-split v-model="splitVal" min="400px" max="400px">
                        <!-- request -->
                        <div slot="left" class="split-pane">
                            <div class="split-pane-content">
                                <obs-title
                                    ref="obs-title"
                                    :title="reqJsonTitle">
                                    <template v-slot:extraTitleBox>
                                        <span style="float: right;">
                                            <a-button type="text"
                                                style="color: var(--link-color);"
                                                @click="handleSaveUseCase">
                                                保存为用例</a-button>
                                        </span>
                                    </template>
                                </obs-title>
                                <a-loading v-if="reqLoading" class="json-box"></a-loading>
                                <div v-else ref="json-box" class="json-box">
                                    <div class="json-box-title"
                                        @click="toggleJsonCollapse('reqHeaders')">
                                        <span>Headers</span>
                                        <h-icon :name="jsonShow.reqHeaders ? 'packup' : 'unfold'"></h-icon>
                                    </div>
                                    <div v-show="jsonShow.reqHeaders" class="json-box-editor">
                                        <vue-json-editor
                                            v-if="reqJsonBox"
                                            v-model="reqJsonData.reqHeaders"
                                            :showBtns="false"
                                            :mode="'code'"
                                            lang="zh"
                                            :expandedOnStart="true"
                                            class="json-record"
                                            :style="{ height: jsonHeight }"
                                            @json-change="onJsonChange('Headers')"
                                            @has-error="onJsonError('Headers')">
                                        </vue-json-editor>
                                    </div>
                                    <div class="json-box-title" style="margin-top: 10px;"
                                        @click="toggleJsonCollapse('reqBody')">
                                        <span>Body</span>
                                        <h-icon :name="jsonShow.reqBody ? 'packup' : 'unfold'"></h-icon>
                                    </div>
                                    <div v-show="jsonShow.reqBody" class="json-box-editor">
                                        <vue-json-editor
                                            v-if="reqJsonBox"
                                            v-model="reqJsonData.reqBody"
                                            :showBtns="false"
                                            :mode="'code'"
                                            lang="zh"
                                            :expandedOnStart="true"
                                            class="json-record"
                                            :style="{ height: jsonHeight }"
                                            @json-change="onJsonChange('Body')"
                                            @has-error="onJsonError('Body')">
                                        </vue-json-editor>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <!-- response -->
                        <div slot="right" class="split-pane">
                            <div class="split-pane-content">
                                <obs-title :title="rspJsonTitle">
                                    <template v-slot:extraTitleBox>
                                        <div v-if="rspCallInfo.totalTime" style="float: right;">
                                            <span style="line-height: 20px; margin-top: 12px;">
                                                {{rspCallInfo.statusCode}}&nbsp;
                                                <h-poptip trigger="hover" placement="top-end" transfer style="cursor: pointer;"
                                                    customTransferClassName="apm-poptip apm-poptip-time monitor-poptip">
                                                    {{ rspCallInfo.totalTime }}
                                                    <div slot="content" class="pop-content" style='white-space: normal;'>
                                                        <div>
                                                            <span class="time-item-label">发包调用总耗时：</span>
                                                            {{ rspCallInfo.totalTime }}
                                                        </div>
                                                        <div>
                                                            <span class="time-item-label">发包调用耗时：</span>
                                                            {{ rspCallInfo.callTime }}
                                                        </div>
                                                    </div>
                                                </h-poptip>&nbsp;
                                                {{rspCallInfo.resourceSize}}
                                            </span>
                                            <a-button v-if="rspCallInfo.hasTimeStamp" type="dark"
                                                style="margin: 0 10px; margin-top: -2px;"
                                                @click="handleTimeVisable">查看时间戳
                                            </a-button>
                                        </div>
                                    </template>
                                </obs-title>
                                <a-loading v-if="rspLoading" class="json-box"></a-loading>
                                <div v-else ref="json-box-rsp" class="json-box">
                                    <div class="json-box-title"
                                        @click="toggleJsonCollapse('rspHeaders')">
                                        <span>Headers</span>
                                        <h-icon :name="jsonShow.rspHeaders ? 'packup' : 'unfold'"></h-icon>
                                    </div>
                                    <div v-show="jsonShow.rspHeaders" class="json-box-editor">
                                        <vue-json-editor
                                            v-if="rspJsonBox"
                                            ref="rspHeaders"
                                            v-model="rspJsonData.rspHeaders"
                                            :showBtns="false"
                                            :mode="'code'"
                                            lang="zh"
                                            :expandedOnStart="true"
                                            class="json-record"
                                            :style="{ height: jsonHeight }"
                                            @json-change="onJsonChange('Headers')"
                                            @has-error="onJsonError('Headers')">
                                        </vue-json-editor>
                                    </div>
                                    <div class="json-box-title" style="margin-top: 10px;"
                                        @click="toggleJsonCollapse('rspBody')">
                                        <span>Body</span>
                                        <h-icon :name="jsonShow.rspBody ? 'packup' : 'unfold'"></h-icon>
                                    </div>
                                    <div v-show="jsonShow.rspBody" class="json-box-editor">
                                        <vue-json-editor
                                            v-if="rspJsonBox"
                                            ref="rspBody"
                                            v-model="rspJsonData.rspBody"
                                            :showBtns="false"
                                            :mode="'code'"
                                            lang="zh"
                                            :expandedOnStart="true"
                                            class="json-record"
                                            :style="{ height: jsonHeight }"
                                            @json-change="onJsonChange('Body')"
                                            @has-error="onJsonError('Body')">
                                        </vue-json-editor>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </h-split>
                </template>
            </menu-layout>
        </div>
    </div>
    <a-loading v-if="loading" class="tab-loading"></a-loading>

    <!-- 保存用例弹窗 -->
    <save-use-case-modal
        v-model="msgBoxInfo.status"
        :modalInfo="msgBoxInfo"
        @update="handleUseCaseUpdate">
    </save-use-case-modal>

    <!-- 另存用例弹窗 -->
    <save-as-new-case-modal
        v-model="saveAsInfo.status"
        :modalInfo="saveAsInfo"
        @update="handleUseCaseUpdate">
    </save-as-new-case-modal>

    <!-- 时间戳 -->
    <timestampLinkDrawer
         v-model="timestampLinkInfo.status"
        :modalInfo="timestampLinkInfo">
    </timestampLinkDrawer>
  </div>
</template>

<script>
import obsTitle from '@/components/common/title/obsTitle';
import aButton from '@/components/common/button/aButton';
import aLoading from '@/components/common/loading/aLoading';
import menuLayout from '@/components/common/menuLayout/menuLayout';
import vueJsonEditor from 'vue-json-editor-fix-cn';
import saveAsNewCaseModal from './modal/saveAsNewCaseModal.vue';
import saveUseCaseModal from './modal/saveUseCaseModal.vue';
import timestampLinkDrawer from './modal/timestampLinkDrawer';
import { getSendPacketReceivedEndpointConfigs } from '@/api/memoryApi';
import { getUseCaseList, getUseCaseInfo, sendPacket } from '@/api/networkApi';
export default {
    props: {
        productInstNo: {
            type: String,
            default: ''
        }
    },
    components: { aButton, obsTitle, aLoading, vueJsonEditor, menuLayout, saveUseCaseModal, saveAsNewCaseModal, timestampLinkDrawer },
    data() {
        return {
            loading: false,
            reqLoading: false,
            rspLoading: false,
            splitVal: 0.5,
            menuFoldStatus: false,
            jsonShow: {
                reqHeaders: false,
                reqBody: true,
                rspHeaders: false,
                rspBody: true
            },
            jsonHeight: '200px',
            endpointList: [],
            protocolList: [
                {
                    value: 'LDP_MSG',
                    label: 'LDPMSG'
                }
            ],
            formItems: {
                endpoint: '',
                protocol: ''
            },
            reqJsonTitle: {
                label: '请求'
            },
            reqJsonData: {
                reqHeaders: '',
                reqBody: ''
            },
            rspJsonTitle: {
                label: '响应'
            },
            rspJsonData: {
                rspHeaders: '',
                rspBody: ''
            },
            rspCallInfo: {
                statusCode: '',
                totalTime: '',
                callTime: '',
                resourceSize: ''
            },
            useCaseData: [],
            menuList: [],
            reqJsonBox: true,
            rspJsonBox: true,
            msgBoxInfo: {
                status: false
            },
            saveAsInfo: {
                status: false
            },
            headersJsonFormat: true,
            bodyJsonFormat: true,
            selectUseCase: '',
            searchText: '',
            // 时间戳
            isTimeShow: false,
            timeStamp: [],
            timestampLinkInfo: {
                status: false
            }
        };
    },
    watch: {
        /**
        * 监听请求头内容this.reqJsonData.reqHeaders，判断请求头内容中是否包含40号域内容
        * 联动控制 时间戳开关
        * 1. 无40号域：时间戳开关-关
        * 2. 有40号域且值为对象类型（例如："40": { }）：时间戳开关-开
        */
        'reqJsonData.reqHeaders'(newVal) {
            const timestampField = newVal?.['OptionalHead']?.['40'];
            this.isTimeShow = timestampField && typeof timestampField === 'object' && !Array.isArray(timestampField);
        }
    },
    mounted() {
        this.disabledTextArea();
        window.addEventListener('resize', this.adjustJsonHeight);
        this.adjustJsonHeight();
    },
    beforeDestroy() {
        window.removeEventListener('resize', this.adjustJsonHeight);
    },
    methods: {
        // 初始化
        async initData() {
            this.loading = true;
            // 清理页面数据
            this.cleanData();
            // 接入点列表
            await this.queryendpoint();
            // 用例列表
            await this.queryUseCaseList();
            // 重置form表单校验结果
            this.$refs['formItems'].resetValidate();
            setTimeout(() => {
                this.loading = false;
            }, 200);
        },
        cleanData() {
            this.reqJsonData = {
                reqHeaders: {},
                reqBody: {}
            };
            this.rspJsonData = {
                rspHeaders: {},
                rspBody: {}
            };
            this.rspCallInfo = {};
            // 重置用例下拉框
            this.selectUseCase = '';
            this.searchText = '';
            // 重置时间戳开关
            this.isTimeShow = false;
            this.timeStamp = [];
        },
        // 模糊搜索
        fuzzySearch(_e) {
            this.menuList = this.searchText
                ? this.useCaseData.filter(
                    (item) => item.name.indexOf(this.searchText) !== -1
                )
                : [...this.useCaseData];
        },
        // json编辑器展开收起
        toggleJsonCollapse(item) {
            this.jsonShow[item] = !this.jsonShow[item];

            if (item === 'reqBody' && this.jsonShow.reqHeaders) {
                // 自动滚动到body位置
                this.$nextTick(() => {
                    const jsonBox = this.$refs['json-box'];
                    if (jsonBox) {
                        const scrollTop = parseInt(this.jsonHeight, 10);
                        jsonBox.scrollTop = scrollTop;
                    }
                });
            }

            if (item === 'rspBody' && this.jsonShow.rspHeaders) {
                // 自动滚动到body位置
                this.$nextTick(() => {
                    const jsonBox = this.$refs['json-box-rsp'];
                    if (jsonBox) {
                        const scrollTop = parseInt(this.jsonHeight, 10);
                        jsonBox.scrollTop = scrollTop;
                    }
                });
            }
        },
        // 计算json编辑器高度
        adjustJsonHeight() {
            const totalHeight = this.$refs['json-box']?.offsetHeight;
            const expendHeight = (totalHeight - 100) + 'px';
            this.jsonHeight = expendHeight;
            this.disabledTextArea();
        },
        // 获取接入点列表
        async queryendpoint() {
            const params = {
                productId: this.productInstNo,
                protocol: this.formItems.protocol
            };
            try {
                const res = await getSendPacketReceivedEndpointConfigs(params);
                if (res.code === '200') {
                    this.endpointList = (res?.data?.configs || []).filter(o => {
                        return o?.supportPacketSender !== undefined ? o.supportPacketSender === true : true;
                    });
                    this.formItems.endpoint = this.endpointList?.[0]?.id || '';
                } else if (res.code?.length === 8) {
                    this.endpointList = [];
                    this.$hMessage.error(res.message);
                }
            } catch (err) {
                this.endpointList = [];
            }
        },
        // 获取发包用例列表
        async queryUseCaseList() {
            this.useCaseData = [];
            const params = {
                productId: this.productInstNo,
                protocol: this.formItems.protocol
            };
            try {
                const res = await getUseCaseList(params);
                if (res.code === '200') {
                    // 用例列表
                    this.useCaseData = res.data || [];
                    this.fuzzySearch(this.searchText);
                } else if (res.code?.length === 8) {
                    this.$hMessage.error(res.message);
                }
            } catch (err) {
                console.error(err);
            }
        },
        // 获取发包用例详情
        async queryUseCaseInfo(useCaseId) {
            this.reqJsonData = {
                reqHeaders: {},
                reqBody: {}
            };

            if (!useCaseId) return;

            this.reqLoading = true;
            const params = {
                productId: this.productInstNo,
                useCaseId: useCaseId,
                protocol: this.formItems.protocol
            };
            try {
                const res = await getUseCaseInfo(params);
                if (res.code === '200') {
                    // 用例列表
                    this.reqJsonData.reqHeaders = JSON.parse(res?.data?.header) || {};
                    this.reqJsonData.reqBody = JSON.parse(res?.data?.body) || {};
                } else if (res.code?.length === 8) {
                    this.$hMessage.error(res.message);
                }
            } catch (err) {
                console.error(err);
            } finally {
                this.reqLoading = false;
                /**
                 * 获取用例详情 header 内容中的 OptionalHead对象中 40号域
                 * 联动控制 时间戳 开关
                 * 1. 无40号域：时间戳开关-关
                 * 2. 有40号域且值为对象（例如："40": {}）：时间戳开关-开
                 */
                const timestampField = this.reqJsonData?.reqHeaders?.['OptionalHead']?.['40'];
                this.isTimeShow = timestampField && typeof timestampField === 'object' && !Array.isArray(timestampField);
            }

            // 销毁重建请求json编辑框
            this.reqJsonBox = false;
            this.$nextTick(() => {
                this.reqJsonBox = true;
            });
        },

        /**
        * 切换时间戳开关的绑定值时
        * 触发更新this.reqJsonData.reqHeaders.OptionalHead对象中40号域的值
        * 1. 开启时间戳开关：新增或修改40号域值为空对象，即设置"40": {}
        * 2. 关闭时间戳开关：删除40号域，即删除"40": {}
        */
        toggleTimeShow(val) {
            // 确保 reqJsonData 存在
            if (!this.reqJsonData) {
                this.reqJsonData = {};
            }

            // 确保 reqHeaders 存在
            if (!this.reqJsonData.reqHeaders) {
                this.$set(this.reqJsonData, 'reqHeaders', {});
            }

            // 确保 OptionalHead 存在
            if (!this.reqJsonData.reqHeaders.OptionalHead) {
                this.$set(this.reqJsonData.reqHeaders, 'OptionalHead', {});
            }

            if (val) {
            // 开启时间戳开关，新增或修改40号域值为空对象
                this.$set(this.reqJsonData.reqHeaders.OptionalHead, '40', {});
            } else {
            // 关闭时间戳开关，删除40号域
                this.$delete(this.reqJsonData.reqHeaders.OptionalHead, '40');
            }
        },

        // 切换用例列表
        async handleUseCaseChange(val) {
            // 重置json格式校验状态
            this.headersJsonFormat = true;
            this.bodyJsonFormat = true;

            if (val) {
                this.selectUseCase = val;
                // 获取用例内容更新
                await this.queryUseCaseInfo(val);
            }
        },
        handleJsonFormat(type, isFormatCorrect) {
            if (type === 'Headers') {
                this.headersJsonFormat = isFormatCorrect;
            } else if (type === 'Body') {
                this.bodyJsonFormat = isFormatCorrect;
            }
        },
        onJsonError(type) {
            this.handleJsonFormat(type, false);
        },
        onJsonChange(type) {
            this.handleJsonFormat(type, true);
        },
        // 打开保存用例弹窗
        handleSaveUseCase() {
            // 校验用例请求内容格式
            if (!this.headersJsonFormat) {
                this.$hMessage.error('请求Headers为空或格式不正确');
                return;
            }
            if (!this.bodyJsonFormat) {
                this.$hMessage.error('请求Body内容为空或格式不正确');
                return;
            }

            const params = {
                productId: this.productInstNo,
                id: this.selectUseCase,
                name: (this.useCaseData || []).find(o => o.id === this.selectUseCase)?.name,
                protocol: this.formItems.protocol,
                header: JSON.stringify(this.reqJsonData.reqHeaders),
                body: JSON.stringify(this.reqJsonData.reqBody)
            };
            // 校验用例选择是否选中
            if (!this.selectUseCase) {
                this.saveAsInfo.status = true;
                this.saveAsInfo.params = params;
            } else {
                this.saveAsInfo.status = false;
                this.msgBoxInfo.status = true;
                this.msgBoxInfo.params = params;
            }
        },
        // 处理用例更新和新建用例
        async handleUseCaseUpdate(id) {
            this.loading = true;
            // 用例列表
            await this.queryUseCaseList();
            this.selectUseCase = id;
            await this.queryUseCaseInfo(id);
            this.$nextTick(() => {
                this.loading = false;
            });
        },
        // 发包请求
        async handleSendPacket() {
            this.$refs['formItems'].validate((valid) => {
                if (!valid) {
                    return;
                }
            });

            // 判断发包协议是否与接入点定义协议匹配
            const pointObj = this.endpointList?.find(o => o.id === this.formItems.endpoint);
            if (pointObj.protocol !== this.formItems.protocol) {
                return this.$hMessage.error({
                    duration: 2,
                    content: `所选接入点不支持${this.formItems.protocol}协议发包`
                });
            }

            // 校验用例请求内容格式
            if (!this.headersJsonFormat) {
                this.$hMessage.error('请求Headers为空或格式不正确');
                return;
            }
            if (!this.bodyJsonFormat) {
                this.$hMessage.error('请求Body内容为空或格式不正确');
                return;
            }

            this.rspLoading = true;
            let rspCallInfo = {};
            this.rspJsonData = {
                rspHeaders: {},
                rspBody: {},
                timeStamp: []
            };
            const params = {
                productId: this.productInstNo,
                endpointId: this.formItems.endpoint,
                protocol: this.formItems.protocol,
                useCaseId: this.selectUseCase,
                header: JSON.stringify(this.reqJsonData.reqHeaders),
                body: JSON.stringify(this.reqJsonData.reqBody)
            };
            try {
                const res = await sendPacket(params);
                if (res.code === '200') {
                    this.$hMessage.success('发包成功！');
                    this.rspJsonData.rspHeaders = JSON.parse(res.data?.header) || {};
                    this.rspJsonData.rspBody = JSON.parse(res.data?.body) || {};
                    // 链路耗时数据
                    this.timeStamp = res?.data?.tracing || [];

                    // 更新耗时信息
                    const totalTime = (res.tracing || []).find(o => o.spanName === 'appApmPacketSendEnterLinkLatency')?.duration;
                    const callTime = (res.tracing || []).find(o => o.spanName === 'appApmPacketSendExitLinkLatency')?.duration;
                    rspCallInfo = {
                        statusCode: '200OK',
                        totalTime: `${totalTime || '-'}ms`,
                        callTime: `${callTime || '-'}ms`,
                        resourceSize: `${res.data?.length || '-'}B`,
                        hasTimeStamp: this.isTimeShow
                    };

                } else if (res.code?.length === 8) {
                    this.$hMessage.error(res.message);
                }
            } catch (err) {
                console.error(err);
            } finally {
                this.rspLoading = false;
                this.rspCallInfo = rspCallInfo;
            }

            // 销毁重建请求json编辑框
            this.rspJsonBox = false;
            this.$nextTick(() => {
                this.rspJsonBox = true;
                this.disabledTextArea();
            });
        },
        // 禁用应答json的编辑功能
        disabledTextArea() {
            this.$nextTick(() => {
                const rspHeaderDom = this.$refs['rspHeaders']?.$el?.children?.[0]?.children?.[0]?.children?.[1]?.children?.[0]?.children?.[0];
                if (rspHeaderDom) {
                    rspHeaderDom.disabled = true;
                }
                const rspBodyDom = this.$refs['rspBody']?.$el?.children?.[0]?.children?.[0]?.children?.[1]?.children?.[0]?.children?.[0];
                if (rspBodyDom) {
                    rspBodyDom.disabled = true;
                }
            });
        },
        // 时间戳
        handleTimeVisable() {
            this.timestampLinkInfo.status = true;
            this.timestampLinkInfo.timeStamp = this.timeStamp;
        }
    }
};
</script>

<style lang="less" scoped>
@import url("@/assets/css/json-editor");
@import url("@/assets/css/input.less");

.tab-box {
    height: 100%;

    .tab-loading {
        height: 100%;
        /* stylelint-disable-next-line plugin/z-index-value-constraint */
        z-index: 999;
    }

    .time-box {
        color: var(--font-color);
        display: flex;

        .split-solid {
            width: 1px;
            background-color: #474e6f;
            height: 26px;
            margin-left: 20px;
            margin-right: 20px;
            position: relative;
            top: 4px;
        }
    }

    .net-send {
        height: 100%;

        /deep/ .h-form-row .h-form-item {
            padding: 6px 5px;
        }

        .nodata-dropdown {
            line-height: 16px;
            height: 150px;
            color: #495060;

            .nodata-img {
                display: flex;
                width: 100%;
                justify-content: center;
            }
        }

        .result-box {
            height: calc(100% - 65px);
            margin-top: 15px;

            .uselist-menu {
                margin-top: 0;
                height: calc(100% - 88px);

                li {
                    padding: 0 5px 0 15px;

                    &::before {
                        content: none;
                    }

                    &:hover .uselist-menu-btn {
                        display: inline-block;
                    }
                }

                .uselist-menu-btn {
                    display: none;
                    float: right;
                    color: var(--link-color);

                    &:hover {
                        text-decoration: underline;
                    }
                }
            }

            /deep/ .h-split-trigger {
                border: none;
            }

            /deep/ .h-split-trigger-vertical {
                width: 4px;
                background: var(--main-color);
            }

            .split-pane {
                height: 100%;
            }

            .split-pane-content {
                height: 100%;
                background-color: var(--wrapper-color);
                border-radius: var(--border-radius);
                overflow: auto;
            }

            .json-box {
                height: calc(100% - 48px);
                border-radius: 4px;
                padding: 0 10px;
                overflow: auto;

                .json-box-title {
                    height: 40px;
                    background: #323953;
                    border-radius: 4px 4px 0 0;
                    padding: 10px;
                    color: var(--font-color);
                    font-weight: 500;

                    span {
                        font-size: 14px;
                    }

                    &:hover {
                        cursor: pointer;
                    }
                }
            }

            .json-record {
                height: calc(100% - 50px);
            }
        }
    }
}
</style>
