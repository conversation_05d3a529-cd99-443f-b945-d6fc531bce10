<template>
    <div>
        <h-msg-box
            v-model="modalData.status"
            title="添加链路日志配置"
            width="600"
            :mask-closable="false"
            :escClose="true"
            @on-open="handleOpen">
            <h-form
                ref="form"
                :model="form"
                :label-width="120"
                :rules="rules">
                <h-form-item
                    label="应用节点类型"
                    prop="instanceType"
                    required>
                    <h-select
                        v-model="form.instanceType"
                        placeholder="请选择"
                        :clearable="false"
                        @on-change="handleInsTypeChange">
                        <h-option
                            v-for="option in instanceTypeOptions"
                            :key="option.value"
                            :value="option.value">
                            {{ option.label }}
                        </h-option>
                    </h-select>
                </h-form-item>

                <h-form-item
                    label="应用节点名"
                    prop="instanceName"
                    required>
                    <h-input
                        v-model.trim="form.instanceName"
                        placeholder="请输入应用节点名"
                        :maxlength="100"
                    />
                </h-form-item>

                <h-form-item
                    label="服务器"
                    prop="serverIp"
                    required
                    :validRules="ip4Rule">
                    <h-input
                        v-model.trim="form.serverIp"
                        :maxlength="100"
                        placeholder="请输入服务器"
                    />
                </h-form-item>

                <h-form-item
                    label="日志类型"
                    prop="logType"
                    required>
                    <h-select
                        v-model="form.logType"
                        placeholder="请选择"
                        setDefSelect
                        disabled>
                        <h-option
                            v-for="option in logTypeOptions"
                            :key="option.value"
                            :value="option.value">
                            {{ option.label }}
                        </h-option>
                    </h-select>
                </h-form-item>

                <h-form-item
                    label="时延日志输出目录"
                    prop="logDir"
                    required>
                    <h-input
                        v-model.trim="form.logDir"
                        :maxlength="255"
                        placeholder="请输入时延日志输出目录"
                    />
                </h-form-item>

                <h-form-item
                    label="时延日志关键字"
                    prop="logNameKeyWord"
                    required>
                    <h-input
                        v-model.trim="form.logNameKeyWord"
                        :maxlength="255"
                        placeholder="请输入时延日志关键字"
                    />
                </h-form-item>
            </h-form>

            <template v-slot:footer>
                <a-button @click="handleCancel">取消</a-button>
                <a-button
                    type="primary"
                    :loading="loading"
                    @click="handleOk">确定
                </a-button>
            </template>
        </h-msg-box>
    </div>
</template>

<script>
import { stringLengthRule } from '@/utils/validate';
import aButton from '@/components/common/button/aButton';
import { addLogSource, querytraceModelInsType } from '@/api/productApi';
import { v4 as uuidv4 } from 'uuid';
export default {
    name: 'AddLogSourceModal',
    components: { aButton },
    props: {
        modalInfo: {
            type: Object,
            default: null
        }
    },
    data() {
        return {
            modalData: this.modalInfo,
            logTypeOptions: [
                { value: 'timelog', label: 'timelog' }
            ],
            instanceTypeOptions: [],
            form: {
                instanceName: '',
                instanceType: '',
                serverIp: '',
                logDir: '',
                logType: '',
                logNameKeyWord: ''
            },
            ip4Rule: ['ip4'],
            rules: {
                instanceName: [{ validator: stringLengthRule(50), trigger: 'change, blur' }],
                logDir: [{ validator: stringLengthRule(100), trigger: 'change, blur' }],
                logNameKeyWord: [{ validator: stringLengthRule(100), trigger: 'change, blur' }]
            },
            loading: false
        };
    },
    computed: {
        // 获取应用类型字典 `appTypeDictDesc`，
        appTypeDictDesc() {
            return this.$store?.state?.apmDirDesc?.appTypeDictDesc || {};
        }
    },
    methods: {
        handleOk() {
            this.$refs.form.validate(valid => {
                if (valid) {
                    this.addLogSource();
                }
            });
        },
        /**
         * 打开弹窗
         *
         * 异步获取应用节点类型选项并处理为下拉选择框的选项格式
         * 将类型值和字典中对应的标签处理为对象数组 `instanceTypeOptions` 并赋值到组件属性 `instanceTypeOptions`
         */
        async handleOpen() {
            const insTypeArr = await this.getInsTypeOptions();
            const instanceTypeOptions = [];
            Array.isArray(insTypeArr) && insTypeArr.forEach(item => {
                instanceTypeOptions.push({
                    value: item,
                    label: this.appTypeDictDesc?.[item] || item
                });
            });
            this.instanceTypeOptions = instanceTypeOptions;
        },
        /**
         * 获取链路日志配置的应用类型
         */
        async getInsTypeOptions() {
            let insTypeArr = [];
            try {
                const param = {
                    productId: this.modalData.productId,
                    selfManage: true
                };
                const res = await querytraceModelInsType(param);
                if (res.code === '200') {
                    insTypeArr = res.data || [];
                }
            } catch (err) {
                console.error(err);
            }

            return insTypeArr;
        },
        /**
         * 切换应用类型，自动生成应用节点名
         * 生成格式： 应用类型名#uuid(截取8位)
         */
        handleInsTypeChange(val) {
            if (!val) return;

            const typeName = this.appTypeDictDesc[val]; // 获取应用类型名称
            // 引入uuid库并截取前8位
            const uuid = uuidv4().substring(0, 8);

            // 生成新的应用节点名并赋值给form.instanceName
            this.form.instanceName = `${typeName}#${uuid}`; // 设置实例名称
        },

        /**
         * 调用接口-添加链路日志配置
         */
        async addLogSource() {
            this.loading = true;
            try {
                const param = {
                    productId: this.modalData.productId,
                    ...this.form
                };
                const res = await addLogSource(param);
                if (res.code === '200') {
                    this.$hMessage.success('添加成功');
                    // 通知父组件更新数据
                    this.$emit('query-log-config');
                    this.modalData.status = false;
                }
            } catch (err) {
                console.error(err);
            } finally {
                this.loading = false;
            }
        },
        handleCancel() {
            this.modalData.status = false;
        }
    }
};
</script>

