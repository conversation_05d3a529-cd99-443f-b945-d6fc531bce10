/*
 * @Description: apmMonitorBar
 * @Author: <PERSON><PERSON>
 * @Date: 2023-04-11 13:28:31
 * @LastEditTime: 2023-08-30 13:26:32
 * @LastEditors: Z<PERSON>
 */
import './apmMonitorBar.less';
import apmScroll from '@/components/common/bestScroll/apmScroll';
export default {
    name: 'apmMonitorBar',
    props: {
        barList: {
            type: Array,
            default: () => []
        }
    },
    components: { apmScroll },
    data() {
        return {
            selectedKey: ''
        };
    },
    mounted() {
        this.handleSelectKey(this.$route.name, this.$route.query?.featureAttributes);
    },
    methods: {
        handleClick(key) {
            this.selectedKey = key;
            this.$emit('jump', key);
        },
        handleSelectKey(name, featureAttributes) {
            this.selectedKey = featureAttributes ? featureAttributes : name;
        }
    },
    watch: {
        $route(to, from){
            this.handleSelectKey(to.name, to.query?.featureAttributes);
        }
    },
    render() {
        return <apm-scroll class="bar-wrapper">
            <div class="bar-box">
                {
                    this.barList.map(item => {
                        return <span
                            class={this.selectedKey === item.key ? 'monitor-active' : ''}
                            onClick={() => { this.handleClick(item.key); }}>{item.label}</span>;
                    })
                }
            </div>
        </apm-scroll>;
    }
};
