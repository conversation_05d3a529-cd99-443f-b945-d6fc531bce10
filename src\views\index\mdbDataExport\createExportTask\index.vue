<template>
  <div class="create-step">
    <h-steps :current="currentStep" class="steps-box">
      <h-step v-for="item in stepList" :key="item" :title="item"> </h-step>
    </h-steps>

    <div ref="content-box" class="content-box">
      <a-loading v-if="loading"></a-loading>
      <!-- 提示信息 -->
      <div class="tips-box">
        <div class="tip-content">
          {{ createTips[currentStep].content }}
        </div>
      </div>

      <!-- 确定导出内容 -->
      <div v-show="currentStep === 0" class="step-content">
        <table-selector
          ref="tableSelecor"
          :key="endpointId"
          :tableLeft="tableLeft"
          :tableRight="tableRight"
          :endpointId="endpointId"
          :productId="productId"
          :instanceList="instanceList"
          @update-tables="handleUpdateTables"
          @resetLogin="resetLogin"
        >
        </table-selector>
      </div>

      <!-- 信息核对 -->
      <div v-show="currentStep === 1" class="step-content">
        <div class="step-content-path h-form-item h-form-item-required">
          <span class="h-form-item-label">拷贝至APM数据存储目录</span>
          <h-input v-model="path" disabled />
        </div>
        <a-simple-table
          :key="currentStep"
          showTitle
          :columns="columns"
          :tableData="tableRight"
          :hasPage="false"
          :height="confirmTableHeight"
        >
        </a-simple-table>
      </div>
    </div>

    <div class="buttom-box">
      <a-button v-show="currentStep" type="dark" @click="handleStepChange('-')">
        上一步
      </a-button>
      <a-button
        v-show="currentStep !== 1"
        type="primary"
        :loading="nextStepLoading"
        @click="handleStepChange('+')"
      >
        下一步：{{ stepList[currentStep + 1] }}
      </a-button>
      <a-button
        v-show="currentStep === 1"
        type="primary"
        :loading="confirmLoading"
        @click="handleExport"
      >
        导出MDB数据
      </a-button>
      <a-button type="dark" @click="handleCancel">取消</a-button>
    </div>
  </div>
</template>

<script>
import aButton from '@/components/common/button/aButton';
import aSimpleTable from '@/components/common/table/aSimpleTable';
import aLoading from '@/components/common/loading/aLoading';
import tableSelector from './tableSelector.vue';
import { createExportTask, getExportPath } from '@/api/mdbExportApi';
import { CREATE_TASK_TIPS } from '../constant';
export default {
    name: 'CreateAppearanceTask',
    components: { aButton, aSimpleTable, aLoading, tableSelector },
    props: {
        productId: {
            type: String,
            default: ''
        },
        endpointId: {
            type: String
        },
        instanceList: {
            type: Array
        }
    },
    data() {
        return {
            loading: false,
            currentStep: 0,
            path: null,
            confirmLoading: false,
            endpointIdVal: this.endpointId,
            nextStepLoading: false,
            stepList: ['确定导出内容', '信息核对'],
            createTips: CREATE_TASK_TIPS,
            tableLeft: [], // 可导出表
            tableRight: [], // 选择的导出表
            columns: [
                {
                    title: '表名',
                    key: 'tableName',
                    minWidth: 250,
                    ellipsis: true
                },
                {
                    title: '服务器',
                    key: 'hostName',
                    minWidth: 220,
                    ellipsis: true
                },
                {
                    title: '节点',
                    key: 'instanceName',
                    minWidth: 200,
                    ellipsis: true
                }
            ],
            confirmTableHeight: 0
        };
    },
    mounted() {
        window.addEventListener('resize', this.setTableHeight);
        // 延迟一段时间再调用，确保所有元素和样式已渲染完毕
        this.$nextTick(() => {
            this.setTableHeight();
        });
    },
    beforeDestroy() {
        window.removeEventListener('resize', this.setTableHeight);
    },
    methods: {
    /**
     * 更新选择的导出表
     * @param {Object} tables 包含可选表和选中表的信息
     */
        handleUpdateTables({ availableTables, selectedTables }) {
            this.tableLeft = availableTables;
            this.tableRight = selectedTables;
        },
        /**
     * 更新导出表规则
     * @param {Array} data 更新后的导出表规则数据
     */
        handleUpdateRules(data) {
            this.tableRight = data;
        },
        /**
         * 重置登录态
         */
        resetLogin() {
            this.$emit('resetLogin');
        },
        /**
     * 执行导出操作
     * @async
     */
        async handleExport() {
            this.confirmLoading = true;
            try {
                // 调用执行导出接口
                const params = {
                    productId: this.productId,
                    tables: this.tableRight
                };
                const res = await createExportTask(JSON.stringify(params));
                if (res.code === '200') {
                    this.$hMessage.success('执行导出中!');
                    // 跳转至列表页
                    this.handleCancel();
                } else if (res?.code?.length === 8) {
                    this.$hMessage.error('执行导出失败!');
                }
            } catch (err) {
                console.error(err);
            }
            this.confirmLoading = false;
        },
        /**
     * 处理步骤的变更操作
     * @param {String} ope 表示步骤变化的方向('+'表示下一步，'-'表示上一步)
     */
        handleStepChange(ope) {
            if (ope === '+') {
                if (this.currentStep === 0 && !this.tableRight.length) {
                    this.$hMessage.warning('请先选择导出的表');
                    return;
                }
                this.getExportPath();
                this.currentStep += 1;
            } else {
                this.currentStep -= 1;
            }
        },
        /**
     * 处理取消操作
     */
        handleCancel() {
            this.$emit('backToHome');
        },
        /**
     * 设置表格高度，根据内容框的高度进行计算
     */
        setTableHeight() {
            const boxHeight = this.$refs['content-box']?.offsetHeight;
            this.confirmTableHeight = boxHeight - 80;
        },
        /**
     * 获取目录
     */
        async getExportPath() {
            try {
                const res = await getExportPath();
                this.path = res?.data?.path ?? '';
            } catch (error) {
                console.log('获取目录err,', error);
            }
        }
    }
};
</script>

<style lang="less" scoped>
.h-icon {
    &:hover {
        cursor: pointer;
    }
}
</style>

<style lang="less" scoped>
@import url("@/components/ustTableVerification/createTask.less");

.create-step {
    position: relative;
    min-width: 1000px;
    height: calc(100% - 44px);

    .content-box {
        overflow: hidden;
    }
}

.step-content {
    height: calc(100% - 60px);
    width: 100%;
    margin-top: 10px;

    &-path {
        display: flex;
        align-items: center;
        color: #fff;
        white-space: nowrap;
        margin-bottom: 20px !important;

        .h-form-item-label {
            margin-right: 11px;
        }

        .h-input-wrapper {
            width: 404px;
        }
    }
}
</style>
