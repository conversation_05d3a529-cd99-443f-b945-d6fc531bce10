// 国际化使用示例

// 1. 在Vue组件中使用
export default {
    template: `
        <div>
            <!-- 基本使用 -->
            <h-button>{{ $t('common.query') }}</h-button>
            
            <!-- 页面特定文案 -->
            <div>{{ $t('pages.managementQuery.pleaseSelect') }}</div>
            
            <!-- 组件特定文案 -->
            <span>{{ $t('components.ldpTable.noData') }}</span>
        </div>
    `,
    methods: {
        showMessage() {
            // 在JavaScript中使用
            this.$hMessage.success(this.$t('common.operationSuccess'));
        }
    }
};

// 2. 在JavaScript文件中使用
import { i18n } from '@/locales';

function showAlert() {
    alert(i18n.t('common.error'));
}

// 3. 常用文案路径参考
const commonPaths = {
    // 通用操作
    'common.query': '查询',
    'common.add': '添加',
    'common.edit': '编辑',
    'common.delete': '删除',
    'common.save': '保存',
    'common.cancel': '取消',
    
    // 状态提示
    'common.success': '成功',
    'common.failed': '失败',
    'common.loading': '加载中',
    'common.noData': '暂无数据',
    
    // 页面文案
    'pages.managementQuery.pleaseSelect': '请选择',
    'pages.sqlCores.query': '查询',
    'pages.analyseData.config': '配置报表',
    
    // 组件文案
    'components.ldpTable.export': '导出',
    'components.common.confirm': '确认'
};

// 4. 国际化key命名规范
/*
结构: {category}.{module}.{key}

category: 
- common: 通用文案
- pages: 页面特定文案  
- components: 组件特定文案

module: 具体的页面名或组件名

key: 具体的文案标识，使用小驼峰命名
*/