/*
 * @Description: apm-table
 * @Author: <PERSON><PERSON>
 * @Date: 2022-11-28 13:52:38
 * @LastEditTime: 2023-03-20 14:59:11
 * @LastEditors: <PERSON><PERSON> Ying
 */
import './treeTable.less';
export default {
    name: 'apm-table',
    props: {
        hasDarkClass: {
            type: Boolean,
            default: true
        },
        columns: {
            type: Array,
            default: []
        },
        treeData: {
            type: Array,
            default: []
        },
        height: {
            type: Number,
            default: 300
        },
        loading: {
            type: Boolean,
            default: false
        },
        showTitle: {
            type: Boolean,
            default: false
        },
        border: {
            type: Boolean,
            default: false
        },
        canDrag: {
            type: Boolean,
            default: false
        },
        disabledHover: {
            type: Boolean,
            default: true
        },
        highlightRow: {
            type: Boolean,
            default: false
        }
    },
    data() {
        return {
        };
    },
    methods: {
    },
    render() {
        return (
            <main class={ this.hasDarkClass ? 'tree-table' : ''}>
                <h-simple-tree-gird
                    columns={this.columns}
                    data={this.treeData}
                    height={this.height}
                    loading={this.loading}
                    showTitle={this.showTitle}
                    border={this.border}
                    canDrag={this.canDrag}
                    disabledHover={this.disabledHover}
                    highlightRow={this.highlightRow}
                ></h-simple-tree-gird>
            </main>
        );
    }
};
