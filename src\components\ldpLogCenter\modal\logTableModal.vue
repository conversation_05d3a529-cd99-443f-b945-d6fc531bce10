<template>
    <div>
        <h-drawer
            ref="drawer-box"
            v-model="modalData.status"
            width="65"
            title="账户回库错误"
            >
                <div class="text-box">
                    <p style="color: #fff;" :title="modalData.ClusterName">集群：{{ modalData.ClusterName }}</p>
                    <h-form
                        ref="formItems1"
                        class="form-box"
                        :model="formItems"
                        inline
                        @submit.native.prevent
                    >
                        <h-form-item prop="tableInProcess" :label-width="0" >
                            <h-checkbox v-model="formItems.tableInProcess" @on-change="handleInputChange">仅显剩余错误不为0</h-checkbox>
                        </h-form-item>
                        <h-form-item prop="locateField" :label-width="0" class="select-box">
                            <h-select v-model="formItems.locateField" style="width: 100px;" :clearable="true" placeholder="账户类型" @on-change="handleInputChange">
                                <h-option v-for="item in modalData.locateFieldList" :key="item.FieldName" :value="item.FieldName">{{item.FieldDesc}}</h-option>
                            </h-select>
                        </h-form-item>
                        <h-form-item prop="locateValue" :label-width="0" :validRules="regRule" class="input-box">
                            <h-input
                                v-model.trim="formItems.locateValue"
                                style="width: 200px;"
                                :clearable="true"
                                icon="search"
                                placeholder="多个账户使用英文逗号隔开"
                                :disabled="!formItems.locateField"
                                @on-blur="handleInputChange"
                                @on-enter="handleInputChange"
                                @on-click="handleInputChange"
                            >
                        </h-input>
                        </h-form-item>
                    </h-form>
                </div>
                <!-- 查询回库资金账号级别详细日志结果 -->
                <obs-table
                    ref="logTableData"
                    border
                    :height="logTableHeight"
                    :columns="logColumns"
                    :tableData="logTableData"
                    :loading="logLoading"
                    :hasPage="false"
                    showTitle
                />
                <br/>
                <div class="page-box">
                    <a-button type="dark" size="small" :disabled="logEndMsgNos.length <= 1 || buttonLoading"  @click="()=>handlePrev()"
                        >上一页</a-button
                    >&nbsp;&nbsp;
                    <a-button type="dark" size="small" :disabled="logTableData.length < pageSize || buttonLoading"  @click="()=>handleNext()"
                        >下一页</a-button
                    >
                </div>
        </h-drawer>
    </div>
</template>

<script>
import obsTable from '@/components/common/obsTable/obsTable';
import aButton from '@/components/common/button/aButton';
import { getManagerProxy } from '@/api/mcApi';
export default {
    name: 'LogTableModal',
    components: { aButton, obsTable },
    props: {
        modalInfo: {
            type: Object,
            default: null
        }
    },
    data() {
        return {
            modalData: this.modalInfo,
            regRule: [
                { test: /^(?:[^,]*(?:,[^,]*){0,49})$/, message: '支持多账号以英文逗号分隔且最多50个', trigger: 'blur' },
                {
                    test: (rule, value, callback) => {
                        const locateField = this.formItems.locateField;
                        const locateValue = this.formItems.locateValue;
                        if (!locateField && locateValue) {
                            callback(new Error('账户类型为空时，账户信息必须为空'));
                            return;
                        }
                        if (locateField && !locateValue) {
                            callback(new Error('请输入账户'));
                            return;
                        }
                        callback();
                    },
                    trigger: 'blur,change'
                }
            ],
            formItems: {
                locateField: '',
                locateValue: '',
                tableInProcess: true
            },
            buttonLoading: false,
            logTableHeight: 0,
            logEndMsgNos: [],
            logLoading: false,
            logColumns: [
                {
                    title: '账户类型',
                    key: 'LocateFieldName',
                    ellipsis: true,
                    render: (h, params) => {
                        const LocateFieldName = params?.row?.FieldDesc || this.modalData.locateFieldList.find(o => o?.FieldName === params?.row?.LocateField)?.FieldDesc || '-';
                        return h('span', {
                            attrs: {
                                title: LocateFieldName
                            }
                        }, LocateFieldName);
                    }
                },
                {
                    title: '账户',
                    key: 'LocateValue',
                    ellipsis: true
                },
                {
                    title: '剩余回库错误事务数',
                    key: 'BizErrorToResendTransCount',
                    ellipsis: true,
                    minWidth: 140
                },
                {
                    title: '最后一次错误号',
                    key: 'LastErrorNo',
                    ellipsis: true,
                    minWidth: 120
                },
                {
                    title: '回库错误事务总数',
                    key: 'BizErrorRspTotalCount',
                    ellipsis: true,
                    minWidth: 140
                },
                {
                    title: '操作',
                    key: 'action',
                    fixed: 'right',
                    width: 110,
                    render: (h, params) => {
                        return h('div', [
                            h('Button',
                                {
                                    props: {
                                        size: 'small',
                                        type: 'text'
                                    },
                                    on: {
                                        click: () => {
                                            this.$emit('config-status', params?.row, 'locateBulk');
                                        }
                                    }
                                },
                                '重试'
                            ),
                            h('Button',
                                {
                                    props: {
                                        size: 'small',
                                        type: 'text'
                                    },
                                    on: {
                                        click: () => {
                                            this.jumpTab({
                                                instanceId: this.modalData?.instanceId,
                                                locateField: params?.row?.LocateField || '',
                                                locateValue: params?.row?.LocateValue || '',
                                                clusterName: params?.row?.ClusterName || this.modalData?.ClusterName
                                            });
                                        }
                                    }
                                },
                                '日志'
                            )
                        ]);
                    }
                }
            ],
            logTableData: [],
            pageSize: 10,
            errorNoTableInfo: {
                status: false
            }
        };
    },
    watch: {
        'formItems.locateField': {
            handler(newVal, oldVal) {
                // 账户类型改变时，触发账户信息字段的验证
                this.$nextTick(() => {
                    this.$refs['formItems1'].validateField('locateValue');
                });
            },
            immediate: false
        }
    },
    beforeDestroy() {
        window.removeEventListener('resize', this.fetTableHeight);
    },
    mounted(){
        this.handleInputChange();
        this.fetTableHeight();
        window.addEventListener('resize', this.fetTableHeight);
    },
    methods: {
        async init() {
            this.clearLogData();
            await this.getBizErrInfo();
        },
        // 页面刷新--刷新当前页
        async handleRefresh(){
            await this.getBizErrInfo('polling');
        },
        handleInputChange() {
            // 检查账户类型和账户信息的逻辑关系
            const locateField = this.formItems.locateField;
            const locateValue = this.formItems.locateValue;

            // 如果账户类型为空，自动清空账户信息
            if (!locateField && locateValue) {
                this.formItems.locateValue = '';
            }

            // 如果账户类型不为空但账户信息为空，不执行查询
            if (locateField && !locateValue) {
                return;
            }

            this.$refs['formItems1'].validate(valid => {
                if (valid) {
                    this.init();
                }
            });
        },
        // 清空
        clearLogData() {
            this.logTableData = [];
            this.logEndMsgNos = [];
        },
        // 跳转
        jumpTab(params) {
            this.$emit('jump-tab', params);
            this.errorNoTableInfo.status = false;
            this.modalData.status = false;
        },
        // 跳转页处理
        handlePageChange(operation) {
            this.getBizErrInfo(operation);
        },
        // 上一页
        handlePrev(){
            this.handlePageChange('minus');
        },
        // 下一页
        handleNext() {
            this.handlePageChange('plus');
        },
        // 接口请求------获取失败账户的总数量及失败的redo数量
        async getBizErrInfo(type) {
            this.logLoading = true;
            this.buttonLoading = true;
            let endMsgNo = 0;
            if (type === 'minus'){
                endMsgNo = this.logEndMsgNos?.[this.logEndMsgNos?.length  - 3] || 0;
            } else if (type === 'polling'){
                endMsgNo = this.logEndMsgNos?.[this.logEndMsgNos?.length  - 2] || 0;
            } else {
                endMsgNo = this.logEndMsgNos?.[this.logEndMsgNos?.length - 1] || 0;
            }
            const param = [
                {
                    manageProxyIp: this.modalData?.ip,
                    manageProxyPort: this.modalData?.manageProxyPort,
                    pluginName: 'ldp_todb',
                    funcName: 'GetBizErrStatistics',
                    params: {
                        StartNo: endMsgNo,
                        Count: this.pageSize,
                        ClusterName: this.modalData?.ClusterName,
                        LocateField: this.formItems?.locateField || '',
                        LocateValue: this.formItems?.locateValue || '',
                        Delimiter: ',',
                        ToResendQuery: this.formItems.tableInProcess ? '1' : '0'
                    }
                }
            ];
            try {
                const res = await getManagerProxy(JSON.stringify(param));
                if (res.code === '200') {
                    if (res?.data?.[0]?.ErrorNo){
                        this.$hMessage.error({
                            content: res.data?.[0]?.ErrorMsg || '查询失败',
                            duration: 2.5
                        });
                        return;
                    }
                    if (type === 'minus') {
                        this.logEndMsgNos.pop();
                    } else if (type !== 'minus' && type !== 'polling') {
                        this.logEndMsgNos.push(res.data?.[0]?.NextNo);
                    }

                    this.logTableData = (res.data?.[0]?.BizErrStatistics || [])?.sort((a, b) => b?.BizErrorRspDropCount - a?.BizErrorRspDropCount);
                }
            } catch (err) {
                console.error(err);
            } finally {
                this.logLoading = false;
                this.buttonLoading = false;
            }
        },
        // 自适应表格高度
        fetTableHeight() {
            this.logTableHeight = this.$refs['drawer-box']?.$el?.offsetTop - (window.LOCAL_CONFIG.HEADER_VISIBLE === true ? 260 : 190);
        }
    }
};
</script>
<style lang="less" scoped >
@import url("@/assets/css/drawer.less");

.h-drawer-body {
    padding: 10px;
}

.text-box {
    display: flex;
    align-items: center;
    width: 100%;

    /deep/ .h-form-inline .h-form-item {
        display: inline-block;
        margin-right: 4px;
        vertical-align: top;
        margin-bottom: 0;
    }

    /deep/ .h-form-item-content {
        color: #fff;
    }

    /* stylelint-disable-next-line selector-class-pattern */
    /deep/ .h-form-item-requiredIcon {
        display: none;
    }

    .select-box {
        margin-right: -3px !important;

        /deep/ .h-select-selection {
            border-radius: 4px 0 0 4px;
        }

        /deep/ .h-select-single .h-select-selection .h-select-placeholder,
        /deep/ .h-select-single .h-select-selection .h-select-selected-value {
            padding-right: 15px;
        }
    }

    .input-box {
        /deep/ .h-input {
            border-radius: 0 4px 4px 0;
        }
    }
}

.text-box p {
    width: calc(100% - 500px);
    color: #fff;
    margin: 0 10px 0 0;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    flex-grow: 1;
    position: relative;
    padding: 0 0 0 15px;

    &::before {
        content: "";
        display: inline-block;
        position: absolute;
        left: 0;
        width: 4px;
        height: 17px;
        background: var(--link-color);
    }
}

.refresh-btn {
    position: absolute;
    color: var(--link-color);
    right: 40px;
    top: 8px;
}

.page-box {
    margin-right: 15px;
    color: var(--font-color);
    text-align: right;
}

/deep/.obs-table {
    height: auto;
    margin-top: 0;

    .table-box {
        margin: 0;
    }
}
</style>
