.table-box {
    width: 100%;
    height: 100%;
    margin-top: 10px;
    background-color: #262b40;
    border-radius: 4px;

    .table-title {
        display: flex;
        justify-content: space-between;
        align-items: center;
        width: 100%;
        height: 48px;
        border-bottom: 1px solid #31364a;

        & > .table-title-text {
            padding-left: 33px;
            font-size: 14px;
            color: #fff;

            &::before {
                display: inline-block;
                position: relative;
                left: -15px;
                top: 3px;
                content: "";
                width: 5px;
                height: 17px;
                background: var(--link-color);
            }
        }
    }

    & > .a-table {
        padding: 0 15px;
    }
}
