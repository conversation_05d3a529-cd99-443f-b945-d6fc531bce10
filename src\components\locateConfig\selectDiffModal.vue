<template>
    <div class="select-diff-modal">
        <h-msg-box-safe v-model="modalData.status" :escClose="true" :mask-closable="false" title="选择对比配置的节点">
            <div class="select-diff-modal-form">
                <h-form ref="formValidate" :model="formValidate" :label-width="100">
                    <h-form-item label="应用节点类型">
                        <span>节点定位AR</span>
                    </h-form-item>

                    <h-form-item label="配置类型">
                        <span>{{ locateType }}</span>
                    </h-form-item>

                    <h-form-item label="原始配置来源">
                        <span>{{ modalData.mode === 'mc' ? 'local' : configSourceType[formValidate.type] }}</span>
                    </h-form-item>

                    <h-form-item v-if="formValidate.type === 'locate_config' || modalData.mode === 'mc'" label="对比源节点" prop="sourceNode"  required>
                        <h-select  v-model="formValidate.sourceNode" autoPlacement transfer>
                            <h-option v-for="item in sourceNodes" :key="item.appInstanceId"
                                :value="item.appInstanceId"><span>{{ item.appInstanceName }}</span></h-option>
                        </h-select>
                    </h-form-item>
                    <h-form-item v-else label="对比源节点" prop="sourceNode">
                        <span>zookeeper</span>
                    </h-form-item>
                    <h-form-item label="对比目标节点" prop="targetNode" required>
                        <h-select v-model="formValidate.targetNode" multiple isCheckall autoPlacement transfer>
                            <h-option v-for="item in targetNodes" :key="item.appInstanceId"
                                :value="item.appInstanceId"><span>{{ item.appInstanceName }}</span></h-option>
                        </h-select>
                    </h-form-item>
                </h-form>
            </div>
            <template v-slot:footer>
                <a-button @click="modalData.status = false">取消</a-button>
                <a-button type="primary" :loading="loading" @click="onSubmit">确定</a-button>
            </template>
        </h-msg-box-safe>
    </div>
</template>
<script>
import aButton from '@/components/common/button/aButton';
import { LOCATETYPE } from './constant';
export default {
    props: {
        selectDiffModal: {
            type: Object,
            default: () => { }
        },
        productId: {
            type: String,
            default: ''
        }
    },
    data() {
        return {
            locateType: '',
            configSourceType: {
                locate_config: 'local',
                locate_rules: 'zookeeper'
            },
            modalData: this.selectDiffModal,
            loading: false,
            formValidate: {
                type: this.selectDiffModal.type,
                sourceNode: '',
                targetNode: []
            },
            instances: []
        };
    },
    mounted() {
        this.init();
    },
    computed: {
        sourceNodes: function() {
            if (!this.formValidate?.targetNode?.length) {
                return this.instances;
            }
            return this.instances.filter((item) => !this.formValidate?.targetNode.includes(item?.appInstanceId));
        },
        targetNodes: function() {
            if (this.formValidate.type === 'locate_config' || this.modalData.mode === 'mc'){
                if (!this.formValidate?.sourceNode?.length) {
                    return this.instances;
                }
                return this.instances.filter((item) => this.formValidate?.sourceNode !== item?.appInstanceId);
            }
            return this.instances;
        }
    },
    methods: {
        init(){
            this.locateType = LOCATETYPE.find(o => o.value ===  this.formValidate.type)?.label;
            this.instances = [...this.selectDiffModal.instances];
            if (this.formValidate.type === 'locate_config' || this.modalData.mode === 'mc'){
                this.formValidate.sourceNode = this.selectDiffModal.defaultInstanceId;
            } else {
                this.formValidate.targetNode = [this.selectDiffModal.defaultInstanceId];
            }
        },
        async onSubmit() {
            this.$refs['formValidate'].validate(async valid => {
                if (valid) {
                    try {
                        this.loading = true;
                        const sourceInstance = this.instances.filter((o) => this.formValidate.sourceNode === o?.appInstanceId)?.[0] || {};
                        const params = {
                            productId: this.productId,
                            configType: this.formValidate.type,
                            mainSource: {
                                id: sourceInstance?.id,
                                appInstanceId: this.formValidate.sourceNode,
                                configSourceType: this.modalData.mode === 'mc' ? 'local' : this.configSourceType[this.formValidate.type],
                                ownerInstance: sourceInstance?.ownerInstance || '',
                                mode: this.modalData.mode
                            },
                            compareSources: this.formValidate.targetNode.map(o => {
                                return {
                                    id: this.instances.filter((v) => o === v?.appInstanceId)?.[0]?.id,
                                    appInstanceId: o,
                                    configSourceType: this.modalData.mode === 'mc' ? 'local' : this.configSourceType[this.formValidate.type]
                                };
                            })
                        };
                        this.$emit('start-diff', params);
                    } finally {
                        this.loading = false;
                        this.modalData.status = false;
                    }
                }
            });
        }
    },
    components: { aButton }
};
</script>
<style scoped lang="less">
.select-diff-modal-form {
    /deep/ .h-form-item {
        margin-bottom: 16px;
    }
}
</style>
