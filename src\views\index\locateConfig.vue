<template>
  <div class="main">
    <header>
      <a-title title="Locate配置一致性校验">
        <slot>
          <h-select
            v-show="productList.length > 1"
            v-model="productInstNo"
            class="title-single-select"
            placeholder="请选择"
            :positionFixed="true"
            :clearable="false"
            @on-change="checkProduct"
          >
            <h-option
              v-for="item in productList"
              :key="item.id"
              :value="item.productInstNo"
              >{{ item.productName }}</h-option
            >
          </h-select>
        </slot>
      </a-title>
    </header>
    <a-loading v-if="loading"></a-loading>
    <h-tabs
      v-model="tabName"
      class="product-box"
      @on-click="handleTabChange(tabName)"
    >
      <h-tab-pane
        label="节点配置管理"
        name="locateConfigManage"
      >
        <locate-config-manage ref="locateConfigManage" :productId="productInstNo" @diff-view="handleDiffView" />
      </h-tab-pane>
      <h-tab-pane
        label="节点配置校验"
        name="locateConfigValid"
      >
        <diff-result ref="locateConfigValid" :productId="productInstNo" />
      </h-tab-pane>
    </h-tabs>
  </div>
</template>
<script>
import _ from 'lodash';
import { mapState, mapActions } from 'vuex';
import aLoading from '@/components/common/loading/aLoading';
import aTitle from '@/components/common/title/aTitle';
import diffResult from '@/components/locateConfig/diffResult';
import locateConfigManage from '@/components/locateConfig/locateConfigManage';
export default {
    components: {
        aLoading,
        aTitle,
        diffResult,
        locateConfigManage
    },
    data() {
        return {
            productInstNo: '',
            loading: false,
            tabName: 'locateConfigManage'
        };
    },
    mounted() {
        this.init();
    },
    computed: {
        ...mapState({
            productList: (state) => {
                return state.product.productListLight || [];
            }
        })
    },
    methods: {
    // 初始化数据
        async init() {
            try {
                this.loading = true;
                await this.getProductList({ filter: 'excludeLdpApm' });
                const productInstNo = localStorage.getItem('productInstNo');
                this.productInstNo =
          _.find(this.productList, ['productInstNo', productInstNo])
              ?.productInstNo || this.productList?.[0]?.productInstNo;
            } catch (e) {
                console.error(e);
            } finally {
                this.loading = false;
            }
        },
        ...mapActions({ getProductList: 'product/getProductListLight' }),
        // 切换产品
        checkProduct(item) {
            this.loading = true;
            localStorage.setItem('productInstNo', item);
            setTimeout(() => {
                this.loading = false;
                this.handleTabChange(this.tabName);
            }, 500);
        },
        // 切换标签页
        handleTabChange(tabName, type, params) {
            this.$nextTick(() => {
        this.$refs?.[tabName] && this.$refs[tabName].initData(type, params);
            });
        },
        // 跳转查看对比结果
        handleDiffView(params){
            this.tabName = 'locateConfigValid';
            this.handleTabChange(this.tabName, 'init', params);
        }
    }
};
</script>
<style lang="less" scoped>
@import url("@/assets/css/input.less");
@import url("@/assets/css/tab.less");

.title-single-select {
    width: 200px;
}

.product-box {
    /deep/ .h-tabs-bar {
        margin-bottom: 0;
    }

    /deep/ .h-tabs-nav-wrap {
        float: none !important;
    }

    /deep/ .h-tabs-nav-right {
        position: absolute;
        right: 0;
        top: 5px;
    }

    /deep/ .h-tabs-content-wrap {
        height: calc(100% - 48px);
    }
}
</style>
