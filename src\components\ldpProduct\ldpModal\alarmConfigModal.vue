<!--
 * @Description: 告警规则配置
 * @Author: <PERSON><PERSON>
 * @Date: 2022-03-08 10:28:21
 * @LastEditTime: 2023-05-08 19:23:28
 * @LastEditors: <PERSON><PERSON>
-->
<template>
    <div>
        <!-- 下发配置列表 -->
        <h-msg-box-safe v-model="modalData.status" :mask-closable="false" title="告警规则配置" width="70" maxHeight="300"
            @on-open="getCollections">
            <div class="alarm-title">
                <span class="group-title">委托全链路时延监控</span>
                <div v-if="alertRuleGroups.length">
                    启用规则
                    <h-switch v-model="monitorEnable" size="small" @on-change="monitorConfigAllSwitchChange"></h-switch>
                </div>
            </div>
            <div v-for="ele in alertRuleGroups" :key="ele.groupId" class="group-box">
                <ul class="tab-box" style="padding-top: 14px; overflow: hidden;">
                    <li v-for="(item, idx) in ele.metrics" :key="idx" style="cursor: pointer;">
                        <div class="inline-html" v-html="item.vHtml"></div>
                        <h-switch v-model="item.enable" size="small"
                            @on-change="(val) => { monitorConfigSingleSwitchChange(val, item, ele.groupId) }"></h-switch>
                    </li>
                </ul>
            </div>
            <template v-slot:footer>
                <a-button @click="modalData.status = false">取消</a-button>
            </template>
        </h-msg-box-safe>
    </div>
</template>

<script>
import _ from 'lodash';
import aButton from '@/components/common/button/aButton';
import { getProductMonitorConfig, getMonitorDictQuery, monitorConfigSwitchChange, updateMonitorConfig } from '@/api/httpApi';
export default {
    props: {
        modalInfo: {
            type: Object,
            default: null
        }
    },
    data() {
        return {
            monitorEnable: false,
            modalData: this.modalInfo,
            monitorDict: [],
            alertRuleGroups: []
        };
    },
    computed: {

    },
    methods: {
        async getCollections() {
            await this.getMonitorDictQuery();
            this.getProductMonitorConfig();
        },
        // 获取监控字典表
        async getMonitorDictQuery() {
            return new Promise((resovle, reject) => {
                getMonitorDictQuery().then(res => {
                    if (res.success) {
                        this.monitorDict = res.data;
                    }
                    resovle(true);
                }).catch(err => {
                    reject(err);
                });
            });
        },
        // 获取告警监控列表
        async getProductMonitorConfig() {
            const types = ['applicationLatency'];
            this.modalData.isAllLink && types.push('penetrationLatency');
            const param = {
                productId: this.modalData.productId,
                monitorType: types
            };
            const { data } = await getProductMonitorConfig(param);
            // 处理告警规则列表
            this.alertRuleGroups = data?.alertRuleExecute?.monitorConfig || [];
            this.monitorEnable = this.alertRuleGroups?.[0]?.enable;
            this.alertRuleGroups.forEach(item => {
                Array.isArray(item.metrics) && item.metrics.forEach(ele => {
                    const types = _.find(this.monitorDict, ['categoryCode', 'monitorType'])?.dicts || [];
                    const typeName = _.find(types, ['code', ele.monitorType])?.desc || '';
                    const vHtml = `<span style="font-weight: bold; padding-right: 20px;">${typeName}</span>${ele.metricsDesc.replace(/\${.*?\}/g, (t, i) => {
                        const type = t.slice(2, -1);
                        let name = ele.placeholder[type];
                        if (type === 'metricsName') {
                            const list = _.find(this.monitorDict, ['categoryCode', 'metricsName'])?.dicts || [];
                            name = _.find(list, ['code', ele.placeholder[type]])?.desc || '';
                        }
                        return `<span style="
                                    padding: 2px 4px;
                                    margin: 0 2px;
                                    font-style: normal;
                                    color: #fff;
                                    background: #358df9;
                                    border-radius: 2px;">${name}</span>`;
                    })}`;
                    ele.vHtml = vHtml;
                });
            });
        },
        async monitorConfigSingleSwitchChange(val, data, monitorGroupId) {
            data.enable = val;
            const param = {
                ...data,
                productId: this.modalData.productId,
                desc: data.metricsDesc,
                monitorGroupId
            };
            const res = await updateMonitorConfig(param);
            res.success && this.getProductMonitorConfig();
        },
        async monitorConfigAllSwitchChange() {
            const param = {
                productId: this.modalData.productId,
                groupId: 1,
                enable: this.monitorEnable
            };
            const res = await monitorConfigSwitchChange(param);
            if (res.success) {
                this.getProductMonitorConfig();
            }
        }
    },
    components: {  aButton }
};
</script>

<style lang="less" scoped>
/deep/ .h-modal-body {
    padding: 16px;
}

.alarm-title {
    display: flex;
    justify-content: space-between;
    border-bottom: 1px solid #ccc;
    padding-bottom: 10px;
}

.group-title {
    font-weight: bold;
    font-size: 14px;
}

.inline-html {
    display: inline-block;
    padding-bottom: 10px;
}
</style>
