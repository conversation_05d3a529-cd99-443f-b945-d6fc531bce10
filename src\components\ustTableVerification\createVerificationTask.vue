<template>
    <div class="create-step">
        <h-steps
            :current="currentStep"
            class="steps-box">
            <h-step
                v-for="item in stepList"
                :key="item"
                :title="item">
            </h-step>
        </h-steps>

        <div class="content-box">
            <a-loading v-if="loading"></a-loading>
            <!-- 提示信息 -->
            <div class="tips-box">
                <!-- <div class="tip-title">
                    {{ createTips[currentStep].title }}
                </div> -->
                <div class="tip-content">
                    {{ createTips[currentStep].content }}
                </div>
            </div>

            <!-- 确定校验对象 -->
            <div v-show="currentStep === 0" class="form-box">
                <h-form ref="formStep0" :model="formItems" :label-width="90" :rules="ruleValidate">
                    <h-form-item label="校验范围" prop="range" required>
                        <h-radio-group
                            v-model="formItems.range"
                            type="button">
                            <h-radio label="inCluster" text="集群内校验"></h-radio>
                        </h-radio-group>
                    </h-form-item>
                    <div v-if="formItems.range === 'inCluster'">
                        <h-form-item label="集群" prop="clusterId" required>
                            <h-select
                                v-model="formItems.clusterId"
                                positionFixed
                                :clearable="false"
                                @on-change="handleClusterChange">
                                <h-option
                                    v-for="item in clusterList" :key="item.clusterId"
                                    :value="item.clusterId">{{ item.clusterName }}
                                </h-option>
                            </h-select>
                        </h-form-item>
                        <h-form-item label="源数据库" prop="sourceInsId" required>
                            <h-select
                                v-model="formItems.sourceInsId"
                                :clearable="false"
                                positionFixed
                                @on-change="handleDatabaseChange">
                                <h-option
                                    v-for="item in databaseList" :key="item.appInstanceId"
                                    :value="item.appInstanceId">{{ item.instanceNo }}
                                </h-option>
                            </h-select>
                        </h-form-item>
                        <h-form-item label="目标数据库" prop="targetInsIds" required>
                            <h-select
                                v-model="formItems.targetInsIds"
                                positionFixed
                                multiple
                                :clearable="false"
                                @on-change="handleDatabaseChange">
                                <h-option
                                    v-for="item in databaseRemainList" :key="item.appInstanceId"
                                    :value="item.appInstanceId">{{ item.instanceNo }}
                                </h-option>
                            </h-select>
                        </h-form-item>
                    </div>
                </h-form>
            </div>

            <!-- 选择校验数据 -->
            <div v-show="currentStep === 1" class="form-box-2">
                <h-form ref="formStep1" :model="formItems" :label-width="100" :rules="ruleValidate">
                    <h-form-item label="校验类型" prop="veriftype" required>
                        <h-radio-group
                            v-model="formItems.veriftype"
                            type="button"
                            @on-change="changeVerifyType">
                            <h-radio label="tableRecordCount" text="按表总量"></h-radio>
                            <h-radio label="tableField" text="按表字段"></h-radio>
                        </h-radio-group>
                    </h-form-item>
                    <!-- 表总量 -->
                    <div v-if="formItems.veriftype === 'tableRecordCount'">
                        <h-form-item label="校验内容" prop="tableNames">
                            <record-verify-content
                                ref="record-verify"
                                :allTableList="allTableList"
                                :selectedTables="formItems.tableNames"
                                @choose-tables="handleChooseTable">
                            </record-verify-content>
                        </h-form-item>
                        <h-form-item label="错误阈值">
                            <h-switch v-model="formItems.errorRadio" disabled>
                                <div slot="open">开</div>
                                <div slot="close">关</div>
                            </h-switch>
                            <p style="color: #9296a1;">
                                “按表总量校验”不设置错误阈值。即不论校验过程是否出错，均完成全量数据校验
                            </p>
                        </h-form-item>
                    </div>
                    <!-- 表字段 -->
                    <div v-if="formItems.veriftype === 'tableField'">
                        <h-form-item
                            label="校验字段" prop="compareSql" required>
                            <h-input
                                v-model.trim="formItems.compareSql"
                                type="textarea"
                                :rows="8"
                                :canResize="false"
                                :placeholder="sqlTip"
                                :maxlength="5000">
                            </h-input>
                            <a :href="fileUrl" download="SQL语法说明.md">
                                <h-icon name="t-b-download"></h-icon>
                                下载SQL语法指南
                            </a>
                        </h-form-item>
                        <h-form-item label="错误阈值" prop="thresholdVal" :validRules="thresholdValRule" required>
                            <h-switch v-model="formItems.errorRadio" disabled>
                                <div slot="open">开</div>
                                <div slot="close">关</div>
                            </h-switch>
                            <div class="error-threshold-box">
                                <h-radio :value="formItems.thresholdUnit === '个'">按错误个数</h-radio>
                                <div class="divider"></div>
                                <h-input v-model="formItems.thresholdVal" type="int" specialFilter :specialLength="10" style="width: 120px;" >
                                    <span slot="append">个</span>
                                </h-input> &nbsp;&nbsp;
                                <span class="tip-text">
                                    当表字段不一致个数达到阈值时，校验任务自动停止，校验不通过
                                </span>
                            </div>
                        </h-form-item>
                    </div>
                </h-form>
            </div>

            <!-- 信息核对 -->
            <div v-show="currentStep === 2" class="confirm-box">
                <a-tips
                    theme="dark"
                    :tipText="`根据所选校验数据，将会新增${ tableData.length }条校验任务，请核对`">
                </a-tips>
                <a-table
                    showTitle
                    :tableData='tableData'
                    :columns="columns"
                    :hasPage="false">
                </a-table>
            </div>
        </div>

        <div class="buttom-box">
            <a-button v-show="currentStep" type="dark" @click="handleStepChange('-')">
                上一步
            </a-button>
            <a-button
                v-show="currentStep !== 2"
                type="primary"
                :loading="nextStepLoading"
                @click="handleStepChange('+')">
                下一步：{{ stepList[currentStep + 1] }}
            </a-button>
            <a-button
                v-show="currentStep === 2"
                type="primary"
                :loading="confirmLoading"
                @click="handleCreateTask">
                创建数据校验任务
            </a-button>
            <a-button type="dark" @click="handleCancel">取消</a-button>
        </div>

        <!-- 修改规则名称 -->
        <update-rule-name
            v-if="ruleNameInfo.status" :modalInfo="ruleNameInfo"
            @update-rule-name="updateRuleName">
        </update-rule-name>
    </div>
</template>

<script>
import aButton from '@/components/common/button/aButton';
import aTable from '@/components/common/table/aTable';
import aTips from '@/components/common/apmTips/aTips';
import aLoading from '@/components/common/loading/aLoading';
import recordVerifyContent from './modal/recordVerifyContent.vue';
import { CREATE_TASK_TIPS, CONFIRM_TABLE_COLUMNS, SQL_TIP } from '@/components/ustTableVerification/constant';
import { getUstTableEndpointConfigs } from '@/api/memoryApi';
import { createVerificationTask } from '@/api/dataVerification';
import { getDatabases, getTables } from '@/api/httpApi';
import { v4 as uuidv4 } from 'uuid';
import { getSqlListFromSqlStr } from '@/utils/utils';
import updateRuleName from './modal/updateRuleName.vue';
function isPositiveInteger(value) {
    /**
     * 判断一个字符串值是否为正整数.
     *
     * @param {string} value - 需要判断的字符串值.
     * @return {boolean} - 如果值是正整数返回 true，否则返回 false.
     */
    if (typeof value !== 'string' || value.trim() === '') return false;

    // 将字符串转为整数
    const num = Number(value);

    // 检查字符串是否为正整数
    return Number.isInteger(num) && num > 0 && num <= 1000;
}
/**
 * sql校验
 */
function sqlValidate(rule, val, callback) {
    const prefix = val.replace(/\n|\t| /g, '')?.slice(0, 6)?.toLowerCase?.();
    const sqlList = getSqlListFromSqlStr(val);
    if (val.length > 500) {
        return callback(new Error('最大输入长度500个字符'));
    }
    if (prefix !== 'select' || sqlList.length > 1) {
        return callback(new Error('只允许写一条select语句'));
    }
    return callback();
}
function intValidate(rule, val, callback) {
    if (!isPositiveInteger(val)) {
        return callback(new Error('输入框输入范围为1~1000的正整数'));
    }
    return callback();
}
export default {
    name: 'CreateVerificationTask',
    components: { aButton, aTable, aTips, aLoading, recordVerifyContent, updateRuleName  },
    props: {
        productId: {
            type: String,
            default: ''
        }
    },
    data() {
        // 重复项检测
        const repeatValidate = (rule, val, callback) => {
            if (this.formItems.targetInsIds.includes(this.formItems.sourceInsId)) {
                const label = this.databaseList.find(o => o.appInstanceId === this.formItems.sourceInsId)?.instanceNo;
                return callback(new Error(`源数据库与目标数据库选择"${label}"节点重复，请修改`));
            }
            return callback();
        };

        return {
            fileUrl: `${this.IMG_HOME}static/usttable-sql-guide.md`,
            loading: false,
            currentStep: 0,
            confirmLoading: false,
            nextStepLoading: false,
            stepList: ['确定校验对象', '选择校验数据', '信息核对'],
            createTips: CREATE_TASK_TIPS,
            sqlTip: SQL_TIP,

            ruleValidate: {
                targetInsIds: [{ validator: repeatValidate, trigger: 'change, blur' }],
                sourceInsId: [{ validator: repeatValidate, trigger: 'change, blur' }],
                compareSql: [{ validator: sqlValidate, trigger: 'change, blur' }],
                tableNames: [{
                    required: true,
                    type: 'array',
                    min: 1,
                    message: '至少选择一张表',
                    trigger: 'change'
                }]
            },
            thresholdValRule: [{ test: intValidate, trigger: 'change, blur' }],
            formItems: {
                range: 'inCluster',
                clusterId: '',
                sourceInsId: '',
                targetInsIds: [],
                veriftype: 'tableRecordCount',
                tableNames: [],
                errorRadio: false,
                thresholdVal: '1000',
                thresholdUnit: '个',
                compareSql: ''
            },
            nodeList: [], // 全部节点列表
            clusterList: [],
            databaseList: [],
            databaseRemainList: [],

            columns: CONFIRM_TABLE_COLUMNS.concat([{
                title: '任务名称',
                key: 'ruleName',
                ellipsis: true,
                width: 150,
                render: (h, params) => {
                    return h('div', {
                        style: {
                            display: 'flex'
                        }
                    }, [
                        h('div', {
                            style: {
                                display: 'inline-block',
                                'max-width': '100px',
                                margin: '4px 3px 0 0',
                                overflow: 'hidden', // 超出的文本隐藏
                                'text-overflow': 'ellipsis', // 溢出用省略号显示
                                'white-space': 'nowrap' // 溢出不换行
                            }
                        }, params.row.ruleName),
                        h('icon', {
                            props: {
                                name: 't-b-modify',
                                color: '#2D8DE5'
                            },
                            on: {
                                'on-click': () => {
                                    // 修改任务名称
                                    this.ruleNameInfo = {
                                        status: true,
                                        ...params.row
                                    };
                                }
                            }
                        })
                    ]);
                }
            }]),
            tableData: [],
            allTableList: [],

            ruleNameInfo: {
                status: false
            }
        };
    },
    methods: {
        // 重置数据
        async initData() {
            this.currentStep = 0;
            // 表单数据重置
            this.$refs[`formStep0`].resetFields();
            this.$refs[`formStep1`].resetFields();

            this.loading = true;
            // 获取集群、应用列表
            await this.getClusterAndIns();
            this.loading = false;
        },
        // 信息核对数据
        handleCheckData() {
            const compareList = [];
            // 解构form参数
            const { clusterId, sourceInsId, targetInsIds, veriftype, tableNames, compareSql  } = this.formItems;
            // 遍历目标节点-一个源、目标节点生成一个compare
            targetInsIds.forEach(item => {
                const compare = {
                    clusterId: clusterId,
                    instanceId: sourceInsId,
                    compareClusterId: clusterId,
                    compareInstanceId: item,
                    sourceDatabaseName: this.nodeList.find(o => o.appInstanceId === sourceInsId)?.instanceNo,
                    targetDatabasesName: this.nodeList.find(o => o.appInstanceId === item)?.instanceNo,
                    compareType: veriftype === 'tableRecordCount' ? '表总量' : '表字段',
                    tables: tableNames,
                    compareSql: compareSql,
                    compareContent: veriftype === 'tableRecordCount' ? tableNames?.join(',') : compareSql,
                    ruleName: '任务' + uuidv4().substring(0, 8)
                };
                compareList.push(compare);
            });
            this.tableData = compareList;
        },
        // 构建创建参数
        generateParams() {
            const { errorRadio, thresholdVal } = this.formItems;
            const errorThreshold = (errorRadio && thresholdVal) ? thresholdVal : '';
            return {
                productId: this.productId,
                ruleType: this.formItems.veriftype,
                compare: this.tableData,
                errorThreshold: errorThreshold
            };
        },
        // 创建数据校验任务
        async handleCreateTask() {
            this.confirmLoading = true;
            try {
                const param = this.generateParams();
                const res = await createVerificationTask(param);
                if (res.code === '200') {
                    this.$hMessage.success('创建成功');
                    // 跳转至列表页面
                    this.$emit('to-task-list', 'table');
                }
            } catch (err) {
                console.error(err);
            } finally {
                this.confirmLoading = false;
            }
        },
        // 取消-返回表格页
        handleCancel() {
            const needBack = this.currentStep > 0 ? true : false;
            this.goBack(needBack);
        },
        // 取消创建 二次校验
        goBack(needVerify) {
            if (needVerify === true) {
                this.$hMsgBoxSafe.confirm({
                    title: `确认离开页面？`,
                    content: `离开后当前操作将不会保存，数据会丢失，请谨慎操作！`,
                    onOk: () => {
                        this.$emit('to-task-list', 'table');
                    },
                    cancelText: '留下'
                });
                return;
            }
            this.$emit('to-task-list', 'table');
        },
        // 切换步骤
        handleStepChange(ope) {
            if (ope === '+') {
                // 校验内容
                this.$refs[`formStep${this.currentStep}`].validate(async (valid) => {
                    if (valid) {
                        try {
                            this.nextStepLoading = true;
                            this.currentStep === 0 && await this.handleVerifyContent();
                            // this.currentStep === 1 && this.handleCheckData();
                            if (this.currentStep === 1) {
                                // 校验错误阈值
                                if (this.formItems.veriftype === 'tableField' && !isPositiveInteger(this.formItems.thresholdVal)) return;
                                this.handleCheckData();
                            }
                            this.currentStep += 1;
                        } finally {
                            this.nextStepLoading = false;
                        }
                    }
                });
            } else {
                this.currentStep -= 1;
            }
        },
        // 选择校验数据
        async handleVerifyContent() {
            // 获取源节点信息
            const sourceInfo = this.nodeList.find(o => o.appInstanceId === this.formItems.sourceInsId);
            // 按表总量-获取全部表名
            this.formItems.veriftype === 'tableRecordCount' && await this.getTableLists(sourceInfo);

            // 表名checkboxGroup组件
            this.$refs['record-verify'] && this.$refs['record-verify'].initData();
        },

        // 切换校验类型 - 表记录总数、表字段校验
        changeVerifyType(val) {
            // 清除校验错误信息
            this.$refs[`formStep1`].resetValidate();

            // 表总量-错误阈值关闭
            this.formItems.errorRadio = val === 'tableField' ? true : false;
        },
        // 整理选中表数据
        handleChooseTable(tables) {
            // 更新选中表
            this.formItems.tableNames = [...tables];
        },
        // 获取集群及应用列表
        async getClusterAndIns() {
            let list = [];
            const res = await getUstTableEndpointConfigs({
                productId: this.productId
            });
            if (res.code === '200') {
                list = res.data?.configs || [];
            }
            this.nodeList = list;
            this.clusterList = [];
            const clusters = [];
            list.forEach(item => {
                if (!clusters.includes(item.clusterId)) {
                    clusters.push(item.clusterId);
                    this.clusterList.push(item);
                }
            });
        },
        // 切换集群
        handleClusterChange(val) {
            // 过滤出对应集群的相关节点列表
            const clusterInsList = this.nodeList.filter(item => item.clusterId === val);
            // 源节点列表
            this.databaseList = clusterInsList;
            this.databaseRemainList = clusterInsList;

            // 源 - 默认带入主节点
            this.formItems.sourceInsId = clusterInsList.find(o => o.clusterRole === 'ARB_ACTIVE')?.appInstanceId;
            // 目标 - 默认带入备节点
            this.formItems.targetInsIds = clusterInsList.filter(o => o.clusterRole === 'ARB_INACTIVE').map(item => {
                return item.appInstanceId;
            });
        },
        // 切换源/目标节点
        handleDatabaseChange() {
            // 触发重新校验
            this.$nextTick(() => {
                this.$refs[`formStep0`].validate();
            });
        },
        // 获取内存表列表
        async getTableLists(source) {
            const param1 = {
                endpoint: `${source.endpointIp}:${source.endpointPort}`,
                instanceId: source.id,
                tableType: 'ustTable'
            };
            let tableList = [];
            try {
                const res1 = await getDatabases(param1);
                if (!res1.errcode) {
                    this.databaseName = res1.databaseNames[0];
                    const param2 = {
                        databaseName: this.databaseName,
                        endpoint: `${source.endpointIp}:${source.endpointPort}`,
                        instanceId: source.id,
                        tableType: 'ustTable'
                    };
                    const res2 = await getTables(param2);
                    if (!res2.errcode) {
                        tableList = res2?.tableInfos || [];
                    }
                }
            } catch (err) {
                console.error(err);
            }
            this.allTableList = tableList.map(item => {
                return item.tableName + 'View';
            });
        },
        // 修改任务名
        updateRuleName(val, _index) {
            this.$set(this.tableData[_index], 'ruleName', val);
        }
    }
};
</script>
<style lang="less" scoped>
.h-icon {
    &:hover {
        cursor: pointer;
    }
}
</style>

<style lang="less" scoped>
@import url("./createTask.less");
</style>
