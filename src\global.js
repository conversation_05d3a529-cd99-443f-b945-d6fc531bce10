// src/global.js

/**
 * 获取当前本地时间（格式：HH:mm:ss）
 * @returns {string} 当前本地时间字符串，格式为HH:mm:ss
 */
function getCurrentLocalTime() {
    const date = new Date();
    const pad = n => n.toString().padStart(2, '0');
    const hours = pad(date.getHours());
    const minutes = pad(date.getMinutes());
    const seconds = pad(date.getSeconds());
    return `${hours}:${minutes}:${seconds}`;
}

/**
 * 获取产品类型描述
 * @param {string} proType - 产品类型编码
 * @param {object} store - Vuex存储对象
 * @returns {string} 产品类型描述或原始产品类型编码
 */
function getProductType(proType, store) {
    const apmDirDesc = store?.state?.apmDirDesc?.['productTypeDict'];
    return apmDirDesc?.[proType] || proType;
}

export default {
    install(Vue) {
        // 挂载全局变量
        Vue.prototype.IMG_HOME = window.LOCAL_CONFIG.IMG_HOME;
        Vue.prototype.APM_LICENSE = window.LOCAL_CONFIG.APM_LICENSE;

        // 挂载全局方法
        Vue.prototype.$getProductType = function(proType) {
            return getProductType(proType, this.$store);
        };
        Vue.prototype.$getCurrentLocalTime = getCurrentLocalTime;
    }
};
