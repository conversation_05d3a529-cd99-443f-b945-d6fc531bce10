<!--
 * @Description: 行情吗产品节点穿透时延分析
 * @Author: <PERSON><PERSON>
 * @Date: 2023-01-04 13:30:53
 * @LastEditTime: 2023-03-20 19:08:15
 * @LastEditors: <PERSON><PERSON>
-->
<template>
    <div class="main">
        <a-title title="行情产品节点穿透时延分析">
            <slot>
                <h-select
                    v-show="productList.length > 1"
                    v-model="productInstNo"
                    class="title-single-select"
                    placeholder="请选择"
                    :positionFixed="true"
                    :setDefSelect="true"
                    :clearable="false"
                    @on-change="handleChangeProduct">
                    <h-option v-for="item in productList" :key="item.id" :value="item.productInstNo">{{ item.productName
                        }}</h-option>
                </h-select>
            </slot>
        </a-title>
        <tab-title-table :tabTableData="tabTableData" style="margin-top: 10px;" />
    </div>
</template>

<script>
import { mapState, mapActions } from 'vuex';
import tabTitleTable from '@/components/common/bestTable/tabTitleTable';
import aTitle from '@/components/common/title/aTitle';
import _ from 'lodash';
export default {
    name: 'MarketPenetrateList',
    data() {
        return {
            productInstNo: '',
            tabTableData: [{
                label: '汇总查询',
                name: 'summary',
                tableData: [],
                columns: [
                    {
                        title: '交易日',
                        key: 'tradeDate',
                        minWidth: 100
                    },
                    {
                        title: '下单数',
                        key: 'totalNum',
                        minWidth: 100
                    },
                    {
                        title: '全链路时延',
                        key: 'fullLinkLatency',
                        minWidth: 100
                    },
                    {
                        title: '柜台上行时延',
                        key: 'counterUpLinkLatency',
                        minWidth: 100
                    },
                    {
                        title: '柜台下行时延',
                        key: 'counterDownLinkLatency',
                        minWidth: 100
                    },
                    {
                        title: 'TGW上行时延',
                        key: 'tgwUpLinkLatency',
                        minWidth: 110
                    },
                    {
                        title: 'TGW下行时延',
                        key: 'tgwDownLinkLatency',
                        minWidth: 110
                    },
                    {
                        title: 'TGW时延',
                        key: 'tgwLatency',
                        minWidth: 80
                    },
                    {
                        title: '交易所时延',
                        key: 'exchangeLatency',
                        minWidth: 90
                    },
                    {
                        title: '行情-交易时延',
                        key: 'mdTradeLatency',
                        minWidth: 105
                    },
                    {
                        title: '交易-收到行情时延',
                        key: 'tradeMdLatency',
                        minWidth: 132
                    },
                    {
                        title: '防火墙上行时延',
                        key: 'firewallUpLinkLatency',
                        minWidth: 115
                    },
                    {
                        title: '防火墙下行时延',
                        key: 'firewallDownLinkLatency',
                        minWidth: 120
                    },
                    {
                        title: '拓扑结构',
                        key: 'topo',
                        fixed: 'right',
                        minWidth: 80,
                        render: (h, params) => {
                            const that = this;
                            return h('Button',
                                {
                                    props: {
                                        size: 'small',
                                        type: 'text'
                                    },
                                    on: {
                                        click: () => {
                                            that.$emit('handleDrawer');
                                        }
                                    }
                                },
                                '查看'
                            );
                        }
                    }
                ],
                formItems: [{
                    type: 'select',
                    label: '交易市场',
                    key: 'exchangeId',
                    options: [
                        {
                            label: '深圳交易所',
                            value: 'SZSE'
                        },
                        {
                            label: '上海交易所',
                            value: 'SSE'
                        }
                    ],
                    value: 'SZSE'
                }, {
                    type: 'daterange',
                    key: 'dateRange',
                    label: '交易日期',
                    value: [new Date(), new Date()],
                    required: true
                }, {
                    type: 'timerange',
                    key: 'timeRange',
                    label: '交易时间',
                    value: '09:15:00 - 15:00:00',
                    required: true
                }, {
                    type: 'select',
                    label: '交易核心',
                    key: 'exchangeId',
                    options: [],
                    value: ''
                }, {
                    type: 'select',
                    label: '报盘网关',
                    key: 'exchangeId',
                    options: [],
                    value: ''
                }, {
                    type: 'select',
                    label: '行情网关',
                    key: 'exchangeId',
                    options: [],
                    value: ''
                }, {
                    type: 'input',
                    label: '资金账户',
                    key: '',
                    value: ''
                }, {
                    type: 'input',
                    label: '证券代码',
                    key: '',
                    value: ''
                }, {
                    type: 'input',
                    label: '席位号',
                    key: '',
                    value: ''
                }, {
                    type: 'select',
                    label: '汇总间隔',
                    key: 'select',
                    options: [
                        {
                            label: '天',
                            value: 'day'
                        }
                    ],
                    value: 'day'
                }, {
                    type: 'select',
                    label: '汇总方式',
                    key: 'select',
                    options: [
                        {
                            label: '天',
                            value: 'day'
                        }
                    ],
                    value: 'day'
                }],
                hasPage: true,
                total: 0
            }, {
                label: '详情查询',
                name: 'detail',
                tableData: [],
                columns: [
                    {
                        title: '日期',
                        key: 'clientReqTimestamp',
                        minWidth: 160
                    },
                    {
                        title: '全链路时延',
                        key: 'fullLinkLatency',
                        minWidth: 100
                    },
                    {
                        title: '柜台上行时延',
                        key: 'counterUpLinkLatency',
                        minWidth: 100
                    },
                    {
                        title: '柜台下行时延',
                        key: 'counterDownLinkLatency',
                        minWidth: 100
                    },
                    {
                        title: 'TGW上行时延',
                        key: 'tgwUpLinkLatency',
                        minWidth: 110
                    },
                    {
                        title: 'TGW下行时延',
                        key: 'tgwDownLinkLatency',
                        minWidth: 110
                    },
                    {
                        title: 'TGW时延',
                        key: 'tgwLatency',
                        minWidth: 80
                    },
                    {
                        title: '交易所时延',
                        key: 'exchangeLatency',
                        minWidth: 90
                    },
                    {
                        title: '行情-交易时延',
                        key: 'mdTradeLatency',
                        minWidth: 105
                    },
                    {
                        title: '交易-收到行情时延',
                        key: 'tradeMdLatency',
                        minWidth: 132
                    },
                    {
                        title: '防火墙上行时延',
                        key: 'firewallUpLinkLatency',
                        minWidth: 115
                    },
                    {
                        title: '防火墙下行时延',
                        key: 'firewallDownLinkLatency',
                        minWidth: 120
                    },
                    {
                        title: '业务字段',
                        key: 'action',
                        fixed: 'right',
                        width: 80,
                        render: (h, params) => {
                            const that = this;
                            return h('div', [
                                h('Poptip',
                                    {
                                        props: {
                                            title: '业务字段',
                                            placement: 'left',
                                            positionFixed: true
                                        }
                                    },
                                    [
                                        h('Button',
                                            {
                                                props: {
                                                    size: 'small',
                                                    type: 'text'
                                                }
                                            },
                                            '查看'
                                        ),
                                        h('div',
                                            {
                                                slot: 'content',
                                                class: ''
                                            },
                                            [
                                                ...that.businessData.map(item => {
                                                    return h('p', {
                                                        style: {
                                                            paddingBottom: '6px'
                                                        }
                                                    }, [
                                                        ...item.map((ele, idx) => {
                                                            return h('span',
                                                                {
                                                                    style: {
                                                                        display: 'inline-block',
                                                                        width: '200px'
                                                                    }
                                                                },
                                                                [
                                                                    h('span', {
                                                                        style: {
                                                                            paddingLeft: idx ? '20px' : 0,
                                                                            color: '#CACFD4'
                                                                        }
                                                                    }, `${ele.label}：`),
                                                                    h('em', {
                                                                        style: {
                                                                            fontStyle: 'normal'
                                                                        }
                                                                    }, `${params.row[ele.key]}`)
                                                                ]
                                                            );
                                                        })
                                                    ]);
                                                })
                                            ]
                                        )
                                    ]
                                )
                            ]);
                        }
                    }
                ],
                formItems: [{
                    type: 'select',
                    label: '交易市场',
                    key: 'exchangeId',
                    options: [
                        {
                            label: '深圳交易所',
                            value: 'SZSE'
                        },
                        {
                            label: '上海交易所',
                            value: 'SSE'
                        }
                    ],
                    value: 'SZSE'
                }],
                hasPage: true,
                total: 0
            }]
        };
    },
    mounted() {
        this.getProductList();
    },
    methods: {
        ...mapActions({ getProductList: 'product/getProductList' }),
        handleChangeProduct() {
            const productInfo = _.find(this.productList, ['productInstNo', this.productInstNo]);
            this.tabTableData[0].formItems[3].options = productInfo?.productInstNoMappingRulesVO?.transactionCoreIps || [];
        }
    },
    computed: {
        ...mapState({
            productList: state => {
                return state.product.productList;
                // return _.filter(state.product.productList, ['bizSysType', '6']) || []
            }
        })
    },
    components: { tabTitleTable, aTitle }
};
</script>
