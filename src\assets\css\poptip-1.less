.poptipPopper() {
    .h-poptip-popper[x-placement^="left"] {
        .h-poptip-arrow {
            border-left-color: var(--poptip-bg-color) !important;

            &::after {
                border-left-color: var(--poptip-bg-color) !important;
            }
        }
    }

    .h-poptip-popper[x-placement^="right"] {
        .h-poptip-arrow {
            border-right-color: var(--poptip-bg-color) !important;

            &::after {
                border-right-color: var(--poptip-bg-color) !important;
            }
        }
    }

    .h-poptip-popper[x-placement^="top"] {
        .h-poptip-arrow {
            border-top-color: var(--poptip-bg-color) !important;

            &::after {
                border-top-color: var(--poptip-bg-color) !important;
            }
        }
    }

    .h-poptip-popper[x-placement^="bottom"] {
        .h-poptip-arrow {
            border-bottom-color: var(--poptip-bg-color) !important;

            &::after {
                border-bottom-color: var(--poptip-bg-color) !important;
            }
        }
    }
}

.apm-poptip {

    // poptip
    .h-poptip-inner {
        background-color: var(--poptip-bg-color);
    }

    .h-poptip-title {
        padding: 8px;
        border-bottom: 1px solid var(--font-opacity-color);
    }

    .h-poptip-title::after {
        display: none;
    }

    .h-poptip-title-inner {
        color: var(--font-color);
    }

    .h-poptip-body {
        padding: 12px;
    }

    .poptipPopper();
}

.monitor-poptip {
    color: var(--font-color);

    .pop-content {
        max-height: 120px;
        overflow: auto;

        .h-row {
            margin: 5px 0;

            .h-col {
                overflow: hidden;
                white-space: nowrap;
                text-overflow: ellipsis;
            }
        }
    }
    .poptipPopper();
}

.apm-poptip.h-poptip-popper[x-placement^="left"],
.monitor-poptip.h-poptip-popper[x-placement^="left"] {
    .h-poptip-arrow {
        border-left-color: var(--poptip-bg-color) !important;

        &::after {
            border-left-color: var(--poptip-bg-color) !important;
        }
    }
}

.apm-poptip.h-poptip-popper[x-placement^="right"],
.monitor-poptip.h-poptip-popper[x-placement^="right"] {
    .h-poptip-arrow {
        border-right-color: var(--poptip-bg-color) !important;

        &::after {
            border-right-color: var(--poptip-bg-color) !important;
        }
    }
}

.apm-poptip.h-poptip-popper[x-placement^="top"],
.monitor-poptip.h-poptip-popper[x-placement^="top"] {
    .h-poptip-arrow {
        border-top-color: var(--poptip-bg-color) !important;

        &::after {
            border-top-color: var(--poptip-bg-color) !important;
        }
    }
}

.apm-poptip.h-poptip-popper[x-placement^="bottom"],
.monitor-poptip.h-poptip-popper[x-placement^="bottom"] {
    .h-poptip-arrow {
        border-bottom-color: var(--poptip-bg-color) !important;

        &::after {
            border-bottom-color: var(--poptip-bg-color) !important;
        }
    }
}

