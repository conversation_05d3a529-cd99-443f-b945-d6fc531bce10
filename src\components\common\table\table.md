
## Table表格
基于HUI Table封装适用于SEE项目的公共表格组件，分为：apm-Table，normalTitleTable和tarTitleTable。
[http://hui.hundsun.com/](http://hui.hundsun.com/)
### apm-Table
#### Props
| **属性** | **说明** | **类型** | **默认值** |
| --- | --- | --- | --- |
| hasDarkClass | 黑色主题 | Boolean | true |
| tableData | 表格数据 | Array(具体参数参考HUI) | [] |
| columns | 表格列的配置描述 | Array(具体参数参考HUI) | [] |
| hasPage | 是否存在分页组件 | Boolean | true |
| total | 数据总数 | Number | 0 |
| highlightRow | 是否支持高亮选中的行，即单选 | Boolean | false |
| disabledHover | 禁用鼠标悬停时的高亮 | Boolean | false |
| showElevator | 显示电梯，可以快速切换到某一页 | Boolean | true |
| showSizer | 显示分页，用来改变page-size | Boolean | true |
| maxHeight | 设置表格的最大高度 | Number &#124; String &#124; undefined | '-' |
| immediateRowClick | 开启后单击将立即触发单击事件，同时双击事件也将触发一次单击事件，可改善单击略微延迟体验（v1.9.0） | Boolean | false |
| @rowClick | 单击某一行时触发（type=radio开启highlightRow时点击单选按钮或type=selection开启rowSelect时点击多选框也会触发） | Function,row：当前行的数据;_index：当前行在表格初始化时的角标;_isHighlight：当前行是否高亮（v1.45.0） | / |
| @onCurrentChange | 开启 highlightRow 后生效，当前行选中的时候会触发 | Function, currentRow：当前高亮行的数据; oldCurrentRow：上一次高亮的数据; _index：当前高亮行在表格初始化时的角标 | /|
| @query | 查询数据 | Function | /|

#### Methods
| **方法名** | **说明** | **用法/参数** |
| --- | --- | --- |
| getPageData | 返回当前页码和数据条数 | this.$refs.xxx.getPageData(); |
| resetPage | 重置页码 | this.$refs.xxx.resetPage(); |
| resetPageSize | 重置数据条数 | this.$refs.xxx.resetPageSize(); |
| resetSortData | 重置排序信息 | this.$refs.xxx.resetSortData(); |
| sortChange | 排序 | this.$refs.xxx.sortChange(); |
| pageChange | 页码改变的回调 | page：页码,会触发外部查询方法 |
| pageSizeChange | 切换每页条数的回调 | pageSize：每页条数,会触发外部查询方法 |
| rowClick | 单击某一行时触发（type=radio开启highlightRow时点击单选按钮或type=selection开启rowSelect时点击多选框也会触发） | / |
| onCurrentChange | 开启 highlightRow 后生效，当前行选中的时候会触发 | / |

#### 示例代码

**a-table**

```vue
<template>
  <div class="main">
    <!-- 无分页 -->
	<a-table ref="form" :columns="columns" :tableData="tableData" :hasPage="false"></a-table>
    <!-- 分页 -->
    <a-table ref="form" :columns="columns" :tableData="tableData" :total="100" highlightRow @rowClick="rowClick" @onCurrentChange="onCurrentChange"></a-table>
    <!-- 简单分页 -->
    <a-table ref="form" :columns="columns" :tableData="tableData" :showSizer="false" :showElevator="false" simple :total="100" @query="query"></a-table>
  </div>
</template>

<script>
import aTable from '@/components/common/table/aTable';
export default {
    data() {
        return {
            columns: [
                {
                    title: '姓名',
                    key: 'name'
                },
                {
                    title: '年龄',
                    key: 'age'
                },
                {
                    title: '地址',
                    key: 'address'
                }
            ],
            tableData: [
                {
                    name: '王小明',
                    age: 18,
                    address: '北京市朝阳区芍药居'
                },
                {
                    name: '张小刚',
                    age: 25,
                    address: '北京市海淀区西二旗'
                },
                {
                    name: '李小红',
                    age: 30,
                    address: '上海市浦东新区世纪大道'
                },
                {
                    name: '周小伟',
                    age: 26,
                    address: '深圳市南山区深南大道'
                }
            ]
        };
    },
    methods: {
        /**
        行点击事件
            row：当前行的数据
            _index：当前行在表格初始化时的角标
            _isHighlight：当前行是否高亮
        */ 
        rowClick(val) {
            
        },
        // 行选中事件
        onCurrentChange(val) {

        },
        // 查询事件
        query() {

        }
    },
    components: { aTable }
};
</script>
```



### normalTitleTable

#### Props
| **属性** | **说明** | **类型** | **默认值** |
| --- | --- | --- | --- |
| title | 标题 | String | '' |
| loading | 表格为加载中状态  | Boolean | false |
| formItems | 表单内容,①type可选值:input,select,date,datetime,daterange,rimerange,timescreen等;② 新增字段：labelWidth:自定义label值宽度;editable:仅日期选择器文本框是否可以输入，只在没有使用 slot 时有效;placement:日期选择器出现的位置| Array(具体参数参考HUI) | [] |
| tableData | 表格数据 | Array(具体参数参考HUI) | [] |
| columns | 表格列的配置描述 | Array(具体参数参考HUI) | [] |
| hasPage | 是否存在分页组件 | Boolean | true |
| total | 数据总数 | Number | 0 |
| hasSetTableColumns | 是否需要配置表格button| Boolean | true |
| @query | 查询数据 | Function | 调用xxx(val){} |

#### Methods
| **方法名** | **说明** | **用法/参数** |
| --- | --- | --- |
| $_init | 初始化数据 | / |
| $_clearCurrentCheckedKeys | 清空选中的多选框值 | / |
| setTableColumns | 配置表格显示drawer | / |
| $_handleCheckbox | 更改多选框后回调 | / |
| $_handleQuery | page切换查询数据 | / |
| $_handleReset | 重置表单 | / |
| handleClickQuery | 点击查询按钮查询数据 | / |
| $_handleResetPageDataAndQuery | 产品列表切换查询数据 | / |

#### 示例代码

```vue
<template>
  <div class="main">
    <normal-title-table ref="table" title="汇总查询" :formItems="formItems" :columns="columns" :loading="loading"
        :tableData="tableData" :total="total" @query="handleQuery">
        <slot>
            <a-button type="dark">添加</a-button>
		</slot>
    </normal-title-table>
  </div>
</template>

<script>
import aButton from '@/components/common/button/aButton';
import normalTitleTable from '@/components/common/bestTable/normalTitleTable';
export default {
    data() {
        return {
            columns: [
                {
                    title: '姓名',
                    key: 'name'
                },
                {
                    title: '年龄',
                    key: 'age'
                },
                {
                    title: '地址',
                    key: 'address'
                }
            ],
            tableData: [
                {
                    name: '王小明',
                    age: 18,
                    address: '北京市朝阳区芍药居'
                },
                {
                    name: '张小刚',
                    age: 25,
                    address: '北京市海淀区西二旗'
                },
                {
                    name: '李小红',
                    age: 30,
                    address: '上海市浦东新区世纪大道'
                },
                {
                    name: '周小伟',
                    age: 26,
                    address: '深圳市南山区深南大道'
                }
            ],
            loading: false,
            formItems: [
                {
                    key: 'abc', // 对应表单域model里的字段，可用于校验
                    value: 'haha', // 可选，表单项初始值
                    type: 'input', // 表单元素类型
                    label: 'input', // 标签文本
                    labelWidth: 100, // 自定义label宽度
                    required: true // 可选，默认false，表单项是否必填
                },
                {
                    key: 'bcd',
                    type: 'select',
                    label: 'select',
                    labelWidth: 100,
                    required: true,
                    options: [
                        { value: 'item1', label: '选项1' },
                        { value: 'item2', label: '选项2' },
                        { value: 'item3', label: '选项3' }
                    ]
                }
            ],
            total: 100
        };
    },
    methods: {
        // 查询事件 - 包含表单及分页参数
        handleQuery(val) {
            console.log(val);
        }
    },
    components: { normalTitleTable, aButton }
};
</script>

```





### tarTitleTable

#### Props
| **属性** | **说明** | **类型** | **默认值** |
| --- | --- | --- | --- |
| tabTableData | 数据 | Array | [] |
| tableLoading  | 表格为加载中状态| Boolean | false |
| hasSetTableColumns | 是否需要配置表格button| Boolean | true |

| **属性** | **说明** | **类型** | **默认值** |
| --- | --- | --- | --- |
| label | 选项卡头显示文字 | String &#124; Function | 空 |
| name | 用于标识当前面板，对应 value | String &#124; Number | - |
| formItems | 表单内容,①type可选值:input,select,date,datetime,daterange,rimerange,timescreen等;② 新增字段：labelWidth:自定义label值宽度;editable:仅日期选择器文本框是否可以输入，只在没有使用 slot 时有效;placement:日期选择器出现的位置| Array(具体参数参考HUI) | [] |
| tableData | 表格数据 | Array(具体参数参考HUI) | [] |
| columns | 表格列的配置描述 | Array(具体参数参考HUI) | [] |
| hasPage | 是否存在分页组件 | Boolean | true |
| total | 数据总数 | Number | 0 |

#### Methods
| **方法名** | **说明** | **用法/参数** |
| --- | --- | --- |
| $_init | 初始化数据 | / |
| $_clearCurrentCheckedKeys | 清空选中的多选框值 | / |
| setTableColumns | 配置表格显示drawer | / |
| $_handleCheckbox | 更改多选框后回调 | / |
| $_handleQuery | page切换查询数据 | / |
| $_handleReset | 重置表单 | / |
| handleClickQuery | 点击查询按钮查询数据 | / |
| $_handleResetPageDataAndQuery | 产品列表切换查询数据 | / |

#### 示例代码1

```vue
<template>
  <div class="main">
        <tab-title-table ref="tab" :tabTableData="tabTableData"
            @query="handleQuery">
        </tab-title-table>
  </div>
</template>

<script>
import tabTitleTable from '@/components/common/bestTable/tabTitleTable';
export default {
    data() {
        return {
            tabTableData: [
                // 数组中每个对象包含一个tab下的table
                {
                    name: 'result', // tab name
                    label: '数据查询', // tab标签名
                    // 该tab下的table的表头
                    columns: [
                        {
                            title: '姓名',
                            key: 'name'
                        },
                        {
                            title: '年龄',
                            key: 'age'
                        },
                        {
                            title: '地址',
                            key: 'address'
                        }
                    ],
                    // 该tab下的table的表数据
                    tableData: [
                        {
                            name: '王小明',
                            age: 18,
                            address: '北京市朝阳区芍药居'
                        },
                        {
                            name: '张小刚',
                            age: 25,
                            address: '北京市海淀区西二旗'
                        },
                        {
                            name: '李小红',
                            age: 30,
                            address: '上海市浦东新区世纪大道'
                        },
                        {
                            name: '周小伟',
                            age: 26,
                            address: '深圳市南山区深南大道'
                        }
                    ],
                    // 该tab下的table的查询表单
                    formItems: [
                        {
                            key: 'form1', // 对应表单域model里的字段，可用于校验
                            value: 'haha', // 可选，表单项初始值
                            type: 'input', // 表单元素类型
                            label: '输入框', // 标签文本
                            labelWidth: 100 // 自定义label宽度
                        },
                        {
                            key: 'form2',
                            type: 'select',
                            label: '选择框',
                            labelWidth: 100,
                            options: [
                                { value: 'item1', label: '选项1' },
                                { value: 'item2', label: '选项2' },
                                { value: 'item3', label: '选项3' }
                            ]
                        }
                    ],
                    tableLoading: false,
                    hasPage: true, // 可选分页组件，默认为true
                    total: 50 // 表格数据总条数
                },
                {
                    name: 'search',
                    label: '数据查询2',
                    columns: [
                        {
                            title: '姓名',
                            key: 'name'
                        },
                        {
                            title: '年龄',
                            key: 'age'
                        },
                        {
                            title: '地址',
                            key: 'address'
                        }
                    ],
                    tableData: [
                        {
                            name: '王小明',
                            age: 18,
                            address: '北京市朝阳区芍药居'
                        },
                        {
                            name: '张小刚',
                            age: 25,
                            address: '北京市海淀区西二旗'
                        },
                        {
                            name: '李小红',
                            age: 30,
                            address: '上海市浦东新区世纪大道'
                        },
                        {
                            name: '周小伟',
                            age: 26,
                            address: '深圳市南山区深南大道'
                        }
                    ],
                    formItems: [
                        {
                            key: 'abc', // 对应表单域model里的字段，可用于校验
                            value: '...', // 可选，表单项初始值
                            type: 'input', // 表单元素类型
                            label: 'input', // 标签文本
                            labelWidth: 100 // 自定义label宽度
                        },
                        {
                            key: 'bcd',
                            type: 'select',
                            label: 'select',
                            labelWidth: 100,
                            options: [
                                { value: 'item1', label: '选项1' },
                                { value: 'item2', label: '选项2' },
                                { value: 'item3', label: '选项3' }
                            ]
                        }
                    ],
                    hasPage: false // 无分页组件
                }
            ]
        };
    },
    methods: {
        // 查询事件 - 包含标签name、表单及分页等参数
        handleQuery(val) {
            console.log(val);
        }
    },
    components: { tabTitleTable }
};
</script>

```

#### 示例代码2

```vue
<template>
  <div class="main">
        <tab-title-table ref="tab" :formItems="formItems" :tabTableData="tabTableData" :queryList="queryList"
           :tableLoading="tableLoading" @query="handleQuery" @save="handleSave">
        </tab-title-table>
        <saveModal v-if="modalInfo.status" :modalInfo="modalInfo" @update="handleDataQuery" />
  </div>
</template>

<script>
import saveModal from '@/components/productTimeAnalysis/saveModal.vue';
import tabTitleTable from '@/components/common/bestTable/productTimeTabTitleTable';
export default {
    data() {
        return {
            // 该tab下的table的查询表单
            formItems: [
                {
                    key: 'form1', // 对应表单域model里的字段，可用于校验
                    value: 'haha', // 可选，表单项初始值
                    type: 'input', // 表单元素类型
                    label: '输入框', // 标签文本
                    labelWidth: 100 // 自定义label宽度
                },
                {
                    key: 'form2',
                    type: 'select',
                    label: '选择框',
                    labelWidth: 100,
                    options: [
                        { value: 'item1', label: '选项1' },
                        { value: 'item2', label: '选项2' },
                        { value: 'item3', label: '选项3' }
                    ]
                }
            ],
            // 快捷查询列表
            queryList: [
                { id: 'rVhawIcBMZNZCXioLt_e', 
                 productInstNo: 'ldpsecu#1', 
                 name: '测试2', 
                 hasDefault: null, 
                 type: 'entrustDetailes', 
                 queryBody: '{"form1":"haha","form2":"item2"}', 
                 gmtCreate: '2023-04-27T01:35:48.436+00:00', 
                 gmtModified: '2023-04-27T01:35:48.436+00:00' }
            ]
            tableLoading: false,
            // 表格数据
            tabTableData: [
                // 数组中每个对象包含一个tab下的table
                {
                    type: 'table',
                    name: 'result', // tab name
                    label: '数据查询', // tab标签名
                    // 该tab下的table的表头
                    columns: [
                        {
                            title: '姓名',
                            key: 'name'
                        },
                        {
                            title: '年龄',
                            key: 'age'
                        },
                        {
                            title: '地址',
                            key: 'address'
                        }
                    ],
                    // 该tab下的table的表数据
                    tableData: [
                        {
                            name: '王小明',
                            age: 18,
                            address: '北京市朝阳区芍药居'
                        },
                        {
                            name: '张小刚',
                            age: 25,
                            address: '北京市海淀区西二旗'
                        },
                        {
                            name: '李小红',
                            age: 30,
                            address: '上海市浦东新区世纪大道'
                        },
                        {
                            name: '周小伟',
                            age: 26,
                            address: '深圳市南山区深南大道'
                        }
                    ],
                    hasPage: true, // 可选分页组件，默认为true
                    total: 50 // 表格数据总条数
                },
                {
                    type: 'table',
                    name: 'search',
                    label: '数据查询2',
                    columns: [
                        {
                            title: '姓名',
                            key: 'name'
                        },
                        {
                            title: '年龄',
                            key: 'age'
                        },
                        {
                            title: '地址',
                            key: 'address'
                        }
                    ],
                    tableData: [
                        {
                            name: '王小明',
                            age: 18,
                            address: '北京市朝阳区芍药居'
                        },
                        {
                            name: '张小刚',
                            age: 25,
                            address: '北京市海淀区西二旗'
                        },
                        {
                            name: '李小红',
                            age: 30,
                            address: '上海市浦东新区世纪大道'
                        },
                        {
                            name: '周小伟',
                            age: 26,
                            address: '深圳市南山区深南大道'
                        }
                    ],
                    hasPage: false // 无分页组件
                }
            ],
            // 保存查询弹窗
            modalInfo: {
                status: false
            }
        };
    },
    methods: {
        // 查询事件 - 包含标签name、表单及分页等参数
        handleQuery(val) {
            console.log(val);
        },
        // 保存form表单内容
        handleSave(val) {
            this.modalInfo.status = true;
            this.modalInfo.queryBody = JSON.stringify({
                ...val
            }) || ''; // 当前form表单内容
        },
        // 保存快捷查询
        async handleDataQuery(val){
            console.log(val);
        }
    },
    components: { tabTitleTable, saveModal }
};
</script>

```

