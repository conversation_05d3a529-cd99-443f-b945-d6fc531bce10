<template>
  <div>
    <h-drawer  v-model="modalData.status" width="300" title="配置" class="drawer" @on-close="handleDrawerClose">
    <div class="title">功能配置</div>
    <h-row>
      结果展示方式
      &nbsp;<h-select v-model="config.showType" :positionFixed="true" :clearable="false" width="100"
        style="margin-left: 15px;" @on-change="(val) => handleConfigChange(val, 'showType')">
        <h-option v-for="item in typeList" :key="item.key" :value="item.key">{{ item.value }}</h-option>
      </h-select>
    </h-row>
    <h-row>
      最大展示Tab个数
      <h-input-number
        v-model="config.maxTabNums"
        :max="20"
        :min="1"
        setzero
        style="width: 100px;"
        @on-change="(val) => handleConfigChange(val, 'maxTabNums')"
      ></h-input-number>
    </h-row>
    <div class="title">交互配置</div>
    <h-row>
      <h-checkbox v-model="config.accordion" style="display: inline-block;"
        @on-change="(val) => handleConfigChange(val, 'accordion')">只保留激活插件功能列表展开</h-checkbox>
    </h-row>
    <h-row>
      <h-checkbox v-model="config.saveLastParam" style="display: inline-block;"
        @on-change="(val) => handleConfigChange(val, 'saveLastParam')">记录最后一次输入参数</h-checkbox>
    </h-row>
    <h-row>
      <h-checkbox v-model="config.resultLink" style="display: inline-block;"
        @on-change="(val) => handleConfigChange(val, 'resultLink')">结果页与管理功能联动</h-checkbox>
    </h-row>
    <h-row>
      <h-checkbox v-model="config.autoGetSend" style="display: inline-block;"
        @on-change="(val) => handleConfigChange(val, 'autoGetSend')">单击Get类型功能菜单时自动发起请求</h-checkbox>
    </h-row>
    </h-drawer>
  </div>
</template>

<script>
export default {
    name: 'ConfigDrawer',
    props: {
        modalInfo: {
            type: Object,
            default: () => {}
        },
        config: {
            type: Object,
            default: () => ({})
        }
    },
    data() {
        return {
            modalData: this.modalInfo,
            typeList: [
                { key: 'JSON', value: 'JSON' },
                { key: 'Table', value: '表格' }
            ]
        };
    },
    methods: {
        handleConfigChange(val, key) {
            this.$emit('config-change', val, key);
        },
        handleDrawerClose() {
            this.modalData.status = false;
        }
    }
};
</script>

<style lang="less" scoped>
@import url("@/assets/css/drawer.less");
@import url("@/assets/css/inputNumber.less");

.drawer {
    .h-row {
        margin-bottom: 10px;
    }

    .title {
        position: relative;
        padding: 0 0 16px 20px;

        &::before {
            content: "";
            display: inline-block;
            position: absolute;
            left: 0;
            width: 4px;
            height: 17px;
            background: var(--link-color);
        }
    }
}
</style>
