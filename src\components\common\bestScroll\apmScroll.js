/*
 * @Description: <PERSON><PERSON>
 * @Author: <PERSON><PERSON>
 * @Date: 2023-04-11 10:48:06
 * @LastEditTime: 2023-04-28 16:40:49
 * @LastEditors: <PERSON><PERSON>
 */
import BScroll from '@better-scroll/core';
export default {
    name: 'apmScroll',
    props: {
        configData: {
            type: Object,
            default: function() {
                return {};
            }
        }
    },
    data() {
        return {
            scroll: null
        };
    },
    mounted() {
        setTimeout(() => {
            this._initScroll();
        }, 1000);
    },
    methods: {
        _initScroll() {
            if (!this.$refs.scrollRef) return ;
            this.scroll = new BScroll(this.$refs.scrollRef, this.configData);
        },
        refresh() {
            this.scroll && this.scroll.refresh();
        }
    },
    render() {
        return <div class="content-scroll" ref="scrollRef" style="height: 100%; overflow: hidden;">
            {this.$slots.default}
        </div>;
    }
};
