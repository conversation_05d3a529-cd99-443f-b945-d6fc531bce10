<template>
    <div class="singleton-form">
        <div class="title" style="position: relative;">
            <span>上下文参数配置</span>&nbsp;&nbsp;<h-icon :name="buttonStatus ? 'packup' : 'unfold'" size="16" style="cursor: pointer;"
                @on-click='changebuttonStatus'></h-icon>
            <span style="position: absolute; right: 140px;">引用模板</span>
            <h-select v-model="formValidate.ref" placeholder="请选择" :positionFixed="true"
                style="width: 120px; position: absolute; right: 0; top: -6px;" :clearable="true"
                @on-change="handleSelectChange">
                <h-option v-for="item in singletonTemplateNames" :key="item.value" :value="item.value">{{ item.label
                }}</h-option>
            </h-select>
        </div>
        <div class="border"></div>
        <normal-setting v-show="buttonStatus" ref="normal-setting" :saveValidateData="formValidate" :rcmId="rcmId" />
    </div>
</template>

<script>
import { normalTempDefault } from '@/config/rcmDefaultConfig';
import normalSetting from '@/components/rcmDeploy/modal/normalSetting';
import { getTemplateDetail } from '@/api/rcmApi';
export default {
    props: {
        singletonTemplateNames: {
            type: Array,
            default: []
        },
        singletonSetting: {
            type: Object,
            default: null
        },
        rcmId: {
            type: String,
            default: ''
        },
        type: {
            type: String,
            default: ''
        }
    },
    name: 'SingletonContextSetting',
    components: { normalSetting },
    data() {
        return {
            formValidate: {
                ref: '',
                ...normalTempDefault
            },
            isChange: true,
            buttonStatus: true
        };
    },
    methods: {
        init() {
            this.isChange = false;
            if (this.type === 'add'){
                this.formValidate = {
                    ref: '',
                    id: null,
                    ...normalTempDefault
                };
            } else {
                this.formValidate = {
                    ...this.singletonSetting.contexts?.[0],
                    ref: this.isExistRef(this.singletonSetting?.ref) ? this.singletonSetting?.ref : ''
                };
            }
            this.$nextTick(() => {
                this.$refs['normal-setting'].init();
            });
            setTimeout(() => {
                this.isChange = true;
            }, 1000);
        },
        // 判断模板是否存在
        isExistRef(ref){
            const refs = this.singletonTemplateNames.filter(v => v.value === ref);
            return refs.length;
        },
        getFileData() {
            const normalRes = this.$refs['normal-setting'].getFileData();
            if (normalRes) {
                return {
                    contexts: [{ ...normalRes }],
                    ref: this.formValidate.ref
                };
            } else {
                return false;
            }
        },
        async handleSelectChange(val) {
            if (this.isChange) {
                if (val) {
                    const param = {
                        id: this.rcmId,
                        name: val,
                        templateType: 'singletonContext'
                    };
                    const res = await getTemplateDetail(param);
                    if (res.code === '200') {
                        const singletonContext = res.data?.template?.singletonContext || {};
                        this.formValidate = {
                            id: this.formValidate.id || null,
                            ip: singletonContext.ip,
                            recordDir: singletonContext.recordDir,
                            repairPortStart: singletonContext.repairPortStart,
                            repairPortEnd: singletonContext.repairPortEnd,
                            ttl: singletonContext.ttl,
                            mtu: singletonContext.mtu,
                            sendWindowSize: singletonContext.sendWindowSize,
                            maxMemoryAllowedMBytes: singletonContext.maxMemoryAllowedMBytes,
                            socketBufferSizeKBytes: singletonContext.socketBufferSizeKBytes,
                            mcLoop: singletonContext.mcLoop,
                            hasTotalOrder: singletonContext.hasTotalOrder,
                            hasRecord: singletonContext.hasRecord,
                            zone: singletonContext.zone,
                            hasHandshake: singletonContext.hasHandshake,
                            ref: val
                        };
                    }
                } else {
                    return;
                }

                // 模板切换值
                this.$nextTick(() => {
                    this.$refs['normal-setting'].init();
                });
            }

        },
        changebuttonStatus() {
            this.buttonStatus = !this.buttonStatus;
        }
    }
};
</script>
<style lang="less" scoped>
.singleton-form {
    .title {
        padding-left: 12px;

        span {
            font-size: 14px;
            font-weight: 600;
        }
    }

    .border {
        border-top: 1px solid #b0b4ba;
        margin-bottom: 15px;
        margin-top: 10px;
    }
}
</style>
