<template>
    <div>
        <h-msg-box-safe v-model="modalData.status" title="终止任务" :closable="true" :mask-closable="false" width="550" maxHeight="350" allowCopy>
            <div class="content-body">
                <div class="header-info">
                    <h-icon name="android-alert" color="var(--warning-color)" size=24></h-icon>
                    <span class="title-info">您确认要终止当前正在执行中的重试任务吗？</span>
                </div>
                <div class="obj-body">
                    <p>
                        <span class="obj-body-text">全部账户：</span>
                        <span :title="modalData.ClusterName"  class="obj-body-value">
                            {{modalData.ClusterName}}
                        </span>
                    </p>
                    <p>
                        <span  class="obj-body-text">进行中：</span>
                        <span :title="modalData.fund_account" class="obj-body-value">
                            {{modalData.fund_account}}
                        </span>
                    </p>
                    <p>
                        <span  class="obj-body-text">正常完成：</span>
                        <span :title="modalData.errorRecords" class="obj-body-value">
                            {{modalData.errorRecords || '-'}}
                        </span>
                    </p>
                    <p>
                        <span  class="obj-body-text">终止完成：</span>
                        <span :title="modalData.errorRecords" class="obj-body-value">
                            {{modalData.errorRecords || '-'}}
                        </span>
                    </p>
                    <p>
                        <span  class="obj-body-text">等待中：</span>
                        <span :title="modalData.errorRecords" class="obj-body-value">
                            {{modalData.errorRecords || '-'}}
                        </span>
                    </p>
                </div>
            </div>
            <template v-slot:footer>
                <div>
                    <a-button @click="modalData.status = false">取消</a-button>
                    <a-button type="primary" @click="submitConfig">确认</a-button>
                </div>
            </template>
        </h-msg-box-safe>
    </div>
</template>

<script>
import aButton from '@/components/common/button/aButton';
export default {
    name: 'SuspensionTaskModal',
    components: { aButton },
    props: {
        modalInfo: {
            type: Object,
            default: null
        }
    },
    data() {
        return {
            loading: false,
            modalData: this.modalInfo
        };
    },
    mounted(){
        this.loading = true;
        setTimeout(() => {
            this.loading = false;
        }, 300);
    },
    methods: {
        // 修正接口请求
        submitConfig() {
            this.$emit('suspension-Task');
            this.modalData.status = false;
        }
    }
};
</script>

<style lang="less" scoped >
.header-info {
    font-weight: 500;
    display: flex;

    & > .h-icon {
        position: relative;
        top: 2px;
    }

    .title-info {
        line-height: 36px;
        font-size: 14px;
        font-weight: 600;
        vertical-align: top;
        padding: 0 5px;
    }
}

/deep/ .h-modal-body {
    padding: 5px 32px;
}

.content-body {
    .text-body {
        font-size: 12px;
        color: #24262b;
        text-align: left;
        line-height: 20px;
        padding-left: 18px;
        position: relative;
    }

    .obj-body {
        color: var(--font-color);
        line-height: 30px;
        width: 100%;

        p {
            display: flex;

            span {
                text-align: left;
                overflow: hidden;
                white-space: nowrap;
                text-overflow: ellipsis;
            }
        }

        .obj-body-text {
            width: 100px;
            text-align: left;
        }
    }
}
</style>
