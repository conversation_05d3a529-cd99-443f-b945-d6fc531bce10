/*
 * @Description: 内存表查询接口管理
 * @Author: <PERSON><PERSON>
 * @Date: 2023-08-29 09:45:34
 * @LastEditTime: 2023-11-24 17:40:29
 * @LastEditors: yingzx38608 <EMAIL>
 */
import fetch from './fetch';
import { objectToQueryString } from '@/utils/utils';

const prefix = window['LOCAL_CONFIG']['API_HOME'] || '/';

/**
 * SQL标准查询
 */
export function getMemoryDataBySql(param, timeout) {
    return fetch({ timeout: timeout, includeResponseHeaders: true }).post(`${prefix}/memory-table/sql`, param);
}

/**
 * 查询数据库列表信息
 */
export function getMemoryDatabaseList(param) {
    return fetch({ includeResponseHeaders: true }).get(`${prefix}/memory-table/sql/databases?${objectToQueryString(param)}`);
}

/**
 * 查询内存表配置信息
 */
export function getMemoryDatabaseConfig(param) {
    return fetch().get(`${prefix}/memory-table/config?${objectToQueryString(param)}`);
}

/**
 * 查询所有接入点信息
 */
export function getEndpoints(param) {
    return fetch().get(`${prefix}/endpoints?${objectToQueryString(param)}`);
}

/**
 * 查询mdb接入点信息
 */
export function getMdbEndpoints(param) {
    return fetch().get(`${prefix}/product/gateway/mdb/endpoints?${objectToQueryString(param)}`);
}

/**
 * 创建、修改mdb接入点信息
 */
export function createMdbEndpoint(param) {
    return fetch().post(`${prefix}/product/gateway/mdb/endpoint`, param);
}

/**
 * 查询mdb接入配置
 */
export function getMdbEndpointConfigs(param) {
    return fetch().get(`${prefix}/product/gateway/mdb/endpoint/backends?${objectToQueryString(param)}`);
}

/**
 * 查询ust-table接入点信息
 */
export function getUstTableEndpoints(param) {
    return fetch().get(`${prefix}/product/gateway/ust-table/endpoints?${objectToQueryString(param)}`);
}

/**
 * 查询抓包发包接入点信息
 */
export function getSendPacketReceivedEndpoints(param) {
    return fetch().get(`${prefix}/product/gateway/send-packet-received/endpoints?${objectToQueryString(param)}`);
}

/**
 * 创建、修改ust-table接入点信息
 */
export function createUstTableEndpoint(param) {
    return fetch().post(`${prefix}/product/gateway/ust-table/endpoint`, param);
}

/**
 * 创建、修改抓包发包接入点信息
 */
export function createSendPacketReceivedEndpoint(param) {
    return fetch().post(`${prefix}/product/gateway/send-packet-received/endpoint`, param);
}

/**
 * 查询ust-table接入配置
 */
export function getUstTableEndpointConfigs(param) {
    return fetch().get(`${prefix}/product/gateway/ust-table/endpoint/backends?${objectToQueryString(param)}`);
}

/**
 * 查询抓包发包接入配置
 */
export function getSendPacketReceivedEndpointConfigs(param) {
    return fetch().get(`${prefix}/product/gateway/send-packet-received/endpoint/backends?${objectToQueryString(param)}`);
}

/**
 * 抓包发包-获取默认cfg配置内容
 */
export function getSendPacketDefaultConfigs() {
    return fetch().get(`${prefix}/product/gateway/send-packet-received/config/default`);
}

/**
 * 抓包发包-获取cfg文件详情
 */
export function getSendPacketConfigs(param) {
    return fetch().get(`${prefix}/product/gateway/send-packet-received/config?${objectToQueryString(param)}`);
}

/**
 * 抓包发包-修改配置内容
 */
export function setSendPacketConfigs(param) {
    return fetch().post(`${prefix}/product/gateway/send-packet-received/config`, param);
}

/**
 * ust-table测试连通性
 */
export function testUstTableConnect(param) {
    return fetch().get(`${prefix}/product/gateway/ust-table/endpoint/backends/connect-test?${objectToQueryString(param)}`);
}

/**
 * 抓包发包测试连通性
 */
export function testSendPacketReceivedConnect(param) {
    return fetch().get(`${prefix}/product/gateway/send-packet-received/endpoint/backends/connect-test?${objectToQueryString(param)}`);
}

/**
 * 测试连通性时的节点列表
 */
export function getClustersNodeList(param) {
    return fetch().get(`${prefix}/memory-table/test-connect-databases?${objectToQueryString(param)}`);
}

/**
 * mdb测试连通性
 */
export function testMdbConnect(param) {
    return fetch().get(`${prefix}/product/gateway/mdb/endpoint/backends/connect-test?${objectToQueryString(param)}`);
}

/**
 * 查询管理功能接入点信息
 */
export function getManageFunctionEndpoints(param) {
    return fetch().get(`${prefix}/product/gateway/manage-function/endpoints?${objectToQueryString(param)}`);
}

/**
 * 查询管理功能接入配置
 */
export function getManageFunctionEndpointConfigs(param) {
    return fetch().get(`${prefix}/product/gateway/manage-function/endpoint/backends?${objectToQueryString(param)}`);
}

/**
 * 管理功能测试连通性
 */
export function testManageFunctionConnect(param) {
    return fetch().get(`${prefix}/product/gateway/manage-function/endpoint/backends/connect-test?${objectToQueryString(param)}`);
}

/**
 * 查询mdb远程配置列表
 */
export function getMdbSqlRemoteConfig(param) {
    return fetch().get(`${prefix}/memory-table/config/mdb/remote?${objectToQueryString(param)}`);
}

/**
 * 获取内存表基础信息
 */
export function getTableMetaDta(param) {
    return fetch({ includeResponseHeaders: true }).get(`${prefix}/memory-table/table/metadata?${objectToQueryString(param)}`);
}

/**
 * 查询mdb配置列表
 */
export function getMdbSqlConfig(param) {
    return fetch().get(`${prefix}/memory-table/config/mdb?${objectToQueryString(param)}`);
}

/**
 * 修改Mdb配置信息
 */
export function updateMdbSqlConfig(param) {
    return fetch().post(`${prefix}/memory-table/config/mdb/update`, param);
}

/**
 * 批量配置USTTable接入点
 */
export function updateUstTableConfig(param) {
    return fetch().post(`${prefix}/product/gateway/ust-table/endpoint/backends`, param);
}

/**
 * 查询locate接入点信息
 */
export function getLocateEndpoints(param) {
    return fetch().get(`${prefix}/product/gateway/locate/endpoints?${objectToQueryString(param)}`);
}

/**
 * 创建、修改locate接入点信息
 */
export function createLocateEndpoint(param) {
    return fetch().post(`${prefix}/product/gateway/locate/endpoint`, param);
}

/**
 * 查询locate接入配置
 */
export function getLocateEndpointConfigs(param) {
    return fetch().get(`${prefix}/product/gateway/locate/endpoint/backends?${objectToQueryString(param)}`);
}

/**
 * locate测试连通性
 */
export function testLocateConnect(param) {
    return fetch().get(`${prefix}/product/gateway/locate/endpoint/backends/connect-test?${objectToQueryString(param)}`);
}

/**
 * 查询表和实例对应关系
 */
export function getMemoryNodeByTables(param) {
    return fetch().get(`${prefix}/product/memory-table/tables?${objectToQueryString(param)}`);
}

/**
 * SQL解析路由
 */
export function getMemorySqlRouter(param) {
    return fetch().post(`${prefix}/memory-table/sql/router`, param);
}
