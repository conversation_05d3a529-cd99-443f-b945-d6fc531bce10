# 国际化文案更新指南

## 文件结构
```
src/locales/
├── final/           # 最终生成的国际化文件
│   ├── zh-CN.js    # 中文文案
│   ├── en-US.js    # 英文文案
│   └── index.js    # 导出文件
├── organized/       # 组织后的文件
├── extracted/       # 原始提取的文件
├── zh-CN.js        # 原有的中文文案
├── en-US.js        # 原有的英文文案
└── index.js        # 原有的导出文件
```

## 使用新的国际化文件

### 1. 更新导入路径
将现有的国际化导入从：
```javascript
import customLocales from '@/locales';
```

更新为：
```javascript
import customLocales from '@/locales/final';
```

### 2. 更新组件中的文案引用
将硬编码的中文文案替换为国际化key：

**之前：**
```vue
<template>
    <h-button>查询</h-button>
    <div>请选择节点</div>
</template>
```

**之后：**
```vue
<template>
    <h-button>{{ $t('common.query') }}</h-button>
    <div>{{ $t('pages.managementQuery.pleaseSelectNode') }}</div>
</template>
```

### 3. 更新JavaScript中的文案
**之前：**
```javascript
this.$hMessage.success('操作成功');
```

**之后：**
```javascript
this.$hMessage.success(this.$t('common.operationSuccess'));
```

## 添加新文案

### 1. 确定分类
- `common`: 通用文案（按钮、状态、提示等）
- `pages.{pageName}`: 页面特定文案
- `components.{componentName}`: 组件特定文案

### 2. 添加到对应文件
在 `src/locales/final/zh-CN.js` 和 `src/locales/final/en-US.js` 中添加新的键值对。

### 3. 使用小驼峰命名
- 好的命名: `pleaseSelect`, `operationSuccess`, `startTime`
- 避免: `please_select`, `operation-success`, `StartTime`

## 注意事项

1. **保持一致性**: 相同含义的文案使用相同的key
2. **避免重复**: 优先使用common中的通用文案
3. **语义化命名**: key名称应该能够表达文案的含义
4. **及时更新**: 添加新文案时同时更新中英文版本

## 常用文案参考

### 操作类
- `common.query`: 查询
- `common.add`: 添加
- `common.edit`: 编辑
- `common.delete`: 删除
- `common.save`: 保存
- `common.cancel`: 取消

### 状态类
- `common.success`: 成功
- `common.failed`: 失败
- `common.loading`: 加载中
- `common.noData`: 暂无数据

### 提示类
- `common.pleaseSelect`: 请选择
- `common.pleaseInput`: 请输入
- `common.operationSuccess`: 操作成功
- `common.confirmDelete`: 确认删除吗？
