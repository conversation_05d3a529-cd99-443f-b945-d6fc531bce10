.apm-title {
    position: relative;
    width: 100%;
    height: 42px;
    padding: 0 0 0 20px;
    background: var(--primary-color);
    border-radius: var(--border-radius);
    color: var(--font-color);
    font-size: var(--title-font-size);
    line-height: 42px;

    &::before {
        display: inline-block;
        position: relative;
        left: -5px;
        top: 3px;
        content: "";
        width: 5px;
        height: 17px;
        background: var(--link-color);
    }

    & > .text {
        margin-left: 10px;
    }

    // stot form样式
    // 单个select
    .title-single-select {
        position: absolute;
        top: 5px;
        right: 10px;
        width: auto;
        min-width: 200px;
    }

    .h-form-item-label {
        color: var(--font-color) !important;
    }

    .h-input {
        background-color: var(--input-bg-color);
        border: var(--border);
        border-radius: var(--border-radius);
        color: var(--font-color);
    }

    .h-select > .h-select-left {
        color: var(--font-color);
        background-color: var(--input-bg-color);
        border: var(--border);
    }

    .h-select-content-input,
    .h-select-input {
        color: var(--font-color);
    }
}

.obs-title {
    position: relative;
    width: 100%;
    height: 42px;
    padding: 0 0 0 20px;
    border-radius: var(--border-radius);
    color: var(--font-color);
    font-size: var(--title-font-size);
    line-height: 42px;

    &::before {
        display: inline-block;
        position: relative;
        left: -5px;
        top: 3px;
        content: "";
        width: 5px;
        height: 17px;
        background: var(--link-color);
    }

    & > .title-text {
        margin-left: 10px;
    }

    & > .title-label {
        position: absolute;
        top: 0;
        left: 35px;

        p {
            display: inline-block;
            margin-right: 10px;
        }
    }

    .title-box {
        display: flex;
        align-items: center;
        position: absolute;
        top: 0;
        right: 0;
        height: 42px;

        & > .h-select {
            width: auto;
            min-width: 200px;
            display: inline-block;
            margin-right: 10px;
        }

        .h-switch {
            margin-right: 15px;
        }

        p {
            margin-right: 10px;
            cursor: pointer;
        }

        button {
            display: inline-block;
            margin-right: 10px;
        }

        .icon-button.h-icon {
            cursor: pointer;

            &:hover {
                color: var(--link-color);
                text-decoration: underline;
            }
        }

        .h-icon {
            cursor: pointer;

            &:hover {
                color: var(--link-color);
                text-decoration: underline;
            }
        }

        .h-input-wrapper {
            width: auto;
        }
    }
}

.obs-title-default {
    position: relative;
    width: 100%;
    height: 42px;
    padding: 0 0 0 15px;
    border-radius: var(--border-radius);
    color: var(--font-color);
    font-size: var(--font-size);
    line-height: 42px;

    &::before {
        display: inline-block;
        position: relative;
        left: 0;
        top: 0;
        content: "";
        width: 0;
        height: 0;
        background: none;
    }

    & > .title-text {
        margin-left: 0;
    }
}
