<template>
    <div>
        <!-- 下发配置列表 -->
        <h-msg-box-safe
            v-model="modalData.status"
            :escClose="true"
            :mask-closable="false"
            :title="modalData.title"
            width="500"
            @on-open="handleOpen"
        >
            <div>
                <h-alert
                    show-icon
                    style="margin: 0 auto 10px;
                    padding: 6px 48px 6px 40px;">
                    {{ modalData.info }}
                </h-alert>
                <ul class="rule-box">
                    <li style="border-bottom: 1px solid var(--font-opacity-color);">
                        <span>规则：</span>
                        <div>
                            <span>{{ monitorRuleNames }}</span>
                            <a
                                class="t3-rule"
                                :data-clipboard-text="monitorRuleNames"
                                :onClick="onCopied('.t3-rule')">复制</a>
                        </div>
                    </li>
                    <li>
                        <span>主机地址：</span>
                        <div>
                            <span>{{ ip }}</span>
                            <a
                                class="t3-ip"
                                :data-clipboard-text="ip"
                                :onClick="onCopied('.t3-ip')">复制</a>
                        </div>
                    </li>
                    <li>
                        <span>端口：</span>
                        <div>
                            <span>{{ port }}</span>
                            <a
                                class="t3-port"
                                :data-clipboard-text="port"
                                :onClick="onCopied('.t3-port')">复制</a>
                        </div>
                    </li>
                     <li>
                        <span>T3API：</span>
                        <div>
                            <span>{{ t3Api }}</span>
                            <a
                                class="t3-api"
                                :data-clipboard-text="t3Api"
                                :onClick="onCopied('.t3-api')">复制</a>
                        </div>
                    </li>
                </ul>
            </div>

            <template v-slot:footer>
                <h-button @click="modalData.status = false">关闭</h-button>
            </template>
        </h-msg-box-safe>
    </div>
</template>

<script>
import { getMonitorRuleT3Api } from '@/api/ruleApi';
import Clipboard from 'clipboard';
export default {
    name: 'EditMonitorRuleModal',
    props: {
        modalInfo: {
            type: Object,
            default: null
        }
    },
    data() {
        return {
            modalData: this.modalInfo,
            copied: false,
            monitorRuleNames: '',
            port: undefined,
            ip: '',
            t3Api: ''
        };
    },
    methods: {
        onCopied(cl) {
            if (this.copied) {
                return;
            }
            const clipBoard = new Clipboard(cl);
            clipBoard.on('success', (e) => {
                this.copied = true;
                setTimeout(() => {
                    this.copied = false;
                }, 2000);
                clipBoard.destroy();
            });
            clipBoard.on('error', (e) => {
                this.$hMessage.error('该浏览器不支持复制');
                clipBoard.destroy();
            });
        },
        handleOpen() {
            this.getMonitorRuleT3Api();
        },
        async getMonitorRuleT3Api() {
            const res = await getMonitorRuleT3Api({
                productId: this.modalData.productId,
                monitorRuleIds: this.modalData.monitorRuleIds
            });
            if (res.success) {
                const data = res.data || {};
                this.monitorRuleNames = data?.monitorRuleNames.join(',');
                this.ip = data?.ip;
                this.port = data?.port;
                this.t3Api = data?.t3Api;
            } else {
                this.$hMessage.error(res.message || '获取T3监视器配置失败');
            }
        }
    }
};
</script>

<style lang="less" scoped>
    /deep/ .h-modal-body {
        padding: 16px 32px;
    }

    .rule-box {
        & > li {
            display: flex;
            padding: 10px 2px;

            & > span {
                width: 60px;
                flex-shrink: 0;
                color: var(--font-color-2);
            }

            & > div {
                & > span {
                    margin-right: 5px;
                }

                & > a {
                    display: inline-block;
                    width: 30px;
                }
            }
        }
    }
</style>
