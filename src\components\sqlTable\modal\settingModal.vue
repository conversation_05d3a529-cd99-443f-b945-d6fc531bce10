<template>
    <div>
        <h-drawer
            ref='drawer-box'
            v-model="modalData.status"
            width="400"
            title="配置"
            @on-close="handleDrawerClose"
        >
            <h-form :label-width="100">
                <h-form-item label="接入点">
                    <h-select
                        v-model="endpointId"
                        widthAdaption
                        class="select2"
                        :positionFixed="true"
                        :clearable="false"
                        @on-change="handleEndpointIdChange">
                        <h-option
                            v-for="item in modalData.endpointList"
                            :key="item.id"
                            :value="item.id">{{ item.name }}</h-option>
                    </h-select>
                </h-form-item>
                <h-form-item v-if="!isCores" label="查询路由策略">
                    <h-select
                        v-model="queryRule"
                        widthAdaption
                        class="select2"
                        :positionFixed="true"
                        :clearable="false"
                        @on-change="handleSqlRouteChange"
                    >
                        <h-option
                            v-for="item in queryRuleList"
                            :key="item.value"
                            :value="item.value">{{ item.label }}</h-option>
                    </h-select>
                </h-form-item>
                <h-form-item v-if="!isCores" label="SQL执行确认">
                    <h-select
                        v-model="sqlTableValid"
                        :positionFixed="true"
                        :clearable="false"
                        @on-change="handlePerformConfirmChange">
                        <h-option
                            v-for="item in sqlTableValidList"
                            :key="item.value"
                            :value="item.value">{{ item.label }}</h-option>
                    </h-select>
                    <p class='tip-text'>{{  getSqlTableValidTip(sqlTableValid) }}</p>
                </h-form-item>
                <h-form-item v-if="isCores" label="执行节点校验">
                    <h-switch v-model="tableCheck" @on-change="handleTableCheckChange"></h-switch>
                    <p class='tip-text'>{{  getTableCheckTip(tableCheck) }}</p>
                </h-form-item>
                <h-form-item v-if="isCores" label="修改操作确认">
                    <h-select
                        v-model="sqlValid"
                        :positionFixed="true"
                        :clearable="false"
                        @on-change="handleSqlValidChange">
                        <h-option
                            v-for="item in sqlValidList"
                            :key="item.value"
                            :value="item.value">{{ item.label }}</h-option>
                    </h-select>
                    <p class='tip-text'>{{  getSqlValidTip(tableCheck) }}</p>
                </h-form-item>
            </h-form>
        </h-drawer>
    </div>
</template>

<script>
export default ({
    props: {
        modalInfo: {
            type: Object,
            default: null
        },
        isCores: {
            type: Boolean,
            default: false
        }
    },
    data() {
        return {
            endpointId: this.modalInfo.endpointId,
            tableCheck: localStorage.getItem('apm.mdbsql.tableCheck') ? localStorage.getItem('apm.mdbsql.tableCheck') === 'true' : true,
            sqlValid: localStorage.getItem('apm.mdbsql.sqlValid') || 'update',
            modalData: this.modalInfo,
            sqlTableValid: localStorage.getItem('apm.mdbsql.performConfirm') ? localStorage.getItem('apm.mdbsql.performConfirm') : 'true',
            queryRule: localStorage.getItem('apm.mdbsql.sqlRoute') ? localStorage.getItem('apm.mdbsql.sqlRoute') : 'onlyMaster',
            queryRuleList: [
                {
                    value: 'first',
                    label: '第一个备节点'
                },
                {
                    value: 'onlyMaster',
                    label: '仅主节点'
                }
            ],
            tableCheckList: [
                {
                    value: true,
                    tip: '根据用户SQL所在的表属性，提供建议执行的节点'
                },
                {
                    value: false,
                    tip: '根据用户SQL所在的表属性，提供建议执行的节点'
                }
            ],
            sqlValidList: [
                {
                    value: 'update',
                    label: '更新操作',
                    tip: '用户SQL为"UPDATE｜DELETE｜INSERT"操作时，执行前需进行二次确认'
                },
                {
                    value: 'never',
                    label: '无需确认',
                    tip: '不区分SQL操作类型，直接执行，无需确认'
                }
            ],
            sqlTableValidList: [
                {
                    value: 'true',
                    label: '更新操作',
                    tip: '在“服务”层执行SQL，含update/delete/insert操作时，执行前需确认'
                },
                {
                    value: 'false',
                    label: '无需确认',
                    tip: '在“服务”层执行SQL，不区分操作类型直接执行，无需确认'
                }
            ]
        };
    },
    mounted() {
    },
    methods: {
        handleEndpointIdChange(val){
            this.$emit('endpointId-change', val);
        },
        // 关闭侧弹窗 清理数据
        handleDrawerClose() {
            this.modalData.status = false;
        },
        // tip切换
        getTableCheckTip(){
            return this.tableCheckList.find(o => o?.value === this.tableCheck)?.tip;
        },
        getSqlValidTip(){
            return this.sqlValidList.find(o => o?.value === this.sqlValid)?.tip;
        },
        getSqlTableValidTip(){
            return this.sqlTableValidList.find(o => o?.value === this.sqlTableValid)?.tip;
        },
        // change
        handleTableCheckChange(){
            localStorage.setItem('apm.mdbsql.tableCheck', this.tableCheck);
        },
        handleSqlValidChange(){
            localStorage.setItem('apm.mdbsql.sqlValid', this.sqlValid);
        },
        handleSqlRouteChange() {
            localStorage.setItem('apm.mdbsql.sqlRoute', this.queryRule);
        },
        handlePerformConfirmChange(e) {
            localStorage.setItem('apm.mdbsql.performConfirm', this.sqlTableValid);
        }
    },
    components: {  }
});
</script>

<style lang="less" scoped>
@import url("@/assets/css/drawer.less");

/deep/ .h-form-item {
    margin-bottom: 15px;
}

/deep/ .h-drawer-body {
    padding: 20px;
}

.tip-text {
    margin-top: 5px;
    font-size: 12px;
    color: #9296a1;
    line-height: 20px;
    font-weight: 400;
}
</style>
