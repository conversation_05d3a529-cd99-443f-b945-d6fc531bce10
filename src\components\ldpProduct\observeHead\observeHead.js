import './observeHead.less';
import { formatDate } from '@/utils/utils';
import timeLine from '@/components/ldpProduct/timeLine/timeLine';
export default {
    name: 'observeHead',
    components: { timeLine },
    props: {
        title: {
            type: String,
            default: ''
        },
        productInstNo: {
            type: String,
            default: ''
        },
        productList: {
            type: Array,
            default: () => []
        },
        timeNode: {
            type: Array,
            default: () => []
        },
        selectedSpan: {
            type: String,
            default: 'marketing'
        }

    },
    data() {
        return {
            currentTime: formatDate(new Date())
        };
    },
    methods: {
        checkProduct(val) {
            this.$emit('check-product', val);
        },
        checkTimeSpan(key) {
            this.$emit('selected-span', key);
        },
        changeTimeLineStatus() {
            this.$refs['timeLine'].init();
        }
    },
    render() {
        return <div class="obs-head">
            <div class="main-top">
                <span class="currenttime">今天是: { this.currentTime }</span>
                <p>{this.title}</p>
                <h-select value={this.productInstNo} class="securities" placeholder="请选择" positionFixed={true}
                    clearable={false} v-on:on-change={this.checkProduct}>
                    {
                        Array.isArray(this.productList) && this.productList.map(item => {
                            return <h-option key={item.id} value={item.productInstNo}>{item.productName}</h-option>;
                        })
                    }
                </h-select>
            </div>
            <time-line ref="timeLine" timeNode={this.timeNode} selectedSpan={this.selectedSpan} v-on:selected-span={this.checkTimeSpan}></time-line>
        </div>;
    }
};
