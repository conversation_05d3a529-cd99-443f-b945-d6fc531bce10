<template>
    <div class="tab-box">
        <a-loading v-if="loading"></a-loading>
        <div v-else>
            <!--MC产品节点信息-->
            <description-bar :data="description" @button-click="handleButtonClick"></description-bar>
            <info-grid ref="info-grid" :gridData="gridData" :loading="loading2">
                <template v-slot:customBox>
                    <h-tabs ref="info-grid-tabs" v-model="brokerId" :showArrow="brokersList.length > 5 ? true : false"
                        @on-click="handleSelectChange(brokerId, 'broker-select')">
                        <h-tab-pane v-for="item in brokersList" :key="item.value" :label="item.label"
                            :name="item.value">
                        </h-tab-pane>
                    </h-tabs>
                    <description-bar v-if="gridData.description" :data="gridData.description"
                        style="margin-top: 0;"></description-bar>
                </template>
            </info-grid>
        </div>
    </div>
</template>

<script>
import aLoading from '@/components/common/loading/aLoading';
import descriptionBar from '@/components/common/description/descriptionBar';
import infoGrid from '@/components/common/infoBar/infoGrid';
import { getMcAPi } from '@/api/mcApi';
const columns = [
    {
        title: '线程编号',
        key: 'ThreadNum',
        ellipsis: true
    },
    {
        title: '当前队列消息个数',
        key: 'NowInQue',
        minWidth: 140,
        ellipsis: true
    },
    {
        title: '历史队列最大消息数',
        key: 'MaxCountInQue',
        minWidth: 150,
        ellipsis: true
    },
    {
        title: '处理总数',
        key: 'DealCount',
        ellipsis: true
    }
];
export default {
    name: 'McCluster',
    props: {
        nodeData: {
            type: Object,
            default: () => { }
        }
    },
    components: { descriptionBar, aLoading, infoGrid },
    data() {
        return {
            loading: false,
            loading2: false,
            tableLoading: false,
            editProductInfo: {
                status: false
            },
            // 基础信息
            description: {
                title: {
                    label: '基础信息',
                    slots: [
                        {
                            type: 'button',
                            buttonType: 'dark',
                            key: 'broker-button',
                            value: '刷新'
                        }
                    ]
                },
                details: [
                    {
                        dataDic: [
                            {
                                label: '产品名称',
                                key: 'name',
                                span: '6'
                            },
                            {
                                label: '产品版本',
                                key: 'version',
                                span: '6'
                            },
                            {
                                label: '产品类型',
                                key: 'type',
                                span: '6'
                            },
                            {
                                label: '集群主机数',
                                key: 'typeNum',
                                span: '6'
                            }
                        ],
                        data: {
                            name: '-',
                            version: '-',
                            type: '-',
                            typeNum: '-',
                            mcAddr: '-'
                        }
                    }
                ]
            },
            brokerId: '',
            brokersList: [],
            gridData: {
                description: {
                    details: [
                        {
                            dataDic: [
                                {
                                    label: '编号',
                                    key: 'BrokerId',
                                    span: '6'
                                },
                                {
                                    label: '主机地址',
                                    key: 'Broker',
                                    span: '6'
                                }
                            ],
                            data: {
                                BrokerId: '-',
                                Broker: '-'
                            }
                        }
                    ]
                },
                layout: [
                    { x: 0, y: 0, w: 6, h: 14, i: '#0' },
                    { x: 6, y: 0, w: 6, h: 14, i: '#1' },
                    { x: 0, y: 1, w: 6, h: 14, i: '#2' },
                    { x: 6, y: 1, w: 6, h: 14, i: '#3' },
                    { x: 0, y: 2, w: 6, h: 14, i: '#4' }
                ],
                details: [
                    {
                        type: 'table',
                        title: '发布线程',
                        hasBackgroundColor: false,
                        info: {
                            tableData: [],
                            columns: columns
                        }
                    },
                    {
                        type: 'table',
                        title: '会话线程',
                        hasBackgroundColor: false,
                        info: {
                            tableData: [],
                            columns: columns
                        }
                    },
                    {
                        type: 'table',
                        title: '订阅线程',
                        hasBackgroundColor: false,
                        info: {
                            tableData: [],
                            columns: columns
                        }
                    },
                    {
                        type: 'table',
                        title: '推送线程',
                        hasBackgroundColor: false,
                        info: {
                            tableData: [],
                            columns: columns
                        }
                    },
                    {
                        type: 'table',
                        title: '主题线程',
                        hasBackgroundColor: false,
                        info: {
                            tableData: [],
                            columns: columns
                        }
                    }
                ]
            },
            fileData: {}
        };
    },
    mounted() {
    },
    beforeDestroy() {
    },
    methods: {
        // 手动清空数据
        clearData() {
            this.description.details[0].data = {};
            this.fileData = {};
            this.gridData.description.details[0].data = {};
            this.gridData.details[0].info.tableData = [];
            this.gridData.details[1].info.tableData = [];
            this.gridData.details[2].info.tableData = [];
            this.gridData.details[3].info.tableData = [];
            this.gridData.details[4].info.tableData = [];
        },
        async initData(val) {
            this.loading = true;
            await this.getFileData();
            this.setDetail();
            this.getBrokerList();
            this.loading = false;

            this.$nextTick(() => {
                this.hasDefaultBroker(val);
            });
        },
        // 获取slot
        getBrokerList() {
            this.brokersList = [];
            Object.keys(this.fileData).forEach((v) => {
                this.brokersList.push({
                    label: v?.NodeName || 'mc' + v,
                    value: v
                });
            });
        },
        // 判断brokerId是否存在,使用历史选中值用于刷新，否则默认选中第一个
        hasDefaultBroker(val) {
            const hasBroker = this.brokersList.filter(v => v?.value === val)?.[0]?.value || '';
            const searchBroker = hasBroker ? hasBroker : this.brokersList?.[0]?.value || '';
            if (!searchBroker) {
                this.clearData();
                return;
            }
            this.brokerId = searchBroker;
            this.handleSelectChange(this.brokerId, 'broker-select');
        },
        // 配置按钮、刷新按钮
        async handleButtonClick(key) {
            if (key === 'broker-button') {
                await this.initData(this.brokerId);
            }
        },
        handleSelectChange(val, key) {
            if (key === 'broker-select') {
                this.loading2 = true;
                const data = this.fileData?.[val] || [];
                this.gridData.description.details[0].data = {
                    BrokerId: data.BrokerId,
                    Broker: data.Broker
                };
                this.gridData.details[0].info.tableData = [...(data?.PubTableData || [])];
                this.gridData.details[1].info.tableData = [...(data?.SesTableData || [])];
                this.gridData.details[2].info.tableData = [...(data?.SubTableData || [])];
                this.gridData.details[3].info.tableData = [...(data?.PushTableData || [])];
                this.gridData.details[4].info.tableData = [...(data?.TopicTableData || [])];
                setTimeout(() => {
                    this.loading2 = false;
                }, 200);
            }
        },
        // 基本信息
        setDetail() {
            const mcAddr = [];
            this.nodeData.productInstances.forEach((v) => {
                mcAddr.push(v.ip + ':' + v.manageProxyPort);
            });
            const Brokers = Object.values(this.fileData).map(v => v?.Broker) || [];
            this.description.details[0].data = {
                name: this.nodeData.clusterName,
                version: '-',
                type: '消息中心',
                typeNum: [...new Set(Brokers)]?.length,
                serveManageAddress: mcAddr.join(',')
            };
        },
        // 构建页面数据
        async getFileData() {
            this.fileData = {};
            const { GetBrokerInfo } = await this.getBrokerInfoAPi();
            for (const b of Object.values(GetBrokerInfo)) {
                const key = '#' + b?.BrokerId;
                this.fileData[key] = [];
                const { GetPublishQue, GetSubPushThreadInfo, GetSessionThreadInfo, GetTopicSvrQue } = await this.getAPi(b);
                const subTableData = GetSubPushThreadInfo?.filter((v) => v.ThreadType.indexOf('subscribe') !== -1) || [];
                const pushTableData = GetSubPushThreadInfo?.filter((v) => v.ThreadType.indexOf('push') !== -1) || [];
                // 表格
                const pubTableData = (GetPublishQue || [])?.map((v) => {
                    return { ...v, ThreadNum: v?.PubThreaad ?? v?.PubThread };
                });
                const sesTableData = (GetSessionThreadInfo || [])?.map((v) => {
                    return { ...v, DealCount: v.QueDealCount };
                });
                const subTableData1 = (subTableData || [])?.map((v) => {
                    return {
                        NowInQue: v.MsgInQueue,
                        MaxCountInQue: v.MaxMsgInQueue,
                        DealCount: v.DealMsgCount,
                        ThreadNum: v.ThreadType.replace('subscribe', '').trim()
                    };
                });
                const pushTableData1 = (pushTableData || [])?.map((v) => {
                    return {
                        NowInQue: v.MsgInQueue,
                        MaxCountInQue: v.MaxMsgInQueue,
                        DealCount: v.DealMsgCount,
                        ThreadNum: v.ThreadType.replace('push', '').trim()
                    };
                });
                const topicTableData = (GetTopicSvrQue || [])?.map((v) => {
                    return { ...v, ThreadNum: 0 };
                });
                // 生产者-表格数据
                this.fileData[key] = {
                    ...b,
                    PubTableData: [...pubTableData],
                    SesTableData: [...sesTableData],
                    SubTableData: [...subTableData1],
                    PushTableData: [...pushTableData1],
                    TopicTableData: [...topicTableData]
                };
            }
        },
        // 接口请求
        async getAPi(b) {
            const broker = b?.Broker?.split(':')[0];
            const addrList = [];
            let data = {};

            for (const instance of Object.values(this.nodeData?.productInstances || [])) {
                if (instance.instanceName === (broker?.NodeName || 'mc#' + b?.BrokerId)) {
                    addrList.push({ ip: instance.ip, manageProxyPort: instance.manageProxyPort });
                }
            }
            if (addrList.length) {
                const funcNameList = ['GetPublishQue', 'GetSubPushThreadInfo', 'GetSessionThreadInfo', 'GetTopicSvrQue'];
                data = await getMcAPi(addrList, funcNameList, 'ldp_mc');
            }

            return data;
        },
        async getBrokerInfoAPi() {
            const funcNameList = ['GetBrokerInfo'];
            const data = await getMcAPi(this.nodeData?.productInstances || [], funcNameList, 'ldp_mc', false);
            return data;
        }
    }
};
</script>
<style lang="less" scoped>
@import url("@/assets/css/tab.less");
</style>
