<template>
    <div>
        <h-msg-box v-model="modalData.status" :escClose="true" :mask-closable="false" :title="modalData.title" width="300"
            :closable='false' @on-open="getCollections">
            <h-input v-model.trim="filterText" placeholder="请搜索匹配主题" clearable :maxlength="30"></h-input>
            <h-form ref="formValidate" :model="formValidate" style="height: 240px; overflow: scroll;">
                <h-form-item prop="selectedNode" :label-width="0">
                    <h-tree ref="tree" :data="dataNodes" show-checkbox :filter-node-method="filterNode" selectToCheck @on-check-change="handleCheckChange"></h-tree>
                </h-form-item>
                <div v-if="loading" class="demo-spin-container">
                    <h-spin fix></h-spin>
                </div>
            </h-form>
            <template v-slot:footer>
                <span class="total-text" :title="'共计' + dataNodes.length + '已选' + checkedNodes.length">共计 <b>{{ dataNodes.length }}</b> 已选 <b>{{ checkedNodes.length }}</b></span>
                <a-button @click="cancelMethod">取消</a-button>
                <a-button type="primary" @click="saveSubmit">确定</a-button>
            </template>
        </h-msg-box>
    </div>
</template>

<script>
import aButton from '@/components/common/button/aButton';
import { queryTopics } from '@/api/rcmApi';
export default {
    props: {
        modalInfo: {
            type: Object,
            default: null
        }
    },
    components: { aButton },
    data() {
        return {
            modalData: this.modalInfo,
            formValidate: {
                selectedNodes: []
            },
            dataNodes: [],
            checkedNodes: [],
            loading: false,
            filterText: ''
        };
    },
    watch: {
        filterText(val) {
            this.$refs.tree.filter(val);
        }
    },
    mounted(){
        this.filterText = '';
    },
    methods: {
        getCollections(){
            this.loading = true;
            const param = {
                rcmId: this.modalData.id,
                ref: '',
                topic: '',
                partition: '',
                tags: [],
                page: 1,
                pageSize: 100000000
            };
            try {
                queryTopics(param).then(res => {
                    this.dataNodes = [];
                    res.data?.list?.length && res.data.list.forEach((item, _index) => {
                        this.modalInfo.selectedNodes.length && this.modalInfo.selectedNodes.forEach(node => {
                            if (item.id === node.id){
                                item.checked = true;
                            }
                        });
                        item.title = item.topic + ' [' + item.partition + ']';
                        this.dataNodes.push(item);
                    });
                    this.checkedNodes = [...this.modalInfo.selectedNodes || []];
                    this.loading = false;
                });
            } catch (e){
                this.loading = false;
            }
        },
        // 点击确定
        saveSubmit() {
            this.$refs['formValidate'].validate((valid) => {
                if (valid) {
                    const checkedNodes = [...this.checkedNodes || []] ;
                    for (const node of checkedNodes){
                        delete node.title;
                    }
                    this.formValidate.selectedNodes = [...checkedNodes];
                    this.$emit('add', { type: this.modalInfo.type, ...this.formValidate });
                    this.modalData.status = false;
                }
            });
        },
        cancelMethod(){
            this.modalData.status = false;
            this.$refs['formValidate'].resetFields();
        },
        filterNode(val, data, node) {
            if (!val) return true;
            return node.title.indexOf(val) !== -1;
        },
        handleCheckChange(_val){
            this.checkedNodes = this.$refs['tree']?.getCheckedNodes() || [];
        }
    }
};
</script>

<style lang="less" scoped>

/deep/ .h-modal-body {
    padding: 16px;
}

/deep/ .h-tree-arrow {
    min-width: 0;
}

/deep/ .h-tree-title-selected,
/deep/ .h-tree-title-selected:hover {
    background-color: transparent;
}

.total-text {
    text-align: left;
    position: absolute;
    left: 14px;
    bottom: 18px;
    width: 140px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

</style>
