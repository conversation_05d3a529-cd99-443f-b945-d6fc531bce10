import '@/assets/css/scopeNotify.less';
// 选中区域悬浮弹窗
export default {
    name: 'bussniNotify',
    props: [],
    data() {
        return {
            scopeNotifyInfo: {}
        };
    },
    watch: {},
    methods: {},
    render() {
        return (
            <div class="scope">
                <p class="title">业务字段</p>
                <p>
                    <i>时间范围：</i>
                    123 ～
                    123231
                </p>
                <p>
                    <i>最大值：</i>
                    1234
                    ns
                </p>
                <p>
                    <i>最小值：</i>
                    543124
                    ns
                </p>
                <p>
                    <i>最大值：</i>
                    1234
                    ns
                </p>
                <p>
                    <i>最小值：</i>
                    4231
                    ns
                </p>
            </div>
        );
    }
};
