import fetch from './fetch';
import { objectToQueryString } from '@/utils/utils';

const v1 = window['LOCAL_CONFIG']['API_HOME'] || '/';
/**
 * 获取应用类型拓扑关系
 */
export function getObservableAppTypeTopology(param) {
    return fetch().get(`${v1}/observation/topology?${objectToQueryString(param)}`);
}

/**
 * 查询拓扑节点信息
 */
export function getTopoInstanceNode(param) {
    return fetch().get(`${v1}/observation/topology/node?${objectToQueryString(param)}`);
}

/**
 * 应用拓扑连接信息
 */
export function getTopoConnections(param) {
    return fetch().get(`${v1}/observation/topology/connections?${objectToQueryString(param)}`);
}

/**
 * 应用拓扑会话信息
 */
export function getTopoSessions(param) {
    return fetch().get(`${v1}/observation/topology/sessions?${objectToQueryString(param)}`);
}

/**
 * 获取上下之间的会话信息
 */
export function getCtxSessions(param) {
    return fetch().post(`${v1}/rcm/products/context/sessions`, param);
}

/**
 * 应用墙产品观测结构
 */
export function getProductObservation(param) {
    return fetch().get(`${v1}/product/observation?${objectToQueryString(param)}`);
}
