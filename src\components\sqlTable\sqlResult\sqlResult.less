.apm-poptip-time {
    .time-item-label {
        display: inline-block;
        text-align: right;
    }
}


.sql-result {
    padding: 0 4px;

    .title-result {
        display: flex;
        padding: 6px 0 0 8px;
    }

    .icon-down {
        cursor: pointer;
    }

    .h-poptip,
    .h-poptip-rel {
        display: inline-block;
        width: 100%;
    }

    .apm-poptip-time,
    .apm-poptip-time .h-poptip-rel {
        display: inline;
    }

    .a-table {
        .h-table th {
            height: 32px;
        }

        .h-table td {
            height: 28px;
        }
    }

    .a-table .h-table-tiptext {
        border: var(--border);
        font-size: 16px;
        color: var(--font-opacity-color);
    }

    .export-tip-wrap {
        display: flex;
        justify-content: flex-end;
    }

    .export-tip {
        height: 24px;
    }

    .option-text {
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
        word-break: break-all;
        cursor: pointer;
        color: #fff;
    }

    .main-flag {
        .node-flag("static/mainFlag.png");
    }

    .node-flag(@url) {
        display: inline-block;
        width: 11px;
        height: 11px;
        background: url(@url);
        background-size: 11px 10px;
        background-repeat: no-repeat;
        position: relative;
        top: 2px;
        margin-left: 5px;
    }

    .excute-time {
        color: #fff;
    }

    .h-tooltip-rel {
        display: flex;
        align-items: center;
    }

    .sql-data-operate-tooltip-icon {
        margin-left: 2px;
    }
}
