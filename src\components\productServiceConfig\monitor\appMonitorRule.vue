<template>
    <div class="tab-box">
        <query-table
            ref="queryTable"
            title="监控规则"
            class="query-table"
            :formItems="formItems"
            :columns="columns"
            :tableData="tableData"
            showTitle
            :loading="loading"
            :total="total"
            tipText="当前的监控规则均需要在“SEE-监控”做监视器配置，APM负责计算。"
            @query="handleQuery"
            @selection="tableSelection">
            <template #operateBlock>
                <a-button type="primary"
                    style="margin-right: 5px;"
                    @click="handleCreateClick">创建
                </a-button>
                <a-button type="dark"
                    style="margin-right: 5px;"
                    @click="handleOpenListClick">启用
                </a-button>
                <a-button type="dark"
                    style="margin-right: 5px;"
                    @click="handleCloseListClick">停用
                </a-button>
                <a-button type="danger"
                    style="margin-right: 8px;"
                    @click="handleDelClick">删除
                </a-button>
                <div class="separator"></div>
                <a-button type="dark"
                    style="margin-right: 5px;"
                    @click="handleT3Click">T3监视器
                </a-button>
            </template>
        </query-table>
        <!--监控详情弹窗-->
        <monitor-rule-detail-modal :modalInfo="modalInfo" />
        <!--监控规则修改-->
        <edit-monitor-rule-modal :modalInfo="editModalInfo" />
        <!--T3监视器配置-->
        <monitor-for-t3-modal :modalInfo="t3ModalInfo" />
    </div>
</template>

<script>
import { getMonitorRules, exchangeRuleEnable, delMonitorRule } from '@/api/ruleApi';
import aButton from '@/components/common/button/aButton';
import queryTable from '@/components/common/bestTable/queryTable/queryTable';
import monitorForT3Modal from '@/components/productServiceConfig/modal/monitorForT3Modal';
import editMonitorRuleModal from '@/components/productServiceConfig/modal/editMonitorRuleModal';
import monitorRuleDetailModal from '@/components/productServiceConfig/modal/monitorRuleDetailModal';
export default {
    name: 'AppMonitorRule',
    components: { queryTable, aButton, monitorRuleDetailModal, editMonitorRuleModal, monitorForT3Modal },
    props: {
        productInfo: {
            type: Object,
            default: () => {}
        }
    },
    data() {
        return {
            modal1: true,
            total: 0,
            formItems: [
                {
                    type: 'input',
                    key: 'monitorRuleName',
                    label: '规则名称',
                    value: ''
                }, {
                    type: 'select',
                    key: 'enable',
                    label: '规则启停',
                    value: '',
                    options: [
                        {
                            label: '启用',
                            value: 1
                        }, {
                            label: '停用',
                            value: 2
                        }
                    ]
                }
            ],
            columns: [
                {
                    type: 'selection',
                    width: 60,
                    align: 'center'
                },
                {
                    title: '规则名称',
                    key: 'monitorRuleName',
                    ellipsis: true
                },
                {
                    title: '监控规则说明',
                    key: 'monitorRuleDesc',
                    ellipsis: true
                },
                {
                    title: '最后修改时间',
                    key: 'updateTime',
                    width: 145
                },
                {
                    title: '规则启停',
                    key: 'enable',
                    width: 80,
                    render: (h, params) => {
                        const enable = Boolean(params.row.enable);
                        return h('h-switch', {
                            props: {
                                value: enable,
                                size: 'large'
                            },
                            on: {
                                'on-change': (val) => { this.enableChange(val, params.row); }
                            },
                            scopedSlots: {
                                open: () => '启用',
                                close: () => '停用'
                            }
                        });
                    }
                },
                {
                    title: '操作',
                    key: 'address',
                    width: 220,
                    render: (h, params) => {
                        return h('div', [
                            h(
                                'Button',
                                {
                                    props: {
                                        type: 'text',
                                        size: 'small'
                                    },
                                    on: {
                                        click: () => {
                                            this.handleDetailModal(params.row);
                                        }
                                    }
                                },
                                '查看'
                            ),
                            h(
                                'Button',
                                {
                                    props: {
                                        type: 'text',
                                        size: 'small'
                                    },
                                    on: {
                                        click: () => {
                                            this.handleEditModal(params.row);
                                        }
                                    }
                                },
                                '编辑'
                            ),
                            h(
                                'Button',
                                {
                                    props: {
                                        type: 'text',
                                        size: 'small'
                                    },
                                    style: {
                                        color: '#F5222D'
                                    },
                                    on: {
                                        click: () => {
                                            this.$hMsgBoxSafe.confirm({
                                                title: `确定要删除「${params.row?.monitorRuleName}」吗？`,
                                                content: '删除监控规则后，请检查并同步修改T3监视器的规则列表，否则该T3监视器将无法正常使用。',
                                                onOk: async () => {
                                                    const data = [
                                                        {
                                                            productId: this.productInfo.productInstNo,
                                                            monitorRuleId: params.row?.monitorRuleId,
                                                            monitorRuleName: params.row?.monitorRuleName
                                                        }
                                                    ];
                                                    this.delMonitorRule(data);
                                                    this.$refs['queryTable'].$_handleQuery();
                                                }
                                            });
                                        }
                                    }
                                },
                                '删除'
                            ),
                            h(
                                'Button',
                                {
                                    props: {
                                        type: 'text',
                                        size: 'small'
                                    },
                                    on: {
                                        click: () => {
                                            this.handleT3Click(params.row);
                                        }
                                    }
                                },
                                'T3监视器'
                            )
                        ]);
                    }
                }
            ],
            tableData: [],
            selection: [],
            modalInfo: {
                status: false,
                title: '查看监控规则内容详情',
                contentList: [{
                    label: '规则名称:',
                    text: ''
                }, {
                    label: '规则内容:',
                    text: ''
                }, {
                    label: '规则说明:',
                    text: ''
                }]
            },
            editModalInfo: {
                status: false,
                title: '修改监控规则',
                data: {}
            },
            t3ModalInfo: {
                status: false,
                title: 'T3监视器配置',
                info: '',
                productId: '',
                monitorRuleIds: []
            },
            loading: false
        };
    },
    mounted() {},
    methods: {
        initData() {
            this.$refs['queryTable'].$_handleQuery();
        },
        // 跳转规则创建页面
        handleCreateClick() {
            this.$hCore.navigate('/createRule');
        },
        // 打开T3监视器配置
        handleT3Click(data) {
            if (data) {
                this.t3ModalInfo.monitorRuleIds = data.monitorRuleId;
                this.t3ModalInfo.info = '请至SEE-监控-T3监视器配置中填写相关内容。';
            } else {
                if (!this.selection.length) {
                    this.$hMessage.warning('请先选择要查看T3监视器配置的规则');
                    return;
                } else if (this.selection.length > 5) {
                    this.$hMessage.warning('请选择五个以下监视器规则查看');
                    return;
                }
                this.t3ModalInfo.info = '请至SEE-监控-T3监视器配置中填写相关内容。一个监视器同时执行的监控规则建议不要超过5个。';
                this.t3ModalInfo.monitorRuleIds =  this.selection.map(o => o.monitorRuleId);
            }
            this.t3ModalInfo.productId = this.productInfo.productInstNo;
            this.t3ModalInfo.status = true;
        },
        // 点击删除
        async handleDelClick() {
            if (!this.selection.length) {
                this.$hMessage.warning('请先选择要删除的规则');
                return;
            }
            this.$hMsgBoxSafe.confirm({
                title: `确定要批量删除选中规则吗？`,
                onOk: async () => {
                    const params = [];
                    this.selection.forEach(ele => {
                        params.push({
                            productId: this.productInfo.productInstNo,
                            monitorRuleId: ele.monitorRuleId
                        });
                    });
                    await this.delMonitorRule(params);
                    this.$refs['queryTable'].$_handleQuery();
                }
            });
        },
        // 删除规则接口
        async delMonitorRule(params) {
            try {
                const res = await delMonitorRule(JSON.stringify(params));
                if (res.success) {
                    this.$hMessage.success('删除成功');
                } else {
                    this.$hMessage.error(res.message || '删除失败');
                }
            } catch (error) {
                console.error(error);
            }
        },
        handleQuery(val) {
            this.selection = [];
            let enable = '';
            if (val.enable === 1) {
                enable = true;
            } else if (val.enable === 2) {
                enable = false;
            }
            const param = {
                monitorRuleName: val.monitorRuleName || '',
                enable: enable,
                page: val?.page || 1,
                pageSize: val?.pageSize || 10
            };
            this.getMonitorRules(param);
        },
        // 选中行
        tableSelection(selection){
            this.selection = selection;
        },
        // 获取监控列表
        async getMonitorRules(param) {
            this.tableData = [];
            try {
                const res = await getMonitorRules({
                    productId: this.productInfo.productInstNo,
                    scene: 'dataCheck',
                    ...param
                });
                if (res.success) {
                    this.tableData = res.data?.list || [];
                    this.total = res.data?.totalCount || 0;
                } else {
                    this.tableData = [];
                    this.total = 0;
                }
            } catch (error) {
                console.error(error);
                this.tableData = [];
                this.total = 0;
            }
        },
        // 开启关闭接口
        async exchangeRuleEnable(params) {
            try {
                const res = await exchangeRuleEnable(JSON.stringify(params));
                if (!res.success) {
                    this.$hMessage.error(res.message || '规则启停失败');
                }
            } catch (error) {
                console.log(error);
            }
            this.$refs['queryTable'].$_handleQuery();
        },
        // 点击规则启停
        enableChange(val, data) {
            const params = [{
                productId: this.productInfo.productInstNo,
                monitorRuleId: data.monitorRuleId,
                enable: val
            }];
            this.exchangeRuleEnable(params);
        },
        // 批量启用
        handleOpenListClick() {
            if (!this.selection.length) {
                this.$hMessage.warning('请先选择要启用的规则');
                return;
            }
            this.$hMsgBoxSafe.confirm({
                title: `确定要批量启用选中规则吗？`,
                onOk: () => {
                    const params = [];
                    this.selection.forEach(ele => {
                        params.push({
                            productId: this.productInfo.productInstNo,
                            monitorRuleId: ele.monitorRuleId,
                            enable: true
                        });
                    });
                    this.exchangeRuleEnable(params);
                }
            });
        },
        // 批量停用
        handleCloseListClick() {
            if (!this.selection.length) {
                this.$hMessage.warning('请先选择要停用的规则');
                return;
            }
            this.$hMsgBoxSafe.confirm({
                title: `确定要批量停用选中规则吗？`,
                onOk: () => {
                    const params = [];
                    this.selection.forEach(ele => {
                        params.push({
                            productId: this.productInfo.productInstNo,
                            monitorRuleId: ele.monitorRuleId,
                            enable: false
                        });
                    });
                    this.exchangeRuleEnable(params);
                }
            });
        },
        // 修改规则信息
        handleEditModal(data) {
            this.editModalInfo.status = true;
            this.editModalInfo.data = data || {};
        },
        // 点击查看详情
        handleDetailModal(data) {
            this.modalInfo.contentList[0].text = data.monitorRuleName;
            this.modalInfo.contentList[1].text = data.monitorRuleContent;
            this.modalInfo.contentList[2].text = data.monitorRuleDesc;
            this.modalInfo.status = true;
        }
    }

};
</script>

<style lang="less" scoped>
.tab-box {
    position: relative;
    width: 100%;
    height: calc(100% - 10px);

    .query-table {
        margin-top: 10px;
        border-radius: 4px;
        background: var(--wrapper-color);
    }
}
</style>
