<template>
    <div>
        <h-msg-box-safe v-model="modalData.status" :escClose="carouselValue !== 2" :mask-closable="false"
            :title="carouselValue !== 2 ? '确认创建修改单' : '修改单执行'" width="500" class="modal" @on-close="handleCancel">
            <h-form v-if='carouselValue === 0' ref="formValidate" :model="formValidate" :label-width="120" :rules="ruleValidator">
                <h-form-item label="修改单名称：" prop="name" required>
                    <h-input v-model.trim="formValidate.name" placeholder="请输入修改单名称" :disabled="disabled">
                        <template v-slot:append>
                            <h-button icon="android-create" @on-click="changeDisable"></h-button>
                        </template>
                    </h-input>
                </h-form-item>
                <h-form-item label="批量修改操作：">自由编辑</h-form-item>
                <h-form-item label="待修改数据条数：">{{ modalData.affectedItemNum || 0 }}</h-form-item>
                <h-form-item label="待修改字段个数：">{{ modalData.affectedFieldNum || 0 }}</h-form-item>
            </h-form>
            <div v-if='carouselValue === 2' class="text">
                <p style="text-align: center;">{{ formValidate.name }}&nbsp;&nbsp;{{ detailInfo.successItemNum || 0 }}/{{
                    detailInfo.failItemNum || 0 }}/{{ detailInfo.affectedFieldNum || 0 }}</p>
                <h-progress :percent="percent" status="active" :hideIcon='true'></h-progress>
                <p style="text-align: center;">
                    <span v-if="!stopSign"><h-icon name="load-c" color="var(--link-color)"></h-icon> 正在执行数据修改，请耐心等待...
                    </span>
                    <span v-if="stopSign"><h-icon name="android-done" color="var(--success-color)"></h-icon> 修改完成 </span>
                    <a-button type="text" @click='changebuttonStatus'><h-icon :name="buttonStatus ? 'packup' : 'unfold'"
                            size="16"></h-icon></a-button>
                </p>
                <div v-if="buttonStatus" class="text-box">
                    <p v-for="item in detailInfo.items" :key="item.id">
                        <h-poptip trigger="click">
                            <span>{{ item.gmtModified }}</span>&nbsp;<span>{{ item.fieldPath }}</span>&nbsp;
                            <span>[{{ item.currentValue }}、{{ item.updateValue }}]</span>
                            <span class="span4">{{ statusEum[item.status] }}</span>
                            <template v-slot:content>
                                <div style='white-space: normal;'>
                                    <p>{{ item.gmtModified }}</p>
                                    <p>{{ item.fieldPath }}</p>
                                    <p>[{{ item.currentValue }}、{{ item.updateValue }}]</p>
                                    <p>{{ statusEum[item.status] }}</p>
                                </div>
                            </template>
                        </h-poptip>
                    </p>
                </div>
            </div>
            <template v-slot:footer>
                <a-button v-if='carouselValue !== 2' @click="handleCancel">放弃修改</a-button>
                <a-button v-if='carouselValue !== 2' type="primary" @click="handleSubmit">执行修改</a-button>
                <a-button v-if='carouselValue === 2' type="primary" @click="handleCancel">确认</a-button>
            </template>
        </h-msg-box-safe>
    </div>
</template>
<script>
import aButton from '@/components/common/button/aButton';
export default {
    props: {
        modalInfo: {
            type: Object,
            default: null
        },
        detailInfo: {
            type: Object,
            default: null
        }
    },
    components: { aButton },
    data() {
        const nameRule = function (_rule, value, callback) {
            if (value?.length > 15) {
                return callback(new Error('字符长度数不得超过15'));
            }
            callback();
        };
        return {
            carouselValue: this.modalInfo?.modelType || 0,
            modalData: this.modalInfo,
            buttonStatus: false,
            modifyFiles: '',
            formValidate: {
                name: this.modalInfo?.tabLabel || '修改单-' + this.modalInfo.orderNums
            },
            statusEum: {
                failed: '失败',
                success: '成功'
            },
            disabled: true,
            ruleValidator: {
                name: [
                    { required: true, message: '不能为空', trigger: ['blur', 'change'] },
                    { validator: nameRule, trigger: ['blur', 'change'] }
                ]
            }
        };
    },
    computed: {
        percent: function () {
            if (!this.detailInfo.affectedFieldNum) {
                return 0;
            }
            const percent = ((this.detailInfo.successItemNum + this.detailInfo.failItemNum) * 100 / this.detailInfo.affectedFieldNum).toFixed(2);
            return Number(percent || 0);
        },
        stopSign: function () {
            return this.detailInfo.status === 'finished';
        }
    },
    methods: {
        handleSubmit() {
            this.$refs['formValidate'].validate((valid) => {
                if (valid) {
                    if (this.formValidate.name.length > 15) {
                        return;
                    }
                    // 轮询修改单明细
                    this.$emit('execute', this.formValidate.name, val => {
                        if (val === false){
                            this.carouselValue = 0;
                            // false 直接退出代码,不继续执行
                            return;
                        } else {
                            this.carouselValue = 2;
                        }
                    });

                }
            });
        },
        handleCancel() {
            this.modalData.status = false;
        },
        changebuttonStatus() {
            this.buttonStatus = !this.buttonStatus;
        },
        changeDisable() {
            this.disabled = false;
        }

    }
};
</script>
<style lang="less" scoped>
.modal {
    /deep/ .h-modal-body {
        padding: 6px 16px 16px;
    }

    .h-input-group {
        width: 200px;
    }

    .h-form-item {
        margin-bottom: 0;
    }

    .text {
        p {
            font-size: var(--font-size);
            margin: 5px;
        }

        /deep/ .h-progress {
            text-align: center;
        }

        .text-box {
            width: 100%;
            max-height: 200px;
            min-height: 20px;
            overflow: auto;
            border: var(--border);
            padding: 5px;

            p {
                font-size: var(--font-size-base);
                margin: 0;

                span {
                    display: inline-block;
                    width: 130px;
                    white-space: nowrap;
                    overflow: hidden;
                    text-overflow: ellipsis;
                }

                .span4 {
                    float: right;
                    width: 25px;
                }
            }

            /deep/ .h-poptip {
                width: 100%;

                .h-poptip-rel {
                    width: 100%;
                }

                .h-poptip-popper[x-placement^="left"] {
                    .h-poptip-arrow {
                        border-left-color: var(--poptip-bg-color);

                        &::after {
                            border-left-color: var(--poptip-bg-color);
                        }
                    }
                }

                .h-poptip-popper[x-placement^="right"] {
                    .h-poptip-arrow {
                        border-right-color: var(--font-opacity-color);

                        &::after {
                            border-right-color: var(--font-opacity-color);
                        }
                    }
                }

                .h-poptip-popper[x-placement^="top"] {
                    .h-poptip-arrow {
                        border-top-color: var(--font-opacity-color);

                        &::after {
                            border-top-color: var(--font-opacity-color);
                        }
                    }
                }

                .h-poptip-popper[x-placement^="bottom"] {
                    .h-poptip-arrow {
                        border-bottom-color: var(--font-opacity-color);

                        &::after {
                            border-bottom-color: var(--font-opacity-color);
                        }
                    }
                }

                .h-poptip-inner {
                    max-width: 400px;
                    background-color: var(--font-opacity-color);
                }
            }
        }
    }
}
</style>
