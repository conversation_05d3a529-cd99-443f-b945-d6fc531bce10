<template>
    <div class="role-permission">
        <h-simple-table
            :columns="type !== 'edit' ? viewColums : columns"
            :data="tableData"
            :height="type !== 'edit' ? '450' : '390'"
            showTitle
            :loading="tableLoading"
          ></h-simple-table>
      </div>
  </template>

<script>
import _ from 'lodash';
import {
    getClusterList,
    getRolesPrivilege
} from '@/api/mdbPrivilegeApi';
export default {
    name: 'ClusterRolePermissionsInfo',
    components: {  },
    props: {
        productId: {
            type: String,
            default: ''
        },
        roleId: {
            type: String,
            default: ''
        },
        isReadonly: {
            type: Boolean,
            default: false
        },
        type: {
            type: String,
            default: 'edit'
        }
    },
    data() {
        const that = this;
        const setRender = (h, params, key) => {
            return h('h-checkbox', {
                props: {
                    value: params.row?.[key],
                    disabled: that.isReadonly
                },
                on: {
                    'on-change': (val) => {
                        this.enableChange(key, val, params.row?.tableCategory, params?.row?.clusterId);
                    }
                }
            });
        };
        const setRenderHeader = (h, params) => {
            return h(
                'h-checkbox',
                {
                    props: {
                        value: that?.[params.column.title + 'All'],
                        disabled: !that.tableData?.length || that.isReadonly
                    },
                    on: {
                        'on-change': (val) => {
                            this.enableAllChange(params.column.title, val);
                        }
                    }
                },
                `${params.column.title}`
            );
        };
        return {
            loading: false,
            // 选择表--表格
            tableLoading: false,
            selectAll: false,
            updateAll: false,
            insertAll: false,
            deleteAll: false,
            columns: [
                {
                    title: '集群',
                    key: 'clusterName',
                    ellipsis: true
                },
                {
                    title: 'select',
                    key: 'select',
                    render: (h, params) => {
                        return setRender(h, params, 'select');
                    },
                    renderHeader: (h, params) => {
                        return setRenderHeader(h, params);
                    }
                },
                {
                    title: 'update',
                    key: 'update',
                    render: (h, params) => {
                        return setRender(h, params, 'update');
                    },
                    renderHeader: (h, params) => {
                        return setRenderHeader(h, params);
                    }
                },
                {
                    title: 'insert',
                    key: 'insert',
                    render: (h, params) => {
                        return setRender(h, params, 'insert');
                    },
                    renderHeader: (h, params) => {
                        return setRenderHeader(h, params);
                    }
                },
                {
                    title: 'delete',
                    key: 'delete',
                    render: (h, params) => {
                        return setRender(h, params, 'delete');
                    },
                    renderHeader: (h, params) => {
                        return setRenderHeader(h, params);
                    }
                }
            ],
            // 只读表头
            viewColums: [
                {
                    title: '集群',
                    key: 'clusterName',
                    ellipsis: true
                },
                {
                    title: '权限',
                    key: 'permission',
                    ellipsis: true,
                    render: (h, params) => {
                        const actions = ['select', 'update', 'insert', 'delete'].filter(action => params?.row?.[action]) || [];
                        return h('span', {
                            attrs: {
                                title: actions.join(',')  || '-'
                            }
                        }, actions.join(',') || '-');

                    }
                }
            ],
            tableData: [],
            clusterList: [],
            tableEditInfos: []
        };
    },
    mounted() {
    },
    beforeDestroy() {},
    computed: {},
    methods: {
        // 初始化数据
        async initData() {
            this.tableLoading = true;
            this.clearAllData();
            try {
                // 特殊处理只读权限，在查看时展示假数据
                await this.getClusterList();
                this.clusterList?.length && await this.getRolesPrivilege();
                this.setRolesPrivilege();
            } finally {
                this.tableLoading = false;
            }
        },
        // 获取修改结果
        getTableEditInfos() {
            const res = [];
            if (this.tableEditInfos?.length){
                res.push({
                    hasAllTable: false,
                    privileges: [...this.tableEditInfos]
                });
            }
            return res;
        },
        // <----------------------------------------------------------- 接口--------------------------------------------------------------------------->
        // 获取集群列表
        async getClusterList() {
            const hasReadOnlyId = this.roleId?.includes('read-only');
            try {
                const param = {
                    productId: this.productId
                };
                const res = await getClusterList(param);
                if (this.productId !== param.productId) return;
                if (res?.code === '200' && Array.isArray(res?.data)) {
                    this.clusterList = (res?.data || []).reduce((acc, current) => {
                        return acc.concat(current.clusters);
                    }, []);

                    this.clusterList.forEach(o => {
                        this.tableData.push({
                            tableCategory: -1,
                            clusterId: o?.id,
                            clusterName: o?.clusterName,
                            select: hasReadOnlyId,
                            update: false,
                            insert: false,
                            delete: false
                        });
                    });
                } else if (res?.code?.length === 8){
                    this.$hMessage.error(res.message);
                }
            } catch (err) {
                console.error(err);
            }
        },
        // 获取默认集群权限
        async getRolesPrivilege(){
            try {
                const param = {
                    productId: this.productId,
                    roleId: this.roleId
                };
                const res = await getRolesPrivilege(param);
                if (this.productId !== param.productId || this.roleId !== param.roleId) return;
                if (res?.code === '200') {
                    for (const [index, item] of Object.entries(this.tableData || [])) {
                        const privileges = _.filter(res?.data?.privileges || [], o => { return (o?.clusterId === item?.clusterId && o?.tableCategory === item?.tableCategory); })?.[0] || {};
                        this.$set(this.tableData, index, { ...item, ...privileges });
                    }
                } else if (res?.code?.length === 8){
                    this.$hMessage.error(res.message);
                }
            } catch (err) {
                console.error(err);
            }
        },

        // <-----------------------------------------------------------  修改缓存相关  --------------------------------------------------------------------------->
        // 状态修改
        // eslint-disable-next-line max-params
        enableChange(key, value, tableCategory, clusterId, isAllChange = false) {
            const index = this.tableData.findIndex((o) => o?.clusterId === clusterId && o?.tableCategory === tableCategory);
            this.$set(this.tableData, index, { ...this.tableData[index], [key]: value });
            if (!isAllChange) {
                this[key + 'All'] = this.tableData?.length ? this.tableData.every((o) => o[key] === true) : false;
                this.setRolesPrivilege();
            }
            this.setTableEditInfos(index, tableCategory, clusterId);
        },
        // 全选、取消全选
        enableAllChange(key, value) {
            this[key + 'All'] = value;
            const data = _.cloneDeep(this.tableData);
            for (const index of Object.keys(data)) {
                this.enableChange(key, value, data[index]?.tableCategory, data[index]?.clusterId, true);
            }
            this.setRolesPrivilege();
        },
        // 缓存修改
        // eslint-disable-next-line max-params
        setTableEditInfos(index, tableCategory, clusterId){
            const infoIndex = this.getEditInfoIndex(tableCategory, clusterId);
            if (infoIndex !== -1) {
                this.tableEditInfos[infoIndex] = { ...this.tableEditInfos?.[infoIndex], ...this.tableData[index] };
            } else {
                this.tableEditInfos.push({ ...this.tableData[index] });
            }
        },
        // 设置表格数据
        setRolesPrivilege(){
            this.checkAllChange();
        },
        // 找寻缓存位置
        getEditInfoIndex(tableCategory, clusterId){
            return this.tableEditInfos.findIndex((o) => o?.clusterId === clusterId && o?.tableCategory === tableCategory);
        },
        // <-----------------------------------------------------------  表切换相关  --------------------------------------------------------------------------->
        // 清理数据
        clearData() {
            this.selectAll = false;
            this.updateAll = false;
            this.insertAll = false;
            this.deleteAll = false;
            this.tableData = [];
            this.tableEditInfos = [];
        },
        // 清理所有数据
        clearAllData() {
            this.clearData();
        },
        // 选择表头全状态联动
        checkAllChange() {
            const hasTableData = this.tableData?.length > 0;
            const updateFlag = flag => hasTableData ? this.tableData.every(o => o?.[flag]) : false;
            this.selectAll = updateFlag('select');
            this.updateAll = updateFlag('update');
            this.insertAll = updateFlag('insert');
            this.deleteAll = updateFlag('delete');
        }
    }
};
</script>
  <style lang="less" scoped>
    @import url("@/assets/css/menu.less");

    /deep/ .h-input-icon {
        cursor: pointer;

        &:hover {
            color: var(--link-color);
        }
    }

    .role-permission {
        width: 100%;
        height: 100%;

        /deep/ .h-checkbox-disabled.h-checkbox-checked .h-checkbox-inner {
            border-color: #298dff;
            background-color: #298dff;
        }

        /deep/ .h-checkbox-disabled.h-checkbox-checked .h-checkbox-inner::after {
            border-color: #fff;
        }

        .icon-button.h-btn.h-btn-ghost {
            padding: 0 6px;
        }

        /deep/ .h-btn-group .h-btn-icon-only .iconfont {
            font-size: 19px;
        }

        .select-icon {
            position: relative;
            top: 0;
        }
    }
  </style>
