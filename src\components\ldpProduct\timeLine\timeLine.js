import './timeLine.less';
import { formatDate } from '@/utils/utils';
export default {
    props: {
        timeNode: {
            type: Array,
            default: () => []
        },
        selectedSpan: {
            type: String,
            default: 'marketing'
        }
    },
    name: 'timeLine',
    data() {
        return {
            timeLines: [],
            timer: null
        };
    },
    methods: {
        init() {
            const date = formatDate(new Date());
            this.timeLines = [];
            Array.isArray(this.timeNode) && this.timeNode.forEach((item, index) => {
                this.timeLines.push({
                    key: item.key || item.name,
                    name: item.name,
                    clickable: item.clickable
                });
                const stu = this.isDuringDate(`${date} ${item.startTime}`, `${date} ${item.endTime}`);
                if (stu > 0 && stu <= 1) {
                    this.timeLines[index].status = 1;
                    this.timeLines[index].width = (stu * 100).toFixed(2) + '%';
                } else if (stu === 2) {
                    this.timeLines[index].status = 2;
                    this.timeLines[index].width = '100%';
                } else {
                    this.timeLines[index].status = 0;
                    this.timeLines[index].width = '0%';
                }
            });
        },
        isDuringDate(beginDateStr, endDateStr) {
            const curDate = new Date(),
                beginDate = new Date(beginDateStr),
                endDate = new Date(endDateStr);
            if (curDate >= beginDate && curDate <= endDate) {
                return (curDate - beginDate) / (endDate - beginDate);
            } else if (curDate > endDate) {
                return 2;
            }
            return 0;
        },
        // 点击选中某时间条
        handleClickSpan(key, clickable) {
            if (!clickable) return;
            this.$emit('selected-span', key);
        }
    },
    async mounted() {
        this.timer && clearInterval(this.timer);
        this.init();
        this.timer = setInterval(() => {
            this.init();
        }, 3000);
    },
    beforeDestroy() {
        this.timer && clearInterval(this.timer);
    },
    render() {
        return <div class="header-time">
            {
                this.timeLines.map((item, i) => {
                    return <div class={(item?.clickable && item.key === this.selectedSpan) ? 'progress-selected' : item.clickable ? 'progress' : 'progress-disclick'} style={{ width: `calc(${100 / this.timeLines.length}% - 5px)` }}
                        onClick={() => this.handleClickSpan(item.key, item.clickable)} >
                        <div class={['progress-bar', 'progress-bar-' + i]} style={{ width: item.width }}></div>
                        <span>{item.name}</span>
                    </div>;
                })
            }
        </div>;
    }
};
