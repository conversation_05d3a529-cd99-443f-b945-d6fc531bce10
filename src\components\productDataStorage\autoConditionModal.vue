<template>
    <div>
        <h-drawer ref="drawer-box" v-model="modalInfo.status" title="每日定时清理" width="65" class="wrap-box">
            <div class="title" style="position: relative; margin-top: 10px;">
                <p>清理配置<span>   (启用后生效，每日定时执行清理操作)</span></p>
                <a-button type="primary" style="position: absolute; right: 10px; top: -7px;" @click="submitConfig">保存</a-button>
            </div>
            <div class="border"></div>
            <h-form ref="formValidate" :model="formItem" :label-width="120" :cols="2" :rules="ruleValidate" >
                <h-form-item label="数据保留天数：" prop="dataRetentionDayNum" required>
                    <h-input v-model="formItem.dataRetentionDayNum" placeholder="最大3600天" type="int"  specialFilter
                        :specialDecimal="0">
                        <span slot="append">天</span>
                    </h-input>
                </h-form-item>
                <h-form-item label="数据清理时间：" prop="clearTime" required>
                    <h-time-picker v-model="formItem.clearTime" placeholder="选择清理时间" :positionFixed="true" format="HH:mm"></h-time-picker>
                </h-form-item>
                <h-form-item label="是否启用：" prop="enable">
                    <h-switch v-model="formItem.enable"></h-switch>
                </h-form-item>
            </h-form>
            <div class="title" style="position: relative; margin-top: 10px;">
                <p>定时清理记录</p>
                <a-button type="primary" style="position: absolute; right: 70px; top: -7px;" @click="getCleanRecords">查询</a-button>
                <a-button style="position: absolute; right: 10px; top: -7px;" @click="handleTableQueryReset">重置</a-button>
            </div>
            <div class="border"></div>
            <h-form ref="formValidate2" :model="formItem2" :label-width="120" :cols="2">
                <h-form-item label="清理日期：" prop="clearDate">
                    <h-date-picker v-model="formItem2.clearDate" type="daterange" transfer autoPlacement :clearable="false" placeholder="选择清理时间" :positionFixed="true" ></h-date-picker>
                </h-form-item>
                <h-form-item label="清理结果：" prop="clearResult">
                    <h-select v-model="formItem2.clearResult" placeholder="请选择" :clearable="false">
                        <h-option value="all">全部</h-option>
                        <h-option value="succeeded">成功</h-option>
                        <h-option value="failed">失败</h-option>
                    </h-select>
                </h-form-item>
            </h-form>
            <a-table
                ref="recordTable"
                showTitle
                :hasDarkClass="false"
                :height="tableHeight"
                :loading="tableLoading"
                :tableData="tableData"
                :columns="column"
                :hasPage="true"
                :total="total"
                @query="getCleanRecords"
            />
        </h-drawer>
    </div>
</template>

<script>
import { formatDate } from '@/utils/utils';
import aTable from '@/components/common/table/aTable';
import aButton from '@/components/common/button/aButton';
import { getCleanConfig, setCleanConfig, getCleanRecords } from '@/api/httpApi';
export default ({
    props: {
        modalInfo: {
            type: Object,
            default: null
        }
    },
    data() {
        const validateDayRange = (rule, value, callback) => {
            if (Number(value) && Number(value) <= 3600) {
                callback();
            } else {
                return callback(new Error('数据保留天数1~3600天'));
            }
        };
        return {
            formItem: {
                dataRetentionDayNum: '',
                clearTime: '',
                enable: false
            },
            formItem2: {
                clearDate: [new Date(), new Date()],
                clearResult: 'all'
            },
            tableLoading: false,
            modalData: this.modalInfo,
            tableHeight: 0,
            column: [
                {
                    title: '清理时间',
                    key: 'clearTime',
                    ellipsis: true
                },
                {
                    title: '清理结果',
                    key: 'clearResult',
                    ellipsis: true,
                    render: (h, params) => {
                        if (params?.row?.clearResult === '失败'){
                            return h('div', [
                                h(
                                    'Poptip',
                                    {
                                        props: {
                                            title: '失败信息',
                                            placement: 'top-end',
                                            positionFixed: true
                                        }
                                    },
                                    [
                                        h(
                                            'h-button',
                                            {
                                                props: {
                                                    size: 'small',
                                                    type: 'text'
                                                }
                                            },
                                            params?.row?.clearResult
                                        ),
                                        h(
                                            'div',
                                            {
                                                slot: 'content',
                                                style: 'max-height:200px;max-width:320px;white-space: normal; overflow:scroll'
                                            },
                                            params?.row?.errorMsg
                                        )
                                    ]
                                )
                            ]);
                        } else {
                            return h('div', {
                                attrs: {
                                    title: params?.row?.clearResult,
                                    style: 'padding: 2px 7px'
                                }
                            }, params?.row?.clearResult);
                        }
                    }
                },
                {
                    title: '被清理数据范围',
                    key: 'clearDataDate',
                    ellipsis: true
                },
                {
                    title: '索引号',
                    key: 'clearIndexName',
                    ellipsis: true
                },
                {
                    title: '被清理数据大小',
                    key: 'clearDataSize',
                    ellipsis: true,
                    render: (h, params) => {
                        return h('span', {}, `${params.row.clearDataSize.replace(/[a-z](?=\d)|\d(?=[a-z])/gi, '$& ').toUpperCase()}`);
                    }
                }
            ],
            tableData: [],
            total: 0,
            ruleValidate: {
                dataRetentionDayNum: [{ validator: validateDayRange, trigger: 'blur' }]
            }
        };
    },
    mounted() {
        this.handleDrawerOpen();
        window.addEventListener('resize', this.fetTableHeight);
    },
    beforeDestroy() {
        window.removeEventListener('resize', this.fetTableHeight);
    },
    methods: {
        // 打开弹窗加载数据
        async handleDrawerOpen() {
            this.fetTableHeight();
            await this.getCleanConfig();
            await this.getCleanRecords();
        },
        fetTableHeight() {
            this.tableHeight = this.$refs['drawer-box']?.$el?.offsetTop - (window.LOCAL_CONFIG.HEADER_VISIBLE === true ? 470 : 400);
        },
        // 查询自动化清理配置
        async getCleanConfig(){
            try {
                const res = await getCleanConfig();
                if (res?.code === '200'){
                    this.formItem = { ...res?.data };
                }
            } catch (err) {
            }
        },
        // 查询自动化清理记录
        async getCleanRecords(){
            try {
                this.tableLoading = true;
                const params = {
                    startDate: formatDate(this.formItem2.clearDate[0]),
                    endDate: formatDate(this.formItem2.clearDate[1]),
                    clearResult: this.formItem2?.clearResult === 'all' ? '' : this.formItem2?.clearResult,
                    ...this.$refs['recordTable']?.getPageData()
                };
                const res = await getCleanRecords(params);
                if (res?.code === '200'){
                    this.tableData = [...(res?.data?.list || [])];
                    this.total = res?.data?.totalCount;
                }
                this.tableLoading = false;
            } catch (err) {
                this.tableLoading = false;
                this.tableData = [];
                this.total = 0;
            }
        },
        // 重置查询条件
        handleTableQueryReset(){
            this.formItem2 = {
                clearDate: [new Date(), new Date()],
                clearResult: 'all'
            };
        },
        // 自动化清理配置
        submitConfig() {
            this.$refs['formValidate'].validate(async (valid) => {
                if (valid) {
                    const param = {
                        dataRetentionDayNum: this.formItem.dataRetentionDayNum,
                        clearTime: this.formItem.clearTime + ':00',
                        enable: this.formItem.enable
                    };
                    const res = await setCleanConfig(param);
                    if (res?.code === '200') {
                        this.$hMessage.success('定时清理配置成功！');
                        this.modalData.status = false;
                    }
                }
            });
        }
    },
    components: { aButton, aTable }
});
</script>
<style lang="less" scoped>

@import url("@/assets/css/tab.less");

/deep/ .h-btn-text {
    color: var(--link-color);
}

/deep/ .h-btn-text:hover {
    color: var(--link-color);
    text-decoration: underline;
}

.wrap-box {
    .title {
        padding-left: 12px;

        p {
            font-size: 14px;
            font-weight: 600;

            span {
                color: #b0b4ba;
            }
        }
    }

    .border {
        border-top: 1px solid #b0b4ba;
        margin-bottom: 15px;
        margin-top: 10px;
    }

    .h-form-item {
        margin-bottom: 10px;
    }

    /deep/ .h-page {
        float: right;
        margin-top: 10px;
    }
}
</style>
