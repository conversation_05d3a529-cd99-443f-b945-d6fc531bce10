#!/usr/bin/env node

const fs = require('fs');
const path = require('path');

/**
 * 完整版中文文案提取脚本
 * 确保扫描项目中的所有Vue、JS文件
 */

class CompleteI18nExtractor {
    constructor() {
        this.srcDir = path.join(__dirname, '../src');
        this.outputDir = path.join(__dirname, '../src/locales/complete');
        this.extractedTexts = new Map();
        this.allTexts = new Set();
        this.processedFiles = [];
        this.skippedFiles = [];
        
        // 中文字符正则表达式
        this.chineseRegex = /[\u4e00-\u9fff]+/g;
        
        // 统计信息
        this.stats = {
            totalFiles: 0,
            processedFiles: 0,
            skippedFiles: 0,
            totalTexts: 0,
            startTime: Date.now()
        };
    }

    /**
     * 递归获取所有Vue和JS文件
     */
    getAllFiles(dir, files = []) {
        try {
            const items = fs.readdirSync(dir);
            
            for (const item of items) {
                const fullPath = path.join(dir, item);
                const stat = fs.statSync(fullPath);
                
                if (stat.isDirectory()) {
                    // 跳过特定目录，但不跳过.hui目录
                    if (!['node_modules', 'dist', '.git', '.vscode'].includes(item)) {
                        this.getAllFiles(fullPath, files);
                    }
                } else if (stat.isFile()) {
                    // 收集所有Vue和JS文件
                    if (/\.(vue|js)$/.test(item)) {
                        files.push(fullPath);
                    }
                }
            }
        } catch (error) {
            console.warn(`扫描目录 ${dir} 失败:`, error.message);
        }
        
        return files;
    }

    /**
     * 提取文件中的中文文案
     */
    extractFromFile(filePath) {
        try {
            const content = fs.readFileSync(filePath, 'utf-8');
            const texts = new Set();
            
            // 更全面的正则表达式模式
            const patterns = [
                // 双引号内的中文
                /"([^"]*[\u4e00-\u9fff][^"]*)"/g,
                // 单引号内的中文
                /'([^']*[\u4e00-\u9fff][^']*)'/g,
                // 模板字符串内的中文
                /`([^`]*[\u4e00-\u9fff][^`]*)`/g,
                // HTML标签内的中文文本（更精确的匹配）
                />([^<>]*[\u4e00-\u9fff][^<>]*?)</g,
                // Vue指令中的中文
                /(?:title|placeholder|label|content|text|message)=["']([^"']*[\u4e00-\u9fff][^"']*)["']/g,
                // 对象属性值中的中文
                /:\s*["']([^"']*[\u4e00-\u9fff][^"']*)["']/g,
                // 数组元素中的中文
                /\[\s*["']([^"']*[\u4e00-\u9fff][^"']*)["']/g,
                // 函数调用参数中的中文
                /\(\s*["']([^"']*[\u4e00-\u9fff][^"']*)["']/g,
                // 注释中的中文（可选，通常不需要国际化）
                // /\/\/\s*([^\/\n]*[\u4e00-\u9fff][^\/\n]*)/g,
                // /\/\*([^*]*[\u4e00-\u9fff][^*]*)\*\//g
            ];
            
            patterns.forEach(pattern => {
                let match;
                const globalRegex = new RegExp(pattern.source, pattern.flags);
                
                while ((match = globalRegex.exec(content)) !== null) {
                    const text = match[1];
                    if (text && this.isValidChineseText(text)) {
                        texts.add(text.trim());
                    }
                }
            });
            
            return Array.from(texts);
        } catch (error) {
            console.warn(`提取文件 ${filePath} 失败:`, error.message);
            this.skippedFiles.push({
                file: filePath,
                error: error.message
            });
            return [];
        }
    }

    /**
     * 验证是否为有效的中文文案
     */
    isValidChineseText(text) {
        text = text.trim();
        
        // 基本过滤条件
        if (!text || text.length < 1 || text.length > 200) return false;
        if (!/[\u4e00-\u9fff]/.test(text)) return false;
        
        // 排除纯数字、符号、英文
        if (/^[\d\s\-_\.\/\\]+$/.test(text)) return false;
        if (/^[a-zA-Z\s\-_\.]+$/.test(text)) return false;
        
        // 排除代码片段和技术内容
        const excludePatterns = [
            /console\./,
            /import\s/,
            /export\s/,
            /function\s/,
            /^\$\w+/,
            /^@\w+/,
            /^#\w+/,
            /^\.\w+/,
            /^\/\//,
            /^\/\*/,
            /^\*\//,
            /^http/,
            /^www\./,
            /\.com$/,
            /\.js$/,
            /\.vue$/,
            /\.css$/,
            /\.html$/,
            /\.json$/,
            /^<\w+/,  // HTML标签
            /^\w+>/,  // HTML标签结束
            /^<!--/,  // HTML注释
            /-->$/,   // HTML注释结束
            /^\{\{/,  // Vue模板语法
            /\}\}$/,  // Vue模板语法
            /^v-\w+/, // Vue指令
            /^:\w+/,  // Vue属性绑定
            /^@\w+/,  // Vue事件绑定
            /^\$\w+/, // Vue实例属性
            /^ref\s*=/, // Vue ref
            /^key\s*=/, // Vue key
            /^class\s*=/, // CSS class
            /^style\s*=/, // CSS style
            /^id\s*=/, // HTML id
            /^data-\w+/, // data属性
            /^aria-\w+/, // aria属性
            /^role\s*=/, // role属性
            /^\w+\s*\(\s*\)/, // 函数调用
            /^\w+\s*\{/, // 对象开始
            /\}\s*$/, // 对象结束
            /^\[\s*$/, // 数组开始
            /\]\s*$/, // 数组结束
            /^return\s/, // return语句
            /^if\s*\(/, // if语句
            /^for\s*\(/, // for语句
            /^while\s*\(/, // while语句
            /^switch\s*\(/, // switch语句
            /^case\s/, // case语句
            /^default\s*:/, // default语句
            /^break\s*;/, // break语句
            /^continue\s*;/, // continue语句
            /^var\s/, // 变量声明
            /^let\s/, // 变量声明
            /^const\s/, // 变量声明
            /^this\./, // this引用
            /^\w+\.\w+/, // 属性访问（可能过于严格，需要调整）
        ];
        
        // 检查是否匹配排除模式
        if (excludePatterns.some(pattern => pattern.test(text))) {
            return false;
        }
        
        // 排除看起来像变量名或路径的内容
        if (/^[a-zA-Z_$][\w$]*$/.test(text)) return false; // 变量名
        if (/^[a-zA-Z_$][\w$]*\.[a-zA-Z_$][\w$]*$/.test(text)) return false; // 属性访问
        if (/^\/[\w\/\-\.]*$/.test(text)) return false; // 路径
        
        return true;
    }

    /**
     * 根据文件路径推断路由信息
     */
    inferRouteFromPath(filePath) {
        const relativePath = path.relative(this.srcDir, filePath);
        
        // 处理views目录下的文件
        if (relativePath.includes('views/index/') || relativePath.includes('views\\index\\')) {
            const fileName = path.basename(filePath, path.extname(filePath));
            return `pages.${fileName}`;
        }
        
        // 处理components目录下的文件
        if (relativePath.includes('components/') || relativePath.includes('components\\')) {
            const parts = relativePath.split(/[/\\]/);
            const componentIndex = parts.findIndex(part => part === 'components');
            if (componentIndex >= 0 && componentIndex + 1 < parts.length) {
                const componentName = parts[componentIndex + 1];
                return `components.${componentName}`;
            }
        }
        
        // 处理API文件
        if (relativePath.includes('api/') || relativePath.includes('api\\')) {
            return 'api';
        }
        
        // 处理utils文件
        if (relativePath.includes('utils/') || relativePath.includes('utils\\')) {
            return 'utils';
        }
        
        // 处理store文件
        if (relativePath.includes('store/') || relativePath.includes('store\\')) {
            return 'store';
        }
        
        // 处理mixins文件
        if (relativePath.includes('mixins/') || relativePath.includes('mixins\\')) {
            return 'mixins';
        }
        
        // 其他文件归类为common
        return 'common';
    }

    /**
     * 生成国际化key
     */
    generateI18nKey(text) {
        // 常见中文到英文的映射
        const mappings = {
            '查询': 'query',
            '搜索': 'search',
            '添加': 'add',
            '新增': 'add',
            '创建': 'create',
            '编辑': 'edit',
            '修改': 'edit',
            '更新': 'update',
            '删除': 'delete',
            '移除': 'remove',
            '保存': 'save',
            '取消': 'cancel',
            '确认': 'confirm',
            '提交': 'submit',
            '重置': 'reset',
            '刷新': 'refresh',
            '导出': 'export',
            '导入': 'import',
            '上传': 'upload',
            '下载': 'download',
            '复制': 'copy',
            '粘贴': 'paste',
            '清空': 'clear',
            '选择': 'select',
            '全选': 'selectAll',
            '配置': 'config',
            '设置': 'setting',
            '管理': 'manage',
            '监控': 'monitor',
            '观测': 'observation',
            '数据': 'data',
            '信息': 'info',
            '详情': 'detail',
            '列表': 'list',
            '表格': 'table',
            '成功': 'success',
            '失败': 'failed',
            '错误': 'error',
            '警告': 'warning',
            '提示': 'tip',
            '加载中': 'loading',
            '运行中': 'running',
            '已停止': 'stopped',
            '已完成': 'finished',
            '请选择': 'pleaseSelect',
            '请输入': 'pleaseInput',
            '暂无数据': 'noData',
            '操作成功': 'operationSuccess',
            '操作失败': 'operationFailed',
            '开始时间': 'startTime',
            '结束时间': 'endTime',
            '名称': 'name',
            '标题': 'title',
            '描述': 'description',
            '备注': 'remark',
            '状态': 'status',
            '类型': 'type'
        };
        
        // 尝试直接映射
        if (mappings[text]) {
            return mappings[text];
        }
        
        // 部分匹配替换
        let key = text;
        for (const [chinese, english] of Object.entries(mappings)) {
            if (key.includes(chinese)) {
                key = key.replace(new RegExp(chinese, 'g'), english);
            }
        }
        
        // 移除剩余的中文字符和特殊符号
        key = key.replace(/[\u4e00-\u9fff]/g, 'text')
                 .replace(/[^\w\s]/g, ' ')
                 .trim();
        
        // 转换为小驼峰
        key = key
            .split(/\s+/)
            .filter(word => word.length > 0)
            .map((word, index) => {
                if (index === 0) {
                    return word.toLowerCase();
                }
                return word.charAt(0).toUpperCase() + word.slice(1).toLowerCase();
            })
            .join('');
        
        return key || 'unknownText';
    }

    /**
     * 处理所有文件
     */
    processAllFiles() {
        console.log('🔍 扫描所有Vue和JS文件...');
        const allFiles = this.getAllFiles(this.srcDir);
        this.stats.totalFiles = allFiles.length;
        
        console.log(`📊 发现 ${allFiles.length} 个文件`);
        
        let processedCount = 0;
        
        allFiles.forEach(filePath => {
            processedCount++;
            const relativePath = path.relative(this.srcDir, filePath);
            
            if (processedCount % 50 === 0) {
                console.log(`📄 处理进度: ${processedCount}/${allFiles.length} (${Math.round(processedCount/allFiles.length*100)}%)`);
            }
            
            const texts = this.extractFromFile(filePath);
            
            if (texts.length > 0) {
                const routeKey = this.inferRouteFromPath(filePath);
                
                if (!this.extractedTexts.has(routeKey)) {
                    this.extractedTexts.set(routeKey, []);
                }
                
                texts.forEach(text => {
                    if (!this.allTexts.has(text)) {
                        this.allTexts.add(text);
                        this.extractedTexts.get(routeKey).push({
                            text,
                            file: relativePath,
                            key: this.generateI18nKey(text)
                        });
                    }
                });
                
                this.processedFiles.push({
                    file: relativePath,
                    textsCount: texts.length
                });
            }
        });
        
        this.stats.processedFiles = this.processedFiles.length;
        this.stats.skippedFiles = this.skippedFiles.length;
        this.stats.totalTexts = this.allTexts.size;
    }

    /**
     * 生成国际化文件
     */
    generateI18nFiles() {
        // 确保输出目录存在
        if (!fs.existsSync(this.outputDir)) {
            fs.mkdirSync(this.outputDir, { recursive: true });
        }
        
        const zhCN = {};
        const enUS = {};
        const mapping = {};
        
        // 按路由组织数据
        this.extractedTexts.forEach((texts, routeKey) => {
            const [category, module] = routeKey.split('.');

            if (!zhCN[category]) zhCN[category] = {};
            if (!enUS[category]) enUS[category] = {};

            if (module) {
                if (!zhCN[category][module]) zhCN[category][module] = {};
                if (!enUS[category][module]) enUS[category][module] = {};
                if (!mapping[category]) mapping[category] = {};
                if (!mapping[category][module]) mapping[category][module] = [];

                texts.forEach(({ text, file, key }) => {
                    // 避免重复key
                    let finalKey = key;
                    let counter = 1;
                    while (zhCN[category][module][finalKey]) {
                        finalKey = `${key}${counter}`;
                        counter++;
                    }

                    zhCN[category][module][finalKey] = text;
                    enUS[category][module][finalKey] = text; // 英文版本暂时使用中文

                    mapping[category][module].push({
                        key: finalKey,
                        chinese: text,
                        english: text,
                        file
                    });
                });
            } else {
                if (!mapping[category]) mapping[category] = [];

                texts.forEach(({ text, file, key }) => {
                    // 避免重复key
                    let finalKey = key;
                    let counter = 1;
                    while (zhCN[category][finalKey]) {
                        finalKey = `${key}${counter}`;
                        counter++;
                    }

                    zhCN[category][finalKey] = text;
                    enUS[category][finalKey] = text;

                    mapping[category].push({
                        key: finalKey,
                        chinese: text,
                        english: text,
                        file
                    });
                });
            }
        });
        
        // 写入文件
        this.writeFile('zh-CN-complete.js', `export default ${JSON.stringify(zhCN, null, 2)};`);
        this.writeFile('en-US-complete.js', `export default ${JSON.stringify(enUS, null, 2)};`);
        this.writeFile('mapping-complete.json', JSON.stringify(mapping, null, 2));
        
        // 生成详细报告
        this.generateDetailedReport();
    }

    /**
     * 写入文件
     */
    writeFile(filename, content) {
        const filePath = path.join(this.outputDir, filename);
        fs.writeFileSync(filePath, content, 'utf-8');
        console.log(`✅ 生成文件: ${filePath}`);
    }

    /**
     * 生成详细报告
     */
    generateDetailedReport() {
        const endTime = Date.now();
        const duration = Math.round((endTime - this.stats.startTime) / 1000);
        
        const report = {
            summary: {
                totalFiles: this.stats.totalFiles,
                processedFiles: this.stats.processedFiles,
                skippedFiles: this.stats.skippedFiles,
                totalTexts: this.stats.totalTexts,
                duration: `${duration}秒`,
                extractTime: new Date().toISOString()
            },
            categories: {},
            topFiles: this.processedFiles
                .sort((a, b) => b.textsCount - a.textsCount)
                .slice(0, 20),
            skippedFiles: this.skippedFiles
        };
        
        // 统计各分类的文案数量
        this.extractedTexts.forEach((texts, routeKey) => {
            const [category, module] = routeKey.split('.');
            
            if (!report.categories[category]) {
                report.categories[category] = {
                    totalTexts: 0,
                    modules: {}
                };
            }
            
            report.categories[category].totalTexts += texts.length;
            
            if (module) {
                report.categories[category].modules[module] = texts.length;
            }
        });
        
        this.writeFile('complete-extraction-report.json', JSON.stringify(report, null, 2));
        
        console.log('\n📊 完整提取统计:');
        console.log(`总文件数: ${report.summary.totalFiles}`);
        console.log(`处理文件数: ${report.summary.processedFiles}`);
        console.log(`跳过文件数: ${report.summary.skippedFiles}`);
        console.log(`总文案数: ${report.summary.totalTexts}`);
        console.log(`处理时间: ${report.summary.duration}`);
        
        console.log('\n🏆 文案最多的文件 (Top 10):');
        report.topFiles.slice(0, 10).forEach((file, index) => {
            console.log(`  ${index + 1}. ${file.file}: ${file.textsCount} 条`);
        });
        
        console.log('\n📁 分类统计:');
        Object.entries(report.categories).forEach(([category, data]) => {
            console.log(`  ${category}: ${data.totalTexts} 条文案`);
        });
    }

    /**
     * 执行完整提取
     */
    run() {
        console.log('🚀 开始完整的中文文案提取...');
        console.log(`源目录: ${this.srcDir}`);
        console.log(`输出目录: ${this.outputDir}`);
        
        this.processAllFiles();
        this.generateI18nFiles();
        
        console.log('✨ 完整提取完成!');
    }
}

// 执行脚本
if (require.main === module) {
    const extractor = new CompleteI18nExtractor();
    extractor.run();
}

module.exports = CompleteI18nExtractor;
