import './description.less';
import { transferVal } from '@/utils/utils';
export default {
    name: 'description',
    props: {
        title: {
            type: String,
            default: ''
        },
        type: {
            type: String,
            default: ''
        },
        dataDic: {
            type: Array,
            default: () => []
        },
        data: {
            type: Object,
            default: () => {}
        }
    },
    data() {
        return {
        };
    },
    methods: {
        handleButtonClick(key) {
            this.$emit('button-click', key);
        }
    },
    render() {
        const generateSlots = (slots = []) => {
            return slots.map((o) => {
                if (o.type === 'button') {
                    return (
                        <h-button
                            type={'text'}
                            onClick={() => this.handleButtonClick(o?.key)}
                            class="slot-button"
                        >
                            {o?.iconName && <h-icon name={o?.iconName} size={o?.iconSize || 14} style={{ color: o?.iconColor || 'var(--font-opacity-color)' }}></h-icon> }
                            {o?.value || ''}
                        </h-button>
                    );
                }
                return '';
            });
        };
        return <div class="descriptions">
            { this.title && <div class="descriptions-title">{this.title}</div>}
            <h-row>
                {this.dataDic.length !== 0 && this.dataDic.map(v => (
                    <h-col
                        span={v.span || 6}
                        className="descriptions-item"
                        key={v.label || v.key}
                    >
                        {/* key-value形式 */}
                        {
                            this.type === 'description' && (
                                <div class="descriptions-item-content">
                                    <div class="descriptions-item-label">{ v.label || v.key }：</div>
                                    <div class="descriptions-item-value" title={transferVal(this.data[v.key]) || '-'}>{ transferVal(this.data[v.key]) || '-'}</div>
                                    {generateSlots(v?.slots || [])}
                                </div>
                            )
                        }
                        {/* 纯文本形式 */}
                        {
                            this.type === 'text' && (
                                <div class="descriptions-item-content">
                                    <div class="descriptions-item-value" title={transferVal(v.label || v.key) || '-'}>{ transferVal(v.label || v.key) || '-'}</div>
                                </div>
                            )
                        }
                    </h-col>
                ))}
                {this.dataDic.length === 0 && (
                    <p style={{ color: 'var(--font-color)' }}>-</p>
                )}
            </h-row>
        </div>;
    }
};
