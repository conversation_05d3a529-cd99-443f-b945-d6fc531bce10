<template>
    <div>
        <h-msg-box-safe v-model="modalData.status" :title="modalData.title" :closable="true" :mask-closable="false" width="550" maxHeight="350" allowCopy>
            <div class="content-body">
                <div class="header-info">
                    <h-icon :name="modalData.headerIcon || 'android-alert'" color="var(--warning-color)" size=24></h-icon>
                    <span class="title-info">{{modalData.headerTitle}}</span>
                </div>
                <div class="obj-body">
                    <p v-for="info in modalData.contentDic" :key="info.key">
                        <span :title="info.title">{{info.title}}</span>
                        <span :title="modalData.contentObj[info.key]">
                            {{modalData.contentObj[info.key]}}
                        </span>
                    </p>
                </div>
            </div>
            <template v-slot:footer>
                <div>
                    <a-button @click="modalData.status = false">{{modalData.cancelText || '取消'}}</a-button>
                    <a-button type="primary" @click="submitConfig">{{ modalData.okText || '确认' }}</a-button>
                </div>
            </template>
        </h-msg-box-safe>
    </div>
</template>

<script>
import aButton from '@/components/common/button/aButton';
export default {
    name: 'RevisionConfigModal',
    components: { aButton },
    props: {
        modalInfo: {
            type: Object,
            default: null
        }
    },
    data() {
        return {
            loading: false,
            modalData: this.modalInfo
        };
    },
    mounted(){
        this.loading = true;
        setTimeout(() => {
            this.loading = false;
        }, 300);
    },
    methods: {
        submitConfig() {
            this.$emit('config', this.modalData);
            this.modalData.status = false;
        }
    }
};
</script>

<style lang="less" scoped >
.header-info {
    font-weight: 500;
    display: flex;

    & > .h-icon {
        position: relative;
        top: 2px;
    }

    .title-info {
        line-height: 36px;
        font-size: 14px;
        font-weight: 600;
        vertical-align: top;
        padding: 0 5px;
    }
}

/deep/ .h-modal-body {
    padding: 5px 32px;
}

.content-body {
    .text-body {
        font-size: 12px;
        color: #24262b;
        text-align: left;
        line-height: 20px;
        padding-left: 18px;
        position: relative;
    }

    .obj-body {
        color: var(--font-color);
        line-height: 30px;
        width: 100%;

        p {
            display: flex;

            span {
                text-align: left;
                overflow: hidden;
                white-space: nowrap;
                text-overflow: ellipsis;
            }
        }

        .obj-body-text {
            width: 100px;
            text-align: left;
        }
    }
}
</style>
