<!-- 网关服务 内存表管理 批量配置弹窗 -->
<template>
    <div>
        <h-msg-box
            v-model="modalData.status"
            :mask-closable="false"
            title="配置文件"
            width="700"
            height="400"
            @on-open="getCollections">
            <div>
                <h-spin v-if="loading" fix>
                    <h-icon
                        name="load-c"
                        size="18"
                        class="demo-spin-icon-load">
                    </h-icon>
                    <div>加载中......</div>
                </h-spin>

                <h-input
                    v-model="fileContent"
                    type="textarea"
                    :rows="18"
                    :canResize="false"
                    :maxlength="1024 * 1024"
                    lengthByByte
                    placeholder="请输入...">
                </h-input>
                <div class="config-container">
                    <a class="default-config-link"
                        action=""
                        @click="handleDefaultConfig">
                        <h-icon name="android-refresh"></h-icon>
                        恢复默认配置
                    </a>
                </div>
            </div>
            <template v-slot:footer>
                <h-button
                    @click="modalData.status = false">取消
                </h-button>
                <h-button type="primary"
                    :loading="btnLoading"
                    @click="submitConfig">确定
                </h-button>
            </template>
        </h-msg-box>
    </div>
</template>

<script>
import { getSendPacketDefaultConfigs, getSendPacketConfigs, setSendPacketConfigs } from '@/api/memoryApi';
export default {
    name: 'UpdatePacketConfigFile',
    components: {  },
    props: {
        modalInfo: {
            type: Object,
            default: () => {}
        }
    },
    data() {
        return {
            modalData: this.modalInfo,
            fileContent: '',
            loading: false,
            btnLoading: false
        };
    },
    methods: {
        async getCollections() {
            await this.handleFileContent();
        },
        // 获取默认配置文件内容
        async handleDefaultConfig() {
            this.loading = true;
            let fileContent = '';
            try {
                const res = await getSendPacketDefaultConfigs();
                if (res.code === '200') {
                    fileContent = res?.data?.context;
                } else if (res.code?.length === 8) {
                    this.$hMessage.error(res.message);
                }
            } catch (err) {
                console.error(err);
            } finally {
                this.loading = false;
                this.fileContent = fileContent;
            }
        },
        // 获取配置文件内容
        async handleFileContent() {
            this.loading = true;
            let fileContent = '';
            try {
                const params = {
                    id: this.modalData?.id
                };
                const res = await getSendPacketConfigs(params);
                if (res.code === '200') {
                    fileContent = res?.data?.context;
                } else if (res.code?.length === 8) {
                    this.$hMessage.error(res.message);
                }
            } catch (err) {
                console.error(err);
            } finally {
                this.loading = false;
                this.fileContent = fileContent;
            }
        },
        // 更新配置文件内容
        async submitConfig() {
            this.btnLoading = true;
            try {
                const params = {
                    id: this.modalData?.id,
                    fileName: this.modalData?.fileName,
                    context: this.fileContent
                };
                const res = await setSendPacketConfigs(params);
                if (res.code === '200') {
                    this.$hMessage.success('配置成功');
                    this.modalData.status = false;
                } else if (res.code?.length === 8) {
                    this.$hMessage.error(res.message);
                }
            } catch (err) {
                console.error(err);
            }
            this.btnLoading = false;
        }
    }
};
</script>

<style lang="less" scoped>
/deep/ .h-modal-body {
    padding: 20px 20px 10px;
}

.config-container {
    margin: 5px 0;
}

.default-config-link {
    display: inline-flex;
    align-items: center;
    vertical-align: middle;

    .h-icon {
        margin-right: 3px;
    }
}

.demo-spin-icon-load {
    animation: ani-demo-spin 1s linear infinite;
    display: inline-block;
}

@keyframes ani-demo-spin {
    from {
        transform: rotate(0deg);
    }

    50% {
        transform: rotate(180deg);
    }

    to {
        transform: rotate(360deg);
    }
}
</style>
