<!-- eslint-disable vue/no-deprecated-slot-attribute -->
<template>
    <div class="normal-form">
        <h-form ref="normalFormValidate" :model="formValidate" :rules="ruleValidate" :label-width="160" cols="2">
            <h-form-item v-if="hasAppName" label="应用名:" prop="appName" >
                <h-input  v-if="!readOnly" v-model="formValidate.appName" :maxlength="50"></h-input>
                <p v-else>{{ formValidate.appName }}</p>
            </h-form-item>
            <h-form-item label="网卡地址:" prop="ip" :validRules="ip4Rule" :required="required">
                <h-input  v-if="!readOnly" v-model="formValidate.ip"></h-input>
                <p v-else>{{ formValidate.ip }}</p>
            </h-form-item>
            <h-form-item label="持久化目录:" prop="recordDir" :required="required">
                <h-input  v-if="!readOnly" v-model="formValidate.recordDir"></h-input>
                <p v-else>{{ formValidate.recordDir }}</p>
            </h-form-item>
            <h-form-item label="补缺端口范围开始:" prop="repairPortStart" :required="required">
                <h-input  v-if="!readOnly" v-model="formValidate.repairPortStart" :maxlength="5" type="int"></h-input>
                <p v-else>{{ formValidate.repairPortStart }}</p>
            </h-form-item>
            <h-form-item label="补缺端口范围结束:" prop="repairPortEnd" :required="required">
                <h-input  v-if="!readOnly" v-model="formValidate.repairPortEnd" :maxlength="5" type="int"></h-input>
                <p v-else>{{ formValidate.repairPortEnd }}</p>
            </h-form-item>
            <h-form-item label="存活时间:" prop="ttl" :required="required">
                <h-input  v-if="!readOnly" v-model="formValidate.ttl" :specialFilter="true" :specialDecimal="2"></h-input>
                <p v-else>{{ formValidate.ttl }}</p>
            </h-form-item>
            <h-form-item label="最大传输单元:" prop="mtu" :required="required">
                <h-input  v-if="!readOnly" v-model="formValidate.mtu" :specialFilter="true" :specialDecimal="2"></h-input>
                <p v-else>{{ formValidate.mtu }}</p>
            </h-form-item>
            <h-form-item label="发送窗口大小:" prop="sendWindowSize" :required="required">
                <h-input  v-if="!readOnly" v-model="formValidate.sendWindowSize" :specialFilter="true" :specialDecimal="2"></h-input>
                <p v-else>{{ formValidate.sendWindowSize }}</p>
            </h-form-item>
            <h-form-item label="发送缓存大小(MB):" prop="maxMemoryAllowedMBytes" :required="required">
                <h-input  v-if="!readOnly" v-model="formValidate.maxMemoryAllowedMBytes" :specialFilter="true" :specialDecimal="2"></h-input>
                <p v-else>{{ formValidate.maxMemoryAllowedMBytes }}</p>
            </h-form-item>
            <h-form-item label="套接字接收缓存大小(KB):" prop="socketBufferSizeKBytes" :required="required">
                <h-input  v-if="!readOnly" v-model="formValidate.socketBufferSizeKBytes" :specialFilter="true" :specialDecimal="2"></h-input>
                <p v-else>{{ formValidate.socketBufferSizeKBytes }}</p>
            </h-form-item>
            <h-form-item label="中心名:" prop="zone">
                <h-select v-if="!readOnly" v-model="formValidate.zone" placement="top" :specialFilter="true">
                    <h-option v-for="item in zones" :key="item.id" :value="item.name">{{ item.name }}</h-option>
                </h-select>
                <p v-else>{{ formValidate.zone || '-' }}</p>
            </h-form-item>
            <h-form-item label="开启本地回环:" :required="required">
                <h-switch  v-if="!readOnly" v-model="formValidate.mcLoop" size="large">
                    <span slot="open">ON</span>
                    <span slot="close">OFF</span>
                </h-switch>
                <p v-else>{{ formValidate.mcLoop ? '是' : '否' }}</p>
            </h-form-item>
            <h-form-item label="是否统一排序:" :required="required">
                <h-switch  v-if="!readOnly" v-model="formValidate.hasTotalOrder" size="large">
                    <span slot="open">ON</span>
                    <span slot="close">OFF</span>
                </h-switch>
                <p v-else>{{ formValidate.hasTotalOrder ? '是' : '否' }}</p>
            </h-form-item>
            <h-form-item label="是否持久化:" :required="required">
                <h-switch  v-if="!readOnly" v-model="formValidate.hasRecord" size="large">
                    <span slot="open">ON</span>
                    <span slot="close">OFF</span>
                </h-switch>
                <p v-else>{{ formValidate.hasRecord ? '是' : '否'}}</p>
            </h-form-item>
            <h-form-item label="接收端是否序号协商:" :required="required">
                <h-switch  v-if="!readOnly" v-model="formValidate.hasHandshake" size="large">
                    <span slot="open">ON</span>
                    <span slot="close">OFF</span>
                </h-switch>
                <p v-else>{{ formValidate.hasHandshake ? '是' : '否'}}</p>
            </h-form-item>
        </h-form>
    </div>
</template>

<script>
import { getZones } from '@/api/rcmApi';
export default {
    props: {
        saveValidateData: {
            type: Object,
            default: {}
        },
        readOnly: {
            type: Boolean,
            default: false
        },
        hasAppName: {
            type: Boolean,
            default: true
        },
        rcmId: {
            type: String,
            default: null
        }
    },
    name: 'NormalSetting',
    data() {
        const validatePortStart = (rule, value, callback) => {
            if (value === '') {
                return callback(new Error('参数不能为空'));
            } else if (value < 1 || value > 65535) {
                return callback(new Error('请输入1到65535之间的端口值'));
            } else if (value > Number(this.formValidate.repairPortEnd)) {
                return callback(new Error('repairPortStart不能大于repairPortEnd'));
            }
            callback();
        };
        const validatePortEnd = (rule, value, callback) => {
            if (value === '') {
                return callback(new Error('参数不能为空'));
            } else if (value < 1 || value > 65535) {
                return callback(new Error('请输入1到65535之间的端口值'));
            } else if (value < Number(this.formValidate.repairPortStart)) {
                return callback(new Error('repairPortStart不能大于repairPortEnd'));
            }
            callback();
        };
        return {
            formValidate: this.saveValidateData,
            ip4Rule: ['ip4'],
            ruleValidate: { // 校验规则
                repairPortStart: [{ validator: validatePortStart, trigger: 'blur' }],
                repairPortEnd: [{ validator: validatePortEnd, trigger: 'blur' }]
            },
            required: true,
            zones: []
        };
    },
    mounted() {
        this.required = !this.readOnly;
        this.init();
    },
    methods: {
        init(context){
            // appName不变化
            this.formValidate = {
                ...this.formValidate,
                ...this.saveValidateData,
                ...context,
                mcLoop: context ? Boolean(context.mcLoop) : Boolean(this.saveValidateData.mcLoop)
            };
            this.queryZones();
        },
        async queryZones() {
            if (this.rcmId) {
                const res = await getZones({ rcmId: this.rcmId });
                if (res.code === '200') {
                    this.zones = res.data || [];
                    if (!this.zones.find(item => item.name === this.formValidate.zone)) {
                        this.formValidate.zone = null;
                    }
                }
            }
        },
        getFileData() {
            let data = '';
            this.$refs['normalFormValidate'].validate((valid) => {
                if (valid) {
                    data =  {
                        ...this.formValidate,
                        id: this.formValidate.id || null,
                        repairPortStart: Number(this.formValidate.repairPortStart),
                        repairPortEnd: Number(this.formValidate.repairPortEnd),
                        ttl: Number(this.formValidate.ttl),
                        mtu: Number(this.formValidate.mtu),
                        sendWindowSize: Number(this.formValidate.sendWindowSize),
                        maxMemoryAllowedMBytes: Number(this.formValidate.maxMemoryAllowedMBytes),
                        socketBufferSizeKBytes: Number(this.formValidate.socketBufferSizeKBytes),
                        mcLoop: Number(this.formValidate.mcLoop)
                    };
                }
            });
            return data;
        }
    }
};
</script>
<style lang="less" scoped>
/deep/ .h-switch {
    vertical-align: baseline;
}
</style>
