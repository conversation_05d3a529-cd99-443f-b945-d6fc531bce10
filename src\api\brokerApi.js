import fetch from './fetch';
import { objectToQueryString } from '@/utils/utils';

const prefix = window['LOCAL_CONFIG']['API_HOME'] || '/';

// 获取名单列表接口
export function getDowngradeConfigList(param) {
    return fetch().get(`${prefix}/downgrade/fuse/rules?${objectToQueryString(param)}`);
}

// 创建或更新名单
export function createOrUpdateConfig(param) {
    return fetch().post(`${prefix}/downgrade/fuse/rule/createOrUpdate`, param);
}

// 删除名单
export function deleteRuleConfig(param) {
    return fetch().post(`${prefix}/downgrade/fuse/rule/delete`, param);
}

// 查看远程规则列表
export function getRemoteRuleList(param) {
    return fetch().get(`${prefix}/downgrade/fuse/remote/rules?${objectToQueryString(param)}`);
}

// 发布规则
export function publishRules(param) {
    return fetch().get(`${prefix}/downgrade/fuse/rules/release?${objectToQueryString(param)}`);
}

// 获取组列表接口
export function getRuleGroupList(param) {
    return fetch().get(`${prefix}/downgrade/fuse/ruleGroups?${objectToQueryString(param)}`);
}

// 创建或更新组
export function createOrUpdateRuleGroup(param) {
    return fetch().post(`${prefix}/downgrade/fuse/ruleGroup/createOrUpdate`, param);
}

// 删除组
export function deleteRuleGroup(param) {
    return fetch().post(`${prefix}/downgrade/fuse/ruleGroup/delete`, param);
}

/** ***************  数据二次上场相关接口  *****************/

// 数据加载规则查询
export function getLoadDataRules(param){
    return fetch().get(`${prefix}/product/load-data/rules?${objectToQueryString(param)}`);
}

// 批量编辑数据加载规则配置
export function batchLoadDataRule(param){
    return fetch().post(`${prefix}/product/load-data/rules/config/batch`, param);
}

// 编辑数据加载规则配置
export function configLoadDataRule(param){
    return fetch().post(`${prefix}/product/load-data/rule/config`, param);
}

// 批量数据加载
export function batchLoadData(param){
    return fetch().post(`${prefix}/product/load-data/batch`, param);
}

// 批量数据加载状态信息查询
export function getLoadDataStatus(param){
    return fetch().get(`${prefix}/product/load-data/batch/status?${objectToQueryString(param)}`);
}

// 数据加载历史记录查询
export function getLoadDataHistory(param){
    return fetch().get(`${prefix}/product/load-data/histories?${objectToQueryString(param)}`);
}

// 数据加载结果查询
export function getLoadDataResult(param){
    return fetch().get(`${prefix}/product/load-data/result?${objectToQueryString(param)}`);
}

// 数据加载规则结果查询
export function getLoadDataRuleResult(param){
    return fetch().get(`${prefix}/product/load-data/rule/result?${objectToQueryString(param)}`);
}

// 应用节点类型字典查询
export function getInstanceTypeDict(param){
    return fetch().get(`${prefix}/product/instance-type/dict?${objectToQueryString(param)}`);
}
//
export function getFuseRulesReleaseTime(param) {
    return fetch().get(`${prefix}/downgrade/fuse/rules/release/time?${objectToQueryString(param)}`);
}

// 数据上场
export function importData(param) {
    return fetch().post(`${prefix}/product/import-data`, param);
}

// 查询数据上场状态
export function queryImportDataStatus(param){
    return fetch().get(`${prefix}/product/import-data/status?${objectToQueryString(param)}`);
}

// 查询数据上场历史记录
export function getImportDataHistory(param){
    return fetch().get(`${prefix}/product/import-data/status?${objectToQueryString(param)}`);
}

// 查询数据上场详情
export function getImportDataDetail(param){
    return fetch().get(`${prefix}/product/import-data/details?${objectToQueryString(param)}`);
}

// 查询数据上场表详情
export function getImportDataTableDetail(param){
    return fetch().get(`${prefix}/product/import-data/table/details?${objectToQueryString(param)}`);
}

/** ***************  数据二次上场相关接口-新  *****************/

/**
 * 查询数据上场历史列表
 */
export function getAppearHistoryData(param) {
    return fetch().get(`${prefix}/product/data/imports-history?${objectToQueryString(param)}`);
}

/**
 * 查询数据上场结果
 */
export function getLatestAppearData(param) {
    return fetch().get(`${prefix}/product/data/import-result?${objectToQueryString(param)}`);
}

/**
 * 执行数据上场
 */
export function executeAppearance(param) {
    return fetch().post(`${prefix}/product/data/import`, param);
}
