import './loading.less';
export default {
    name: 'a-Loading',
    props: {
        width: {
            type: String,
            default: '100'
        },
        height: {
            type: String,
            default: '100'
        }
    },
    data() {
        return {
        };
    },
    render() {
        return <div class="spin-container">
            <div class="reverse-spinner" style={{ width: this.width + 'px', height: this.height + 'px' }}></div>
        </div>;
    }
};
