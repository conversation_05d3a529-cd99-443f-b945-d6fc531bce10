<template>
    <h-form
      ref="form"
      class="createRule-content-form"
      :model="condition"
      :labelWidth="103"
    >
      <div class="createRule-content-form-wrap" :data-showLabel="showLabel">
        <h-form-item
          :label="showLabel ? '阈值与等级' : null "
          class="first-form"
          prop="leftMetricName"
        >
          <!-- <h-input
            v-model="condition.leftMetricName"
            readonly
            placeholder="请输入指标名"
          /> -->
          <span>“{{condition.leftMetricName}}”</span>
        </h-form-item>
        <div class="createRule-content-form-wrap-text">与</div>
        <h-form-item prop="rightMetricName">
          <!-- <h-input
            v-model="condition.rightMetricName"
            readonly
            placeholder="请输入指标名"
          /> -->
          <span>“{{condition.rightMetricName}}”</span>
        </h-form-item>
        <div class="createRule-content-form-wrap-text">差值</div>
        <h-form-item prop="operator" required>
          <h-select
            v-model="condition.operator"
            style="width: 75px; margin-right: 8px;"
            placeholder="请选择"
            placement="bottom"
            :positionFixed="true"
            :clearable="false"
          >
            <h-option value="gt">&gt;</h-option>
            <h-option value="gte">&gt;=</h-option>
            <h-option value="eq">=</h-option>
          </h-select>
        </h-form-item>
        <h-form-item prop="threshold" required
        :valid-rules="[{ test: rule, trigger: 'blur, change' }]"
        >
          <h-input
            v-model="condition.threshold"
            placeholder="请输入阈值"
          />
        </h-form-item>
        <div class="createRule-content-form-wrap-text">时告警</div>
        <!-- <h-form-item prop="alertLevel" required>
          <div class="createRule-content-form-wrap-level">
            <h-input disabled placeholder="告警等级" />
            <h-select
              v-model="condition.alertLevel"
              placeholder="请选择"
              placement="bottom"
              :positionFixed="true"
              :clearable="false"
            >
              <h-option value=">">></h-option>
              <h-option value="=">=</h-option>
            </h-select>
          </div>
        </h-form-item> -->
      </div>
    </h-form>
  </template>

<script>
import { matchValidate } from '@/utils/validate';

function isPositiveInteger(value) {
    /**
     * 判断一个字符串值是否为正整数.
     *
     * @param {string} value - 需要判断的字符串值.
     * @return {boolean} - 如果值是正整数返回 true，否则返回 false.
     */
    if (String(value).trim() === '') return false;

    // 将字符串转为整数
    const num = Number(value);

    // 检查字符串是否为正整数
    return Number.isInteger(num) && num > 0;
}

export default {
    props: {
        showLabel: {
            type: Boolean,
            default: () => true
        }
    },
    data() {
        return {
            matchValidate,
            condition: {
                leftMetricName: null,
                rightMetricName: null,
                operator: 'gt',
                threshold: 1000
            }
        };
    },
    methods: {
        /**
         * 校验
        */
        validate(...args) {
            this.$refs['form'].validate(...args);
        },
        /**
         * 设置填充form的值
        */
        setValue(arg) {
            this.condition = { ...this.condition, ...arg };
        },
        /**
         * 获取表单数据
        */
        getValue() {
            return this.condition;
        },
        /**
         * 阈值校验规则
        */
        rule(rule, val, callback) {
            if (!isPositiveInteger(val)) {
                return callback(new Error('只能输入正整数'));
            }
            if (String(val).length > 9) {
                return callback(new Error('不能大于999999999'));
            }
            return callback();
        },
        /**
         * validateField
         */
        validateField(...args) {
            this.$refs['form'].validateField(...args);
        }
    }
};

</script>

  <style lang="less">
    /* stylelint-disable-next-line selector-class-pattern */
    .createRule-content-form-wrap {
        display: flex;
        flex-wrap: wrap;

        &-text {
            height: 32px;
            line-height: 32px;
            margin: 0 8px;
        }

        .h-form-item-content {
            margin-left: 0 !important;
        }

        /* stylelint-disable-next-line selector-class-pattern */
        .h-form-item-requiredIcon {
            display: none;
        }

        .first-form {
            .h-form-item-content {
                margin-left: 0 !important;
            }
        }

        &[data-showLabel="true"] {
            .first-form {
                .h-form-item-content {
                    margin-left: 103px !important;
                }
            }
        }

        &-level {
            display: flex;

            input {
                width: 50px;
            }

            .h-input {
                border-top-right-radius: 0 !important;
                border-bottom-right-radius: 0 !important;
            }

            .h-select-selection {
                border-top-left-radius: 0 !important;
                border-bottom-left-radius: 0 !important;
                position: relative;
                left: -1px;
            }
        }

        .verify-tip-inner {
            max-width: unset !important;
        }
    }
  </style>
