<template>
    <div class="tab-box">
        <a-loading v-if="loading"></a-loading>
        <div>
            <obs-table
                :height="240"
                showTitle
                :tableData="tableData"
                :columns="columns"
                highlightRow
                rowSelectOnly
                border
                @on-current-change="handleCurrentChange">
                <template v-slot:extraTitle>
                    <obs-title :title="tableTitle" class="table-title">
                        <template v-slot:extraTitleBox>
                            <span class="title-slots">
                                <h-select v-model="selectGroup.selectCluster" :clearable="false" setDefSelect filterable
                                    style="width: 150px;" @on-change="handleSelectChange($event, 'selectCluster')">
                                    <h-option v-for="item in consumeClusterList" :key="item.value"
                                        :value="item.value">{{item.label}}</h-option>
                                </h-select>
                                <h-select v-if="selectGroup.selectCluster && selectGroup.selectCluster !== 'allCluster'"
                                    v-model="selectGroup.selectTopic" :clearable="false" setDefSelect filterable
                                    style="width: 150px;" @on-change="handleSelectChange($event, 'selectTopic')">
                                    <h-option v-for="item in topicList" :key="item.value"
                                        :value="item.value">{{item.label}}</h-option>
                                </h-select>
                                <h-select v-model="selectGroup.selectRule" :clearable="false" setDefSelect
                                    style="width: 200px;" @on-change="handleSelectChange($event, 'selectRule')">
                                    <h-option v-for="item in sortRuleList" :key="item.value"
                                        :value="item.value">{{item.label}}</h-option>
                                </h-select>
                                <h-select v-model="selectGroup.selectTop" :clearable="false" setDefSelect
                                    style="width: 100px;" @on-change="handleSelectChange($event, 'selectTop')">
                                    <h-option v-for="item in topList" :key="item.value"
                                        :value="item.value">{{item.label}}</h-option>
                                </h-select>
                            </span>
                        </template>
                    </obs-title>
                </template>
            </obs-table>

            <!-- 消息处理变化趋势-消息积压数 -->
            <chart
                v-show="selectTableRow.GroupName"
                ref="backlogChart"
                :title="backlogChart.title"
                :basicOpiton="backlogChart.basicOpiton"
                :chartData="backlogChart.chartData"
                style="height: 300px;"
                @select-change="handleSwitchChange">
            </chart>

            <!-- 消息处理变化趋势-客户端耗时 -->
            <chart
                v-show="selectTableRow.GroupName"
                ref="timeChart"
                :title="timeChart.title"
                :basicOpiton="timeChart.basicOpiton"
                :chartData="timeChart.chartData"
                style="height: 300px;"
                class="chart-title"
                @select-change="chartSelectChange">
            </chart>
        </div>
    </div>
</template>

<script>
import _ from 'lodash';
import chart from '@/components/common/chart/chart';
import obsTitle from '@/components/common/title/obsTitle';
import obsTable from '@/components/common/obsTable/obsTable';
import aLoading from '@/components/common/loading/aLoading';
import { chartConvertTime, autoConvertTimeRender, formatChartNumber } from '@/utils/utils';
import { getManagerProxy, getMcMetricsTrend } from '@/api/mcApi';
import { defaultConsumeCluster, defaultTopicList, metricEnum, sortRuleList, topList, defaultTrend } from './constant';

export default {
    name: 'ConsumeBacklog',
    components: { aLoading, chart, obsTable, obsTitle },
    props: {
        nodeData: {
            type: Object,
            default: () => {}
        },
        productId: {
            type: String,
            default: ''
        },
        timerInterval: {
            type: Number,
            default: 1
        }
    },
    data() {
        return {
            isFirstRender: true,
            loading: false,
            tableTitle: {
                label: '消费者集群消息处理'
            },
            // 下拉框
            selectGroup: {
                selectCluster: 'allCluster',
                selectTopic: 'allTopic',
                selectRule: 'TotalMsgInQueCount',
                selectTop: 6
            },
            consumeClusterList: defaultConsumeCluster,
            topicList: defaultTopicList,
            sortRuleList: sortRuleList,
            topList: topList,

            columns: [
                {
                    title: '消费者集群',
                    key: 'GroupName',
                    ellipsis: true,
                    minWidth: 110
                },
                {
                    title: '实例名',
                    key: 'InstanceName',
                    ellipsis: true
                },
                {
                    title: '主题名',
                    key: 'TopicName',
                    ellipsis: true
                },
                {
                    title: '分区号',
                    key: 'PartitionNo',
                    ellipsis: true
                },
                {
                    title: '消息积压数',
                    key: 'TotalMsgInQueCount',
                    ellipsis: true,
                    minWidth: 110
                },
                {
                    title: '消费消息序号',
                    key: 'ConsumeMsgId',
                    ellipsis: true,
                    minWidth: 120
                },
                {
                    title: '最新消息序号',
                    key: 'LatestMsgId',
                    ellipsis: true,
                    minWidth: 120
                },
                {
                    title: '调用次数',
                    key: 'TotalCount',
                    ellipsis: true,
                    minWidth: 100
                },
                {
                    title: '当日客户端平均处理耗时',
                    key: 'AvgTotalTimeUs',
                    ellipsis: true,
                    minWidth: 190,
                    render: (h, params) => {
                        return autoConvertTimeRender(h, params, 'AvgTotalTimeUs', 'μs');
                    }
                },
                {
                    title: '当日客户端平均排队耗时',
                    key: 'AveQueueTimeUs',
                    ellipsis: true,
                    minWidth: 190,
                    render: (h, params) => {
                        return autoConvertTimeRender(h, params, 'AveQueueTimeUs', 'μs');
                    }
                },
                {
                    title: '当日客户端平均执行耗时',
                    key: 'AvgExecTimeUs',
                    ellipsis: true,
                    minWidth: 190,
                    render: (h, params) => {
                        return autoConvertTimeRender(h, params, 'AvgExecTimeUs', 'μs');
                    }
                },
                {
                    title: '当日客户端最大处理耗时',
                    key: 'MaxTotalTimeUs',
                    ellipsis: true,
                    minWidth: 190,
                    render: (h, params) => {
                        return autoConvertTimeRender(h, params, 'MaxTotalTimeUs', 'μs');
                    }
                },
                {
                    title: '当日客户端最大排队耗时',
                    key: 'MaxQueueTimeUs',
                    ellipsis: true,
                    minWidth: 190,
                    render: (h, params) => {
                        return autoConvertTimeRender(h, params, 'MaxQueueTimeUs', 'μs');
                    }
                },
                {
                    title: '当日客户端最大执行耗时',
                    key: 'MaxExecTimeUs',
                    ellipsis: true,
                    minWidth: 190,
                    render: (h, params) => {
                        return autoConvertTimeRender(h, params, 'MaxExecTimeUs', 'μs');
                    }
                },
                {
                    title: '当日客户端最小处理耗时',
                    key: 'MinTotalTimeUs',
                    ellipsis: true,
                    minWidth: 190,
                    render: (h, params) => {
                        return autoConvertTimeRender(h, params, 'MinTotalTimeUs', 'μs');
                    }
                },
                {
                    title: '当日客户端最小排队耗时',
                    key: 'MinQueueTimeUs',
                    ellipsis: true,
                    minWidth: 190,
                    render: (h, params) => {
                        return autoConvertTimeRender(h, params, 'MinQueueTimeUs', 'μs');
                    }
                },
                {
                    title: '当日客户端最小执行耗时',
                    key: 'MinExecTimeUs',
                    ellipsis: true,
                    minWidth: 190,
                    render: (h, params) => {
                        return autoConvertTimeRender(h, params, 'MinExecTimeUs', 'μs');
                    }
                }
            ],
            tableData: [],
            selectTableRow: {},
            wholeData: [],

            backlogChart: {
                title: {
                    label: '消息处理变化趋势',
                    slots: [
                        {
                            type: 'select',
                            key: 'trendSourceSelect',
                            defaultValue: 'front',
                            options: [
                                {
                                    value: 'front',
                                    label: '最新15分钟'
                                },
                                {
                                    value: 'backend',
                                    label: '历史15分钟'
                                }
                            ]
                        }
                    ]
                },
                basicOpiton: {
                    chartType: 'axis',
                    lineDeploy: [
                        {
                            name: '消息积压数',
                            type: 'line',
                            unit: '个'
                        }
                    ],
                    yAxiDeploy: [
                        {
                            name: '数量(个)',
                            axisLabel: {
                                formatter: formatChartNumber
                            }
                        }
                    ]
                },
                chartData: {
                    xData: [],
                    data: {
                        消息积压数: []
                    }
                }
            },
            timeChart: {
                title: {
                    label: '',
                    slots: [
                        {
                            type: 'select',
                            key: 'statistics',
                            defaultValue: 'avg',
                            options: [
                                {
                                    value: 'avg',
                                    label: '平均'
                                },
                                {
                                    value: 'max',
                                    label: '最大'
                                },
                                {
                                    value: 'min',
                                    label: '最小'
                                }
                            ]
                        }
                    ]
                },
                basicOpiton: {
                    chartType: 'axis',
                    lineDeploy: [
                        {
                            name: '当日客户端处理耗时',
                            type: 'line',
                            tooltip: {
                                valueFormatter: (val) => {
                                    return chartConvertTime(val, 'μs');
                                }
                            }
                        },
                        {
                            name: '当日客户端排队耗时',
                            type: 'line',
                            tooltip: {
                                valueFormatter: (val) => {
                                    return chartConvertTime(val, 'μs');
                                }
                            }
                        },
                        {
                            name: '当日客户端执行耗时',
                            type: 'line',
                            tooltip: {
                                valueFormatter: (val) => {
                                    return chartConvertTime(val, 'μs');
                                }
                            }
                        }
                    ],
                    yAxiDeploy: [
                        {
                            name: '耗时',
                            axisLabel: {
                                formatter: (val) => {
                                    return chartConvertTime(val, 'μs');
                                }
                            }

                        }
                    ]
                },
                additionalOpiton: {
                },
                chartData: {
                    xData: [],
                    data: {
                        当日客户端处理耗时: [],
                        当日客户端排队耗时: [],
                        当日客户端执行耗时: []
                    }
                }
            },
            selectStatistics: 'avg',

            trendSource: '', // "后端获取"或者"前端保存"趋势图数据 front / backend
            trendObj: {}
        };
    },
    methods: {
        initData() {
            this.loading = true;
            this.cleanData();
            // 页面数据更新-定时器轮询
            this.$nextTick(async () => {
                await this.getFileData();
                this.loading = false;
                this.isFirstRender = false;
            });
        },
        // 清理数据
        cleanData() {
            this.consumeClusterList = defaultConsumeCluster;
            this.topicList = defaultTopicList;
            this.selectGroup.selectCluster = 'allCluster';
            this.selectGroup.selectTopic = 'allTopic';
            this.selectGroup.selectRule = 'TotalMsgInQueCount';
            this.selectGroup.selectTop = 6;
            this.$refs['backlogChart'].setSelectVal('trendSourceSelect', 'front');
            this.$refs['timeChart'].setSelectVal('statistics', 'avg');
            this.selectTableRow = {};
        },
        // 表格下拉框组改变
        handleSelectChange(value, key) {
            this.selectGroup[key] = value;
            // 切换消费者集群默认选择展示全部主题
            if (key === 'selectCluster') {
                this.selectGroup.selectTopic = 'allTopic';
            }
            this.hanldeTableView();
        },
        // 表格选中行改变
        async handleCurrentChange(row) {
            this.selectTableRow = row || {};

            this.trendObj = _.cloneDeep(defaultTrend); // 前端缓存趋势图数据
            const newTime = this.$getCurrentLocalTime();
            this.handleXaxisData(newTime);
            this.handleTrendStorage(newTime);

            await this.handleHistoryChart();
            this.backlogChart.title.label = `消息处理变化趋势 - ${row?.GroupName} | ${row?.InstanceName} | ${row?.TopicName} | ${row?.PartitionNo}`;
        },
        // 折线图下拉切换（平均、最大、最小）统计
        chartSelectChange(val) {
            this.selectStatistics = val;
            if (!this.isFirstRender) {
                this.handleHistoryChart();
            }
        },
        handleSwitchChange(val) {
            this.trendSource = val;
            if (val === 'front') {
                this.trendObj = _.cloneDeep(defaultTrend);
                const newTime = this.$getCurrentLocalTime();
                this.handleXaxisData(newTime);
                this.handleTrendStorage(newTime);
            }
            this.handleHistoryChart();
        },
        // 更新页面数据
        async getFileData() {
            try {
                const newTime = this.$getCurrentLocalTime();
                this.handleXaxisData(newTime);
                await this.handlePageData();
                if (Object.keys(this.selectTableRow)?.length) {
                    if (this.trendSource === 'front') {
                        this.handleTrendStorage(newTime); // 前端缓存趋势图数据
                    }
                    await this.handleHistoryChart();
                }
            } catch (err) {
                this.$emit('clear');
            }
        },
        // 处理消费积压数据
        async handlePageData() {
            const backlogList = await this.getMcAPi();
            const consumeArr = [];

            Array.isArray(backlogList) && backlogList.forEach(item => {
                // 下拉消费者集群
                if (!consumeArr.some(e => e.value === item.GroupName)) {
                    consumeArr.push({
                        value: item.GroupName,
                        label: item.GroupName
                    });
                }
            });
            this.consumeClusterList = [...defaultConsumeCluster, ...consumeArr];

            // 保存完整数据
            this.wholeData = [...backlogList];

            this.hanldeTableView();
        },

        // 表格数据展示
        hanldeTableView() {
            const { selectCluster, selectTopic, selectRule, selectTop } = this.selectGroup;
            const backlogList = this.wholeData;
            const topicArr = [];
            let filterBacklogList = [];

            // 下拉主题列表
            backlogList.forEach(item => {
                if (!topicArr.some(e => e.value === item.TopicName) && item.GroupName === this.selectGroup.selectCluster) {
                    topicArr.push({
                        value: item.TopicName,
                        label: item.TopicName
                    });
                }
            });
            this.topicList = [...defaultTopicList, ...topicArr];

            // 表格过滤
            if (selectCluster === 'allCluster') {
                filterBacklogList = backlogList;
            } else if (selectCluster !== 'allCluster' && selectTopic === 'allTopic') {
                filterBacklogList = backlogList.filter(o => (o?.GroupName === selectCluster));
            } else if (selectCluster !== 'allCluster' && selectTopic !== 'allTopic') {
                filterBacklogList = backlogList.filter(o => (o?.GroupName === selectCluster) && (o.TopicName === selectTopic));
            }

            // 表格排序 - topN
            const sortedBacklogList = _.orderBy(filterBacklogList || [], [selectRule], 'desc');
            const tableData = _.slice(sortedBacklogList, 0, selectTop);

            this.tableData = tableData.map(item => {
                return { ...item,
                    _highlight: (this.selectTableRow.GroupName === item.GroupName) &&
                    (this.selectTableRow.InstanceName === item.InstanceName) &&
                    (this.selectTableRow.TopicName === item.TopicName) &&
                    (this.selectTableRow.PartitionNo === item.PartitionNo)
                };
            });
        },
        // 获取管理功能接口参数
        getMcApiParams(funcName) {
            const params = [];
            const ipPortList = this?.nodeData?.productInstances;
            Array.isArray(ipPortList) && ipPortList.forEach(insInfo => {
                params.push({
                    manageProxyIp: insInfo?.ip,
                    manageProxyPort: insInfo?.manageProxyPort,
                    instanceName: insInfo?.instanceName,
                    pluginName: 'ldp_mc',
                    funcName: funcName
                });
            });
            return params;
        },
        // 接口请求 - GetConsumeOffset每个MC节点都访问取总的数据
        async getMcAPi() {
            let data = [];
            const param = this.getMcApiParams('GetConsumeOffset');
            try {
                const res = await getManagerProxy(JSON.stringify(param));
                if (res.code === '200') {
                    // 取多节点汇总数据
                    Array.isArray(res.data) && res.data.forEach(item => {
                        if (item?.Result?.length) {
                            data = data.concat(item.Result);
                        }
                    });
                }
            } catch (err) {
                this.$emit('clear');
            }
            return data;
        },

        // 处理展示历史15分钟折线图数据
        async handleHistoryChart() {
            let trandObj = {};
            if (this.trendSource === 'backend') {
                const trendData = await this.getMcMetricsTrend();
                if (this.trendSource !== 'backend') return;
                trandObj['xaxis'] = trendData?.[0]?.trendChart?.xaxis || [];
                trendData.forEach(item => {
                    trandObj[item.metricName] = item?.trendChart?.yaxis || [];
                });
            } else {
                trandObj = this.trendObj;
            }

            // 消息积压数趋势
            this.backlogChart.chartData.xData = trandObj?.xaxis;
            this.backlogChart.chartData.data = {
                消息积压数: trandObj?.['mc_subscriber_message_backlog_quantity']
            };

            // 客户端耗时趋势
            this.timeChart.chartData.xData = trandObj?.xaxis;
            this.timeChart.chartData.data = {
                当日客户端处理耗时: trandObj?.['mc_subscriber_handle_latency_' + this.selectStatistics],
                当日客户端排队耗时: trandObj?.['mc_subscriber_queue_latency_' + this.selectStatistics],
                当日客户端执行耗时: trandObj?.['mc_subscriber_exec_latency_' + this.selectStatistics]
            };
        },
        // 获取MC消费者集群消息处理变化趋势
        async getMcMetricsTrend() {
            // 获取指标list
            const metricNames = this.getMetricNames();
            let trendData = [];
            const params = {
                productId: this.productId,
                appClusterId: this.nodeData?.id,
                metricNames: metricNames,
                timeWindow: 900,
                groupName: this.selectTableRow?.GroupName,
                instanceName: this.selectTableRow?.InstanceName,
                topicName: this.selectTableRow?.TopicName,
                partitionNo: this.selectTableRow?.PartitionNo
            };
            try {
                const res = await getMcMetricsTrend(params);
                if (res.code === '200') {
                    trendData = res.data || [];
                } else if (res.code?.length === 8) {
                    this.$hMessage.error(res.message);
                    this.$emit('clear');
                }
            } catch (err) {
                this.$emit('clear');
                console.error(err);
            }
            return trendData;
        },
        getMetricNames() {
            let list = [];
            const backloglist = ['mc_subscriber_message_backlog_quantity'];
            const clientTimeList = ['mc_subscriber_handle_latency', 'mc_subscriber_exec_latency', 'mc_subscriber_queue_latency'].map(item => {
                return `${item}_${this.selectStatistics}`;
            });
            list = [...backloglist, ...clientTimeList];
            return list;
        },
        handleXaxisData(newTime) {
            // 超过15分钟删除
            if (this.trendObj?.['xaxis']?.length > (15 * 60 / this.timerInterval)) {
                Object.values(this.trendObj).forEach(item => {
                    item.shift();
                });
            }
            if (!this.trendObj?.['xaxis']?.includes(newTime)) {
                this.trendObj['xaxis'].push(newTime);
            }
        },
        // 前端存储趋势图数据
        handleTrendStorage(newTime) {
            const selectObj = _.find(this.wholeData, (item) => {
                return item.GroupName === this.selectTableRow.GroupName &&
                    item.InstanceName === this.selectTableRow.InstanceName &&
                    item.TopicName === this.selectTableRow.TopicName &&
                    item.PartitionNo === this.selectTableRow.PartitionNo;
            });
            const index = this.trendObj['xaxis'].lastIndexOf(newTime);
            Object.keys(this.trendObj).forEach(key => {
                const metricKey = metricEnum[key];
                if (key !== 'xaxis') {
                    this.$set(this.trendObj[key], index, selectObj?.[metricKey] ?? '-');
                }
            });
        }
    }
};
</script>

<style lang="less" scoped>
.tab-box {
    height: calc(100% - 16px);

    /deep/ .obs-table {
        margin: 5px 0 15px;

        .h-table td:hover {
            cursor: pointer;
        }
    }

    .table-title {
        position: relative;

        .title-slots {
            position: absolute;
            right: 15px;
        }
    }

    .chart-title {
        /deep/ .obs-title::before {
            width: 0;
        }
    }
}
</style>
