<template>
  <div class="i18n-test-page">
    <div class="test-section">
      <h2>{{ $t('page.sqlCores') }}</h2>

      <div class="button-group">
        <h-button type="primary">{{ $t('common.query') }}</h-button>
        <h-button type="warning">{{ $t('common.edit') }}</h-button>
        <h-button type="error">{{ $t('common.delete') }}</h-button>
      </div>

      <div class="form-section">
        <h-form :label-width="120">
          <h-form-item :label="$t('common.tableName')">
            <h-input />
          </h-form-item>
          <h-form-item :label="$t('common.chineseName')">
            <h-input :placeholder="$t('common.placeholder.input')" />
          </h-form-item>
          <h-form-item :label="$t('common.status')">
            <h-select :placeholder="$t('common.placeholder.select')">
              <h-option value="running" :label="$t('common.running')" />
              <h-option value="stopped" :label="$t('common.stopped')" />
              <h-option value="failed" :label="$t('common.failed')" />
            </h-select>
          </h-form-item>
        </h-form>
      </div>

      <div class="table-section">
        <h-table :columns="columns" :data="tableData">
        </h-table>
      </div>

      <div class="validation-section">
        <h3>{{ $t('validation.required') }}</h3>
        <p>{{ $t('formValidation.invalidEmail') }}</p>
        <p>{{ $t('formValidation.onlyFloat') }}</p>
      </div>

      <div class="no-data-section">
        <no-data />
      </div>
    </div>
  </div>
</template>

<script>
import noData from '@/components/common/noData/noData';

export default {
    name: 'I18nTest',
    components: {
        noData
    },
    data() {
        return {
            tableData: [
                {
                    tableName: 'user_info',
                    chineseName: '用户信息表',
                    status: 'running',
                    node: 'node-001'
                },
                {
                    tableName: 'order_detail',
                    chineseName: '订单详情表',
                    status: 'stopped',
                    node: 'node-002'
                }
            ]
        };
    },
    computed: {
        columns() {
            return [
                {
                    title: this.$t('common.tableName'),
                    key: 'tableName',
                    width: 150
                },
                {
                    title: this.$t('common.chineseName'),
                    key: 'chineseName',
                    width: 150
                },
                {
                    title: this.$t('common.status'),
                    key: 'status',
                    width: 100,
                    render: (h, params) => {
                        const statusMap = {
                            running: this.$t('common.running'),
                            stopped: this.$t('common.stopped'),
                            failed: this.$t('common.failed')
                        };
                        return h('span', statusMap[params.row.status] || params.row.status);
                    }
                },
                {
                    title: this.$t('common.node'),
                    key: 'node',
                    width: 100
                },
                {
                    title: this.$t('common.operation'),
                    key: 'operation',
                    width: 200,
                    render: (h, params) => {
                        return h('div', [
                            h('h-button', {
                                props: {
                                    type: 'text',
                                    size: 'small'
                                },
                                style: {
                                    marginRight: '8px'
                                }
                            }, this.$t('common.detail')),
                            h('h-button', {
                                props: {
                                    type: 'text',
                                    size: 'small'
                                },
                                style: {
                                    marginRight: '8px'
                                }
                            }, this.$t('common.edit')),
                            h('h-button', {
                                props: {
                                    type: 'text',
                                    size: 'small'
                                }
                            }, this.$t('common.delete'))
                        ]);
                    }
                }
            ];
        }
    }
};
</script>

<style lang="less" scoped>
.i18n-test-page {
    padding: 20px;

    .test-section {
        max-width: 1200px;
        margin: 0 auto;

        h2 {
            margin-bottom: 20px;
            color: #333;
        }

        .button-group {
            margin-bottom: 20px;

            .h-btn {
                margin-right: 10px;
            }
        }

        .form-section {
            margin-bottom: 20px;
            padding: 20px;
            border: 1px solid #e8e8e8;
            border-radius: 4px;
        }

        .table-section {
            margin-bottom: 20px;
        }

        .message-section {
            margin-bottom: 20px;

            .h-alert {
                margin-bottom: 10px;
            }
        }

        .validation-section {
            margin-bottom: 20px;
            padding: 20px;
            background-color: #f5f5f5;
            border-radius: 4px;

            h3 {
                margin-bottom: 10px;
            }

            p {
                margin-bottom: 5px;
                color: #666;
            }
        }

        .no-data-section {
            text-align: center;
            padding: 40px;
            border: 1px dashed #d9d9d9;
            border-radius: 4px;
        }
    }
}
</style>
