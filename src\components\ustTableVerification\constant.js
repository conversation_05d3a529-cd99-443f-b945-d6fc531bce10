// 校验类型
export const VERIFE_TYPE_LIST = [
    {
        value: 'tableRecordCount',
        label: '表总量'
    },
    {
        value: 'tableField',
        label: '表字段'
    }
];

// 校验结果
export const VERIFE_RESULT_LIST = [
    {
        value: 'running',
        label: '校验中',
        icon: 'loading'
    },
    {
        value: 'succeeded',
        label: '通过',
        icon: 'success'
    },
    {
        value: 'exception',
        label: '程序异常',
        icon: 'warn'
    },
    {
        value: 'reachingThreshold',
        label: '达到阈值',
        icon: 'error'
    },
    {
        value: 'notPass',
        label: '不通过',
        icon: 'error'
    },
    {
        value: 'proactivelyStop',
        label: '主动停止',
        icon: 'error'
    }
];

// 多行表头配置
export const MULTI_LEVEL = [
    [
        {
            title: '',
            cols: 1,
            hiddenCol: false,
            align: 'center',
            rows: 2,
            type: 'selection'
        },
        {
            title: '任务名称',
            cols: 1,
            hiddenCol: false,
            rows: 2
        },
        {
            title: '源数据库',
            cols: 1,
            hiddenCol: false,
            rows: 2
        },
        {
            title: '目标数据库',
            cols: 1,
            hiddenCol: false,
            rows: 2
        },
        {
            title: '校验类型',
            cols: 1,
            hiddenCol: false,
            rows: 2
        },
        {
            title: '错误阈值',
            cols: 1,
            hiddenCol: false,
            rows: 2
        },
        {
            title: '操作',
            cols: 1,
            hiddenCol: false,
            className: 'muti-border-right',
            rows: 2
        },
        {
            title: '最近执行情况',
            cols: 4,
            align: 'center',
            className: 'muti-border-bottom',
            hiddenCol: false
        }
    ],
    [
        {
            title: '执行开始时间',
            cols: 1,
            hiddenCol: false
        },
        {
            title: '当前执行进度',
            cols: 1,
            hiddenCol: false
        },
        {
            title: '执行耗时',
            cols: 1,
            hiddenCol: false
        },
        {
            title: '校验结果',
            cols: 1,
            hiddenCol: false
        }
    ]
];

// 创建校验任务流程提示
export const CREATE_TASK_TIPS = {
    0: {
        title: '确定校验对象 - 选择【源数据库】与【目标数据库】。',
        content: '根据实际需要，可选择指定核心集群下的源数据库（默认）与目标数据库'
    },
    1: {
        title: '确定校验内容 - 选择校验数据',
        content: `明确校验类型「按表总量」或「按表字段」。基于上一步骤确定的数据库，根据类型和目标选择校验数据对象。
                按表总量：选择指定表进行比对；按表字段：可输入SQL，按SQL执行的结果对比每一行。`
    },
    2: {
        title: '信息核对',
        content: '基于前两个步骤的选择，将对应产生的任务数与任务对应的校验内容汇总。请仔细核对，无误后点击“创建数据校验任务”即可。'
    }
};

// 创建校验任务确认表格表头
export const CONFIRM_TABLE_COLUMNS = [
    {
        type: 'index',
        title: '序号',
        width: 60,
        align: 'center'
    },
    {
        title: '源数据库',
        key: 'sourceDatabaseName',
        width: 120,
        ellipsis: true
    },
    {
        title: '目标数据库',
        key: 'targetDatabasesName',
        width: 120,
        ellipsis: true
    },
    {
        title: '校验类型',
        key: 'compareType',
        width: 100,
        ellipsis: true
    },
    {
        title: '校验内容',
        key: 'compareContent',
        ellipsis: true
    }
];

export const SQL_TIP =
    `1.只允许输入一条select语句，支持最大输入长度500个字符；
    2.系统将以英文字符';'视为SQL输入结束的标识符，如果SQL语句中无';'则视整个用户输入为一个完整SQL；
    3.系统“不支持”对输入SQL进行语法正确性校验，请用户自行保证SQL语法正确；
    4.表名需要统一添加“View”后缀；
    下载“SQL语法指南”了解更多。`;
