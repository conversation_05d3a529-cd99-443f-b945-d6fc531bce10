<!-- 测试连通性弹窗 -->
<template>
    <div class="connect">
        <h-msg-box
            v-model="modalData.status"
            title="连通性测试"
            :mask-closable="false"
            width="60"
            maxHeight="350"
        >
            <div class="connect-table">
                <h-table
                    height="300"
                    loadingText="测试中"
                    :loading="isTesting"
                    :columns="columns"
                    :data="tableData"
                />
            </div>

            <template v-slot:footer>
                <h-button @click="handleCancel">关闭</h-button>
            </template>
        </h-msg-box>
    </div>
</template>

<script>
import { testHostsConnection } from '@/api/productApi';
export default {
    name: 'ServerTestConnectModal',
    props: {
        modalInfo: {
            type: Object,
            default: () => ({})
        },
        productId: {
            type: String,
            default: ''
        }
    },
    data() {
        return {
            modalData: this.modalInfo,
            loading: false,
            isTesting: false,
            tableData: [],
            columns: [
                {
                    title: '主机名',
                    key: 'hostName',
                    minWidth: 130
                },
                {
                    title: 'IP地址/域名',
                    key: 'ips',
                    minWidth: 200,
                    render: (h, { row }) => {
                        const ips = row.ips ? row.ips?.join(',') : '-';
                        return h('div', ips);
                    }
                },
                {
                    title: 'SSH用户名',
                    key: 'loginUser',
                    minWidth: 130,
                    render: (h, { row }) => {
                        const loginUser = row.loginUser || '-';
                        return h('div', loginUser);
                    }
                },
                {
                    title: '连通性测试结果',
                    key: 'connectionStatus',
                    minWidth: 130,
                    render: (h, { row }) => {
                        return h('span', {
                            style: {
                                color: row?.connectionStatus === 'SUCCESS' ? 'green' : 'red'
                            }
                        },
                        row?.connectionStatus === 'SUCCESS' ? '成功' : `失败`
                        );
                    }
                }
            ]
        };
    },
    mounted() {
        this.testHostsConnection();
    },
    methods: {
        /**
         * 开始连通性检测
         */
        async testHostsConnection() {
            try {
                this.isTesting = true;
                const params = {
                    productId: this.productId,
                    ids: this.modalData.ids
                };
                const res = await testHostsConnection(params);
                if (res.code === '200') {
                    this.tableData = res.data || [];
                } else if (res.code?.length === 8) {
                    this.$hMessage.error(`测试失败: ${res.message}`);
                }
            } finally {
                this.isTesting = false;
            }
        },
        handleCancel() {
            this.modalData.status = false;
        }
    }
};
</script>

