<template>
  <div ref="table-box" class="topo-config">
    <obs-title :title="title" class="obs-title-1"></obs-title>
    <obs-table
        v-if="supportFeature"
        ref="table2"
        :key="tkey"
        :height="tableHeight"
        :title="tableTitle"
        :tableData="tableData"
        showTitle
        :columns="columns"
        :hasPage="false"
        :loading="tableLoading"
    />
    <no-data v-else text="不支持该特性" />
  </div>
</template>

<script>
import _ from 'lodash';
import obsTitle from '@/components/common/title/obsTitle';
import obsTable from '@/components/common/obsTable/obsTable';
import noData from '@/components/common/noData/noData';
import {
    getProductFeatures,
    setProductLatencyTraceModels,
    getProductLatencyTraceModels
} from '@/api/productApi';

export default {
    props: {
        productInfo: {
            type: Object,
            default: {}
        }
    },
    components: { obsTitle, obsTable, noData },
    data() {
        return {
            tableLoading: false,
            tkey: 1,
            title: {
                label: {
                    labelDic: [
                        {
                            key: 'supportFeature',
                            label: '是否支持该特性'
                        }
                    ],
                    labelInfo: {
                        supportFeature: '不支持'
                    }
                }
            },
            supportFeature: false,
            tableHeight: 0,
            tableTitle: {
                label: '已支持业务链路模型列表'
            },
            tableData: [],
            columns: [
                {
                    title: '业务类型',
                    key: 'bizType',
                    ellipsis: true,
                    render: (h, params) => {
                        const res = this.$store?.state?.apmDirDesc?.bizTypeDict?.[params.row?.bizType] || params.row?.bizType || '';
                        return h('span', {
                            attrs: {
                                title: res
                            }
                        }, res);
                    }
                },
                {
                    title: '业务链路度量模型',
                    key: 'enableTraceModelId',
                    ellipsis: true,
                    render: (h, params) => {
                        const oldVal = params.row.enableTraceModelId;

                        return h('h-select', {
                            props: {
                                value: params.row?.enableTraceModelId,
                                placeholder: '选择模型',
                                autoPlacement: true,
                                transfer: true
                            },
                            on: {
                                'on-change': async (val) => {
                                    if (oldVal === val) return;
                                    // 弹窗提示
                                    this.handleModelChange(params.row, val);
                                }
                            }
                        }, [
                            h('h-option', { attrs: { value: '', key: '选择模型' } }, '选择模型'),
                            ...params.row?.traceModels.map((item) => {
                                return h('h-option', { attrs: { value: item.traceModelId, key: item.traceModelId } }, item.traceModelName);
                            })
                        ]);
                    }
                },
                {
                    title: '业务系统类型',
                    key: 'bizSysType',
                    ellipsis: true,
                    render: (h, params) => {
                        const bizSysTypes = _.find(params.row?.traceModels, ['traceModelId', params.row?.enableTraceModelId])?.bizSysTypes || [];
                        const res = bizSysTypes.map(o => {
                            return this.$store?.state?.apmDirDesc?.bizSysTypeDict?.[o] || o || '';
                        });
                        return h('span', {
                            attrs: {
                                title: res?.join(',') || '-'
                            }
                        }, res?.join(',') || '-');
                    }
                },
                {
                    title: '模型预览',
                    key: 'action',
                    fixed: 'right',
                    width: 100,
                    render: (h, params) => {
                        return h('div', [
                            h(
                                'Button',
                                {
                                    props: {
                                        size: 'small',
                                        type: 'text',
                                        disabled: !params.row?.enableTraceModelId
                                    },
                                    on: {
                                        click: () => {
                                            this.getPreviewTopoData(params.row?.enableTraceModelId);
                                        }
                                    }
                                },
                                '查看'
                            )
                        ]);
                    }
                }
            ]
        };
    },
    mounted() {
        window.addEventListener('resize', this.fetTableHeight);
    },
    beforeDestroy() {
        window.removeEventListener('resize', this.fetTableHeight);
    },
    computed: {},
    methods: {
    // 设置table高度
        fetTableHeight() {
            this.tableHeight = this.$refs['table-box']?.offsetHeight - 60;
        },
        // 初始化页面
        async initData() {
            this.clearData();
            try {
                this.tableLoading = true;
                await this.getProductFeatures();
                this.supportFeature && (await this.queryTableData());
            } catch (err) {
                console.error(err);
            } finally {
                this.tableLoading = false;
            }
            this.fetTableHeight();
        },
        // 清理数据
        clearData() {
            this.supportFeature = false;
            this.tableData = [];
        },
        // 获取产品特性
        async getProductFeatures() {
            const param = { productId: this.productInfo.id };
            const res = await getProductFeatures(param);
            if (this.productInfo.id !== param?.productId) return;
            this.supportFeature = Boolean(res?.data?.latencyMeasurement);
            this.title.label.labelInfo.supportFeature = this.supportFeature
                ? '支持'
                : '不支持';
        },
        // 获取表格数据
        async queryTableData() {
            await this.getProductLatencyTraceModels();
        },
        // 查询产品时延链路模型配置
        async getProductLatencyTraceModels() {
            const param = { productId: this.productInfo.id };
            const res = await getProductLatencyTraceModels(param);
            if (res?.code === '200') {
                this.tableData = res?.data || [];
            } else {
                this.tableData = [];
            }
        },
        /**
         * 切换链路模型确认弹窗
         */
        handleModelChange(row, traceModelId) {
            this.$hMsgBoxSafe.confirm({
                title: `确定要切换度量模型？`,
                content: `切换度量模型，本产品节点下对应的「链路日志配置」将自动同步更新。本次变动需要重启采集子系统才可生效。`,
                onOk: async () => {
                    // 调用接口
                    await this.setProductLatencyTraceModels(row?.bizType, traceModelId);
                },
                onCancel: () => {
                    // 下拉框切换回原值：直接重新渲染表格即可，因为表格对应值此时不会被触发更新
                    this.tkey++;
                }
            });
        },
        // 设置产品时延链路模型
        async setProductLatencyTraceModels(bizType, traceModelId) {
            const param = { productId: this.productInfo.id, bizType, traceModelId };
            const res = await setProductLatencyTraceModels(param);
            if (res?.code === '200') {
                this.$hMessage.success('模型配置成功');
                this.queryTableData();
            } else {
                this.$hMessage.error('切换模型失败');
            }
        },
        // tab跳转
        getPreviewTopoData(modelId) {
            this.$emit('tab-jump', { modelId });
        }
    }
};
</script>

<style lang="less" scoped>
.topo-config {
    position: relative;
    width: 100%;
    height: calc(100% - 70px);

    .obs-title-1 {
        background: var(--wrapper-color);
        margin-top: 10px;
    }
}
</style>
