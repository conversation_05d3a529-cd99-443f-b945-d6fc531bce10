<template>
  <div class="tab-box" :style="[{background: data.isWhiteBg ? 'var(--font-color)' : 'var(--wrapper-color)'}]">
    <obs-title
      v-if="data.title"
      ref="obs-title"
      :title="data.title"
      @button-click="handleButtonClick"
      @select-change="handleSelectChange"
      @input-change="handleInputChange"
      @modal-click="handleModalClick"
    >
      <!-- 标题插槽 -->
      <template #extraTitleBox>
        <slot name="extraTitleBox"></slot>
      </template>
    </obs-title>
      <!-- 自定义组件 -->
    <slot name="customBox"></slot>
    <grid-layout
      :layout.sync="data.layout"
      :col-num="12"
      :row-height="10"
      :is-draggable="false"
      :is-resizable="false"
      :is-mirrored="false"
      :vertical-compact="true"
      :margin="[10, 10]"
      :use-css-transforms="true"
    >
      <div class="grid-item-box">
        <grid-item
          v-for="(item, idx) in data.layout"
          :key="item.i"
          :x="item.x"
          :y="item.y"
          :w="item.w"
          :h="item.h"
          :i="item.i"
        >
          <info-bar
            ref="info-bar"
            v-bind="data.details[idx]"
            :loading="loading"
            :selectInfoId="selectInfoId"
            :style="[data.details[idx].canClick && data.details[idx].infoId === selectInfoId ? selectedStyle[selectedStyleType] :  '']"
            @select-change="handleSelectChange"
            @button-click="handleButtonClick"
            @on-current-change="handleTableRowChange"
            @pie-click="handlePieClick"
            @info-bar-click="handleBarClicK"
          ></info-bar>
        </grid-item>
      </div>
    </grid-layout>
  </div>
</template>

<script>
import infoBar from '@/components/common/infoBar/infoBar';
import obsTitle from '@/components/common/title/obsTitle';
import { GridLayout, GridItem } from 'vue-grid-layout';
export default {
    props: {
        gridData: {
            type: Object,
            default: () => {}
        },
        // 表格加载
        loading: {
            type: Boolean,
            default: false
        },
        // infoId必须全局唯一性！！！！
        selectInfoId: {
            type: String,
            default: ''
        },
        selectedStyleType: {
            type: String,
            default: 'background' // border  background
        }
    },
    components: { infoBar, GridLayout, GridItem, obsTitle },
    data() {
        return {
            data: this.gridData,
            selectedStyle: {
                border: { border: '2px solid var(--link-color)' },
                background: { backgroundColor: 'var(--poptip-bg-color)' }
            }
        };
    },
    methods: {
        handleBarClicK(id) {
            this.$emit('info-bar-click', id);
        },
        handleTableRowChange(val) {
            this.$emit('on-current-change', val);
        },
        handleButtonClick(key) {
            this.$emit('button-click', key);
        },
        handleSelectChange(val, key) {
            this.$emit('select-change', val, key);
        },
        handleInputChange(val, searchKey) {
            this.$emit('input-change', val, searchKey);
        },
        handlePieClick(key) {
            this.$emit('pie-click', key);
        },
        handleModalClick() {
            this.$emit('modal-click');
        },
        setSelectVal(key, val) {
            this.$refs['obs-title'].setSelectVal(key, val);
        },
        getSelectVal(key) {
            return this.$refs['obs-title'].getSelectVal(key);
        },
        setCheckVals(key, val) {
            this.$refs['obs-title'].setCheckVals(key, val);
        },
        getCheckVals(key) {
            return this.$refs['obs-title'].getCheckVals(key);
        }
    }
};
</script>

<style lang="less" scoped>
.tab-box {
    margin-top: 15px;

    .info-bar {
        margin: 0;
        height: 100%;
    }
}
</style>
