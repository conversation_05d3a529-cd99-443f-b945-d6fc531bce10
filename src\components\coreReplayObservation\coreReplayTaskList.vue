<template>
  <div class="query-table-box">
    <div class="title">
      <a-title title="核心数据重演">
        <template v-slot>
          <div v-show="productList.length > 1" class="product-select">
            <h-select
              v-model="productInstNo"
              placeholder="请选择"
              placement="bottom"
              :positionFixed="true"
              :clearable="false"
              @on-change="checkProduct"
            >
              <h-option
                v-for="item in productList"
                :key="item.id"
                :value="item.productInstNo"
              >
                {{ item.productName }}
              </h-option>
            </h-select>
          </div>
        </template>
      </a-title>
    </div>
    <div class="product-box">
      <query-table
        ref="query-table"
        :border="false"
        showTitle
        btnTitle="更多条件"
        :formItems="formItems"
        :formCols="3"
        :columns="columns"
        :tableData="tableData"
        :total="taskCount"
        :loading="loading"
        @query="handleQuery"
      >
        <template #tableTitle>
          <div class="table-title">重演任务列表</div>
          <div class="table-describe">
            任务总数:
            <span>{{ taskCount }}</span>
          </div>
        </template>
        <template #operateBlock>
          <!-- <a-button
            type="primary"
            style="margin-right: 5px;"
            :disabled="loading"
            @click="handleCreateTask"
            >创建
          </a-button> -->
          <a-button
            type="dark"
            style="margin-right: 5px;"
            :disabled="loading"
            @click="handleSyncTask"
            >同步任务
          </a-button>
          <span class="split-solid"></span>
          <h-select
            v-model="extraForm.execResult"
            placeholder="执行结果"
            style="width: 120px; margin-right: 5px;"
            :disabled="loading"
             @on-change="handleImdeQuery"
          >
            <h-option key="SUCCESS" value="SUCCESS">一致</h-option>
            <h-option key="FAILURE" value="FAILURE">不一致</h-option>
          </h-select>
          <h-date-picker
            v-model="extraForm.tradeDay"
            type="date"
            placement="bottom-end"
            placeholder="选择交易日"
            style="width: 120px; margin-right: 5px;"
            :disabled="loading"
             @on-change="handleImdeQuery"
          ></h-date-picker>
          <h-input
            v-model="extraForm.replayName"
            placeholder="输入任务名称"
            icon="search"
            style="width: 150px; margin-right: 5px;"
            clearable
            :maxLength="50"
            :disabled="loading"
            @on-blur="handleImdeQuery"
            @on-enter="handleImdeQuery"
             @on-click="handleImdeQuery"
          ></h-input>
        </template>
      </query-table>
    </div>
    <!-- 重演内容查看 -->
    <view-replay-context v-if="contextInfo.status" :modalInfo="contextInfo" />
    <!-- 修改 -->
    <modify-replay-modal v-if="editInfo.status" :modalInfo="editInfo" @edit="handleEditReplay" />
    <!-- 同步错误原因 -->
    <sysn-result-modal v-if="syscInfo.status" :modalInfo="syscInfo"></sysn-result-modal>
  </div>
</template>
<script>
import _ from 'lodash';
import {
    setReplayCreate,
    setReplaySync,
    setReplayStart,
    setReplayStop,
    setReplayPause,
    setReplayDelete,
    setReplayResume,
    getReplayList,
    getReplayStatusList,
    downloadInconsistencyfile
} from '@/api/coreReplayObservationApi';
import { formatDate, transferVal, getCurrentDatetime } from '@/utils/utils';
import { VERIFE_STATUS_LIST, VERIFE_RESULT_LIST } from './constant';
import aTitle from '@/components/common/title/aTitle';
import aButton from '@/components/common/button/aButton';
import queryTable from '@/components/common/bestTable/queryTable/queryTable';
import viewReplayContext from '@/components/coreReplayObservation/modal/viewReplayContext';
import modifyReplayModal from '@/components/coreReplayObservation/modal/modifyReplayModal';
import importStatusTableIcon from '@/components/secondAppearance/importStatusTableIcon.vue'; // 有用
import buttonGroup from './modal/buttonGroup'; // 有用
import sysnResultModal from '@/components/coreReplayObservation/modal/sysnResultModal';

export default {
    replayName: 'CoreReplayTaskList',
    components: {
        aTitle,
        queryTable,
        aButton,
        sysnResultModal,
        viewReplayContext,
        modifyReplayModal
    },
    props: {
        productList: {
            type: Array,
            default: []
        }
    },
    data() {
        const formatMethod = (val) => { return { value: transferVal(val) ? Number(val)?.toLocaleString() : '-' }; };
        const renderInconsistentCount = (h, params, key) => {
            const val = transferVal(params?.row?.[key]);
            if (val === '0') {
                return <span>0</span>;
            }
            if (val && Number(val) > 0) {
                return <span title={val}>{formatMethod(val)?.value}
                    <h-icon name={this.downloadingIds[params?.row?.id] ? 'load-c' : 't-b-download'} color="var(--link-color)" style="cursor: pointer" v-on:on-click={() => this.handleDownload(params?.row)} />
                </span>;
            }
            return <span>-</span>;
        };
        return {
            loading: false,
            productInstNo: '',
            dateList: [],
            extraForm: {
                execResult: '',
                tradeDay: '',
                sourceType: ''
            },
            formItems: [
                {
                    type: 'select',
                    key: 'sourceType',
                    label: '任务来源',
                    options: [
                        {
                            value: 'apm',
                            label: 'APM'
                        },
                        {
                            value: 'see',
                            label: 'SEE'
                        }
                    ],
                    value: ''
                },
                {
                    type: 'daterange',
                    key: 'time',
                    label: '执行开始时间',
                    value: [],
                    placement: 'bottom-end',
                    placeholder: '请选择时间范围'
                }
            ],
            columns: [
                {
                    title: '任务名称',
                    key: 'replayName',
                    minWidth: 140,
                    render: (h, params) => {
                        return h('div',
                            [
                                h(
                                    'Poptip',
                                    {
                                        class: 'apm-poptip',
                                        props: {
                                            trigger: 'hover',
                                            placement: 'top-start',
                                            positionFixed: true
                                        }
                                    },
                                    [
                                        h(
                                            'div',
                                            {
                                                attrs: {
                                                    class: 'h-table-cell-ellipsis',
                                                    title: params?.row?.replayName || params?.row?.replayId || '-'
                                                }
                                            },
                            params?.row?.replayName || params?.row?.replayId || '-'
                                        ),
                                        h(
                                            'div',
                                            {
                                                slot: 'content',
                                                class: 'pop-content'
                                            },
                                            '任务标识：' + (params?.row?.replayId || '-')
                                        )
                                    ]
                                )
                            ]);
                    }
                },
                {
                    title: '任务来源',
                    key: 'sourceType',
                    ellipsis: true,
                    formatMethod: (row) => (row?.sourceType || '-').toUpperCase()
                },
                {
                    title: '执行状态',
                    key: 'execStatus',
                    minWidth: 140,
                    render: (h, params) => {
                        const resultObj = _.find(VERIFE_STATUS_LIST, [
                            'value',
                            params?.row?.execStatus?.toLowerCase() || ''
                        ]);
                        const canClick = ['failed'].includes(resultObj?.value);
                        return (
                            <div>
                                <importStatusTableIcon type={resultObj?.icon || ''} />
                                <span title={resultObj?.label || '-'}>{resultObj?.label || '-'}</span>
                                {canClick ? (
                                    <h-poptip autoPlacement trigger="click" customTransferClassName="apm-poptip monitor-poptip">
                                        <span style="color: #2d8de5;margin: 0 5px;">错误原因</span>
                                        <div slot="content" class="pop-content" style="white-space: normal; max-width: 250px">
                                            {params?.row?.execErrorInfo || '-'}
                                        </div>
                                    </h-poptip>
                                ) : ''}
                            </div>
                        );
                    }
                },
                {
                    title: '执行开始时间',
                    key: 'execStartTime',
                    minWidth: 120,
                    ellipsis: true,
                    formatMethod: (row) => row.execStartTime || '-'
                },
                {
                    title: '执行结束时间',
                    key: 'execEndTime',
                    minWidth: 120,
                    ellipsis: true,
                    formatMethod: (row) => row.execEndTime || '-'
                },
                {
                    title: '执行结果',
                    key: 'execResult',
                    minWidth: 100,
                    render: (h, params) => {
                        const type = params?.row?.execResult || '';
                        const responseConsistentCount = transferVal(params?.row?.responseConsistentCount) ? '(不一致数：' + transferVal(params?.row?.responseConsistentCount) + ')' : '';
                        const resultObj = _.find(VERIFE_RESULT_LIST, ['value', type]) || {};
                        return (
                            <div title={(resultObj?.label || '-') + responseConsistentCount } class='h-table-cell-ellipsis'>
                                <importStatusTableIcon type={resultObj?.icon || ''} />
                                <span>{resultObj?.label || '-'} {responseConsistentCount }</span>
                            </div>
                        );
                    }
                },
                {
                    title: '不一致数',
                    key: 'responseInconsistentCount1',
                    minWidth: 120,
                    render: (h, params) => renderInconsistentCount(h, params, 'responseInconsistentCount')
                },
                {
                    title: '操作',
                    key: 'action',
                    fixed: 'right',
                    width: 180,
                    render: (h, params) => {
                        return (
                            params?.row?.execStatus ? <buttonGroup
                                currentState={params?.row?.execStatus?.toLowerCase() || ''}
                                v-on:button-click={(action, callback) => this.onButtonClick(action, callback, params?.row)
                                }
                            /> : <span>  -</span>
                        );
                    }
                }
            ],
            tableData: [],
            taskCount: 0,
            timer: null,
            // 重演内容
            contextInfo: {
                status: false
            },
            // 修改配置
            editInfo: {
                status: false
            },
            isLock: false,
            syscInfo: {
                status: false
            },
            downloadingIds: {}
        };
    },
    beforeDestroy() {
        this.clearPolling();
    },
    methods: {
        async initData() {
            const productInstNo = localStorage.getItem('productInstNo');
            this.productInstNo =
        _.find(this.productList, ['productInstNo', productInstNo])
            ?.productInstNo || this.productList?.[0]?.productInstNo;
            this.clearPolling();
            // 查询列表
            await this.$refs['query-table'].$_handleResetPageDataAndQuery();
            // 轮询列表更新状态
            this.setPolling();
        },
        checkProduct(item) {
            this.clearPolling();
            this.$emit('check-product', item);
        },
        // 外部触发查询表格
        async handleImdeQuery() {
            await this.$refs['query-table'].$_handleQuery();
        },
        // 表格查询
        async handleQuery(val, shouldShowLoading) {
            shouldShowLoading && (this.loading = true);
            try {
                const params = {
                    productId: this.productInstNo,
                    tradeDay: this.extraForm?.tradeDay
                        ? formatDate(this.extraForm?.tradeDay)
                        : '',
                    sourceType: val?.sourceType || '',
                    startTime: val?.time?.[0] ? formatDate(val?.time?.[0]) + ' 00:00:00' : '',
                    endTime: val?.time?.[1] ? formatDate(val?.time?.[1]) + ' 23:59:59' : '',
                    page: val?.page || 1,
                    pageSize: val?.pageSize || 10,
                    replayName: this.extraForm?.replayName || '',
                    execResult: this.extraForm?.execResult
                };

                const res = await getReplayList(params);
                if (res?.code === '200') {
                    this.tableData = res?.data?.list || [];
                    this.taskCount = res.data?.totalCount;
                } else {
                    this.tableData = [];
                    this.taskCount = 0;
                    this.clearPolling();
                }
            } catch (err) {
                console.error(err);
                this.tableData = [];
                this.taskCount = 0;
                this.clearPolling();
            } finally {
                this.loading = false;
            }
        },

        // 启动定时器查询
        setPolling() {
            this.clearPolling();
            this.timer = setInterval(async () => {
                if (this.isLock) return;
                this.isLock = true;
                this.tableData?.length && (await this.getReplayStatusList());
                this.isLock = false;
            }, 5000);
        },
        clearPolling() {
            this.timer && clearInterval(this.timer);
        },
        // 表格数据状态更新
        async getReplayStatusList() {
            // 获取当前查询参数
            const param = {
                ids: this.tableData.map(o => o.id) || []
            };
            let allData = [];
            try {
                const res = await getReplayStatusList(param);
                if (res.code === '200') {
                    allData = res?.data || [];
                } else {
                    this.clearPolling();
                }
            } catch (err) {
                console.error(err);
                this.clearPolling();
            }

            // 轮询刷新时状态数据
            const tableDataUpdate = this.tableData.map(item => {
                // 兼容对应的任务id不存在了的情况
                const statusData = _.find(allData, ['id', item?.id]) || { execStatus: '' };
                return {
                    ...item,
                    ...statusData
                };
            });
            this.tableData = tableDataUpdate;
        },
        // 创建任务页面
        async handleCreateTask() {
            this.clearPolling();
            this.$emit('to-create-task', 'create-task');
        },
        // 同步任务
        async handleSyncTask() {
            const params = {
                productId: this.productInstNo
            };
            const res = await setReplaySync(params);
            if (res?.code === '200') {
                await this.$refs['query-table'].$_handleResetPageDataAndQuery();
                if (res?.data?.failureCount){
                    this.syscInfo = {
                        status: true,
                        successCount: res?.data?.successCount || 0,
                        failureCount: res?.data?.failureCount || 0,
                        failedTasks: res?.data?.failedTasks || []
                    };
                } else {
                    this.$hMessageSafe.success(`重演任务同步成功！数量：${res?.data?.successCount || 0}`);
                }
            } else if (res?.code?.length === 8) {
                this.$hMessageSafe.error(res?.message);
            } else {
                this.$hMessageSafe.error('重演任务同步失败，请重试！');
            }
        },
        // 修改配置
        async handleEditReplay(vals){
            const params = {
                productId: this.productInstNo,
                id: vals?.id,
                notes: vals?.notes,
                replayName: vals?.replayName,
                sendIntervalMs: vals?.sendIntervalMs
            };
            const res = await setReplayCreate(params);
            if (res?.code === '200'){
                await this.$refs['query-table'].$_handleQuery(false);
                this.$hMessageSafe.success('修改成功');
            }  else if (res?.code?.length === 8) {
                this.$hMessageSafe.error(res?.message);
            }
        },
        // 操作按钮
        async onButtonClick(action, callback, row) {
            const params = {
                id: row.id
            };

            // 通用成功处理函数
            const handleSuccess = async (message) => {
                await this.$refs['query-table'].$_handleQuery(false);
                this.$hMessageSafe.success(message);
            };

            // 通用 API 调用方法
            const executeApiAction = async (apiMethod, successMessage) => {
                const res = await apiMethod(params);
                if (res?.code === '200') {
                    await handleSuccess(successMessage);
                    callback(true);
                } else if (res?.code?.length === 8) {
                    this.$hMessageSafe.error(res?.message);
                    callback(false);
                } else {
                    callback(false);
                }
            };

            const actions = {
                detail: {
                    handler: () => {
                        this.clearPolling();
                        this.$emit('to-detail-task', 'task-detail', row?.id);
                    }
                },
                copy: {
                    handler: () => {
                        this.$emit('to-create-task', 'create-task', row);
                    }
                },
                view: {
                    handler: () => {
                        this.contextInfo = {
                            status: true,
                            ...row
                        };
                    }
                },
                modify: {
                    handler: () => {
                        this.editInfo = {
                            status: true,
                            ...row
                        };
                    }
                },
                start: {
                    api: setReplayStart,
                    successMessage: '启动成功'
                },
                terminated: {
                    api: setReplayStop,
                    successMessage: '终止成功'
                },
                delete: {
                    handler: async () => {
                        this.$hMsgBoxSafe.confirm({
                            title: `确定要删除此任务？`,
                            content: `删除重演任务后，任务将从列表移除，同时任务所产生的数据将一并被删除。请谨慎操作。`,
                            onOk: async () => {
                                await executeApiAction(setReplayDelete, '删除成功');
                            }
                        });
                    }
                },
                paused: {
                    api: setReplayPause,
                    successMessage: '暂停成功'
                },
                resume: {
                    api: setReplayResume,
                    successMessage: '恢复成功'
                }
            };

            const actionDetail = actions[action];
            if (actionDetail) {
                const { handler, api, successMessage } = actionDetail;
                if (handler) {
                    await handler();
                } else if (api && successMessage) {
                    await executeApiAction(api, successMessage);
                } else {
                    callback(false);
                }
            } else {
                callback(false);
            }
        },
        // 下载
        async handleDownload(row){
            if (this.downloadingIds[row?.id]) return; // 如果正在下载中则不重复下载
            const params = {
                replayId: row?.id,
                clusterName: ''
            };
            try {
                this.$set(this.downloadingIds, row.id, true); // 设置下载状态
                const res = await downloadInconsistencyfile(params);
                // blob类型 text/xml
                if (res?.type !== 'application/json') {
                    const url = URL.createObjectURL(res); // 生成临时下载链接
                    const link = document.createElement('a');
                    link.href = url;
                    link.download = `${row?.replayInstanceName}_${row?.replayId}_${getCurrentDatetime()}.zip`;
                    document.body.appendChild(link);
                    link.click();
                    document.body.removeChild(link);
                    window.URL.revokeObjectURL(url);
                    this.$hMessage.success('下载成功!');
                } else if (res?.type === 'application/json') {
                    const text = await res.text();
                    const json = JSON.parse(text);
                    this.$hMsgBoxSafe.error({
                        title: '下载失败',
                        content: json?.message
                    });
                }
            } catch (err) {
                this.$hMessage.error(err);
            } finally {
                this.$set(this.downloadingIds, row.id, false); // 清除下载状态
            }
        }
    }
};
</script>

<style lang="less" scoped>
@import url("@/assets/css/input.less");

.query-table-box {
    width: 100%;
    height: 100%;

    /deep/ .h-poptip.apm-poptip,
    /deep/.h-poptip-rel {
        width: 100%;
    }

    .split-solid {
        width: 1px;
        background-color: #474e6f;
        height: 26px;
        display: block;
        margin-left: 0;
        margin-right: 5px;
    }

    .title {
        .product-select {
            float: right;
            margin-right: 15px;
            top: 5px;
            min-width: 200px;
            width: auto;
        }
    }

    .product-box {
        min-width: 800px;
        height: calc(100% - 53px);
        margin-top: 10px;
        border-radius: 4px;
        background: var(--wrapper-color);
    }

    .table-title {
        font-size: 14px;
        color: var(--font-color);
        padding-right: 10px;
    }

    .table-describe {
        font-size: 12px;
        color: var(--font-opacity-color);

        span {
            font-weight: 600;
        }
    }

    /deep/ .h-icon-load-c {
        animation: ani-loading-spin 1s linear infinite;
        display: inline-block;
    }

    @keyframes ani-loading-spin {
        from {
            transform: rotate(0deg);
        }

        25% {
            transform: rotate(90deg);
        }

        50% {
            transform: rotate(180deg);
        }

        75% {
            transform: rotate(270deg);
        }

        to {
            transform: rotate(360deg);
        }
    }
}
</style>
