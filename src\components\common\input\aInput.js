import './input.less';
import { typeOf } from '@/utils/utils';
import AsyncValidator from 'async-validator';
export default {
    name: 'a-input',
    props: {
        prop: {
            type: String // 必须
        },
        algin: {
            type: String,
            default: 'left'
        },
        type: {
            type: String,
            default: 'text' // 'text', 'textarea', 'password', 'int'
        },
        value: {
            type: [String, Number],
            default: ''
        },
        placeholder: {
            type: String,
            default: ''
        },
        disabled: {
            type: Boolean,
            default: false
        },
        // tooltip位置
        placement: {
            type: String,
            default: 'bottom'
        },
        // 是否显示tooltip
        showErrorMessage: {
            type: <PERSON>olean,
            default: true
        },
        required: {
            type: Boolean,
            default: false
        },
        requiredTrigger: {
            type: String,
            default: 'blur'
        },
        positionFixed: {
            type: Boolean,
            default: false
        },
        validRules: {
        // 例如：
        // [
        //    'ip4'
        //    { required: true, message: '输入不能为空', trigger: 'blur'}
        //    { test: /^[a-zA-Z]+$/, message: "不全是字母", trigger: "blur" },
        //    { test: validFunc, trigger: "blur" }
        // ]
            type: Array
        },
        noTrim: {
            type: Boolean,
            default: false  // 默认需要Trim, 传入后取消trim
        }
    },
    data() {
        const allCusRules = {
            // 整数
            intege: {
                pattern: /^-?\d+$/,
                message: '输入的不是整数格式'
            },
            // 正整数
            intege1: {
                pattern: /^[1-9]\d*$/,
                message: '输入的不是正整数格式'
            },
            // 负整数
            intege2: {
                pattern: /^-[1-9]\d*$/,
                message: '输入的不是负整数格式'
            },
            // 数字
            num: {
                pattern: /^-?[1-9]\d*$|^0/,
                message: '只能输入数字格式'
            },
            // 非负整数
            num1: {
                pattern: /^[1-9]\d*$|^0$/,
                message: '只能输入非负整数数字格式'
            },
            // 非正整数
            num2: {
                pattern: /^-[1-9]\d*$|^0$/,
                message: '只能输入非正整数数字格式'
            },
            // 浮点数
            decmal: {
                pattern: /^-?(([1-9]\d*)|0)(\.\d+)?$/,
                message: '只能输入浮点数格式'
            },
            // 正浮点数
            decmal1: {
                pattern: /^(([0-9]+\.[0-9]*[1-9][0-9]*)|([0-9]*[1-9][0-9]*\.[0-9]+)|([0-9]*[1-9][0-9]*))$/,
                message: '只能输入正浮点数格式'
            },
            // 负浮点数
            decmal2: {
                pattern: /^-([1-9]\d*.\d*|0.\d*[1-9]\d*)$/,
                message: '只能输入负浮点数格式'
            },
            // 浮点数
            decmal3: {
                pattern: /^-?([1-9]\d*.\d*|0.\d*[1-9]\d*|0?.0+|0)$/,
                message: '只能输入浮点数格式'
            },
            // 非负浮点数
            decmal4: {
                pattern: /^(([1-9]\d*)|0)(\.\d+)?$/,
                message: '只能输入非负浮点数格式'
            },
            // 非正浮点数
            decmal5: {
                pattern: /^(-([1-9]\d*.\d*|0.\d*[1-9]\d*))|0?.0+|0$/,
                message: '只能输入非正浮点数格式'
            },
            // 邮件
            email: {
                // eslint-disable-next-line no-useless-escape
                pattern: /^(\w+((-\w+)|(\.\w+))*\@[A-Za-z0-9]+((\.|-)[A-Za-z0-9]+)*\.[A-Za-z0-9]+){0,1}$/,
                message: '邮件地址不正确'
            },
            // 颜色
            color: {
                pattern: /^[a-fA-F0-9]{6}$/,
                message: '只能输入颜色格式'
            },
            // url
            url: {
                pattern: /^http[s]?:\/\/([\w-]+\.)+[\w-]+([\w-./?%&=]*)?$/,
                message: '只能输入url格式'
            },
            // 仅中文
            chinese: {
                pattern: /^[\u4E00-\u9FA5\uF900-\uFA2D]+$/,
                message: '只能输入中文格式'
            },
            // ascii
            ascii: {
                pattern: /^[\x00-\xFF]+$/,
                message: '只能输入ACSII字符格式'
            },
            // 邮编
            zipcode: {
                pattern: /^\d{6}$/,
                message: '邮编输入格式不正确，请输入6位编码'
            },
            // 手机号
            mobile: {
                pattern: /^(13|14|15|16|17|18|19)[0-9]{9}$/,
                message: '移动电话格式不正确'
            },
            // ip地址
            ip4: {
                pattern: /^(25[0-5]|2[0-4]\d|[0-1]\d{2}|[1-9]?\d)\.(25[0-5]|2[0-4]\d|[0-1]\d{2}|[1-9]?\d)\.(25[0-5]|2[0-4]\d|[0-1]\d{2}|[1-9]?\d)\.(25[0-5]|2[0-4]\d|[0-1]\d{2}|[1-9]?\d)$/,
                message: '只能输入ip4地址格式'
            },
            // 图片
            picture: {
                pattern: /(.*)\.(jpg|bmp|gif|ico|pcx|jpeg|tif|png|raw|tga)$/,
                message: '只能输入图片格式'
            },
            // 压缩文件
            rar: {
                pattern: /(.*)\.(rar|zip|7zip|tgz)$/,
                message: '只能输入压缩文件格式'
            },
            // 日期
            date: {
                pattern: /^\d{4}\d{1,2}\d{1,2}$/,
                message: '日期格式不正确'
            },
            // qq
            qq: {
                pattern: /[1-9][0-9]{4,11}/,
                message: 'QQ号码格式不正确'
            },
            // 电话号码(包括验证国内区号,国际区号,分机号)
            tel: {
                // eslint-disable-next-line no-useless-escape
                pattern: /^(([0\+]\d{2,3}-)?(0\d{2,3})-)?(\d{7,8})(-(\d{3,}))?$/,
                message: '电话号码格式不正确'
            },
            // 用户名注册。匹配由数字、26个英文字母或者下划线组成的字符串
            username: {
                pattern: /^\w+$/,
                message: '只能输入由数字、26个英文字母或者下划线组成的字符串'
            },
            // 字母
            letter: {
                pattern: /^[A-Za-z]+$/,
                message: '只能输入字母格式'
            },
            // 大写字母
            letter_u: {
                pattern: /^[A-Z]+$/,
                message: '只能输入大写字母格式'
            },
            // 小写字母
            letter_l: {
                pattern: /^[a-z]+$/,
                message: '只能输入小写字母格式'
            }
        };

        return {
            inputValue: typeof (this.value) === 'number' ? this.value : this.value.trim(),
            allCusRules: allCusRules,
            reqRules: [],
            transCustRules: [],
            mustShowErrorList: [],
            validateMessage: ''
        };
    },
    mounted() {
        if (this.validRules?.length) {
            this.customRules(); // 自定义规则 生成 transCustRules
        };
        if (this.required) {
            const reqRule = { required: true, message: '输入不能为空', trigger: this.requiredTrigger };
            this.reqRules.push(reqRule); // 必填规则增加
        };
        // 初始化的时候校验
        if (this.validRules?.length || this.required){
            this.$nextTick(() => {
                this.validate('blur', () => {});
            });
        }

    },
    methods: {
        handleBlur(event) {

            if (this.validRules?.length || this.required){
                this.$nextTick(() => {
                    this.validate('blur', () => {});
                    this.$emit('on-blur', event, this.validateMessage);
                });
            } else {
                this.$emit('on-blur', event);
            }
        },
        handleChange(event){
            if (this.noTrim) {
                this.inputValue = event.target.value;
            } else {
                this.inputValue = typeof (event.target.value) === 'number' ? event.target.value : event.target.value.trim();
            }
            this.$emit('on-change', this.inputValue);
            if (this.validRules?.length || this.required) {
                this.$nextTick(() => {
                    this.validate('change', () => {});
                });
            }
        },
        // 自定义规则 transCustRules
        customRules() {
            for (const rule of this.validRules) {
                this.custRuleValid(rule);
            }
        },
        // 根据规则是字符串还是对象 转换规则参数格式
        // 默认支持验证的，有intege等,默认blur触发
        /**
        * 自定义验证规则
        *{
        *	 test:FuncOrRegExp,
        *	  message:'', // 必填
        *     trigger:'blur' // 目前只支持blur
        *    *}
        */
        custRuleValid(rule) {
            const isString = typeOf('String', rule);
            const isObj = typeOf('Object', rule);
            if (isString) {
                this.stringRuleValid(rule);
            }
            if (isObj && rule.required) {
                const reqRule = { required: true, message: '输入不能为空', trigger: this.requiredTrigger };
                this.reqRules.push(reqRule);
            };
            if (isObj && rule.test) {
                // test:为正则表达式
                const isRegExp =
                rule.test.constructor && rule.test.constructor.name === 'RegExp';
                // test:为定义函数
                const isFunc = typeOf('Function', rule.test);
                if (isRegExp) {
                    // { pattern: pattern, message: message, trigger: trigger };
                    this.regularValid(rule.test, rule.message, rule.trigger);
                } else if (isFunc) {
                    const funcRule = { validator: rule.test, trigger: rule.trigger };
                    this.transCustRules.push(funcRule);
                }
            }
            if (isObj && rule.validator) {
                const funcRule = { validator: rule.validator, trigger: rule.trigger };
                this.transCustRules.push(funcRule);
            }
        },
        // 若规则类型是字符串 则从 allCusRules 自定义规则列表中 获得对应的 规则对象
        stringRuleValid(rule) {
            if (this.allCusRules[rule]) {
                const tRule = this.allCusRules[rule];
                this.regularValid(tRule.pattern, tRule.message, 'blur');
            }
        },
        // 转换规则参数格式，添加至 transCustRules
        regularValid(pattern, message, trigger) {
            const rule = { pattern: pattern, message: message, trigger: trigger };
            this.transCustRules.push(rule);
        },
        // 根据trigger筛选
        getFilteredRule(trigger) {
            const rules = [];
            if (this.reqRules?.length){
                rules.push(...this.reqRules);
            }
            if (this.transCustRules?.length){
                rules.push(...this.transCustRules);
            }
            return rules.filter(
                (rule) => !rule.trigger || rule.trigger.indexOf(trigger) !== -1
            );
        },
        // 校验
        validate(trigger, callback = function() {}) {
            const rules = this.getFilteredRule(trigger);
            const descriptor = {};
            if (!rules || rules.length === 0) {
                callback();
                return true;
            }
            descriptor[this.prop] = rules;
            const model = {};
            model[this.prop] = this.inputValue;
            const validator = new AsyncValidator(descriptor);
            this.validateMessage = '';
            validator.validate(model, { firstFields: true }, (errors) => {
                this.validateMessage = errors ? errors[0].message : '';
            });
        },
        // 触发校验
        setValidate() {
            this.$nextTick(() => {
                this.validate('blur', () => {});
            });
        },
        // 返回校验状态
        getValidateStatus() {
            return !this.validateMessage;
        }
    },
    render() {
        return <main class={ `a-input ${this.validateMessage ? 'a-input-error' : ''}`} style={{ display: 'inline-block' }}>
            {
                this.showErrorMessage && this.validateMessage ? <h-tooltip content={ this.validateMessage ? this.validateMessage : ''} placement={this.placement} positionFixed={this.positionFixed}>
                    <h-input
                        ref='a-input'
                        algin={this.algin}
                        type={this.type}
                        value={this.inputValue}
                        placeholder={this.placeholder}
                        disabled={this.disabled}
                        v-on:on-blur={this.handleBlur}
                        v-on:on-change={this.handleChange}
                    ></h-input>
                </h-tooltip>
                    : <h-input
                        ref='a-input'
                        algin={this.algin}
                        type={this.type}
                        value={this.inputValue}
                        placeholder={this.placeholder}
                        disabled={this.disabled}
                        v-on:on-blur={this.handleBlur}
                        v-on:on-change={this.handleChange}
                    ></h-input>
            }
        </main>;
    }
};
