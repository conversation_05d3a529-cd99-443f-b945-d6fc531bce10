<template>
    <div>
        <h-msg-box-safe
            v-model="modalData.status"
            :escClose="true"
            :mask-closable="false"
            title="执行节点校验"
            width="600"
            height="320"
            @on-open="handleOpen">
            <a-tips
                style="margin-top: 10px;"
                type='warning'
                :tipText='`包含“${tableName}”表的节点与您选择的节点不完全一致，请确认后再执行。系统会自动记录最后一次执行节点。`'
            ></a-tips>
            <!-- 当前已选择节点 -->
            <div v-if="intances.length">
                <h-radio
                    v-model="nowRadio"
                    class="radio-line"
                    @on-change="handleNowChange">当前已选节点</h-radio>
                <div class="node-group-box">
                    <h-checkbox-group v-model="nowIntance">
                        <h-checkbox
                            v-for="item in intances"
                            :key="item.value"
                            :label="item.value"
                            :disabled="!nowRadio"
                            style="margin: 0 10px 8px 0;">{{ item.label }}
                            <span v-if="isMainFlag(item.value)" class="main-flag"></span>
                        </h-checkbox>
                    </h-checkbox-group>
                </div>
            </div>
            <!-- 建议选择节点 -->
            <div style="position: relative;">
                <h-radio
                    v-model="adviceRadio"
                    class="radio-line"
                    :disabled="!suggestList.length"
                    @on-change="handleAdviceChange">包含 {{`${tableName}`}} 表的节点</h-radio>
                    <span
                        v-if="suggestList.length"
                        style="
                            position: absolute;
                            right: 0;
                            top: 9px;
                            background-color: #e6f7ff;
                            padding: 3px 8px;
                            color: #2d8de5;
                            border-radius: 4px;">
                        {{modalData.hasWriteRedo ? '主备同步表' : '主备不同步表' }}</span>
                <div v-if="suggestList.length" class="node-group-box">
                    <h-checkbox-group v-model="adviceInstance">
                        <h-checkbox
                            v-for="item in suggestList"
                            :key="item.value"
                            :label="item.value"
                            :disabled="!adviceRadio"
                            style="margin: 0 10px 8px 0;">{{ item.label }}
                            <span v-if="isMainFlag(item.value)" class="main-flag"></span>
                        </h-checkbox>
                    </h-checkbox-group>
                </div>
            </div>
            <template v-slot:footer>
                <h-poptip placement="top" width="300" trigger="hover">
                    <h-checkbox v-model="verifyStatus" style="margin-right: 10px;">关闭校验</h-checkbox>
                    <div slot="content" class="info">
                        <div class="info-line">
                            <span class="info-line-title">校验：</span>
                            <p>根据用户SQL所在的表属性，提供建议执行的节点</p>
                        </div>
                        <div class="info-line">
                            <span class="info-line-title">注：</span>
                            <p>执行节点校验配置可在页面“配置”中修改</p>
                        </div>
                    </div>
                </h-poptip>
                <h-button @click="modalData.status = false">取消</h-button>
                <h-button type="primary" @click="handleSubmit">确认</h-button>
            </template>
        </h-msg-box-safe>
    </div>
</template>

<script>
import _ from 'lodash';
import aTips from '@/components/common/apmTips/aTips';
export default ({
    props: {
        modalInfo: {
            type: Object,
            default: null
        },
        intances: {
            type: Array,
            default: () => []
        },
        clusterRoles: {
            type: Object,
            default: () => {}
        }
    },
    data() {
        return {
            nowIntance: [],
            adviceInstance: [],
            suggestList: [],
            nowRadio: true,
            adviceRadio: false,
            verifyStatus: false,
            modalData: this.modalInfo,
            tableName: ''
        };
    },
    mounted() {
    },
    methods: {
        handleOpen() {
            this.suggestList = _.cloneDeep(this.modalInfo.suggestList);
            const from = this.modalData.astResult?.[0]?.type === 'select'
                ? this.modalData.astResult?.[0]?.from || []
                : this.modalData.astResult?.[0]?.table;
            this.tableName = from?.[0]?.table;
            if (!this.intances.length) {
                this.nowRadio = false;
                this.adviceRadio = true;
                this.adviceInstance = _.map(this.suggestList, o => {
                    return o.value;
                });
            } else {
                let shardingList = []; // 分片列表
                this.intances.forEach(ele => {
                    this.nowIntance.push(ele.value);
                    shardingList.push(_.find(this.modalData.instanceList, ['instanceId', ele.value])?.systemNo);
                });
                shardingList = [...new Set(shardingList)];
                this.modalData.suggestList.forEach(ele => {
                    if (shardingList.includes(_.find(this.modalData.instanceList, ['instanceId', ele.value])?.systemNo)) {
                        this.adviceInstance.push(ele.value);
                    }
                });
            }
        },
        handleNowChange(val) {
            this.adviceRadio = !val;
        },
        handleAdviceChange(val) {
            this.nowRadio = !val;
        },
        handleSubmit() {
            if ((this.adviceRadio && this.adviceInstance.length === 0) ||
                (this.nowRadio && this.nowIntance.length === 0)) {
                this.$hMessage.warning('请选择执行节点');
                return;
            }
            if (this.verifyStatus) {
                localStorage.setItem('apm.mdbsql.tableCheck', 'false');
            }
            const arr1 = this.adviceRadio ? this.suggestList : this.intances;
            const arr2 = this.adviceRadio ? this.adviceInstance : this.nowIntance;
            const result = _.filter(arr1, (obj) => _.includes(arr2, obj.value)) || [];
            this.$emit('verifySubmit', this.modalInfo.astResult, result, this.adviceRadio);
            this.modalData.status = false;
        },
        isMainFlag(id) {
            return _.find(this.clusterRoles, ['instanceId', id])?.clusterRole === 'ARB_ACTIVE';
        }
    },
    components: { aTips }
});
</script>

<style lang="less" scoped>
/deep/ .h-modal-body {
    padding: 15px;
}

.radio-line {
    margin: 15px 0 10px;
}

.node-group-box {
    width: calc(100% - 20px);
    height: auto;
    padding: 10px 10px 2px;
    margin-left: 20px;
    background-color: #cacfd454;
    border-radius: 4px;
}

.main-flag {
    display: inline-block;
    width: 11px;
    height: 11px;
    background: url("static/mainFlag.png");
    background-size: 11px 10px;
    background-repeat: no-repeat;
    position: relative;
    top: 2px;
}

.info {
    & > .info-line {
        display: flex;
        margin-bottom: 6px;

        & > span {
            text-align: left;
        }

        & > p {
            width: 280px;
            text-align: left;
            white-space: normal;
        }

        & > .info-line-title {
            width: 50px;
            flex: none;
        }
    }

    .info-line:last-child {
        margin: 0;
    }
}
</style>

