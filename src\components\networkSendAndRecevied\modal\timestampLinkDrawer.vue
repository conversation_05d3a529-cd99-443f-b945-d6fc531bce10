<template>
    <div>
        <h-drawer
            ref="drawer-box"
            v-model="modalData.status"
            title="时间戳"
            width="50"
            @on-visible-change="handleDrawerOpen"
            @on-close="handleClose">
            <a-button type="dark" style="margin-right: 5px;" @click="expandAll">
                全部展开
            </a-button>
            <a-button type="dark" @click="collapseAll">
                全部收起
            </a-button>

            <h-simple-groups-table
                v-if="tableData.length"
                ref="simpleGroupsTable"
                :columns="columns"
                :data="tableData"
                :height="tableHeight"
                showTitle
                :disabledHover="false"
                :canMove="false"
                @on-expand="onExpand"
            ></h-simple-groups-table>
            <no-data v-else />
        </h-drawer>
    </div>
</template>

<script>
import { v4 as uuidv4 } from 'uuid';
import noData from '@/components/common/noData/noData';
import aButton from '@/components/common/button/aButton';
export default {
    name: 'TimestampLinkDrawer',
    components: {
        noData, aButton
    },
    props: {
        modalInfo: {
            type: Object,
            default: () => {}
        }
    },
    data() {
        return {
            modalData: this.modalInfo,
            tableHeight: 0,
            columns: [
                {
                    title: '节点',
                    key: 'instanceName',
                    ellipsis: true,
                    minWidth: 120
                },
                {
                    title: '插件',
                    key: 'pluginName',
                    ellipsis: true
                },
                {
                    title: '时间戳(μs)',
                    key: 'timestampUs',
                    ellipsis: true,
                    minWidth: 120
                },
                {
                    title: '差值(μs)',
                    key: 'duration',
                    ellipsis: true
                },
                {
                    title: '标签',
                    key: 'tag',
                    ellipsis: true,
                    minWidth: 220
                }
            ],
            tableData: [],
            tableLoading: false
        };
    },
    computed: {

    },
    mounted() {
        this.fetTableHeight();
        window.addEventListener('resize', this.fetTableHeight);
    },
    beforeDestroy() {
        window.removeEventListener('resize', this.fetTableHeight);
    },
    methods: {
        /**
         * 打开抽屉并处理表格数据。
         *
         * 该方法用于打开抽屉组件，并为表格设置数据，包括以下步骤：
         * 1. 调用 `fetTableHeight` 方法以获取并设置表格的高度。
         * 2. 获取父组件传参 `timeStamp` 原始数据
         * 3. 使用 `handleTreeTableData` 方法将 `timeStamp` 原始数据转换为所需的表格数据格式。
         * 4. 将转换后的数据赋值给 `this.tableData`，以便在表格中展示。
         *
         */
        handleDrawerOpen(status) {
            if (!status) return;

            this.fetTableHeight();
            // 模拟数据
            // const timeStamp = [
            //     {
            //         instanceName: 'ldplt-query',
            //         pluginName: 'ldp_postmantool',
            //         timestampUs: 1746776658818100,
            //         tag: 'proc request ldp_postmantool'
            //     },
            //     {
            //         instanceName: 'front#0',
            //         pluginName: 'ldp_front',
            //         timestampUs: 1746776658818200,
            //         tag: 'proc request ldp_front at:front#0'
            //     },
            //     {
            //         instanceName: 'front#0',
            //         pluginName: 'ldp_front',
            //         timestampUs: 1746776658818300,
            //         tag: 'proc request ldp_front at:front#0'
            //     },
            //     {
            //         instanceName: 'front#0',
            //         pluginName: 'ldp_front',
            //         timestampUs: 1746776658818500,
            //         tag: 'proc request ldp_front at:front#0'
            //     },
            //     {
            //         instanceName: 'urct_bid#0',
            //         pluginName: 'ldp_probiz',
            //         timestampUs: 1746776658819100,
            //         tag: 'proc request ldp_probiz at:front#0'
            //     },
            //     {
            //         instanceName: 'urct_bid#0',
            //         pluginName: 'ldp_probiz',
            //         timestampUs: 1746776658819300,
            //         tag: 'proc request ldp_probiz at:front#0'
            //     },
            //     {
            //         instanceName: 'urct_bid#0',
            //         pluginName: 'ldp_probiz',
            //         timestampUs: 1746776658819500,
            //         tag: 'proc request ldp_probiz at:front#0'
            //     },
            //     {
            //         instanceName: '',
            //         pluginName: 'cres_agent',
            //         timestampUs: 1746776658829500,
            //         tag: 'proc request cres_agent'
            //     },
            //     {
            //         instanceName: '',
            //         pluginName: 'cres_agent',
            //         timestampUs: 1746776658829700,
            //         tag: 'proc request cres_agent'
            //     },
            //     {
            //         instanceName: 'front#0',
            //         pluginName: 'ldp_front',
            //         timestampUs: 1746776658819800,
            //         tag: 'proc request ldp_front at:front#0'
            //     },
            //     {
            //         instanceName: 'front#0',
            //         pluginName: 'ldp_front',
            //         timestampUs: 1746776658819900,
            //         tag: 'proc request ldp_front'
            //     },
            //     {
            //         instanceName: 'ldplt-query',
            //         pluginName: 'ldp_postmantool',
            //         timestampUs: 1746776659818100,
            //         tag: 'proc request ldp_postmantool'
            //     }
            // ];

            const timeStamp = this.modalData.timeStamp || [];
            this.tableData = this.handleTreeTableData(timeStamp);
        },

        /**
         * 将原始数据转换为树形表格数据格式。
         *
         * 该方法接收原始数据 `orgData`，并对其进行处理，转换为适用于树形表格的数据格式。
         * 转换后的数据结构可以用于树形展示，通常包括层级关系和节点信息。
         *
         * @param {Array} orgData - 原始数据，需要转换为树形结构的数据。
         * @returns {Array} 转换后的树形表格数据，符合树形展示要求。
         */
        handleTreeTableData(orgData) {
            /**
             * 方法处理逻辑：
             * 1. instanceName存在时，将instanceName相邻且一致的数据合并计算出一行汇总数据生成父级，
             *  其中父级duration为其子级最后一行timestampUs减第一行timestampUs；
             *  其中子级第一行duration为'-'，其余行为下一行timestampUs减上一行timestampUs,子级节点名为空
             * 2. 若instanceName为空不存在时，为该行创建一个父级，duration为'-'，其子级则为该行内容
             *  且instanceName为空不存在，用'-'来代替该列内容展示
             * 3. 若后续出现之前数据出现过的相同instanceName，该行duration为该行的timestampUs减去上一次出现同instanceName的timestampUs
             *
             */
            const groups = [];
            let currentGroup = null;

            // 分组逻辑
            orgData.forEach(item => {
                const instance = item.instanceName || '-'; // 统一处理空值

                if (instance === '-') {
                    // 空实例立即生成独立分组
                    if (currentGroup) groups.push(currentGroup);
                    groups.push({
                        instanceName: '-',
                        children: [item]
                    });
                    currentGroup = null;
                } else {
                    if (currentGroup?.instanceName === instance) {
                        currentGroup.children.push(item);
                    } else {
                        if (currentGroup) groups.push(currentGroup);
                        currentGroup = {
                            instanceName: instance,
                            children: [item]
                        };
                    }
                }
            });
            if (currentGroup) groups.push(currentGroup);

            // 处理树形结构
            const result = [];
            const lastTimestampMap = {};

            groups.forEach((group) => {
                if (group.instanceName === '-') {
                    // 空实例每个子项独立成组
                    group.children.forEach(child => {
                        result.push({
                            id: uuidv4(), // 使用UUID生成唯一ID
                            instanceName: '-',
                            duration: '-',
                            children: [{
                                ...child,
                                duration: '-'
                            }]
                        });
                    });
                } else {
                    // 通过数组索引直接计算时间差
                    const children = group.children.map((child, index) => {
                        const duration = index === 0
                            ? (lastTimestampMap[group.instanceName]
                                ? child.timestampUs - lastTimestampMap[group.instanceName]
                                : '-')
                            : child.timestampUs - group.children[index - 1].timestampUs;
                        return { ...child, duration, instanceName: '' };
                    });

                    // 计算父级duration
                    const parentDuration = children.length > 1
                        ? children[children.length - 1].timestampUs - children[0].timestampUs
                        : '-';

                    // 更新最后一次出现时间
                    lastTimestampMap[group.instanceName] = children[children.length - 1].timestampUs;

                    // 构建父节点
                    const parentNode = {
                        id: uuidv4(), // 使用UUID生成唯一ID
                        instanceName: group.instanceName,
                        duration: parentDuration,
                        children
                    };

                    result.push(parentNode);
                }
            });

            return result;
        },

        /**
         * 点击表头展开、折叠时触发
         * 该方法用于处理用户点击表头展开或折叠动作，并更新对应的分组表格数据的展开状态。
         * 功能概述：
         * 1. 根据传入的 `data` 参数查找对应的表格数据行。
         * 2. 更新找到的表格数据行的 `expand` 属性为传入的 `status` 以表示该行的展开或折叠状态。
         * 3. 如果找不到对应的数据行，输出错误信息。
         *
         * @param {Object} data - 表头点击时传入的分组表格数据对象，通常包含 `id` 识别信息。
         * @param {Boolean} status - 展开或折叠状态，`true` 代表展开，`false` 代表折叠。
         */
        onExpand(data, status) {
            const index = this.tableData.findIndex(item => item.id === data.id);
            if (index !== -1) {
                this.$set(this.tableData[index], 'expand', status);
            } else {
                console.error('未找到对应数据');
            }
        },

        /**
         * 展开树形表格中的所有父节点。
         */
        expandAll() {
            this.tableData.forEach(item => {
                this.$set(item, 'expand', true);
            });
        },

        /**
         * 收起树形表格中的所有父节点。
         */
        collapseAll() {
            this.tableData.forEach(item => {
                this.$set(item, 'expand', false);
            });
        },
        fetTableHeight() {
            this.tableHeight = this.$refs['drawer-box']?.$el?.offsetTop - (window.LOCAL_CONFIG.HEADER_VISIBLE === true ? 210 : 140);;
        },
        handleClose() {
            this.modalData.status = false;
        }

    }
};
</script>

<style lang="less" scoped>
@import url("@/assets/css/drawer.less");
@import url("@/assets/css/tree-table.less");
</style>
