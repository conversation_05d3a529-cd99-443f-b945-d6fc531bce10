<template>
    <div>
        <h-msg-box
            v-model="modalData.status"
            :escClose="false"
            :mask-closable="false"
            title="编辑数据区间"
            @on-cancle="cancel"
            @on-ok="confirm"
        >
            <div class="data-range-container">
                <div v-for="(item, index) in dataRanges" :key="index" class="range-item">
                    <h-select v-model="item.type" style="width: 100px;" :clearable="false">
                        <h-option value="range">区间</h-option>
                        <h-option value="enum">枚举</h-option>
                    </h-select>
                    <span >&nbsp;</span>
                    <template v-if="item.type === 'range'">
                        <h-input-number v-model="item.start" :min="0" style="width: 150px;"></h-input-number>
                        <span style="margin: 0 5px;">-</span>
                        <h-input-number v-model="item.end" :min="0"  style="width: 150px;"></h-input-number>
                    </template>
                    <template v-else-if="item.type === 'enum'">
                        <h-input-number v-model="item.value" :min="0" placeholder="输入数字" style="width: 150px;"></h-input-number>
                        <span style="width: 164px;"></span>
                    </template>

                    <h-button  type="error" shape="circle" icon="minus" size="small" style="margin-left: 10px;" @on-click="removeRange(index)"></h-button>
                </div>
                <h-button icon="plus" shape="circle" type="primary" size="small" style="margin-top: 10px;" @on-click="addRange"></h-button>
            </div>
            <template v-slot:footer>
                <h-button type="ghost" @on-click="cancel">取消</h-button>
                <h-button type="primary" style="margin-left: 8px;" @on-click="confirm">确定</h-button>
            </template>
        </h-msg-box>
    </div>
</template>

<script>
export default {
    props: {
        modalInfo: {
            type: Object,
            default: () => ({
                status: false,
                data: []
            })
        }
    },
    data() {
        return {
            modalData: this.modalInfo,
            dataRanges: this.modalInfo.data.length ? this.modalInfo.data : [{ type: 'range', start: null, end: null }]
        };
    },
    methods: {
        addRange() {
            this.dataRanges.push({ type: 'range', start: null, end: null });
        },
        removeRange(index) {
            this.dataRanges.splice(index, 1);
        },
        cancel() {
            this.modalData.status = false;
        },
        confirm() {
            this.modalData.status = false;
            const data = (this.dataRanges || []).map(item => {
                if (item.type === 'range') {
                    return `${item.start}-${item.end}`;
                } else {
                    return `${item.value}`;
                }
            });
            this.$emit('save', data);
        }
    }
};
</script>

<style scoped>

.range-item {
    display: flex;
    align-items: center;
    margin-bottom: 10px;
}

.title {
    font-size: 16px;
    font-weight: bold;
    margin-bottom: 10px;
}
</style>
