<template>
  <div class="node-diff-wrap">
    <div class="node-diff-wrap-title">
      <h-row>
        <h-col class="node-diff-wrap-title-col" span="12">
          <a-title :title="`对比源节点: ${sourceDiff.sourceNode || '-'}`">
            <slot>
               <span class="context-type">配置文件格式：{{ language || '-' }}</span>
            </slot>
          </a-title>
        </h-col>
        <h-col class="node-diff-wrap-title-col" span="12">
          <a-title :title="`对比目标节点: ${targetDiff.targetNode || '-'}`">
            <slot>
              <span class="context-type">配置文件格式：{{ language || '-' }}</span>
            </slot>
          </a-title>
        </h-col>
      </h-row>
    </div>
    <div class="node-diff-wrap-box">
      <monaco-diff-editor
      ref= "diff-code"
      :originalCode="sourceDiff.sourceValue"
      :modifiedCode="targetDiff.targetValue"
      :language="language"
    />
    <div class="node-diff-wrap-box-path">
      <h-row>
        <h-col class="node-diff-wrap-box-path-col" span="12">
          <p :title="sourceDiff.sourcePath">配置路径：{{ sourceDiff.sourcePath || '-' }}</p>
        </h-col>
        <h-col class="node-diff-wrap-box-path-col" span="12">
          <p :title="targetDiff.targetPath">配置路径：{{ targetDiff.targetPath || '-' }}</p>
        </h-col>
      </h-row>
    </div>
    </div>
  </div>
</template>
<script>
import MonacoDiffEditor from './MonacoDiffEditor.vue';
import aTitle from '@/components/common/title/aTitle';
export default {
    name: 'NodeDiff',
    props: {
    },
    data() {
        return {
            sourceValueCopied: false,
            targetValueCopied: false,
            // diff
            curViewtype: '',
            sourceDiff: {
                sourceId: '',
                sourceNode: '',
                sourceValue: '',
                sourceType: ''
            },
            targetDiff: {
                targetId: '',
                targetNode: '',
                targetValue: '',
                targetType: ''
            },
            language: ''
        };
    },
    beforeUnmount() {
    },
    methods: {
        // diff 数据
        initData(row){
            this.curViewtype = row?.configType;
            // 源
            this.sourceDiff.sourceId = row?.id;
            this.sourceDiff.sourceNode  = row?.appInstanceName;
            this.sourceDiff.sourceType = row?.configSourceType;
            this.sourceDiff.sourcePath = row?.storagePath;
            this.sourceDiff.sourceValue = this.curViewtype !== 'locate_config' ? JSON.stringify(JSON.parse(row?.configContext || '{}'), null, 4) : row?.configContext || '';
            // 目标
            this.targetDiff.targetId = row?.compareId;
            this.targetDiff.targetNode = row?.compareAppInstanceName;
            this.targetDiff.targetType = row?.compareConfigSourceType;
            this.targetDiff.targetPath = row?.compareStoragePath;
            this.targetDiff.targetValue = this.curViewtype !== 'locate_config' ?  JSON.stringify(JSON.parse(row?.compareConfigContext || '{}'), null, 4) : row?.compareConfigContext || '';

            this.language = this.curViewtype ? this.curViewtype !== 'locate_config' ? 'json' : 'xml' : '';
        },
        // 清空数据
        clearData(){
            this.curViewtype = '';
            // 源
            this.sourceDiff.sourceId = '';
            this.sourceDiff.sourceNode  = '';
            this.sourceDiff.sourceValue = '';
            this.sourceDiff.sourceType = '';
            this.sourceDiff.sourcePath = '';
            // 目标
            this.targetDiff.targetId = '';
            this.targetDiff.targetNode = '';
            this.targetDiff.targetValue = '';
            this.targetDiff.targetType = '';
            this.targetDiff.targetPath = '';
            this.language = '';
        }
    },
    components: { MonacoDiffEditor, aTitle }
};
</script>
<style lang="less">
/// 整体背景色
@themeColor: #2d334c;
// @themeColor: #1e1e1e;
/// 其他无变化文字颜色
@noChangeTextColor: #acd7e8;
// 删除行的背景色
@delBlockLineBgColor: #481722;
// 删除内容的背景色
@delContentBgColor: #711410;
// 删除的文字颜色
@delTextColor: #d18878;

// 新增行的背景色
@addBlockLineBgColor: #4a5a2b;
// 新增内容的背景色
@addContentBgColor: #abf2bc;
// 新增的文字颜色
@addTextColor: #88bb87;

.node-diff-wrap {
    height: 100%;

    &-copy {
        margin: 10px 0;

        &-line {
            text-align: right;
            padding-right: 10px;
        }
    }

    &-title {
        margin-bottom: 5px;

        .apm-title {
            .context-type {
                position: absolute;
                right: 10px;
            }
        }

        .h-row {
            display: flex;
        }

        &-col {
            flex: 1;
            margin-right: 5px;
        }
    }

    .copy {
        color: #49b3ff;
        cursor: pointer;

        &-copied {
            color: #49b3ff;
            opacity: 0.5;
        }
    }

    &-box {
        height: calc(100% - 80px);

        &-path {
            margin: 10px;

            &-col {
                font-size: 14px;
                color: var(--font-color);
            }

            p {
                width: 100%;
                overflow: hidden;
                white-space: nowrap;
                text-overflow: ellipsis;
            }
        }
    }

    /* stylelint-disable-next-line selector-class-pattern */
    .h-textdiff .h-textdiff-wrapper .h-textdiff-content pre code.h-textdiff-rowSelect,
    .h-textdiff .h-textdiff-wrapper .h-textdiff-content pre code:hover {
        background: none;
    }

    .code-diff-view,
    .code-diff-view .diff-table .blob-code-hunk {
        background-color: @themeColor !important;
        color: @noChangeTextColor !important;
        border-color: #6e7781;
        margin-top: 10px;
        max-height: 500px;
        min-height: 100px;
    }

    .h-textdiff .h-textdiff-wrapper .h-textdiff-content {
        border-color: #6e7781;
    }

    .file-header {
        display: none;
    }

    .code-diff-view .diff-table {
        .blob-num-addition,
        .blob-num-deletion {
            color: #6e7781;
        }

        .blob-num-deletion,
        .blob-code-deletion,
        .blob-code-addition,
        .blob-num-addition,
        .blob-num-hunk {
            background-color: @themeColor;
        }

        td {
            border-left-color: #6e7781 !important;
        }
    }

    .code-diff-view .diff-table .blob-code .blob-code-inner {
        color: @noChangeTextColor;
    }

    .code-diff-view .empty-cell {
        background-color: rgba(121, 121, 121, 0.17);
    }

    .rcm-source-diff-wrap {
        background-color: @themeColor;

        .d2h-file-header,
        d2h-sticky-header {
            display: none;
        }

        .d2h-code-side-linenumber,
        .d2h-file-wrapper {
            border-color: #888;
        }

        .d2h-code-side-linenumber {
            background-color: @themeColor !important;
        }

        td,
        .d2h-info {
            background-color: transparent;
            color: @noChangeTextColor;
        }

        .default {
            color: @noChangeTextColor;
        }

        .d2h-code-side-emptyplaceholder,
        .d2h-emptyplaceholder {
            background-color: rgba(81, 81, 81, 0.26);
        }
    }

    .code-diff-view .diff-table .blob-code-deletion .x {
        background-color: @delContentBgColor;
        // text-decoration: line-through;
        color: @delTextColor;
    }

    .blob-code-deletion {
        background-color: @delBlockLineBgColor !important;
    }

    .blob-code-addition {
        background-color: #383d29 !important;

        .x {
            color: @addContentBgColor !important;
            background-color: @addBlockLineBgColor !important;
        }
    }

    .h-textdiff .h-textdiff-wrapper .h-textdiff-content pre {
        background-color: @themeColor;
        color: @noChangeTextColor;
    }
}
</style>
