{"summary": {"totalFiles": 523, "processedFiles": 421, "totalTexts": 3387, "duration": "1秒", "extractTime": "2025-07-31T06:03:42.862Z"}, "categories": {"common": {"totalTexts": 148, "modules": {}}, "api": {"totalTexts": 3, "modules": {}}, "components": {"totalTexts": 2776, "modules": {"accordObservation": 8, "analyse": 77, "analyseConfig": 51, "appBindingCore": 15, "brokerDataLimit": 52, "businessMonitor": 19, "common": 220, "coreReplayObservation": 114, "dataSecondAppearance": 41, "eccomProduct": 4, "endpointConfig": 90, "latencyTrendAnalysis": 23, "ldpDataObservation": 292, "ldpLinkConfig": 181, "ldpLogCenter": 85, "ldpMonitor": 79, "ldpProduct": 98, "ldpTable": 99, "locateConfig": 33, "managementQuery": 59, "marketAllLink": 34, "mcDataObservation": 150, "mcDeploy": 22, "mdbDataObservation": 46, "mdbPrivilegeManage": 109, "networkSendAndRecevied": 79, "productDataStorage": 62, "productServiceConfig": 108, "productTimeAnalysis": 14, "rcmBacklogMonitor": 9, "rcmDeploy": 185, "rcmObservation": 52, "secondAppearance": 33, "sms": 17, "sqlTable": 94, "transaction": 27, "tripartiteServiceConfig": 18, "ustTableVerification": 77}}, "store": {"totalTexts": 1, "modules": {}}, "utils": {"totalTexts": 42, "modules": {}}, "pages": {"totalTexts": 417, "modules": {"accordMonitor": 4, "accordObservation": 2, "analyseConfig": 5, "analyseData": 14, "apmMonitorConfig": 1, "appRunningState": 8, "brokerDataLimit": 8, "displaySettingDrawer": 9, "index": 27, "createRule": 33, "dataSecondAppearance": 1, "addModal": 2, "helpModal": 1, "routeConfig": 9, "routeInfoForm": 6, "util": 8, "latencyTrendAnalysis": 13, "ldpDataObservation": 1, "ldpLinkConfig": 5, "ldpLogCenter": 3, "clusterMonitor": 1, "ldpAppMonitor": 1, "ldpTable": 6, "locateConfig": 3, "managementQuery": 13, "marketAllLink": 6, "marketMonitor": 5, "marketNodeDelayList": 12, "marketPenetrateList": 21, "marketTimeDelay": 6, "mcDataObservation": 1, "mcDeploy": 1, "constant": 10, "tableSelector": 3, "detailDrawer": 3, "exportHistory": 10, "exportTable": 1, "mdbDataObservation": 0, "mdbPrivilegeManage": 32, "businessMonitor": 0, "networkSendAndRecevied": 4, "noticeManagerList": 3, "productDataStorage": 9, "productServiceList": 2, "productTimeDetail": 11, "productTimeSummary": 4, "rcmBacklogMonitor": 2, "rcmDeploy": 2, "rcmObservation": 2, "publishStatusDetail": 7, "smsList": 32, "sqlCores": 13, "sqlTable": 17, "threadInfoOverview": 1, "topoMonitor": 4, "transaction": 6, "tripartiteServiceList": 2, "ustTableVerification": 1}}}, "topFiles": [{"file": "config\\exchangeConfig.js", "textsCount": 115}, {"file": "components\\common\\topo\\ldpNodeTopo.js", "textsCount": 99}, {"file": "components\\coreReplayObservation\\coreReplayDetail.vue", "textsCount": 51}, {"file": "components\\ldpLogCenter\\ldpTodbErrorRetry.vue", "textsCount": 50}, {"file": "views\\index\\createRule\\createRule.vue", "textsCount": 49}, {"file": "components\\ldpLinkConfig\\productInfoConfig.vue", "textsCount": 45}, {"file": "components\\mcDataObservation\\mcTopic.vue", "textsCount": 44}, {"file": "components\\ldpDataObservation\\front\\backendStat.vue", "textsCount": 42}, {"file": "views\\index\\smsList.vue", "textsCount": 42}, {"file": "components\\transaction\\settingModal.vue", "textsCount": 41}, {"file": "config\\spanConfig.js", "textsCount": 40}, {"file": "components\\ldpDataObservation\\core\\funNumProcess.vue", "textsCount": 39}, {"file": "views\\index\\latencyTrendAnalysis.vue", "textsCount": 39}, {"file": "components\\ldpProduct\\businessBox\\businessPoptip.js", "textsCount": 38}, {"file": "components\\ldpProduct\\businessBox\\dataAccordBusinessPoptip.js", "textsCount": 37}, {"file": "components\\ustTableVerification\\createVerificationTask.vue", "textsCount": 36}, {"file": "views\\index\\mdbPrivilegeManage.vue", "textsCount": 36}, {"file": "views\\index\\sqlTable.vue", "textsCount": 36}, {"file": "components\\ldpDataObservation\\core\\nodeDeploy.vue", "textsCount": 35}, {"file": "views\\index\\marketPenetrateList.vue", "textsCount": 35}]}