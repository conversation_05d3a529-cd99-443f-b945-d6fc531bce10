<template>
    <div class="main">
        <div class="title">
            <a-title title="上下文积压监控">
                <slot>
                    <h-select
                        v-show="productList.length > 1"
                        v-model="productInstNo"
                        placeholder="请选择"
                        class="title-select"
                        placement="bottom"
                        :positionFixed="true"
                        :clearable="false" @on-change="checkProduct">
                        <h-option
                            v-for="item in productList" :key="item.id" :value="item.productInstNo">
                            {{ item.productName }}
                        </h-option>
                    </h-select>
                </slot>
            </a-title>
        </div>
        <a-loading v-if="loading" style="z-index: 101;"></a-loading>

        <menu-layout ref="menu" :menuList="menuList" menuItemId="id" titleAttribute="name" menuTitle="RCM配置列表"
            @check-menu="selectMenuChange">
            <template v-slot:right>
                <rcm-backlog v-if="rcmId" ref="rcm-backlog" :rcmId="rcmId" :rcmName="rcmName"></rcm-backlog>
                <no-data v-else class="box" style="background: var(--wrapper-color);"></no-data>
            </template>
        </menu-layout>

    </div>
</template>

<script>
import _ from 'lodash';
import { mapState, mapActions } from 'vuex';
import { getRcmInstanceList } from '@/api/rcmApi';
import noData from '@/components/common/noData/noData';
import aTitle from '@/components/common/title/aTitle';
import aLoading from '@/components/common/loading/aLoading';
import menuLayout from '@/components/common/menuLayout/menuLayout';
import rcmBacklog from '@/components/rcmBacklogMonitor/rcmBacklog.vue';
export default {
    name: 'RcmDeploy',
    components: { aTitle, noData, aLoading, menuLayout, rcmBacklog },
    data() {
        return {
            rcmId: '', // rcm文档id
            productInstNo: '', // 选中的产品
            menuList: [],
            loading: false,
            timer: null,
            instanceData: {},
            isFirstRender: true
        };
    },
    mounted() {
        this.init();
    },
    computed: {
        ...mapState({
            productList: state => {
                return state.product.productListLight || [];
            }
        }),
        rcmName() {
            return this.menuList.find(item => item.id === this.rcmId)?.name;
        }
    },
    methods: {
        ...mapActions({ getProductList: 'product/getProductListLight' }),
        ...mapActions({ getRcmProductTemplates: 'rcm/getRcmProductTemplates' }),
        // 初始化页面
        async init() {
            try {
                this.loading = true;
                await this.getProductList({
                    filter: 'supportRcm'
                });
                const productInstNo = localStorage.getItem('productInstNo');
                this.productInstNo = _.find(this.productList, ['productInstNo', productInstNo])?.productInstNo || this.productList?.[0]?.productInstNo;
            } catch (e) {
                console.error(e);
            } finally {
                this.loading = false;
            }
        },
        // 切换导航产品
        async checkProduct(item) {
            this.loading = true;
            localStorage.setItem('productInstNo', item);
            this.productInstNo = item;
            await this.getRcmInstanceList(item);
            // rcm配置跳转
            this.instanceData = this.isFirstRender ? _.find(this.menuList, ['id', this.$route.query.rcmId]) || this.menuList?.[0] : this.menuList?.[0];
            await this.$refs['menu'].initMenu(this.instanceData);
            setTimeout(() => {
                this.loading = false;
                this.isFirstRender = false;
            }, 500);
        },
        // 导航栏切换 - name对应rcm产品实例下的rcm文档id
        async selectMenuChange(item) {
            this.instanceData = item;
            this.rcmId = item?.id;
            this.$nextTick(() => {
                this.$refs['rcm-backlog'] && this.$refs['rcm-backlog'].initData();
            });
        },
        // 获取rcm实例节点列表
        async getRcmInstanceList(productInstNo) {
            try {
                const res = await getRcmInstanceList({ productId: productInstNo });
                if (res.code === '200') {
                    this.menuList = res?.data || [];
                } else {
                    this.menuList = [];
                }
            } catch (err) {
                this.menuList = [];
            }

            if (this.menuList.length <= 1) {
                // 收起菜单
                this.$refs['menu'].setMenuStatus(true);
            } else {
                this.$refs['menu'].setMenuStatus(false);
            }
        }
    }
};
</script>

<style lang="less" scoped>
@import url("@/assets/css/tab.less");
@import url("@/assets/css/input.less");
@import url("@/assets/css/menu.less");

.main {
    .title {
        min-width: 1000px;

        p {
            display: inline-block;
            color: var(--font-color);
            font-size: var(--title-font-size);
            position: absolute;
            left: 50%;
            transform: translateX(-50%);
        }

        .title-select {
            float: right;
            margin-right: 4px;
            top: 5px;
            min-width: 200px;
            width: auto;
        }
    }

    & > .apm-box {
        height: calc(100% - 58px);
        background: none;
        min-width: 1000px;

        /deep/ .menu {
            border: 0;
        }

        /deep/ .left-box {
            border-radius: 4px;

            .h-menu-item {
                padding-left: 40px;
            }
        }

        /deep/ .right-box {
            background: none;
            padding: 0 10px;
        }

        /deep/ .h-tabs-bar {
            margin-bottom: 5px;
        }

        /deep/ .h-tabs-content-wrap {
            height: calc(100% - 50px);
        }
    }
}
</style>
