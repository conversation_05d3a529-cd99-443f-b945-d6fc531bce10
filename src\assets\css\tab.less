// 自定义tab
/deep/ .h-tabs {
    height: 100%;
}

/deep/ .h-tabs-content-wrap {
    height: calc(100% - 60px);
}

/deep/.h-tabs-content {
    height: 100%;
}

/deep/ .h-tabs-tabpane {
    height: 100%;
    position: relative;
    overflow-x: hidden;
}

/deep/ .h-tabs-nav {
    margin-left: 10px;
}

/deep/ .h-tabs-nav-container {
    height: 44px;
}

/deep/ .h-tabs-bar {
    border-bottom: var(--border);
}

/deep/ .h-tabs-tab {
    padding: 12px 6px;
    color: var(--font-color);
    background: none !important;
    border: none !important;
}

/deep/ .h-tabs-tab-active {
    color: var(--link-color);
    background-color: var(--wrapper-color);
}

// 删除按钮
/deep/ .h-tabs-tab > .h-icon {
    color: var(--error-color) !important;
}

/deep/ .h-tabs-return {
    padding: 12px 5px 0 0;
}

/deep/ .h-tabs-enter,
.h-tabs-return {
    height: 100%;
    padding: 12px 5px 0 0;
}
