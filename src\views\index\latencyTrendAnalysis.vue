<template>
    <div class="main" style="display: block; position: relative;">
        <div>
            <header>
                <a-title title="链路时延趋势分析">
                    <slot>
                        <h-select
                            v-show="productList.length > 1"
                            v-model="productInstNo"
                            class="title-single-select"
                            placeholder="请选择"
                            :positionFixed="true"
                            :clearable="false"
                            @on-change="checkProduct">
                            <h-option
                                v-for="item in productList"
                                :key="item.id"
                                :value="item.productInstNo"
                                >{{ item.productName }}</h-option>
                        </h-select>
                    </slot>
                </a-title>

            </header>
            <a-loading
                v-if="loading"
                key="loading1"
                width="70"
                height="70"
                style="top: 105px;
                height: 280px;"></a-loading>
            <a-loading
                v-if="loading3"
                key="loading3"
                width="70"
                height="70"
                style="top: 1043px; height: 280px;"></a-loading>

            <div class="wrapper-box">
                <obs-title :title="obsLineTitle" />
                <div class="slot-box">
                    <!-- 日期选择器 -->
                    <h-date-picker
                        v-model="modalInfo.formItem.date"
                        class="line-box"
                        type="date"
                        format="yyyy-MM-dd"
                        placement="bottom-end"
                        placeholder="选择日期"
                        :clearable="false"
                        :disabled="loading"
                        @on-change="quickSearch">
                    </h-date-picker>

                    <!-- 选择业务类型 -->
                    <h-select
                        v-model="modalInfo.formItem.bizType"
                        class="line-box"
                        placeholder="业务类型"
                        :clearable="false"
                        :disabled="loading"
                        @on-change="quickSearchByBizType">
                        <h-option
                            v-for="item in traceModels"
                            :key="item.bizType"
                            :value="item.bizType">
                            {{ item.bizTypeAlias }}</h-option>
                    </h-select>

                    <!-- 选择链路 -->
                    <h-select
                        v-model="modalInfo.formItem.bizTraceType"
                        class="line-box"
                        placeholder="链路状态"
                        :clearable="false"
                        :disabled="loading"
                        @on-change="quickSearch">
                        <h-option
                            v-for="item in bizTraceTypes"
                            :key="item.bizTraceType"
                            :value="item.bizTraceType">
                            {{ item.bizTraceTypeAlias }}</h-option>
                    </h-select>

                    <!-- 选择跨度 -->
                    <h-select
                        v-model="modalInfo.formItem.linkSpanList"
                        class="line-box"
                        placeholder="选择跨度"
                        :disabled="loading"
                        multiple
                        :multipleNumber="4"
                        @on-change="quickSearch">
                        <h-option
                            v-for="item in spanList"
                            :key="item"
                            :value="item">
                            {{ item }}</h-option>
                    </h-select>

                    <!--度量数据类型选择-->
                    <h-select
                        v-model="modalInfo.formItem.secondType"
                        class="line-box"
                        :clearable="false"
                        @on-change="quickSearch">
                        <h-option
                            v-for="item in secondTypeList"
                            :key="item.key"
                            :value="item.key">
                            {{ item.value }}</h-option>
                    </h-select>

                    <!-- 配置按钮 -->
                    <a-button
                        type="dark"
                        icon="android-settings"
                        style="padding: 6px 10px;"
                        @click="showSettingModel" />
                </div>
                <!-- echarts -->
                <div
                    id="container"
                    class="echarts"
                    :class="`${timeStamp.length}` === 0 ? 'hidden' : ''"
                    style="width: 97.5%; height: calc(100% - 40px); margin: 0 auto;" >
                </div>
                <div
                    v-if="!traceModels.length"
                    style="position: absolute;
                    top: 42px;
                    width: 100%;
                    height: 280px;
                    background-color: var(--wrapper-color);">
                    <apm-blank
                        name="Config"
                        width="100"
                        height="70">
                        <slot>
                            <div
                                style="font-size: 12px;">当前产品尚未配置模型，请前往“产品服务配置”进行模型配置</div>
                            <a-button
                                type="primary"
                                size="small"
                                style="margin-top: 7px;"
                                @click="$hCore.navigate(`/productServiceList`);">前往配置</a-button>
                        </slot>
                    </apm-blank>
                </div>
                <div
                    v-if="traceModels.length && !timeStamp.length"
                    style="position: absolute;
                    top: 42px;
                    width: 100%;
                    height: 280px;
                    background-color: var(--wrapper-color);">
                    <no-data
                        width="120"
                        height="90"
                        text="暂无数据"
                    />
                </div>
            </div>

            <div class="wrapper-box"  style="max-height: 270px;">
                <obs-table
                    :title="tableTitle"
                    :tableData="tableData"
                    :columns="tableColumns"
                    :loading="loading2"
                    rowSelectOnly
                    :maxHeight="230"
                    @on-current-change="handleSelected"
                />
            </div>

            <div class="wrapper-box">
                <obs-title ref="histogramObsTitle" :title="obsHistogramTitle" />
                <!-- 直方图 -->
                <div id="histogram-chart" class="histogram-chart"></div>
                <div
                    v-if="!histogramAxis.length"
                    style="position: absolute;
                        top: 42px;
                        width: 100%;
                        height: 280px;
                        background-color: var(--wrapper-color);">
                    <no-data
                        width="120"
                        height="90"
                        text="暂无数据" />
                </div>
            </div>

            <div class="wrapper-box">
                <obs-title
                    ref="dotObsTitle"
                    :title="obsDotTitle"
                    @select-change="reportScreenChange" />
                <!-- 报表视图 -->
                <div id="report-chart" class="report-chart"></div>
                <div
                    v-if="!dotAxis.length || !dotXAxis.length"
                    style="position: absolute;
                        top: 42px;
                        width: 100%;
                        height: 280px;
                        background-color: var(--wrapper-color);">
                    <no-data
                        width="120"
                        height="90"
                        text="暂无数据" />
                </div>
            </div>

            <!-- 配置弹出框 -->
            <setting-modal
                v-if="modalInfo.status"
                :modalInfo="modalInfo"
                :loading="loading"
                :traceModels="traceModels"
                :bizTraceTypes="bizTraceTypes"
                :spanList="spanList"
                @setInfoData="setInfoData" />
        </div>
    </div>
</template>
<script>
import _ from 'lodash';
import * as echarts from 'echarts';
import { formatDate, autoConvertTime, chartConvertTime, formatNumber } from '@/utils/utils';
import { getTraceModels, getLatencyDistribution, getLatencyAnalysisDistribution, getLatencyAnalysisReport } from '@/api/productApi';
import { getDelayAnalysisTrendData } from '@/api/httpApi';
import settingModal from '@/components/latencyTrendAnalysis/settingModal';
import aTitle from '@/components/common/title/aTitle';
import aLoading from '@/components/common/loading/aLoading';
import obsTitle from '@/components/common/title/obsTitle';
import obsTable from '@/components/common/obsTable/obsTable';
import aButton from '@/components/common/button/aButton';
import apmBlank from '@/components/common/apmBlank/apmBlank';
import noData from '@/components/common/noData/noData';
import { mapState, mapActions } from 'vuex';
export default {
    data() {
        const that = this;
        return {
            productInstNo: '',
            obsLineTitle: {
                label: '应用分段时延趋势'
            },
            tableTitle: {
                label: '应用分段时延统计',
                slots: [{
                    type: 'text',
                    value: ' '
                }]
            },
            tableColumns: [
                {
                    minWidth: 100,
                    title: '跨度/指标',
                    key: 'span',
                    render: (h, params) => {
                        return h('span', {
                            attrs: {
                                title: this.spanLatencyDictDesc[params.row.span]
                            }
                        }, params.row.span);
                    }
                },
                {
                    minWidth: 100,
                    title: '最小（min）',
                    key: 'min',
                    align: 'right',
                    render: (h, params) => {
                        return h('span', {}, this.filterData(params.row.min));
                    }
                },
                {
                    minWidth: 105,
                    title: '中位数（p50）',
                    key: 'p50',
                    align: 'right',
                    render: (h, params) => {
                        return h('span', {}, this.filterData(params.row.p50));
                    }
                },
                {
                    minWidth: 100,
                    title: '平均（avg）',
                    key: 'avg',
                    align: 'right',
                    render: (h, params) => {
                        return h('span', {}, this.filterData(params.row.avg));
                    }
                },
                {
                    minWidth: 110,
                    title: '90分位（p90）',
                    key: 'p90',
                    align: 'right',
                    render: (h, params) => {
                        return h('span', {}, this.filterData(params.row.p90));
                    }
                },
                {
                    minWidth: 110,
                    title: '95分位（p95）',
                    key: 'p95',
                    align: 'right',
                    render: (h, params) => {
                        return h('span', {}, this.filterData(params.row.p95));
                    }
                },
                {
                    minWidth: 110,
                    title: '99分位（p99）',
                    key: 'p99',
                    align: 'right',
                    render: (h, params) => {
                        return h('span', {}, this.filterData(params.row.p99));
                    }
                },
                {
                    minWidth: 110,
                    title: '最大（max）',
                    key: 'max',
                    align: 'right',
                    render: (h, params) => {
                        return h('span', {}, this.filterData(params.row.max));
                    }
                },
                {
                    minWidth: 160,
                    title: '标准差（stdDeviation）',
                    key: 'stdDeviation',
                    align: 'right',
                    render: (h, params) => {
                        return h('span', {}, this.filterData(params.row.stdDeviation));
                    }
                },
                {
                    type: 'radio',
                    width: 120,
                    align: 'center',
                    fixed: 'right',
                    title: '查看时延分布'
                }
            ],
            obsHistogramTitle: {
                label: '应用分段时延分布'
            },
            dotValue: '',
            obsDotTitle: {
                label: '应用逐笔时延统计',
                slots: [
                    {
                        type: 'select',
                        key: 'paging',
                        options: [],
                        placeholder: '请选择查询分页'
                    }
                ]
            },
            dotOption: {
                title: {
                    text: 'Linear Regression',
                    left: 'center',
                    top: '-5',
                    textStyle: {
                        color: '#ccc'
                    }
                },
                grid: {
                    left: '20',
                    right: '20',
                    bottom: '20',
                    top: '30',
                    containLabel: true
                },
                tooltip: {
                    show: true,
                    enterable: true,
                    // triggerOn: 'click',
                    // eslint-disable-next-line max-params
                    position: function (point, params, dom, rect, size) {
                        // 鼠标坐标和提示框位置的参考坐标系是：以外层div的左上角那一点为原点，x轴向右，y轴向下
                        // 提示框位置
                        let x = 0; // x坐标位置
                        let y = 0; // y坐标位置

                        // 当前鼠标位置
                        const pointX = point[0];
                        const pointY = point[1];

                        // 提示框大小
                        const boxWidth = size.contentSize[0];
                        const boxHeight = size.contentSize[1];

                        // boxWidth > pointX 说明鼠标左边放不下提示框
                        if (boxWidth > pointX) {
                            x = 5;
                        } else {
                            // 左边放的下
                            x = pointX - boxWidth;
                        }

                        // boxHeight > pointY 说明鼠标上边放不下提示框
                        if (boxHeight > pointY) {
                            y = 5;
                        } else {
                            // 上边放得下
                            y = pointY - boxHeight;
                        }

                        return [x, y];
                    },
                    backgroundColor: 'rgba(88,94,106,0.50)',
                    borderColor: 'rgba(88,94,106,0.40)',
                    textStyle: {
                        color: '#fff',
                        fontSize: 12
                    },
                    trigger: 'axis',
                    axisPointer: {
                        type: 'cross'
                    },
                    formatter: function (obj) {
                        const data = that.indicatorsData[obj[0].dataIndex];
                        if (data.gtid) {
                            const later = !isNaN(obj[0].data[1]) ? that.filterData(obj[0].data[1]) : '-';
                            return (
                                '<div style="border-bottom: 1px solid #baf; font-size: 14px;padding-bottom: 7px;margin-bottom: 7px">' +
                                '指标详情' +
                                '</div>' +
                                'GTID' +
                                '：' +
                                (data.gtid || '-') +
                                '<br>' +
                                '资金账号' +
                                '：' +
                                (data.accountId || '-') +
                                '<br>' +
                                '交易所申报编号' +
                                '：' +
                                (data.exchangeOrderId || '-') +
                                '<br>' +
                                '柜台委托号' +
                                '：' +
                                (data.entrustNo || '-') +
                                '<br>' +
                                '时延数据' +
                                '：' +
                                (later) +
                                '<br>'
                            );
                        }
                        return;
                    }
                },
                textStyle: {
                    color: '#ccc'
                },
                dataZoom: [
                    {
                        type: 'inside',
                        start: 0,
                        end: 100
                    },
                    {
                        type: 'slider',
                        bottom: 10,
                        height: 20,
                        start: 0,
                        end: 100,
                        show: true,
                        maxValueSpan: 1000
                        // showDataShadow: 'true'
                    }
                ],
                xAxis: {
                    splitLine: {
                        lineStyle: {
                            color: '#31364a'
                        }
                    },
                    boundaryGap: false
                },
                yAxis: {
                    splitLine: {
                        lineStyle: {
                            color: '#31364a'
                        }
                    },
                    axisLabel: {
                        formatter: (val) => {
                            return chartConvertTime(val);
                        }
                    }
                },
                series: [{
                    type: 'scatter',
                    symbolSize: 7,
                    data: []
                }]
            },
            secondTypeList: [
                {
                    key: 'p50',
                    value: '中位数'
                },
                {
                    key: 'avg',
                    value: '平均值'
                }, {
                    key: 'max',
                    value: '最大值'
                }, {
                    key: 'min',
                    value: '最小值'
                }, {
                    key: 'p90',
                    value: '90分位'
                }, {
                    key: 'p95',
                    value: '95分位'
                }, {
                    key: 'p99',
                    value: '99分位'
                }],
            modalInfo: {
                status: false,
                formItem: {
                    // 公共字段
                    time: ['09:15:00', '15:00:00'],
                    date: new Date(),
                    bizType: '',
                    bizTraceType: '',
                    linkSpanList: [],
                    interval: 1000, // 间隔时间，默认单位ms
                    secondType: 'p50',
                    indicators: []
                },
                bizTags: []
            },
            loading: false,
            loading2: false,
            loading3: false,
            timeStamp: [],
            option: {
                backgroundColor: '#262b40',
                grid: {
                    left: '1%',
                    right: '25',
                    top: '5%',
                    bottom: '30',
                    containLabel: true
                },
                legend: {
                    data: []
                },
                // 提示框组件
                tooltip: {
                    trigger: 'axis',
                    enterable: true,
                    backgroundColor: 'rgba(88,94,106,0.50)',
                    borderColor: 'rgba(88,94,106,0.40)',
                    textStyle: {
                        color: '#fff',
                        fontSize: 12
                    },
                    formatter: function (params, ticket, callback) {
                        return that.generateHtmlStr(params);
                    },
                    // eslint-disable-next-line max-params
                    position: function (point, params, dom, rect, size) {
                        // 鼠标坐标和提示框位置的参考坐标系是：以外层div的左上角那一点为原点，x轴向右，y轴向下
                        // 提示框位置
                        let x = 0; // x坐标位置
                        let y = 0; // y坐标位置

                        // 当前鼠标位置
                        const pointX = point[0];
                        const pointY = point[1];

                        // 提示框大小
                        const boxWidth = size.contentSize[0];
                        const boxHeight = size.contentSize[1];

                        // boxWidth > pointX 说明鼠标左边放不下提示框
                        if (boxWidth > pointX) {
                            x = 5;
                        } else {
                            // 左边放的下
                            x = pointX - boxWidth;
                        }

                        // boxHeight > pointY 说明鼠标上边放不下提示框
                        if (boxHeight > pointY) {
                            y = 5;
                        } else {
                            // 上边放得下
                            y = pointY - boxHeight;
                        }

                        return [x, y];
                    },
                    axisPointer: {
                        type: 'cross',
                        label: {
                            backgroundColor: '#6a7985'
                        }
                    }
                },
                toolbox: {
                    top: -5
                },
                brush: {
                    toolbox: ['lineX', 'clear'],
                    xAxisIndex: 0,
                    brushMode: 'single',
                    brushType: 'lineX',
                    // transformable: false,
                    removeOnClick: true,
                    brushStyle: {
                        borderWidth: 1,
                        color: 'rgba(88,94,106,0.40)',
                        borderColor: 'rgba(88,94,106,0.40)'
                    },
                    throttleType: 'debounce',
                    throttleDelay: 600,
                    outOfBrush: {
                        colorAlpha: 0.1
                    }
                },
                xAxis: {
                    type: 'category',
                    boundaryGap: false,
                    data: [],
                    axisLabel: {
                        showMaxLabel: true
                    }
                },
                yAxis: {
                    type: 'value',
                    boundaryGap: false,
                    axisLabel: {
                        formatter: (val) => {
                            return chartConvertTime(val);
                        }
                    }
                },
                dataZoom: [
                    {
                        type: 'inside',
                        start: 0,
                        end: 100
                    },
                    {
                        type: 'slider',
                        bottom: 5,
                        height: 20,
                        start: 0,
                        end: 100,
                        show: true
                        // showDataShadow: 'true'
                    }
                ]
            },
            histogramOption: {
                grid: {
                    left: '30',
                    right: '30',
                    bottom: '20',
                    top: '30',
                    containLabel: true
                },
                textStyle: {
                    color: '#ccc'
                },
                // 提示框组件
                tooltip: {
                    trigger: 'axis',
                    enterable: true,
                    backgroundColor: 'rgba(88,94,106,0.50)',
                    borderColor: 'rgba(88,94,106,0.40)',
                    textStyle: {
                        color: '#fff',
                        fontSize: 12
                    },
                    formatter: function (params, ticket, callback) {
                        return that.generateHistogramHtml(params);
                    },
                    // eslint-disable-next-line max-params
                    position: function (point, params, dom, rect, size) {
                        // 鼠标坐标和提示框位置的参考坐标系是：以外层div的左上角那一点为原点，x轴向右，y轴向下
                        // 提示框位置
                        let x = 0; // x坐标位置
                        let y = 0; // y坐标位置

                        // 当前鼠标位置
                        const pointX = point[0];
                        const pointY = point[1];

                        // 提示框大小
                        const boxWidth = size.contentSize[0];
                        const boxHeight = size.contentSize[1];

                        // boxWidth > pointX 说明鼠标左边放不下提示框
                        if (boxWidth > pointX) {
                            x = 5;
                        } else {
                            // 左边放的下
                            x = pointX - boxWidth;
                        }

                        // boxHeight > pointY 说明鼠标上边放不下提示框
                        if (boxHeight > pointY) {
                            y = 5;
                        } else {
                            // 上边放得下
                            y = pointY - boxHeight;
                        }

                        return [x, y];
                    },
                    axisPointer: {
                        type: 'shadow'
                    }
                },
                xAxis: {
                    type: 'category',
                    data: [],
                    splitLine: {
                        lineStyle: {
                            color: '#31364a'
                        }
                    },
                    axisLabel: {
                        formatter: function(value, index) {
                            const list = value.split('-');
                            if (list[1] === '+inf') return list[1];
                            return chartConvertTime(list[1] || 0);
                        }
                    }
                    // boundaryGap: false
                },
                yAxis: {
                    type: 'value',
                    splitLine: {
                        lineStyle: {
                            color: '#31364a'
                        }
                    },
                    axisLabel: {
                        formatter: (val) => {
                            return formatNumber(val);
                        }
                    }
                },
                series: [
                    {
                        data: [],
                        type: 'bar',
                        barCategoryGap: '0%'
                        // axisTick: {
                        //     alignWithLabel: false
                        // }
                    }
                ]
            },
            coordRange: [], // 刷选区间
            brushing: false,
            tableData: [],
            traceModels: [],
            queryTime: [],
            dotAxis: [],
            dotXAxis: [],
            histogramAxis: [],
            myChart1: null,
            myChart2: null,
            myChart3: null,
            dotSpan: '',
            histogramIndex: 0,
            resizing: false,
            latencyList: []
        };
    },
    computed: {
        ...mapState({
            productList: (state) => {
                return state.product.productListLight || [];
            }
        }),
        bizTraceTypes: function() {
            const list = _.find(this.traceModels, ['bizType', this.modalInfo.formItem.bizType])?.bizTraceTypes || [];
            const newBizTraceTypes = _.filter(list, { enable: true });
            this.modalInfo.formItem.bizTraceType = newBizTraceTypes?.[0]?.bizTraceType;
            this.modalInfo.formItem.linkSpanList = newBizTraceTypes?.[0]?.spans?.slice(0, 4) || [];
            return newBizTraceTypes;
        },
        spanLatencyDictDesc: function() {
            const data = {};
            const list = _.find(this.traceModels, ['bizType', this.modalInfo.formItem.bizType])?.spans || [];
            list.forEach(ele => {
                data[ele.spanName] = ele.spanAlias;
            });
            return data;
        },
        spanList: function() {
            return _.find(this.bizTraceTypes, ['bizTraceType', this.modalInfo.formItem.bizTraceType])?.spans || [];
        }
    },
    watch: {
        productInstNo: {
            handler(newVal, oldVal) {
                if (newVal) {
                    this.getTraceModels();
                }
            },
            deep: true
        }
    },
    methods: {
        ...mapActions({ getProductList: 'product/getProductListLight' }),
        // 生成悬浮弹窗
        generateHtmlStr(params) {
            let htmlStr = '';
            for (let i = 0; i < params.length; i++) {
                const param = params[i];
                const xName = param.name;// x轴的名称
                const seriesName = param.seriesName;// 图例名称
                const value = param.value;// y轴值
                const color = param.color;// 图例颜色

                if (i === 0) htmlStr += xName + '<br/>';// x轴的名称

                htmlStr += '<div>';

                // 为了保证和原来的效果一样，这里自己实现了一个点的效果
                htmlStr += '<span style="margin-right:5px;display:inline-block;width:10px;height:10px;border-radius:5px;background-color:' + color + ';"></span>';

                // 圆点后面显示的文本
                htmlStr += seriesName + '：' + this.filterData(value);
                htmlStr += '</div>';
            }
            return htmlStr;
        },
        // 生成直方图悬浮弹窗
        generateHistogramHtml(params) {
            const data = params[0] || {};
            const style = `
            display: inline-block;
            margin-top: 5px;
            float: right;
            padding: 4px 6px;
            background: #2d8de5;
            border-radius: 4px;
            cursor: pointer;
            color: #d4d6dc;`;
            const list = data.axisValue.split('-');
            let htmlStr = '';
            htmlStr += '<div>';
            htmlStr += '委托笔数: ' + data.value + '<br/>';
            const time = list[1] === '+inf'
                ? '+inf'
                : chartConvertTime(list[1] || 0);

            htmlStr += '时延: ' +
                chartConvertTime(list[0] || 0) +
                '-' +
                time +
                '<br/>';
            if (data.value) {
                htmlStr += `<span onclick="createReport('${data.value}', ${data.dataIndex})" style="${style}">查看</span>`;
            }
            return htmlStr;
        },
        // 业务字段切换查询
        quickSearchByBizType(key) {
            const traceModel = _.find(this.traceModels, ['bizType', key]);
            this.modalInfo.bizTags = traceModel?.bizTags || [];
            // 删除不同业务类型的业务字段
            const keysToKeep = ['time', 'date', 'bizType', 'bizTraceType', 'linkSpanList', 'interval', 'secondType', 'indicators'];
            Object.keys(this.modalInfo.formItem).forEach(ele => {
                if (!keysToKeep.includes(ele)) {
                    delete this.modalInfo.formItem[ele];
                }
            });
            // 重新添加业务字段
            this.modalInfo.bizTags.forEach(ele => {
                this.$set(this.modalInfo.formItem, ele.key, ele.value);
            });
            this.quickSearch();
        },
        // 快捷查询
        quickSearch() {
            this.$nextTick(() => {
                this.getData();
            });
        },
        // 切换产品节点
        async checkProduct(e) {
            if (this.productList.length) {
                this.productInfo = e ? _.find(this.productList, ['productInstNo', e]) : this.productList[0];
                localStorage.setItem('productInstNo', this.productInstNo);
            }
        },
        // 根据产品节点获取链路模型
        async getTraceModels() {
            const res = await getTraceModels({ productId: this.productInstNo });
            this.traceModels = res?.data || [];
            if (this.traceModels.length) {
                const traceModel = this.traceModels[0];
                this.modalInfo.formItem.bizType = traceModel?.bizType;
                // this.modalInfo.bizTags = traceModel?.bizTags || [];
            }
        },
        // 初始化时延图表数据参数
        initTimeDelayParam() {
            const param = {};
            const formData = this.modalInfo.formItem;
            const paramList = ['startTime', 'endTime', ...Object.keys(formData)];
            for (const i of paramList) {
                const idx = ['startTime', 'endTime'].indexOf(i);
                param[i] = idx > -1
                    ? `${formatDate(formData.date)} ${formData.time[idx]}`
                    : formData[i];
            }
            param['indicators'] = [formData.secondType];
            param['productInstNo'] = this.productInstNo;
            delete param.date;
            delete param.time;
            return param;
        },
        // 渲染echart数据
        drawEchart(list) {
            if (!list.length) return;
            const series = [];
            this.option.legend.data = [];
            list.forEach(ele => {
                series.push({
                    name: ele.span,
                    type: 'line',
                    symbol: 'none',
                    sampling: 'lttb',
                    data: ele?.trendChart?.yaxis,
                    lineStyle: {
                        width: 1
                        // type: stu ? 'dotted' : ''
                    },
                    emphasis: {
                        lineStyle: {
                            width: 1
                        }
                    }
                });
                this.option.legend.data.push(ele.span);
            });

            // 设置y轴数据
            this.option.series = series;
            // 设置x轴数据
            this.option.xAxis.data = this.timeStamp;

            this.myChart1.setOption(this.option);
            this.myChart1.dispatchAction({
            // 刷选模式的开关。使用此 action 可将当前鼠标变为可刷选状态。 事实上，点击 toolbox 中的 brush 按钮时，就是通过这个 action，将当前普通鼠标变为刷选器的。
                type: 'takeGlobalCursor',
                // 如果想变为“可刷选状态”，必须设置。不设置则会关闭“可刷选状态”。
                key: 'brush',
                brushOption: {
                // 参见 brush 组件的 brushType。如果设置为 false 则关闭“可刷选状态”。
                    brushType: 'lineX'
                }
            });
            /* 添加 */
            this.brushing = false;
            this.myChart1.off('brush');
            this.myChart1.on('brush', param => {
                this.brushing = true;
            });
            this.myChart1.off('brushselected'); // 解绑事件处理函数（可根据情况而定是否需要，这里我这边会重绘几次表，所以需要解绑事件处理函数）。
            this.myChart1.on('brushselected', (param) => {
                if (this.resizing) return;
                this.brushing = false;
                this.myChart2 && this.myChart2.clear();
                this.myChart3 && this.myChart3.clear();
                this.coordRange = param.batch?.[0]?.areas?.[0]?.coordRange || [];
                let [x1, x2] = this.coordRange;
                let maxNum = 0;
                if (this.modalInfo.formItem.interval) {
                    maxNum = 3600000 / this.modalInfo.formItem.interval;
                }
                if ((x2 - x1) > maxNum) x2 = x1 + maxNum;
                this.getLatencyAnalysisReport(this.option.xAxis.data[x1], this.option.xAxis.data[x2]);
            });
        },
        // 获取图表数据
        getData() {
            this.resetData();
            return new Promise((resolve, reject) => {
                // 如果已经有数据在查询了，则跳过下一笔数据请求，直到第一笔查询结束
                if (this.modalInfo.status || this.loading || !this.productInstNo) return;
                this.loading = true;
                const param = this.initTimeDelayParam();
                getDelayAnalysisTrendData(param).then(res => {
                    if (res.success) {
                        const list = res.data || [];
                        this.timeStamp = list?.[0]?.trendChart?.xaxis || [];
                        this.drawEchart(list);
                        resolve(true);
                    }
                    resolve(false);
                }).catch(err => {
                    reject(err);
                    console.log(err);
                    this.$hMessage.error('时延数据查询失败！');
                }).finally(() => {
                    this.loading = false;
                });
            });
        },
        // 重置数据
        resetData() {
            this.myChart1 && this.myChart1.clear();
            this.myChart2 && this.myChart2.clear();
            this.myChart3 && this.myChart3.clear();
            this.tableData = [];
            this.tableTitle.slots[0].value = ' ';
            this.dotAxis = [];
            this.dotSpan = '';
            this.dotValue = '';
            this.timeStamp = [];
        },
        // 显示设置弹窗
        showSettingModel() {
            this.modalInfo.status = true;
        },
        // 子组件修改modalInfoData的数据
        setInfoData(obj) {
            Object.keys(obj).forEach(ele => {
                this.modalInfo.formItem[ele] = obj[ele];
            });
            this.getData();
        },
        // 获取委托跨度时延分布查询
        async getLatencyDistribution(span) {
            this.histogramAxis = [];
            try {
                const param = this.initTimeDelayParam();
                param.startTime = this.queryTime[0];
                param.endTime = this.queryTime[1];
                param.span = span;
                const res = await getLatencyDistribution(param);
                if (res.success) {
                    const list = res?.data?.distributions || [];
                    this.latencyList = list;
                    const xAxis = list.map((v, index) => {
                        return `${v.fromLatency}-${index === (list.length - 1) ? '+inf' : v.toLatency}`;
                    });
                    this.histogramAxis = xAxis;
                    const yAxis = list.map(v => {
                        return v.count;
                    });
                    this.histogramOption.xAxis.data = xAxis;
                    this.histogramOption.series[0].data = yAxis;
                }
            } catch (error) {
                console.log(error);
            }
        },
        // 生成直方图
        async initHistogramChart(span) {
            await this.getLatencyDistribution(span);
            const chartDom = document.getElementById('histogram-chart');
            this.myChart3 = echarts.init(chartDom);
            this.myChart3.setOption(this.histogramOption);
        },
        // 生成散点图
        initDotChart(span, xaxis, yaxis) {
            if (!xaxis.length || !yaxis.length) {
                this.myChart2 && this.myChart2.clear();
                return;
            };
            const list = [];
            Array.isArray(xaxis) && xaxis.forEach((ele, index) => {
                let yData = 0;
                yData = Number(yaxis[index].duration);
                list.push([ele, yData]);
            });
            if (list.length) {
                this.$nextTick(() => {
                    this.myChart2 = echarts.init(document.getElementById('report-chart'), '#262b40');
                    this.dotOption.series[0].data = list;
                    this.indicatorsData = yaxis;
                    this.dotOption.title.text = `${span} 指标详情`;
                    this.myChart2.setOption(this.dotOption, false);
                });
            }
        },
        // 获取委托分布
        async getLatencyAnalysisReport(sTime, eTime) {
            if (!sTime || !eTime) return;
            this.loading2 = true;
            try {
                const param = this.initTimeDelayParam();
                param.indicators = ['p99', 'p90', 'stdDeviation', 'avg', 'min', 'max', 'p50', 'p95'];
                const formData = this.modalInfo.formItem;
                this.queryTime = [`${formatDate(formData.date)} ${sTime}`, `${formatDate(formData.date)} ${eTime}`];
                param.startTime = this.queryTime[0];
                param.endTime = this.queryTime[1];
                const res = await getLatencyAnalysisReport(param);
                this.tableData = res?.data?.list || [];
                const count = res?.data?.count || 0;
                this.tableTitle.slots[0].value = `时间范围：${sTime} - ${eTime} 总记录笔数：${count || 0}笔`;
                if (this.tableData.length) {
                    this.tableData[0]._highlight = true;
                    this.handleSelected(this.tableData[0]);
                }
            } catch (error) {
                console.log(error);
            }
            this.loading2 = false;
        },
        // 获取逐笔分段时延
        async getLatencyAnalysisDistribution() {
            if (!this.dotSpan) return;
            this.loading3 = true;
            try {
                const param = this.initTimeDelayParam();
                param.indicators = ['p99', 'stdDeviation', 'avg', 'min', 'max', 'p50', 'p95'];
                param.startTime = this.queryTime[0];
                param.endTime = this.queryTime[1];
                param.span = this.dotSpan;
                param.page = this.dotValue;
                param.pageSize = 5000;
                param.fromLatency = this.latencyList[this.histogramIndex].fromLatency;
                param.toLatency = this.latencyList[this.histogramIndex].toLatency;
                const { data } = await getLatencyAnalysisDistribution(param);
                const yAxis = data?.trendChart?.yaxis || [];
                this.dotAxis = yAxis;
                this.dotSpan = param.span;
                this.reportScreenChange();
            } catch (error) {
                console.log(error);
            }
            this.loading3 = false;
        },
        filterData(num) {
            const data = autoConvertTime(num);
            return data.value + data.unit;
        },
        // 筛选过滤数据
        reportScreenChange(val) {
            if (val) {
                this.dotValue = val;
                this.getLatencyAnalysisDistribution();
            } else {
                if (this.tableData.length) {
                    const xAxis = [], yAxis = [];
                    let i = 0;
                    this.dotAxis.forEach(ele => {
                        i++;
                        xAxis.push(i);
                        if (ele.duration) {
                            yAxis.push(ele);
                        } else {
                            yAxis.push('');
                        }
                    });
                    this.dotXAxis = xAxis;
                    this.initDotChart(this.dotSpan, xAxis, yAxis);
                }
            }
        },
        // 生成翻页数组
        generatePageArray(num) {
            const groupSize = 5000;
            const groups = Math.ceil(num / groupSize);

            return Array.from({ length: groups }, (_, i) => {
                const start = i * groupSize + 1;
                const end = Math.min((i + 1) * groupSize, num);
                return {
                    label: `${start}-${end}`,
                    value: i + 1
                };
            });
        },
        // 选中跨度指标
        handleSelected(row) {
            this.myChart2 && this.myChart2.clear();
            this.dotAxis = [];
            if (row.span) {
                this.dotSpan = row.span;
                this.initHistogramChart(row.span);
                this.obsHistogramTitle.label = `应用分段时延分布（${row.span}）`;
            } else {
                this.dotSpan = '';
            }
        },
        resize() {
            this.resizing = true;
            this.myChart1 && this.myChart1.resize();
            this.myChart2 && this.myChart2.resize();
            this.myChart3 && this.myChart3.resize();
            setTimeout(() => {
                this.resizing = false;
            }, 1500);
        }
    },
    async mounted() {
        await this.getProductList({ filter: 'supportEntrustLatency' });
        if (this.productList.length) {
            const id = localStorage.getItem('productInstNo');
            this.productInstNo =  _.find(this.productList, ['productInstNo', id])?.productInstNo ||
                this.productList[0].productInstNo;
        }
        this.$nextTick(() => {
            this.myChart1 = echarts.init(document.getElementById('container'), 'dark');
        });
        window.createReport = (count, index) => {
            this.obsDotTitle.slots[0].options = [];
            this.$refs['dotObsTitle'].setSelectVal('paging', '');
            this.myChart2 && this.myChart2.clear();
            this.$nextTick(() => {
                this.dotAxis = [];
                this.histogramIndex = index || 0;
                let [x1, x2] = this.coordRange;
                let maxNum = 0;
                if (this.modalInfo.formItem.interval) {
                    maxNum = 3600000 / this.modalInfo.formItem.interval;
                }
                if ((x2 - x1) > maxNum) x2 = x1 + maxNum;
                this.obsDotTitle.slots[0].options = [...this.generatePageArray(count)];
                this.$refs['dotObsTitle'].setSelectVal('paging', 1);
            });
        };
        window.addEventListener('resize', this.resize);
    },
    beforeDestroy() {
        window.removeEventListener('resize', this.resize);
    },
    components: { settingModal, aTitle, aButton, noData, obsTitle, obsTable, aLoading, apmBlank }
};
</script>

<style lang="less" scoped>
@import "@/assets/css/input";

.wrapper-box {
    position: relative;
    width: 100%;
    height: 320px;
    margin-top: 10px;
    background-color: var(--wrapper-color);
    border-radius: var(--border-radius);

    & > .obs-title {
        position: relative;
        width: 100%;
        height: 42px;
        padding: 0 0 0 20px;
        border-radius: var(--border-radius);
        font-size: var(--title-font-size);
        line-height: 42px;
    }

    & > .slot-box {
        // display: flex;
        height: 40px;
        justify-content: center;
        align-items: center;
        position: absolute;
        top: 5px;
        right: 10px;

        & > .line-box {
            width: 140px;
        }
    }

    /deep/ .h-checkbox-inner {
        border: 1px solid #d7dde4;
        border-radius: 2px;
        background-color: #fff;
    }

    & > .report-chart {
        width: 100%;
        height: 280px;
    }

    & > .histogram-chart {
        width: 100%;
        height: 280px;
    }
}
</style>
