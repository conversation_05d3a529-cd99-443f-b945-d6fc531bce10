import { IndexedDB } from './indexedDB';

let dbInstance = null;
let dbReadyPromise = null;

// 表创建 注意version
export function getDBInstance() {
    if (dbInstance) return Promise.resolve(dbInstance);
    if (dbReadyPromise) return dbReadyPromise;
    dbReadyPromise = new Promise((resolve, reject) => {
        // dbInstance = new IndexedDB('APM_DB', ['management-query', 'management-jsonpath'], 2, (db) => {
        //     resolve(db);
        // });
        dbInstance = new IndexedDB('APM_DB', ['management-query'], 1, (db) => {
            resolve(db);
        });
    });
    return dbReadyPromise;
}
