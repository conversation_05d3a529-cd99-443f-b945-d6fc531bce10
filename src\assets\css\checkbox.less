// checkbox 覆盖部分input文件中公共样式

/deep/ .h-checkbox + span {
    color: #cacfd4;
}

/deep/ .h-checkbox-inner {
    border: 1px solid #485565;
    border-radius: 2px;
    background: var(--input-bg-color);
}

/deep/ .h-checkbox-inner::after {
    border: none;
    content: "";
    display: table;
    width: 4px;
    height: 8px;
    position: absolute;
    top: 1px;
    left: 4px;
    border-top: 0;
    border-left: 0;
}

/deep/ .h-checkbox-indeterminate .h-checkbox-inner {
    border-color: var(--link-color) !important;
    background: var(--link-color) !important;

    &::after {
        border: 1px solid #fff;
        content: "";
        width: 8px;
        height: 1px;
        transform: scale(1);
        position: absolute;
        left: 2px;
        top: 5px;
    }
}
