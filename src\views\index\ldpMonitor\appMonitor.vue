<template>
    <div ref="wrapper" class="grid-box">
        <grid-layout
            :layout.sync="layout"
            :col-num="colNum"
            :row-height="rowHeight"
            :is-draggable="false"
            :is-resizable="false"
            :is-mirrored="false"
            :vertical-compact="true"
            :margin="[10, 10]"
            :use-css-transforms="true">
            <grid-item
                v-for="(item, idx) in layout"
                :key="item.i"
                class="service-box"
                :x="item.x"
                :y="item.y"
                :w="item.w"
                :h="item.h"
                :i="item.i">
                <business-box ref="business" :panel="panels[idx]" :placement="item.x < colNum/2 ? 'right' : 'left'" />
            </grid-item>
        </grid-layout>
        <a-loading v-if="loading" style="width: calc(100% - 12px); height: calc(100% - 110px);"></a-loading>
    </div>
</template>
<script>
import { GridLayout, GridItem } from 'vue-grid-layout';
import { isDivisible, getObjByArray } from '@/utils/utils';
import { getMonitorDashboards, getMonitorApplications, getMonitorHeartbeats } from '@/api/httpApi';
import aLoading from '@/components/common/loading/aLoading';
import businessBox from '@/components/ldpProduct/businessBox/businessBox';
export default {
    props: {
        productInfo: {
            type: Object,
            default: () => { return {}; }
        },
        productInstNo: {
            type: String,
            default: ''
        }
    },
    data() {
        return {
            layout: [],
            panels: [],
            rowHeight: 0,
            colNum: 12,
            layoutStu: true,
            timer: null,
            loading: false
        };
    },
    async mounted() {
        this.$init();
        this.$nextTick(() => {
            this.rowHeight = parseInt(this.$refs.wrapper.clientHeight / 10 - 5 * 2, 10);
        });
        window.addEventListener('resize', this.resetLayout);
    },
    watch: {
        productInstNo() {
            this.$init();
        }
    },
    methods: {
        // 初始化
        async $init() {
            this.loading = true;
            const panels = await this.getMonitorDashboards();
            const appObj = await this.getMonitorApplications();
            this.loading = false;
            if (!appObj || !panels) return;
            this.generateLayout(panels, appObj);
            this.generateStatus();
            this.timer && clearInterval(this.timer);
            this.timer = setInterval(() => {
                this.generateStatus();
            }, 3000);
        },
        // 重置layout
        resetLayout() {
            this.layoutStu = false;
            this.$nextTick(() => {
                this.rowHeight = parseInt(this.$refs.wrapper.clientHeight / 10 - 5 * 2, 10);
                this.layoutStu = true;
            });
        },
        // 获取模版接口
        getMonitorDashboards() {
            const param = {
                productId: this.productInfo.id,
                scene: 'appStatus'
            };
            return new Promise((resolve, reject) => {
                getMonitorDashboards(param).then(res => {
                    let panels = [];
                    if (res.success) {
                        panels = res.data?.dashboard?.panels || [];
                        resolve(panels);
                    } else {
                        resolve(false);
                    }
                }).catch(error => {
                    reject(error);
                    console.error(error);
                });
            });
        },
        // 获取应用实例列表
        getMonitorApplications() {
            const param = {
                productId: this.productInfo.id
                // productId: '5xohNYYBJUkmHzi9-Rys'
            };
            return new Promise((resolve, reject) => {
                getMonitorApplications(param).then(res => {
                    let data = {};
                    if (res.success && Array.isArray(res.data)) {
                        data = getObjByArray(res.data, 'baseInfo', 'instanceName');
                        resolve(data);
                    } else if (
                        resolve(false)
                    );
                }).catch(error => {
                    reject(error);
                    console.error(error);
                });
            });

        },
        // 获取心跳数据
        getMonitorHeartbeats() {
            const param = {
                productId: this.productInfo.id,
                type: 'instance'
            };
            return new Promise((resolve, reject) => {
                getMonitorHeartbeats(param).then(res => {
                    let data = {};
                    if (res.success && Array.isArray(res.data)) {
                        data = getObjByArray(res.data, 'id');
                    }
                    resolve(data);
                }).catch(error => {
                    this.timer && clearInterval(this.timer);
                    reject(error);
                    console.error(error);
                });
            });
        },
        // 生成模型
        generateLayout(panels, appObj) {
            this.colNum = isDivisible(panels.length);
            const w = this.colNum / panels.length;
            this.layout = [];
            this.panels = [];
            panels.forEach((item, index) => {
                this.layout.push({
                    x: index * w,
                    y: 0,
                    w,
                    h: 10,
                    i: item.id.toString()
                });
                item.content = JSON.parse(item.content);
                this.panels.push({
                    ...item
                });
            });
            this.panels.forEach(item => {
                Array.isArray(item.content) && item.content.forEach((element, index) => {
                    Array.isArray(element.nodes) && element.nodes.forEach(ele => {
                        ele.target = { ...ele.target, ...appObj[ele?.target?.instanceName] };
                    });
                });
            });
        },
        // 往模型中加入节点状态
        async generateStatus() {
            const appStus = await this.getMonitorHeartbeats();
            this.panels.forEach(item => {
                Array.isArray(item.content) && item.content.forEach((element, index) => {
                    Array.isArray(element.nodes) && element.nodes.forEach(ele => {
                        ele.target.runningInfo = { ...ele.target.runningInfo, ...appStus[ele?.target?.instanceName] };
                    });
                });
            });
        }
    },
    components: { GridLayout, GridItem, businessBox, aLoading },
    beforeDestroy() {
        this.timer && clearInterval(this.timer);
        window.removeEventListener('resize', this.resetLayout);
    }
};
</script>
<style lang="less" scoped>
.grid-box {
    width: calc(100% - 33px);
    height: 100%;
    position: relative;

    .service-box {
        border-radius: 8px;
        box-shadow: 0 1px 25px 0 rgba(58, 144, 247, 0.42) inset;
    }

    .app-grid-title {
        padding-left: 15px;
        color: #fff;
        font-size: 14px;
    }

    .app-grid-title::before {
        display: inline-block;
        position: relative;
        left: -10px;
        top: 2px;
        content: "";
        width: 4px;
        height: 15px;
        background: var(--link-color);
    }
}
</style>
