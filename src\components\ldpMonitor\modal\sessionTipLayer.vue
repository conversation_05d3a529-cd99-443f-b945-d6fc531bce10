<template>
    <!-- 会话信息浮层盒子 -->
    <div v-if="showTipBox"
        ref="tipBox"
        class="tip-box"
        :style="tipBoxStyle">
        <h-table
            :columns="tipColumns"
            :data="tipTableData"
            :loading="tipLoading"
            height="180">
        </h-table>

        <!-- 三角标 -->
        <div class="tip-arrow"
            :style="tipArrowStyle">
        </div>
    </div>
</template>

<script>
import { getTopoSessions } from '@/api/topoApi';
export default {
    name: 'SessionTipLayer',
    props: {
        productId: {
            type: String,
            default: ''
        }
    },
    computed: {
        connectStatusDict() {
            return this.$store?.state?.apmDirDesc?.connectStatusDict || {};
        }
    },
    data() {
        return {
            showTipBox: false,
            tipBoxStyle: {},
            tipArrowStyle: {},

            tipColumns: [
                {
                    title: '发送方插件实例',
                    key: 'senderNode'
                },
                {
                    title: '发送方地址',
                    key: 'senderAddr',
                    minWidth: 120,
                    formatMethod: row => row?.senderAddr || '-'
                },
                {
                    title: '连接方式',
                    key: 'connectType'
                },
                {
                    title: '接收方地址',
                    key: 'receiverAddr',
                    minWidth: 120,
                    formatMethod: row => row?.senderAddr || '-'
                },
                {
                    title: '接收方插件实例',
                    key: 'receiverNode'
                },
                {
                    title: '连接状态',
                    key: 'connectStatus',
                    formatMethod: (row) => this.connectStatusDict[row.connectStatus]
                }
            ],
            tipTableData: [],
            tipLoading: false
        };
    },
    beforeDestroy() {
        document.removeEventListener('click', this.hideTipBox);
    },
    methods: {
        // 打开浮层
        handleShowTipBox(event, row) {
            // 获取点击的元素
            const targetElement = event.currentTarget || event.target;

            // 获取元素的边界框
            const boundingRect = targetElement.getBoundingClientRect();

            // 计算中心点位置
            const centerX = boundingRect.left + (boundingRect.width / 2);
            const centerY = boundingRect.top + (boundingRect.height / 2);

            // 调用 getTipBoxPosition 方法显示提示框
            this.getTipBoxPosition(centerX, centerY);

            // 获取数据展示
            this.showTipLayerInfo(row);

            document.addEventListener('click', this.hideTipBox);
        },
        // 展示浮层信息
        async showTipLayerInfo(row) {
            // 显示 tip box
            this.showTipBox = true;
            // 获取展示数据
            this.tipLoading = true;
            this.tipTableData = await this.getTopoSessions(row);
            this.tipLoading = false;
        },
        // 隐藏浮层
        hideTipBox(event) {
            if (this.$refs.tipBox && !this.$refs.tipBox.contains(event.target)) {
                this.showTipBox = false;
                document.removeEventListener('click', this.hideTipBox);
            }
        },
        // 根据图标位置计算浮层位置
        getTipBoxPosition(x, y) {
            const boxWidth = 800;
            const boxHeight = 200;
            const verticalGap = 20; // 上下间距调整为20px

            // 计算初始定位
            let top = y - boxHeight - verticalGap;     // 正上方
            let left = x - (boxWidth / 2); // 居中

            // 调整水平边界（防止左右溢出）
            left = Math.max(0, Math.min(left, window.innerWidth - boxWidth));

            // 判断上方空间是否足够
            if (top < 0) {
                // 上方空间不足，尝试放置在下方（假设y是图标底部坐标）
                top = y + verticalGap;
            }

            // 确保垂直方向不溢出（处理上下边界）
            top = Math.max(0, Math.min(top, window.innerHeight - boxHeight));

            // 计算三角标的位置
            const arrowLeft = x - left - 10; // -10 是为了让三角形居中

            this.tipBoxStyle = {
                top: `${top}px`,
                left: `${left}px`
            };

            this.tipArrowStyle = {
                left: `${arrowLeft}px`
            };
        },
        // 获取topo会话信息
        async getTopoSessions(row) {
            let data = [];
            try {
                const params = {
                    productId: this.productId,
                    type: row.type,
                    senderNode: row.senderNodeId,
                    receiverNode: row.receiverNodeId,
                    connectType: row.connectType,
                    ...(row.clusterId && { clusterId: row.clusterId })
                };
                const res = await getTopoSessions(params);
                if (res.code === '200') {
                    data = res?.data || [];
                } else if (res.code?.length === 8) {
                    this.$hMessage.error(res.message);
                }
            } catch (err) {
                console.error(err);
            }

            return data;
        }
    }
};
</script>

<style scoped lang="less">
.tip-box {
    /* stylelint-disable-next-line plugin/z-index-value-constraint */
    z-index: 1003; /* 确保它位于前面，可以根据需要调整 */
    position: fixed;
    height: 200px;
    width: 800px;
    border-radius: 4px;
    background: var(--poptip-bg-color);

    // 改写表格样式
    /deep/ .h-table-wrapper {
        border: none;
        margin: 5px;
    }

    /deep/ .h-table::after {
        display: none;
    }

    /deep/ .h-table::before {
        display: none;
    }

    /deep/ .h-table,
    /deep/ .h-table-body {
        background: transparent;
        color: var(--font-color);
    }

    /deep/ .h-table .h-table-header {
        background: var(--poptip-line-color);
    }

    /deep/ .h-table th {
        background: transparent;
        color: var(--font-color);
        border: none;
        height: 28px;
    }

    /deep/ .h-table td {
        background: var(--poptip-bg-color);
        color: var(--font-color);
        border-color: #626971;
        height: 28px;
    }

    /deep/ .h-table-body::-webkit-scrollbar-thumb {
        border-radius: 6px;
        background-color: #757881;
    }

    /deep/ .h-spin-fix {
        border: none;
        background: #525965;
    }
}

.tip-arrow {
    position: absolute;
    width: 0;
    height: 0;
    border-left: 8px solid transparent;
    border-right: 8px solid transparent;
    border-top: 8px solid var(--poptip-bg-color);
    bottom: -7px; /* 三角标在盒子底部 */
}
</style>
