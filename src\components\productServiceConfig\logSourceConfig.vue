<template>
    <div ref="tab-box" class="tab-box">
        <obs-table
            ref="table"
            :title="tableTitle"
            :tableData="tableData"
            :columns="columns"
            :loading="tableLoading"
            :height="tableHeight"
            showTitle
            border
            @query="queryLogSource">
            <template slot="extraTitleBox">
                <div class="slots-box">
                    <div class="title-tip">
                        当前已配置链路模型:
                        <h-poptip
                            v-if="traceModels.length"
                            title="链路模型"
                            trigger="hover"
                            customTransferClassName="apm-poptip monitor-poptip"
                            autoPlacement
                            placement="right"
                            transfer>
                                <div class="model-count">{{ traceModels.length }}</div>
                                <!-- 内容插槽 -->
                                <div slot="content" class="pop-content" >
                                    <div v-for="model in traceModels" :key="model.type">
                                        {{ model.type }}：<span>{{ model.value }}</span>
                                    </div>
                                </div>
                        </h-poptip>
                        <span v-else class="model-count">{{ traceModels.length }}</span>
                    </div>

                    <div class="title-operate">
                        <a-button
                            type="primary"
                            :loading="btnLoading"
                            @click="handleSync">同步配置
                        </a-button>
                        <a-button
                            class="add-button"
                            type="dark"
                            @click="handleAdd">添加
                        </a-button>
                        <h-input
                            v-model="searchText"
                            class="search-input"
                            placeholder="应用节点名"
                            clearable
                            icon="search"
                            @on-blur="handleSearch"
                            @on-enter="handleSearch" />
                    </div>
                </div>
            </template>
        </obs-table>
        <a-loading v-if="loading"></a-loading>

        <!-- 添加链路日志配置 -->
        <add-log-source-modal
           v-if="modalInfo.status"
           :modalInfo="modalInfo"
           @query-log-config="queryLogSource">
        </add-log-source-modal>
    </div>
</template>

<script>
import obsTable from '@/components/common/obsTable/obsTable';
import aButton from '@/components/common/button/aButton';
import aLoading from '@/components/common/loading/aLoading';
import addLogSourceModal from '@/components/productServiceConfig/modal/addLogSourceModal.vue';
import { queryLogSource, syncLogSource, delLogSource, getProductLatencyTraceModels } from '@/api/productApi';

export default {
    name: 'LogSourceConfig',
    components: { obsTable, aButton, aLoading, addLogSourceModal },
    props: {
        productInfo: {
            type: Object,
            default: () => {}
        }
    },
    computed: {
        // 获取应用类型字典 `appTypeDictDesc`，
        appTypeDictDesc() {
            return this.$store?.state?.apmDirDesc?.appTypeDictDesc || {};
        }
    },
    data() {
        return {
            traceModels: [],
            tableTitle: {
                label: '链路日志配置'
            },
            tableHeight: 0,
            searchText: '',
            tableData: [],
            allTableData: [],
            columns: [
                {
                    title: '应用节点名',
                    key: 'instanceName',
                    minWidth: 120,
                    ellipsis: true
                },
                {
                    title: '应用节点类型',
                    key: 'instanceType',
                    minWidth: 120,
                    ellipsis: true,
                    render: (h, { row }) => {
                        const instanceTypeName = this.appTypeDictDesc?.[row.instanceType] || row.instanceType;
                        return h('div', {
                            class: 'h-table-cell-ellipsis',
                            attrs: {
                                title: instanceTypeName
                            }
                        }, instanceTypeName);
                    }
                },
                {
                    title: '服务器',
                    key: 'serverIp',
                    minWidth: 120,
                    ellipsis: true
                },
                {
                    title: '日志类型',
                    key: 'logType',
                    minWidth: 100,
                    ellipsis: true
                },
                {
                    title: '时延日志输出目录',
                    key: 'logDir',
                    minWidth: 240,
                    ellipsis: true
                },
                {
                    title: '时延日志关键字',
                    key: 'logNameKeyWord',
                    minWidth: 160,
                    ellipsis: true
                },
                {
                    title: '操作',
                    key: 'action',
                    width: 80,
                    fixed: 'right',
                    render: (h, params) => {
                        return h('div', [
                            h('Button',
                                {
                                    props: {
                                        size: 'small',
                                        type: 'text'
                                    },
                                    style: {
                                        padding: 0
                                    },
                                    on: {
                                        click: () => {
                                            this.handleDelete(params.row);
                                        }
                                    }
                                },
                                '删除'
                            )
                        ]);
                    }
                }
            ],
            loading: false,
            tableLoading: false,
            btnLoading: false,
            modalInfo: {
                status: false
            }
        };
    },
    mounted() {
        this.fetTableHeight();
        window.addEventListener('resize', this.fetTableHeight);
    },
    beforeDestroy() {
        window.removeEventListener('resize', this.fetTableHeight);
    },
    methods: {
        async initData() {
            this.loading = true;
            try {
                await this.getProductLatencyTraceModels();
                await this.queryLogSource();
            } catch (err) {
                console.error(err);
            }
            this.loading = false;
        },
        // 设置table高度
        fetTableHeight() {
            this.tableHeight = this.$refs['tab-box']?.offsetHeight - 62;
        },
        /**
         * 添加链路日志配置
         */
        handleAdd() {
            this.modalInfo.status = true;
            this.modalInfo.productId = this.productInfo.id;
        },
        /**
         * 同步配置
         */
        async handleSync() {
            this.btnLoading = true;
            try {
                const param = {
                    productId: this.productInfo.id
                };
                const res = await syncLogSource(param);
                if (res.code === '200') {
                    this.$hMessage.success('同步配置成功');
                    await this.queryLogSource();
                } else if (res.code.length === 8) {
                    this.$hMessage.error(res.message);
                }
            } catch (err) {
                console.error(err);
            }
            this.btnLoading = false;
        },
        /**
         * 输入应用名搜索配置列表
         * 1. 搜索值为空时，展示全部数据
         * 2. 搜索值存在时，模糊匹配
         */
        handleSearch() {
            if (!Array.isArray(this.allTableData)) {
                this.tableData = [];
                return;
            }

            if (this.searchText) {
                // 模糊匹配搜索值，并更新筛选后的数据
                const filteredData = this.allTableData.filter(item =>
                    item.instanceName.includes(this.searchText)
                );
                this.tableData = filteredData;
            } else {
                // 搜索值为空时，展示全部数据
                this.tableData = this.allTableData;
            }
        },
        /**
         * 查询日志源信息更新表格数据
         */
        async queryLogSource() {
            this.tableLoading = true;
            try {
                const param = {
                    productId: this.productInfo.id
                };
                const res = await queryLogSource(param);
                if (res.code === '200') {
                    this.allTableData = res.data || [];
                } else if (res.code.length === 8) {
                    this.$hMessage.error(res.message);
                    this.allTableData = [];
                }
            } catch (err) {
                this.allTableData = [];
            } finally {
                this.tableLoading = false;
                this.handleSearch();
            }
        },
        /**
         * 删除日志配置
         */
        handleDelete(row) {
            this.$hMsgBoxSafe.confirm({
                title: `删除`,
                content: `您确认要删除"${row.instanceName}"的链路日志配置吗？`,
                onOk: async () => {
                    // 调用接口
                    const param = {
                        id: row.id
                    };
                    const res = await delLogSource(param);
                    if (res.code === '200') {
                        this.$hMessage.success('删除成功');
                        this.queryLogSource();
                    } else if (res.code.length === 8) {
                        this.$hMessage.error(res.message);
                    }
                }
            });
        },
        /**
         * 查询产品时延链路模型配置
         */
        async getProductLatencyTraceModels() {
            let modelsData = [];
            const param = {
                productId: this.productInfo.id
            };
            const res = await getProductLatencyTraceModels(param);
            if (res?.code === '200') {
                modelsData = res?.data || [];
            }
            const bizTypeDict = this.$store?.state?.apmDirDesc?.bizTypeDict || {};
            // 模型数据转换
            const traceModels = [];
            modelsData.forEach(ele => {
                if (ele.enableTraceModelId) {
                    const valName = (ele.traceModels || []).find(o => o.traceModelId === ele.enableTraceModelId)?.traceModelName;
                    traceModels.push({
                        type: bizTypeDict?.[ele.bizType] || ele.bizType,
                        value: valName
                    });
                }
            });
            this.traceModels = traceModels;
        }
    }
};
</script>

<style lang="less" scoped>
@import url("@/assets/css/poptip-1.less");

.pop-content {
    div {
        line-height: 22px;
        color: var(--font-body-color);

        span {
            color: var(--font-color);
        }
    }
}

.tab-box {
    position: relative;
    width: 100%;
    height: calc(100% - 15px);
}

.slots-box {
    width: calc(100% - 108px);
    display: inline-flex;
    justify-content: space-between;
    padding-left: 15px;

    .title-tip {
        height: 20px;
        line-height: 20px;
        font-size: 12px;
        padding: 0 5px;
        color: var(--font-body-color);

        .model-count {
            color: var(--font-color);
            padding-right: 5px;

            &:hover {
                cursor: pointer;
            }
        }
    }

    .title-operate {
        margin-top: -10px;
    }

    .add-button {
        margin: 0 3px;
    }

    .search-input {
        width: 200px;
        margin-right: 10px;
    }
}
</style>
