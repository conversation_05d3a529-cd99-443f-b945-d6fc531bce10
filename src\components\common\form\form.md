## Form 表单

基于 HUI Form 封装适用于 SEE 项目的公共表单组件，分为：apm-form。
[http://hui.hundsun.com/](http://hui.hundsun.com/)

#### Props

| **属性**  | **说明**                                                                                                                                                                                                                       | **类型**                | **默认值** |
| --------- | ------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------ | ----------------------- | ---------- |
| formItems | 表单内容,①type 可选值:input,select,date,datetime,daterange,rimerange,timescreen 等;② 新增字段：labelWidth:自定义 label 值宽度;editable:仅日期选择器文本框是否可以输入，只在没有使用 slot 时有效;placement:日期选择器出现的位置 | Array(具体参数参考 HUI) | []         |

#### Methods

| **方法名**     | **说明**                                    | **用法/参数**          |
| -------------- | ------------------------------------------- | ---------------------- |
| init           | 初始化                                      | /                      |
| reset          | 重置表单                                    | this.$refs.xxx.reset() |
| query          | 返回表单数据                                | this.$refs.xxx.query() |
| fieldValueBlur | 输入框失去焦点时触发,仅支持 type:timescreen | /                      |

#### 示例程序

```vue
<template>
  <div class="main">
    <a-form :formItems="formItems"></a-form>
  </div>
</template>

<script>
import aForm from '@/components/common/form/aForm';
export default {
  data() {
    return {
      formItems: [
        {
          key: 'abc', // 对应表单域model里的字段，可用于校验
          value: 'haha', // 可选，表单项初始值
          type: 'input', // 表单元素类型
          label: 'input', // 标签文本
          placeholder: '...', // 可选，提示内容
          labelWidth: 80, // 自定义label宽度
          required: true // 可选，默认false，表单项是否必填
        },
        {
          key: 'bcd', // 对应表单域model里的字段，可用于校验
          type: 'select', // 表单元素类型
          label: 'select', // 标签文本
          labelWidth: 100, // 自定义label宽度
          required: true, // 可选，默认false，表单项是否必填
          options: [
            { value: 'item1', label: '选项1' },
            { value: 'item2', label: '选项2' },
            { value: 'item3', label: '选项3' }
          ]
        },
        {
          key: 'cde', // 对应表单域model里的字段，可用于校验
          type: 'selectSearch', // 表单元素类型
          label: 'selectSearch', // 标签文本
          labelWidth: 100, // 自定义label宽度
          loading: false, // 搜索加载状态
          remoteMethod: () => {}, // 远程搜索方法
          options: [
            { value: 'item1', label: '选项1' },
            { value: 'item2', label: '选项2' },
            { value: 'item3', label: '选项3' }
          ]
        },
        {
          key: 'edc',
          type: 'date', // 表单元素类型
          label: 'date', // 标签文本
          editable: true, // 可选，文本框是否可以输入
          placement: 'bottom-start' // 可选，选择器出现的方向(默认值：bottom-start)
        },
        {
          key: '123',
          type: 'daterange', // 表单元素类型
          label: 'daterange', // 标签文本
          editable: true, // 可选，文本框是否可以输入
          placement: 'bottom-start' // 可选，选择器出现的方向(默认值：bottom-start)
        },
        {
          key: '234',
          type: 'dateTime', // 表单元素类型
          label: 'dateTime', // 标签文本
          editable: true, // 可选，文本框是否可以输入
          placement: 'bottom-start' // 可选，选择器出现的方向(默认值：bottom-start)
        },
        {
          key: '345',
          type: 'timerange', // 表单元素类型
          label: 'timerange', // 标签文本
          editable: true, // 可选，文本框是否可以输入
          placement: 'bottom-start' // 可选，选择器出现的方向(默认值：bottom-start)
        },
        {
          key: '456',
          type: 'timescreen', // 表单元素类型
          label: 'timescreen', // 标签文本
          editable: true, // 可选，文本框是否可以输入
          placement: 'bottom-start', // 可选，选择器出现的方向(默认值：bottom-start)
          options: [
            { value: 'item1', label: '选项1' },
            { value: 'item2', label: '选项2' },
            { value: 'item3', label: '选项3' }
          ]
        }
      ]
    };
  },
  methods: {
    // 重置表单
    resetFrom() {
      this.$refs['form'].reset();
    },
    // 返回表单数据 - 查询参数
    queryParam() {
      const param = this.$refs['form'].query();
      return param;
    }
  },
  components: { aForm }
};
</script>
```
