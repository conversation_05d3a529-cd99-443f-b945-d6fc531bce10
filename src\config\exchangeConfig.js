export const businessList = [
    {
        label: '1',
        value: '证券'
    },
    {
        label: '2',
        value: '两融'
    },
    {
        label: '3',
        value: '期权'
    },
    {
        label: '4',
        value: '期货'
    },
    {
        label: '5',
        value: '风控'
    },
    {
        label: '6',
        value: '行情'
    }
];

export const exchangeList = [
    {
        label: 'SSE',
        value: '上海证券交易所',
        marketValue: '上交所行情时延监控'
    },
    {
        label: 'SZSE',
        value: '深圳证券交易所',
        marketValue: '深交所行情时延监控'
    },
    {
        label: 'CZCE',
        value: '郑州商品交易所'
    },
    {
        label: 'DCE',
        value: '大连商品交易所'
    },
    {
        label: 'SHFE',
        value: '上海期货交易所'
    },
    {
        label: 'CFFEX',
        value: '中国金融期货交易所'
    },
    {
        label: 'INE',
        value: '上海国际能源交易中心股份有限公司'
    }
];

export const rangeList = [
    {
        label: 'ALL_DAY',
        value: '全天'
    },
    {
        label: 'MORNING',
        value: '上午'
    },
    {
        label: 'AFTERNOON',
        value: '下午'
    },
    {
        label: 'OTHER',
        value: '自定义'
    }
];

export const loopList = [
    {
        value: 'Rsp',
        label: '核心应答'
    },
    {
        value: 'RtnCfmMbr',
        label: '核心确认'
    },
    {
        value: 'RtnCfmXchg',
        label: '交易所确认'
    },
    {
        value: 'RtnTrd',
        label: '交易所成交'
    }
];

// 行情系统时延链路列表
export const marketLoopList = [
    {
        label: 'NSQ链路',
        value: 'NSQ'
    },
    {
        label: 'FPGA链路',
        value: 'FPGA'
    }
];

// 数据类型
export const checkList = [
    {
        label: 'SM',
        name: '快照行情'
    },
    {
        label: 'IM',
        name: '指数行情'
    },
    {
        label: 'OBOE',
        name: '逐笔委托'
    },
    {
        label: 'TYT',
        name: '逐笔成交'
    }
];

// span链路枚举
export const spanList = [
    {
        name: '委托全链路时延',
        span: 'C1-C2'
    },
    {
        name: '系统内部全链路时延',
        span: 'L1-L4'
    },
    {
        name: '委托上行时延',
        span: 'C1-L2'
    },
    {
        name: '系统内部上行时延',
        span: 'L1-L2'
    },
    {
        name: '交易所全链路时延',
        span: 'L2-L3'
    },
    {
        name: '系统内部下行时延',
        span: 'L3-L4'
    },
    {
        name: '委托下行时延',
        span: 'C2-L3'
    }
];

// 应用配置列表
export const instanceTypeList = [
    {
        key: 'default',
        name: '默认'
    },
    {
        key: 'Cli',
        name: '客户端'
    },
    {
        key: 'Cli,Api',
        name: '策略客户端'
    },
    {
        key: 'Front',
        name: '前置机'
    },
    {
        key: 'Core',
        name: '核心'
    },
    {
        key: 'Offer',
        name: '报盘'
    },
    {
        key: 'Core,Offer',
        name: '交易核心和报盘'
    },
    {
        key: 'tgw',
        name: '交易所网关'
    },
    {
        key: 'mdgw',
        name: '深交所网关'
    },
    {
        key: 'ToDb',
        name: 'todb'
    },
    {
        key: 'fpga',
        name: 'FPGA行情解码器'
    },
    {
        key: 'ArcsFront',
        name: '风控前置'
    },
    {
        key: 'Arcs',
        name: '风控核心'
    },
    {
        key: 'Swap',
        name: '收益互换核心'
    }
];

// 时延字典表
export const timeLineConfig = {
    'c1-c2': '终端总时延',
    'api1-api2': '客户端上行时延',
    'api3-api4': '客户端下行时延',
    'api1-api4': '客户端总时延',
    'api2-api3': '客户端下游处理时延',
    'sw1-sw2': '收益互换核心上行时延',
    'sw3-sw4': '收益互换核心下行时延',
    'sw1-sw4': '收益互换核心总时延',
    'sw2-sw3': '收益互换核心下游处理时延',
    'f1-f2': '前置机上行时延',
    'f3-f4': '前置机下行时延',
    'f1-f4': '前置机总时延',
    'f2-f3': '前置机下游处理时延',
    'f5-f6': '前置机风控上行时延',
    'af1-af2': '⻛控前置机上行时延',
    'af3-af4': '⻛控前置机下行时延',
    'af1-af4': '⻛控前置机总时延',
    'af2-af3': '⻛控前置下游处理时延',
    'a1-a2': '⻛控上行时延',
    'a3-a4': '⻛控下行时延',
    'a1-a4': '⻛控总时延',
    'a2-a3': '⻛控下游处理时延',
    'k1-k2': '核心上行时延',
    'k3-k4': '核心下行时延',
    'k1-k4': '核心总时延',
    'k2-k3': '核心下游处理时延',
    'o1-o2': '报盘上行时延',
    'o3-o4': '报盘下行时延',
    'o1-o4': '报盘总时延',
    'o2-o3': '报盘下游处理时延',
    'pen1-pen4': '全链路总时延',
    'counter1-counter2': '柜台上行',
    'counter3-counter4': '柜台下行',
    'fireWall1-fireWall2': '防火墙穿透上行时延',
    'fireWall3-fireWall4': '防火墙穿透下行时延',
    'market-entrust': '行情-交易总时延',
    'entrust-market': '交易-行情总时延',
    'exchange1-exchange4': '交易所总时延',
    'tgw1-tgw2': 'TGW网关穿透上行时延',
    'tgw3-tgw4': 'TGW网关穿透下行时延',
    'tgw1-tgw4': 'TGW网关穿透总时延'
};

export const comparatorConfig = {
    default: [
        {
            value: '=',
            label: '='
        },
        {
            value: '>=',
            label: '>='
        },
        {
            value: '>',
            label: '>'
        },
        {
            value: '<=',
            label: '<='
        },
        {
            value: '<',
            label: '<'
        }
    ],
    bool: [
        {
            value: '=',
            label: '='
        }
    ],
    char: [
        {
            value: 'like',
            label: '='
        },
        {
            value: 'contain',
            label: '⊇'
        }
    ]
};

export const featureConfig = [
    {
        title: '应用时延度量',
        featureAttributes: 'appLatency',
        viewNavigate: '/monitor/businessMonitor',
        configNavigate: 'application'
    },
    {
        title: '穿透时延度量',
        featureAttributes: 'penLatency',
        viewNavigate: '/monitor/businessMonitor',
        configNavigate: 'penetration'
    },
    {
        title: '自动化性能测试',
        featureAttributes: 'performanceTest',
        viewNavigate: '/analyseConfig',
        configNavigate: 'performanceTest'
    },
    {
        title: '产品监控管理',
        featureAttributes: 'productMonitor',
        viewNavigate: '/monitor/businessMonitor',
        configNavigate: 'monitor'
    },
    {
        title: 'RCM配置管理',
        featureAttributes: 'rcmConfig',
        viewNavigate: '/rcmDeploy',
        configNavigate: 'rcmDeploy'
    },
    {
        title: '内存数据管理',
        featureAttributes: 'memoryTable',
        viewNavigate: '/ldpTable',
        configNavigate: 'ldpTable'
    }
];

// 应用监控实例信息字典
export const instanceInfoDict = {
    // 分组名称字典
    groupName: {
        baseInfo: '基础信息',
        runningInfo: '运行状态',
        extInfo: '扩展信息',
        operateInfo: '节点操作'
    },
    // 基础信息
    baseInfo: {
        instanceName: '应用节点名',
        instanceDesc: '应用节点类型',
        version: '应用节点版本',
        developPlatform: '应用开发平台'
    },
    // 运行信息
    runningInfo: {
        ip: '部署服务器',
        port: '管理端口',
        clusterRole: '集群角色'
    },
    // 扩展信息
    extInfo: {
        collectionType: '集成方式',
        kafkaAddress: 'KafKa地址',
        prometheusAddress: 'Promethus地址',
        kafkaTopicList: 'Topic主题',
        indexName: '时延数据存储索引',
        dataReceivePort: '时延数据接收端口号',
        targetInstanceName: '时延日志输出节点',
        dataDir: '日志输出目录',
        processName: '时延日志文件名关键字'
    }
};

