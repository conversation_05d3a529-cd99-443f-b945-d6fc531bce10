// businessMonitor/marketMonitor页面布局
.wrapper,
.wrapper-flex {
    margin-top: 4px;
    height: calc(100% - 50px);

    .topo-box {
        position: relative;
        width: 100%;
        height: 35%;
        transition: height 1s;
    }

    .echart-box {
        position: relative;
        width: 100%;
        overflow: hidden;
    }
}

.securities {
    position: absolute;
    width: auto;
    top: 5px;
    right: 0;
    min-width: 200px;
}

.bg-none {
    width: 100%;
    height: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
}

.title-select {
    float: right;
    margin-right: 4px;
    top: 5px;
    width: 120px;
}

.account-select {
    width: 138px;
}

.echange-select {
    width: 138px;
}

.time-interval {
    width: 125px;
}

.second-select {
    width: 100px;
}

.unit-select {
    width: 80px;
}
