<template>
    <div>
      <h-msg-box-safe
        v-model="modalData.status"
        :mask-closable="false"
        title="导入权限"
        width="600"
        allowCopy
      >
        <div>
          <p class="text-body">导入MDB权限配置</p>
          <h-form ref="formRef" :model="form">
                <h-form-item prop="fileName" label="配置文件" required>
                    <div style="display: flex;">
                        <h-input
                            v-model="form.fileName"
                            style="width: 85%; margin-right: 10px;"
                            placeholder="请选择文件"
                            disabled
                        ></h-input>
                        <h-upload  action=""
                            accept=".enc"
                            :before-upload="handleUpload">
                            <h-button type="ghost">选择文件</h-button>
                        </h-upload>
                    </div>
                </h-form-item>
            </h-form>
          <p class="text-body">集群关系匹配
                <h-poptip autoPlacement trigger="hover" transfer customTransferClassName="apm-poptip monitor-poptip">
                    <h-icon name="prompt" size="16" color="#999" style="cursor: pointer; margin-left: 6px; position: relative; top: 1px;"></h-icon>
                    <div slot="content" style="white-space: normal;">权限中涉及的本地集群名称与导入的集群需一一匹配</div>
                </h-poptip>
            </p>
          <h-table ref="tableRef" :loading="tableLoading" :columns="columns" :data="tableData" no-data-text="暂无数据,请先选择要导入的配置文件" showTitle height="220"></h-table>
        </div>
        <template v-slot:footer>
             <a-button  @click="modalData.status = false">取消</a-button>
             <a-button type="primary" :disabled="!form.fileName" @click="handleConfirm">确定</a-button>
        </template>
      </h-msg-box-safe>
       <!-- 文件内部格式出错弹窗 -->
       <h-msg-box-safe
            v-model="jsonErrorVisible"
            :escClose="true"
            width="360"
            >
            <template v-slot:header>
                <div class="header-info header-slot">
                    <h-icon
                        name='closecircled'
                        color='#F5222D'
                        :size="28"
                    ></h-icon>
                    <span class="title-info">文件内部格式出错</span>
                </div>
            </template>
            <p>文件内部格式出错，无法读取集群信息。请重新选择文件！</p>
            <template v-slot:footer>
             <a-button style="margin-right: 10px;" @click="modalData.status = false; jsonErrorVisible = false;">取消</a-button>
             <h-upload  action=""
                accept=".enc"
                :before-upload="handleUpload">
                <a-button type="primary">重新选择</a-button>
            </h-upload>
        </template>
        </h-msg-box-safe>
    </div>
  </template>

<script>

import aButton from '@/components/common/button/aButton';
import aSelect from '@/components/common/select/aSelect';
import { setMdbPerAuthDecrypt } from '@/api/mdbPrivilegeApi';
import { getMdbEndpointConfigs } from '@/api/memoryApi';
import _ from 'lodash';

export default {
    props: {
        modalInfo: {
            type: Object,
            default: null
        }
    },
    components: { aButton },
    data() {
        return {
            tableLoading: false,
            modalData: this.modalInfo,
            form: {
                fileName: '',
                fileContent: {}
            },
            localClusters: [],      // 本地集群
            importClusters: [],     // 导入集群
            tableData: [],          // 表格数据
            jsonErrorVisible: false,
            columns: [
                { title: '本地集群', key: 'clusterName', ellipsis: true },
                {
                    title: '导入集群',
                    key: 'importClusterId',
                    render: (h, params) => {
                        return h(aSelect, {
                            props: {
                                prop: `row_${params.index}_importClusterId`,
                                value: params?.row?.importClusterId,
                                placeholder: '请选择',
                                width: '250',
                                positionFixed: true,
                                placement: 'top',
                                validRules: [
                                    {
                                        validator: (rule, value, callback) => {
                                            if (!value) {
                                                callback();
                                                return;
                                            }
                                            // 统计所有已选的集群
                                            const allSelected = this.tableData.map(row => row?.importClusterId);
                                            // 找出重复的集群
                                            const duplicates = allSelected.filter((item, idx, arr) =>
                                                item && arr.indexOf(item) !== idx
                                            );
                                            // 如果当前值在重复列表中，则报错
                                            if (duplicates.includes(value)) {
                                                callback(new Error('导入集群不能重复'));
                                            } else {
                                                callback();
                                            }
                                        },
                                        trigger: 'change'
                                    }
                                ],
                                options: this.importClusters.map(item => ({
                                    value: item?.id || '',
                                    label: item?.clusterName || ''
                                }))
                            },
                            on: {
                                'on-change': (val) => {
                                    this.tableData[params.index].importClusterId = val;
                                    this.$nextTick(() => {
                                        this.validateAllRows();
                                    });
                                }
                            }
                        });
                    }
                }
            ]
        };
    },
    computed: {
    },
    async mounted(){
        this.form = {
            fileName: '',
            fileContent: {}
        };
        this.localClusters = [];    // 本地集群
        this.importClusters = [];     // 导入集群
        this.tableData = [];
        await this.fetchClusterOptions();
    },
    methods: {
        handleUpload(file) {
            this.tableLoading = true;
            this.jsonErrorVisible = false;
            const suffix = file.name?.split('.').pop();
            if (suffix !== 'enc') {
                this.handleFormatError(file);
                this.tableLoading = false;
                return false;
            }
            if (file.size > 10 * 1024 * 1024) {
                this.handleSizeError();
                this.tableLoading = false;
                return false;
            }
            this.parseFileContent(file);
            return false;
        },
        parseFileContent(file) {
            const reader = new FileReader();
            reader.readAsText(file, 'UTF-8');
            reader.onload = async (evt) => {
                const context = evt?.target?.result || '';
                try {
                    const res = await setMdbPerAuthDecrypt({ context });
                    if (res?.code === '200') {
                        const json = res?.data || {};
                        this.form.fileName = file.name;
                        this.form.fileContent = json;
                        this.importClusters = [...(json?.clusters || [])];
                        this.generateTableData();
                    } else if (res?.code?.length === 8) {
                        this.handleContentError();
                    }
                } finally {
                    this.tableLoading = false;
                }
            };
            this.tableLoading = false;
        },
        generateTableData() {
            // 本地集群：this.localClusters
            // 导入集群：this.importClusters（为对象数组，包含 clusterName 和 clusterId）
            this.tableData = this.localClusters.map(local => {
                // 1. 优先完全等于
                const exact = this.importClusters.find(importCluster => local?.clusterName === importCluster?.clusterName);
                if (exact) {
                    return {
                        clusterId: local.clusterId,
                        clusterName: local.clusterName,
                        importClusterId: exact?.id || ''
                    };
                }
                // 2. 其次包含关系
                const fuzzy = this.importClusters.find(importCluster =>
                    local.clusterName && importCluster.clusterName &&
                    (local?.clusterName.includes(importCluster?.clusterName) || importCluster?.clusterName.includes(local?.clusterName))
                );
                return {
                    clusterId: local.clusterId,
                    clusterName: local.clusterName,
                    importClusterId: fuzzy ? fuzzy.id : ''
                };
            });
        },
        validateAllRows() {
            // 强制刷新，确保最新数据
            this.$forceUpdate();

            // 等待DOM更新后，手动触发所有aSelect的校验
            this.$nextTick(() => {
                // 通过 ref 获取表格组件，然后查找其中的 aSelect 组件
                const tableRef = this.$refs.tableRef;
                if (tableRef) {
                    // 查找表格中的所有 aSelect 组件
                    const aSelectComponents = tableRef.$el.querySelectorAll('.a-select');
                    aSelectComponents.forEach(element => {
                        // 获取对应的 Vue 组件实例
                        const component = element.__vue__;
                        if (component && component.validate) {
                            component.validate('change', () => {});
                        }
                    });
                }
            });

            // 保持原有的重复校验逻辑
            let valid = true;
            const selectedIds = new Set();
            for (const row of this.tableData) {
                if (!row?.importClusterId) continue;
                if (selectedIds.has(row?.importClusterId)) {
                    valid = false;
                    break;
                }
                selectedIds.add(row?.importClusterId);
            }
            return valid;
        },
        handleConfirm() {
            // 1、校验表单字段
            if (!this.form.fileName) return;
            // 2、集群是否重复
            if (!this.validateAllRows()) return;
            // 3、覆盖本地权限二次确认
            this.showPermissionConfirmDialog();
        },
        showPermissionConfirmDialog() {
            this.$hMsgBoxSafe.confirm({
                title: '是否覆盖本地权限？',
                content: '导入的权限配置文件将覆盖已有的本地权限配置，请确认。',
                okText: '覆盖',
                onOk: () => {
                    this.doImport();
                },
                onCancel: () => {
                    this.modalData.status = false;
                }
            });
        },
        doImport() {
            const clusterMapping = {};
            this.tableData.forEach(row => {
                if (row?.importClusterId) {
                    clusterMapping[row.clusterId] = row.importClusterId;
                }
            });
            this.form.fileContent.clusterMapping = clusterMapping;
            this.form.fileContent.productId = this.modalData.productId;
            this.$emit('import-permissions', this.form.fileContent);
            this.modalData.status = false;
        },
        handleFormatError(file) {
            this.$hMessage.error({
                title: '文件格式不正确',
                desc: `文件 ${file.name} 格式不正确，请上传txt格式的文件。`
            });
        },
        handleSizeError() {
            this.$hMessage.warning({
                title: '文件大小超出限制',
                desc: '文件大小超出限制，请上传 10MB 以内大小的文件。'
            });
        },
        handleContentError() {
            this.jsonErrorVisible = true;
        },
        async fetchClusterOptions() {
            try {
                const res = await getMdbEndpointConfigs({ productId: this.modalData.productId });
                if (res?.code === '200') {
                    this.localClusters = _.uniqBy(res?.data?.configs || [], 'clusterId');
                } else {
                    this.localClusters = [];
                }
            } catch (e) {
                this.localClusters = [];
                console.error(e);
            }
        }
    }
};
</script>

  <style lang="less" scoped>
/deep/ .h-modal-body {
    padding: 5px 20px 16px;
}

/deep/ .h-modal-footer {
    display: flex;
    justify-content: flex-end;
}

.header-info {
    font-weight: 500;
}

// header插槽样式（左对齐）
.header-slot {
    display: flex;

    & > .h-icon {
        position: relative;
        top: 2px;
    }

    .title-info {
        line-height: 30px;
        font-size: 14px;
        font-weight: 600;
        vertical-align: top;
        padding: 0 5px;
    }
}

.text-body {
    position: relative;
    font-size: 14px;
    text-align: left;
    font-weight: 400;
    color: #333;
    line-height: 20px;
    padding-left: 16px;
    margin-bottom: 12px;
    font-family: PingFangSC-Regular;

    &::before {
        content: "";
        display: inline-block;
        position: absolute;
        left: 0;
        top: 3px;
        width: 4px;
        height: 16px;
        background: var(--link-color);
    }
}
  </style>
