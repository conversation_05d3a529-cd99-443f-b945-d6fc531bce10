import fetch from './fetch';
import { objectToQueryString } from '@/utils/utils';

const prefix = window['LOCAL_CONFIG']['API_HOME'] || '/';

// 节点定位配置对比
export function setConfigLocateCompare(param) {
    return fetch().post(`${prefix}/config/locate/compare`, param);
}

// 获取节点定位配置列表
export function getConfigAppLocate(param) {
    return fetch().get(`${prefix}/config/app/locate?${objectToQueryString(param)}`);
}

// 编辑保存配置内容
export function getConfigLocateCompareEdit(param, showErrorToast = true) {
    return fetch({ showErrorToast }).post(`${prefix}/config/locate/compare/edit`, param);
}

// 获取节点定位配置详情
export function getConfigLocateDetail(param) {
    return fetch().get(`${prefix}/config/app/locate/detail?${objectToQueryString(param)}`);
}
