.pop-business {
    .h-poptip-inner {
        width: 360px;
        overflow: auto;
        background-color: var(--poptip-bg-color);
    }

    .h-poptip-rel {
        width: 100%;
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
    }

    .h-poptip-title {
        padding: 8px;
        border-bottom: 1px solid var(--font-opacity-color);

        &::after {
            display: none;
        }
    }

    &[x-placement^="left"] {
        .h-poptip-arrow {
            border-left-color: var(--poptip-bg-color) !important;

            &::after {
                border-left-color: var(--poptip-bg-color) !important;
            }
        }
    }

    &[x-placement^="right"] {
        .h-poptip-arrow {
            border-right-color: var(--poptip-bg-color) !important;

            &::after {
                border-right-color: var(--poptip-bg-color) !important;
            }
        }
    }

    &[x-placement^="top"] {
        .h-poptip-arrow {
            border-top-color: var(--poptip-bg-color) !important;

            &::after {
                border-top-color: var(--poptip-bg-color) !important;
            }
        }
    }

    &[x-placement^="bottom"] {
        .h-poptip-arrow {
            border-bottom-color: var(--poptip-bg-color) !important;

            &::after {
                border-bottom-color: var(--poptip-bg-color) !important;
            }
        }
    }

    .h-poptip-title-inner {
        width: 300px;
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
        line-height: 18px;
        color: var(--font-color);
    }

    .h-poptip-body {
        padding: 8px;
    }
}

.pop-business.data-accord-business-poptip {
    .h-poptip-title-inner {
        width: 70%;
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
    }

    .data-accord-business-detail-btn {
        position: absolute;
        right: 22px;
        top: 10px;
    }
}

.pop-content.large-pop-content {
    max-height: 365px;
}

.pop-content {
    max-height: 250px;
    overflow: auto;

    .pop-content-info {
        display: flex;
        color: #fff;
        max-width: 400px;

        .info-title {
            line-height: 12px;
            padding-right: 10px;
            border-right: 1px solid rgba(255, 255, 255, 0.15);
            font-weight: bold;
            width: 60px;
            text-align: right;
        }

        .info-warn {
            position: absolute;
            top: 8px;
            right: 15px;
            cursor: pointer;

            &:hover {
                color: #298dff;
            }
        }

        & > ul {
            & > li {
                line-height: 16px;
                white-space: normal;
                padding: 0 0 4px 10px;

                & > span {
                    color: #cacfd4;
                }
            }

            li:last-child {
                padding: 0 0 0 10px;
            }
        }
    }
}

.h-poptip-popper[x-placement^="top"] {
    .pop-content {
        max-height: 180px;
    }
}

.pop-business-label {
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 0 5px;

    p {
        flex: 1;
        width: 70%;
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
    }
}

.pop-business-value {
    border-radius: 7px;
    background-color: #6b7a99;
    padding: 0 4px;
    margin: 0 0 0 5px;
    min-width: 14px;
    max-width: 40px;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
    height: 14px;
    line-height: 14px;
    text-align: center;
}

.info-role {
    width: 100%;

    .h-col {
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
    }

    .h-row {
        padding: 0 0 4px 10px;
        height: 24px;
        line-height: 24px;
    }

    .h-row:hover {
        background: #6c727e8f;
    }
}

.info-drawer-box {
    /* stylelint-disable-next-line plugin/z-index-value-constraint */
    z-index: 1061; //poptip的z-index:1060
}

.info-drawer {
    text-align: right;
    margin-bottom: 10px;

    .h-checkbox-wrapper {
        margin-right: 15px;
        display: inline-block;
    }

    .h-input-wrapper {
        max-width: 40%;
        display: inline-block;
    }

    .h-input-icon {
        cursor: pointer;

        &:hover {
            color: var(--link-color);
        }
    }
}

.alarm-text {
    &:hover {
        color: var(--link-color);
    }
}