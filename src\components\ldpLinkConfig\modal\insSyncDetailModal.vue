 <!--
    * @Description: 应用节点同步详情弹窗
-->
<template>
    <div>
        <h-msg-box-safe
            v-model="modalData.status"
            :mask-closable="false"
            title="节点信息同步"
            top="50"
            width="80"
            maxHeight="450">
            <h-spin v-if="loading" fix>
                <h-icon name="load-c" size="18" class="demo-spin-icon-load"></h-icon>
                <div>正在识别节点信息，请稍等......</div>
            </h-spin>

            <h-tabs :key="tKey" v-model="tabName">
                <h-tab-pane
                    v-for="(val, key) in statisticsInfo"
                    :key="key"
                    :label="`${val.label}(${val.count})`"
                    :name="key">
                    <div>
                        <a-tips :tipText="val.tipText"></a-tips>
                        <h-table
                            :row-class-name="handleRowClass"
                            :columns="val.columns"
                            :data="val.tableData"
                            height="300"
                            style="margin-top: 10px;">
                        </h-table>
                    </div>
                </h-tab-pane>
            </h-tabs>

            <template v-slot:footer>
                <a-button @click="modalData.status = false">取消</a-button>
                <a-button
                    type="primary"
                    :loading="loading"
                    @click="submitConfig">同步
                </a-button>
            </template>
        </h-msg-box-safe>
    </div>
</template>

<script>
import aButton from '@/components/common/button/aButton';
import aTips from '@/components/common/apmTips/aTips';
import { getZkProductInstances, updateBatchAppInstance } from '@/api/productApi';
export default {
    name: 'InsSyncDetailModal',
    components: { aButton, aTips },
    props: {
        modalInfo: {
            type: Object,
            default: null
        },
        productInfo: {
            type: Object
        }
    },
    data() {
        const GENERAL_COLUMNS = [
            {
                title: '应用节点名',
                key: 'instanceName',
                minWidth: 120
            },
            {
                title: '应用节点类型',
                key: 'instanceType',
                render: (h, params) => {
                    return h('span',
                        this.$store?.state?.apmDirDesc?.appTypeDictDesc?.[params.row.instanceType] ||
                             params.row.instanceType || '未识别'
                    );
                },
                minWidth: 100
            },
            {
                title: '短应用名',
                key: 'instanceShortName',
                minWidth: 90,
                formatMethod: (row) => row?.instanceShortName ?? '未识别'

            },
            {
                title: '节点编号',
                key: 'instanceNo',
                minWidth: 80,
                formatMethod: (row) => row?.instanceNo ?? '未识别'
            },
            {
                title: '分片编号',
                key: 'shardingNo',
                minWidth: 80
            },
            {
                title: '应用节点身份',
                key: 'instanceIdentities',
                minWidth: 110,
                render: (h, params) => {
                    return h('span',
                        params.row?.instanceIdentities?.map(item => {
                            return this.$store?.state?.apmDirDesc?.instanceIdentityDict?.[item] || '';
                        })?.join(',')
                    );
                }
            },
            {
                title: '开发平台',
                key: 'developPlatform',
                minWidth: 80
            },
            {
                title: '管理IP',
                key: 'manageProxyIp',
                minWidth: 110,
                formatMethod: (row) => row?.manageProxyIp ?? '未识别'
            },
            {
                title: '管理端口',
                key: 'manageProxyPort',
                minWidth: 80,
                formatMethod: (row) => row?.manageProxyPort ?? '未识别'
            },
            {
                title: 'zk配置节点',
                key: 'zkConfigNode',
                minWidth: 150
            }
        ];

        return {
            modalData: this.modalInfo,
            tabName: 'delete',
            tKey: 1,

            tabStaticInfo: {
                delete: {
                    label: '删除',
                    columns: GENERAL_COLUMNS.concat([
                        {
                            title: '删除确认',
                            key: 'action',
                            width: 120,
                            fixed: 'right',
                            render: (_, params) => {
                                return <h-select
                                    value={params.row.delConfirm || 'delete'}
                                    clearable={false}
                                    transfer
                                    autoPlacement
                                    v-on:on-change={(val) => this.handleSelectChange(val, params.row._index)}>
                                    <h-option value="delete">删除</h-option>
                                    <h-option value="notDelete">不删除</h-option>
                                </h-select>;
                            }
                        }
                    ]),
                    tipText: '此分类下的节点存在于本地，但zk未识别到，请核对后做“删除确认”（如需保留，请手动确认“不删除”）。'
                },
                add: {
                    label: '新增',
                    columns: GENERAL_COLUMNS,
                    tipText: '以下节点为zk识别到的新节点（本地无）。同步后会将添加至本地节点列表。'
                },
                update: {
                    label: '更新',
                    columns: GENERAL_COLUMNS,
                    tipText: '以下节点为zk识别到的有更新的节点，同步后会将对应节点更新至本地节点列表。'
                },
                failed: {
                    label: '识别失败',
                    columns: GENERAL_COLUMNS.concat([
                        {
                            title: '是否忽略',
                            key: 'ignoreConfirm',
                            fixed: 'right',
                            width: 90,
                            render: (h, params) => {
                                return <h-switch
                                    value={params.row.ignoreConfirm || 'notIgnore'}
                                    true-value="ignore"
                                    false-value="notIgnore"
                                    v-on:on-change={(val) => this.handleSwitchChange(val, params.row._index)}>
                                    <span slot="open">是</span>
                                    <span slot="close">否</span>
                                </h-switch>;
                            }
                        },
                        {
                            title: '操作',
                            key: 'action',
                            fixed: 'right',
                            width: 90,
                            render: (h, params) => {
                                return h('div', [
                                    h(
                                        'Button',
                                        {
                                            props: {
                                                type: 'text',
                                                size: 'small'
                                            },
                                            style: {
                                                padding: 0,
                                                color: '#2d8de5'
                                            },
                                            on: {
                                                click: async () => {
                                                // 更新列表
                                                    this.getSyncData();
                                                }
                                            }
                                        },
                                        '重新识别'
                                    )
                                ]);
                            }
                        }
                    ]),
                    tipText: '以下节点识别失败，无法同步。可重新识别或选择“忽略”。“忽略”即表示此应用节点不会被同步。'
                }
            },
            statisticsInfo: {},
            noChangeInstances: [],
            loading: false,
            btnLoading: false
        };
    },
    mounted() {
        this.loading = true;
        this.handleInterfaceData(this.modalData.insStatistics || {});
        this.$nextTick(() => {
            this.loading = false;
        });
    },
    methods: {
        // 接口数据转化
        handleInterfaceData(data) {
            // 无变化节点
            this.noChangeInstances = data?.noChangeInstances || [];

            // 展示tab页内容
            const statisticsInfo = {};
            const { deleteInstances, addInstances, updateInstances, failedInstances } = data;
            statisticsInfo.delete = {
                ...this.tabStaticInfo.delete,
                count: data.deleteInstanceCount ?? 0,
                tableData: deleteInstances?.map(item => {
                    return {
                        ...item,
                        delConfirm: 'delete'
                    };
                })
            };
            statisticsInfo.add = {
                ...this.tabStaticInfo.add,
                count: data.addInstanceCount ?? 0,
                tableData: addInstances
            };
            statisticsInfo.update = {
                ...this.tabStaticInfo.update,
                count: data.updateInstanceCount ?? 0,
                tableData: updateInstances
            };
            if (data.failedInstanceCount && failedInstances?.length) {
                // 未识别tab若无数据 则不展示
                statisticsInfo.failed = {
                    ...this.tabStaticInfo.failed,
                    count: data.failedInstanceCount ?? 0,
                    tableData: failedInstances?.map(item => {
                        return {
                            ...item,
                            ignoreConfirm: 'notIgnore'
                        };
                    })
                };
            }
            this.statisticsInfo = statisticsInfo;
            // 避免数据变化未映射至dom上
            this.tKey++;
        },
        // 同步确认
        submitConfig() {
            // 存在未识别且未选择忽略的节点 - 告警提示并跳转tab
            const failedIns = this.statisticsInfo?.failed?.tableData;
            if (failedIns?.length && failedIns.find(item => item.ignoreConfirm === 'notIgnore')) {
                this.$hMessage.warning('请先处理“识别失败”应用节点！');
                this.tabName = 'failed';
                return;
            }

            // 数据处理 - 判断是否可提交
            const submitData = [
                ...(this.statisticsInfo?.delete?.tableData || []).filter(item =>
                    item.delConfirm === 'notDelete'
                ),
                ...(this.statisticsInfo?.add?.tableData || []),
                ...(this.statisticsInfo?.update?.tableData || []),
                ...(this.noChangeInstances || [])
            ];

            this.syncInstanceData(submitData);
        },
        // 获取同步信息 - 重新识别
        getSyncData() {
            this.loading = true;
            const param = {
                productId: this.productInfo.id
            };
            getZkProductInstances(param).then(res => {
                if (res.code === '200') {
                    this.handleInterfaceData(res.data || {});
                    // 处理识别失败节点报错信息
                    this.handleFailedInsMessage(res.data?.failedInstances);
                } else if (res.code?.length === 8) {
                    this.$hMessage.error({
                        content: res.message,
                        duration: 3
                    });
                }
            }).finally(() => {
                this.$nextTick(() => {
                    this.loading = false;

                    // 避免识别失败tab不存在，导致tab未选中
                    const tabList = Object.keys(this.statisticsInfo) || [];
                    if (!tabList.includes(this.tabName)) {
                        this.tabName = tabList?.[0];
                    }
                });
            });
        },
        handleFailedInsMessage(failedData) {
            if (!failedData?.length) {
                return this.$hMessage.success('节点识别成功！');
            } else {
                // 组织节点识别失败原因
                const message = failedData.map(item => {
                    return item.instanceName + '：' + (item.reason || '识别失败');
                }).join('；');
                this.$hMessage.error({
                    content: message,
                    duration: 5
                });
            }
        },
        // 同步节点接口
        async syncInstanceData(data) {
            this.btnLoading = true;
            try {
                const params = {
                    productId: this.productInfo.id,
                    instances: data
                };
                const res = await updateBatchAppInstance(params);
                if (res.code === '200') {
                    this.$emit('update', this.modalData.productId);
                    this.$hMessage.success('应用节点信息列表已更新！');
                    this.modalInfo.status = false;
                } else if (res.code.length === 8) {
                    this.$hMessage.error({
                        content: res.message,
                        duration: 3
                    });
                }
            } finally {
                this.btnLoading = false;
            }
        },
        /**
         * 更新表格列下拉框值 - 删除确认
         * @param val 选中值
         * @param index 更新行索引
         */
        handleSelectChange(val, index) {
            if (!this.statisticsInfo?.delete?.tableData?.length) return;
            this.$set(this.statisticsInfo.delete.tableData[index], 'delConfirm', val);
        },
        /**
         * 更新开关值 - 忽略未识别项确认
         * @param val 选中值
         * @param index 更新行索引
         */
        handleSwitchChange(val, index) {
            if (!this.statisticsInfo?.failed?.tableData?.length) return;
            this.$set(this.statisticsInfo.failed.tableData[index], 'ignoreConfirm', val);
        },
        // 选择不删除的行 背景颜色区分
        handleRowClass(row) {
            if (row.delConfirm === 'notDelete') {
                return 'demo-table-info-row';
            }
            return '';
        }
    }
};
</script>

<style lang="less" scoped>
/deep/ .h-modal-content {
    min-width: 700px;
}

/deep/ .h-modal-body {
    padding: 10px 32px 20px;
}

/deep/ .h-tabs-nav .h-tabs-tab {
    padding: 8px 4px;
}

/deep/ .h-tabs-content-wrap {
    height: 350px;

    .h-tabs-tabpane {
        padding: 0 2px;
    }
}

/deep/ .h-table .demo-table-info-row td {
    background-color: #d8edff;
}

.demo-spin-icon-load {
    animation: ani-demo-spin 1s linear infinite;
    display: inline-block;
}

@keyframes ani-demo-spin {
    from {
        transform: rotate(0deg);
    }

    50% {
        transform: rotate(180deg);
    }

    to {
        transform: rotate(360deg);
    }
}
</style>
