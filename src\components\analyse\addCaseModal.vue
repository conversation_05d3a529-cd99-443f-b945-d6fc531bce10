<template>
    <div>
        <!-- 下发配置列表 -->
        <h-msg-box-safe
            v-model="modalData.status"
            :escClose="true"
            :mask-closable="false"
            title="新建测试用例"
            width="50"
        >
            <h-form
                ref="formValidate"
                :model="formValidate"
                :label-width="100"
                >
                <h-form-item label="用例名称" prop="name" required>
                    <h-input
                        v-model="formValidate.name"
                        placeholder="请输入测试用例名称"
                        onkeydown="if(event.keyCode==13){event.keyCode=0;event.returnValue=false;}"
                    ></h-input>
                </h-form-item>
            </h-form>

            <template v-slot:footer>
                <a-button @click="modalData.status = false">取消</a-button>
                <a-button type="primary" :loading="loading" @click="submitConfig">确定</a-button>
            </template>
        </h-msg-box-safe>
    </div>
</template>

<script>
import { saveCase } from '@/api/httpApi';
import aButton from '@/components/common/button/aButton';
export default {
    props: {
        modalInfo: {
            type: Object,
            default: null
        }
    },
    data() {
        return {
            modalData: this.modalInfo,
            loading: false,
            formValidate: {
                name: ''
            }
        };
    },
    methods: {
        submitConfig() {
            const that = this;
            this.$refs['formValidate'].validate(async (valid) => {
                if (valid) {
                    if (this.formValidate.name.length > 20) {
                        this.$hMessage.error('字符长度数不得超过20！');
                        return;
                    }
                    that.loading = true;
                    try {
                        const res = await saveCase({
                            caseName: this.formValidate.name,
                            sceneId: this.modalInfo.sceneId
                        });
                        if (res.success) {
                            this.$emit('update', res?.data?.sceneId);
                            that.$hMessage.success('用例创建成功!');
                            that.modalInfo.status = false;
                            this.$emit('query', this.modalInfo.sceneId);
                        } else {
                            that.loading = false;
                            this.$hMessage.error('用例创建失败!');
                        }
                    } catch (err) {
                        that.loading = false;
                    }
                }
            });
        }
    },
    components: { aButton }
};
</script>
