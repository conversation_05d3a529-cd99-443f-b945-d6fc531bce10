<template>
  <div class="perform-box">
    <!-- 指标总览 -->
    <info-sum-bar :data="performOverview" :selectInfoId="selectInfoId" selectedStyleType="border" @info-bar-click="handleBarClick" @select-change="selectChange"></info-sum-bar>
    <div v-if="performOverview.details.length && selectInfoId"  >
         <!-- 指标执行详情 -->
        <info-grid ref="grid" :gridData="performData"></info-grid>
    </div>
    <a-loading v-if="loading" style="width: 100%; height: 100%;"></a-loading>
    <no-data v-if="!selectInfoId" />
  </div>
</template>
<script>
import infoGrid from '@/components/common/infoBar/infoGrid';
import infoSumBar from '@/components/common/infoBar/infoSumBar';
import noData from '@/components/common/noData/noData';
import { formatNumber, autoConvertTime, getObjByArray } from '@/utils/utils';
import { getPerformanceIndicators, getPerformanceTrend } from '@/api/productApi';
import aLoading from '@/components/common/loading/aLoading';
import _ from 'lodash';
export default {
    props: {
        productInfo: {
            type: Object,
            default: () => {
                return {};
            }
        },
        productInstNo: {
            type: String,
            default: ''
        },
        type: {
            type: String,
            default: ''
        },
        curTabDashBorad: {
            type: Object,
            default: () => { return {}; }
        }
    },
    components: {
        infoGrid,
        aLoading,
        noData,
        infoSumBar
    },
    data() {
        return {
            timer: null,
            loading: false,
            // 委托业务性能总览
            timeInterval: 300,
            interval: 5000,
            secondType: 'avg',
            performOverview: {
                title: {
                    label: '委托业务性能总览',
                    slots: [
                        {
                            key: 'timeIntervalSelect',
                            type: 'select',
                            defaultValue: 300,
                            options: [
                                {
                                    label: '最近5分钟',
                                    value: 300
                                }, {
                                    label: '最近15分钟',
                                    value: 900
                                }, {
                                    label: '最近30分钟',
                                    value: 1800
                                }, {
                                    label: '最近1小时',
                                    value: 3600
                                }
                            ]
                        },
                        {
                            key: 'intervalSelect',
                            type: 'select',
                            defaultValue: 5000,
                            options: [
                                {
                                    label: '5秒',
                                    value: 5000
                                }, {
                                    label: '10秒',
                                    value: 10000
                                }, {
                                    label: '30秒',
                                    value: 30000
                                }
                            ]
                        },
                        {
                            key: 'secondTypeSelect',
                            type: 'select',
                            defaultValue: 'avg',
                            options: [
                                {
                                    value: 'p50',
                                    label: '中位数'
                                },
                                {
                                    value: 'avg',
                                    label: '平均值'
                                }, {
                                    value: 'max',
                                    label: '最大值'
                                }, {
                                    value: 'min',
                                    label: '最小值'
                                }
                            ]
                        }
                    ]
                },
                direction: 'grid',
                autoGrid: true,
                autoScroll: true,
                gridMinWidth: '300px',
                scrollMaxHeight: '300px',
                details: []
            },
            selectInfoId: '',
            // 委托业务性能趋势
            performData: {
                title: {
                    label: '-性能趋势'
                },
                layout: [
                    { x: 0, y: 0, w: 12, h: 12, i: 'info1' },
                    { x: 0, y: 1, w: 12, h: 12, i: 'info2' }
                ],
                details: [
                    {
                        type: 'chart',
                        info: {
                            basicOpiton: {
                                chartType: 'axis',
                                lineDeploy: [
                                    {
                                        name: '吞吐',
                                        type: 'line'
                                    },
                                    {
                                        name: '时延',
                                        type: 'line',
                                        yAxisIndex: 1
                                    }
                                ],
                                yAxiDeploy: [
                                    {
                                        name: '吞吐(qbs)',
                                        alignTicks: true,
                                        axisLabel: {
                                            formatter: formatNumber
                                        }
                                    },
                                    {
                                        name: '时延(ns)',
                                        show: true,
                                        alignTicks: true,
                                        axisLabel: {
                                            formatter: formatNumber
                                        }
                                    }
                                ]
                            },
                            additionalOpiton: {
                                backgroundColor: '#2d334c'
                            },
                            chartData: {
                                xData: [],
                                data: {
                                    吞吐: [],
                                    时延: []
                                }
                            }
                        }
                    },
                    {
                        type: 'chart',
                        info: {
                            basicOpiton: {
                                chartType: 'axis',
                                lineDeploy: [
                                    {
                                        name: '请求数',
                                        type: 'bar'
                                    }
                                ],
                                yAxiDeploy: [
                                    {
                                        name: '次',
                                        axisLabel: {
                                            formatter: formatNumber
                                        }
                                    }
                                ]
                            },
                            additionalOpiton: {
                                backgroundColor: '#2d334c'
                            },
                            chartData: {
                                xData: [],
                                data: {
                                    请求数: []
                                }
                            }
                        }
                    }
                ]
            },
            // 指标值---后端接口不具有通用性
            indicatorsData: [
                {
                    label: '吞吐',
                    key: 'throughput',
                    unit: 'qbs'
                },
                {
                    label: '时延',
                    key: 'latency',
                    unit: 'ns'
                },
                {
                    label: '请求数',
                    key: 'requestNum',
                    unit: 'c'
                }
            ]
        };
    },
    mounted() {
        window.addEventListener('resize', this.resetLayout);
    },
    methods: {
        // 初始化
        async init() {
            this.clearPolling();
            this.cleanData();
            this.loading = true;
            await this.getPerformanceData();
            this.loading = false;
            this.curTabDashBorad?.timerSwitch && this.setPolling(this.curTabDashBorad?.timerInterval || 5);
        },
        // 手动清理页面数据
        cleanData(){
            this.timeInterval = 300;
            this.interval = 5000;
            this.secondType = 'avg';
            this.selectInfoId = '';
            this.performOverview.details = [];
            this.cleanPerformDataDetail();
        },
        // 表格下拉框切换
        selectChange(value, key) {
            if (key === 'timeIntervalSelect'){
                this.timeInterval = value;
                this.getPerformanceData();
            }

            if (key === 'intervalSelect'){
                this.interval = value;
                this.getPerformanceData();
            }

            if (key === 'secondTypeSelect'){
                this.secondType = value;
                this.getPerformanceData();
            }
        },
        // 点击事件
        async handleBarClick(id){
            if (this.selectInfoId !== id){
                this.cleanPerformDataDetail();
            }
            this.selectInfoId = id;
            const trendData = await this.getPerformanceTrend(id);
            this.setPerformDataDetail(id, trendData);
        },
        // 设置chart数据
        setPerformDataDetail(id, trendData){
            this.performData.title.label = this.getInfoTitleById(id) + '业务性能趋势';
            this.performData.details[0].info.chartData.xData = trendData['throughput']?.trendChart?.xaxis;
            this.performData.details[0].info.chartData.data.吞吐 = trendData['throughput']?.trendChart?.yaxis;
            this.performData.details[0].info.chartData.data.时延 = trendData['latency']?.trendChart?.yaxis;
            this.performData.details[1].info.chartData.xData = trendData['requestNum']?.trendChart?.xaxis;
            this.performData.details[1].info.chartData.data.请求数 = trendData['requestNum']?.trendChart?.yaxis;
        },
        // 根据infoId找寻对应的指标类型
        getInfoTitleById(id){
            const title = _.find(this.performOverview.details || [], ['infoId', id])?.title || '-';
            return title;
        },
        // 清空chart数据
        cleanPerformDataDetail(){
            this.performData.title.label = '-业务性能趋势';
            this.performData.details[0].info.chartData.xData = [];
            this.performData.details[0].info.chartData.data.吞吐 = [];
            this.performData.details[0].info.chartData.data.时延 = [];
            this.performData.details[1].info.chartData.xData = [];
            this.performData.details[1].info.chartData.data.请求数 = [];
        },
        // 构建info组件数据---先写死指标值后续调整接口
        setIndicatorsInfoData(indicators){
            const infoList = [];
            for (const indicator of Object.values(indicators)){
                infoList.push({
                    type: 'monitor',
                    title: indicator.subBizTypeAlias,
                    canClick: true,
                    infoId: indicator.subBizType,
                    info: this.setIndicators(indicator)
                });
            }
            return infoList;
        },
        // 指定指标--后端接口不具有通用性
        setIndicators(indicator){
            const info = [];
            Object.values(this.indicatorsData).forEach(data => {
                let val = '';
                if (data.key === 'latency'){
                    const { value, unit } = autoConvertTime(indicator?.latency);
                    val = value + unit;
                } else {
                    val = formatNumber(indicator.requestNum) + data.unit;
                }
                info.push(
                    {
                        type: 'text',
                        label: data.label,
                        key: data.key,
                        value: val
                    }
                );
            });
            return info;
        },
        // 一定时间段内委托的运行性能指标
        async getPerformanceIndicators() {
            let indicators = [];
            const params = {
                productId: this.productInstNo,
                interval: this.interval,
                secondType: this.secondType
            };
            try {
                const res = await getPerformanceIndicators(params);
                if (res.code === '200') {
                    indicators = this.setIndicatorsInfoData([...res?.data] || []);
                }
            } catch (err) {
                this.clearPolling();
            }
            return indicators;
        },
        // 一定时间段内委托的运行性能指标
        async getPerformanceTrend(id){
            let trendData = [];
            const params = {
                productId: this.productInstNo,
                timeWindow: this.secondType,
                interval: 5000,
                subBizType: id
            };
            try {
                const res = await getPerformanceTrend(params);
                if (res.code === '200' && Array.isArray(res.data)) {
                    trendData = getObjByArray(res.data, 'indicatorName');
                }
            } catch (err) {
                this.clearPolling();
            }
            return trendData;
        },
        // 展示指标数据以及指标图表
        async getPerformanceData(){
            this.performOverview.details = await this.getPerformanceIndicators();
            if (this.performOverview.details.length){
                const id = _.find(this.performOverview.details, ['infoId', this.selectInfoId])?.infoId || '';
                this.handleBarClick(id || this.performOverview.details[0]?.infoId);
            }
        },
        // 定时器
        setPolling(timerInterval) {
            this.clearPolling();
            this.timer = setInterval(() => {
                this.getPerformanceData();
            }, timerInterval * 1000);
        },
        // 清楚定时器
        clearPolling(){
            if (this.timer){
                for (let i = 1; i <= this.timer; i++) {
                    clearInterval(i);
                }
            }
        }
    },
    beforeDestroy() {
        this.timer && clearInterval(this.timer);
    }
};
</script>
<style lang="less" scoped>
.perform-box {
    width: 100%;
    height: calc(100% - 60px);

    .info-sum-bar {
        margin-top: 0;
    }

    /deep/ .obs-title {
        background: var(--primary-color);
    }
}

</style>
