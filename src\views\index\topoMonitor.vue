<!--
 * @Description: 拓扑监控
 * @Author: <PERSON><PERSON>
 * @Date: 2023-09-01 15:01:37
 * @LastEditTime: 2024-08-26 16:04:57
 * @LastEditors: yingzx38608 <EMAIL>
-->
<template>
    <div class="main">
        <header>
            <a-title :title="scene === `topologyObservation` ? `应用拓扑结构` : `RCM拓扑结构`">
                <slot>
                    <h-select
                        v-show="productList.length > 1"
                        v-model="productInstNo"
                        class="title-single-select"
                        placeholder="请选择"
                        :positionFixed="true"
                        :clearable="false"
                        @on-change="checkProduct">
                        <h-option
                            v-for="item in productList"
                            :key="item.id"
                            :value="item.productInstNo">
                            {{ item.productName }}
                        </h-option>
                    </h-select>
                </slot>
            </a-title>
        </header>
        <div v-if="editableTabs.length > 0" class="topo-container">
            <div ref="container" class="topo-container-top">
                <div ref="tabsWrap" class="topo-container-top-tabs" :style="{minWidth: `100px`, width: `${tabsWrapWidth}px`}">
                    <h-tabs
                        ref="tabs"
                        v-model="tabName"
                        :showArrow="showArrow"
                        class="topo-container-top-tabs"
                        :animated="false"
                        @on-click="handleTabChange(tabName)">
                        <h-tab-pane
                            v-for="item in editableTabs"
                            :key="item.name"
                            :label="item.describe"
                            :name="item.name">
                        </h-tab-pane>
                    </h-tabs>
                </div>
                <div ref="topWrap" class="topo-container-top-wrap">
                    <div v-if="productInstNo" :key="productInstNo" class="topo-container-top-wrap-menus">
                        <!-- 集群拓扑菜单 -->
                        <ldp-cluster-observer-menu
                            v-show="tabName === 'appClusterTopology'"
                            ref="appClusterTopologyMenu"
                            :productId="productInstNo"
                            @changeParam="onChangeQueryParam"
                        />
                        <!-- 应用拓扑菜单 -->
                        <app-instance-topology-menu
                            v-show="tabName === 'appInstanceTopology'"
                            ref="appInstanceTopologyMenu"
                            :productId="productInstNo"
                            @changeParam="onChangeQueryParam"
                        />
                        <!-- 上下文拓扑菜单 -->
                        <context-topology-menu
                            v-show="tabName === 'contextTopology'"
                            ref="contextTopologyMenu"
                            :productId="productInstNo"
                            @changeParam="onChangeQueryParam"
                            @handleRcmChange="handleRcmChange"
                        />
                    </div>
                    <div class="topo-container-top-wrap-setting" @click="() => toggleSetting(true)">
                        <h-icon name="setup" />
                    </div>
                </div>
            </div>
            <div class="topo-container-view">
                <div
                    v-for="item in editableTabs"
                    v-show="item.name === tabName"
                    :key="item.name"
                    style="height: 100%;">
                    <component
                        :is='item.name'
                        :ref="item.name"
                        :productId="productInstNo"
                        @setRcmList="setRcmList"
                        @setShowTopicList="setShowTopicList"
                        @setAppClustersList="setAppClustersList"
                        @setTagList="setTagList"
                        @setQueryParam="handleDrillDown"
                    />
                </div>
            </div>
        </div>
        <div v-else>
            <no-data text="当前产品不支持拓扑展示" style="height: 800px;" />
        </div>
        <observer-setting
            v-if="settingData.status"
            :settingData="settingData"
            :productId="productInstNo"
            @onChangeSetting="onChangeSetting"
            @onClose="() => toggleSetting(false)" />
    </div>
</template>
<script>
import _ from 'lodash';
import { mapState, mapActions } from 'vuex';
import { getDashboardTag, getDashboardConfigV2 } from '@/api/httpApi';
import aTitle from '@/components/common/title/aTitle';
import noData from '@/components/common/noData/noData';
import serviceTopology from '@/components/ldpMonitor/appTypeObserver.vue';
import appClusterTopology from '@/components/ldpMonitor/ldpClusterObserver.vue';
import appInstanceTopology from '@/components/ldpMonitor/ldpAppObserver.vue';
import appInstanceTopologyMenu from '@/components/ldpMonitor/ldpAppObserverMenu.vue';
import ldpClusterObserverMenu from '@/components/ldpMonitor/ldpClusterObserverMenu.vue';
import contextTopology from '@/components/ldpMonitor/rcmObserver.vue';
import observerSetting from '@/components/ldpMonitor/observerSetting.vue';
import contextTopologyMenu from '@/components/ldpMonitor/rcmObserverMenu.vue';
export default {
    components: { noData, observerSetting, contextTopologyMenu, appInstanceTopologyMenu, ldpClusterObserverMenu, appInstanceTopology, appClusterTopology, contextTopology, aTitle, serviceTopology },
    data() {
        return {
            scene: 'topologyObservation',
            tabName: '',
            productInstNo: '',
            loading: false,
            editableTabs: [],
            firstPaint: true,
            tabsWrapWidth: 0,
            defaultTabsWidth: 0,
            settingData: {
                status: false,
                excludeUnmanagedNode: localStorage.getItem('rcm_excludeUnmanagedNode') && localStorage.getItem('rcm_excludeUnmanagedNode') === 'true'
            },
            showArrow: false
        };
    },
    async mounted() {
        this.scene = this.$route.query?.scene === 'rcmTopologyObservation' ? 'rcmTopologyObservation' : 'topologyObservation';
        window.addEventListener('resize', this.onResize);
        this.loading = true;
        await this.getProductList({ filter: this.scene === 'topologyObservation' ? 'excludeLdpApm' : 'supportRcm' });
        this.loading = false;
        const productInstNo = localStorage.getItem('productInstNo');
        this.productInstNo = _.find(this.productList, ['productInstNo', productInstNo])?.productInstNo ||
            this.productList?.[0]?.productInstNo;
        const { tabName } = this.$route?.query;
        if (tabName) {
            this.tabName = tabName;
        }

        this.$hCore.on('topo-jump-global-event', (params) => {
            const { type, arg, tabName } = params;
            this.handleDrillDown(type, arg, tabName);
        });
    },
    beforeDestroy() {
        this.clearPolling();
        this.$hCore.off('topo-jump-global-event');
        window.removeEventListener('resize', this.onResize);
    },
    computed: {
        ...mapState({
            productList: state => {
                return _.filter(state.product.productListLight, function (o) {
                    return o.productType !== 'NSQ1.0';
                }) || [];
            }
        })
    },
    methods: {
        ...mapActions({ getProductList: 'product/getProductListLight' }),
        async getDashboardConfig() {
            try {
                let tags = [];
                const param = {
                    objectType: 'product',
                    objectId: this.productInstNo
                };
                param.scene = this.scene;
                // 获取dashboard查询tag
                const tagsRes = await getDashboardTag(param);
                if (tagsRes.code === '200') {
                    tags = tagsRes?.data || [];
                }
                // tags作为入参，查询dashboard配置
                const res = await getDashboardConfigV2({
                    ...param,
                    tags: tags
                });
                if (res.code === '200') {
                    this.editableTabs = res?.data?.filter(v => v?.visible === true) || [];
                    this.$nextTick(() => {
                        this.setDefaultTabsWidth();
                    });
                }
            } catch (err) {
                this.editableTabs = [];
            }
        },
        /**
         * 设置默认tabs宽度
         */
        setDefaultTabsWidth() {
            this.$nextTick(() => {
                const tabsWrap = this.$refs['tabsWrap'];
                if (tabsWrap) {
                    const huiTabs = tabsWrap.querySelector('.h-tabs-bar');
                    this.defaultTabsWidth = huiTabs.getBoundingClientRect().width;
                    this.setTabArrowVisible();
                }
            });
        },
        /**
         * 监听窗口变化事件
         */
        onResize: _.throttle(function () {
            this.setTabArrowVisible();
        }, 40),
        /**
         * 设置tabbar的箭头是否可见
         */
        setTabArrowVisible() {
            this.$nextTick(() => {
                const container = this.$refs['container'];
                const tabsWrap = this.$refs['tabsWrap'];
                const topWrap = this.$refs['topWrap'];
                if (container && tabsWrap && topWrap) {
                    const totalWidth = container.getBoundingClientRect().width;
                    const huiTabs = tabsWrap.querySelector('.h-tabs-bar');
                    if (!huiTabs) return;
                    const topWrapWidth = topWrap.getBoundingClientRect().width;
                    this.tabsWrapWidth = totalWidth - topWrapWidth;
                    huiTabs.style.width = `${this.tabsWrapWidth}px`;
                    huiTabs.style.minWidth = `100px`;
                    this.showArrow = totalWidth < this.defaultTabsWidth + topWrapWidth;
                    this.$refs['tabs'].updateBar();
                }
            });
        },
        // 切换产品
        async checkProduct(e) {
            // 修复菜单数据不重置问题
            this.editableTabs.length = 0;
            if (this.productList.length) {
                this.productInfo = e ? _.find(this.productList, ['productInstNo', e]) : this.productList[0];
                this.productInstNo = this.productInfo.productInstNo;
                localStorage.setItem('productInstNo', this.productInfo.productInstNo);
                await this.getDashboardConfig();
                const tab = _.find(this.editableTabs, ['name', this.tabName]);
                this.tabName = tab ? this.tabName : this.editableTabs?.[0]?.name || '';
                this.handleTabChange(this.tabName);
                // 首次渲染调用
                // if (this.firstPaint) {
                //     const { instanceName } = this.$route?.query;
                //     this.$nextTick(() => {
                //         const appInstanceTopology = this.$refs['appInstanceTopology']?.[0];
                //         if (appInstanceTopology) {
                //             appInstanceTopology.handleInstanceSelect(instanceName);
                //         }
                //     });
                // }
                this.firstPaint = false;
            }
        },
        handleTabChange(tabName) {
            this.tabName = tabName;
            this.clearPolling();
            this.$nextTick(async () => {
                // 上下文先初始化菜单
                if (tabName === 'contextTopology') {
                    await this.$refs['contextTopologyMenu'].init();
                } else if (tabName === 'appInstanceTopology') {
                    await this.$refs['appInstanceTopologyMenu'].validQueryParam();
                }
                await this.$refs[tabName]?.[0]?.init();
                this.setTabArrowVisible();
            });
        },
        clearPolling() {
            for (const item of this.editableTabs) {
                const ref = this.$refs[item.name]?.[0];
                ref && ref.clearPolling();
            }
        },
        /**
         * 通过外部设置激活tab设置查询参数，例如下钻
         */
        handleDrillDown(type, arg, tabName = this.tabName) {
            const menuRef = this.$refs[`${tabName}Menu`];
            menuRef.resetParam && menuRef.resetParam();
            // 节点下钻查询
            if (type === 'nodeType') {
                menuRef.setQueryParam(arg, 'nodeType');
            // 连接关系下钻查询
            } else if (type === 'linkType' && this.$refs[tabName]?.[0]) {
                this.$refs[tabName][0].clearData();
                this.tabName = tabName;
                this.setTabArrowVisible();
                this.clearPolling();
                menuRef.setQueryParam(arg, 'linkType');
                this.$refs[`${tabName}`][0].setPolling();
            }
        },
        /**
         * 改变搜索条件时，参数为查询参数
         */
        onChangeQueryParam(param) {
            const tabRef = this.$refs[this.tabName]?.[0];
            if (!tabRef?.getObservableAppTypeTopology) return;
            tabRef.getObservableAppTypeTopology(param);
        },
        /**
         * 设置上下文页面：rcm下拉列表
         */
        setRcmList(list) {
            if (this.$refs['contextTopologyMenu']) {
                this.$refs['contextTopologyMenu'].setRcmList(list);
            }
        },
        /**
         * 上下文：切换rcm
         */
        handleRcmChange(item, list) {
            if (this.$refs['contextTopology']?.[0]) {
                this.$refs['contextTopology'][0].handleRcmChange(item, list);
            }
        },
        /**
         * 上下文：设置主题下拉列表
         */
        setShowTopicList(list) {
            const newList = list.map(item => ({
                label: item.topic,
                value: item.topic
            }));
            if (this.$refs['contextTopologyMenu']) {
                this.$refs['contextTopologyMenu'].setShowTopicList(newList);
            }
        },
        /**
         * 上下文：设置集群下拉列表
         */
        setAppClustersList(list) {
            if (this.$refs['contextTopologyMenu']) {
                this.$refs['contextTopologyMenu'].setAppClustersList(list);
            }
        },
        /**
         * 上下文：设置标签下拉列表
         */
        setTagList(list) {
            if (this.$refs['contextTopologyMenu']) {
                this.$refs['contextTopologyMenu'].setTagList(list);
            }
        },
        /**
         * 设置抽屉
         */
        toggleSetting(bool){
            this.settingData.status = bool;
        },
        /**
         * 抽屉设置变化，只会对应用、上下文起作用
         */
        onChangeSetting({ key, value }) {
            // 应用拓扑，切换未托管节点
            if (key === 'excludeUnmanagedNode') {
                // 当前tab是应用拓扑才会触发刷新，减少不必要的请求
                this.$refs['appInstanceTopology'][0].updateDeepParam({ [key]: !value }, this.tabName === 'appInstanceTopology');
            } else if (key === 'tags') {
                this.$refs['contextTopologyMenu'].updateTags(value, this.tabName === 'contextTopology');
            }
            this.settingData[key] = value;
        }
    }
};
</script>

<style lang="less" scoped>
@import url("@/assets/css/input.less");
@import url("@/assets/css/tab.less");

.topo-container {
    height: 100%;

    &-top {
        height: 44px;
        display: flex;
        align-items: center;
        border-bottom: var(--border);
        justify-content: space-between;
        border-bottom-color: #31364a;

        &-wrap {
            display: flex;

            &-setting {
                background: #262d43;
                border: 1px solid #485565;
                border-radius: 4px;
                cursor: pointer;
                margin-left: 8px;

                .h-icon {
                    color: #fff;
                    width: 30px;
                    height: 30px;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                }
            }
        }

        /deep/ .h-tabs-bar {
            margin-bottom: 0;
        }

        /deep/ .h-tabs-content-wrap {
            display: none;
        }

        &-tabs {
            flex: 1;
            display: flex;
            font-size: 14px;
            color: #fff;
            cursor: pointer;
            margin: 0 !important;
            padding: 0 !important;

            /deep/ .h-tabs-bar {
                border-bottom-color: #31364a;
            }

            div[data-current="true"] {
                color: #2d8de5;
                border-bottom: 2px solid #2d8de5;
            }

            div {
                margin: 0 10px;
                padding: 7px 0;
                border-bottom: 2px solid transparent;
                transition: all 0.5s;

                &:hover {
                    color: #8fc3ff;
                }
            }
        }
    }

    &-view {
        height: calc(100% - 40px);
    }
}
</style>
