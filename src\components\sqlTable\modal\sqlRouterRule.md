执行 SQL 路由会根据所选对象(服务/集群/节点)的关系，并通过预设“路由策略”共同作用，决定最终执行对象。具体规则如下：

### 1. 概念

一级操作：服务 (Service)：面向业务的逻辑操作层，系统根据SQL内容自动决定路由范围。

二级操作：集群/分片 (Cluster/Shard)：面向数据分片的物理操作层，通常由一级操作自动选定，但您也可以直接在此层级操作并选择路由策略。

三级操作：节点 (Node)：最终执行SQL的应用进程，由二级操作的路由策略完全决定。您也可以直接指定，完全忽略路由规则。

### 2. 一级操作：面向“服务”的路由规则

当您选择在“服务”（如“证券交易服务集群”）的维度下执行SQL时，系统会首先对您的SQL语句进行解析，并根据解析结果采用以下三种路由策略之一：

#### 策  略  A：广播路由 (Broadcast Routing)

触发条件：您的SQL语句是 SELECT 查询，但 WHERE 条件中不包含分片Key。

路由行为：系统会将该查询请求发送到您所选服务下的所有集群/分片上并行执行。所有分片的查询结果将被自动汇聚后在一个结果集中展示给您。

注意：此模式主要用于数据探查，为了避免性能问题，请确保您的查询条件能有效缩小结果集。

#### 策  略  B：精确路由 (Precise Routing)

触发条件：您的SQL语句（SELECT, UPDATE, DELETE）的 WHERE 条件中包含了业务定义的分片Key（例如 ... WHERE 资金帐号 = 'xxxx'）。

路由行为：系统将直接把该SQL请求仅发送到包含此分片Key数据的特定集群/分片去执行。这是最高效、最安全的点对点操作方式。

注意：对于判断条件语法的场景时，必须保证条件右侧为具体的值而非函数或子查询等。当且仅当SQL中未命中任何路由分片时走全局路由，否则按照SQL命中的分片进行路由。

<table>
  <thead>
    <tr>
      <th style="width: 20%;">场景类型</th>
      <th style="width: 20%;">具体语法</th>
      <th style="width: 40%;">示例</th>
      <th style="width: 20%;">路由策略</th>
    </tr>
  </thead>
  <tbody>
    <tr>
      <td rowspan="4">基础比较运算符</td>
      <td><strong>=</strong>：等于</td>
      <td>SELECT * FROM employees WHERE salary = 50000;</td>
      <td>命中分区字段则进行精确路由</td>
    </tr>
    <tr>
      <td><strong>&lt;&gt;</strong> 或 <strong>!=</strong>：不等于</td>
      <td>SELECT * FROM users WHERE gender &lt;&gt; 'M';</td>
      <td>命中分区字段则进行精确路由</td>
    </tr>
    <tr>
      <td><strong>&gt;</strong> / <strong>&lt;</strong>：大于/小于</td>
      <td>SELECT * FROM products WHERE price &gt; 10;</td>
      <td>命中分区字段则进行精确路由</td>
    </tr>
    <tr>
      <td><strong>&gt;=</strong> / <strong>&lt;=</strong>：大于等于/小于等于</td>
      <td>SELECT * FROM orders WHERE order_date &gt;= '2023-01-01';</td>
      <td>命中分区字段则进行精确路由</td>
    </tr>
    <tr>
      <td rowspan="3">逻辑运算符组合</td>
      <td><strong>AND</strong>：同时满足所有条件</td>
      <td>（SELECT * FROM company WHERE age &gt;= 25 AND salary &gt;= 65000;）</td>
      <td>命中则多条件进行并集路由，多个条件进行分析后并集分片</td>
    </tr>
    <tr>
      <td><strong>OR</strong>：满足任意条件</td>
      <td>（SELECT * FROM company WHERE age &gt;= 25 OR salary &gt;= 65000;）</td>
      <td>命中则多条件进行并集路由，多个条件进行分析后并集分片</td>
    </tr>
    <tr>
      <td><strong>NOT</strong>：取反条件</td>
      <td>SELECT * FROM users WHERE NOT gender IN ('M', 'F');</td>
      <td>全局路由</td>
    </tr>
    <tr>
      <td rowspan="5">特殊条件运算符</td>
      <td><strong>LIKE</strong>：模糊匹配字符串</td>
      <td>% 匹配任意字符或单个字符_（SELECT * FROM employees WHERE name LIKE 'A%';）</td>
      <td>全局路由</td>
    </tr>
    <tr>
      <td><strong>GLOB</strong>：区分大小写的模式匹配</td>
      <td>SELECT * FROM company WHERE name GLOB 'Ki*';</td>
      <td>全局路由</td>
    </tr>
    <tr>
      <td><strong>IN</strong>：匹配列表中的任意值</td>
      <td>SELECT * FROM products WHERE category IN ('Electronics', 'Books');</td>
      <td>命中分区字段则进行精确路由</td>
    </tr>
    <tr>
      <td><strong>BETWEEN ... AND ...</strong>：范围匹配（含边界）</td>
      <td>SELECT * FROM orders WHERE price BETWEEN 10 AND 50;</td>
      <td>命中分区字段则进行精确路由</td>
    </tr>
    <tr>
      <td><strong>IS NULL</strong> / <strong>IS NOT NULL</strong>：空值检查</td>
      <td>SELECT * FROM employees WHERE manager_id IS NULL;</td>
      <td>全局路由</td>
    </tr>
    <tr>
      <td rowspan="2">子查询条件</td>
      <td><strong>EXISTS</strong>：子查询返回结果时为真</td>
      <td>SELECT age FROM company WHERE EXISTS (SELECT age FROM company WHERE salary &gt; 65000);</td>
      <td>全局路由</td>
    </tr>
    <tr>
      <td>比较运算符 + 子查询</td>
      <td>SELECT * FROM company WHERE age &gt; (SELECT AVG(age) FROM company);</td>
      <td>全局路由</td>
    </tr>
    <tr>
      <td rowspan="5">其它</td>
      <td>聚合函数 + <strong>HAVING</strong>：需与 GROUP BY 联用</td>
      <td>SELECT dept, AVG(salary) FROM employees GROUP BY dept HAVING AVG(salary) &gt; 50000;</td>
      <td>全局路由</td>
    </tr>
    <tr>
      <td><strong>LIMIT</strong> / <strong>OFFSET</strong>：分页控制</td>
      <td>SELECT * FROM company LIMIT 5 OFFSET 10;</td>
      <td>命中分区字段则进行精确路由</td>
    </tr>
    <tr>
      <td><strong>CTE</strong>语法：是一种通过 WITH 子句定义的临时命名结果集，用于简化复杂查询的模块化设计</td>
      <td>WITH [RECURSIVE] cte_name [(column1, column2, ...)] AS ( *-- CTE 查询定义* SELECT ... ) *-- 主查询（引用 CTE）* SELECT ... FROM cte_name ...;</td>
      <td>全局路由</td>
    </tr>
    <tr>
      <td>其它</td>
      <td>-</td>
      <td>全局路由</td>
    </tr>
  </tbody>
</table>

#### 策  略  C：交互式路由 (Interactive Routing)

触发条件：您的SQL语句是 UPDATE 或 DELETE 更新操作时。

路由行为：为了防止大规模的误操作，系统会阻止SQL立即执行，并弹出一个对话框，要求您手动确认希望在哪些集群/分片上执行此更新操作。

安全第一：这是核心的系统安全机制，确保您的每一个全域更新操作都是经过深思熟虑的。

### 3. 二级操作：面向“集群/分片”的路由规则

当您直接选择一个或多个“集群/分片”进行操作时，或者当“服务”级操作将请求路由到具体集群后，您可以选择以下两种节点路由策略来控制SQL在集群内部的执行方式。

#### 策  略  A：读写分离

规则描述：“更新操作到主节点，查询到第一个备节点”。

行为详解：所有 UPDATE, DELETE, INSERT 等更新类操作，将被强制发送到集群的主节点执行，以保证数据写入的唯一性。所有 SELECT 查询类操作，将被发送到集群的第一个可用备节点执行。

适用场景：默认选项，通过将读请求分流到备节点，可以有效降低主节点的负载，适合绝大多数日常查询和维护场景。

#### 策  略  B：强制主节点

规则描述：“查询和更新都强制在主节点”。

行为详解：无论是什么类型的SQL操作（查询或更新），都将被强制发送到集群的主节点上执行。

适用场景：当需要查询可能尚未同步到备节点的最新写入数据时。当备节点不可用或集群处于单主节点（无备节点）模式下运行时。

#### 4. 三级操作：面向“节点”的操作

当您需要精确指定SQL的执行目标节点时适用，此时系统无任何路由策略，直接将SQL发送至目标节点执行并返回执行结果。