<!-- 网关服务 应用抓包发包 配置弹窗 -->
<template>
    <div>
        <h-msg-box-safe
            v-model="modalData.status"
            :mask-closable="false"
            title="配置节点"
            width="700"
            maxHeight="400">
            <h-form ref="formValidate"
                :rules="rules"
                :model="formValidate"
                :label-width="120">
                <h-form-item label="应用节点：">
                    {{ formValidate.instanceNo }}
                </h-form-item>
                <h-form-item label="接入点IP：" prop="endpointIp" required :validRules="ipRule">
                    <h-input
                        v-model.trim="formValidate.endpointIp"
                        placeholder="请输入接入点IP">
                    </h-input>
                </h-form-item>
                <h-form-item label="接入点端口：" prop="endpointPort" required :validRules="portRule">
                    <h-input
                        v-model.trim="formValidate.endpointPort"
                        placeholder="请输入接入点端口"
                        type="int"
                        :maxlength="5">
                    </h-input>
                </h-form-item>
                <h-form-item class="upload" label="发包配置文件：" prop="protocolCertificateAddr">
                    <h-upload
                            action=""
                            accept=".cfg"
                            :before-upload="handleUpload">
                            <div class="upload-box">
                                <div class="upload-box-text">
                                    {{ formValidate.protocolCertificateAddr || "点击上传文件" }}
                                </div>
                                <h-icon name="android-folder-open"></h-icon>
                            </div>
                        </h-upload>
                </h-form-item>
            </h-form>

            <template v-slot:footer>
                <a-button @click="modalData.status = false">取消</a-button>
                <a-button type="primary" :loading="loading" @click="submitConfig">确定</a-button>
            </template>
        </h-msg-box-safe>
    </div>
</template>

<script>
import { fileUpload } from '@/api/productApi';
import { validatePort } from '@/utils/validate';
import aButton from '@/components/common/button/aButton';
import { createSendPacketReceivedEndpoint } from '@/api/memoryApi';

export default {
    name: 'UpdateEndpointModal',
    components: {  aButton },
    props: {
        modalInfo: {
            type: Object,
            default: null
        },
        productInfo: {
            type: Object,
            default: null
        },
        instanceList: {
            type: Array,
            default: () => []
        }
    },
    data() {
        return {
            modalData: this.modalInfo,
            ipRule: ['ip4'],
            portRule: [{ test: validatePort, trigger: 'blur' }],
            formValidate: {
                instanceNo: '',
                endpointIp: '',
                endpointPort: ''
            },
            loading: false,
            file: null,
            btnLoading: false,
            connectStatus: '',
            firstRender: true,
            rules: {
                protocolCertificateAddr: { required: true, message: '请上传文件' }
            }
        };
    },
    mounted() {
        this.formValidate = { ...this.modalData };
    },
    methods: {
        submitConfig() {
            this.$refs['formValidate'].validate((valid) => {
                if (valid) {
                    this.updatePacketEndpoint();
                }
            });
        },
        // 验证上传文件后缀错误
        handleFormatError(file) {
            this.$hNotice.warning({
                title: '文件格式不正确',
                desc: '文件 ' + file.name + ' 格式不正确，请上传cfg格式的文件。'
            });
        },
        // 验证上传文件大小限制
        handleSizeError() {
            this.$hNotice.warning({
                title: '文件大小超出限制',
                desc: '文件大小超出限制，请上传 1MB 以内大小的文件。'
            });
        },
        // 证书上传
        handleUpload(file) {
            this.file = file;
            this.getActionData(file);
            return false;
        },
        // 解析获取文件内容
        async getActionData(file) {
            // 获取文件后缀名，当前只支持 .cfg 后缀的文件
            const suffix = file.name?.substring(file.name?.lastIndexOf('.') + 1);
            if (!['cfg'].includes(suffix)) {
                return this.handleFormatError(file);
            }
            // 限制文件大小 1MB 内
            if (file?.size > 1024 * 1024) {
                return this.handleSizeError();
            }
            const formData = new FormData();
            formData.append('file', file);
            try {
                const res = await fileUpload(formData);
                if (res.code === '200') {
                    this.formValidate.protocolCertificateAddr = res.data.filePath;
                    this.$hMessage.success('上传成功');
                } else {
                    this.$hNotice.error({
                        title: '上传失败',
                        desc: res?.message
                    });
                }
            } catch (res) {
            }
            this.$refs['formValidate'].validateField('protocolCertificateAddr');
        },
        // 调用配置发包节点接口
        async updatePacketEndpoint() {
            this.loading = true;
            try {
                const params = {
                    ...this.formValidate,
                    productInstNo: this.productInfo?.productInstNo
                };
                const res = await createSendPacketReceivedEndpoint(params);
                if (res.success) {
                    this.$hMessage.success('配置成功！');
                    this.$emit('update');
                    this.modalData.status = false;
                }
            } finally {
                this.loading = false;
            }
        }
    }

};
</script>

<style lang="less" scoped>
.upload-config {
    display: flex;
    gap: 10px;
    margin-bottom: 10px;
}

.upload-desc {
    margin-top: -10px;
}

/deep/ .h-upload-click-wrapper {
    width: 100%;
}

.upload-box {
    display: flex;
    cursor: pointer;
    border: 1px solid #ddd;
    border-radius: 4px;
    justify-content: space-between;

    &:hover {
        border-color: #54a4ff;
    }

    .upload-box-text {
        flex: 1;
        padding: 0 10px;
        overflow: hidden; //超出的文本隐藏
        text-overflow: ellipsis; //溢出用省略号显示
        white-space: nowrap; //溢出不换行
    }

    .h-icon {
        padding: 0 10px;
        border-left: 1px solid #ddd;
    }
}
</style>
