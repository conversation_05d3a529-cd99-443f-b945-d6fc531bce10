<template>
    <div>
        <h-msg-box
            v-model="modelVisible"
            title="导出SQL"
            width="70"
            height="75"
            :closable="false"
            :maskClosable="false"
        >
            <h-form
                ref="sqlExportForm"
                :model="sql"
                :label-width="100"
                :rules="rules"
            >
                <h-form-item prop="query" label="用户SQL">
                    <p>
                        <span>{{ instanceName }}</span>
                        <span v-if="isArbActive" class="main-flag" />    <!-- 浅色颜色有问题 -->
                    </p>
                    <h-input
                        :value="sqlQuery"
                        type="textarea"
                        readonly
                        :canResize="false"
                        :rows="3"
                    />
                </h-form-item>
                <h-form-item prop="type" label="SQL类型">
                    <h-radio-group v-model="sql.type">
                        <h-radio label="insert">INSERT</h-radio>
                        <h-radio label="update" :disabled="indices.length === 0">UPDATE</h-radio>
                    </h-radio-group>
                </h-form-item>
                <h-form-item prop="fields" label="表字段">
                    <h-select
                        v-model="sql.fields"
                        class="sql-field-select"
                        placeholder="请选择表字段"
                        not-found-text="不存在表字段"
                        multiple
                        isCheckall
                        positionFixed
                    >
                        <h-option v-for="(item, index) in sqlFields" :key="index" :value="item">
                            {{ item }}
                        </h-option>
                    </h-select>
                </h-form-item>
                <h-form-item v-if="sql.type === 'update'" prop="index" label="唯一索引" not-found-text="不存在唯一索引">
                    <h-select v-model="sql.index" placeholder="请选择唯一索引" positionFixed>
                        <h-option v-for="({ indexName }, index) in indices" :key="index" :value="indexName">
                            {{ indexName }}
                        </h-option>
                    </h-select>
                </h-form-item>
                <h-form-item prop="template" label="SQL模板">
                    <h-input
                        :value="sql.template"
                        type="textarea"
                        readonly
                        :canResize="false"
                        :rows="6"
                    />
                </h-form-item>
            </h-form>
            <template v-slot:footer>
                <a-button
                    :disabled="downloading"
                    @click="closeModal"
                >
                    取消
                </a-button>
                <a-button
                    type="primary"
                    :loading="downloading"
                    @click="downloadSql"
                >
                    导出
                </a-button>
            </template>
        </h-msg-box>
    </div>
</template>

<script>
import aButton from '@/components/common/button/aButton';
import { exportFile } from '@/utils/utils';
import { Parser } from 'node-sql-parser';
import _ from 'lodash';
export default {
    name: 'SqlExportModal',
    components: { aButton },
    props: {
        visible: {
            type: Boolean,
            default: false
        },
        instanceName: {
            type: String,
            default: ''
        },
        isArbActive: {
            type: Boolean,
            default: false
        },
        sqlQuery: {
            type: String,
            default: ''
        },
        sqlFields: {
            type: Array,
            default: []
        },
        getTableMetaData: {
            type: Function
        },
        fetchMemoryDataBySql: {
            type: Function
        }
    },
    data() {
        const ast = new Parser().astify(this.sqlQuery);
        const tableName = ast[0].from[0].table;

        return {
            indices: [],
            sqlData: undefined,
            tableName,
            sql: {
                type: 'insert',
                fields: [],
                index: '',
                template: ''
            },
            rules: {
                query: [{
                    type: 'string',
                    required: true,
                    validator: (_, value, callback) => {
                        return callback();
                    }
                }],
                type: [{
                    type: 'string',
                    required: true
                }],
                fields: [{
                    type: 'array',
                    required: true,
                    message: '请选择表字段',
                    trigger: 'change'
                }],
                index: [{
                    type: 'string',
                    required: true,
                    trigger: 'change',
                    validator: (_, value, callback) => {
                        if (this.sql.type === 'update' && !value) {
                            return callback(new Error('请选择唯一索引'));
                        }
                        return callback();
                    }
                }],
                template: [{
                    type: 'string',
                    required: true,
                    trigger: 'change',
                    validator: (_, value, callback) => {
                        return callback();
                    }
                }]
            },
            downloading: false // 判断文件是否正在生成中
        };
    },
    computed: {
        modelVisible: {
            get() {
                return this.visible;
            },
            set(visible) {
                if (visible === false) {
                    this.$emit('closeModal');
                }
            }
        }
    },
    watch: {
        'sql.type'() {
            this.updateTemplate();
        },
        'sql.fields'() {
            this.updateTemplate();
        },
        'sql.index'() {
            this.updateTemplate();
        }
    },
    mounted() {
        this.getIndices();
    },
    methods: {
        // 获取表对应的索引信息
        async getIndices() {
            try {
                const { indices } = await this.getTableMetaData({ tableName: this.tableName });
                this.indices = indices.filter(({ indexType, filedName }) => this.filterUniqueIndex(indexType, filedName));
                this.sql.index = this.indices[0] ? this.indices[0].indexName : undefined;
            } catch (e) {
                this.$hMessage.error(e.message || '唯一索引获取失败');
            }
        },
        // 筛选符合要求的唯一索引值
        filterUniqueIndex(indexType, filedName) {
            if (indexType !== 'UNIQUE') {
                return false;
            }
            const indices = filedName.split('|');
            return indices.every(index => this.sqlFields.includes(index));
        },
        // 生成模板
        updateTemplate: _.debounce(async function() {
            if (this.sql.type === 'insert') {
                this.sql.template = this.templateInsertSql();
            } else {
                this.sql.template = this.templateUpdateSql();
            }
        }, 50),
        // 获取表数据
        async getSqlData() {
            if (this.sqlData === undefined) {
                this.sqlData = await this.fetchMemoryDataBySql();
            }
            return this.sqlData;
        },
        // 生成 insert 模版
        templateInsertSql(sqlData) {
            if (this.sql.fields.length === 0) {
                // 字段不存在，不需要生成模板
                return '';
            }

            const fields = this.sql.fields.join(',');
            if (sqlData && Array.isArray(sqlData)) {
                // 生成真实的 sql 语句
                const template = sqlData.map(data => {
                    const fieldsValue = this.sql.fields.map(field => `"${data[field]}"`).join(',');
                    return `INSERT INTO ${this.tableName} (${fields}) VALUES (${fieldsValue});`;
                });
                return template.join('\n');
            }

            // 生成页面上显示的数据
            const fieldsValue = this.sql.fields.map(field => `{${field}}`).join(',');
            return `INSERT INTO ${this.tableName} (${fields}) VALUES (${fieldsValue});`;
        },
        // 生成 update 模板
        templateUpdateSql(sqlData) {
            if (this.sql.fields.length === 0 || this.sql.index.length === 0) {
                // 字段\唯一索引不存在，不需要生成模板
                return '';
            }

            const indices = this.indices.find(({ indexName }) => indexName === this.sql.index).filedName.split('|');
            if (sqlData && Array.isArray(sqlData)) {
                // 生成真实的 sql 语句
                const template = sqlData.map((data) => {
                    const fieldsValue = this.sql.fields.map(field => `${field}="${data[field]}"`).join(',');
                    const indicesValue = indices.map(index => `${index}="${data[index]}"`).join(' AND ');
                    return `UPDATE ${this.tableName} SET ${fieldsValue} WHERE ${indicesValue};`;
                });
                return template.join('\n');
            }

            // 生成模板语句
            const fieldsValue = this.sql.fields.map(field => `${field}={${field}}`).join(',');
            const indicesValue = indices.map(index => `${index}={${index}}`).join(' AND ');
            return `UPDATE ${this.tableName} SET ${fieldsValue} WHERE ${indicesValue};`;
        },
        // 关闭弹框
        closeModal() {
            if (this.downloading) {
                // 生成过程中，不能取消
                return;
            }
            this.modelVisible = false;
        },
        // 导出文件
        async downloadSql() {
            return new Promise(resolve => {
                this.$refs['sqlExportForm'].validate(async valid => {
                    try {
                        if (!valid) {
                            resolve();
                            return;
                        }

                        this.downloading = true;
                        const fileData = await this.generateSqlFileData();
                        exportFile(fileData, `${this.tableName}-${this.sql.type}-${this.formatDate()}`, 'sql');
                        this.downloading = false;
                        this.closeModal();
                        resolve();
                    } catch (e) {
                        this.$hMessage.error(e.message || '导出SQL失败');
                        this.downloading = false;
                        resolve();
                    }
                });
            });
        },
        async generateSqlFileData() {
            if (this.sql.type === 'insert') {
                return this.generateInsertSqlFileData();
            }
            return this.generateUpdateSqlFileData();
        },
        async generateInsertSqlFileData() {
            if (this.sql.fields.length === 0) {
                // 字段不存在，不需要生成模板
                return '';
            }

            const sqlData = await this.getSqlData();
            return this.templateInsertSql(sqlData);
        },
        async generateUpdateSqlFileData() {
            if (this.sql.fields.length === 0 || this.sql.index.length === 0) {
                // 字段\唯一索引不存在，不需要生成模板
                return '';
            }

            const sqlData = await this.getSqlData();
            return this.templateUpdateSql(sqlData);
        },
        formatDate() {
            const now = new Date();
            const pad = n => n < 10 ? '0' + n : n;
            const year = now.getFullYear();
            const month = pad(now.getMonth() + 1);
            const day = pad(now.getDate());
            const hour = pad(now.getHours());
            const minute = pad(now.getMinutes());
            const second = pad(now.getSeconds());
            return `${year}-${month}-${day}_${hour}:${minute}:${second}`;
        }
    }
};
</script>

<style scoped lang="less">
.node-flag(@url) {
    display: inline-block;
    width: 11px;
    height: 11px;
    background: url(@url);
    background-size: 11px 10px;
    background-repeat: no-repeat;
    position: relative;
    top: 2px;
    margin-left: 5px;
}

.main-flag {
    .node-flag("static/mainFlag.png");
}

/deep/ .sql-field-select.h-select {
    height: auto;
    min-height: 32px;
    max-height: 112px;
}

/deep/ .sql-field-select.h-select .h-select-selection {
    height: auto !important;
    min-height: 32px;
    max-height: 112px;
}

/deep/ .h-input-wrapper {
    .h-input {
        line-height: 20px;
    }
}
</style>
