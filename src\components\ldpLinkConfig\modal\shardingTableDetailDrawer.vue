<template>
  <div>
    <h-drawer
      ref="drawer-box"
      v-model="modalData.status"
      :title="`已加载内存表（${tableData.length}）`"
      width="50"
      @on-close="handleClose"
    >
      <a-table
        border
        :hasPage="false"
        :height="tableHeight"
        :columns="logColumns"
        :tableData="tableData"
        :loading="loading"
        showTitle
      />
    </h-drawer>
  </div>
</template>

<script>
import { getTablesMetaInfo } from '@/api/productApi'; // 引入获取表元数据信息的API接口
import aTable from '@/components/common/table/aTable'; // 引入 obsTable 组件
import { transferVal } from '@/utils/utils'; // 引入工具方法
import importStatusTableIcon from '@/components/secondAppearance/importStatusTableIcon.vue'; // 有用

export default {
    name: 'ShardingTableDetailDrawer', // 组件名称
    components: {
        aTable
    },
    props: {
        modalInfo: {
            type: Object, // 属性类型为对象
            default: () => ({}) // 默认值为空对象
        }
    },
    data() {
        return {
            modalData: this.modalInfo, // 弹窗数据
            tableHeight: 0, // 表格高度
            logColumns: [
                {
                    title: '表名',
                    key: 'tableName',
                    ellipsis: true,
                    minWidth: 150
                },
                {
                    title: '是否分片表',
                    key: 'isSharding',
                    render: this.renderTrueFalse,
                    minWidth: 110
                },
                {
                    title: '是否主备同步',
                    key: 'hasStandbySync',
                    render: this.renderTrueFalse,
                    minWidth: 110
                },
                {
                    title: '是否启用',
                    key: 'isEnabled',
                    render: this.renderTrueFalse,
                    minWidth: 110
                }
            ],
            tableData: [], // 表格数据
            loading: false // 加载状态
        };
    },
    mounted() {
        this.handleDrawerOpen(); // 打开抽屉时的处理
        window.addEventListener('resize', this.setTableHeight); // 监听窗口 resize 事件
    },
    beforeDestroy() {
        window.removeEventListener('resize', this.setTableHeight); // 移除窗口 resize 事件监听
    },
    methods: {
        renderTrueFalse(h, params) { // 判断值的真假的渲染方法
            const value = transferVal(params?.row[params?.column?.key]);
            const enumsList = {
                true: 'success',
                false: 'error'
            };
            return <importStatusTableIcon type={enumsList[value] || ''} />;
        },
        handleDrawerOpen() { // 打开抽屉时的处理
            this.clearTableData(); // 清空表格数据
            this.getTablesMetaInfo(); // 获取表的元数据
            this.setTableHeight(); // 设置表格高度
        },
        handleClose() { // 关闭抽屉时的处理
            this.clearTableData(); // 清空表格数据
            this.modalData.status = false; // 设置弹窗状态为关闭
        },
        clearTableData() { // 清空表格数据
            this.tableData = [];
        },
        async getTablesMetaInfo() { // 获取表的元数据信息的接口请求
            this.loading = true; // 设置加载状态为 true
            const param = {
                productId: this.modalInfo?.productId,
                clusterId: this.modalInfo?.clusterId,
                instanceType: this.modalInfo?.instanceType
            };
            try {
                const res = await getTablesMetaInfo(param); // 发送请求获取数据
                if (res?.code === '200') {
                    this.tableData = res?.data || []; // 设置表格数据
                } else if (res.code.length === 8) {
                    this.$hMessage.error(res.message);
                }
            } catch (err) {
                console.error('Failed to get table metadata info:', err);
            } finally {
                this.loading = false; // 设置加载状态为 false
            }
        },
        setTableHeight() { // 设置表格高度
            const offsetTop = this.$refs['drawer-box']?.$el?.offsetTop || 0;
            const headerOffset = window.LOCAL_CONFIG.HEADER_VISIBLE === true ? 185 : 105;
            this.tableHeight = Math.max(offsetTop - headerOffset, 200); // 确保表格高度至少为 200
        }
    }
};
</script>

<style lang="less" scoped>
@import url("@/assets/css/drawer.less"); // 引入样式文件

.h-drawer-body {
    overflow: hidden;
}
</style>
