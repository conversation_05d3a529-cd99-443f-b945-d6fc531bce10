<template>
    <div ref="rule-configure" class="rule-configure">
        <h-select
            v-model="batchMode"
            placeholder="批量修改上场方式"
            class="batch-mode-selector"
            @on-change="handleBatchModeChange"
        >
            <h-option
                v-for="option in batchOptions"
                :key="option.value"
                :value="option.value">
                {{ option.label }}
            </h-option>
        </h-select>

        <a-simple-table
            showTitle
            :border="true"
            :tableData="tableData"
            :columns="columns"
            :hasPage="false"
            :height="configTableHeight"
        />
    </div>
</template>

<script>
import aSimpleTable from '@/components/common/table/aSimpleTable';
import { LOADING_TYPE_OPTIONS } from '@/components/dataSecondAppearance/constant.js';

export default {
    name: 'ConfigureRules',
    components: {
        aSimpleTable
    },
    props: {
        tableRulesList: {
            type: Array,
            default: () => []
        }
    },
    data() {
        return {
            batchMode: '',
            batchOptions: LOADING_TYPE_OPTIONS,
            configTableHeight: 0,
            columns: this.getColumns(),
            tableData: []
        };
    },

    watch: {
        tableRulesList: {
            immediate: true,
            handler(newVal) {
                this.tableData = newVal.slice();
            }
        }
    },
    mounted() {
        window.addEventListener('resize', this.setTableHeight);
        this.$nextTick(() => {
            this.setTableHeight();
        });
    },
    beforeDestroy() {
        window.removeEventListener('resize', this.setTableHeight);
    },
    methods: {
        /**
         * 获取表格列配置
         * @returns {Array} 表格列配置数组
         */
        getColumns() {
            return [
                {
                    title: '表名',
                    key: 'tableName',
                    minWidth: 120,
                    ellipsis: true
                },
                {
                    title: '集群',
                    key: 'clusterName',
                    minWidth: 100,
                    ellipsis: true
                },
                {
                    title: '分片',
                    key: 'shardingNo',
                    ellipsis: true
                },
                {
                    title: '加载方式',
                    key: 'importMode',
                    minWidth: 150,
                    render: this.renderImportModeSelect
                },
                {
                    title: '上场规则',
                    key: 'importSql',
                    minWidth: 500,
                    renderHeader: this.renderImportSqlHeader,
                    render: this.renderImportSqlInput
                }
            ];
        },
        /**
         * 渲染'加载方式'选择框
         * @param {Function} h - 渲染函数
         * @param {Object} params - 当前行参数
         * @returns {VNode} 渲染的选择框节点
         */
        renderImportModeSelect(h, params) {
            return (
                <h-select
                    v-model={params.row.importMode}
                    transfer
                    clearable={false}
                    on-on-change={(value) => this.handleSelectChange(params, value)}
                >
                    {LOADING_TYPE_OPTIONS.map(option => (
                        <h-option
                            key={option.value}
                            value={option.value}>
                            {option.label}
                        </h-option>
                    ))}
                </h-select>
            );
        },
        /**
         * 渲染'上场规则'列的表头
         * @param {Function} h - 渲染函数
         * @returns {VNode} 渲染的表头节点
         */
        renderImportSqlHeader(h) {
            return h('div', [
                h('span', {
                    style: {
                        color: '#fff',
                        verticalAlign: '2px',
                        marginRight: '5px'
                    }
                }, '上场规则'),
                h('poptip',
                    {
                        class: 'apm-poptip',
                        props: {
                            trigger: 'hover',
                            placement: 'top',
                            positionFixed: true
                        }
                    },
                    [
                        h('icon', {
                            props: {
                                name: 'prompt1',
                                color: '#9296A1',
                                size: '14'
                            }
                        }),
                        h('div',
                            {
                                slot: 'content',
                                class: 'pop-content',
                                style: {
                                    width: '160px',
                                    padding: '10px',
                                    lineHeight: '20px'
                                }
                            },
                            [
                                h('div',
                                    [
                                        h('p',
                                            {
                                                style: {
                                                    whiteSpace: 'normal'
                                                }
                                            },
                                            '可对每个表单独设置上场数据的查询条件；不设置规则即表示全表上场。'
                                        )
                                    ]
                                )
                            ]
                        )
                    ]
                )
            ]);
        },
        /**
         * 渲染'上场规则'输入框
         * @param {Function} h - 渲染函数
         * @param {Object} params - 当前行参数
         * @returns {VNode} 渲染的输入框节点
         */
        renderImportSqlInput(h, params) {
            return (
                <h-input
                    v-model={params.row.importSql}
                    placeholder="输入上场规则"
                    maxlength={1000}
                    on-on-blur={(event) => this.handleInputChange(params, event.target.value)}
                />
            );
        },
        /**
         * 批量修改上场方式
         */
        handleBatchModeChange() {
            this.tableData.forEach(row => {
                row.importMode = this.batchMode;
            });
            this.$emit('update-rules', this.tableData);
        },
        /**
         * 处理选择框数据变化
         * @param {Object} params - 当前行参数
         * @param {String} value - 当前选择值
         */
        handleSelectChange(params, value) {
            // 更新当前行数据
            params.row.importMode = value;

            // 查找并更新 this.tableData 中对应的行数据
            const index = params.row._index;
            if (index !== -1) {
                this.tableData.splice(index, 1, { ...params.row });
            }

            // 发出 update-rlues 事件，通知父组件更新
            this.$emit('update-rules', this.tableData);
        },
        /**
         * 处理输入框数据变化
         * @param {Object} params - 当前行参数
         * @param {String} value - 当前输入值
         */
        handleInputChange(params, value) {
            params.row.importSql = value;

            // 查找并更新 this.tableData 中对应的行数据
            const index = params.row._index;
            if (index !== -1) {
                this.tableData.splice(index, 1, { ...params.row });
            }
            this.$emit('update-rules', this.tableData);
        },
        /**
         * 设置表格高度
         */
        setTableHeight() {
            this.configTableHeight = this.$el?.offsetHeight - 60;
        }
    }
};
</script>

<style lang="less" scoped>
.rule-configure {
    height: 100%;
}

.batch-mode-selector {
    width: 200px;
}

</style>
