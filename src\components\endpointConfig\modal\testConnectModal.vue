<!-- 测试连通性弹窗 -->
<template>
    <div class="connect">
        <h-msg-box-safe v-model="modalInfo.status" footerHide title="连通性测试" :mask-closable="false" width="950"
            maxHeight="400">
            <template>
                <div class="connect-wrap">
                    <div class="connect-wrap-label">选择接入点:</div>
                    <div class="connect-wrap-select">
                        <h-select :value="selectedEndpoint" positionFixed width="200"
                            :clearable="false" @on-change="handleChangeEndpoint">
                            <h-option v-for="item in onlineEndpoints" :key="item.id" :value="item.id">{{ item.name
                                }}</h-option>
                        </h-select>
                    </div>
                    <div class="connect-wrap-btn">
                        <a-button :loading="isTesting" @click="test">测试</a-button>
                    </div>
                </div>
                <div class="connect-table">
                    <h-table height="300" loadingText="测试中" :loading="isTesting" :columns="columns"
                        :data="dataSource" />
                </div>
            </template>
        </h-msg-box-safe>
    </div>
</template>

<script>
import { getMonitorHeartbeats } from '@/api/httpApi';
import aButton from '@/components/common/button/aButton';
import {
    PROTOCOL_LIST_MAP,
    LOCATE_TEST_CONNECT_COL,
    ENDPOINT_TYPE,
    TEST_CONNECT,
    TEST_CONNECT_RESULT_TITLE,
    QUERY_ONLINE_ENDPINTS_MAP,
    DEFAULT_TEST_CONNECT_COL,
    SEND_PACKET_TEST_CONNECT_COL
} from '../constant';
export default {
    props: {
        modalInfo: {
            type: Object,
            default: () => { }
        },
        productInfo: {
            type: Object
        },
        /**
     * 接入点类型
     */
        endpointType: {
            type: String,
            default: () => ENDPOINT_TYPE.MDB
        }
    },
    data() {
        return {
            loading: false,
            selectedEndpoint: undefined,
            isFetchingEndpoints: false,
            isTesting: false,
            endpoints: [],
            nodeList: [],
            dataSource: [],
            onlineEndpoints: [],
            columns: this.getColByEndpointType().concat([
                {
                    title: TEST_CONNECT_RESULT_TITLE[this.endpointType],
                    render: (h, params) =>
                        h(
                            'span',
                            { style: { color: params.row?.testResult ? 'green' : 'red' } },
                            params.row?.testResult ? '访问成功' : '访问失败'
                        )
                }
            ])
        };
    },
    methods: {
        handleChangeEndpoint(val) {
            this.selectedEndpoint = val;
        },
        /**
         * 查询在线接入点
         */
        async queryOnlineEndpoints() {
            const apiName = QUERY_ONLINE_ENDPINTS_MAP[this.endpointType];
            const res = await apiName({
                productInstNo: this.productInfo.productInstNo,
                productId: this.productInfo.productInstNo,
                hasFilterOffline: true,
                protocol: PROTOCOL_LIST_MAP[this.endpointType]?.[0],
                hasOnlyCurrentServer: true,
                type: this.endpointType
            });
            if (res?.data?.length) {
                this.onlineEndpoints = res.data;
                this.selectedEndpoint = res.data[0].id;
            }
        },
        /**
         * 开始连通性检测
         */
        async test() {
            if (!this.selectedEndpoint) return;
            try {
                this.isTesting = true;
                const apiName = TEST_CONNECT[this.endpointType];
                if (!apiName) {
                    console.warn('不支持的接入点类型 ', this.endpointType);
                    return;
                }
                const res = await apiName({
                    endpointId: this.selectedEndpoint,
                    productId: this.productInfo.productInstNo
                });
                if (res?.data?.length === 0) {
                    this.$hMessageSafe.warning({
                        content: '暂无节点'
                    });
                    return;
                }
                if (res?.data) {
                    const heartbeats = await getMonitorHeartbeats({
                        productId: this.productInfo.productInstNo,
                        type: 'instance'
                    });
                    const tableData = [];
                    for (const instance of res.data) {
                        let status = '-';
                        const findInstance = heartbeats?.data?.find(
                            (item) => item.id === instance.instanceId
                        );
                        if (findInstance) {
                            status =
                                findInstance.status === 'runing' ||
                                    findInstance.status === 'warning'
                                    ? '在线'
                                    : '离线';
                        }
                        tableData.push({
                            ...instance,
                            status
                        });
                    }
                    this.dataSource = tableData;
                }
            } finally {
                this.isTesting = false;
            }
        },
        /**
         * 根据接入点协议，生成表头
         */
        getColByEndpointType() {
            switch (this.endpointType) {
                case ENDPOINT_TYPE.MANAGEMENT:
                    return [
                        {
                            title: '应用节点名',
                            key: 'instanceName'
                        },
                        {
                            title: '应用节点类型',
                            key: 'instanceType',
                            render: (h, params) => {
                                return h(
                                    'div',
                                    this.$store?.state?.apmDirDesc?.appTypeDictDesc?.[
                                        params.row.instanceType
                                    ] || params.row.instanceType
                                );
                            },
                            minWidth: 120
                        },
                        {
                            title: '应用身份',
                            key: 'instanceIdentities',
                            minWidth: 120,
                            render: (h, params) => {
                                return h(
                                    'span',
                                    params.row?.instanceIdentities
                                        ?.map((item) => {
                                            return this.$store?.state?.apmDirDesc
                                                ?.instanceIdentityDict?.[item];
                                        })
                                        ?.join(',') ?? '-'
                                );
                            }
                        },
                        {
                            title: '管理ip',
                            key: 'manageProxyIp'
                        },
                        {
                            title: '管理端口',
                            key: 'manageProxyPort'
                        }
                    ];
                case ENDPOINT_TYPE.SEND_PACKET_RECEIVED:
                    return SEND_PACKET_TEST_CONNECT_COL;
                case ENDPOINT_TYPE.LOCATE:
                    return LOCATE_TEST_CONNECT_COL;
                default:
                    return DEFAULT_TEST_CONNECT_COL;
            }
        }
    },
    mounted() {
        this.queryOnlineEndpoints();
    },
    components: { aButton }
};
</script>

<style lang="less" scoped>
p {
    line-height: 150%;
    margin: -20px 0 20px 120px;
    color: var(--font-opacity-color);
}

.connect {
    &-table {
        margin-top: 10px;
    }

    &-wrap {
        display: flex;
        align-items: center;

        &-select {
            margin: 0 10px;
        }
    }
}
</style>
