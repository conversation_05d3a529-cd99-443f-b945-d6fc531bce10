import fetch from './fetch';
import { objectToQueryString } from '@/utils/utils';

const prefix = window['LOCAL_CONFIG']['API_HOME'] || '/';

// ---------------------------  usttable数据校验 ----------------------------------------------------

// 对比启动、停止操作
export function setCompareOperate(param) {
    return fetch().post(`${prefix}/product/data-compare`, param);
}

// 创建校验任务
export function createVerificationTask(param) {
    return fetch().post(`${prefix}/product/data-compare/rule`, param);
}

// 获取数据校验任务列表
export function getVerificationTaskList(param) {
    return fetch().get(`${prefix}//product/data-compare/rules?${objectToQueryString(param)}`);
}

// 删除校验任务
export function deleteCompareRule(param) {
    return fetch().post(`${prefix}/product/data-compare/rule/delete`, param);
}

// 获取表字段规则历史比对结果列表
export function getFieldCompareHistories(param) {
    return fetch().get(`${prefix}/product/data-compare/table-field/rule/results?${objectToQueryString(param)}`);
}

// 获取表记录总数规则历史比对结果列表
export function getRecordCompareHistories(param) {
    return fetch().get(`${prefix}/product/data-compare/table-record-count/rule/results?${objectToQueryString(param)}`);
}

// 表记录总数 获取比对详情
export function getTableCountDetail(param) {
    return fetch().get(`${prefix}/product/data-compare/table-record-count/rule/result?${objectToQueryString(param)}`);
}

// 比对结果详情下载
export function downloadCompareResult(param) {
    return fetch({ responseType: 'blob' }).get(`${prefix}/product/data-compare/table-field/rule/result/file?${objectToQueryString(param)}`);
}
