<template>
    <div class="tab-box">
        <a-loading v-show="loading"></a-loading>
        <div v-show="!loading">
            <div class="overview-group">
                <directionCross
                    v-for="item in overviewGroup" :key="item.title"
                    :title="item.title"
                    :dataDic="item.dataDic"
                    :data="item.data">
                </directionCross>
            </div>

            <!-- 主题生产消费消息变化趋势 -->
            <chart v-if="isChartActive" ref="chart2"
                :title="chartObj2.title"
                :slotHeight="groupType === 'singleTopic' ? 84 : 40"
                :basicOpiton="chartObj2.basicOpiton"
                :additionalOpiton="chartObj2.additionalOpiton"
                :chartData="chartObj2.chartData"
                :style="`height: ${groupType === 'singleTopic' ? 400 : 356}px;`">
                <template v-slot>
                    <h-radio-group
                        v-model="groupType"
                        class="radio-group"
                        @on-change="selectGroupChange">
                        <h-radio text="全主题统计" label="allTopic"></h-radio>
                        <h-radio text="单主题统计" label="singleTopic"></h-radio>
                    </h-radio-group>
                    <div v-show="groupType === 'singleTopic'" class="select-group">
                        <span v-for="item in selectGroup" :key="item.key">
                            <h-simple-select
                                v-if="item.key==='topic'"
                                v-model="item.selectValue"
                                :clearable="false"
                                :placeholder="item.placeholder"
                                :filterable="item.filterable"
                                :filterMethod="topicFilterMethod"
                                :multiple="item.isMultiple"
                                :class="item.class"
                                @on-change="(val) => selectGroupChange(val, item.key)">
                                <h-select-block :data=item.options></h-select-block>
                            </h-simple-select>
                            <h-select
                                v-else
                                v-model="item.selectValue"
                                :clearable="false"
                                :placeholder="item.placeholder"
                                :filterable="item.filterable"
                                :multiple="item.isMultiple"
                                :multipleNumber="item.multipleNumber"
                                :class="item.class"
                                @on-change="(val) => selectGroupChange(val, item.key)">
                                <h-option v-for="option in item.options" :key="option.value"
                                    :value="option.value">{{option.label}}
                                </h-option>
                            </h-select>
                        </span>
                        <a-button type="dark" style="float: right; margin-top: 5px;" @click="refreshTopicList">刷新</a-button>
                    </div>
                </template>
            </chart>

            <!-- 集群线程队列消息变化趋势 -->
            <chart ref="chart1"
                :title="chartObj1.title"
                :basicOpiton="chartObj1.basicOpiton"
                :chartData="chartObj1.chartData"
                style="height: 300px;"
                @select-change="chartSelectChange">
            </chart>
        </div>
    </div>
</template>

<script>
import _ from 'lodash';
import { getManagerProxy } from '@/api/mcApi';
import aButton from '@/components/common/button/aButton';
import chart from '@/components/common/chart/chart';
import aLoading from '@/components/common/loading/aLoading';
import directionCross from '@/components/common/description/directionCross';
import { formatChartNumber } from '@/utils/utils';
import { sumSet, batchSumSet, batchSum, sum, filterSum, sumFilterRemove, getTopicPubMsgCount,
    mutiHostMergeArr, removeDuplicates, generateApiParam, handleMutiIpFunc } from '@/components/mcDataObservation/constant';
const POINT_COUNT = 60;
export default {
    name: 'McOverview',
    props: {
        nodeData: {
            type: Object,
            default: () => {}
        }
    },
    components: { aButton, aLoading, chart, directionCross },
    data() {
        return {
            loading: false,
            isChartActive: true,
            overviewGroup: [
                {
                    title: '集群',
                    dataDic: [
                        { label: '节点数', key: 'brokerNum' },
                        { label: '会话数', key: 'sessionNum' }
                    ],
                    data: {
                        brokerNum: '-',
                        sessionNum: '-'
                    }
                },
                {
                    title: '主题',
                    dataDic: [
                        { label: '主题总数', key: 'topicTotalNum' },
                        { label: '持久化主题数', key: 'topicPersistNum' },
                        { label: '分区总数', key: 'partitionTotalNum' },
                        { label: '分区副本总数', key: 'partitionReplicasNum' }
                    ],
                    data: {
                        topicTotalNum: '-',
                        topicPersistNum: '-',
                        partitionTotalNum: '-',
                        partitionReplicasNum: '-'
                    }
                },
                {
                    title: '生产者',
                    dataDic: [
                        { label: '生产者总数', key: 'pubMsgNum' },
                        { label: '生产消息总数', key: 'pubTopicMsgNum' }
                    ],
                    data: {
                        pubMsgNum: '-',
                        pubTopicMsgNum: '-'
                    }
                },
                {
                    title: '消费者',
                    dataDic: [
                        { label: '消费者总数', key: 'subscriberNum' },
                        { label: '订阅项总数', key: 'subscriberInfoNum' },
                        { label: '推送消息总数', key: 'subscriberPushNum' }
                    ],
                    data: {
                        subscriberNum: '-',
                        subscriberInfoNum: '-',
                        subscriberPushNum: '-'
                    }
                }
            ],

            chartObj1: {
                title: {
                    label: '集群线程队列消息变化趋势',
                    slots: [
                        {
                            type: 'select',
                            key: 'broker',
                            defaultValue: 'all',
                            options: [
                                {
                                    value: 'all',
                                    label: '集群'
                                }
                            ]
                        }
                    ]
                },
                basicOpiton: {
                    chartType: 'axis',
                    lineDeploy: [
                        {
                            name: '会话线程',
                            type: 'line'
                        },
                        {
                            name: '订阅线程',
                            type: 'line'
                        },
                        {
                            name: '推送线程',
                            type: 'line'
                        },
                        {
                            name: '发布线程',
                            type: 'line'
                        },
                        {
                            name: '主题线程',
                            type: 'line'
                        }
                    ],
                    yAxiDeploy: [
                        {
                            name: '个',
                            axisLabel: {
                                formatter: formatChartNumber
                            }
                        }
                    ]
                },
                chartData: {
                    xData: [],
                    data: {
                        会话线程: [],
                        订阅线程: [],
                        推送线程: [],
                        发布线程: [],
                        主题线程: []
                    }
                }
            },
            chartObj2: {
                title: {
                    label: '主题生产消费消息变化趋势'
                },
                basicOpiton: {
                    chartType: 'axis',
                    lineDeploy: [
                        {
                            name: '发布消息变化差量',
                            type: 'line'
                        },
                        {
                            name: '订阅消息变化差量',
                            type: 'line'
                        }
                    ],
                    yAxiDeploy: [
                        {
                            name: '消息数(个)',
                            axisLabel: {
                                formatter: formatChartNumber
                            }
                        }
                    ]
                },
                chartData: {
                    xData: [],
                    data: {
                        发布消息变化差量: [],
                        订阅消息变化差量: []
                    }
                }
            },
            groupType: 'allTopic', // 全主题、单主题模式

            selectGroup: [
                {
                    key: 'topic',
                    selectValue: '',
                    placeholder: '请选择主题',
                    class: 'muti-select',
                    filterable: true,
                    options: []
                },
                {
                    key: 'publish',
                    selectValue: [],
                    isMultiple: true,
                    filterable: true,
                    multipleNumber: 3,
                    placeholder: '请选择生产者',
                    class: 'muti-select',
                    options: []
                },
                {
                    key: 'subscribe',
                    selectValue: [],
                    isMultiple: true,
                    filterable: true,
                    multipleNumber: 3,
                    placeholder: '请选择消费者',
                    class: 'muti-select',
                    options: []
                }
            ],

            brokerInfo: {}, // 集群主机列表
            brokerSelect: '', // 集群下拉框
            brokerType: '', // 集群类型
            overviewData: {},
            brokerData: {},
            timer: null,

            historyDeviation: {},
            msgFuncObj: {}, // 上次请求各节点生产、订阅消息管理功能数据
            topicNumSumObj: {} // 上次请求保存发布、订阅消息变化差量总量值
        };
    },
    methods: {
        initData() {
            this.loading = true;
            // 切换节点重置数据
            this.cleanChartData();
            this.$nextTick(async () => {
                await this.getFileData();
            });
            this.loading = false;
        },
        // 清空折线图数据
        cleanChartData() {
            this.overviewData = {};
            this.brokerData = {};
            this.msgFuncObj = {};
            this.topicNumSumObj = {};
            this.resetBrokerChart();
            this.groupType = 'allTopic';
            this.$refs['chart1'].$refs['obs-title'].setSelectVal('broker', 'all');
            this.resetAllTopicMsg();
        },
        // 下拉框搜索逻辑 - 从头开始匹配
        topicFilterMethod(val, obj) {
            if (obj?.label.toLowerCase().startsWith(val.toLowerCase())) return true;
            return false;
        },
        // LDP管理功能批量请求代理接口统一处理返回数据 - 汇总用
        async getBatchManageInfo(ipPortList, funcNameList) {
            const param = generateApiParam(ipPortList, funcNameList);
            try {
                const res = await getManagerProxy(JSON.stringify(param));
                if (res.code === '200') {
                    const handleData = {};
                    Array.isArray(param) && param.forEach((item, index) => {
                        handleData[item.funcName] = (handleData?.[item?.funcName] || []).concat(res?.data?.[index]);
                    });
                    this.overviewData = handleData; // 数据按功能号区分
                    const brokerData = {};
                    Array.isArray(param) && param.forEach((item, index) => {
                        brokerData[`${item.instanceName}`] = brokerData[`${item.instanceName}`] || {};
                        brokerData[`${item.instanceName}`][item.funcName] = [res?.data?.[index]]; // 赋值为数组，可以用通用方法
                    });
                    this.brokerData = brokerData; // 数据按集群区分
                } else {
                    this.overviewData = {};
                    this.brokerData = {};
                }
            } catch (err) {
                this.$emit('clear');
            }
        },
        // 处理折线图数据方法
        handleChartData(chartDataList, newTime) {
            Array.isArray(chartDataList) && chartDataList.forEach(chartData => {
                if (chartData.xData.length > POINT_COUNT) {
                    chartData.xData.shift();
                    Object.values(chartData.data).forEach(item => {
                        item.shift();
                    });
                }
                chartData.xData.push(newTime);
            });
        },
        // 更新总览页数据
        async getFileData() {
            // 处理折线图x轴数据
            const newTime = this.$getCurrentLocalTime();
            const chartxData1 = this.chartObj1.chartData;
            const chartxData2 = this.chartObj2.chartData;
            this.handleChartData([chartxData1, chartxData2], newTime);

            const funcNameList = ['GetBrokerInfo', 'GetSessionInfo', 'GetTopicInfo', 'GetPubMsgNo', 'GetTopicSubPubInfo', 'GetAllSubscribers',
                'GetAllSubscribeInfo', 'GetSessionThreadInfo', 'GetSubPushThreadInfo', 'GetPublishQue', 'GetTopicSvrQue'];
            await this.getBatchManageInfo(this?.nodeData?.productInstances, funcNameList);
            this.handleOverviewInfo();
            this.handleBrokerChartInfo(newTime);
            this.handelTopicChartInfo(newTime);
        },
        // 概况信息数据处理
        handleOverviewInfo() {
            const { GetBrokerInfo, GetSessionInfo, GetTopicInfo, GetPubMsgNo, GetTopicSubPubInfo, GetAllSubscribers, GetAllSubscribeInfo } = this.overviewData;
            // 判断节点是否为单例，如果为单例节点数为1
            this.brokerInfo = handleMutiIpFunc(GetBrokerInfo, 'Result') || {};
            const brokerArr = [];
            Object.values(this.brokerInfo).forEach(item => {
                !brokerArr.includes(item.Broker) && brokerArr.push(item.Broker);
            });
            this.brokerType = brokerArr.length === 1 ? 'single' : 'cluster'; // 集群类型

            // 集群
            this.overviewGroup[0].data = {
                brokerNum: brokerArr.length === 1 ? 1 : brokerArr?.length,
                sessionNum: batchSum(GetSessionInfo, '', (data) => { return data?.length || 0; })
            };
            // 主题
            const topicInfo = handleMutiIpFunc(GetTopicInfo);
            this.overviewGroup[1].data = {
                topicTotalNum: topicInfo?.length || 0,
                topicPersistNum: topicInfo?.filter(item => item.ReliableLevel === 1)?.length || 0,
                partitionTotalNum: sum(topicInfo, 'PartitionCount'),
                partitionReplicasNum: sum(topicInfo, 'ReplicationFactor')
            };
            // 生产者
            const topicPubMsgCount = getTopicPubMsgCount(mutiHostMergeArr(GetTopicSubPubInfo));
            this.overviewGroup[2].data = {
                pubMsgNum: batchSumSet(GetPubMsgNo, 'PubName', sumSet),
                pubTopicMsgNum: topicPubMsgCount
            };
            // 消费者
            this.overviewGroup[3].data = {
                subscriberNum: batchSum(GetAllSubscribers, '', (data) => { return data?.length || 0; }),
                subscriberInfoNum: batchSum(GetAllSubscribeInfo, '', (data) => { return data?.length || 0; }),
                subscriberPushNum: batchSum(GetTopicSubPubInfo, 'PushMsgCount', sumFilterRemove)
            };
        },

        // 集群线程队列消息变化趋势 信息处理展示
        handleBrokerChartInfo(newTime) {
            let overviewData = {};
            if (this.brokerSelect === 'all') {
                overviewData = this.overviewData;
            } else {
                const NodeName = this.brokerSelect;
                overviewData = this.brokerData[NodeName];
            }
            if (!Object.keys(overviewData || {}).length) return;
            const { GetSessionThreadInfo, GetSubPushThreadInfo, GetPublishQue, GetTopicSvrQue } = overviewData;
            let brokerList = [{
                value: 'all',
                label: '集群'
            }];
            Object.values(this.brokerInfo || {}).forEach((item) => {
                brokerList.push({
                    value: item?.NodeName ? item?.NodeName : `mc#${item.BrokerId}`,
                    label: item?.NodeName ? item?.NodeName : `#${item.BrokerId}`
                });
            });
            if (this.brokerType === 'single') {
                brokerList = brokerList.slice(0, 2);
            }
            this.chartObj1.title.slots[0].options = brokerList;

            // 生成折线图数据
            const yData = {
                会话线程: batchSum(GetSessionThreadInfo, 'NowInQue', sum),
                订阅线程: batchSum(GetSubPushThreadInfo, 'sub', filterSum),
                推送线程: batchSum(GetSubPushThreadInfo, 'push', filterSum),
                发布线程: batchSum(GetPublishQue, 'NowInQue', sum),
                主题线程: batchSum(GetTopicSvrQue, 'NowInQue', sum)
            };
            const chartData = this.chartObj1.chartData;
            const index = chartData.xData.lastIndexOf(newTime);
            Object.keys(chartData.data).forEach(item => {
                this.$set(chartData.data[item], index, yData?.[item] || 0);
            });
        },
        // 集群线程队列消息变化趋势 下拉框切换
        chartSelectChange(val) {
            this.msgFuncObj = {};
            this.topicNumSumObj = {};
            this.brokerSelect = val;
            // 清空当前数据
            this.resetBrokerChart();
            this.handleBrokerChartInfo();
        },
        // 获取集群消息变化量
        getBrokersMsgIncrement(GetTopicSubPubInfo) {
            // 无上次轮询数据，设置变化量为0
            if (!Object.keys(this.msgFuncObj)?.length) {
                return [0, 0];
            }
            // 生产者
            // 计算两次发布消息数变化量
            const currentPubMsgNo = getTopicPubMsgCount(mutiHostMergeArr(_.cloneDeep(GetTopicSubPubInfo)));
            const lastPubMsgNo = getTopicPubMsgCount(mutiHostMergeArr(_.cloneDeep(this.msgFuncObj.GetTopicSubPubInfo)));;
            let pubMsgIncrement = currentPubMsgNo ? currentPubMsgNo - lastPubMsgNo : 0;

            const currentSubInfo = _.cloneDeep(GetTopicSubPubInfo);
            const lastSubInfo = _.cloneDeep(this.msgFuncObj.GetTopicSubPubInfo);
            // 计算两次订阅消息数变化量
            const currentSubFunc = batchSum(currentSubInfo, 'PushMsgCount', sumFilterRemove);
            const lastSubFunc = batchSum(lastSubInfo, 'PushMsgCount', sumFilterRemove);
            let subMsgIncrement = currentSubFunc ? currentSubFunc - lastSubFunc : 0;

            pubMsgIncrement = this.makePositive(
                pubMsgIncrement + (this.historyDeviation['pubMsgIncrement'] || 0),
                'pubMsgIncrement'
            );
            subMsgIncrement = this.makePositive(
                subMsgIncrement + (this.historyDeviation['subMsgIncrement'] || 0),
                'subMsgIncrement'
            );
            return [pubMsgIncrement, subMsgIncrement];
        },
        // 轮询应答顺序错乱导致数据为负，置为0
        makePositive(value, key) {
            // 定义一个量保存偏差负值，在下一次轮询中需减去偏差
            this.historyDeviation[key] = 0;
            if (value < 0) {
                this.historyDeviation[key] = value;
                return 0;
            }
            return value;
        },

        // 主题生产消费消息变化趋势 信息处理
        handelTopicChartInfo(newTime) {
            if (!Object.keys(this.overviewData || {}).length) return;
            const { GetTopicSubPubInfo } = this.overviewData;
            let yData = {};
            if (this.groupType === 'allTopic') {
                // 在此处判断并计算的各节点发布、订阅消息数
                const [pubMsgIncrement, subMsgIncrement] = this.getBrokersMsgIncrement(GetTopicSubPubInfo);
                // 保存上次轮询发布、订阅消息管理功能数据
                this.msgFuncObj = {
                    GetTopicSubPubInfo
                };

                yData = {
                    发布消息变化差量: pubMsgIncrement,
                    订阅消息变化差量: subMsgIncrement
                };

                // 折线图轮询更新数据
                const chartData = this.chartObj2.chartData;
                const index = chartData.xData.lastIndexOf(newTime);
                Object.keys(chartData.data).forEach(item => {
                    this.$set(chartData.data[item], index, yData?.[item]);
                });
            } else {
                // 无选中值不设置数据
                if (!this.selectGroup?.[1]?.selectValue?.length && !this.selectGroup?.[2]?.selectValue?.length) return;

                const selectTopicNo = this.selectGroup?.[0]?.selectValue; // 所选主题编号
                const selectTopicName = _.find(this.selectGroup[0].options, ['value', selectTopicNo])?.label; // 所选主题名
                // 根据主题名获取相应主题下 发布消费者数据
                const { pubList, subList } = this.filterTopicSubPub(selectTopicNo, selectTopicName);
                // 保存对应主题下 发布消费者数据
                Array.isArray(pubList) && pubList.forEach(pubItem => {
                    yData[pubItem?.label] = pubItem?.number;
                });
                Array.isArray(subList) && subList.forEach(subItem => {
                    yData[subItem?.label] = subItem?.number;
                });

                // 折线图轮询更新数据
                const chartData = this.chartObj2.chartData;
                const index = chartData.xData.lastIndexOf(newTime);
                Object.keys(chartData.data).forEach(item => {
                    const oldPointData = this.topicNumSumObj[item];
                    // 若旧值不存在，不存在时无法计算差值,设置差值为0；
                    // 若新值不存在或为0，表示节点可能不在线，设置差值为0
                    this.$set(chartData.data[item], index, ((oldPointData || oldPointData === 0) && yData?.[item]) ? yData[item] - oldPointData : 0);
                    this.topicNumSumObj[item] = yData?.[item];
                });
            }
        },
        // 过滤筛选所选主题相关发布、消费者信息
        filterTopicSubPub(topicNo, topicName) {
            const { GetPubMsgNo, GetAllSubscribeInfo } = this.overviewData;
            const pubList = [];
            const subList = [];
            const pubNameArr = [];
            const subNameArr = [];

            const pubMsgNo = removeDuplicates(mutiHostMergeArr(GetPubMsgNo));
            Array.isArray(pubMsgNo) && pubMsgNo.forEach(pub => {
                if (Math.floor(pub?.TopicPartition / 10000) === topicNo) {
                    // 生产者列表去重 消息数累加
                    if (pubNameArr.includes(pub.PubName)) {
                        pubList.forEach(item => {
                            if (item.value === pub.PubName) {
                                item.number += pub.PublishCount;
                            }
                        });
                    } else {
                        pubList.push({
                            value: pub.PubName,
                            label: JSON.stringify(pub.PubName).split('"')[1],
                            number: pub.PublishCount
                        });
                    }
                    pubNameArr.push(pub.PubName);
                }
            });
            Array.isArray(GetAllSubscribeInfo) && GetAllSubscribeInfo.forEach(item => {
                Array.isArray(item?.Result) && item.Result.forEach(sub => {
                    if (sub?.TopicName === topicName) {
                        // 消费者列表去重 消息数累加
                        if (subNameArr.includes(`${sub.SubName}(${sub.SessionId})`)) {
                            subList.forEach(item => {
                                if (item.label === `${sub.SubName}(${sub.SessionId})`) {
                                    item.number += sub.PushCount;
                                }
                            });
                        } else {
                            subList.push({
                                value: sub.SubName + sub.SessionId,
                                label: `${sub.SubName}(${sub.SessionId})`,
                                number: sub.PushCount
                            });
                        }
                        subNameArr.push(`${sub.SubName}(${sub.SessionId})`);
                    }
                });
            });
            return { pubList, subList };
        },
        // 手动刷新主题列表 - 防抖处理
        refreshTopicList: _.debounce(function() {
            const { GetTopicInfo } = this.overviewData;
            const topicList = [];
            const topicInfo = handleMutiIpFunc(GetTopicInfo);
            Array.isArray(topicInfo) && topicInfo.forEach(item => {
                topicList.push({
                    value: item?.TopicNo,
                    label: item?.Topic
                });
            });
            // 主题名排序
            this.selectGroup[0].options = topicList.sort((a, b) => {
                return a.label.localeCompare(b.label, 'en', { sensitivity: 'base' });
            });
            // 判断选中主题是否还存在，不存在默认选列表第一个
            const selectTopicNo = this.selectGroup[0].selectValue; // 所选主题编号
            this.selectGroup[0].selectValue = _.find(
                topicList, ['value', selectTopicNo]
            )?.value ?? topicList?.[0]?.value;

            this.refershSubPubList();
        }, 300), // 300ms 的防抖处理

        // 更新订阅、生产者列表
        refershSubPubList() {
            const lineDeploy = [];

            const selectTopicNo = this.selectGroup?.[0]?.selectValue; // 所选主题编号
            const selectTopicName = _.find(this.selectGroup[0].options, ['value', selectTopicNo])?.label; // 所选主题名
            // 根据主题名获取相应主题相关发布消费者列表
            const { pubList, subList } = this.filterTopicSubPub(selectTopicNo, selectTopicName);
            this.selectGroup[1].options = pubList;
            this.selectGroup[2].options = subList;
            this.selectGroup[1].selectValue = pubList?.[0]?.value ? [pubList[0].value] : [];
            this.selectGroup[2].selectValue = subList?.[0]?.value ? [subList[0].value] : [];

            // 初始化折线图数据
            const chartData = {
                xData: [],
                data: {}
            };
            Array.isArray(pubList) && pubList.forEach(pubItem => {
                chartData.data[pubItem?.label] = [];
                if (this.selectGroup[1].selectValue.includes(pubItem?.value)) {
                    lineDeploy.push({
                        name: pubItem?.label,
                        type: 'line'
                    });
                }
            });
            Array.isArray(subList) && subList.forEach(subItem => {
                chartData.data[subItem?.label] = [];
                if (this.selectGroup?.[2]?.selectValue.includes(subItem?.value)) {
                    lineDeploy.push({
                        name: subItem?.label,
                        type: 'line'
                    });
                }
            });

            this.chartObj2.basicOpiton.lineDeploy = lineDeploy;
            this.chartObj2.chartData = chartData;

            this.isChartActive = false;
            setTimeout(() => {
                this.isChartActive = true;
            }, 0);
        },
        // 主题生产消费消息变化趋势 下拉框切换
        selectGroupChange(val, key) {
            this.msgFuncObj = {};
            this.topicNumSumObj = {};
            if (key === 'topic') {
                this.refershSubPubList();
            } else if (['publish', 'subscribe'].includes(key)) {
                this.resetSingleTopicMsg();
            }

            if (val === 'allTopic') {
                this.resetAllTopicMsg();
            } else if (val === 'singleTopic') {
                // 点击选择单主题统计，更新主题列表
                this.refreshTopicList();
            }
        },
        // 重置全主题统计折线图数据
        resetAllTopicMsg() {
            this.chartObj2.basicOpiton.lineDeploy = [
                {
                    name: '发布消息变化差量',
                    type: 'line'
                },
                {
                    name: '订阅消息变化差量',
                    type: 'line'
                }];
            this.chartObj2.chartData = {
                xData: [],
                data: {
                    发布消息变化差量: [],
                    订阅消息变化差量: []
                }
            };
            this.isChartActive = false;
            setTimeout(() => {
                this.isChartActive = true;
            }, 0);
        },
        // 重置单主题统计折线图数据
        resetSingleTopicMsg() {
            // 重新生成图例配置
            const lineDeploy = [];
            Array.isArray(this.selectGroup?.[1]?.selectValue) && this.selectGroup[1].selectValue.forEach(item => {
                const pubItem = _.find(this.selectGroup[1].options, ['value', item]);
                lineDeploy.push({
                    name: pubItem?.label,
                    type: 'line'
                });
            });
            Array.isArray(this.selectGroup?.[2]?.selectValue) && this.selectGroup[2].selectValue.forEach(item => {
                const subItem = _.find(this.selectGroup[2].options, ['value', item]);
                lineDeploy.push({
                    name: subItem?.label,
                    type: 'line'
                });
            });
            this.chartObj2.basicOpiton.lineDeploy = lineDeploy;

            // echart折线图销毁再创建-解决线条变化echart（option有缓存）不更新的问题
            this.isChartActive = false;
            setTimeout(() => {
                this.isChartActive = true;
            }, 0);
        },
        // 清理集群线程折线图数据
        resetBrokerChart() {
            this.chartObj1.chartData = {
                xData: [],
                data: {
                    会话线程: [],
                    订阅线程: [],
                    推送线程: [],
                    发布线程: [],
                    主题线程: []
                }
            };
        }
    }
};
</script>

<style lang="less" scoped>
@import url("@/assets/css/simpleSelect.less");

.tab-box {
    .overview-group {
        display: flex;
    }

    /deep/ .apm-chart {
        margin-top: 15px;

        .title-box {
            & > .h-select {
                min-width: 120px;
            }
        }
    }

    .radio-group {
        height: 42px;
        line-height: 42px;
        margin-left: 20px;
        color: var(--font-color);

        /deep/ .h-radio-wrapper:first-child {
            border-left: none;
        }

        /deep/ .h-radio-group-item {
            background: none;
        }
    }

    .select-group {
        background: var(--primary-color);
        height: 42px;
        line-height: 42px;
        padding: 0 15px;
        min-width: 800px;

        /deep/ .h-checkbox-inner {
            border: 1px solid #d7dde4;
            border-radius: 2px;
            background-color: #fff;
        }

        /* stylelint-disable-next-line selector-class-pattern */
        /deep/ .h-selectTable {
            width: auto;
            min-width: 150px;
            display: inline-block;
            margin-right: 5px;
        }

        /deep/ .h-select {
            min-width: 150px;
            max-width: 250px;
            display: inline-block;
            margin-right: 5px;
        }
    }
}
</style>
