<template>
    <div class="main">
        <!-- 头部 -->
        <div class="title">
            <a-title title="数据二次上场">
                <slot>
                    <h-select
                        v-show="productList.length > 1"
                        v-model="productInstNo"
                        placeholder="请选择"
                        class="title-select"
                        placement="bottom"
                        :positionFixed="true"
                        :clearable="false"
                        @on-change="checkProduct">
                        <h-option
                            v-for="item in productList"
                            :key="item.id"
                            :value="item.productInstNo">
                            {{ item.productName }}
                        </h-option>
                    </h-select>
                    <publish-status-detail :hasTask="hasTask" :taskDetail="taskDetail"/>
                </slot>
            </a-title>
        </div>
        <!-- 加载进度条 -->
        <a-loading v-if="loading"></a-loading>
        <!-- 主体内容 -->
        <h-tabs v-model="tabName" class="product-box" @on-click="tabClick(tabName)">
            <h-tab-pane label="按SQL条件" name="sqlCondition">
                <sql-condition-list
                    :key="productInstNo"
                    ref="sqlCondition"
                    :productId="productInstNo"
                    :clusterList="clusterList">
                </sql-condition-list>
            </h-tab-pane>
            <h-tab-pane label="按资金账户" name="businessAccount">
                <business-account-list
                    :key="productInstNo"
                    ref="businessAccount"
                    :productId="productInstNo"
                    :clusterList="clusterList">
                </business-account-list>
            </h-tab-pane>
            <h-tab-pane label="上场记录" name="playRecord">
                <play-record-list
                    :key="productInstNo"
                    ref="playRecord"
                    :productId="productInstNo"
                    :instanceTypeOptions="instanceTypeOptions">
                </play-record-list>
            </h-tab-pane>
        </h-tabs>
    </div>
</template>

<script>
import _ from 'lodash';
import { mapState, mapActions } from 'vuex';
import aTitle from '@/components/common/title/aTitle';
import aLoading from '@/components/common/loading/aLoading';
import sqlConditionList from '@/components/secondAppearance/sqlConditionList/index.js';
import businessAccountList from '@/components/secondAppearance/businessAccountList/index.js';
import playRecordList from '@/components/secondAppearance/playRecordList/index.js';
import publishStatusDetail from './publishStatusDetail.vue';
import { getInstanceTypeDict, getLoadDataStatus } from '@/api/brokerApi';
import { getProductClusters } from '@/api/productApi';

function formatDateTime(input) {
    // 如果输入是字符串，将其解析为日期对象
    const date = typeof input === 'string' ? new Date(input) : input;

    if (isNaN(date.getTime())) {
        // 如果解析失败，返回一个错误提示
        return 'Invalid Date';
    }

    // 获取年、月、日、时、分、秒
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0'); // 月份从0开始，需要加1
    const day = String(date.getDate()).padStart(2, '0');
    const hours = String(date.getHours()).padStart(2, '0');
    const minutes = String(date.getMinutes()).padStart(2, '0');
    const seconds = String(date.getSeconds()).padStart(2, '0');

    // 格式化成 "YYYY-MM-DD HH:mm:ss" 的形式
    const formattedDateTime = `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;

    return formattedDateTime;
}

export default {
    name: 'SecondAppearance',
    components: {
        aTitle,
        aLoading,
        sqlConditionList,
        businessAccountList,
        playRecordList,
        publishStatusDetail
    },
    data() {
        return {
            instanceTypeOptions: [],
            clusterList: [],
            hasTask: false,
            taskDetail: {},
            productInstNo: '', // 选中的产品
            tabName: 'sqlCondition', // tab默认选择
            loading: false,
            loadDataStart: false // 标示当时是否已经开启了上场任务
        };
    },
    async mounted() {
        await this.init();
        await this.queryImportDataStatus();
        // 每隔 5s 调用后端接口查询上场状态,并更新 UI 界面
        this.timer = setInterval(async () => {
            await this.queryImportDataStatus();
        }, 5000);
        // 监听上场行为
        this.$hCore.on('LoadDataStart', () => {
            this.queryImportDataStatus();
            this.loadDataStart = true;
        });
    },
    computed: {
        ...mapState({
            productList: state => {
                return state.product.productListLight || [];
            }
        })
    },
    methods: {
        ...mapActions({ getProductList: 'product/getProductListLight' }),
        // 初始化页面
        async init() {
            try {
                this.loading = true;
                await this.getProductList();
                const productInstNo = localStorage.getItem('productInstNo');
                this.productInstNo = _.find(this.productList, ['productInstNo', productInstNo])?.productInstNo || this.productList?.[0]?.productInstNo;
            } catch (e) {
                console.error(e);
            } finally {
                this.loading = false;
            }
        },
        // 切换产品
        async checkProduct(item) {
            this.loading = true;
            localStorage.setItem('productInstNo', item);
            await this.queryInstanceTypeByProductNo(item);
            await this.getProductClusters(item);
            setTimeout(() => {
                this.loading = false;
                this.tabClick(this.tabName);
            }, 500);
        },
        // 切换tab
        tabClick(name) {
            this.$refs[name] && this.$refs[name].initData();
        },
        // 调用接口，查询数据上场状态
        async queryImportDataStatus(){
            const params = {
                productId: this.productInstNo,
                isLastExec: true
            };
            try {
                const res = await getLoadDataStatus(params);
                if (res.success){
                    // 如果没有历史上场记录
                    if (res.data === null){
                        this.hasTask = false;
                        this.taskDetail = {};
                        return;
                    }
                    const data = res?.data || {};
                    this.hasTask = data.execBatchStatus === 'running';
                    this.taskDetail =  {
                        title: data.whereCondition === 'all' ? '按SQL条件' : '按资金账户',
                        startTime: data.startTime ? formatDateTime(data.startTime) : '',
                        endTime: data.endTime ? formatDateTime(data.endTime) : '',
                        totalCount: data.totalCount,
                        successCount: data.successCount,
                        failCount: data.failCount
                    };
                    // 什么时候通知下面表格更新数据：触发上场行为且上场任务已经完成(状态变为失败或者成功)
                    if (!this.hasTask && this.loadDataStart){
                        this.loadDataStart = false;
                        this.$hCore.trigger('loadDataFinish', data.whereCondition);
                    }
                } else {
                    // 请求失败，取消定时轮回查询
                    this.timer && clearInterval(this.timer);
                }
            } catch (e) {
                // 请求失败，取消定时轮回查询
                this.timer && clearInterval(this.timer);
            }
        },
        // 调用接口，获取产品对应的核心类型
        async queryInstanceTypeByProductNo(){
            const params = {
                productId: this.productInstNo
            };
            try {
                const res = await getInstanceTypeDict(params);
                if (res.success){
                    const result = res?.data || {};
                    this.instanceTypeOptions = Object.keys(result).map((key) => {
                        return {
                            label: result[key],
                            value: key
                        };
                    });
                }
            } catch (e) {
                this.$hMessage.error(e);
            }
        },
        // 调用接口，获取产品集群列表
        async getProductClusters() {
            const params = {
                productId: this.productInstNo
            };
            try {
                const res = await getProductClusters(params);
                if (res.success) {
                    const list = res?.data?.appClusters || [];
                    this.clusterList = _.filter(list, item => _.includes(item.clusterInstanceIdentities, 'bizproc')).map(o => {
                        return {
                            label: o.clusterName,
                            value: o.clusterName
                        };
                    });
                }
            } catch (e) {
                this.$hMessage.error(e);
            }
        }
    },
    beforeDestroy() {
        this.$hCore.off('LoadDataStart');
        this.timer && clearInterval(this.timer);
    }
};
</script>

<style lang="less" scoped>
@import url("@/assets/css/tab.less");
@import url("@/assets/css/input.less");
@import url("@/assets/css/menu.less");

.main {
    .title {
        p {
            display: inline-block;
            color: var(--font-color);
            font-size: var(--title-font-size);
            position: absolute;
            left: 50%;
            transform: translateX(-50%);
        }

        .title-select {
            float: right;
            margin-right: 4px;
            top: 5px;
            min-width: 200px;
            width: auto;
        }
    }

    .product-box {
        /deep/ .h-tabs-nav-wrap {
            float: none !important;
        }

        /deep/ .h-tabs-nav-right {
            position: absolute;
            right: 0;
            top: 5px;
        }
    }
}
</style>
