import * as echarts from 'echarts';

export default {
    name: 'lineChart',
    props: {
        title: {
            type: String,
            default: ''
        },
        valueName: {
            type: String,
            default: ''
        },
        xData: {
            type: Array,
            default: () => []
        },
        yData: {
            type: Array,
            default: () => []
        }
    },
    data() {
        return {
            myChart: null,
            option: {
                title: {
                    text: this.title,
                    textStyle: {
                        color: '#fff',
                        fontSize: 12
                    },
                    top: 0
                },
                grid: {
                    left: 50,
                    right: 1,
                    bottom: 30,
                    top: 40
                },
                tooltip: {
                    trigger: 'axis',
                    backgroundColor: 'rgba(88,94,106,0.40)',
                    borderColor: 'rgba(88,94,106,0.40)',
                    textStyle: {
                        color: '#fff'
                    },
                    formatter: function(params) {
                        let relVal = params[0].name;
                        for (let i = 0, l = params.length; i < l; i++) {
                            relVal += '<br/>' + params[i].marker + params[i].seriesName + '：' + params[i].value + ' μs';
                        }
                        return relVal;
                    },
                    // eslint-disable-next-line max-params
                    position: function (point, params, dom, rect, size) {
                        // 鼠标坐标和提示框位置的参考坐标系是：以外层div的左上角那一点为原点，x轴向右，y轴向下
                        // 提示框位置
                        let x = 0; // x坐标位置
                        let y = 0; // y坐标位置

                        // 当前鼠标位置
                        const pointX = point[0];
                        const pointY = point[1];

                        // 外层div大小
                        // var viewWidth = size.viewSize[0];
                        // var viewHeight = size.viewSize[1];

                        // 提示框大小
                        const boxWidth = size.contentSize[0];
                        const boxHeight = size.contentSize[1];

                        // boxWidth > pointX 说明鼠标左边放不下提示框
                        if (boxWidth > pointX) {
                            x = 5;
                        } else {
                            // 左边放的下
                            x = pointX - boxWidth;
                        }

                        // boxHeight > pointY 说明鼠标上边放不下提示框
                        if (boxHeight > pointY) {
                            y = 5;
                        } else {
                            // 上边放得下
                            y = pointY - boxHeight;
                        }

                        return [x, y];
                    }
                },
                legend: {
                    textStyle: {
                        color: '#fff',
                        fontSize: 11,
                        padding: [0, 0, 0, 10]
                    },
                    itemHeight: 1,
                    itemWidth: 16
                },
                xAxis: {
                    type: 'category',
                    boundaryGap: false,
                    axisLine: {
                        show: true,
                        lineStyle: {
                            color: '#31364a'
                        }
                    },
                    axisTick: {
                        show: false
                    },
                    axisLabel: {
                        color: '#fff'
                    },
                    data: []
                },
                yAxis: [
                    {
                        type: 'value',
                        axisLine: {
                            show: true,
                            lineStyle: {
                                color: '#31364a'
                            }
                        },
                        axisLabel: {
                            color: '#fff'
                        },
                        splitLine: {
                            lineStyle: {
                                color: '#31364a'
                            }
                        }
                    },
                    {
                        axisLine: {
                            show: true,
                            lineStyle: {
                                color: '#31364a'
                            }
                        }
                    }
                ],
                toolbox: {
                    right: 10
                },
                series: [
                    {
                        name: '',
                        type: 'line',
                        showSymbol: false,
                        lineStyle: {
                            color: '#2D8DE5'
                        },
                        itemStyle: {
                            color: '#2D8DE5'
                        },
                        data: []
                    }
                ]
            }
        };
    },
    mounted() {
        setTimeout(() => {
            this.myChart = echarts.init(this.$refs.chart, '#262B40');
            this.init();
        }, 500);
    },
    beforeDestroy() {
        window.removeEventListener('resize', this.resize);
    },
    methods: {
        init() {
            // if (!this.xData.length) {
            //     this.option.xAxis.data = ['09:15:00', "11:30:00", "15:00:00"]
            // }
            // if (!this.yData.length) {
            //     this.option.series[0].data = [0, 0, 0]
            // }
            this.option.series[0].name = this.valueName;
            this.myChart && this.myChart.setOption(this.option, true);
            window.addEventListener('resize', this.resize);
        },
        resize() {
            this.myChart && this.myChart.resize();
        }
    },
    watch: {
        xData(newVal) {
            if (newVal) {
                this.option.xAxis.data = this.xData;
                this.option.series[0].data = this.yData;
                this.init();
            } else {
                this.myChart.dispose();
            }
        }
    },
    render() {
        return <div ref="chart" class="chart" style="width: 100%; height: 100%;"></div>;
    }
};
