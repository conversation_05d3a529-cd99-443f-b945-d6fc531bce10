<!--
 * @Description:
 * @Author: <PERSON><PERSON>
 * @Date: 2023-02-02 18:50:46
 * @LastEditTime: 2023-11-21 18:23:15
 * @LastEditors: yingzx38608 <EMAIL>
-->
<template>
    <div>
        <!-- 下发配置列表 -->
        <h-msg-box-safe v-model="modalData.status" :escClose="true" :mask-closable="false" title="按条件清理" width="50" maxHeight="240" @on-open="getCollections">
            <h-form ref="formValidate" :model="formItem" :rules="ruleValidate" :label-width="85">
                <h-form-item label="产品节点：" prop="productInstNo" required>
                    <h-select v-model="formItem.productInstNo" placeholder="请选择" :positionFixed="true">
                        <h-option v-for="item in productList" :key="item.id" :value="item.productInstNo" >{{item.productName}}</h-option>
                    </h-select>
                </h-form-item>
                <h-form-item label="数据日期：" prop="time" required>
                    <h-date-picker v-model="formItem.time" type="daterange" :options="options" placeholder="选择日期" :positionFixed="true" ></h-date-picker>
                </h-form-item>
                <h-form-item v-if="modalData.indexType === 'latency' || modalData.indexType === 'entrustLatency'" label="资金账号：" prop="accountId">
                    <h-input v-model="formItem.accountId" placeholder="请输入"></h-input>
                </h-form-item>
            </h-form>
            <template v-slot:footer>
                <a-button @click="modalData.status = false">取消</a-button>
                <a-button type="error" :loading="loading" @click="submitConfig">清理</a-button>
            </template>
        </h-msg-box-safe>
    </div>
</template>

<script>
import { mapState, mapActions } from 'vuex';
import { formatDate } from '@/utils/utils';
import { clearManagementData } from '@/api/httpApi';
import aButton from '@/components/common/button/aButton';
export default ({
    props: {
        modalInfo: {
            type: Object,
            default: null
        }
    },
    data() {
        const validateDayRange = (rule, value, callback) => {
            if (Array.isArray(value) && value.length === 2) {
                const subtime = (value[1] - value[0]) / 1000;
                const days = parseInt(subtime / 86400, 10);
                if (days > 30) {
                    return callback(new Error('请限制清理日期小于30天'));
                }
                callback();
            }
        };
        return {
            loading: false,
            modalData: this.modalInfo,
            options: {
                disabledDate(date) {
                    return date && date.valueOf() >= Date.now() - ********;
                }
            },
            formItem: {
                productInstNo: '',
                indexType: '',
                indexName: '',
                startTime: '',
                endTime: '',
                time: [],
                accountId: ''
            },
            ruleValidate: { // 校验规则
                time: [{ validator: validateDayRange, trigger: 'change' }]
            }
        };
    },
    computed: {
        ...mapState({
            productList: state => {
                return state.product.productListLight || [];
            }
        })
    },
    methods: {
        ...mapActions({ getProductList: 'product/getProductListLight' }),
        getCollections() {
            this.$refs['formValidate'].resetFields();
            this.formItem.indexType = this.modalInfo.indexType;
            this.formItem.indexName = this.modalInfo.indexName;
            this.getProductList();
        },
        submitConfig() {
            this.$refs['formValidate'].validate(async (valid) => {
                if (valid) {
                    const param = { ...this.formItem };
                    param.startTime = formatDate(this.formItem.time[0]) + ' 00:00:00';
                    param.endTime = formatDate(this.formItem.time[1]) + ' 00:00:00';
                    const res = await clearManagementData(param);
                    if (res.success) {
                        this.$hMessage.success('清理指令发送成功！');
                        this.modalData.status = false;
                    } else {
                        this.$hMessage.error({
                            content: res.message,
                            duration: 5
                        });
                    }
                }
            });
        }
    },
    components: {  aButton }
});
</script>
