<template>
    <div>
        <!-- 创建/编辑名单 -->
        <h-msg-box-safe
            v-model="modalData.status"
            :mask-closable="false"
            :title="modalData.title"
            width="630"
            height="410"
            :canDrag="false"
            @on-open="getCollections">
            <h-form
                ref="formValidate"
                :model="formValidate"
                :rules="ruleValidate"
                :label-width="120">
                <h-form-item label="组名称" prop="groupName">
                    <h-input v-model.trim="formValidate.groupName" placeholder="最大输入长度20" :maxlength="20"></h-input>
                </h-form-item>
                <h-form-item label="组描述" prop="groupDescribe">
                    <h-input v-model.trim="formValidate.groupDescribe" placeholder="最大输入长度1000" type="textarea" :rows="3" :maxlength="1000" style="line-height: 22px;"></h-input>
                </h-form-item>
                <h-form-item label="组对象" prop="interest">
                    <div style="display: flex;">
                        <h-checkbox-group v-model="formValidate.interest">
                            <h-checkbox label="functionNos">功能号</h-checkbox>
                            <h-checkbox label="accountIds">资金账号</h-checkbox>
                        </h-checkbox-group>
                        <h-poptip placement="right" content="至少选择一个对象进行绑定，可多选" :transfer="true" positionFixed>
                            <h-icon name="feedback_fill" size="24" color="#999" style="cursor: pointer; margin-left: 6px;"></h-icon>
                        </h-poptip>
                    </div>
                </h-form-item>
                <h-form-item v-if="formValidate.interest.indexOf('functionNos') > -1" label="功能号" prop="ruleMap.functionNos">
                    <div style="display: flex;">
                        <h-input v-model.trim="formValidate.ruleMap.functionNos" placeholder="最大输入长度1000" :maxlength="1000"></h-input>
                        <h-poptip placement="left" :transfer="true" positionFixed width="360">
                            <h-icon name="feedback_fill" size="24" color="#999" style="cursor: pointer; margin-left: 6px;"></h-icon>
                            <pre slot="content">{{`1.功能号可以包含数字和英文字符“?”号\n2.英文字符“?”号表示0~9任意一个数字，只能出现在最后\n（比如“22??”表示2200~2299这一段功能号）\n3.多个功能号使用英文字符“;”号分隔`}}</pre>
                        </h-poptip>
                    </div>
                </h-form-item>
                <h-form-item v-if="formValidate.interest.indexOf('accountIds') > -1" label="资金账号" prop="ruleMap.accountIds">
                    <div style="display: flex;">
                        <h-input v-model.trim="formValidate.ruleMap.accountIds" placeholder="最大输入长度1000" :maxlength="1000"></h-input>
                        <h-poptip placement="left" :transfer="true" positionFixed>
                            <h-icon name="feedback_fill" size="24" color="#999" style="cursor: pointer; margin-left: 6px;"></h-icon>
                            <pre slot="content">{{`1.需要输入具体账号\n2.只能是数字\n3.多个账号使用英文字符“;”号分隔`}}</pre>
                        </h-poptip>
                    </div>
                </h-form-item>
            </h-form>
            <template v-slot:footer>
                <a-button type="primary" :loading="loading" @click="handleCreateGroup">提交</a-button>
                <a-button @click="modalData.status = false">取消</a-button>
            </template>
        </h-msg-box-safe>
    </div>
</template>

<script>
import { createOrUpdateRuleGroup } from '@/api/brokerApi';
import aButton from '@/components/common/button/aButton';
export default {
    props: {
        productId: {
            type: String,
            default: ''
        },
        modalInfo: {
            type: Object,
            default: null
        }
    },
    data() {
        return {
            loading: false,
            modalData: this.modalInfo,
            formValidate: {
                id: '',
                groupName: '',
                interest: ['functionNos', 'accountIds'],
                groupDescribe: '',
                ruleMap: {
                    functionNos: '',
                    accountIds: ''
                }
            },
            ruleValidate: {
                groupName: [{ required: true, message: '组名不能为空', trigger: 'blur' }],
                interest: [
                    {
                        required: true,
                        type: 'array',
                        min: 1,
                        message: '至少选择一个组对象',
                        trigger: 'change'
                    }
                ],
                'ruleMap.functionNos': [{ required: true, message: '功能号不能为空', trigger: 'blur' }],
                'ruleMap.accountIds': [{ required: true, message: '资金账号不能为空', trigger: 'blur' }]
            }
        };
    },
    methods: {
        async getCollections() {
            if (this.modalInfo.type === 'edit') {
                const data = this.modalInfo.data || {};
                this.formValidate.id = data?.id;
                this.formValidate.interest = [];
                this.formValidate.groupName = data?.groupName;
                this.formValidate.groupDescribe = data?.groupDescribe;
                this.formValidate.ruleMap = { ...data?.ruleMap };
                data?.ruleMap?.functionNos && this.formValidate.interest.push('functionNos');
                data?.ruleMap?.accountIds && this.formValidate.interest.push('accountIds');
            }
        },
        handleCreateGroup() {
            this.$refs['formValidate'].validate(async (valid) => {
                if (valid) {
                    const param = {
                        productId: this.productId,
                        id: this.formValidate.id,
                        groupName: this.formValidate.groupName,
                        ruleMap: {},
                        groupDescribe: this.formValidate.groupDescribe
                    };
                    this.formValidate.interest.forEach(ele => {
                        param.ruleMap[ele] = this.formValidate.ruleMap[ele];
                    });
                    try {
                        this.loading = true;
                        const res = await createOrUpdateRuleGroup(param);
                        if (res.success) {
                            this.$hMessage.success('操作成功!');
                            this.$emit('query');
                            this.modalData.status = false;
                        } else if (res.code.length === 8) {
                            this.$hMessage.error(res.message);
                        }
                    } catch (error) {
                        console.error(error);
                    }
                    this.loading = false;
                }
            });
        }
    },
    components: {  aButton }
};
</script>

<style lang="less" scoped>
    /deep/ .h-modal-body {
        padding: 16px;
    }

    .alarm-title {
        display: flex;
        justify-content: space-between;
        border-bottom: 1px solid #ccc;
        padding-bottom: 10px;
    }

    .group-title {
        font-weight: bold;
        font-size: 14px;
    }

    .inline-html {
        display: inline-block;
        padding-bottom: 10px;
    }
</style>
