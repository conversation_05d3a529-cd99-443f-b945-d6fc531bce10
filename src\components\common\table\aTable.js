/*
 * @Description: apm-table
 * @Author: <PERSON><PERSON>
 * @Date: 2022-11-28 13:52:38
 * @LastEditTime: 2023-12-12 19:14:08
 * @LastEditors: yingzx38608 <EMAIL>
 */
import './table.less';
export default {
    name: 'apm-table',
    props: {
        hasDarkClass: {
            type: Boolean,
            default: true
        },
        multiLevel: {
            type: Array,
            default: () => []
        },
        columns: {
            type: Array,
            default: []
        },
        tableData: {
            type: Array,
            default: []
        },
        border: {
            type: Boolean,
            default: false
        },
        showTitle: {
            type: Boolean,
            default: false
        },
        hasPage: {
            type: Boolean,
            default: true
        },
        showSizer: {
            type: Boolean,
            default: true
        },
        showElevator: {
            type: Boolean,
            default: true
        },
        simple: {
            type: Boolean,
            default: false
        },
        total: {
            type: Number,
            default: 0
        },
        height: {
            type: Number | String | undefined,
            default: undefined
        },
        maxHeight: {
            type: Number | String | undefined,
            default: undefined
        },
        loading: {
            type: <PERSON><PERSON><PERSON>,
            default: false
        },
        disabledHover: {
            type: Boolean,
            default: false
        },
        highlightRow: {
            type: Boolean,
            default: false
        },
        immediateRowClick: {
            type: Boolean,
            default: false
        },
        rowSelectOnly: {
            type: Boolean,
            default: false
        },
        notSetWidth: {
            type: Boolean,
            default: false
        },
        autoHeadWidth: {
            type: Boolean,
            default: false
        },
        showTotal: {
            type: Boolean,
            default: false
        },
        canDrag: {
            type: Boolean,
            default: false
        },
        hPageSize: { // page尺寸
            type: String || undefined,
            default: undefined
        },
        isBlur: { // 只在 showElevator 为 true 时使用，设置后电梯触发方式在输入框失去焦点时触发，默认触发方式为键盘 enter 按键触发
            type: Boolean,
            default: false
        },
        noDataText: {
            type: String,
            default: '暂无数据'
        }
    },
    data() {
        return {
            page: 1,
            pageSize: 10,
            sizerOption: {
                transfer: true,
                autoPlacement: true
            },
            sortField: '',
            sortType: ''
        };
    },
    methods: {
        getPageData() {
            return {
                page: this.page,
                pageSize: this.pageSize,
                sortField: this.sortField,
                sortType: this.sortType
            };
        },
        setPageSizeInfo(page, pageSize) {
            this.page = page;
            this.pageSize = pageSize;
        },
        resetPage() {
            this.page = 1;
        },
        resetPageSize() {
            this.pageSize = 10;
        },
        resetSortData() {
            this.sortType = '';
            this.sortField = '';
        },
        pageChange(page) {
            this.page = page;
            this.$emit('query');
        },
        pageSizeChange(size) {
            this.page = 1;
            this.pageSize = size;
            this.$emit('query');
        },
        rowClick(row) {
            this.$emit('rowClick', row);
        },
        onCurrentChange(row){
            this.$emit('onCurrentChange', row);
        },
        sortChange(data) {
            this.sortType = data.order.toUpperCase();
            this.sortField = data.key;
            this.resetPage();
            this.$emit('query');
        },
        tableSelection(selection){
            this.$emit('selection', selection);
        },
        exportCsv(param) {
            this.$refs.table.exportCsv(param);
        }
    },
    render() {
        return (
            <div class={ this.hasDarkClass ? 'a-table' : ''}>
                <h-table
                    id={this.multiLevel?.length ? 'multi' : ''}
                    ref="table"
                    multiLevel={this.multiLevel}
                    columns={this.columns}
                    data={this.tableData}
                    border={this.border}
                    showTitle={this.showTitle}
                    height={this.height}
                    maxHeight={this.maxHeight}
                    loading={this.loading}
                    canDrag={this.canDrag}
                    v-on:on-row-click={this.rowClick}
                    disabledHover={this.disabledHover}
                    v-on:on-sort-change={this.sortChange}
                    highlightRow={this.highlightRow}
                    immediateRowClick={this.immediateRowClick}
                    v-on:on-current-change={this.onCurrentChange}
                    rowSelectOnly={this.rowSelectOnly}
                    notSetWidth={this.notSetWidth}
                    autoHeadWidth={this.autoHeadWidth}
                    v-on:on-selection-change={this.tableSelection}
                    noDataText={this.noDataText}
                ></h-table>
                <div class='a-table-footer'>
                    {
                        !!this.$slots.footerInfo && <div>{this.$slots.footerInfo}</div>
                    }
                    {this.hasPage && !this.loading ? (
                        <h-page
                            total={this.total}
                            current={this.page}
                            pageSize={this.pageSize}
                            v-on:on-change={this.pageChange}
                            v-on:on-page-size-change={this.pageSizeChange}
                            showSizer={this.showSizer}
                            showTotal={this.showTotal}
                            showElevator={this.showElevator}
                            isBlur={this.isBlur}
                            sizerOption={this.sizerOption}
                            simple={this.simple}
                            size={this.hPageSize}
                        ></h-page>
                    ) : (
                        ''
                    )}
                </div>
            </div>
        );
    }
};
