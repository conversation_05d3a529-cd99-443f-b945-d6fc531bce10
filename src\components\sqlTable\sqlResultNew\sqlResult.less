.apm-poptip-time {
    .time-item-label {
        display: inline-block;
        text-align: right;
    }
}

.sql-result {
    .h-tabs-return,
    .h-tabs-enter {
        display: flex;
        align-items: center;
    }

    .icon-down {
        cursor: pointer;
    }

    .a-table .h-table-wrapper > .h-spin-fix {
        background-color: unset;
    }

    .h-poptip,
    .h-poptip-rel {
        display: inline-block;
        width: 100%;
        position: relative;
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
        word-break: break-all;
        cursor: pointer;
    }

    .apm-poptip-time,
    .apm-poptip-time .h-poptip-rel {
        display: inline;
    }


    .a-table {
        .h-table th {
            height: 32px;
        }

        .h-table td {
            height: 28px;
        }
    }

    .a-table .h-table-tiptext {
        border: var(--border);
        font-size: 16px;
        color: var(--font-opacity-color);
    }

    .export-tip-wrap {
        display: flex;
        justify-content: flex-end;
    }

    .export-tip {
        height: 24px;
    }

    .cores-list-wrap {
        display: inline-block;
        margin-right: 10px;
    }

    .main-flag {
        .node-flag("static/mainFlag.png");
    }

    .option-text {
        position: relative;
    }

    .tip {
        position: absolute;
        width: 6px;
        height: 6px;
        right: -7px;
        top: -4px;
        transform: scaleX(-1);
        background: #b3b6bd;
        border-radius: 0 0 6px;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    }

    .node-flag(@url) {
        display: inline-block;
        width: 11px;
        height: 11px;
        background: url(@url);
        background-size: 11px 10px;
        background-repeat: no-repeat;
        position: relative;
        top: 2px;
        margin-left: 5px;
    }
}
