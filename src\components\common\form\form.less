.best-form {
    display: flex;
    flex-wrap: wrap;

    // 自定义输入框，下拉框样式
    .h-form-item-label {
        color: var(--font-color) !important;
    }

    .h-input {
        background-color: var(--input-bg-color);
        border: var(--border);
        border-radius: var(--border-radius);
        color: var(--font-color);
    }

    .h-input input {
        color: var(--font-color);
    }

    .h-form-item-error .h-input {
        border-color: var(--error-color) !important;
    }

    .h-select > .h-select-left {
        color: var(--font-color);
        background-color: var(--input-bg-color);
        border: var(--border);
    }

    .h-select-content-input,
    .h-select-input {
        color: var(--font-color);
    }

    .h-form-item-error .h-select-selection {
        border: 1px solid var(--error-color);
    }

    // drawer
    .h-drawer-content {
        background: var(--main-color);
    }

    .h-drawer-header {
        border-bottom: var(--border);
    }

    .h-drawer-header-inner {
        color: var(--font-color);
    }

    // checkbox 公共样式
    .h-checkbox.h-checkbox-disabled > .h-checkbox-inner {
        background-color: var(--border-color) !important;
        border-color: var(--border-color) !important;
    }

    .h-checkbox.h-checkbox-disabled + span {
        color: var(--border-color);
    }

    .h-checkbox + span {
        color: var(--font-color);
    }

    .h-checkbox-inner {
        border: 1px solid var(--font-color);
        border-radius: 2px;
        background: var(--input-bg-color);
    }

    .h-checkbox-inner::after {
        border: none;
    }

    .h-checkbox-checked > .h-checkbox-inner::after {
        border: 2px solid var(--font-color);
        border-top: 0;
        border-left: 0;
    }

    .h-checkbox-checked > .h-checkbox-inner {
        border-color: var(--link-color) !important;
        background: var(--link-color) !important;
    }

    // radio
    .h-radio-inner {
        background-color: var(--wrapper-color);
    }

    // poptip
    .h-input-group-append,
    .h-input-group-prepend {
        background: none;
        border: none;
    }

    .td-time {
        cursor: pointer;

        &:hover {
            color: var(--link-color);
        }
    }
}
