// transaction 页面布局
.demo-spin-icon-load {
    animation: ani-demo-spin 1s linear infinite;
}

@keyframes ani-demo-spin {
    from {
        transform: rotate(0deg);
    }

    50% {
        transform: rotate(180deg);
    }

    to {
        transform: rotate(360deg);
    }
}

.demo-spin-col {
    height: 100%;
    position: relative;
    border: 1px solid var(--font-color);
}

/deep/ .h-spin-fix {
    width: 10%;
    height: 10%;
    position: absolute;
    top: 25%;
    left: 50%;
    transform: translateX(-50%);
    background-color: transparent;
}

.nodata {
    color: var(--font-color);
    font-size: var(--font-size);
    text-align: center;
    margin-top: 50px;
}

.hidden {
    display: none;
}

.main {
    .line-box {
        width: 170px;
        height: 32px;
        margin: 0 5px;
    }

    .line-product {
        min-width: 200px;
        width: auto;
    }

    @media screen and (max-width: 1200px) {
        .line-box {
            width: 140px !important;
        }
    }

    @media screen and (max-width: 1100px) {
        .line-box {
            width: 130px !important;
        }
    }

    @media screen and (max-width: 1000px) {
        .line-box {
            width: 110px !important;
        }
    }

    @media screen and (max-width: 900px) {
        .line-box {
            width: 90px !important;
        }
    }

    @media screen and (max-width: 800px) {
        .line-box {
            width: 80px !important;
        }
    }

    header {
        position: relative;

        .slot-box {
            display: flex;
            justify-content: center;
            align-items: center;
            height: 100%;
            position: absolute;
            top: 1px;
            right: 10px;
        }
    }

    // 间隔线
    .line {
        width: 1px;
        height: 30px;
        border-right: var(--border);
        position: absolute;
        top: 50%;
        right: 590px;
        transform: translateY(-50%);
    }

    // gtid
    .gtid {
        position: relative;

        &::after {
            content: "";
            position: absolute;
            display: inline-block;
            width: 2px;
            height: 20px;
            height: 30px;
            right: -6px;
            top: 1px;
            background: #3f495d;
        }
    }

    // 设置图标
    .icon-setting {
        margin: 0 10px 0 5px;
        cursor: pointer;
    }

    .icon-setting:hover {
        color: #c7c7c7 !important;
    }

    .echarts {
        margin: 0 auto;
        margin-top: 16px;
    }

    #container {
        height: 40%;
    }

    .topo {
        width: 100%;
        display: flex;
        height: calc(48% - 79px);
        justify-content: center;
        align-items: center;
    }

    .highlight {
        border-color: #aaf6ff !important;
        box-shadow: 0 0 4px 0 #63dcf47a;
    }

    .highlight::before {
        width: 0;
        height: 0;
        border: 8px solid transparent;
        margin: 0 auto;
        border-top: 16px solid #aaf6ff !important;
    }
}

.footer {
    position: fixed;
    width: 100%;
    bottom: 10px;
    display: flex;
    justify-content: center;
    color: var(--font-color);
}

.poptip-content div {
    color: #8e8a8a;
    cursor: pointer;

    &:hover {
        color: var(--link-color);
        text-decoration: underline;
    }
}

.poptip-content-active {
    color: var(--link-color) !important;
}
