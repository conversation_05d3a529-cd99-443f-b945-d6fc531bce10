<template>
    <div>
        <h-msg-box v-model="modalData.status" :escClose="true" :mask-closable="false" :title="modalData.title" width="300"
            :closable='false' class="msgbox" @on-open="getCollections">
            <h-input v-model.trim="filterText" placeholder="请输入匹配标签" :icon="modalData.operate === 'del' ? '' : 'plus-round'" clearable :maxlength="30"
                @on-clear="handleInputChange" @on-change="handleInputChange" @on-click="handleAddTag"></h-input>
            <div style="height: 240px; overflow: scroll;">
                <h-checkbox-group v-model="selection" vertical>
                    <h-checkbox v-for="item in filterTagList" :key="item" :label="item" style="display: flex; align-items: center;"><span style="margin: 0 8px; white-space: nowrap;">{{ item
                    }}</span></h-checkbox>
                </h-checkbox-group>
                <p v-show="!filterTagList.length">暂无数据</p>
                <div v-if="loading" class="demo-spin-container">
                    <h-spin fix></h-spin>
                </div>
            </div>
            <template v-slot:footer>
                <span class="total-text" :title="'共计' + tagList.length + '已选' + selection.length">共计 <b>{{ tagList.length }}</b> 已选 <b>{{ selection.length }}</b></span>
                <a-button @click="modalData.status = false">取消</a-button>
                <a-button type="primary" @click="saveSubmit">确定</a-button>
            </template>
        </h-msg-box>
    </div>
</template>

<script>
import _ from 'lodash';
import aButton from '@/components/common/button/aButton';
import { queryTopicsTags, queryContextTags } from '@/api/rcmApi';
export default {
    props: {
        modalInfo: {
            type: Object,
            default: null
        }
    },
    components: { aButton },
    data() {
        return {
            modalData: this.modalInfo,
            formValidate: {
                context: ''
            },
            label: '',
            tagList: [],
            filterTagList: [],
            selection: [],
            filterText: '',
            loading: false
        };
    },
    methods: {
        async getCollections() {
            this.loading = true;
            this.label = this.modalData.type === 'topic' ? '主题' : '上下文';
            this.formValidate.context = this.modalData.context;
            this.selection = [...this.modalData.tags] || [];
            if (this.modalData.operate === 'del') {
                this.tagList = _.uniq(this.modalData.tagList || []);
            } else {
                if (this.modalData.type === 'topic'){
                    await this.queryTopicsTags();
                } else if (this.modalData.type === 'context-tag'){
                    await this.queryContextTags();
                } else {
                    this.tagList = [];
                }
                this.tagList = _.uniq([...this.selection].concat([...this.tagList]));
            }
            this.handleInputChange('');
            this.loading = false;
        },
        // 点击确定
        saveSubmit() {
            this.formValidate.selection = [...this.selection || []];
            // 增加或删除标签
            this.$emit(this.modalData.operate || 'add', { type: this.modalInfo.type || 'tag', ...this.formValidate });
            this.modalData.status = false;
        },
        async queryTopicsTags(){
            try {
                const param = {
                    rcmId: this.modalData.id
                };
                const res = await queryTopicsTags(param);
                if (res.code === '200') {
                    this.tagList = [...res.data?.tags || []];
                } else {
                    this.tagList = [];
                }
            } catch (err) {
                this.tagList = [];
            }
        },
        async queryContextTags(){
            try {
                const param = {
                    rcmId: this.modalData.id
                };
                const res = await queryContextTags(param);
                if (res.code === '200') {
                    this.tagList = [...res.data?.tags || []];
                } else {
                    this.tagList = [];
                }
            } catch (err) {
                this.tagList = [];
            }
        },
        handleInputChange(_e){
            this.filterTagList = this.filterText ? this.tagList.filter(o => o.indexOf(this.filterText) !== -1) : this.tagList;
        },
        handleAddTag(){
            if (!this.filterText) return;
            this.tagList = _.uniq([this.filterText].concat([...this.tagList]));
            this.selection = _.uniq([this.filterText].concat([...this.selection]));
            this.$nextTick(() => {
                this.filterText = '';
                this.handleInputChange();
            });
        }
    }
};
</script>
<style lang="less" scoped>

/deep/ .h-input-icon {
    cursor: pointer;

    &:hover {
        color: var(--link-color);
    }
}

/deep/ .h-modal-body {
    padding: 16px;
}

/deep/ .h-tree-arrow {
    min-width: 0;
}

.h-btn {
    padding: 6px 8px;
}

.total-text {
    text-align: left;
    position: absolute;
    left: 14px;
    bottom: 18px;
    width: 140px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}
</style>
