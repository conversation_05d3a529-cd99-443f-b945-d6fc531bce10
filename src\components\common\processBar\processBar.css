.process {
    width: calc(100% - 12px);
    display: flex;
}

.process .process-label {
    color: var(--font-color);
    line-height: 42px;
    width: 10%;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
}

.process .process-bar-box {
    width: 100%;
    border-radius: var(--border-radius);
    padding: 10px;
    margin: 5px;
    /* background-color: var(--border-color); */
}

.process .process-bar-box > div {
    position: relative;
    background: #2d8de536;
    height: 12px;
    border-radius: 2px;
}

.process .process-bar-box > div > span {
    position: absolute;
    top: -4px;
    left: 50%;
    width: 70%;
    font-weight: 600;
    text-align: center;
    transform: translateX(-50%);
    color: var(--font-opacity-color);
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
}

.process .process-bar-box .progress-box {
    background: var(--box-color);
}

.process .process-bar-box .progress-box > span {
    color: var(--font-color);
}

.process .process-bar-box .progress-bar {
    height: 100%;
    border-radius: 2px;
    transition-property: all;
    transition-duration: 1s;
    background: var(--link-color);
}
