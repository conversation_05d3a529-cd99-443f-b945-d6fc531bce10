<template>
    <div ref="tab-box" class="tab-box">
        <a-loading v-if="loading"></a-loading>
        <div v-else>
            <obs-table
                ref="table1"
                :title="dirTitle"
                :height="tableHeight"
                :showTitle="true"
                :isSimpleTable="true"
                :tableData="dirTableData"
                :columns="dirColumns"
                :notSetWidth="true"
                border
                :autoHeadWidth="true"
                :loading="tableLoading"
                @button-click="handleRefresh" />
        </div>
    </div>
</template>

<script>
import { formatDates } from '@/utils/utils';
import { getManagerProxy } from '@/api/mcApi';
import aLoading from '@/components/common/loading/aLoading';
import obsTable from '@/components/common/obsTable/obsTable';
export default {
    props: {
        nodeData: {
            type: Object,
            default: () => { }
        }
    },
    components: { aLoading, obsTable },
    data() {
        return {
            loading: false,
            tableHeight: 0,
            // 数据内存表运行信息
            dirTitle: {
                label: '慢事务列表',
                slots: [
                    {
                        key: 'refresh',
                        type: 'button',
                        buttonType: 'text',
                        iconName: 'refresh'
                    }
                ]
            },
            dirColumns: [
                {
                    title: '执行时间',
                    key: 'BeginTime',
                    width: 170,
                    render: (h, params) => {
                        const time = params.row?.BeginTime || 0;
                        const timeDate = new Date(time * 1000);
                        return h('div', {
                        }, formatDates(timeDate));
                    }
                },
                {
                    title: '事务号',
                    key: 'TransactionNo',
                    ellipsis: true
                },
                {
                    title: '事务控制器',
                    key: 'TransCtrlID',
                    ellipsis: true
                },
                {
                    title: '耗时（ms）',
                    key: 'UseTimeMS',
                    ellipsis: true
                },
                {
                    title: '功能号',
                    key: 'FunctionID',
                    ellipsis: true
                },
                {
                    title: '处理表',
                    key: 'Tables',
                    width: 200,
                    render: (h, params) => {
                        const list = params.row.Tables.map(v => v.Name);
                        const data = list.length ? list.join(',') : '-';
                        return h('poptip', {
                            props: {
                                customTransferClassName: 'apm-poptip monitor-poptip',
                                title: '处理表明细',
                                placement: 'left-end',
                                positionFixed: true,
                                transfer: true,
                                trigger: 'hover'
                            }
                        }, [
                            h('div', {
                                style: {
                                    width: '180px',
                                    paddingLeft: '7px',
                                    overflow: 'hidden',
                                    textOverflow: 'ellipsis',
                                    whiteSpace: 'nowrap',
                                    cursor: 'pointer'
                                }
                            }, data),
                            h('p', {
                                slot: 'content',
                                style: {
                                    width: '220px',
                                    maxHeight: '100px',
                                    whiteSpace: 'break-spaces',
                                    wordWrap: 'break-word'
                                }
                            }, data)
                        ]);
                    }
                }
            ],
            dirTableData: [],
            tableLoading: false
        };
    },
    mounted() {
        this.fetTableHeight();
        window.addEventListener('resize', this.fetTableHeight);
    },
    beforeDestroy() {
        window.removeEventListener('resize', this.fetTableHeight);
    },
    methods: {
        async initData() {
            this.loading = true;
            await this.getFileData();
            this.loading = false;
        },
        async getFileData() {
            try {
                this.tableLoading = true;
                const { transactions } = await this.getAPi();
                this.dirTableData = transactions?.LongTimeTransactionInfo || [];
            } catch (error) {
                this.dirTableData = [];
            }
            this.tableLoading = false;
        },
        // 接口请求
        async getAPi() {
            const data = {
                sequences: {}
            };
            const param = [
                {
                    manageProxyIp: this.nodeData.ip,
                    manageProxyPort: this.nodeData.manageProxyPort,
                    pluginName: 'ldp_bizproc',
                    funcName: 'uftmdb_data_GetMdbLongTimeTransactionHistory',
                    params: {
                        Count: 10000
                    }
                }
            ];
            const res = await getManagerProxy(JSON.stringify(param));
            if (res.code === '200') {
                !res.data?.[0]?.ErrorNo && Object.keys(res.data?.[0]).length && (data.transactions = res.data[0]);
            }
            return data;
        },
        // 设置table高度
        fetTableHeight() {
            this.tableHeight = this.$refs['tab-box']?.offsetHeight - 80;
        },
        // 刷新页面
        handleRefresh() {
            this.getFileData();
        }
    }
};
</script>
<style lang="less" scoped>
@import url("@/assets/css/poptip-1.less");

.tab-box {
    height: 100%;
    overflow: hidden;
}
</style>
