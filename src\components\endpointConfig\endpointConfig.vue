<!-- 接入点信息 -->
<template>
    <div class="tab-box">
        <div ref="table-box" class="table-box">
            <obs-table :title="tableTitle" :tableData="tableData" :columns="columns"
                :height="tableHeight" @button-click="updateEndpointInfo" />
        </div>

        <div class="table-box">
            <obs-table :title="configTableTitle" :tableData="configTableData" :columns="configColumns"
                :height="tableHeight" showTitle @button-click="configBtnClick" />
        </div>
        <a-loading v-if="loading"></a-loading>

        <!-- 添加、修改接入点弹窗 -->
        <update-endpoint-modal
            v-if="endpointInfo.status" :endpointType="endpointType"
            :modalInfo="endpointInfo" :instanceList="instances || []" @update="initData">
        </update-endpoint-modal>

        <!-- 内存表接入批量配置 -->
        <update-ust-table-config-modal
            v-if="ustTableConfigInfo.status" :modalInfo="ustTableConfigInfo" @update="initData" >
        </update-ust-table-config-modal>

        <!-- MDB配置SQL批量配置 -->
        <update-mdb-config-modal
            v-if="mdpConfigInfo.status" :modalInfo="mdpConfigInfo" @update="initData">
        </update-mdb-config-modal>

        <!-- 抓包发包接入配置 -->
         <update-packet-config-modal
            v-if="packetConfigInfo.status" :modalInfo="packetConfigInfo"
            :productInfo="productInfo" @update="initData">
         </update-packet-config-modal>

        <!-- 抓包发包配置文件更新 -->
         <update-packet-config-file
            v-if="packetConfigFileInfo.status" :modalInfo="packetConfigFileInfo"
            :productInfo="productInfo">
         </update-packet-config-file>

        <!-- MDB配置连通性测试 -->
        <test-connect-modal
            v-if="connectTestModalData.status" :endpointType="endpointType"
            :modalInfo="connectTestModalData" :productInfo="productInfo">
        </test-connect-modal>
    </div>
</template>

<script>
import _ from 'lodash';
import { deleteEndpoint, getProductInstances }  from '@/api/productApi';
import obsTable from '@/components/common/obsTable/obsTable';
import aLoading from '@/components/common/loading/aLoading';
import updateEndpointModal from './modal/updateEndpointModal.vue';
import updateMdbConfigModal from './modal/updateMdbConfigModal.vue';
import testConnectModal from './modal/testConnectModal.vue';
import updatePacketConfigModal from './modal/updatePacketConfigModal.vue';
import updateUstTableConfigModal from './modal/updateUstTableConfigModal.vue';
import updatePacketConfigFile from './modal/updatePacketConfigFile.vue';

import {
    ENDPOINT_TYPE,
    PROTOCOL,
    PROTOCOL_LIST_MAP,
    GET_ENDPOINTS_MAP,
    GET_ENDPOINT_CONFIGS,
    ACCESS_CONFIG_TITLE
} from './constant';
export default {
    name: 'EndpointConfig',
    components: { obsTable, aLoading, updatePacketConfigModal, updateUstTableConfigModal,
        updateEndpointModal, updateMdbConfigModal, testConnectModal, updatePacketConfigFile },
    props: {
        productInfo: {
            type: Object,
            default: {}
        },
        /**
         * 接入点类型
         * @enum typeof ENDPOINT_TYPE
         */
        endpointType: {
            type: String,
            default: () => ENDPOINT_TYPE.MDB
        }
    },
    data() {
        return {
            loading: false,
            tableHeight: 0,
            charsetList: [
                { value: 'gbk', label: 'GBK' },
                { value: 'utf-8', label: 'UTF-8' }
            ],
            tableTitle: {
                label: `接入网关配置`,
                slots: [ENDPOINT_TYPE.MDB, ENDPOINT_TYPE.LOCATE].includes(this.endpointType) ? [{
                    type: 'button',
                    buttonType: 'dark',
                    value: '添加网关节点'
                }] : null
            },
            columns: [
                {
                    title: '接入点名',
                    key: 'name'
                },
                {
                    title: '接入应用节点',
                    key: 'instanceName'
                },
                {
                    title: '接入点IP',
                    key: 'endpointIp'
                },
                {
                    title: '接入点端口',
                    key: 'endpointPort'
                },
                {
                    title: '接入协议',
                    key: 'protocol'
                },
                {
                    title: '字符集',
                    key: 'charset',
                    render: (h, params) => {
                        return h('div', _.find(this.charsetList, ['value', params.row.charset])?.label || params.row.charset);
                    }
                },
                {
                    title: '操作',
                    key: 'action',
                    width: 120,
                    hiddenCol: [ENDPOINT_TYPE.MANAGEMENT, ENDPOINT_TYPE.UST_TABLE, ENDPOINT_TYPE.SEND_PACKET_RECEIVED].includes(this.endpointType),
                    render: (h, params) => {
                        return h('div', [
                            h(
                                'Button',
                                {
                                    props: {
                                        type: 'text',
                                        size: 'small'
                                    },
                                    on: {
                                        click: () => {
                                            this.updateEndpointInfo(params.row);
                                        }
                                    }
                                },
                                '修改'
                            ),
                            h(
                                'Button',
                                {
                                    props: {
                                        type: 'text',
                                        size: 'small'
                                    },
                                    on: {
                                        click: () => {
                                            this.deleteEndpoint(params.row.id, params.row.name);
                                        }
                                    }
                                },
                                '删除'
                            )
                        ]);
                    }
                }
            ],
            tableData: [],
            instances: [],

            configTableTitle: {
                label: `后端服务：${ACCESS_CONFIG_TITLE[this.endpointType]}`,
                slots: this.getAccessConfigTitle()
            },
            configColumns: this.getAccessConfigCol(),
            configTableData: [],

            endpointInfo: {
                status: false,
                isAddModel: true
            },
            mdpConfigInfo: {
                status: false
            },
            ustTableConfigInfo: {
                status: false
            },
            packetConfigInfo: {
                status: false
            },
            packetConfigFileInfo: {
                status: false
            },
            connectTestModalData: {
                status: false
            }
        };
    },
    mounted() {
        this.fetTableHeight();
        window.addEventListener('resize', this.fetTableHeight);
    },
    beforeDestroy() {
        window.removeEventListener('resize', this.fetTableHeight);
    },
    methods: {
        // 设置table高度
        fetTableHeight() {
            this.tableHeight = this.$refs['table-box']?.offsetHeight - 55;
        },
        async initData() {
            this.clearPageData();
            this.loading = true;
            try {
                await this.getProductInstances();
                await this.getEndpoint();
                await this.getEndpointConfigs();
            } finally {
                this.loading = false;
            }
        },
        // 清空页面数据
        clearPageData() {
            this.instances = [];
            this.tableData = [];
            this.configTableData = [];
        },
        /**
         * 添加修改接入点弹窗
         */
        updateEndpointInfo(row) {
            this.endpointInfo = row ? {
                status: true,
                productInstNo: this.productInfo.productInstNo,
                type: 'update',
                ...row
            } : {
                status: true,
                productInstNo: this.productInfo.productInstNo,
                type: 'add',
                protocol: PROTOCOL_LIST_MAP[this.endpointType]?.[0] || PROTOCOL.T2,
                id: ''
            };
        },
        /**
        * 配置按钮事件
        */
        configBtnClick(key) {
            // 连通性测试
            if (key === 'openTestConnecttModal') {
                this.openTestConnecttModal();
                return;
            }

            // 新增接入点
            if (key === 'addEndpoint') {
                this.updateEndpointInfo();
                return;
            }

            // 批量配置
            if (!this.configTableData?.length) return this.$hMessage.warning('无数据可配置');
            this.handleBatchConfig();

        },
        /**
         * 抓包发包配置-新增、配置接入点
         */
        handlePacketConfig(row) {
            this.packetConfigInfo = {
                ...row,
                status: true
            };
        },

        handlePacketFile(row) {
            this.packetConfigFileInfo = {
                ...row,
                status: true
            };
        },

        /**
         * 批量配置
         */
        handleBatchConfig() {
            // 识别是内存表还是MDB批量配置
            if (this.endpointType === ENDPOINT_TYPE.MDB) {
                this.mdpConfigInfo = {
                    status: true,
                    productInstNo: this.productInfo.productInstNo,
                    configData: this.configTableData
                };
            } else if (this.endpointType === ENDPOINT_TYPE.UST_TABLE) {
                this.ustTableConfigInfo = {
                    status: true,
                    productInstNo: this.productInfo.productInstNo,
                    configData: this.configTableData
                };
            }
        },

        // 获取应用节点列表
        async getProductInstances() {
            const res = await getProductInstances({ productId: this.productInfo.id });
            if (res.code === '200') {
                this.instances = res?.data?.instances || [];
            }
        },
        /**
         * 获取接入点信息
         */
        async getEndpoint() {
            const param = {
                productId: this.productInfo.productInstNo,
                protocol: PROTOCOL_LIST_MAP[this.endpointType]?.[0]
            };
            const apiName = GET_ENDPOINTS_MAP[this.endpointType];
            if (!apiName) {
                console.warn('不存在的接入点类型：', this.endpointType);
                return;
            }
            const res = await apiName(param);
            if (res.code === '200') {
                this.tableData = res?.data || [];
            }
        },
        /**
         * 获取接入配置信息
         */
        async getEndpointConfigs() {
            const apiName = GET_ENDPOINT_CONFIGS[this.endpointType];
            if (!apiName) {
                console.warn('不存在的接入点类型：', this.endpointType);
                return;
            }
            const res = await apiName({ productId: this.productInfo.productInstNo });
            if (res.code === '200') {
                this.configTableData = res?.data?.configs || [];
            }
        },
        // 删除接入点
        deleteEndpoint(id, name) {
            this.$hMsgBoxSafe.confirm({
                title: `删除`,
                content: `您确定删除名为"${name}"的节点吗？`,
                onOk: async () => {
                    const res = await deleteEndpoint({
                        id
                    });
                    if (res.success) {
                        this.$hMessage.success('删除成功');
                        this.initData();
                    }
                }
            });
        },
        // 打开连通性测试弹窗
        openTestConnecttModal() {
            this.connectTestModalData = {
                status: true
            };
        },

        /**
         * 根据接入点类型，生成 后端服务配置 title和按钮配置
         */
        getAccessConfigTitle() {
            switch (this.endpointType) {
                case ENDPOINT_TYPE.MDB:
                case ENDPOINT_TYPE.UST_TABLE:
                    return [{
                        type: 'button',
                        buttonType: 'dark',
                        value: '批量配置',
                        key: `betchConfig`
                    },
                    {
                        type: 'button',
                        buttonType: 'dark',
                        value: '连通性测试',
                        key: 'openTestConnecttModal'
                    }];
                case ENDPOINT_TYPE.SEND_PACKET_RECEIVED:
                    return [
                        {
                            type: 'button',
                            buttonType: 'dark',
                            value: '添加节点',
                            key: `addEndpoint`
                        },
                        {
                            type: 'button',
                            buttonType: 'dark',
                            value: '连通性测试',
                            key: 'openTestConnecttModal'
                        }
                    ];
                default:
                    return [
                        {
                            type: 'button',
                            buttonType: 'dark',
                            value: '连通性测试',
                            key: 'openTestConnecttModal'
                        }
                    ];
            }
        },

        /**
         * 根据接入点类型，生成 后端服务配置 的表头信息
         */
        getAccessConfigCol() {
            switch (this.endpointType) {
                case ENDPOINT_TYPE.MANAGEMENT:
                    return  [
                        {
                            title: '应用节点名',
                            key: 'instanceName'
                        },
                        {
                            title: '应用节点类型',
                            key: 'instanceType',
                            render: (h, params) => {
                                return h(
                                    'div',
                                    this.$store?.state?.apmDirDesc?.appTypeDictDesc?.[params.row.instanceType] ||
                                    params.row.instanceType
                                );
                            },
                            minWidth: 120
                        },
                        {
                            title: '应用身份',
                            key: 'instanceIdentities',
                            minWidth: 120,
                            render: (h, params) => {
                                return h(
                                    'span', params.row?.instanceIdentities?.map(
                                        item => {
                                            return this.$store?.state?.apmDirDesc?.instanceIdentityDict?.[item];
                                        })?.join(',') ?? '-'
                                );
                            }
                        },
                        {
                            title: '管理IP',
                            key: 'manageProxyIp'
                        },
                        {
                            title: '管理端口',
                            key: 'manageProxyPort'
                        }
                    ];
                case ENDPOINT_TYPE.MDB:
                    return  [
                        {
                            title: '应用节点',
                            key: 'instanceNo',
                            render: (h, params) => {
                                return h('div', params.row.instanceNo || '-');
                            }
                        },
                        {
                            title: '应用集群',
                            key: 'clusterName',
                            render: (h, params) => {
                                return h('div', params.row.clusterName || '-');
                            }
                        },
                        {
                            title: '系统号',
                            key: 'systemNo',
                            formatMethod: (row) => row?.systemNo ?? '-'
                        },
                        {
                            title: '功能号',
                            key: 'sqlFuncNo',
                            formatMethod: (row) => row?.sqlFuncNo ?? '-'
                        }
                    ];
                case ENDPOINT_TYPE.UST_TABLE:
                    return  [
                        {
                            title: '应用节点',
                            key: 'instanceNo',
                            render: (h, params) => {
                                return h('div', params.row.instanceNo || '-');
                            }
                        },
                        {
                            title: '应用集群',
                            key: 'clusterName',
                            render: (h, params) => {
                                return h('div', params.row.clusterName || '-');
                            }
                        },
                        {
                            title: '接入IP',
                            key: 'endpointIp'
                        },
                        {
                            title: '接入端口',
                            key: 'endpointPort'
                        },
                        {
                            title: '接入协议',
                            key: 'protocol'
                        },
                        {
                            title: '字符集',
                            key: 'charset',
                            render: (h, params) => {
                                return h('div', _.find(this.charsetList, ['value', params.row.charset])?.label || params.row.charset);
                            }
                        },
                        {
                            title: '系统号',
                            key: 'systemNo',
                            formatMethod: (row) => row?.systemNo ?? '-'
                        },
                        {
                            title: '功能号',
                            key: 'sqlFuncNo',
                            formatMethod: (row) => row?.sqlFuncNo ?? '-'
                        }
                    ];
                case ENDPOINT_TYPE.SEND_PACKET_RECEIVED:
                    return  [
                        {
                            title: '应用节点',
                            key: 'instanceNo',
                            render: (h, params) => {
                                return h('div', params.row.instanceNo || '-');
                            }
                        },
                        {
                            title: '应用集群',
                            key: 'clusterName',
                            render: (h, params) => {
                                return h('div', params.row.clusterName || '-');
                            }
                        },
                        {
                            title: '接入IP',
                            key: 'endpointIp',
                            minWidth: 110
                        },
                        {
                            title: '接入端口',
                            key: 'endpointPort'
                        },
                        {
                            title: '接入协议',
                            key: 'protocol'
                        },
                        {
                            title: '字符集',
                            key: 'charset',
                            render: (h, params) => {
                                return h('div', _.find(this.charsetList, ['value', params.row.charset])?.label || params.row.charset);
                            }
                        },
                        {
                            title: '支持抓包',
                            key: 'supportPacketCapture',
                            formatMethod: (row) => row.supportPacketCapture ? '是' : '否'
                        },
                        {
                            title: '支持发包',
                            key: 'supportPacketSender',
                            formatMethod: (row) => row.supportPacketSender ? '是' : '否'
                        },
                        {
                            title: '配置文件',
                            key: 'protocolCertificateAddr',
                            minWidth: 180,
                            fixed: 'right',
                            render: (h, params) => {
                                const filePath = params.row.protocolCertificateAddr || '-';
                                const fileName = filePath.split('/').pop();  // 通过 split 和 pop 提取文件名
                                return h('div', {
                                    attrs: {
                                        title: fileName
                                    },
                                    style: {
                                        width: '100%'
                                    }
                                }, [
                                    h('Button',
                                        {
                                            props: {
                                                size: 'small',
                                                type: 'text'
                                            },

                                            style: {
                                                width: '100%',
                                                padding: 0,
                                                'text-align': 'left',
                                                overflow: 'hidden', // 超出的文本隐藏
                                                'text-overflow': 'ellipsis', // 溢出用省略号显示
                                                'white-space': 'nowrap' // 溢出不换行
                                            },
                                            on: {
                                                click: () => {
                                                    this.handlePacketFile(params.row);
                                                }
                                            }
                                        },
                                        fileName
                                    )
                                ]);
                            }
                        },
                        {
                            title: '操作',
                            key: 'action',
                            fixed: 'right',
                            width: 100,
                            render: (h, params) => {
                                return h('div', [
                                    h(
                                        'Button',
                                        {
                                            props: {
                                                type: 'text',
                                                size: 'small'
                                            },
                                            style: {
                                                padding: 0
                                            },
                                            on: {
                                                click: () => {
                                                    this.handlePacketConfig(params.row);
                                                }
                                            }
                                        },
                                        '配置'
                                    ),
                                    h(
                                        'Button',
                                        {
                                            props: {
                                                type: 'text',
                                                size: 'small'
                                            },
                                            on: {
                                                click: () => {
                                                    this.deleteEndpoint(params.row.id, params.row?.instanceNo);
                                                }
                                            }
                                        },
                                        '删除'
                                    )
                                ])
                                ;
                            }
                        }
                    ];
                case ENDPOINT_TYPE.LOCATE:
                    return [
                        {
                            title: '应用节点',
                            key: 'instanceName'
                        },
                        {
                            title: '开发平台',
                            key: 'developPlatform'
                        },
                        {
                            title: '应用插件ID',
                            key: 'pluginId'
                        },
                        {
                            title: '功能号',
                            key: 'funNo',
                            formatMethod: (row) => row?.funNo ?? '-'
                        }
                    ];
            }
        }
    }
};
</script>

<style lang="less" scoped>
.tab-box {
    position: relative;
    width: 100%;
    height: calc(100% - 18px);

    .table-box {
        height: calc(50% - 8px);
        padding: 0;

        .obs-table {
            padding: 0;
        }
    }
}
</style>
