/*
 * @Description: apm 下拉菜单
 * @Author: <PERSON><PERSON>
 * @Date: 2023-09-05 14:00:07
 * @LastEditTime: 2024-02-28 13:52:23
 * @LastEditors: yingzx38608 <EMAIL>
 */
import _ from 'lodash';
import './apmDropMenu.less';
export default {
    name: 'apmDropMenu',
    props: {
        dropMenuList: {
            type: Array,
            default: () => []
        },
        isMultiple: {
            type: Boolean,
            default: false
        },
        disabled: {
            type: Boolean,
            default: false
        }

    },
    data() {
        return {
            selectedMenu: [],
            visable: false
        };
    },
    mounted() {
    },
    computed: {
        selectedLabel: {
            get: function() {
                const list = [];
                this.selectedMenu.forEach(ele => {
                    const menus = ele.split(':');
                    const group = _.find(this.dropMenuList, o => { return o.key === menus[0]; });
                    const node = _.find(group?.menus, o => { return o.value === menus[1]; })?.label;
                    node && list.push(`${group?.label}：${node}`);
                });
                return list.join(',') || '请选择节点';
            }
        }
    },
    methods: {
        handleClick(key, val) {
            if (this.disabled) return;
            const value = key + ':' + val;
            const index = this.selectedMenu.indexOf(value);
            if (index > -1) {
                this.selectedMenu.splice(index, 1);
            } else {
                if (this.isMultiple) {
                    this.selectedMenu.push(value);
                } else {
                    this.selectedMenu = [value];
                }
            }
            if (!this.isMultiple) this.visable = false;
            this.$emit('on-change', this.selectedMenu);
        },
        setSelectMenu(data) {
            this.selectedMenu = data;
        },
        getComputedStyle() {
            return { 'max-height': this.visable ? '250px' : 0 };
        },
        toggleDropModal() {
            if (this.dropMenuList.length) {
                this.visable = !this.visable;
            } else {
                this.$hMessage.info('当前菜单无节点信息');
            }
        }
    },
    render() {
        return <div>
            <div class="apm-drop-menu">
                <span
                    title={this.selectedMenu.join(',')}
                    onClick={this.toggleDropModal}>{this.selectedLabel}</span>
                <h-icon class="icon-drop" name="unfold" v-on:on-click={this.toggleDropModal}></h-icon>
                <div class="box-drop" style={this.getComputedStyle()}>
                    <h-icon class="drop-close" name="close-round" v-on:on-click={() => { this.visable = false; }}></h-icon>
                    <div>
                        {
                            this.dropMenuList.map(item => {
                                return <h-row class="menu-list">
                                    { item.groupName ? <h-col class="menu-title" span="24">{item.groupName}</h-col> : ''}
                                    <h-col class="menu-key" span="5">{item.label}</h-col>
                                    <h-col class="menu-values" span="19">
                                        {
                                            item.menus.map(ele => {
                                                const value = item.key + ':' + ele.value;
                                                return <span
                                                    class={this.selectedMenu.indexOf(value) > -1
                                                        ? 'drop-span-active'
                                                        : ''}
                                                    onClick={
                                                        () => {
                                                            this.handleClick(item.key, ele.value);
                                                        }}>{ele.label}</span>;
                                            })
                                        }
                                    </h-col>
                                </h-row>;
                            })
                        }
                    </div>
                </div>
            </div>
        </div>;
    }
};
