<!-- /**
 * <AUTHOR>
 * @email <EMAIL>
 * @create date 2024-09-10 07:07:08
 * @modify date 2024-09-11 11:07:29
 * @desc [中心间配置]
 */ -->
<template>
  <div class="inner">
    <a-title title="中心间配置（Inter Zones）">
      <slot>
        <a-button type="dark" class="inner-add" @click="onAdd"> 新增 </a-button>
      </slot>
    </a-title>

    <div class="inner-tablle">
      <a-table
        ref="table"
        :tableData="tableData"
        :columns="columns"
        :loading="loading"
        :hasPage="false"
        showTitle
      />
    </div>

    <inter-zone-modal v-if="modal.status" :rcmId="rcmId" @reload="initData" />
  </div>
</template>

<script>
import { defineComponent, getCurrentInstance, ref } from 'vue';
import aTitle from '@/components/common/title/aTitle';
import aButton from '@/components/common/button/aButton';
import aTable from '@/components/common/table/aTable';
import { getInterZones, deleteInterZoneConfig } from '@/api/rcmApi';
import interZoneModal from './interZongModal.vue';
import { useInterZoneModal } from './hooks';
import { SYNC_MODEL_LIST } from './constant';

export default defineComponent({
    name: 'InterZone',
    props: {
        rcmId: String
    },
    setup(props) {
        const { onAdd, onEdit, modal } = useInterZoneModal();
        const { proxy } = getCurrentInstance();
        const tableData = ref([]);
        const loading = ref(false);
        /**
     * 删除配置
     */
        const handleDeleteRule = async (id) => {
            const res = await deleteInterZoneConfig({ rcmId: props.rcmId, id });
            if (res.code === '200') {
                initData();
                proxy.$hMessageSafe.success({
                    content: `该配置已删除"`,
                    duration: 3
                });
            }
        };
        const columns = [
            {
                title: '左中心',
                key: 'firstZoneNames',
                render: (h, params) => {
                    return h(
                        'span',
                        {},
                        params.row.firstZoneNames?.length
                            ? params.row.firstZoneNames.join(',')
                            : '-'
                    );
                }
            },
            {
                title: '右中心',
                key: 'secondZoneNames',
                render: (h, params) =>
                    h(
                        'span',
                        {},
                        params.row.secondZoneNames?.length
                            ? params.row.secondZoneNames.join(',')
                            : '-'
                    )
            },
            {
                title: '跨中心节点间RPO值',
                key: 'rpo'
            },
            {
                title: '消息同步的确认时刻',
                key: 'syncAckPoint',
                render: (h, params) =>
                    h(
                        'span',
                        {},
                        SYNC_MODEL_LIST.find(
                            (item) => item.value === params.row.syncAckPoint
                        )?.name || '-'
                    )
            },
            {
                title: '操作',
                key: 'action',
                fixed: 'right',
                width: 110,
                render: (h, params) => {
                    return h('div', [
                        h(
                            'Button',
                            {
                                props: {
                                    size: 'small',
                                    type: 'text'
                                },
                                on: {
                                    click: () => {
                                        onEdit(params.row);
                                    }
                                }
                            },
                            '编辑'
                        ),
                        h(
                            'Button',
                            {
                                props: {
                                    size: 'small',
                                    type: 'text'
                                },
                                on: {
                                    click: () => {
                                        proxy.$hMsgBoxSafe.confirm({
                                            title: `删除`,
                                            content: `您确认要删除该配置吗？`,
                                            onOk: async () => {
                                                handleDeleteRule(params.row.id);
                                            }
                                        });
                                    }
                                }
                            },
                            '删除'
                        )
                    ]);
                }
            }
        ];
        const initData = async () => {
            try {
                loading.value = true;
                const res = await getInterZones({ rcmId: props.rcmId });
                if (res.data) {
                    tableData.value = res.data;
                }
            } finally {
                loading.value = false;
            }
        };
        return {
            columns,
            onAdd,
            modal,
            onEdit,
            initData,
            tableData,
            loading
        };
    },
    components: { aTitle, aButton, aTable, interZoneModal }
});
</script>

<style scoped lang="less">
.inner {
    position: relative;

    &-add {
        position: absolute;
        right: 6px;
        top: 5px;
    }
}
</style>
