<template>
    <div class="tab-box">
        <a-loading v-if="loading"></a-loading>
        <div v-else class="tab-child-box">
            <obs-table ref="routsTable" :title="routsTitle" :height="140" showTitle :tableData='routsTableData'
                :columns="routsColumns" :hasPage="false" />
            <div class="tab-child">
                <h-tabs value="GW">
                    <h-tab-pane label="GW" name="GW">
                        <div v-if="GwBackendList.length">
                            <div v-for="item in GwBackendList" :key="item.BackendId" class="detail-box">
                                <info-sum-bar :data="setBackendData(item)"></info-sum-bar>
                                <obs-table maxHeight="220" showTitle :tableData='item.Sites' :columns="backendColumns"
                                    :hasPage="false" />
                            </div>
                        </div>
                        <no-data v-else></no-data>
                    </h-tab-pane>
                    <h-tab-pane label="AS_TCP" name="AS_TCP">
                        <div v-if="AstcpBackendList.length">
                            <div v-for="item in AstcpBackendList" :key="item.BackendId" class="detail-box">
                                <info-sum-bar :data="setBackendData(item)"></info-sum-bar>
                                <obs-table maxHeight="220" showTitle :tableData='item.Sites' :columns="backendColumns"
                                    :hasPage="false" />
                            </div>
                        </div>
                        <no-data v-else></no-data>
                    </h-tab-pane>
                    <h-tab-pane label="AS_RCM" name="AS_RCM">
                        <div v-if="RcmBackendList.length">
                            <div v-for="item in RcmBackendList" :key="item.BackendId" class="detail-box">
                                <info-sum-bar :data="setBackendData(item)"></info-sum-bar>
                                <obs-table maxHeight="220" showTitle :tableData='item.Sites' :columns="rcmBackendColumns"
                                    :hasPage="false" />
                            </div>
                        </div>
                        <no-data v-else></no-data>
                    </h-tab-pane>
                    <h-tab-pane label="PLUGIN" name="PLUGIN">
                        <div v-if="PluginBackendList.length">
                            <div v-for="item in PluginBackendList" :key="item.BackendId" class="detail-box">
                                <info-sum-bar :data="setBackendData(item)"></info-sum-bar>
                                <br />
                            </div>
                        </div>
                        <no-data v-else></no-data>
                    </h-tab-pane>
                </h-tabs>
            </div>
        </div>
    </div>
</template>

<script>
import obsTable from '@/components/common/obsTable/obsTable';
import infoSumBar from '@/components/common/infoBar/infoSumBar';
import aLoading from '@/components/common/loading/aLoading';
import noData from '@/components/common/noData/noData';
import { getManagerProxy } from '@/api/mcApi';
import _ from 'lodash';
export default {
    props: {
        nodeData: {
            type: Object,
            default: () => { }
        },
        pollTime: {
            type: Number,
            default: 0
        }
    },
    components: { obsTable, aLoading, noData, infoSumBar },
    data() {
        // eslint-disable-next-line max-params
        function setRender(h, params, key, poptipInfo) {
            return h('div', [
                h(
                    'Poptip',
                    {
                        class: 'apm-poptip',
                        props: {
                            title: poptipInfo.title,
                            placement: 'top-start',
                            positionFixed: true
                        }
                    },
                    [
                        h(
                            'span',
                            [params.row[key]]
                        ),
                        h(
                            'div',
                            {
                                slot: 'content',
                                class: 'pop-content'
                            },
                            [
                                ...Object.keys(poptipInfo.content).map((key) => {
                                    return h(
                                        'p',
                                        [
                                            h(
                                                'span',
                                                {
                                                    class: 'pop-label'
                                                },
                                                `${poptipInfo.contentDic[key] || key}`
                                            ),
                                            h(
                                                'span',
                                                {
                                                    class: 'pop-value',
                                                    attrs: {
                                                        title: `${params.row[key]}`
                                                    }
                                                },
                                                `${params.row[key]}`
                                            )
                                        ]
                                    );
                                })
                            ]
                        )
                    ]
                )
            ]);
        };
        const backendColumns = [
            {
                title: '节点名称',
                key: 'SiteName',
                ellipsis: true,
                render: (h, params) => {
                    const poptipInfo = {
                        title: '节点信息',
                        contentDic: {
                            SiteId: '节点ID',
                            SiteIndex: '节点索引',
                            SiteName: '节点名称',
                            IP: '节点IP',
                            Port: '节点端口',
                            ActiveStatus: '节点激活状态'
                        },
                        content: {
                            SiteId: params.row.SiteId,
                            SiteIndex: params.row.SiteIndex,
                            SiteName: params.row.SiteName,
                            IP: params.row.IP,
                            Port: params.row.Port,
                            ActiveStatus: params.row.ActiveStatus
                        }
                    };
                    return setRender(h, params, 'SiteName', poptipInfo);
                }
            },
            {
                title: '激活状态',
                key: 'ActiveStatus',
                ellipsis: true
            },
            {
                title: '连接状态',
                key: 'State',
                ellipsis: true
            },
            {
                title: '连接成功次数',
                key: 'ConnectTimes',
                ellipsis: true
            },
            {
                title: '接收消息计数',
                key: 'RecvCount',
                ellipsis: true
            },
            {
                title: '发送成功计数',
                key: 'SendSuccessCount',
                ellipsis: true
            },
            {
                title: '发送失败计数',
                key: 'SendFailCount',
                ellipsis: true
            }
        ];
        const rcmBackendColumns = [
            {
                title: 'SessionNodeName',
                key: 'SessionNodeName',
                ellipsis: true
            },
            {
                title: 'CombineTx',
                key: 'CombineTx',
                ellipsis: true
            },
            {
                title: 'PeerCtxId',
                key: 'PeerCtxId',
                ellipsis: true,
                render: (h, params) => {
                    const poptipInfo = {
                        title: '会话信息',
                        contentDic: {},
                        content: {
                            SiteIndex: params.row.SiteIndex,
                            NodeNo: params.row.NodeNo,
                            SessionNodeName: params.row.SessionNodeName,
                            PeerCtxId: params.row.PeerCtxId,
                            RxPartiNo: params.row.RxPartiNo,
                            Version: params.row.Version,
                            BizStatus: params.row.BizStatus,
                            LaunchTimestamp: params.row.LaunchTimestamp,
                            CombineTx: params.row.CombineTx,
                            ToBackupLastIndex: params.row.ToBackupLastIndex
                        }
                    };
                    return setRender(h, params, 'PeerCtxId', poptipInfo);
                }
            },
            {
                title: 'ArbStatus',
                key: 'ArbStatus',
                ellipsis: true
            },
            {
                title: 'AppStatus',
                key: 'AppStatus',
                ellipsis: true
            },
            {
                title: 'IsConnected',
                key: 'IsConnected',
                ellipsis: true
            },
            {
                title: 'SendRegCount',
                key: 'SendRegCount',
                ellipsis: true
            },
            {
                title: 'RecvRegCount',
                key: 'RecvRegCount',
                ellipsis: true
            },
            {
                title: 'SendCount',
                key: 'SendCount',
                ellipsis: true
            }
        ];
        // GW
        const spanDic = {
            RecvCount: '接收消息计数',
            SendSuccessCount: '发送成功计数',
            SendFailCount: '发送失败计数',
            InterceptCount: '拦截计数',
            ErrorRspCount: '接收错误应答计数'
        };
        // AS_TCP
        const astcpSpanDic = {
            SendMasterSuccessCount: '发送主核心成功消息计数',
            SendMasterFailCount: '发送主核心失败消息计数',
            SendBackupSuccessCount: '发送备核心成功消息计数',
            SendBackupFailCount: '发送备核心失败消息计数'
        };
        // PLUGIN
        const pluginSpanDic = {
            RecvRegRspCount: '收到注册应答个数',
            SendRegSuccCount: '发送注册信息成功个数',
            SendRegFailCount: '发送注册信息失败个数',
            UnReadyDropCount: '未就绪过滤消息个数'
        };
        // RCM
        const rcmSpanDic = {
            ...spanDic,
            ...pluginSpanDic,
            ToSendRcmRspCount: '发送应答个数',
            ...astcpSpanDic
        };
        // PopTip
        const poptipDic = {
            BackendId: 'ID',
            Type: '类型',
            ConfigName: '配置名称',
            MsgType: '通信消息类型',
            ProductName: '产品名',
            ClusterName: '集群名',
            SiteInUse: '维护的节点个数',
            IsArb: '是否仲裁',
            MasterSiteIndex: '主核心节点索引',
            MasterNode: '主核心节点名'
        };
        return {
            loading: true,
            // 路由转发统计
            routsTitle: {
                label: '路由转发统计'
            },
            routsTableData: [],
            routsColumns: [
                {
                    title: '功能号',
                    key: 'Functions',
                    ellipsis: true
                },
                {
                    title: '目标功能号',
                    key: 'SystemNo',
                    ellipsis: true
                },
                {
                    title: '目标节点号',
                    key: 'NodeNo',
                    ellipsis: true
                },
                {
                    title: '目标端ID',
                    key: 'BackendID',
                    ellipsis: true
                },
                {
                    title: '类型',
                    key: 'Type',
                    ellipsis: true
                },
                {
                    title: '转发次数',
                    key: 'Count',
                    ellipsis: true
                }
            ],
            BackendData: {
                title: {
                    label: {
                        labelDic: [],
                        labelInfo: {}
                    },
                    slots: [
                        {
                            type: 'text', // 文本
                            label: 'ID',
                            value: '-',
                            poptipInfo: {
                                placement: 'top-end',
                                title: '端信息',
                                contentDic: poptipDic,
                                content: {}
                            }
                        }
                    ]
                },
                direction: 'grid',
                gridSpan: 5,
                details: []
            },
            // type 分类
            GwBackendList: [],
            AstcpBackendList: [],
            RcmBackendList: [],
            PluginBackendList: [],
            spanDic: spanDic,
            astcpSpanDic: astcpSpanDic,
            pluginSpanDic: pluginSpanDic,
            rcmSpanDic: rcmSpanDic,
            backendColumns: backendColumns,
            rcmBackendColumns: rcmBackendColumns
        };
    },
    methods: {
        async initData() {
            this.loading = true;
            await this.getFileData();
            this.loading = false;
        },
        // infoBar数据转换
        setBackendData(param) {
            const data = _.cloneDeep(this.BackendData);
            data.title.slots[0].poptipInfo.content = {};
            if (param.Type === 'GW' || param.Type === 'AS_TCP') {
                this.setGwAsTcp(data, param);
            } else if (param.Type === 'PLUGIN') {
                this.setPlugin(data, param);
            } else if (param.Type === 'AS_RCM') {
                this.setRCM(data, param);
            }

            data.details.forEach((v, idx) => {
                data.details[idx].info.value = param[v.info.key];
            });
            return data;
        },
        // GW ASTCP
        setGwAsTcp(data, param) {
            data.title.label.labelDic = [{ key: 'ConfigName', label: '配置名称' }];
            data.title.label.labelInfo = {
                ConfigName: param.ConfigName
            };
            data.title.slots[0].value = param.BackendId;
            data.title.slots[0].poptipInfo.content = {
                BackendId: param.BackendId,
                Type: param.Type,
                ConfigName: param.ConfigName,
                MsgType: param.MsgType,
                ProductName: param.ProductName,
                ClusterName: param.ClusterName,
                SiteInUse: param.SiteInUse,
                IsArb: param.IsArb
            };
            if (param.Type === 'GW') {
                data.details = this.setInfoDetail(this.spanDic);
            }
            if (param.Type === 'AS_TCP') {
                data.title.slots[0].poptipInfo.content = {
                    ...data.title.slots[0].poptipInfo.content,
                    MasterSiteIndex: param.MasterSiteIndex,
                    MasterNode: param.MasterNode
                };
                data.details = this.setInfoDetail({ ...this.spanDic, ...this.astcpSpanDic });
            }
        },
        // PLUGIN
        setPlugin(data, param) {
            data.title.label.labelDic = [{ key: 'ConfigName', label: '配置名称' }];
            data.title.label.labelInfo = {
                ConfigName: param.ConfigName
            };
            data.title.slots[0].value = param.BackendId;
            data.title.slots[0].poptipInfo.content = {
                BackendId: param.BackendId,
                Type: param.Type,
                ConfigName: param.ConfigName,
                IsDynamicCreate: param.IsDynamicCreate,
                ConfigInst: param.ConfigInst,
                PluginName: param.PluginName,
                PluginInst: param.PluginInst,
                PluginState: param.PluginState
            };
            data.details = this.setInfoDetail({ ...this.spanDic, ...this.pluginSpanDic });
        },
        // RCM
        setRCM(data, param) {
            data.title.label.labelDic = [{ key: 'AliasName', label: '别名' }];
            data.title.label.labelInfo = {
                AliasName: param.AliasName
            };
            data.title.slots[0].value = param.BackendId;
            data.title.slots[0].poptipInfo.content = {
                BackendId: param.BackendId,
                Type: param.Type,
                AliasName: param.AliasName,
                Product: param.Product,
                Cluster: param.Cluster,
                TxTopic: param.TxTopic,
                IsDynamicCreate: param.IsDynamicCreate,
                IsArb: param.IsArb,
                IsEnableSession: param.IsEnableSession,
                IsEnableQueryFirst: param.IsEnableQueryFirst,
                IsEnableMultiShard: param.IsEnableMultiShard,
                IsRcmProxy: param.IsRcmProxy
            };
            data.details = this.setInfoDetail(this.rcmSpanDic);
            param.Sites = param?.ClusterSession ? this.setRcmTableData(param.ClusterSession) : [...param?.Sessions || []];
        },
        // 解耦rcm表格数据
        setRcmTableData(clusterSession) {
            const tableData = [];
            for (const sessions of Object.values(clusterSession || [])) {
                for (const session of Object.values(sessions.Sessions)) {
                    tableData.push({
                        CombineTx: sessions.CombineTx,
                        ToBackupLastIndex: sessions.ToBackupLastIndex,
                        ...session
                    });
                }
            }
            return tableData;
        },
        // 设置infoBar的detail值
        setInfoDetail(spanDic) {
            const details = [];
            Object.keys(spanDic).forEach(key => {
                details.push({ type: 'text', title: spanDic[key], info: { key: key, value: '-' } });
            });
            return details;
        },
        // 构造页面数据
        async getFileData() {
            const { BackendStat } = await this.getAPi();
            // Routes表格
            const Routes = [...(BackendStat?.Routes || [])];
            // Backends分类
            const Backends = [...(BackendStat?.Backends || [])];
            Routes.forEach(v => { v.Type = _.find(Backends, ['BackendId', v.BackendID])?.Type || ''; });
            this.routsTableData = [...Routes];
            this.GwBackendList = Backends.filter(v => v.Type === 'GW');
            this.AstcpBackendList = Backends.filter(v => v.Type === 'AS_TCP');
            this.RcmBackendList = Backends.filter(v => v.Type === 'AS_RCM');
            this.PluginBackendList = Backends.filter(v => v.Type === 'PLUGIN');
        },
        // 接口请求
        async getAPi() {
            const data = {
                BackendStat: {}
            };
            const param = [
                {
                    manageProxyIp: this.nodeData.ip,
                    manageProxyPort: this.nodeData.manageProxyPort,
                    pluginName: 'ldp_front',
                    funcName: 'BackendStat'
                }
            ];
            try {
                const res = await getManagerProxy(JSON.stringify(param));
                if (res.code === '200') {
                    !res.data?.[0]?.ErrorNo && Object.keys(res.data?.[0]).length && (data.BackendStat = res.data[0]);
                }
                return data;
            } catch (err) {
                this.$emit('clear');
            }
        }
    }
};
</script>
<style lang="less">
// render函数生成的节点无法匹配scoped id， 需要用全局去处理
@import url("@/assets/css/poptip-1.less");
</style>

<style lang="less" scoped>
.tab-box {
    height: 100%;
    overflow: hidden;

    /deep/ .info-sum-bar .info-grid {
        padding: 0 15px 0 5px;

        .info-bar {
            border: none;
        }
    }

    .tab-child-box {
        height: 100%;

        .tab-child {
            height: calc(100% - 220px);
        }

        .obs-table {
            height: auto;
        }
    }

    .tab-child .detail-box {
        background: var(--wrapper-color);
    }

    /deep/ .h-poptip-popper {
        width: 250px;

        .pop-content {
            max-height: 200px;

            .pop-label {
                display: inline-block;
                width: 50%;
                margin-right: 10px;
                overflow: hidden;
                white-space: nowrap;
                text-overflow: ellipsis;
            }

            .pop-value {
                display: inline-block;
                width: 45%;
                overflow: hidden;
                white-space: nowrap;
                text-overflow: ellipsis;
            }
        }
    }
}
</style>
