export default {
    // 通用文本
    common: {
        // 操作按钮
        query: '查询',
        reset: '重置',
        export: '导出',
        delete: '删除',
        edit: '编辑',
        detail: '详情',
        add: '添加',
        save: '保存',
        cancel: '取消',
        confirm: '确认',
        submit: '提交',
        close: '关闭',
        back: '返回',
        next: '下一步',
        previous: '上一步',
        refresh: '刷新',
        search: '搜索',
        clear: '清空',
        select: '选择',
        selectAll: '全选',

        // 状态文本
        success: '成功',
        failed: '失败',
        error: '错误',
        warning: '警告',
        info: '信息',
        loading: '加载中',
        running: '运行中',
        stopped: '已停止',
        paused: '已暂停',
        finished: '已完成',
        pending: '待处理',
        processing: '处理中',

        // 通用提示
        noData: '暂无数据',
        pleaseSelect: '请选择',
        pleaseInput: '请输入',
        operationSuccess: '操作成功',
        operationFailed: '操作失败',
        confirmDelete: '确认删除吗？',
        deleteSuccess: '删除成功',
        saveSuccess: '保存成功',

        // 时间相关
        today: '今天',
        yesterday: '昨天',
        thisWeek: '本周',
        thisMonth: '本月',
        startTime: '开始时间',
        endTime: '结束时间',
        timeRange: '时间范围',

        // 表单
        required: '必填',
        optional: '可选',
        placeholder: {
            input: '请输入',
            select: '请选择',
            search: '请输入搜索内容'
        },
        // 验证相关
        validation: {
            required: '此字段为必填项',
            email: '请输入有效的邮箱地址',
            phone: '请输入有效的手机号码',
            url: '请输入有效的URL',
            number: '请输入有效的数字',
            integer: '请输入整数',
            positive: '请输入正数',
            range: '请输入{min}到{max}之间的值',
            minLength: '最少输入{min}个字符',
            maxLength: '最多输入{max}个字符'
        },

        // 错误信息
        errorMsg: {
            networkError: '网络错误',
            serverError: '服务器错误',
            timeout: '请求超时',
            unauthorized: '未授权',
            forbidden: '禁止访问',
            notFound: '未找到',
            internalError: '内部错误',
            unknownError: '未知错误'
        }
    },

    // 导航菜单
    menu: {
        home: '首页',

        // 子菜单
        productService: {
            applicationLatency: '应用时延度量',
            linkModelConfig: '链路模型配置',
            modelConfig: '模型配置',
            modelPreview: '模型预览',
            linkLogConfig: '链路日志配置',
            logSourceConfig: '链路日志配置'
        }
    },

    // 页面标题
    title: {
        sqlCores: 'SQL核心查询',
        managementQuery: '管理查询',
        dataObservation: '数据观测'
    },

    // 配置相关
    config: {
        basic: '基础配置',
        advanced: '高级配置',
        network: '网络配置',
        database: '数据库配置',
        security: '安全配置',
        performance: '性能配置'
    },

    // 业务特定文本------------ TODO: 观测类页面
    business: {
        productInstance: '产品实例',
        coreInstance: '核心实例',
        dataSource: '数据源',
        cluster: '集群',
        shard: '分片'
    },

    // 管理功能---------------举例
    management: {
        // 文本类文案
        label: {
            pluginName: '插件名称',
            functionName: '管理功能名称',
            chineseName: '中文名称',
            remark: '备注',
            description: '说明',
            version: '版本',
            updateTime: '更新时间',
            provider: '提供者，默认hs-ldp',
            accessUrl: '访问URL'
        },

        // 表格类文案（可选、business 代替）
        table: {},

        // 消息提示
        message: {
            editorContentTooLarge: '编辑器内容超过1M不支持暂存!',
            noManagementFunction: '未查询到管理功能',
            showChineseName: '显示"中文译名"',
            confirmSaveContent: '您确认保存当前编辑器中的内容吗？',
            fileNameRequired: '文件名不得为空',
            enterFileName: '请输入文件名',
            supportDragSql: '支持拖拽导入SQL文件'
        },

        // 表单验证
        formValidation: {
            onlyFloat: '只能输入浮点数格式',
            onlyNonNegativeFloat: '只能输入非负浮点数格式',
            onlyNonPositiveFloat: '只能输入非正浮点数格式',
            invalidEmail: '邮件地址不正确',
            onlyColor: '只能输入颜色格式',
            onlyUrl: '只能输入url格式',
            onlyChinese: '只能输入中文格式'
        },

        // 验证状态
        verifyStatus: {
            notStarted: '未启动',
            running: '执行中',
            finished: '已完成',
            paused: '已暂停',
            stopped: '已终止',
            failed: '失败',
            consistent: '一致',
            inconsistent: '不一致'
        }

    }
};
