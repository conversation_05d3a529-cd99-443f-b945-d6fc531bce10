<template>
  <div>
    <h-msg-box-safe
      v-model="modalData.status"
      :escClose="true"
      :mask-closable="false"
      title="查看密钥"
      width="500"
      class="wrap-msgbox1"
    >
      <a-tips tipText="此密钥用于配置三方工具管理MDB数据。"></a-tips>
      <h-form ref="formValidate" :model="formValidate" :label-width="60" labelPosition="left">
        <h-form-item label="用户ID：" prop="id">
          <span
            class="modal-text"
            :title="showId ? (formValidate.id || '-') : (formValidate.id ? '********' : '-')"
          >
            {{ showId ? (formValidate.id || '-') : (formValidate.id ? '********' : '-') }}
          </span>
          <h-icon
            :name="showId ? 'browse_fill' : 'eye-disabled'"
            style="cursor: pointer; margin-left: 8px;"
            color="var(--link-color)"
            @on-click="showId = !showId"
          />
          <a-button
            v-if="formValidate.id && showId"
            class="btn-cp"
            type="text"
            :data-clipboard-text="formValidate.id"
            @click="onCopied"
            >复制</a-button>
        </h-form-item>
        <h-form-item label="密钥：" prop="password">
          <span
            class="modal-text"
            :title="showPassword ? (formValidate.password || '-') : (formValidate.password ? '********' : '-')"
          >
            {{ showPassword ? (formValidate.password || '-') : (formValidate.password ? '********' : '-') }}
          </span>
          <h-icon
            :name="showPassword ? 'browse_fill' : 'eye-disabled'"
            style="cursor: pointer; margin-left: 8px; color: var(--link-color);"
            @on-click="showPassword = !showPassword"
          />
          <a-button
            v-if="formValidate.password && showPassword"
            class="btn-cp"
            type="text"
            :data-clipboard-text="formValidate.password"
            @click="onCopied"
            >复制</a-button>
        </h-form-item>
      </h-form>

      <template v-slot:footer>
        <a-button @click="modalData.status = false">关闭</a-button>
      </template>
    </h-msg-box-safe>
  </div>
</template>

<script>
import aTips from '@/components/common/apmTips/aTips';
import aButton from '@/components/common/button/aButton';
import Clipboard from 'clipboard';
export default {
    props: {
        modalInfo: {
            type: Object,
            default: null
        }
    },
    data() {
        return {
            modalData: this.modalInfo,
            loading: false,
            formValidate: {
                id: this.modalInfo.id,
                password: this.modalInfo.password
            },
            showId: false,
            showPassword: false
        };
    },
    mounted() {
    },
    methods: {
        onCopied(cl) {
            const clipBoard = new Clipboard('.btn-cp');
            clipBoard.on('success', (e) => {
                clipBoard.destroy();
                this.$hMessage.success('复制成功');
            });
            clipBoard.on('error', (e) => {
                this.$hMessage.error('该浏览器不支持复制');
                clipBoard.destroy();
            });
        }
    },
    components: { aButton, aTips }
};
</script>
<style lang="less" scoped>
.wrap-msgbox1 {
    .tips-content {
        margin-bottom: 15px;
    }

    .h-btn-text.btn-cp {
        color: var(--link-color);

        &:hover {
            text-decoration: underline;
        }
    }

    /deep/.h-form-item-content {
        display: flex;
    }

    .modal-text {
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
        cursor: pointer;
    }

    /deep/ .h-modal-body {
        padding: 16px 32px;
    }

    /deep/.h-form-item {
        margin-bottom: 15px;
    }
}
</style>
