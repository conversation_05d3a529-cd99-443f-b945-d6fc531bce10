<template>
    <div class="mc-box">
        <normal-table
            ref="tableRef"
            formTitle="筛选条件"
            tableTitle="查询结果"
            showTitle
            :hasSetTableColumns="false"
            :formItems="formItems"
            :columns="columns"
            :tableData="tableData"
            :total="tableObj.totalCount"
            :loading="loading"
            @handleSelectChange="handleSelectChange"
            @query="handleQuery"
        >
            <template v-slot:btns>
                <p v-show="tableObj.dataSource" class="sub-title">
                    数据来源：{{tableObj.dataSource || '-'}} &nbsp;&nbsp;
                    消息ID范围：{{tableObj.beginMsgId || '-'}} ~ {{tableObj.endMsgId || '-'}}
                </p>
            </template>
        </normal-table>

        <!-- 查看消息内容 -->
        <mc-msg-content-modal v-if="msgInfo.status" :modalInfo="msgInfo"></mc-msg-content-modal>
    </div>
</template>

<script>
import normalTable from '@/components/common/bestTable/normalTable/normalTable';
import mcMsgContentModal from './modal/mcMsgContentModal.vue';
import { queryTypeEnum, handleMutiIpFunc, generateApiParam } from './constant';
import { getMsgBackward, getManagerProxy } from '@/api/mcApi';
export default {
    name: 'BacktrackQuery',
    components: { normalTable, mcMsgContentModal },
    props: {
        nodeData: {
            type: Object,
            default: () => {}
        },
        productId: {
            type: String,
            default: ''
        }
    },
    data() {
        return {
            topicList: [],
            partitionList: [],

            loading: false,
            formItems: [
                {
                    type: 'simpleSelect',
                    key: 'topicName',
                    label: '主题名',
                    required: true,
                    filterable: true,
                    filterMethod: this.topicFilterMethod,
                    clearable: true,
                    placeholder: '请选择主题名',
                    optionData: []
                },
                {
                    type: 'select',
                    key: 'partitionNo',
                    label: '分区',
                    required: true,
                    placeholder: '请选择分区',
                    options: []
                },
                {
                    type: 'customMode',
                    key: 'queryType',
                    label: '查询方式',
                    inputkey: 'count',
                    beginInputkey: 'begin',
                    endInputkey: 'end',
                    singleInputValidRules: [
                        { test: /^(?:[1-9]\d{0,2}|1000)$/, message: '请输入1~1000的正整数', trigger: 'blur' }
                    ],
                    required: true,
                    options: [
                        {
                            value: 'last:1:input',
                            label: '最近'
                        },
                        {
                            value: 'old:1:input',
                            label: '最早'
                        },
                        {
                            value: 'range:2:input',
                            label: '消息ID范围'
                        }
                    ]
                }
            ],
            columns: [
                {
                    title: '主题名',
                    key: 'topicName',
                    ellipsis: true
                },
                {
                    title: '分区号',
                    key: 'partitionNo',
                    ellipsis: true
                },
                {
                    title: '消息ID',
                    key: 'msgId',
                    ellipsis: true
                },
                {
                    title: '生产者',
                    key: 'producerName',
                    ellipsis: true,
                    render: (h, params) => {
                        const producerName = JSON.stringify(params.row.producerName).split('"')[1];
                        return h('span', {
                            attrs: {
                                title: producerName
                            }
                        }, producerName);
                    }
                },
                {
                    title: '发布序号',
                    key: 'produceMsgId',
                    ellipsis: true
                },
                {
                    title: '发布时间',
                    key: 'publishTime',
                    ellipsis: true
                },
                {
                    title: '消息大小',
                    key: 'msgLen',
                    ellipsis: true
                },
                {
                    title: '过滤条件',
                    key: 'filter',
                    ellipsis: true
                },
                {
                    title: '消息内容',
                    key: 'msg',
                    width: 80,
                    render: (h, params) => {
                        return h('div', [
                            h(
                                'Button',
                                {
                                    props: {
                                        type: 'text',
                                        size: 'small'
                                    },
                                    on: {
                                        click: () => {
                                            this.showTopicMsgInfo(params.row);
                                        }
                                    }
                                },
                                '查看'
                            )
                        ]);
                    }
                }
            ],
            tableData: [],
            tableObj: {},
            syncInfo: {
                status: false
            },
            configureInfo: {
                status: false
            },
            msgInfo: {
                status: false
            }
        };
    },
    methods: {
        async initData() {
            await this.getMcTopic();

            // this.$nextTick(() => {
            //     this.$refs['tableRef'].$_handleResetPageDataAndQuery();
            // });
        },
        // 获取全部主题分区
        async getMcTopic() {
            let topicList = [];
            this.partitionList = [];
            const funcNameList = ['GetTopicInfo'];
            const param = generateApiParam(this?.nodeData?.productInstances, funcNameList);
            try {
                const res = await getManagerProxy(JSON.stringify(param));
                if (res.code === '200') {
                    topicList = handleMutiIpFunc(res?.data) || [];
                }
            } catch (err) {
                this.$emit('clear');
            }
            this.topicList = topicList;
            this.formItems[0].optionData = topicList.map(item => {
                return {
                    value: item?.Topic,
                    label: item?.Topic
                };
            }).sort((a, b) => {
                return a.label.localeCompare(b.label, 'en', { sensitivity: 'base' });
            });
        },
        // 下拉框搜索逻辑 - 从头开始匹配
        topicFilterMethod(val, obj) {
            if (obj?.label.toLowerCase().startsWith(val.toLowerCase())) return true;
            return false;
        },
        // 切换主题下拉框，设置分区默认值
        handleSelectChange(key, val) {
            if (key === 'topicName') {
                // 获取选中主题分区号
                const partitions = this.topicList.find(o => o.Topic === val)?.Partitions || [];
                // 使用 Array.from 将索引转换成数组
                const partitionList = Array.from({ length: partitions.length }, (_, idx) => idx);
                this.formItems[1].options = partitionList.map(item => ({
                    value: item,
                    label: item
                }));
                this.$refs['tableRef'].handleSetItemVal('partitionNo', partitionList?.[0]);
            }
        },
        // 查询
        async handleQuery(val){
            // 输入框校验不通过
            if (!val.aInputValid) return;

            let tableData = [];
            let tableObj = {};
            try {
                this.loading = true;
                const param = {
                    appClusterId: this.nodeData?.id,
                    queryType: queryTypeEnum?.[val.queryType],
                    productId: this.productId,
                    topicName: val.topicName,
                    partitionNo: val.partitionNo,
                    beginId: val.begin,
                    endId: val.end,
                    count: val.count,
                    page: val.page,
                    pageSize: val.pageSize
                };
                const res = await getMsgBackward(param);
                if (res.code === '200') {
                    tableData = res?.data?.list || [];
                    tableObj = res?.data || {};
                } else if (res.code.length === 8){
                    this.$hMessage.error(res.message);
                }
            } finally {
                this.tableData = tableData;
                this.tableObj = tableObj;
                this.loading = false;
            }
        },
        // 查看主题详情
        showTopicMsgInfo(row) {
            this.msgInfo.params = {
                ...row,
                productId: this.productId,
                appClusterId: this.nodeData?.id
            };
            this.msgInfo.status = true;
        }
    }
};
</script>

<style lang="less" scoped>
.mc-box {
    height: 100%;

    .sub-title {
        padding: 0 20px;
        color: var(--font-color);
    }
}
</style>
