<!--
 * @Description:
 * @Author: <PERSON><PERSON>
 * @Date: 2023-02-24 15:45:31
 * @LastEditTime: 2023-04-12 10:09:52
 * @LastEditors: <PERSON><PERSON>
-->
<template>
  <router-view v-if="apmDirDesc && apmDirDesc.appTypeDictDesc" :key="$route.fullPath"></router-view>
</template>

<script>
import { mapState, mapActions } from 'vuex';
export default {
    computed: {
        ...mapState({
            apmDirDesc: state => {
                return state.apmDirDesc || {};
            }
        })
    },
    mounted() {
        this.getApmDirDesc();
        this.$nextTick(() => {
            document.documentElement.style.fontSize = document.documentElement.clientWidth / 200 + 'px';
        });
    },
    methods: {
        jump(path) {
            this.$hCore.navigate(path);
        },
        ...mapActions({ getApmDirDesc: 'getApmDirDesc' })
    }
};
</script>

<style lang="less" scoped>
.header {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 60px;
}

.item {
    cursor: pointer;
    padding: 0 5px;
    text-decoration: underline;
    font-size: 12px;

    &.active {
        color: #047cf3;
    }
}
</style>
