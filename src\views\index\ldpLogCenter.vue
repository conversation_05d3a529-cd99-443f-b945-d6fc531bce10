<template>
    <div class="main" >
        <div class="title">
            <a-title title="回库错误重试运维">
                <slot>
                    <div class="title-slots">
                        <div class="title-slots-date">日期：{{ currentDate }}</div>
                        <div
                            v-show="productList.length > 1"
                            class="title-slots-separator">
                        </div>
                        <h-select
                            v-show="productList.length > 1"
                            v-model="productInstNo"
                            placeholder="请选择"
                            placement="bottom"
                            :positionFixed="true"
                            :clearable="false"
                            class="title-slots-select"
                            @on-change="checkProduct">
                            <h-option
                                v-for="item in productList"
                                :key="item.id"
                                :value="item.productInstNo || ''">
                                {{ item.productName }}
                            </h-option>
                        </h-select>
                    </div>
                </slot>
            </a-title>
        </div>

        <h-tabs v-model="tabName" class="product-box" :animated="false" @on-click="tabClick(tabName)">
            <h-tab-pane v-for="item in editableTabs" :key="item.name" :label="item.describe" :name="item.name">
                <component
                    :is="item.name"
                    :ref="item.name"
                    :productInstNo="productInstNo"
                    :timeout="timeoutNum"
                    @jump-tab="jumpTab">
                </component>
            </h-tab-pane>
            <div ref="operation" slot="extra" class="tabs-operation">
                <div class="tabs-operation-setting" @click="openSetting">
                    <h-icon name="setup" />
                </div>
            </div>
        </h-tabs>

        <!-- 设置 -->
        <h-drawer
            v-model="drawerVisible"
            width="320"
            title="配置"
            class="drawer">
            <h-row>
                日志查询超时时间：
                <h-input
                    v-model="timeout"
                    type="int"
                    specialFilter
                    :specialLength="10"
                    :specialDecimal="0"
                    style="width: 100px;"
                    size="small">
                    <span slot="append">秒</span>
                </h-input>
            </h-row>
        </h-drawer>
    </div>
</template>

<script>
import _ from 'lodash';
import { formatDate } from '@/utils/utils';
import { mapState, mapActions } from 'vuex';
import aTitle from '@/components/common/title/aTitle';
import ldpTodbErrorLog from '@/components/ldpLogCenter/ldpTodbErrorLog.vue';
import ldpTodbErrorRetry from '@/components/ldpLogCenter/ldpTodbErrorRetry.vue';
export default {
    name: 'LdpLogCenter',
    components: {
        aTitle,
        ldpTodbErrorLog,
        ldpTodbErrorRetry
    },
    data() {
        return {
            currentDate: formatDate(new Date()),
            productInstNo: '',
            tabName: 'ldpTodbErrorRetry',
            editableTabs: [
                {
                    name: 'ldpTodbErrorRetry',
                    describe: '回库错误重试',
                    timerSwitch: false,
                    timerInterval: 10
                },
                {
                    name: 'ldpTodbErrorLog',
                    describe: '回库错误日志',
                    timerSwitch: false,
                    timerInterval: 10
                }
            ],
            drawerVisible: false,
            timeout: 3
        };
    },
    async mounted() {
        await this.init();
    },
    beforeDestroy() {
    },
    computed: {
        ...mapState({
            productList: (state) => {
                return state.product.productListLight || [];
            }
        }),
        timeoutNum() {
            if (this.timeout === '') {
                return;
            }
            const parsedValue = parseInt(this.timeout, 10);
            return  isNaN(parsedValue) ? '' : parsedValue;
        }
    },
    methods: {
        async init() {
            try {
                await this.getProductList({ filter: 'excludeLdpApm' });
                const productInstNo = localStorage.getItem('productInstNo');
                this.productInstNo =
          _.find(this.productList, ['productInstNo', productInstNo])
              ?.productInstNo || this.productList?.[0]?.productInstNo;
            } catch (e) {
                console.error(e);
            }
        },
        ...mapActions({ getProductList: 'product/getProductListLight' }),
        // 切换产品
        checkProduct(item) {
            localStorage.setItem('productInstNo', item);
            this.$nextTick(() => {
                this.tabClick(this.tabName);
            });
        },
        tabClick(name, params){
            if (name !== 'ldpTodbErrorRetry'){
                this.$refs?.['ldpTodbErrorRetry']?.[0] && this.$refs['ldpTodbErrorRetry'][0].clearPolling();
            }
            this.$refs?.[name]?.[0] && this.$refs[name][0].initData(params);
        },
        // 跳转
        jumpTab(params){
            this.tabName = 'ldpTodbErrorLog';
            this.tabClick('ldpTodbErrorLog', params);
        },
        /**
         * 打开设置
         */
        openSetting() {
            this.drawerVisible = true;
        }
    }
};
</script>
<style lang="less" scoped>
@import url("@/assets/css/input.less");
@import url("@/assets/css/tab.less");

.title-slots {
    float: right;
    margin-right: 4px;
    display: flex; /* 使用flexbox布局 */
    align-items: center; /* 子元素垂直居中对齐 */

    &-date {
        font-size: 12px;
    }

    &-separator {
        display: inline-block;
        width: 1px;
        height: 26px;
        margin: 0 8px;
        background: #474e6f;
    }

    &-select {
        display: inline-block;
        width: auto;
        min-width: 200px;
    }
}

.product-box {
    /deep/ .h-tabs-bar {
        margin-bottom: 10px;
    }

    /deep/ .h-tabs-nav-wrap {
        float: none !important;
    }

    /deep/ .h-tabs-nav-right {
        position: absolute;
        right: 0;
        top: 5px;
        padding-right: 5px;
    }

    /deep/ .h-tabs-content-wrap {
        height: calc(100% - 48px);
    }

    .tabs-operation {
        display: flex;
        align-items: center;
        flex-wrap: nowrap;

        &-setting {
            background: #262d43;
            border: 1px solid #485565;
            border-radius: 4px;
            cursor: pointer;

            .h-icon {
                color: #fff;
                width: 32px;
                height: 32px;
                display: flex;
                align-items: center;
                justify-content: center;
            }
        }
    }
}

.drawer {
    /deep/ .h-input-group-append {
        border: var(--border);
        color: var(--font-color);
    }
}

</style>
