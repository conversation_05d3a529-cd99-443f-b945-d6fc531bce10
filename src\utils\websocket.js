/*
 * @Description: WebSocket 链接初始化配置
 * @Author: <PERSON><PERSON>
 * @Date: 2023-01-11 16:50:36
 * @LastEditTime: 2023-03-20 19:05:56
 * @LastEditors: <PERSON><PERSON>
 */
import { store } from 'hui-core';
import { v4 as uuidv4 } from 'uuid';
import Notice from 'h_ui/dist/lib/Notice';

let ws, tt, lockReconnect = false;
const clientId = uuidv4();

const ws_init = () => {
    ws.onclose = () => {
        console.log('ws连接关闭');
        ws_reconnect(window.LOCAL_CONFIG.WS_URL + '?clientSessionId=' + clientId);
    };
    ws.onerror = (err) => {
        console.log(err);
        ws_reconnect(window.LOCAL_CONFIG.WS_URL + '?clientSessionId=' + clientId);
    };
    ws.onmessage = (event) => {
        const redata = JSON.parse(event.data);
        if (redata.module === 'dataManagement') {
            store.commit('socket/setDataManagement', redata);
        }
    };
    ws.onopen = () => {
        console.log('连接成功');
    };
};

const ws_reconnect = (url) => {
    if (lockReconnect) return;
    lockReconnect = true;
    tt && clearTimeout(tt);
    tt = setTimeout(() => {
        ws_connect(url);
        lockReconnect = false;
    }, 5000);
};

export const ws_connect = (url = window.LOCAL_CONFIG.WS_URL + '?clientSessionId=' + clientId) => {
    try {
        ws = new WebSocket(url);
        ws_init();
    } catch (e) {
        console.log(e);
    }
};

export const ws_close = () => {
    ws && ws.close();
};

export const ws_send = (action) => {
    if (ws) {
        ws.send(JSON.stringify(action));
        return true;
    }
    Notice.warning({
        title: 'WS异常',
        desc: 'ws连接异常！'
    });
    return false;
};
