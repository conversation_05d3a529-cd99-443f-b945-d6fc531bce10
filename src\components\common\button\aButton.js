/*
 * @Description: apm-button
 * @Author: <PERSON><PERSON>
 * @Date: 2022-11-23 16:34:54
 * @LastEditTime: 2023-06-29 10:10:05
 * @LastEditors: <PERSON>ale Ying
 */
import './button.less';
export default {
    name: 'apm-button',
    props: {
        disabled: {
            type: Boolean,
            default: false
        },
        icon: {
            type: String,
            default: ''
        },
        loading: {
            type: Boolean,
            default: false
        },
        long: {
            type: Boolean,
            default: false
        },
        nativeType: {
            type: String,
            default: 'button'   // button, submit, reset
        },
        size: {
            type: String,
            default: undefined    // large, small
        },
        shape: {
            type: String,
            default: undefined    // circle 或者 不设置
        },
        title: {
            type: String,
            default: ''
        },
        type: {
            type: String,
            default: undefined // primary、ghost、dashed、text、info、success、warning、error、transparent、dark 、danger
        },
        loadingTime: {
            type: String,
            default: ''
        }
    },
    data() {
        return {
            timer: null,
            init: {
                status: false,
                time: '',
                count: this.loadingTime
            }
        };
    },
    methods: {
        click(event){
            this.timer = null;
            this.loadingTime && this.runTime();
            this.$emit('click');
        },
        runTime() {
            this.init.status = true;
            this.init.time = this.loadingTime;
            this.timer = setInterval(() => {
                this.init.time = --this.init.count;
                if (this.init.time === 0) {
                    this.init = {
                        status: false,
                        time: '',
                        count: this.loadingTime
                    };
                    clearInterval(this.timer);
                }
            }, 1000);
        }
    },
    render() {
        return <h-button
            class={[
                this.type === 'dark' && 'h-btn-dark'
            ]}
            type={this.type === 'dark' ? undefined : this.type}
            disabled={ this.loadingTime ? this.init.status : this.disabled}
            icon={this.icon}
            loading={this.loading}
            long={this.long}
            nativeType={this.nativeType}
            size={this.size}
            shape={this.shape}
            title={this.title}
            onclick={() => this.click()}
        >
            {this.$slots.default}
            {
                this.init.time && <span> {'(' + this.init.time + 's)' }</span>
            }
        </h-button>;
    }
};
