/*
 * @Description: 内存表数据展开
 * @Author: <PERSON><PERSON>
 * @Date: 2023-05-16 14:42:37
 * @LastEditTime: 2023-06-05 15:36:34
 * @LastEditors: <PERSON><PERSON>
 */
import _ from 'lodash';
import './memoryTabData.less';
import { matchValidate } from '@/utils/validate';
import aInput from '@/components/common/input/aInput';
export default {
    name: 'memoryTabData',
    props: {
        memoryKey: {
            type: String,
            default: ''
        },
        initStructList: {
            type: Array,
            default: () => {
                return [];
            }
        },
        memoryData: {
            type: Object,
            default: () => { return {}; }
        },
        memoryStruct: {
            type: Array,
            default: () => { return []; }
        },
        parentStructList: {
            type: Array,
            default: () => { return []; }
        },
        isEdit: {
            type: Boolean,
            default: true
        }
    },
    data () {
        return {
            editData: {},
            structList: [],
            memoryList: this.memoryStruct
        };
    },
    mounted() {
        this.handelSturct({ ev: '', key: this.memoryKey, idx: '', arrIndex: -1, value: JSON.stringify(this.memoryData) });
        this.initStructList.forEach((ele, index) => {
            let param = this.memoryData;
            const list = ele.key.split('.');
            list.shift();
            list.forEach(item => {
                param = param[item];
            });
            const key = list.pop();
            const arrIndex = index === 0 ? -1 : this.initStructList[index - 1].index;
            this.handelSturct({ ev: '', key, idx: index, arrIndex, value: JSON.stringify(param) });
        });
    },
    methods: {
        // 生成父结构
        generateParentList(data) {
            return <li class="li-parent">
                <section class="struct-name">
                    <span>{data.LevelTableName}</span>
                    <h-icon name="t-b-organizational" color="#999"></h-icon>
                </section>
                <ul class="struct-content scrollbar">
                    {
                        Object.keys(data).map(item => {
                            return <li class="li-parent-item">
                                <span class="struct-key">{ item }</span>
                                <span class="struct-value">{ this.isVisibleValue(data[item]) }</span>
                            </li>;
                        })
                    }
                </ul>
            </li>;
        },
        // 生成每一列数据
        generateBoxListItem(param, data, idx) {
            return <ul class="struct-content scrollbar">
                {
                    param && Object.keys(param).map(item => {
                        const structType = data.structType;
                        const showInput = this.isShowInput(structType, item) && this.isEdit;
                        const key = data.index > -1 ? this.structList[idx]?.key + '.' + data.index + '.' + item : this.structList[idx]?.key + '.' + item;
                        // 判断修改值是否存在 存在读取缓存，不存在新建一个
                        if (showInput && !this.editData[key]) {
                            this.$set(this.editData, key, { oldVal: param[item], newVal: param[item], validate: true, type: structType[item].type });
                        }
                        const structValue = this.isVisibleValue(param[item]);
                        return <li
                            class={[showInput ? 'li-edit' : '', this.isShowHighLight(key) ? 'li-active' : '']}
                            key={key}
                            data-value={this.isVisible(param[item]) ? '' : JSON.stringify(param[item])}
                            onClick={event => { return this.handelSturct({ ev: event, key: item, idx, arrIndex: data.index }); }}>
                            <span class="struct-key" title={item}>{ item }</span>
                            <span class="struct-value"
                                title={structValue}>
                                {structValue}
                            </span>
                            {
                                showInput ? <a-input
                                    class="struct-value-input"
                                    validRules={matchValidate(structType[item].type, structType[item].size, false)}
                                    prop='input'
                                    algin='right'
                                    key={key}
                                    noTrim={true}
                                    positionFixed={true}
                                    placement='bottom-end'
                                    value={this.editData[key].newVal}
                                    on-on-blur={ (event, err) => { this.handleValueEdit(event, key, err); }}
                                    placeholder="输入修改值" /> : ''
                            }
                        </li>;
                    })
                }
            </ul>;
        },
        // 生成每一个页
        generateBoxList(data, idx) {
            // 判断是数组还是其他
            const isType = Array.isArray(data.value) && !this.isVisible(data.value[0]);
            const param = isType ? data.value[data.index] : data.value;
            const dataKey = data.key.split('.').pop();
            return <li>
                <section class="struct-name">
                    <span title={dataKey}>{dataKey}</span>
                    {
                        isType ? <span style="text-align: right; cursor: pointer;">
                            <h-icon style="position: relative; top: 2px;" name="return" on-on-click={ () => { data.index = this.handleArrayIndex(data, idx, 'prev'); data.page = data.index + 1;  }}></h-icon>
                            <h-input class="input-array"
                                v-model={data.page}
                                type="int"
                                on-on-blur={(event) => { data.page = this.hanleBlurIndex(event.target._value, data, idx); data.index = data.page - 1; }} />
                        / { data.value.length } <h-icon style="position: relative; top: 2px;" name="enter" on-on-click={() => { data.index = this.handleArrayIndex(data, idx, 'next'); data.page = data.index + 1; }}></h-icon></span> : ''
                    }
                </section>
                {
                    this.generateBoxListItem(param, data, idx)
                }
            </li>;
        },
        hanleBlurIndex(value, data, idx) {
            this.clearTab(idx);
            if (value >= 1 && value <= data.value.length) return Number(value);
            return 1;
        },
        handleArrayIndex(data, idx, type) {
            this.clearTab(idx);
            if (type === 'prev') {
                data.index -= 1;
            } else if (type === 'next') {
                data.index += 1;
            }
            if (data.index >= 0 && data.index < data.value.length){
                return data.index;
            }
            return 0;
        },
        /**
         * @param {ev} 当前事件节点
         * @param {key} 表名
         * @param {idx} 当前tab Index
         * @param {arrIndex} 数组的Index
         * @param {value} 当前值
         */
        handelSturct(structData) {
            const data = structData.value || structData.ev.currentTarget.dataset.value;
            if (!data) return;
            let param = {};
            if (structData.idx !== '') {
                this.structList.splice(structData.idx + 1, this.structList.length - structData.idx);
                const struct = this.structList?.[structData.idx]?.structType[structData.key];
                if (struct?.children) {
                    struct.children.forEach(item => {
                        param[item.name] = item;
                    });
                } else {
                    param = struct;
                }
            } else {
                this.memoryList.forEach(item => {
                    param[item.name] = item;
                });
            }
            // idx 表示第几个tab
            const path = structData.idx !== '' ? this.recursiveKey(structData.key, structData.arrIndex) : structData.key;
            const result = JSON.parse(data);
            this.$set(this.structList, this.structList.length, {
                key: path,
                value: result,
                structType: param,
                index: Array.isArray(result) ? 0 : -1,
                page: 1 });
            this.$emit('struct-number', this.parentStructList.length + this.structList.length);
        },
        handleValueEdit(event, key, err) {
            if (err) {
                this.editData[key].validate = false;
            } else {
                this.editData[key].validate = true;
            }
            this.editData[key].newVal = event.target._value;
        },
        callBackEditData() {
            return this.editData;
        },
        // 清楚展开列
        clearTab(idx) {
            this.structList.splice(idx + 1, this.structList.length - idx);
        },
        isVisible(val) {
            return ['undefined', 'boolean', 'number', 'string'].indexOf(typeof (val)) > -1;
        },
        isVisibleValue(val) {
            return ['undefined', 'boolean', 'number', 'string'].indexOf(typeof (val)) > -1
                ? val : Array.isArray(val) ? `array[${val.length}]` : 'struct >';
        },
        // 是否需要修改
        isShowInput(item, key) {
            return Boolean(item[key]?.readOnly === false);
        },
        // 是否需要高亮
        isShowHighLight(key) {
            const list = _.map(this.structList, 'key');
            return list.indexOf(key) > -1;
        },
        // 生成path链路
        recursiveKey(key, arrIndex) {
            let list = [];
            const len = this.structList.length;
            if (len) list = this.structList[len - 1].key.split('.');
            // 判断是不是数组点进来的
            if (arrIndex >= 0) list.push(arrIndex);
            list.push(key);
            return list.join('.');
        },
        // 返回初始化数据列表
        getInitSturctList() {
            const list = [];
            this.structList.forEach((ele, index) => {
                // 过滤掉第一个
                if (index) {
                    list.push({
                        index: ele.index > -1 ? 0 : ele.index,
                        key: ele.key
                    });
                }
            });
            return list;
        }
    },
    watch: {
        isEdit() {
            this.editData = {};
        }
    },
    render() {
        return <ul class="memory-tab-box">
            {
                // TODO 这里要确定下parent的数据结构
                this.parentStructList.map(item => {
                    return this.generateParentList(item);
                })
            }
            {
                this.structList.map((item, index) => {
                    return this.generateBoxList(item, index);
                })
            }
        </ul>;
    },
    components: {
        aInput
    }
};
