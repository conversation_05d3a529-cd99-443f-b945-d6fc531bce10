<template>
    <div>
        <h-msg-box-safe
            v-model="modalData.status"
            :mask-closable="false"
            :escClose="true"
            title="管理功能导出"
            width="75"
            height="400"
            :allowCopy="true"
            @on-open="handleOpen"
        >
            <a-tips
                class="management-tips"
                tipText='根据需要，选择想要导出的管理功能。'
            ></a-tips>

            <h-table
                :columns="columns"
                :data="tabelList"
                @on-selection-change="handleSelectionChange"></h-table>

            <template v-slot:footer>
                <a-button @click="modalInfo.status = false">取消</a-button>
                <a-button
                    type="primary"
                    :loading="loading"
                    :disabled="selectionList.length === 0"
                    @click="submitConfig">
                    导出{{ selectionList.length ? `(${selectionList.length})` : '' }}
                </a-button>
            </template>
        </h-msg-box-safe>
    </div>
</template>

<script>
import aTips from '@/components/common/apmTips/aTips';
import aButton from '@/components/common/button/aButton';
import { exportJsonToZip } from '@/utils/exportToZip';
export default {
    name: 'ExportDataModal',
    props: {
        modalData: {
            type: Object,
            default: () => {}
        }
    },
    data() {
        return {
            modalInfo: {},
            columns: [
                {
                    type: 'selection',
                    width: 60,
                    align: 'center'
                },
                {
                    title: '管理功能',
                    key: 'name'
                },
                {
                    title: '节点',
                    key: 'appName'
                },
                {
                    title: '插件',
                    key: 'pluginName'
                },
                {
                    title: '入参',
                    key: 'params'
                }
            ],
            loading: false,
            tabelList: [],
            selectionList: []
        };
    },
    methods: {
        // 首次打开
        handleOpen() {
            this.modalInfo = this.modalData;
            this.tabelList = this.modalData.managementList;
        },
        // 表格选项变化
        handleSelectionChange(selection) {
            this.selectionList = selection;
        },
        // 开始导出
        async submitConfig() {
            try {
                this.loading = true;
                const list = [];
                this.selectionList.forEach(ele => {
                    list.push({
                        name: ele.name,
                        jsonData: ele.jsonData
                    });
                });
                await exportJsonToZip({
                    zipName: this.selectionList?.[0]?.appName,
                    fileList: list
                });
            } catch (error) {
                this.$hMessage.error('导出失败');
                console.error(error);
            } finally {
                this.loading = false;
            }
        }
    },
    components: { aTips, aButton }
};
</script>

<style lang="less" scoped>
/deep/ .h-modal-body {
    padding: 0 16px 16px;
}

/deep/ .h-table-wrapper {
    margin: 10px 10px 0;
}

.management-tips {
    width: calc(100% - 20px);
    margin: 10px 10px 0;
}

</style>
