<template>
    <div>
        <h-msg-box-safe v-model="modalData.status" title="同步配置" :closable="true" :mask-closable="false" width="550" maxHeight="350" allowCopy
        @on-close="submitConfig">
            <div class="content-body">
                <div v-if="allStatusSure" class="header-info">
                    <h-icon name="warning_fill" color="var(--warning-color)" size=24></h-icon>
                    <span class="title-info">节点配置同步中，请稍后...</span>
                </div>
                <div v-else class="header-info">
                    <h-icon name="success" color="var(--success-color)" size=24></h-icon>
                    <span class="title-info">节点配置同步完成</span>
                </div>
                <u-table
                    :border="true"
                    :canDrag="true"
                    :showTitle="true"
                    :height="220"
                    :columns="columns"
                    :data="tableData"
                    :span-method="handleSpan"
                    :loading="loading"
                ></u-table>
            </div>
            <template v-slot:footer>
                <div>
                    <a-button type="primary" @click="submitConfig">确认</a-button>
                </div>
            </template>
        </h-msg-box-safe>
    </div>
</template>

<script>
import aButton from '@/components/common/button/aButton';
import { getConfigLocateCompareEdit } from '@/api/locateApi';

const eumnsStatus = {
    0: '等待中',
    1: '执行中',
    2: '已成功',
    3: '失败'
};
export default {
    name: 'ApmMsgBox',
    components: { aButton },
    props: {
        modalInfo: {
            type: Object,
            default: null
        }
    },
    data() {
        return {
            loading: false,
            modalData: this.modalInfo,
            columns: [
                {
                    title: '源节点',
                    key: 'appInstanceName',
                    ellipsis: true
                },
                {
                    title: '目标节点',
                    key: 'compareAppInstanceName',
                    ellipsis: true
                },
                {
                    title: '同步状态',
                    key: 'status',
                    ellipsis: true,
                    render: (h, params) => {
                        const status =  String(params?.row?.status) === '3' ? `${eumnsStatus[params?.row?.status]},{${params?.row?.errmsg}}` : eumnsStatus[params?.row?.status || 0];
                        return h('span', {
                            attrs: {
                                title: status
                            }
                        }, status);
                    }
                }
            ],
            tableData: [],
            mergeArr: [],
            timer: null,
            terminate: false  // 终止请求的标志
        };
    },
    mounted(){
        this.loading = true;
        this.init();
        setTimeout(() => {
            this.loading = false;
        }, 300);
    },
    computed: {
        allStatusSure: function() {
            return this.tableData.filter(o => String(o?.status) === '0' || String(o?.status) === '1')?.length;
        }
    },
    methods: {
        init(){
            this.tableData = [...this.modalData.tableData];
            this.mergeArr = this.getMergeParam(this.tableData, ['appInstanceName']);

            this.$nextTick(async () => {
                await this.startRequests();
            });
        },
        async startRequests() {
            for (let i = 0; i < this.tableData.length; i++) {
                if (this.terminate) {
                    break;
                }
                try {
                    this.tableData[i].status = 1;
                    const res = await getConfigLocateCompareEdit({
                        configSourceType: this.tableData[i]?.configSourceType,
                        id: this.tableData[i]?.compareId,
                        configContext: this.tableData[i]?.configContext
                    }, false);
                    if (res?.code === '200') {
                        this.tableData[i].status = 2;
                        this.tableData[i].errmsg = '';
                    } else {
                        this.tableData[i].status = 3;
                        this.tableData[i].errmsg = res?.message;
                    }
                } catch (e){
                    this.tableData[i].status = 3;
                    this.tableData[i].errmsg = e;
                }
            }
        },
        submitConfig() {
            this.terminate = true;
            this.$emit('refresh');
            this.modalData.status = false;
        },
        // 获取表格合并参数
        getMergeParam(tableData, keyArr) {
            // 拼接判断项
            let keyVal = keyArr.map(item => tableData?.[0]?.[item]).join();
            let count = 1;
            const result = [0];
            for (let i = 1; i < tableData.length; i++) {
                const iKeyVal = keyArr.map(item => tableData?.[i]?.[item]).join();
                if (iKeyVal === keyVal) {
                    count++;
                } else {
                    result.push(count);
                    result.push(i);
                    keyVal = iKeyVal;
                    count = 1;
                }
            }
            result.push(count);
            return result;
        },
        // 表格合并回调
        handleSpan({ row, column, rowIndex, columnIndex }){
            if ([0].includes(columnIndex)) {
                const mergeArr = this.mergeArr || [];
                for (let i = 0; i < mergeArr.length; i += 2) {
                    if (mergeArr[i + 1] > 1) {
                        if (rowIndex === mergeArr[i]) {
                            return [mergeArr[i + 1], 1];
                        } else if (rowIndex < mergeArr[i] + mergeArr[i + 1]) {
                            return [0, 0];
                        }
                    } else if (rowIndex === mergeArr[i]) {
                        return [1, 1];
                    }
                }
            }
        }
    }
};
</script>

<style lang="less" scoped >
.header-info {
    font-weight: 500;
    display: flex;

    & > .h-icon {
        position: relative;
        top: 2px;
    }

    .title-info {
        line-height: 36px;
        font-size: 14px;
        font-weight: 600;
        vertical-align: top;
        padding: 0 5px;
    }
}

/deep/ .h-modal-body {
    padding: 5px 32px;
}

.content-body {
    .text-body {
        font-size: 12px;
        color: #24262b;
        text-align: left;
        line-height: 20px;
        padding-left: 18px;
        position: relative;
    }

    .obj-body {
        color: var(--font-color);
        line-height: 30px;
        width: 100%;

        p {
            display: flex;

            span {
                text-align: left;
                overflow: hidden;
                white-space: nowrap;
                text-overflow: ellipsis;
            }
        }

        .obj-body-text {
            width: 100px;
            text-align: left;
        }
    }

    .obj-tip {
        font-size: 14px;
        font-weight: 600;
        color: var(--error-color);
    }
}
</style>
