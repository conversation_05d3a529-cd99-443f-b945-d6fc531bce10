import './normalTable.less';
import aForm from '@/components/common/form/aFormDashboard';
import aTable from '@/components/common/table/aTable';
import aSimpleTable from '@/components/common/table/aSimpleTable';
import aButton from '@/components/common/button/aButton';
export default {
    name: 'NormalTable',
    props: {
        isSimpleTable: {
            type: Boolean,
            default: false
        },
        formTitle: {
            type: String,
            default: ''
        },
        tableTitle: {
            type: String,
            default: ''
        },
        additionalTitle: {
            type: String,
            default: ''
        },
        loading: {
            type: Boolean,
            default: false
        },
        formItems: {
            type: Array,
            default: []
        },
        formCols: {
            type: Number,
            default: 3
        },
        tableData: {
            type: Array,
            default: []
        },
        columns: {
            type: Array,
            default: []
        },
        hasPage: {
            type: Boolean,
            default: true
        },
        total: {
            type: Number,
            default: 0
        },
        hasSetTableColumns: {
            type: Boolean,
            default: true
        },
        showTitle: {
            type: Boolean,
            default: false
        }
    },
    data() {
        return {
            formStatus: true,
            visible: false,
            styles: {
                height: 'calc(100% - 105px)',
                paddingBottom: '53px'
            },
            checkList: [],
            currentCheckedKeys: [],
            columnData: [],
            tableHeight: 0
        };
    },
    mounted() {
        this.$_init();
        window.addEventListener('resize', this.fetTableHeight);
        this.$nextTick(() => {
            this.fetTableHeight();
        });
    },
    beforeDestroy() {
        window.removeEventListener('resize', this.fetTableHeight);
    },
    methods: {
        resetHeight() {
            return new Promise((resolve, reject) => {
                this.tableHeight = 0;
                resolve();
            });
        },
        // 计算form高度
        fetFormHeight() {
            return this.formStatus ? Math.ceil(this.formItems.length / this.formCols) * 45 + 48 : 48;
        },
        // 设置table高度
        fetTableHeight() {
            this.resetHeight().then(res => {
                const height = this.fetFormHeight();
                this.tableHeight = this.$refs['table-box'].getBoundingClientRect().height - height - (this.hasPage ? 120 : 80);
            });
        },
        $_clearCurrentCheckedKeys(){
            this.currentCheckedKeys = [];
        },
        $_init(){
            this.checkList = [];
            this.columnData = [];
            if (this.currentCheckedKeys?.length){
                this.checkList = [...this.currentCheckedKeys];
                this.columns.forEach(element => {
                    if (this.currentCheckedKeys.includes(element.key)){
                        this.columnData.push(element);
                    }
                });
            } else {
                this.columns.forEach(element => {
                    this.checkList.push(element.key);
                    this.columnData.push(element);
                });
            }
        },
        changeFormStatus(){
            this.formStatus = !this.formStatus;
            this.fetTableHeight();
        },
        setTableColumns() {
            this.visible = true;
        },
        $_handleCheckbox(value) {
            this.currentCheckedKeys = [...value];
            this.columnData = [];
            this.columns.forEach(ele => {
                if (value.includes(ele.key)){
                    this.columnData.push(ele);
                }
            });
            this.columnData.forEach(ele => {
                ele.hiddenCol = !value.includes(ele.key);
            });
        },
        $_handleQuery() {
            const param = this.$refs['forms'].query();
            if (!param) return;
            const pageParam = this.hasPage ? this.$refs['table'].getPageData() : {};
            this.$emit('query', { ...param, ...pageParam });
        },
        $_handleReset() {
            this.$refs['forms'].init();
        },
        handleClickQuery() {
            this.$refs['table'].resetPage();
            this.$_handleQuery();
        },
        $_handleResetPageDataAndQuery() {
            this.$refs['table'].resetPage();
            this.$refs['table'].resetPageSize();
            this.$_handleQuery();
        },
        $_handleClear(){
            const param = this.$refs['forms'].query();
            if (!param) return;
            const pageParam = this.hasPage ? this.$refs['table'].getPageData() : {};
            this.$emit('clear', { ...param, ...pageParam });
        },
        $_tableSelection(selection){
            this.$emit('selection', selection);
        },
        $_echoFormData(param){
            this.$refs['forms'].echoFormData(param);
        },
        // 监听下拉框变化
        handleSelectChange(key, val) {
            this.$emit('handleSelectChange', key, val);
        },
        // 设置某项formitem值
        handleSetItemVal(key, val) {
            this.$refs['forms'].handleSetItemVal(key, val);
        }
    },
    components: { aForm, aTable, aSimpleTable, aButton },
    render() {
        return <div class="best-table">
            <div class="form-box" style={{ height: `${this.fetFormHeight()}px`, overflow: this.formStatus ?  'visible' : 'hidden' }}>
                <a-form ref="forms" formItems={this.formItems} title={this.formTitle} formCols={this.formCols}
                    v-on:handleSelectChange={this.handleSelectChange}>
                    <div class="form-slot-box" slot="btns">
                        <a-button type="primary" onClick={this.handleClickQuery} disabled={this.loading}>查询</a-button>
                        <a-button type="dark" onClick={this.$_handleReset}>重置</a-button>
                        {this.hasSetTableColumns && <a-button type="dark" onClick={this.setTableColumns} disabled={this.loading}>配置表格</a-button>}
                        <a-button type="dark" onClick={this.changeFormStatus}>
                            {this.formStatus ? '收起搜索' : '展开搜索'}
                            <h-icon name={this.formStatus ? 'packup' : 'unfold'}></h-icon></a-button>
                        {this.$slots.default}
                    </div>
                </a-form>
            </div>

            <div ref="table-box" class='table-box'>
                <div class="table-title">
                    <div class="table-title-text">
                        {this.tableTitle}
                        <span class="additional-title-text" title={this.additionalTitle}>{this.additionalTitle}</span>
                    </div>
                    <div class="table-title-right">
                        {this.$slots.btns}
                    </div>
                </div>
                {
                    this.isSimpleTable
                        ? <a-simple-table
                            ref="table"
                            tableData={this.tableData}
                            height={this.tableHeight}
                            columns={this.columnData}
                            hasPage={this.hasPage}
                            total={this.total}
                            canDrag={true}
                            border={true}
                            loading = {this.loading}
                            showTitle={this.showTitle}
                            v-on:query={this.$_handleQuery}
                            v-on:selection={this.$_tableSelection} />
                        : <a-table
                            ref="table"
                            tableData={this.tableData}
                            height={this.tableHeight}
                            columns={this.columnData}
                            hasPage={this.hasPage}
                            total={this.total}
                            canDrag={true}
                            border={true}
                            loading = {this.loading}
                            showTitle={this.showTitle}
                            v-on:query={this.$_handleQuery}
                            v-on:selection={this.$_tableSelection} />
                }
            </div>
            <h-drawer
                v-model={this.visible}
                width="280"
                title="表格配置"
                styles={this.styles}>
                <h-checkbox-group
                    v-model={this.checkList}
                    onInput={this.$_handleCheckbox}
                    vertical>
                    {
                        this.columns.map(item => {
                            return <h-checkbox key={item.key}
                                label={item.key}
                                disabled={item.disabled || (this.checkList.length <= 1 && this.checkList.includes(item.key))} style="display: flex; align-items: center;">
                                <span style="white-space:nowrap;">{item.title}</span>
                            </h-checkbox>;
                        })
                    }
                </h-checkbox-group>
            </h-drawer>
            <style jsx>
                {
                    `    
                        .form-slot-box > .h-btn {
                            margin-right: 10px;
                        }
                    `
                }
            </style>
        </div>;
    }
};
