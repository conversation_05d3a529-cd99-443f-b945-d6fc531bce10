<template>
    <!-- 告警信息列表 -->
    <h-drawer
        v-model="monitorVisable"
        title="告警信息"
        width="480">
        <div
            v-for="(item, index) in monitorEventList"
            :key="index"
            class="monitor-line">
            <span class="monitor-tag">警告</span>
            <span class="monitor-title">{{ item.instanceName }}</span>
            <span class="monitor-time">{{ item.time }}</span>
            <p
                class="monitor-text"
                :title="item.message">{{ item.message }}</p>
        </div>
        <no-data v-if="monitorEventList.length === 0" />
    </h-drawer>
</template>

<script>
import noData from '@/components/common/noData/noData';
import { getMonitorEvents } from '@/api/httpApi';
export default {
    name: 'AlarmDrawer',
    props: {
        productId: {
            type: String,
            default: ''
        }
    },
    components: { noData },
    data() {
        return {
            monitorVisable: false,
            monitorEventList: []
        };
    },
    methods: {
        init(type, id) {
            this.getMonitorEvents(type, id);
        },
        // 获取告警列表
        async getMonitorEvents(type, id) {
            this.monitorVisable = true;
            this.monitorEventList = [];
            const param = {
                productId: this.productId
            };
            if (type === 'service') {
                param.serviceCode = id;
            } else {
                param[type] = id; // 可能是 instanceId 或者 contextId
            }
            const res = await getMonitorEvents({ ...param });
            this.monitorEventList = res?.data || [];
        }
    }

};
</script>

<style lang="less" scoped>
.monitor-line {
    position: relative;
    width: 100%;
    height: 56px;
    margin-bottom: 10px;
    padding: 5px 0;

    &:hover {
        background-color: #1f3759;
    }

    &::before {
        display: inline-block;
        content: "";
        width: 1px;
        height: 46px;
        background: #2d8de5;
    }

    .monitor-tag {
        position: absolute;
        left: 10px;
        color: #fff;
        background-color: #4e3b29;
        padding: 3px 8px;
        border-radius: 4px;
    }

    .monitor-time {
        position: absolute;
        color: #cacfd4;
        right: 4px;
    }

    .monitor-text {
        position: absolute;
        color: #fff;
        width: 430px;
        top: 35px;
        left: 10px;
        white-space: nowrap;         /* 防止换行 */
        overflow: hidden;            /* 隐藏溢出内容 */
        text-overflow: ellipsis;     /* 使用省略号表示溢出内容 */
    }

    .monitor-title {
        position: absolute;
        left: 60px;
        font-size: 14px;
        color: #fff;
        font-weight: bold;
    }
}
</style>
