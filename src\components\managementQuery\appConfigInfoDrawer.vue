<template>
<div>
  <h-drawer
    ref="drawer-box"
    v-model="modalData.status"
    :title="info.functionName"
    width="60"
    @on-close="handleClose"
  >
    <h-tabs value="output">
      <h-tab-pane label="基本信息" name="base">
        <div style="margin: 0 10px;">
          <div class="info-row">
            <span class="info-row-label">功能名称：</span>
            <p class="info-row-content">{{ info.funcName }}</p>
          </div>
          <div class="info-row">
            <span class="info-row-label">功能备注：</span>
            <p class="info-row-content">{{ info.remark }}</p>
          </div>
          <div class="info-row">
            <span class="info-row-label">版本号：</span>
            <p class="info-row-content">{{ info.version }}</p>
          </div>
          <div class="info-row">
            <span class="info-row-label">更新时间：</span>
            <p class="info-row-content">{{ info.updateDate }}</p>
          </div>
          <div class="info-row">
            <span class="info-row-label">提供者：</span>
            <p class="info-row-content">{{ info.provider }}</p>
          </div>
          <div class="info-row">
            <span class="info-row-label">功能说明：</span>
            <p class="info-row-content">{{ info.description }}</p>
          </div>
        </div>
      </h-tab-pane>
      <h-tab-pane label="入参说明" name="input">
           <tree-table
              ref="treeTable"
              showTitle
              :columns="inputColumns"
              :treeData="inputData"
              :height="logTableHeight"
              >
          </tree-table>
      </h-tab-pane>
      <h-tab-pane label="出参说明" name="output">
        <tree-table
              ref="treeTable"
              showTitle
              :columns="outputColumns"
              :treeData="outputData"
              :height="logTableHeight"
          >
          </tree-table>
      </h-tab-pane>
      <h-tab-pane label="案例说明" name="example">
        <div class="title">入参案例</div>
        <div class="json-box">
          <json-viewer class="json-box" :value="inputExample" :expand-depth="3" copyable />
        </div>
        <div class="title">出参案例</div>
        <div class="json-box">
          <json-viewer  class="json-box" :value="outputExample" :expand-depth="3" copyable />
        </div>
      </h-tab-pane>
    </h-tabs>
  </h-drawer>
</div>
</template>

<script>
import JsonViewer from 'vue-json-viewer';
import { transformSchema } from '@/components/managementQuery/schema-parser';
import treeTable from '@/components/common/treeTable/treeTable';

export default {
    name: 'AppConfigInfoModal',
    components: { JsonViewer, treeTable },
    props: {
        modalInfo: {
            type: Object,
            default: () => {}
        }
    },
    data() {
        return {
            modalData: this.modalInfo,
            info: {
                funcName: '',
                remark: '',
                version: '',
                updateDate: '',
                provider: '',
                description: ''
            },
            inputColumns: [
                {
                    title: '入参名称',
                    key: 'paramName',
                    width: 200
                },
                {
                    title: '参数类型',
                    key: 'paramType',
                    width: 120
                },
                {
                    title: '参数说明',
                    key: 'paramDesc'
                }
            ],
            inputData: [],
            outputColumns: [
                {
                    title: '出参名称',
                    key: 'paramName',
                    width: 200
                },
                {
                    title: '参数类型',
                    key: 'paramType',
                    width: 140
                },
                {
                    title: '参数说明',
                    key: 'paramDesc'
                }
            ],
            outputData: [],
            inputExample: {},
            outputExample: {},
            logTableHeight: 0
        };
    },
    mounted() {
        this.handleDrawerOpen();
        window.addEventListener('resize', this.fetTableHeight);
    },
    beforeDestroy() {
        window.removeEventListener('resize', this.fetTableHeight);
    },
    methods: {
        handleDrawerOpen() {
            // 基础数据
            this.info = {
                funcName: this.modalData.details?.funcName || '-',
                functionName: (this.modalData.details?.funcName || '-') + '（' + (this.modalData.details?.funcNameCn || '-') + '）',
                remark: this.modalData.details?.remark || '-',
                version: this.modalData.details?.version || '-',
                updateDate: this.modalData.details?.updateDate || '-',
                provider: this.modalData.details?.provider || '-',
                description: this.modalData.details?.description || '-'
            };

            // 入参说明
            const schema = this.modalData.details?.schema?.request || {};

            // 出参说明
            const responseList = this.modalData.details?.schema?.response || {};

            // 递归解析 responseList 结构为 outputData
            this.inputData = transformSchema(schema);
            this.outputData = transformSchema(responseList);

            // 案例说明
            this.inputExample = this.modalData.details?.example?.request || {};
            this.outputExample = this.modalData.details?.example?.response || {};

            this.fetTableHeight();
        },
        handleClose() {
            this.modalData.status = false;
        },
        fetTableHeight() {
            this.logTableHeight = this.$refs['drawer-box']?.$el?.offsetTop - (window.LOCAL_CONFIG.HEADER_VISIBLE === true ? 240 : 160);;
        }
    }
};
</script>

<style lang="less" scoped>
@import url("@/assets/css/input.less");
@import url("@/assets/css/tab.less");
@import url("@/assets/css/json-view.less");

/deep/.h-drawer-body {
    padding: 0 10px 10px;
}

.info-row {
    display: flex;
    align-items: flex-start;
    margin-bottom: 20px;
    color: var(--font-body-color);
}

.info-row-label {
    font-weight: bold;
    min-width: 90px;
    margin-right: 8px;
}

.info-row-content {
    flex: 1;
    text-align: left;
    word-break: break-all;
    white-space: pre-line;
    margin: 0;
}

/deep/.h-tabs-bar {
    margin-bottom: 10px;
}

/deep/ .h-tabs-content-wrap {
    height: calc(100% - 60px);
}

.title {
    position: relative;
    padding: 0 0 0 20px;
    color: var(--font-body-color);
    font-size: 14px;
    margin-left: 15px;

    &::before {
        content: "";
        display: inline-block;
        position: absolute;
        top: 1px;
        left: 0;
        width: 4px;
        height: 17px;
        background: var(--link-color);
    }
}

.json-box {
    width: auto;
    height: calc(50% - 42px);
}

.json-box .jv-container {
    height: calc(100% - 10px);
}
</style>
