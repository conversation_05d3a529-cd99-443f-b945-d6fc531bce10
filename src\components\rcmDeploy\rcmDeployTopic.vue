<template>
    <div class="topic-box">
        <rcm-normal-title-table ref="table" title="筛选条件" :formItems="formItems" :columns="columns" :loading="loading"
            :tableData="tableData" :total="total" :hasSetTableColumns="false" :showTitle="true" @query="handleQuery" @clear="handleClear"
            @add-tag="handleTag('add')" @del-tag="handleTag('del')" @selection='tableSelection' @create="createOrUpdateTopicModal(false,'add')">

        </rcm-normal-title-table>
        <add-or-update-topic-modal :rcmId="rcmId" :modalInfo="modalInfo" @create-or-update-topic="createOrUpdateTopic"/>
        <create-tag-modal v-if="inputInfo.status" :modalInfo="inputInfo" @add="addTags" @del="delTags" />
    </div>
</template>
<script>
import { mapState } from 'vuex';
import { topicTempDefault } from '@/config/rcmDefaultConfig';
import rcmNormalTitleTable from '@/components/common/bestTable/rcmNormalTitleTable';
import createTagModal from '@/components/rcmDeploy/modal/topic/createTagModal';
import addOrUpdateTopicModal from '@/components/rcmDeploy/modal/topic/addOrUpdateTopicModal.vue';
import { formatDates } from '@/utils/utils';
import { createTopics, queryTopics, deleteTopic, addTopicTags, delTopicTags, queryTopicsTags } from '@/api/rcmApi';
export default {
    name: 'RcmDeployTopic',
    props: {
        rcmId: {
            type: String,
            default: ''
        }
    },
    data() {
        const rangeFunc = (rule, value, callback) => {
            if (value && value <= 0 || value > 65535) {
                return callback(new Error('请输入在1-65535之间的值'));
            }
            callback();
        };
        return {
            loading: false,
            formItems: [
                {
                    type: 'select',
                    label: '模板名称',
                    key: 'ref',
                    options: [{
                        value: 'all',
                        label: '全部'
                    }],
                    value: 'all'
                },
                {
                    type: 'input',
                    key: 'topic',
                    label: '主题名称',
                    value: '',
                    placeholder: '请输入主题名称',
                    clearable: true
                },
                {
                    type: 'input',
                    key: 'partition',
                    label: '分区号',
                    value: '',
                    placeholder: '请输入分区号',
                    clearable: true,
                    rules: [
                        { test: /^[0-9]\d*$/, message: '只支持输入数字' },
                        { test: rangeFunc, trigger: 'blur' }
                    ]
                },
                {
                    type: 'select',
                    key: 'tags',
                    label: '查询标签',
                    options: [],
                    placeholder: '请选择查询标签',
                    multiple: true,
                    value: []
                }
            ],
            modalInfo: {
                status: false
            },
            inputInfo: {
                status: false
            },
            columns: [
                {
                    type: 'selection',
                    width: 60,
                    align: 'center'
                },
                {
                    title: 'ID',
                    key: 'id',
                    width: 100,
                    ellipsis: true
                },
                {
                    title: '主题名',
                    key: 'topic',
                    ellipsis: true
                },
                {
                    title: '分区号',
                    key: 'partition',
                    ellipsis: true
                },
                {
                    title: '通讯地址',
                    key: 'addr',
                    ellipsis: true
                },
                {
                    title: '通讯端口',
                    key: 'port',
                    ellipsis: true
                },
                {
                    title: '引用模板',
                    key: 'ref',
                    ellipsis: true,
                    render: (h, params) => {
                        return h('span', [params.row.ref || '-']);
                    }
                },
                {
                    title: '标签',
                    key: 'tags',
                    ellipsis: true,
                    render: (h, params) => {
                        return h('span', [params.row.tags?.join(',') || '-']);
                    }
                },
                {
                    title: '操作',
                    key: 'action',
                    fixed: 'right',
                    width: 120,
                    render: (h, params) => {
                        return h('div', [
                            h('Button',
                                {
                                    props: {
                                        size: 'small',
                                        type: 'text'
                                    },
                                    on: {
                                        click: () => { this.createOrUpdateTopicModal(params.row, 'update'); }
                                    }
                                },
                                '配置'
                            ),
                            h('Button',
                                {
                                    props: {
                                        size: 'small',
                                        type: 'text'
                                    },
                                    on: {
                                        click: () => {
                                            this.$hMsgBoxSafe.confirm({
                                                title: `删除`,
                                                content: `您确认要删除名为"${params.row.topic}"的主题吗？`,
                                                onOk: async () => {
                                                    this.handleDeleteTopic(params.row.id);
                                                }
                                            });
                                        }
                                    }
                                },
                                '删除'
                            )
                        ]);
                    }
                }
            ],
            tableData: [],
            total: 0,
            selection: [],
            templates: []
        };
    },
    mounted() {
    },
    computed: {
        ...mapState({
            transportTempList: state => {
                return state.rcm.transportTempList || [];
            }
        })
    },
    methods: {
        // 初始化数据
        async initData(){
            this.formItems[3].options = await this.queryTopicsTags();
            const options = [];
            const tempList = [];
            for (const item of this.transportTempList) {
                if (options.indexOf(item.name) === -1){
                    tempList.push({
                        value: item.name,
                        label: item.name
                    });
                    options.push(item.name);
                }
            }
            this.formItems[0].options = [{ label: '全部', value: 'all' }].concat(tempList);

            // 表格重查询数据
            this.$nextTick(() => {
                this.$refs['table'].$_handleResetPageDataAndQuery();
            });
        },
        // 查询
        async handleQuery(val){
            this.selection = [];
            try {
                const param = {
                    rcmId: this.rcmId,
                    ref: val.ref !== 'all' ? val.ref : '',
                    topic: val.topic,
                    partition: val.partition,
                    tags: val.tags?.join(',') || '',
                    page: val.page,
                    pageSize: val.pageSize
                };
                this.loading = true;
                const res = await queryTopics(param);
                this.loading = false;
                if (res.code === '200') {
                    this.tableData = res.data?.list || [];
                    this.total = res.data?.totalCount || 0;
                } else if (res.code.length === 8) {
                    this.$hMessage.error(res.message);
                }
            } catch (err) {
                this.loading = false;
                this.$hMessage.error(err.message);
                this.tableData = [];
                this.total = 0;
            }
        },
        // 创建
        createOrUpdateTopicModal(param, type) {
            this.modalInfo.status = true;
            this.modalInfo.id = this.rcmId;
            this.modalInfo.type = type;
            this.modalInfo.transportTempList = this.transportTempList;
            this.modalInfo.data = param ? {
                ...param
            } : {
                id: '',
                topic: 'topic_' + formatDates(new Date()).replace(/[^\d]/gi, ''),
                partition: 1,
                addr: '239.1.0.1',
                port: 27000,
                ref: '',
                tags: [],
                heartbeatIntervalMilli: topicTempDefault.heartbeatIntervalMilli,
                heartbeatTimeoutMilli: topicTempDefault.heartbeatTimeoutMilli,
                ackIntervalMilli: topicTempDefault.ackIntervalMilli,
                ackTimeoutMilli: topicTempDefault.ackTimeoutMilli,
                useSharedMemory: topicTempDefault.useSharedMemory
            };
        },
        // 创建、配置主题
        async createOrUpdateTopic(val){
            const param = {
                rcmId: this.rcmId,
                ...val
            };
            const res = await createTopics(param);
            if (res.code === '200') {
                // 只要查询下就更新下标签---适配删除操作和增加标签操作
                this.formItems[3].options = await this.queryTopicsTags();
                this.$refs['table'].$_handleQuery();
                this.$hMessage.success('创建/配置主题成功!');
            }
        },
        // 选中行
        tableSelection(selection){
            this.selection = selection;
        },
        // 删除行
        async handleDeleteTopic(id){
            const ids = [id.toString()];
            await this.deleteTopic(ids);
        },
        // 批量删除
        handleClear(){
            this.$hMessage.destroy();
            if (!this.selection.length) {
                this.$hMessage.info('请选择一条或多条数据');
                return;
            }
            this.$hMsgBoxSafe.confirm({
                title: '批量删除主题',
                content: '您确认要批量删除已选中的主题吗？',
                onOk: async () => {
                    const ids = this.selection.map(v => v.id.toString());
                    await this.deleteTopic(ids);
                }
            });
        },
        // 删除
        async deleteTopic(ids){
            const param = {
                rcmId: this.rcmId,
                transportIds: ids
            };
            const res = await deleteTopic(param);
            if (res.code === '200'){
                this.$hMessage.success('删除成功!');
                // 只要查询下就更新下标签---适配删除操作和增加标签操作
                this.formItems[3].options = await this.queryTopicsTags();
                this.$refs['table'].$_handleQuery();
                this.selection = [];
            }
        },
        // 添加、删除标签
        handleTag(operate){
            this.$hMessage.destroy();
            if (!this.selection.length) {
                this.$hMessage.info('请选择一条或多条数据');
                return;
            }
            // 所勾选项标签列表-用于批量删除
            const taglist = this.selection.map(o => o.tags || []);
            const selectTagList = taglist?.flat() || [];
            if (operate === 'del' && !selectTagList.length) {
                this.$hMessage.warning('所选项未绑定标签');
                return;
            }
            this.inputInfo.id = this.rcmId;
            this.inputInfo.type = 'topic';
            this.inputInfo.context = this.selection.map(v => v.topic)?.join(',');
            this.inputInfo.status = true;
            this.inputInfo.operate = operate;
            this.inputInfo.title = operate === 'add' ? '添加标签' : '删除标签';
            this.inputInfo.tags = [];
            this.inputInfo.tagList = selectTagList;
        },
        // 添加标签
        async addTags(val){
            if (!val?.selection?.length) {
                return;
            }
            const param = {
                rcmId: this.rcmId,
                transportIds: this.selection.map(v => v.id.toString()),
                tags: [...val.selection]
            };
            const res = await addTopicTags(param);
            if (res.code === '200'){
                this.$hMessage.success('添加标签成功!');
                // 只要查询下就更新下标签---适配删除操作和增加标签操作
                this.formItems[3].options = await this.queryTopicsTags();
                this.$refs['table'].$_handleQuery();
                this.selection = [];
            }
        },
        // 删除标签
        async delTags(val) {
            if (!val?.selection?.length) {
                return;
            }
            const param = {
                rcmId: this.rcmId,
                transportIds: this.selection.map(v => v.id.toString()),
                tags: [...val.selection]
            };
            const res = await delTopicTags(param);
            if (res.code === '200'){
                this.$hMessage.success('删除标签成功!');
                // 只要查询下就更新下标签---适配删除操作和增加标签操作
                this.formItems[3].options = await this.queryTopicsTags();
                this.$refs['table'].$_handleQuery();
                this.selection = [];
            }
        },
        // 查询所有的标签
        async queryTopicsTags(){
            const tagList = [];
            const res = await queryTopicsTags({
                rcmId: this.rcmId
            });
            if (res.code === '200'){
                res?.data?.tags?.length && res.data.tags.forEach(v => {
                    tagList.push({
                        value: v,
                        label: v
                    });
                });
            }
            return tagList || [];
        }
    },
    components: { addOrUpdateTopicModal, rcmNormalTitleTable, createTagModal }
};
</script>
<style lang="less" scoped>
.topic-box {
    width: 100%;
    height: 100%;

    .best-table {
        padding: 0;
    }

    /deep/ .h-select {
        .h-checkbox-inner {
            border: 1px solid #d7dde4;
            border-radius: 2px;
            background-color: #fff;
        }
    }
}
</style>
