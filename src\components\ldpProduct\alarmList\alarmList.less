.alarm-wrapper {
    width: 100%;
    height: 100%;
    padding: 10px;
    box-sizing: border-box;
    overflow: hidden;

    & > .alarm-list {
        height: calc(100% - 15px);
        overflow: auto;

        & > li {
            display: flex;
            color: #fff;
            user-select: auto;
            padding-bottom: 10px;

            &:hover {
                color: var(--link-color);
            }

            .h-poptip {
                width: 100%;
            }

            .h-poptip-rel {
                width: 100%;
            }

            .alarm-content {
                display: flex;
                width: 100%;
                overflow: hidden;
            }

            span {
                display: inline-block;
                padding-right: 10px;
                cursor: pointer;
            }

            .text-time {
                width: 150px;
            }

            .text-level {
                width: 45px;
            }

            .text-location {
                width: 100px;
                .ellipsis();
            }

            .text-source {
                width: 120px;
                .ellipsis();
            }

            .text-message {
                flex: 1;
                width: calc(100% - 300px);
                padding: 0;
                .ellipsis();
            }

            .btn-alarm {
                color: var(--link-color);
                cursor: pointer;

                &:hover {
                    text-decoration: underline;
                }
            }
        }
    }
}

.h-page {
    position: absolute;
    bottom: 20px;
    left: 6px;
}

.h-page-item {
    background: var(--wrapper-color);
    color: #fff;
}

.h-page.mini .h-page-disabled {
    background-color: var(--wrapper-color);
}

.ellipsis() {
    overflow: hidden; //超出的文本隐藏
    text-overflow: ellipsis; //溢出用省略号显示
    white-space: nowrap; // 默认不换行；
}

.pop-alarm {
    .content-text {
        width: 380px;
        max-height: 200px;
        word-break: break-all;
        white-space: normal;
        color: #fff;
    }

    .h-poptip-inner {
        background-color: var(--poptip-bg-color);
    }

    .h-poptip-rel {
        width: 100%;
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
    }

    .h-poptip-title {
        padding: 8px;
        border-bottom: 1px solid var(--font-opacity-color);

        &::after {
            display: none;
        }
    }

    &[x-placement^="top"] {
        .h-poptip-arrow {
            border-top-color: var(--poptip-bg-color) !important;

            &::after {
                border-top-color: var(--poptip-bg-color) !important;
            }
        }
    }

    .h-poptip-title-inner {
        line-height: 18px;
        color: var(--font-color);
    }

    .h-poptip-body {
        padding: 8px;
    }
}
