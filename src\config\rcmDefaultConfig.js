/*
 * @Description: 上下文主题默认配置
 * @Author: <PERSON><PERSON>
 * @Date: 2023-06-20 14:06:12
 * @LastEditTime: 2023-10-11 14:01:28
 * @LastEditors: <PERSON>ale <PERSON>
 */
export const normalTempDefault = {
    appName: '',
    ip: '127.0.0.1',
    recordDir: '.',
    repairPortStart: 30001,
    repairPortEnd: 40000,
    ttl: 8,
    mtu: 1500,
    sendWindowSize: 64,
    maxMemoryAllowedMBytes: 256,
    socketBufferSizeKBytes: 256,
    mcLoop: 1,
    hasTotalOrder: true,
    hasRecord: false,
    zone: null
};

export const topicTempDefault = {
    heartbeatIntervalMilli: 1000,
    heartbeatTimeoutMilli: 3000,
    ackIntervalMilli: 1000,
    ackTimeoutMilli: 3000,
    useSharedMemory: false,
    asynchronousRms: false,
    hasBypass: false
};

export const clusterTempDefault = {
    syncIp: '*********',
    syncAddr: '*********',
    syncPort: 27001,
    syncHeartbeatIntervalMilli: 1000,
    syncHeartbeatTimeoutMilli: 3000,
    syncAckIntervalMilli: 1000,
    syncAckTimeoutMilli: 3000,
    syncSendWindowSize: 64,
    syncMtu: 1500,
    syncMode: 0
};

export const materialRange = {
    localPort: {
        minValue: 1,
        maxValue: 65535
    },
    transportId: {
        minValue: 1,
        maxValue: 65535
    },
    multicastAddr: {
        minValue: '*********',
        maxValue: '***********'
    },
    contextId: {
        minValue: 1,
        maxValue: 65535
    },
    partitionNo: {
        minValue: 1,
        maxValue: 65535
    },
    syncPort: {
        minValue: 1,
        maxValue: 65535
    },
    syncAddr: {
        minValue: '*********',
        maxValue: '***********'
    },
    multicastPort: {
        minValue: 1,
        maxValue: 65535
    }
};

// rcm上下文状态
export const rcmStatusObj = {
    online: '在线',
    offline: '离线'
};
// rcm上下文 状态颜色定义
export const rcmStyle = {
    online: '#7DFCB2',
    offline: '#9E9D9D'
};

// 集群、应用 边和节点 状态对应颜色定义
export const edgeStyle = {
    connected: {
        color: '#7DFCB2',
        type: 'solid'
    },
    connecting: {
        color: '#4A92FF',
        type: 'dashed'
    },
    disconnected: {
        color: '#5470c6',
        type: 'solid'
    }
};
export const nodeStyle = {
    unmanaged: '#4A92FF',
    runing: '#7DFCB2',
    warning: '#FDDD60',
    exception: '#FF6E76',
    stop: '#9E9D9D'
};
