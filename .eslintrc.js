/*
 * @Description:
 * @Author: <PERSON><PERSON>
 * @Date: 2023-03-28 13:13:35
 * @LastEditTime: 2023-04-17 15:03:22
 * @LastEditors: <PERSON><PERSON>
 */
module.exports = {
    extends: [require.resolve('@toolkit-js/iconfig/lib/eslintrc-hs')],
    parserOptions: {
        parser: '@babel/eslint-parser',
        babelOptions: {
            parserOpts: {
                plugins: ['jsx']
            }
        }
    },
    rules: {
        'no-multi-spaces': 'off',
        'vue/no-deprecated-slot-attribute': 'off',
        indent: ['error', 4, {
            SwitchCase: 1
        }]
    }
};
