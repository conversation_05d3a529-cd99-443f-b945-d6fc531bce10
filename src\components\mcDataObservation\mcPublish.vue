<template>
    <div ref="tab-box" class="tab-box">
        <a-loading v-if="loading"></a-loading>
        <div v-else>
            <div class="title">
                <p>生产者信息</p>
                <apm-select-search
                    ref="publisherSearch"
                    minWidth="150px"
                    focusWidth="350px"
                    dropHeight="200px"
                    class="securities"
                    :list="publisherList"
                    clearable
                    placeholder="请选择生产者"
                    :border="true"
                    @onChange="(val) => handleSelectChange(val, 'publisher')"
                />
                <a-button type="dark"  @click="handleButtonClick">刷新</a-button>
            </div>
            <!--基础信息-->
            <description-bar :data="description" @select-change="handleSelectChange"></description-bar>
            <!--发布详情 -->
            <obs-table ref="table" :height="tableHeight" :title="title" :tableData="tableData" :columns="columns" showTitle :hasPage="false" :loading="tableLoading" />
        </div>
    </div>
</template>

<script>
import aButton from '@/components/common/button/aButton';
import aLoading from '@/components/common/loading/aLoading';
import descriptionBar from '@/components/common/description/descriptionBar';
import apmSelectSearch from '@/components/common/apmSelectSearch/apmSelectSearch';
import obsTable from '@/components/common/obsTable/obsTable';
import { getMcAPi } from '@/api/mcApi';
import { removeDuplicates } from '@/components/mcDataObservation/constant';
export default {
    name: 'McPublish',
    props: {
        nodeData: {
            type: Object,
            default: () => {}
        }
    },
    components: { descriptionBar, aLoading, aButton, obsTable, apmSelectSearch },
    data() {
        return {
            loading: false,
            tableLoading: false,
            tableHeight: 0,
            // 基础信息
            description: {
                title: {
                    label: '基础信息'
                },
                details: [
                    {
                        dataDic: [
                            {
                                label: '生产者',
                                key: 'PubName'
                            },
                            {
                                label: '发布主题数',
                                key: 'TopicCount'
                            },
                            {
                                label: '发布分区数',
                                key: 'partitionCount'
                            },
                            {
                                label: '生产消息数',
                                key: 'PublishCounts'
                            }
                        ],
                        data: {
                            PubName: '-',
                            TopicCount: '-',
                            partitionCount: '-',
                            PublishCounts: '-'
                        }
                    }
                ]
            },
            // 发布详情
            title: {
                label: '发布详请'
            },
            tableData: [],
            columns: [
                {
                    title: '主题',
                    key: 'Topic',
                    ellipsis: true
                },
                {
                    title: '分区号',
                    key: 'Partition',
                    ellipsis: true
                },
                {
                    title: '发布序号',
                    key: 'MsgNo',
                    ellipsis: true
                }
            ],
            // 参数
            publisher: '',
            publisherList: [],
            fileData: {}
        };
    },
    mounted() {
        this.fetTableHeight();
        window.addEventListener('resize', this.fetTableHeight);
    },
    beforeDestroy() {
        window.removeEventListener('resize', this.fetTableHeight);
    },
    methods: {
        // 设置table高度
        fetTableHeight() {
            this.tableHeight = this.$refs['tab-box']?.getBoundingClientRect()?.height - 220;
        },
        // 手动清空数据
        clearData(){
            this.publisher = '';
            this.publisherList = [];
            this.fileData = {};
            this.tableData = [];
            this.description.details[0].data = {};
        },
        // 初始化构建数据结构
        async initData(val) {
            this.loading = true;
            await this.getFileData();
            this.getPublisherList();
            this.loading = false;
            this.$nextTick(() => {
                this.setDefaultPublisher(val);
            });
        },
        // 刷新---重新构建数据结构, 默认选择第一位生产者。若之前的生产者存在，则页面更新为当前生产者信息
        async handleButtonClick(){
            await this.initData(this.publisher);
        },
        // 判断生产者是否存在, 使用历史选中值用于刷新，否则默认选中第一个
        setDefaultPublisher(val){
            const hasPublisher = this.publisherList.filter(v => v.value === val)?.[0] || '';
            const searchPublisher = hasPublisher ? hasPublisher : this.publisherList?.[0] || '';
            if (!searchPublisher){
                this.clearData();
                return;
            }
            this.$refs['publisherSearch'].onChangeSelect(searchPublisher);
        },
        // 生产者切换
        handleSelectChange(val, key){
            if (key === 'publisher'){
                this.publisher = val;
                const data = this.fileData?.[val] || [];
                const topicList = data.map(v => v.Topic);
                const partitionList = data.map(v => v.Partition);
                const publishCounts = data.map(v => v.PubCount).reduce((a, b) => a + b, 0);
                this.description.details[0].data = {
                    PubName: val,
                    TopicCount: [...new Set(topicList)]?.length,
                    partitionCount: [...new Set(partitionList)]?.length,
                    PublishCounts: publishCounts
                };
                this.tableLoading = true;
                this.tableData = [...data];
                setTimeout(() => {
                    this.tableLoading = false;
                }, 200);
                return;
            }
        },
        // 生产者select
        getPublisherList(){
            this.publisher = '';
            this.publisherList = [];
            const publisherList = [];
            Object.keys(this.fileData).forEach((v) => {
                publisherList.push({
                    label: v,
                    value: v
                });
            });
            this.publisherList = publisherList?.sort((a, b) => a?.value?.localeCompare(b?.value));
        },
        // 构建页面数据
        async getFileData(){
            this.fileData = {};
            const { GetPubMsgNo } = await this.getPubAPi();
            const { GetTopicInfo } = await this.getTopicAPi();
            for (const pub of Object.values(GetPubMsgNo)){
                const key = JSON.stringify(pub.PubName).replace(/"/g, '');
                this.fileData[key] = this.fileData?.[key] || [];
                const str = String(pub?.TopicPartition);
                const topicNo = str.substring(0, str.length - 4); // 剩余部分
                const partition = str.substring(str.length - 4); // 后四位
                const topic = GetTopicInfo?.filter(v => String(v?.TopicNo) === topicNo)?.[0]?.Topic || '';
                // 生产者-表格数据
                this.fileData[key].push({
                    PubName: pub.PubName,
                    Topic: topic,
                    Partition: Number(partition),
                    MsgNo: pub.NextMsgNo ? pub.NextMsgNo - 1 : 0,
                    PubCount: pub.PublishCount ? pub.PublishCount : 0
                });
            }
        },
        // 接口请求
        async getPubAPi() {
            const funcNameList = ['GetPubMsgNo'];
            const data = await getMcAPi(this.nodeData?.productInstances || [], funcNameList, 'ldp_mc');
            const res = removeDuplicates(data?.GetPubMsgNo || []);
            return { GetPubMsgNo: res };
        },
        // 接口请求
        async getTopicAPi() {
            const funcNameList = ['GetTopicInfo'];
            const data = await getMcAPi(this.nodeData?.productInstances || [], funcNameList, 'ldp_mc', false);
            return data;
        }
    }
};
</script>

<style lang="less" scoped>
.tab-box {
    height: 100%;

    .title {
        color: var(--font-color);
        font-size: 14px;
        width: 100%;
        height: 40px;
        line-height: 40px;
        margin: 0 10px;
        position: relative;

        .securities {
            position: absolute;
            width: auto;
            top: 5px;
            right: 80px;
            min-width: 200px;
        }

        button {
            position: absolute;
            top: 5px;
            right: 10px;
        }
    }
}
</style>
