.spin-container {
    width: 100%;
    height: 100%;
    position: absolute;
    top: 0;
    left: 0;
    text-align: center;
    background-color: var(--input-bg-color);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 8;

    .reverse-spinner {
        position: relative;
        height: 60px;
        width: 60px;
        border: 4px solid transparent;
        border-top-color: #1976d2;
        border-left-color: #1976d2;
        border-radius: 50%;
        animation: spin 1.5s linear infinite;
    }

    .reverse-spinner::before {
        position: absolute;
        top: 15px;
        left: 15px;
        right: 15px;
        bottom: 15px;
        content: "";
        border: 4px solid transparent;
        border-top-color: #03a9f4;
        border-left-color: #03a9f4;
        border-radius: 50%;
        animation: spinBack 1s linear infinite;
    }

    @-webkit-keyframes spin {
        from {
            transform: rotate(0deg);
        }

        to {
            transform: rotate(360deg);
        }
    }

    @keyframes spin {
        from {
            transform: rotate(0deg);
        }

        to {
            transform: rotate(360deg);
        }
    }

    @-webkit-keyframes spinBack {
        from {
            transform: rotate(0deg);
        }

        to {
            transform: rotate(-720deg);
        }
    }

    @keyframes spinBack {
        from {
            transform: rotate(0deg);
        }

        to {
            transform: rotate(-720deg);
        }
    }
}

