<template>
    <div class="main">
        <a-title title="产品节点配置管理" class="title">
            <slot>
                <p>{{ productInfo.productName }}</p>
                <a-button class="btn-add" type="primary" :loading="loading" @click="addProductNode">
                    对接产品节点
                </a-button>
            </slot>
        </a-title>
        <a-loading v-if="loading"></a-loading>

        <menu-layout ref="menu-layout" customMenu @menu-fold="menuFold">
            <template v-slot:menu>
                <div class="menu">
                    <div class="header-menu" :style="{ padding: menuFoldStatus ? '0' : '0px 10px' }">
                        已注册产品节点
                    </div>
                    <h-menu
                        v-if="productList.length"
                        theme="dark"
                        :active-name="productInstNo"
                        :open-names="openName"
                        style="height: calc(100% - 90px);"
                        @on-select="selectMenuChange">
                        <submenu
                            v-for="(item, idx) in productList"
                            :key="item.productType + idx"
                            :name="item.productType">
                            <template v-slot:title>
                                {{ productTypeList[item.productType] || item.productType }}
                            </template>
                            <h-menu-item v-for="child in item.products" :key="child.id" :name="child.id">
                                <!-- <span :class="bindClass(child.productRuntimeEnvironment)"
                                    style="display: inline-block; width: 25px;">
                                    {{ child.productName }}</span> -->
                                <span>{{ child.productName }}</span>
                            </h-menu-item>
                        </submenu>
                    </h-menu>
                    <a-button
                        type="danger" class="menu-footer"
                        :style="{ display: menuFoldStatus ? 'none' : 'block' }"
                        @click="delProductNode">
                        删除产品节点
                    </a-button>
                </div>
            </template>
            <template v-slot:right>
                <h-tabs
                    v-if="productInstNo && editableTabs.length"
                    ref="tabs"
                    v-model="tabName"
                    class="product-box"
                    @on-click="tabClick(tabName)">
                    <h-tab-pane
                        v-for="item in editableTabs"
                        :key="item.name"
                        :label="item.describe"
                        :name="item.name">
                        <component
                            :is='item.name'
                            :ref="item.name"
                            :productInfo="productInfo"
                            @reload="init">
                        </component>
                    </h-tab-pane>
                    <!-- tab-slots -->
                    <div ref="operation" slot="extra" class="tabs-operation">
                        <a-button
                            v-show="tabName === 'appInstanceConfig'"
                            type="primary"
                            @click="handleTabBtnClick">
                            同步节点信息
                        </a-button>
                        <a-button
                            v-show="tabName === 'productService'"
                            class="icon-setting"
                            type="dark"
                            icon="setup"
                            @click="handleSettingClick">
                        </a-button>
                    </div>
                </h-tabs>
                <no-data v-else class="box"></no-data>
            </template>
        </menu-layout>

        <!-- 创建产品节点弹窗 -->
        <add-product-manage-modal
            v-if="addProductInfo.status"
            :modalInfo="addProductInfo"
            @reload="init"
            @selectMenuChange="onChangeMenu" />
    </div>
</template>

<script>
import _ from 'lodash';
import { mapState, mapActions } from 'vuex';
import { delProductNode, getDashboardTag, getDashboardConfigV2 } from '@/api/httpApi';
import aButton from '@/components/common/button/aButton';
import aTitle from '@/components/common/title/aTitle';
import menuLayout from '@/components/common/menuLayout/menuLayout';
import productInfoConfig from '@/components/ldpLinkConfig/productInfoConfig.vue';
import appInstanceConfig from '@/components/ldpLinkConfig/appInstanceConfig.vue';
import appClusterConfig from '@/components/ldpLinkConfig/appClusterConfig.vue';
import machineRoomConfig from '@/components/ldpLinkConfig/machineRoomConfig.vue';
import productService from '@/components/ldpLinkConfig/productServiceConfig.vue';
import serverConfig from '@/components/ldpLinkConfig/serverConfig.vue';
import rcmContext from '@/components/ldpLinkConfig/rcmContext.vue';
import addProductManageModal from '@/components/ldpLinkConfig/modal/addProductManageModal.vue';
import aLoading from '@/components/common/loading/aLoading';
import noData from '@/components/common/noData/noData';

export default {
    components: { aTitle, aButton, aLoading, noData, menuLayout, addProductManageModal,
        rcmContext, productInfoConfig, appInstanceConfig, appClusterConfig,
        machineRoomConfig, productService, serverConfig
    },
    data() {
        return {
            editableTabs: [],
            tabName: 'productInfoConfig',
            loading: false,
            addProductInfo: {
                status: false
            },
            menuFoldStatus: false,
            openName: [],   // menu默认展开
            productInstNo: '', // menu默认选择
            productInfo: {} // 选中的menu信息
        };
    },
    async mounted() {
        this.init();
    },
    computed: {
        ...mapState({
            productList: state => {
                return state.product.productListLight || [];
            }
        }),
        productTypeList() {
            const list = this.$store?.state?.apmDirDesc?.productTypeDict || {};
            return list;
        }
    },
    methods: {
        ...mapActions({ getProductList: 'product/getProductListLight' }),
        // 产品名绑定生产、测试样式
        // bindClass(environ) {
        //     const classInfo = { 'pro-environ': environ === 'produce', 'test-environ': environ === 'test' };
        //     return classInfo;
        // },

        menuFold(status) {
            this.menuFoldStatus = status;
        },
        async getDashboardConfig() {
            try {
                let tags = [];
                // 获取dashboard查询tag
                const tagsRes = await getDashboardTag({
                    scene: 'productObservation',
                    objectType: 'product',
                    objectId: this.productInstNo
                });
                if (tagsRes.code === '200') {
                    tags = tagsRes?.data || [];
                }
                // tags作为入参，查询dashboard配置
                const res = await getDashboardConfigV2({
                    scene: 'productObservation',
                    tags: tags,
                    objectType: this.productInfo.productType,
                    objectId: this.productInstNo
                });
                if (res.code === '200') {
                    this.editableTabs = res?.data?.filter(v => v?.visible === true) || [];
                }
            } catch (err) {
                this.editableTabs = [];
            }
        },
        // 初始化页面
        async init(type) {
            try {
                this.loading = true;
                await this.getProductList(
                    { groupMode: 'productType' }
                );

                // 判断左侧菜单少于一个选项时，隐藏菜单
                this.$nextTick(() => {
                    if (this.$refs['menu-layout']) {
                        const isOnlyOneItem = this.productList?.length <= 1 &&
                                (this.productList?.[0]?.products?.length <= 1);
                        this.$refs['menu-layout'].setMenuStatus(!this.productList?.length || isOnlyOneItem);
                    }
                });

                const productInstNo = this.$route.query?.productId || localStorage.getItem('productInstNo');
                // 获取缓存的产品信息
                const { openName, productInfo } = this.getProductInfo(productInstNo);
                // type为创建新产品的类型 新创建成功选中该类型第一个产品
                this.openName = openName ? [openName] : type ? [type] : [this.productList?.[0]?.productType];
                this.productInfo = productInfo ? productInfo : type
                    ? _.find(this.productList, ['productType', type])?.products?.[0]
                    : this.productList?.[0]?.products?.[0] || {};
                this.productInstNo = this.productInfo?.id || '';
                this.productInstNo && this.selectMenuChange(this.productInstNo);

                // 延迟300毫秒，单产品时保证菜单收起后再展示页面
                setTimeout(() => {
                    this.loading = false;
                }, 500);
            } catch (e){
                console.error(e);
            } finally {
                this.loading = false;
            }
        },
        // 根据productInstNo找到所在产品分组和产品信息
        getProductInfo(productInstNo) {
            for (const menu of this.productList) {
                const items = _.find(menu.products, ['id', productInstNo]);
                if (items) {
                    return {
                        openName: menu?.productType,
                        productInfo: items || {}
                    };
                }
            }
            return {};
        },
        // 创建产品节点
        addProductNode() {
            this.addProductInfo.status = true;
        },
        // 删除产品节点
        delProductNode() {
            if (!this.productList?.length) return this.$hMessage.warning('无产品可删除');
            this.$hMsgBoxSafe.confirm({
                title: '删除',
                content: `您确定删除名为"${this.productInfo.productName}"产品节点吗？`,
                onOk: async () => {
                    const res = await delProductNode({
                        id: this.productInfo.id
                    });
                    if (res.success) {
                        this.$hMessage.success('删除成功');
                        this.init();
                    } else {
                        this.$hMessage.warning('删除失败');
                    }
                }
            });
        },
        // 切换菜单
        async selectMenuChange(id) {
            this.productInstNo = id;
            localStorage.setItem('productInstNo', id);
            this.productInfo = this.getProductInfo(id)?.productInfo;

            // 获取tab页配置
            await this.getDashboardConfig();

            const tab = _.find(this.editableTabs, ['name', this.tabName]);
            this.tabName = tab ? this.tabName : this.editableTabs?.[0]?.name || 'productInfoConfig';

            this.$nextTick(async () => {
                await this.tabClick(this.tabName);
            });
        },
        // 切换tab
        async tabClick(name) {
            this.tabName = name;
            this.$nextTick(async () => {
                this.$refs?.[name]?.[0] && await this.$refs[name][0].initData();
            });
        },
        /**
         * 由内部指定选中菜单
         */
        onChangeMenu(id) {
            const { openName } = this.getProductInfo(id) || {};
            this.openName = openName ? [openName] : [this.productList?.[0]?.productType];
            this.selectMenuChange(id);
        },
        /**
         * 处理 Tab 按钮点击事件，根据当前选中的 Tab 名称执行相应的操作.
         */
        handleTabBtnClick() {
            // 判断当前选中的 tab
            if (this.tabName === 'appInstanceConfig') {
                // 如果是 'appInstanceConfig'，执行节点信息同步操作
                this.$refs?.[this.tabName]?.[0] && this.$refs[this.tabName][0].handleInsSyncPrompt();
            }
        },
        /**
         * 处理服务服务同步配置
         */
        handleSettingClick() {
            if (this.tabName === 'productService') {
                // 如果是 'appInstanceConfig'，执行节点信息同步操作
                this.$refs?.[this.tabName]?.[0] && this.$refs[this.tabName][0].handleServiceSetting();
            }
        }
    }

};
</script>

<style lang="less" scoped>
@import url("@/assets/css/menu.less");
@import url("@/assets/css/tab.less");
@import url("@/assets/css/input.less");

.main {
    .title {
        min-width: 1000px;

        p {
            display: inline-block;
            color: var(--font-color);
            font-size: var(--title-font-size);
            position: absolute;
            left: 50%;
            transform: translateX(-50%);
        }

        .btn-add {
            position: absolute;
            right: 10px;
            top: 5px;
        }
    }

    & > .apm-box {
        height: calc(100% - 58px);
        background: none;
        min-width: 1000px;

        /deep/ .menu {
            border: 0;
        }

        /deep/ .left-box {
            border-radius: 4px;
        }

        /deep/ .right-box {
            background: none;
            padding: 0 10px;
        }

        /deep/ .h-tabs-bar {
            margin-bottom: 5px;
        }

        /deep/ .h-tabs-content-wrap {
            height: calc(100% - 50px);
        }
    }

    .menu {
        /deep/ .h-menu-item {
            padding-left: 30px;
        }

        .pro-environ::before {
            .environ("\4EA7");
        }

        .test-environ::before {
            .environ("\6D4B");
        }
    }
    .environ(@text) {
        display: flex;
        justify-content: center;
        align-items: center;
        width: 22px;
        height: 22px;
        content: @text !important;
        position: absolute;
        left: 14px;
        top: 5px;
        font-size: 12px;
        font-weight: 600;
        border-radius: 50%;
        text-align: center;
        border: 2px solid rgb(192, 196, 203);
        color: rgb(192, 196, 203);
        transform: scale(0.75, 0.75);
    }

    /deep/ .h-tabs-nav-wrap {
        width: 100%;

        .h-tabs-nav-right {
            float: right;
            margin-top: 3px;
            padding: 0;
        }
    }

    .tabs-operation {
        display: flex;
        height: 32px;
        gap: 8px;

        .icon-setting {
            cursor: pointer;
            border: var(--border);
            border-radius: 4px;
            padding: 2px 6px;

            /deep/ .iconfont {
                font-size: 18px;
            }
        }
    }
}
</style>
