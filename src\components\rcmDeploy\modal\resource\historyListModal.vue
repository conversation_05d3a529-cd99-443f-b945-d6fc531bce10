<!-- stylelint-disable indentation -->
<template>
  <div>
    <h-msg-box-safe
      v-model="modalData.status"
      :escClose="true"
      :mask-closable="false"
      style="overflow: auto;"
      title="历史发布版本"
      width="550"
      height="300"
    >
      <h-timeline v-if="historyList.length">
        <h-timeline-item
          v-for="(item, index) in historyList"
          :key="index"
          color="blue"
        >
          <p style="font-size: 14px; font-weight: 500; line-height: 18px;">
            {{ item.publishDate }} - 发布版本：{{ item.version }}
            <a style="float: right;" @click="handleEventData(index)">查看</a>
          </p>
          <p
            style="margin-top: 8px; color: #777; white-space: nowrap; text-overflow: ellipsis; overflow: hidden; display: flex;"
          >
            <span>备注：</span>
            <h-poptip
              trigger="hover"
              transfer
              class="popo-icon apm-poptip"
              placement="left"
            >
              <span
                style="white-space: nowrap; text-overflow: ellipsis; overflow: hidden; display: inline-block; max-width: 400px;"
              >
                {{ item.remark }}
              </span>
              <template v-slot:content>
                <div style="width: 180px; white-space: normal;">
                  {{ item.remark }}
                </div>
              </template>
            </h-poptip>
          </p>
        </h-timeline-item>
      </h-timeline>
      <no-data v-else isWhite />
      <template v-slot:footer>
        <h-button @click="modalData.status = false">取消</h-button>
      </template>
    </h-msg-box-safe>
  </div>
</template>

<script>
import noData from '@/components/common/noData/noData';
export default {
    props: {
        modalInfo: {
            type: Object,
            default: null
        },
        historyList: {
            type: Array,
            default: () => []
        }
    },
    data() {
        return {
            modalData: this.modalInfo
        };
    },
    methods: {
        handleEventData(idx) {
            this.$emit('restContrastData', idx);
            this.modalData.status = false;
        }
    },
    components: { noData }
};
</script>
