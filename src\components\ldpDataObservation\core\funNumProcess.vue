<template>
    <div class="tab-box">
        <a-loading v-if="loading"></a-loading>
        <div v-else>
            <!-- 功能号处理详情 -->
            <obs-table ref="table" :title="title" :tableData="tableData" :columns="columns" showTitle highlightRow rowSelectOnly :maxHeight="200"
                @select-change="tableSelectChange" @check-change="handleCheckboxChange" @on-current-change="tableRowcheckedChange"/>

            <!-- 功能号执行详情 -->
            <info-grid v-if="currentFuncNo || currentFuncNo === 0" ref="grid" :gridData="funcExecData" @select-change="chartSelectChange"></info-grid>
        </div>
    </div>
</template>

<script>
import _ from 'lodash';
import aLoading from '@/components/common/loading/aLoading';
import obsTable from '@/components/common/obsTable/obsTable';
import infoGrid from '@/components/common/infoBar/infoGrid';
import { getManagerProxy } from '@/api/mcApi';
import { formatChartNumber, nsConvertTime, compareObjArr, calculateTimeDifference } from '@/utils/utils';
const extendColumns = {
    Ave: [
        { key: 'AveExecTimeNs', value: '执行单次平均耗时(不含排队)' },
        { key: 'AveQueueTimeNs', value: '队列积压的平均时间' },
        { key: 'AveLocateTimeNs', value: '平均定位时间' },
        { key: 'AveTotalTimeNs', value: '执行单次平均耗时(含排队)' },
        { key: 'AveRedoTimeNs', value: 'redo平均耗时' }
    ],
    Min: [
        { key: 'MinExecTimeNs', value: '执行单次最小耗时(不含排队)' },
        { key: 'MinQueueTimeNs', value: '队列积压的最小时间' },
        { key: 'MinLocateTimeNs', value: '最小定位时间' },
        { key: 'MinTotalTimeNs', value: '执行单次最小耗时(含排队)' },
        { key: 'MinRedoTimeNs', value: 'redo最小耗时' }
    ],
    Max: [
        { key: 'MaxExecTimeNs', value: '执行单次最大耗时(不含排队)' },
        { key: 'MaxQueueTimeNs', value: '队列积压的最大时间' },
        { key: 'MaxLocateTimeNs', value: '最大定位时间' },
        { key: 'MaxTotalTimeNs', value: '执行单次最大耗时(含排队)' },
        { key: 'MaxRedoTimeNs', value: 'redo最大耗时' }
    ],
    Sum: [
        { key: 'SumExecTimeNs', value: '累计执行时间(不含排队)' },
        { key: 'SumQueueTimeNs', value: '队列积压的总时间' },
        { key: 'SumLocateTimeNs', value: '定位的总时间' },
        { key: 'SumTotalTimeNs', value: '累计执行时间(含排队)' },
        { key: 'SumRedoTimeNs', value: 'Redo累计耗时' }
    ],
    Last: [
        { key: 'LastExecTimeNs', value: '最后一次执行时间(不含排队)' },
        { key: 'LastQueueTimeNs', value: '最后一次队列积压时间' },
        { key: 'LastLocateTimeNs', value: '最后一次定位时间' },
        { key: 'LastTotalTimeNs', value: '最后一次执行时间(含排队)' },
        { key: 'LastRedoTimeNs', value: 'redo最后一次调用耗时' }
    ]
};
const POINT_COUNT = 60;
export default {
    name: 'FunNumProcess',
    components: { aLoading, obsTable, infoGrid },
    props: {
        nodeData: {
            type: Object,
            default: () => { }
        },
        pollTime: {
            type: Number,
            default: 0
        }
    },
    data() {
        return {
            timer: null,
            loading: true,
            selectTopValue: 10,
            selectValue: 'Ave',
            checkboxValue: true,
            currentFuncNo: '', // 选中功能号
            title: {
                label: '功能号处理详情',
                slots: [
                    {
                        key: 'funcSelect',
                        type: 'select',
                        defaultValue: 10,
                        options: [
                            {
                                value: 10,
                                label: 'Top10'
                            },
                            {
                                value: 30,
                                label: 'Top30'
                            },
                            {
                                value: 50,
                                label: 'Top50'
                            }
                        ]
                    },
                    {
                        type: 'checkbox',
                        label: '执行次数大于0',
                        key: 'checkbox',
                        defaultValue: true
                    }
                ]
            },
            tableData: [],
            columns: [
                { key: 'FunctionID', title: '功能号', sortable: true, minWidth: 100  },
                { key: 'Throughput', title: '吞吐(次/秒)', sortable: true, minWidth: 120  },
                { key: 'AveTotalTimeNs', title: '执行单次平均耗时(纳秒)', sortable: true, minWidth: 200  },
                { key: 'TotalExecNum', title: '累计执行总次数', sortable: true, minWidth: 160  },
                { key: 'FailExecNum', title: '累计执行失败次数', sortable: true, minWidth: 160  },
                { key: 'FailRate', title: '错误率', sortable: true, minWidth: 100,
                    render: (h, params) => {
                        return h('div', params.row.FailRate + '%');
                    }
                }
            ],
            // 功能号执行详情
            funcExecData: {
                title: {
                    label: '功能号执行详情',
                    slots: [
                        {
                            key: 'funcSelect',
                            type: 'select',
                            defaultValue: 'Ave',
                            options: [
                                {
                                    value: 'Ave',
                                    label: 'AVE'
                                },
                                {
                                    value: 'Min',
                                    label: 'MIN'
                                },
                                {
                                    value: 'Max',
                                    label: 'MAX'
                                },
                                {
                                    value: 'Sum',
                                    label: 'SUM'
                                },
                                {
                                    value: 'Last',
                                    label: 'LAST'
                                }
                            ]
                        }
                    ]
                },
                layout: [
                    { x: 0, y: 0, w: 12, h: 12, i: 'info1' },
                    { x: 0, y: 2, w: 3, h: 12, i: 'info2' },
                    { x: 3, y: 2, w: 9, h: 12, i: 'info3' },
                    { x: 0, y: 2, w: 12, h: 12, i: 'info4' }
                ],
                details: [
                    {
                        type: 'chart',
                        info: {
                            basicOpiton: {
                                chartType: 'axis',
                                lineDeploy: [
                                    {
                                        name: '吞吐',
                                        type: 'line'
                                    },
                                    {
                                        name: '时延',
                                        type: 'line',
                                        tooltip: {
                                            valueFormatter: nsConvertTime
                                        },
                                        yAxisIndex: 1
                                    }
                                ],
                                yAxiDeploy: [
                                    {
                                        name: '吞吐(次/秒)',
                                        alignTicks: true,
                                        axisLabel: {
                                            formatter: formatChartNumber
                                        }
                                    },
                                    {
                                        name: '时延',
                                        show: true,
                                        alignTicks: true,
                                        axisLabel: {
                                            formatter: nsConvertTime
                                        }
                                    }
                                ]
                            },
                            additionalOpiton: {
                                backgroundColor: '#2d334c'
                            },
                            chartData: {
                                xData: [],
                                data: {
                                    吞吐: [],
                                    时延: []
                                }
                            }
                        }
                    },
                    {
                        type: 'obj',
                        title: '功能号处理时延分布',
                        infoDic: [
                            {
                                label: 'AveExecTimeNs',
                                key: 'AveExecTimeNs'
                            },
                            {
                                label: 'AveQueueTimeNs',
                                key: 'AveQueueTimeNs'
                            },
                            {
                                label: 'AveLocateTimeNs',
                                key: 'AveLocateTimeNs'
                            },
                            {
                                label: 'AveTotalTimeNs',
                                key: 'AveTotalTimeNs'
                            },
                            {
                                label: 'AveRedoTimeNs',
                                key: 'AveRedoTimeNs'
                            }
                        ],
                        info: {}
                    },
                    {
                        type: 'chart',
                        info: {
                            basicOpiton: {
                                chartType: 'axis',
                                lineDeploy: [
                                    {
                                        name: 'TotalTime',
                                        type: 'line',
                                        tooltip: {
                                            valueFormatter: nsConvertTime
                                        }
                                    },
                                    {
                                        name: 'LocateTime',
                                        type: 'line',
                                        areaStyle: true,
                                        areaColor: [253, 221, 96], // 设置区域渐变色[r,g,b]
                                        stack: 'Total',
                                        tooltip: {
                                            valueFormatter: nsConvertTime
                                        }
                                    },
                                    {
                                        name: 'QueueTime',
                                        type: 'line',
                                        areaStyle: true,
                                        areaColor: [255, 110, 118],
                                        stack: 'Total',
                                        tooltip: {
                                            valueFormatter: nsConvertTime
                                        }
                                    },
                                    {
                                        name: 'ExecTime',
                                        type: 'line',
                                        areaStyle: true,
                                        areaColor: [4, 192, 145],
                                        stack: 'Total',
                                        tooltip: {
                                            valueFormatter: nsConvertTime
                                        }
                                    },
                                    {
                                        name: 'RedoTime',
                                        type: 'line',
                                        areaStyle: true,
                                        areaColor: [141, 72, 227],
                                        stack: 'Total',
                                        tooltip: {
                                            valueFormatter: nsConvertTime
                                        }
                                    }
                                ],
                                yAxiDeploy: [
                                    {
                                        name: '时延',
                                        axisLabel: {
                                            formatter: nsConvertTime
                                        }
                                    }
                                ]
                            },
                            additionalOpiton: {
                                backgroundColor: '#2d334c'
                            },
                            chartData: {
                                xData: [],
                                data: {
                                    TotalTime: [],
                                    LocateTime: [],
                                    QueueTime: [],
                                    ExecTime: [],
                                    RedoTime: []
                                }
                            }
                        }
                    },
                    {
                        type: 'chart',
                        info: {
                            basicOpiton: {
                                chartType: 'axis',
                                lineDeploy: [
                                    {
                                        name: '错误次数',
                                        type: 'bar'
                                    },
                                    {
                                        name: '执行次数',
                                        type: 'bar'
                                    },
                                    {
                                        name: '错误率',
                                        type: 'line',
                                        yAxisIndex: 1
                                    }
                                ],
                                yAxiDeploy: [
                                    {
                                        name: '次数',
                                        axisLabel: {
                                            formatter: formatChartNumber
                                        }
                                    },
                                    {
                                        name: '错误率(‱)',
                                        show: true,
                                        min: 0,
                                        max: 10000
                                    }
                                ]
                            },
                            additionalOpiton: {
                                backgroundColor: '#2d334c',
                                xAxis: { boundaryGap: true }
                            },
                            chartData: {
                                xData: [],
                                data: {
                                    错误次数: [],
                                    执行次数: [],
                                    错误率: []
                                }
                            }
                        }
                    }
                ]
            }
        };
    },
    mounted() {
    },
    methods: {
        async initData() {
            this.loading = true;
            this.cleanData(); // 清理图表数据
            await this.getFileData();
            this.loading = false;
        },
        // 清理定时器
        clearPolling() {
            this.timer && clearInterval(this.timer);
        },
        // 处理折线图数据方法
        handleChartData(newTime) {
            const chartData0 = this.funcExecData.details[0].info.chartData;
            const chartData2 = this.funcExecData.details[2].info.chartData;
            const chartData3 = this.funcExecData.details[3].info.chartData;
            const chartDataList = [chartData0, chartData2, chartData3];
            Array.isArray(chartDataList) && chartDataList.forEach(chartData => {
                if (chartData.xData.length > POINT_COUNT) {
                    chartData.xData.shift();
                    Object.values(chartData.data).forEach(item => {
                        item.shift();
                    });
                }
                if (!chartData.xData.includes(newTime)) chartData.xData.push(newTime);
            });
        },
        // 构造页面数据
        async getFileData() {
            // 全局折线图x轴处理
            const newTime = this.$getCurrentLocalTime();
            this.handleChartData(newTime);

            // 保存上一次数据
            const lastData = [];
            Array.isArray(this.wholeData) && this.wholeData.forEach(item => {
                lastData.push({ lastTotalExecNum: item.TotalExecNum, FunctionID: item.FunctionID });
            });
            this.lastData = lastData;
            const data = await this.getAPi();
            // 将上次请求数据插入当前请求数据中
            Array.isArray(data?.GetFuncDealInfo) && data.GetFuncDealInfo.forEach(item => {
                this.lastData.forEach(value => {
                    if (value.FunctionID === item.FunctionID) item.lastTotalExecNum = value.lastTotalExecNum;
                });
            });
            this.wholeData = data?.GetFuncDealInfo || [];
            this.handleTableData(newTime);
        },
        // 接口请求
        async getAPi() {
            let data = {};
            const param = [{
                manageProxyIp: this.nodeData.manageProxyIp,
                manageProxyPort: this.nodeData.manageProxyPort,
                pluginName: 'ldp_bizproc',
                funcName: 'GetFuncDealInfo'
            }];
            try {
                const res = await getManagerProxy(JSON.stringify(param));
                if (res.code === '200') {
                    !res.data?.[0]?.ErrorNo && Object.keys(res.data?.[0]).length && (data = res.data[0]);
                    return data;
                }
            } catch (err) {
                this.$emit('clear');
            }
        },
        // 表格数据处理
        handleTableData(newTime) {
            if (!this.wholeData) return;

            const chartData = this.funcExecData.details[0].info.chartData;
            const index = chartData.xData.indexOf(newTime);
            const pollTime = chartData.xData[index - 1] ? calculateTimeDifference(newTime, chartData.xData[index - 1]) : 1;

            // this.checkboxValue=true => 处理过滤TotalExecNum=0的数据
            const data = this.checkboxValue ? (this.wholeData || []).filter(item => item.TotalExecNum !== 0) : this.wholeData;
            // 默认根据时延大小进行数据排序
            this.tableData = data?.sort(compareObjArr('AveTotalTimeNs'))?.slice(0, this.selectTopValue);
            // 设置默认选中
            this.currentFuncNo = (this.currentFuncNo || this.currentFuncNo === 0) ? this.currentFuncNo : this.tableData?.[0]?.FunctionID;
            // 判断选中功能号是否还存在
            const funNo = _.find(this.tableData, ['FunctionID', this.currentFuncNo])?.FunctionID || this.tableData?.[0]?.FunctionID;
            if (this.currentFuncNo !== funNo) this.cleanData(); // 选中功能号改变清空折线图
            this.currentFuncNo = funNo;

            Array.isArray(this.tableData) && this.tableData.forEach(item => {
                item._highlight = this.currentFuncNo === item.FunctionID; // 表格刷新后默认选中
                // 吞吐计算公式:(Next.GetFuncDealInfo.TotalExecNum - Last.GetFuncDealInfo.TotalExecNum)/ (NextTime - LastTime)
                item.Throughput = item.lastTotalExecNum ? ((item.TotalExecNum - item.lastTotalExecNum) / pollTime).toFixed(2) : 0;
                item.FailRate = (item.FailExecNum / (item.TotalExecNum || 1) * 100).toFixed(2);
            });
            this.handleChartsData(newTime);
        },
        // 处理功能号执行详情数据
        handleChartsData(newTime) {
            this.funcExecData.title.label = `功能号执行详情 - ${this.currentFuncNo}`;
            const funcIdData = _.find(this.tableData, ['FunctionID', this.currentFuncNo]);
            this.funcExecData.details[1].infoDic = extendColumns[this.selectValue];
            this.funcExecData.details[1].info = funcIdData || {};
            const yData = {
                吞吐: funcIdData?.Throughput,
                时延: funcIdData?.[this.selectValue + 'TotalTimeNs'],
                错误次数: funcIdData?.FailExecNum,
                执行次数: funcIdData?.TotalExecNum,
                错误率: funcIdData?.FailRate * 100,
                TotalTime: funcIdData?.[this.selectValue + 'TotalTimeNs'],
                LocateTime: funcIdData?.[this.selectValue + 'LocateTimeNs'],
                QueueTime: funcIdData?.[this.selectValue + 'QueueTimeNs'],
                ExecTime: funcIdData?.[this.selectValue + 'ExecTimeNs'],
                RedoTime: funcIdData?.[this.selectValue + 'RedoTimeNs']
            };
            // 折线图轮询更新数据
            const chartNameList = [0, 2, 3];
            this.generateChart(chartNameList, yData, newTime);
        },
        generateChart(chartNameList, yData, newTime) {
            Array.isArray(chartNameList) && chartNameList.forEach(ele => {
                const chartData = this.funcExecData.details[ele].info.chartData;
                const index = chartData.xData.lastIndexOf(newTime);
                Object.keys(chartData.data).forEach(item => {
                    this.$set(chartData.data[item], index, yData?.[item]);
                });
            });
        },
        // 表格下拉框切换
        tableSelectChange(value) {
            this.selectTopValue = value;
            this.getFileData();
        },
        // checkbox切换
        handleCheckboxChange(value) {
            this.checkboxValue = value;
            this.getFileData();
        },
        // 功能号执行详情折线图下拉框切换
        chartSelectChange(value) {
            this.selectValue = value;
            this.cleanFuncChart();
            this.getFileData();
        },
        // 表格切换选中行
        tableRowcheckedChange(currentRow) {
            this.currentFuncNo = currentRow.FunctionID;
            this.cleanData();
            this.getFileData();
        },
        // 清理重置单功能号执行详情折线图
        cleanFuncChart() {
            this.funcExecData.details[0].info.chartData = {
                xData: [],
                data: {
                    吞吐: [],
                    时延: []
                }
            };
            this.funcExecData.details[2].info.chartData = {
                xData: [],
                data: {
                    TotalTime: [],
                    LocateTime: [],
                    QueueTime: [],
                    ExecTime: [],
                    RedoTime: []
                }
            };
        },
        // 清理重置折线图数据
        cleanData() {
            this.funcExecData.details[3].info.chartData = {
                xData: [],
                data: {
                    错误次数: [],
                    执行次数: [],
                    错误率: []
                }
            };
            this.cleanFuncChart();
        }
    }
};
</script>

<style lang="less" scoped>
.tab-box {
    .obs-table {
        margin: 0;
    }
}
</style>
