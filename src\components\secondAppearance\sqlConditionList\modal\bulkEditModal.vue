<template>
    <div>
        <!-- 批量编辑弹窗 -->
        <h-msg-box-safe v-model="modalData.status" :mask-closable="false" :title="modalData.title" width="602" height="112">
             <h-form
                ref="formValidate"
                :model="formData"
                :rules="ruleValidate"
                :label-width="120">
                <h-form-item label="加载方式: " prop="loadMode">
                    <h-select
                        v-model="formData.loadMode"
                        style="width: 398px;"
                        placeholder="请选择加载方式"
                        :clearable="false"
                        transfer
                    >
                        <h-option v-for="item in loadingTypeOptions" :key="item.label" :value="item.value">{{ item.label }}</h-option>
                    </h-select>
                </h-form-item>
            </h-form>
            <template v-slot:footer>
                <a-button @click="modalData.status = false">取消</a-button>
                <a-button type="primary" :loading="loading" @click="handleSubmit">确定</a-button>
            </template>
        </h-msg-box-safe>
    </div>
</template>

<script>
import { LOADING_TYPE_OPTIONS } from '@/components/secondAppearance/constant';
import aButton from '@/components/common/button/aButton';
import { batchLoadDataRule } from '@/api/brokerApi';

export default {
    props: {
        productId: {
            type: String,
            default: ''
        },
        modalInfo: {
            type: Object,
            default: null
        },
        msgBoxData: {
            type: Object,
            default: () => {}
        },
        checkedItems: {
            type: Array,
            default: () => []
        }
    },
    components: { aButton },
    data() {
        return {
            modalData: this.modalInfo,
            formData: this.msgBoxData,
            loading: false,
            ruleValidate: { loadMode: [{ required: true }] },
            loadingTypeOptions: LOADING_TYPE_OPTIONS
        };
    },
    methods: {
        // 调用接口 按SQL条件 批量编辑
        handleSubmit(){
            this.$refs['formValidate'].validate(async (valid) => {
                if (valid) {
                    const param = {
                        productId: this.productId,
                        whereCondition: 'all',
                        loadMode: Number(this.formData.loadMode),
                        tableNames: this.checkedItems.map(item => {
                            return {
                                tableName: item.tableName,
                                clusterName: item.clusterName
                            };
                        })
                    };
                    try {
                        this.loading = true;
                        const res = await batchLoadDataRule(param);
                        if (res.success){
                            this.$hMessage.success('操作成功');
                            this.$emit('query');
                            this.modalData.status = false;
                        }
                    } catch (error) {
                        console.error(error);
                    }
                    this.loading = false;
                }
            });
        }
    }
};
</script>

<style lang="less" scoped>
    /deep/ .h-modal-body {
        padding: 16px;
    }
</style>
