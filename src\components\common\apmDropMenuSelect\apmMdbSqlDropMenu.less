/* stylelint-disable property-no-unknown */
/* stylelint-disable declaration-block-no-redundant-longhand-properties */
.apm-drop-menu-select-box {
    position: relative;
    z-index: 100;

    .apm-drop-menu-select-wrap-mask {
        position: fixed;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        width: 100vw;
        height: 100vh;
        background: #1c1a0354;
        display: flex;
        justify-content: center;
        align-items: center;
    }
}

.apm-drop-menu-select {
    position: relative;
    z-index: 100;
    transition-delay: 0s, 0s, 0s, 0s;
    transition-duration: 0.2s, 0.2s, 0.2s, 0.2s;
    transition-property: width;
    transition-timing-function: ease-in-out, ease-in-out, ease-in-out, ease-in-out;

    .h-input,
    .h-input-group-append {
        background-color: transparent !important;
        border-color: transparent !important;
        cursor: pointer;

        &:focus-visible,
        &:focus-within,
        &:focus,
        &:hover {
            outline: none !important;
            box-shadow: none !important;
            border-radius: 0 !important;
            border-color: transparent !important;
        }
    }

    .h-input {
        color: #fff;
    }

    .h-input-group-append {
        padding-right: 13px;
        padding-left: 0;

        .h-icon {
            color: #fff;
        }
    }

    .h-input-icon {
        color: #fff;
    }

    .h-input-wrapper {
        border: 1px solid #485565;
        transition-behavior: normal, normal, normal, normal;
        transition-delay: 0s, 0s, 0s, 0s;
        border-radius: 4px;
        display: block;
        transition-duration: 0.2s, 0.2s, 0.2s, 0.2s;
        transition-property: border, background, box-shadow, -webkit-box-shadow;
        transition-timing-function: ease-in-out, ease-in-out, ease-in-out, ease-in-out;
        cursor: pointer;

        &:hover {
            background: #383f59;
        }
    }

    .h-checkbox {
        margin-right: 4px;
    }

    .h-checkbox + span,
    .h-checkbox-wrapper + span {
        margin: 0;
    }

    .select-search-append {
        display: flex;
        align-items: center;

        .clear-icon {
            width: 14px;
            height: 14px;
            margin-right: 5px;
        }
    }

    .apm-drop-menu-select-wrap {
        position: absolute;
        z-index: 100;
        width: 75vw;
        height: 60vh;
        top: 100%;
        left: 0;
        background-color: #262d43;
        border: 1px solid #485565;
        box-shadow: 0 1px 6px rgba(0, 0, 0, 0.2);
        padding: 15px;
        border-radius: 4px;

        .no-data {
            .text {
                color: #fff;
                font-size: 12px;
                padding: 0 10px;
            }
        }

        .apm-drop-menu-select-wrap-list {
            width: 100%;
            display: flex;
            color: #fff;
            margin: 15px 0;

            .apm-drop-menu-select-wrap-key {
                font-size: 12px;
                flex: 0 0 auto;
                width: 12%;
                min-width: 130px;
                text-align: center;
                word-wrap: break-word;
                display: flex;
                flex-wrap: wrap;
                color: #fff;

                .apm-drop-menu-select-wrap-key-part {
                    font-size: 12px;
                    flex: 1;
                    margin: 0 6px;
                    line-height: 20px;
                    text-align: center;
                    white-space: nowrap;
                    overflow: hidden;
                    text-overflow: ellipsis;
                    box-sizing: border-box;
                }

                .apm-drop-menu-select-wrap-key-part-hover {
                    &:hover {
                        background: #1f3759;
                        cursor: pointer;
                    }
                }
            }

            .panel-key .apm-drop-menu-select-wrap-key-part {
                color: #fff;

                .apm-drop-menu-select-wrap-key-part-span {
                    width: 100%;
                    display: inline-block;
                    background: #2c334a;
                    padding: 6px 8px;
                    white-space: nowrap;
                    overflow: hidden;
                    text-overflow: ellipsis;
                }
            }

            .panel-key .h-checkbox-wrapper.apm-drop-menu-select-wrap-key-part {
                padding: 6px 8px;
                text-align: left;
                border-right: 1px solid #485565;
            }

            .panel-key .h-radio-wrapper.apm-drop-menu-select-wrap-key-part {
                padding: 6px 8px;
                text-align: left;
                border-right: 1px solid #485565;
            }

            .apm-drop-menu-select-wrap-key.sub-key {
                width: 12%;

                .apm-drop-menu-select-wrap-key-part {
                    padding: 6px 8px;
                    text-align: left;
                }
            }

            .apm-drop-menu-select-wrap-sub-part {
                flex: 1;
            }

            .apm-drop-menu-select-wrap-nodes {
                width: 100%;
                display: flex;
            }

            .apm-drop-menu-select-wrap-values {
                font-size: 12px;
                flex: 1;
                padding: 0 8px;
                color: #fff;
                border-left: 1px solid rgba(255, 255, 255, 0.15);

                .apm-drop-menu-select-wrap-value {
                    font-size: 12px;
                    display: inline-block;
                    padding: 4px 8px;
                    line-height: 20px;
                    color: #fff;
                    border-radius: 2px;
                    margin: 2px 6px;
                    cursor: pointer;
                    position: relative;

                    &:hover {
                        background: #1f3759;
                    }
                }

                .apm-drop-menu-select-wrap-value[data-active="true"] {
                    background: #1f3759;
                }

                .option-text[data-match="true"] {
                    color: #2d8de5;
                }

                .tip {
                    position: absolute;
                    width: 6px;
                    height: 6px;
                    right: 0;
                    top: 0;
                    transform: scaleX(-1);
                    background: #b3b6bd;
                    border-radius: 0 0 6px;
                    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
                }

                .main-flag {
                    .node-flag("static/mainFlag.png");
                }
            }
        }


        .apm-drop-menu-select-wrap-footer {
            float: right;

            .h-btn {
                margin: 5px;
            }
        }

        // tab 样式
        .apm-drop-menu-select-tab.h-tabs {
            margin-top: 10px;
            height: calc(100% - 20px);

            .h-tabs-content-wrap {
                height: calc(100% - 60px);
                overflow-y: auto;
            }

            .h-tabs-bar {
                margin-bottom: 0;
                padding: 1px 0;
            }

            .h-tabs-tab {
                padding: 4px 8px;
                border-radius: 0;
                font-size: 12px;
            }

            .h-tabs-tab-active {
                border-bottom: 1px solid #298dff !important;
            }

            .h-tabs-enter,
            .h-tabs-return {
                padding: 4px;

                .h-icon {
                    font-size: 12px;
                }
            }
        }

        .apm-drop-menu-select-simple-tab {
            height: calc(100% - 60px);
            overflow-y: auto;
        }
    }


    .h-spin-fix {
        background-color: #2c324df2;
    }

    .spin-icon-load {
        animation: ani-demo-spin 1s linear infinite;
        display: inline-block;
    }

    @keyframes ani-demo-spin {
        from {
            transform: rotate(0deg);
        }

        50% {
            transform: rotate(180deg);
        }

        to {
            transform: rotate(360deg);
        }
    }

    // 模拟select多选样式
    .apm-drop-menu-select-tag-container {
        display: flex;
        height: 32px;
        padding: 0 5px 0 0;
        line-height: 28px;
        justify-content: center;
        overflow-y: auto;
        border: 1px solid #485565;
        transition-behavior: normal, normal, normal, normal;
        transition-delay: 0s, 0s, 0s, 0s;
        border-radius: 4px;
        transition-duration: 0.2s, 0.2s, 0.2s, 0.2s;
        transition-property: border, background, box-shadow, -webkit-box-shadow;
        transition-timing-function: ease-in-out, ease-in-out, ease-in-out, ease-in-out;
        cursor: pointer;

        .placeholder-span {
            margin-left: 10px;
            color: #ccc;
            flex: 1;
            box-sizing: border-box;
        }

        .apm-drop-menu-select-tag {
            flex: 1;
            box-sizing: border-box;
        }

        .apm-drop-menu-select-tag-append {
            flex: 0 0 auto;
            max-width: 35px;
            margin-left: 5px;
            text-align: right;
            position: sticky;
            top: 0;
            right: 0;
            box-sizing: border-box;
            z-index: 1;
        }

        .h-icon {
            color: #fff;
        }

        .clear-icon {
            width: 14px;
            height: 14px;
            margin-right: 5px;
        }

        .a-tag .h-tag {
            padding: 0 4px;
            background: #33394e;
            border: 1px solid #485565;
            border-radius: 2px;
            color: #fff;
        }

        .a-tag .h-icon {
            color: #9296a1 !important;
        }
    }

    // text模式
    .apm-drop-menu-select-tag-container.apm-drop-menu-text {
        height: 34px;
        line-height: 32px;
        color: #fff;
        margin: 0 10px;
        border: none;
    }

    .tips-content .tip-text {
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
    }

    .node-flag(@url) {
        display: inline-block;
        width: 11px;
        height: 11px;
        background: url(@url);
        background-size: 11px 10px;
        background-repeat: no-repeat;
        position: relative;
        top: 2px;
        margin-left: 5px;
    }
}
