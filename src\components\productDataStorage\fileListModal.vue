<template>
    <div>
      <h-msg-box
        v-model="modalData.status"
        :escClose="true"
        title="待归档数据清单"
        footerHide
        width="70"
        maxHeight="240"
        @on-open="handleOpen"
      >
      <a-table
        ref="table"
        :tableData="tableData"
        :columns="columns"
        :loading="loading"
        :total="total"
        :hasPage="false"
        :hasDarkClass="false"
        :simple="true"
        :showSizer="false"
        :showElevator="false"
        />
        <p style="font-size: var(--font-size); margin: 10px 0 5px;">总计:索引文件: <strong>{{ indexTotals }}</strong> 个; 文件条数: <strong>{{ docsTotals }}</strong> 条; 存储空间: <strong>{{ storeSizeSum }}</strong></p>
      </h-msg-box>
    </div>
  </template>
<script>
import aTable from '@/components/common/table/aTable';
export default {
    props: {
        modalInfo: {
            type: Object,
            default: null
        }
    },
    data() {
        return {
            modalData: this.modalInfo,
            loading: false,
            columns: [
                {
                    title: '索引名',
                    key: 'index'
                },
                {
                    title: '文件条数',
                    key: 'docsCount'
                },
                {
                    title: '占用空间',
                    key: 'storeSize',
                    render: (h, params) => {
                        return h('span', {}, `${params.row.storeSize.replace(/[a-z](?=\d)|\d(?=[a-z])/gi, '$& ').toUpperCase()}`);
                    }
                }
            ],
            total: 0,
            tableData: [],
            indexTotals: 0,
            docsTotals: 0,
            storeSizeSum: '0 GB'
        };
    },
    methods: {
        handleOpen(){

        }
    },
    components: { aTable }
};
</script>

