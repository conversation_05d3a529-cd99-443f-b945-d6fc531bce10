
.create-container {
    display: flex;
    flex-direction: column;
    // overflow-y: auto;
}

.create-rule {
    overflow-y: auto;
    height: 100%;

    .verify-tip.verify-bottom {
        padding: 0 !important;
        margin-top: 0 !important;
    }

    .verify-tip.verify-bottom > .verify-tip-arrow {
        top: -5px !important;
    }

    &-step {
        margin: 23px 0;
        min-width: 930px;
        max-width: 1400px;

        .h-steps-main {
            background-color: var(--main-color) !important;
        }

        .h-steps-item.h-steps-status-process .h-steps-title {
            font-weight: 500;
        }
    }


    &-wrap {
        flex: 1;
        overflow-y: auto;
    }

    &-content {
        color: #fff;
        min-width: 930px;
        max-width: 1400px;

        button[disabled="disabled"] {
            color: #969797 !important;
        }

        textarea {
            resize: none;
            height: 99px;
            line-height: 20px;
        }

        &-tips {
            background: #262d43;
            border-radius: 4px;
            padding: 15px;
            color: #cacfd4;
        }

        &-form {
            margin-top: 20px;

            .h-select-placeholder {
                color: #9296a1 !important;
            }

            textarea::placeholder {
                line-height: 20px;
                color: #9296a1;
                font-family: PingFangSC-Regular;
            }

            &-pre {
                background: #262d43;
                border-radius: 4px;
                padding: 15px;
                margin-left: 103px;

                &-title {
                    display: flex;
                    align-items: center;

                    &::before {
                        content: "";
                        margin-right: 8px;
                        background: #2d8de5;
                        width: 3px;
                        height: 16px;
                    }
                }

                &-alert {
                    background: #1f3759;
                    border-radius: 4px;
                    border: none;
                    color: #fff;
                    margin-top: 15px;


                    .h-alert-message {
                        display: flex;
                        align-items: center;
                    }

                    i {
                        color: #2d8de5;
                        margin-right: 9px;
                        position: relative;
                        top: 1px;
                    }

                    &-download {
                        &-icon {
                            top: 0 !important;
                            margin-right: 0 !important;
                        }
                    }
                }

                .h-form-item {
                    margin-bottom: 15px;
                }

                textarea {
                    resize: none;
                    height: 128px;
                    line-height: 20px;
                }

                &-btn {
                    margin-left: 103px;

                    &-tip {
                        margin-left: 12px;
                        color: #9296a1;
                    }
                }
            }

            .h-form-item-label {
                color: #fff;

                .h-poptip {
                    i {
                        color: #9296a1;
                    }
                }
            }

            .h-radio-group-item {
                background-color: unset !important;
                border-color: unset !important;
                color: unset !important;
            }

            &-var {
                display: flex;
                margin-top: 20px;

                &-label {
                    width: 103px;
                    text-align: right;
                }

                &-list {
                    flex: 1;
                    background: #262d43;
                    border-radius: 4px;
                    color: #cacfd4;
                    padding: 4px;
                    min-height: 68px;
                    display: flex;
                    flex-wrap: wrap;

                    &-item {
                        background: #1f3759;
                        border-radius: 4px;
                        padding: 5px 8px;
                        margin: 4px;
                        height: 27px;
                        box-sizing: border-box;
                        color: #fff;
                    }
                }
            }

            &-run {
                margin-top: 20px;
                margin-bottom: 41px;

                &-container {
                    display: flex;
                    flex-direction: column;

                    &-download {
                        color: #2d8de5;
                        margin-top: 3px;
                        cursor: pointer;

                        i {
                            margin-right: 2px;
                        }
                    }
                }

                &-wrap {
                    padding: 15px;
                    flex: 1;
                    display: flex;
                    flex-direction: column;
                    background: #262d43;
                    margin-top: 14px;
                    border-radius: 4px;

                    &-alert {
                        background: #1f3759;
                        border-radius: 4px;
                        border: none;
                        color: #fff;

                        .h-alert-message {
                            display: flex;
                            align-items: center;
                        }

                        i {
                            color: #2d8de5;
                            margin-right: 9px;
                            position: relative;
                            top: 1px;
                        }
                    }
                }

                textarea {
                    resize: none;
                    height: 252px;
                    line-height: 20px;
                }

                &-label {
                    width: 103px;
                }

                &-item {
                    flex: 1;
                    display: flex;

                    &-test {
                        margin-left: 68px;
                        margin-bottom: 24px;

                        &-result {
                            background: #2c334a;
                            border-radius: 4px;

                            textarea {
                                height: 100px;
                                border: 1px solid #485565;
                                line-height: 20px;
                                background-color: #2c334a !important;
                            }
                        }
                    }

                    &-left,
                    &-right {
                        flex: 1;

                        &-title {
                            display: flex;
                            align-items: flex-start;

                            &-label {
                                width: 68px;
                                border-left: 3px solid #2d8de5;
                                padding-left: 8px;
                                margin-top: 8px;
                            }

                            &-input {
                                flex: 1;
                            }
                        }
                    }

                    &-left {
                        border-right: 1px solid #444a60;
                        padding-right: 15px;
                    }

                    &-right {
                        margin-left: 15px;
                    }

                    .h-form-item-label {
                        width: 68px !important;
                    }

                    .h-form-item-content {
                        margin-left: 68px !important;
                    }

                    /* stylelint-disable-next-line selector-class-pattern */
                    .h-form-item-reqNoLabel {
                        .h-form-item-content {
                            margin-left: 0 !important;
                        }

                        /* stylelint-disable-next-line selector-class-pattern */
                        .h-form-item-requiredIcon {
                            display: none;
                        }
                    }
                }
            }
        }

        .form-border {
            position: relative;
            margin-top: 0;
            padding-top: 20px;

            &::before {
                content: "";
                position: absolute;
                width: calc(100% - 103px);
                top: 0;
                left: 103px;
                height: 1px;
                background-color: #444a60;
            }
        }
    }

    &-footer {
        padding: 12px;
        border-top: 1px solid #444a60;
        display: flex;
        align-items: center;

        &-cancel {
            margin-left: 8px;
        }

        &-line {
            background: #444a60;
            width: 1px;
            height: 100%;
            margin: 0 8px;
        }
    }
}

.create-rule-title {
    // height: 48px;
    // background: #2c324d;
    // border-radius: 4px;
    // display: flex;
    // align-items: center;
    // justify-content: space-between;
    // padding: 8px 16px;

    &-text {
        font-size: 14px;
        color: #fff;
        flex: 1;

        a {
            color: #fff;
        }

        i {
            cursor: pointer;
        }
    }

    .select {
        width: auto;
    }
}


.create-rule-poptip {
    .h-poptip-body {
        padding: 6px 8px !important;
    }
}
