<!-- /**
 * <AUTHOR>
 * @email <EMAIL>
 * @create date 2024-09-10 11:07:29
 * @modify date 2024-09-10 11:07:29
 * @desc [中心内配置]
 */ -->
<template>
  <div class="inner">
    <a-title title="中心内配置（Zones）">
      <slot>
        <a-button type="dark" class="inner-add" @click="onAdd"> 新增 </a-button>
      </slot>
    </a-title>

    <div class="inner-tablle">
      <a-table
        ref="table"
        :tableData="tableData"
        :loading="loading"
        :columns="columns"
        :hasPage="false"
        showTitle
      />
    </div>

    <inner-zone-modal v-if="modal.status" :rcmId="rcmId" />
  </div>
</template>

<script>
import { defineComponent, getCurrentInstance, ref } from 'vue';
import aTitle from '@/components/common/title/aTitle';
import aButton from '@/components/common/button/aButton';
import aTable from '@/components/common/table/aTable';
import { deleteZoneConfig } from '@/api/rcmApi';
import innerZoneModal from './innerZongModal.vue';
import { useInnerZoneModal, useZonesList } from './hooks';
import { SYNC_MODEL_LIST } from './constant';

export default defineComponent({
    name: 'InnerZone',
    props: {
        rcmId: String
    },
    setup(props, context) {
        const { onAdd, onEdit, modal } = useInnerZoneModal();
        const { list, fetchData } = useZonesList();
        const { proxy } = getCurrentInstance();
        const loading = ref(false);
        /**
     * 删除配置
     */
        const handleDeleteRule = async (id, name) => {
            const res = await deleteZoneConfig({ rcmId: props.rcmId, id });
            if (res.code === '200') {
                await initData();
                const zoneName = name.length > 10 ? name.slice(0, 10) + '...' : name;
                proxy.$hMessageSafe.success({
                    content: `中心内配置 ${zoneName} 已删除`,
                    duration: 3
                });
            }
        };
        const columns = [
            {
                title: '中心名',
                key: 'name'
            },
            {
                title: '同中心节点间RPO值',
                key: 'rpo'
            },
            {
                title: '消息同步的确认时刻',
                key: 'syncAckPoint',
                render: (h, params) =>
                    h(
                        'span',
                        {},
                        SYNC_MODEL_LIST.find(
                            (item) => item.value === params.row.syncAckPoint
                        )?.name || '-'
                    )
            },
            {
                title: '关联上下文个数',
                key: 'relationContextSize',
                render: (h, params) => {
                    return h(
                        'a',
                        {
                            on: {
                                click: () => {
                                    const { relationContextSize } = params.row;
                                    if (
                                        !relationContextSize ||
                    Number(relationContextSize) === 0 ||
                    isNaN(relationContextSize)
                                    )
                                        return;
                                    // 跳转
                                    context.emit('turnToContext', params.row.name);
                                }
                            }
                        },
                        params.row.relationContextSize
                    );
                }
            },
            {
                title: '操作',
                key: 'action',
                fixed: 'right',
                width: 110,
                render: (h, params) => {
                    return h('div', [
                        h(
                            'Button',
                            {
                                props: {
                                    size: 'small',
                                    type: 'text'
                                },
                                on: {
                                    click: () => {
                                        onEdit(params.row);
                                    }
                                }
                            },
                            '编辑'
                        ),
                        h(
                            'Button',
                            {
                                props: {
                                    size: 'small',
                                    type: 'text'
                                },
                                on: {
                                    click: () => {
                                        proxy.$hMsgBoxSafe.confirm({
                                            title: `删除`,
                                            content: `您确认要删除名为"${params.row.name}"的配置吗？`,
                                            onOk: async () =>
                                                handleDeleteRule(params.row.id, params.row.name)
                                        });
                                    }
                                }
                            },
                            '删除'
                        )
                    ]);
                }
            }
        ];
        const initData = async () => {
            try {
                loading.value = true;
                await fetchData(props.rcmId);
            } finally {
                loading.value = false;
            }
        };

        return {
            columns,
            onAdd,
            modal,
            onEdit,
            tableData: list,
            initData,
            loading
        };
    },
    components: { aTitle, aButton, aTable, innerZoneModal }
});
</script>

<style scoped lang="less">
.inner {
    position: relative;

    &-add {
        position: absolute;
        right: 6px;
        top: 5px;
    }
}
</style>
