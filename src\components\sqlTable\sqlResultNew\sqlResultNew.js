/*
 * @Description: sql执行结果集
 * @Author: <PERSON><PERSON>
 * @Date: 2023-08-24 15:43:07
 * @LastEditTime: 2023-11-29 17:58:10
 * @LastEditors: yingzx38608 <EMAIL>
 */
import _ from 'lodash';
import { exportFile, exportCsv } from '@/utils/utils';
import './sqlResult.less';
import '@/assets/css/poptip-1.less';
import aTable from '@/components/common/table/aTable';
import { getMemoryDataBySql } from '@/api/memoryApi';
import { MDB_NO_LOGIN } from '@/config/errorCode';

export default {
    name: 'sqlResult',
    components: { aTable },
    emits: ['onChangePage', 'callSetLoginInfo'],
    props: {
        resultList: {
            type: Array,
            default: () => []
        },
        // 多核心模式
        isMutipleCores: {
            type: Boolean,
            default: () => false
        },
        coreList: {
            type: Array,
            default: () => []
        },
        clusterRoles: {
            type: Object,
            default: () => {}
        },
        height: {
            type: Number,
            default: 100
        }
    },
    data() {
        return {
            isExportingFile: true,
            tabName: '',
            page: 1,
            pageSize: 10,
            total: 0,
            label: (item) => (h) => {
                const badge = this.clusterRoles?.[item?.id]?.clusterRole === 'ARB_ACTIVE';
                return h('div', {
                    class: 'option-text'
                }, [
                    h('span', item.label),
                    badge ? h('span', { class: 'main-flag' }) : ''
                ]);
            }
        };
    },
    computed: {
        tableData: function () {
            const tData = _.find(this.isMutipleCores ? this.sqlResultList : this.resultList, ['label', this.tabName])?.data || [];
            return tData;
        },
        sqlResultList() {
            return this.resultList;
        }
    },
    mounted() {
    },
    methods: {
        handlePageChange(index) {
            const curTable = this.sqlResultList.find(item => item.label === this.tabName);
            const data = this.$refs[`table_${this.tabName}`].getPageData();
            this.$emit('handlePageChange', {
                index,
                pageNo: data.page,
                pageSize: data.pageSize,
                onSetPage: (newPageNo, newPageSize) => {
                    this.$nextTick(() => {
                        this.$refs[`table_${curTable.label}`].setPageSizeInfo(newPageNo, newPageSize);
                    });
                }
            });
        },
        resetPage() {
            this.$nextTick(() => {
                const curTable = this.sqlResultList.find(item => item.label === this.tabName);
                this.$refs[`table_${curTable.label}`] && this.$refs[`table_${curTable.label}`].setPageSizeInfo(curTable.page, curTable.pageSize);
            });
        },
        // 导出表格数据
        async downTableData(index, tableRecords) {
            const data = this.sqlResultList[index];
            // this.$refs[`table_${this.tabName}`].exportCsv({
            //     filename: data.label,
            //     columns: data.columns,
            //     data: tableRecords
            // });
            exportCsv(data.label, data.columns, tableRecords);
        },
        // 导出SQL数据
        downSqlData(index, tableRecords) {
            const data = this.sqlResultList[index];
            const ast = data.ast;
            let sqlStr = '';
            let columns = '(';
            data.columns.forEach((ele, idx) => {
                columns += '`' + ele.key + '`';
                if (idx !== (data.columns.length - 1)) {
                    columns += ',';
                }
            });
            columns += ')';

            tableRecords.forEach(item => {
                let values = '';
                Object.values(item).forEach((ele, idx) => {
                    values += typeof (ele) === 'number' ? ele : `'${ele}'`;
                    if (idx !== (data.columns.length - 1)) {
                        values += ',';
                    }
                });
                sqlStr += `INSERT INTO \`${ast.from[0].table}\` ${columns} VALUES (${values});\n`;
            });
            exportFile(sqlStr, `SQL${index + 1}`, 'sql');
        },
        // 导出表格数据
        async exportCsv(val, index) {
            this.isExportingFile = true;
            const curTable = this.sqlResultList[index];
            try {
                const tableRes = await getMemoryDataBySql({
                    ...curTable.param,
                    instanceId: curTable.id,
                    page: 1,
                    sql: curTable.sql,
                    pageSize: 10000
                }, curTable.param.performSqlTimeout + 2000);
                if (tableRes.code === MDB_NO_LOGIN){
                    this.$emit('callSetLoginInfo');
                    return;
                }
                if (!tableRes.success) return;
                const tableRecords = tableRes.data.tableRecords || [];
                if (val === '导出表格') {
                    this.downTableData(index, tableRecords);
                } else {
                    this.downSqlData(index, tableRecords);
                }
            } finally {
                this.isExportingFile = false;
            }
        },
        // 清空选中tab
        clearData() {
            this.tabName = '';
        }
    },
    watch: {
        resultList(newVal) {
            if (newVal.length) {
                this.tabName = this.tabName ? this.tabName : newVal[0].label;
            }
        }
    },
    render() {
        return (
            <h-tabs
                ref="sqlResult"
                class="sql-result"
                v-model={this.tabName}
                showArrow
                v-on:on-click={this.resetPage}>
                {this.sqlResultList.map((item, index) => {
                    return (
                        <h-tab-pane key={item.key} label={item.id ? this.label(item) : item.label } name={item.label}>
                            {!item.errorText ? (
                                (this.coreList?.length || (this.tabName === item.label)) ? (
                                    <div>
                                        <h-row style="color: #CACFD4; padding: 8px 10px 0;">
                                            <h-col span="14" style="display: flex; align-items: center;">
                                                <h-poptip placement="top-start" transfer customTransferClassName="apm-poptip monitor-poptip">
                                                    {item.sql}
                                                    <div class="pop-content" slot="content" style='white-space: normal;'>{item.sql}</div>
                                                </h-poptip>
                                            </h-col>
                                            <h-col span="10" style="text-align: right;">
                                                <div class="export-tip-wrap">
                                                    <div>
                                                        {
                                                            (item.time === '-' || !item.time)
                                                                ? <span>执行时间：-ms&nbsp;</span>
                                                                :                                                                                                                        <h-poptip trigger="hover" class="apm-poptip-time" transfer customTransferClassName="apm-poptip apm-poptip-time monitor-poptip">
                                                                    执行时间：{item.totalTime}ms&nbsp;
                                                                    <div class="pop-content" slot="content" style='white-space: normal;'>
                                                                        <div>
                                                                            <span class="time-item-label">整体耗时：</span>
                                                                            {item.totalTime}ms
                                                                        </div>
                                                                        <div>
                                                                            <span class="time-item-label">APM耗时：</span>
                                                                            {item.apmTime}ms
                                                                        </div>
                                                                        <div>
                                                                            <span class="time-item-label">MDB耗时：</span>
                                                                            {item.sqlRunTime}ms
                                                                        </div>
                                                                    </div>
                                                                </h-poptip>
                                                        }
                                                        <h-dropdown v-on:on-click={(val) => { this.exportCsv(val, index); }}>
                                                            <a href="javascript:void(0)">
                                                                数据导出
                                                                <h-icon name="unfold"></h-icon>
                                                            </a>
                                                            <h-dropdown-menu slot="list">
                                                                <h-dropdown-item disabled={!item.isExport || !this.tableData.length}>导出表格</h-dropdown-item>
                                                                <h-dropdown-item disabled={!item.isExport || !this.tableData.length}>导出SQL</h-dropdown-item>
                                                            </h-dropdown-menu>
                                                        </h-dropdown>
                                                    </div>
                                                    <div>
                                                        <h-poptip placement="left" class="export-tip" transfer positionFixed customTransferClassName="apm-poptip monitor-poptip">
                                                            <h-icon name="feedback_fill" size="20" color="#999" style="cursor: pointer; margin-left: 6px; position: relative; top: -4px;"></h-icon>
                                                            <div class="pop-content" slot="content">最多导出一万条数据。</div>
                                                        </h-poptip>
                                                    </div>
                                                </div>
                                                {/* <h-icon class="icon-down" name='t-b-download' v-on:on-click={() => { this.downTableData(index); }} /> */}
                                            </h-col>
                                        </h-row>
                                        <a-table
                                            ref={`table_${item.label}`}
                                            columns={item.columns || []}
                                            border
                                            total={item.totalCount ?? item.data?.length}
                                            tableData={this.tableData}
                                            height={this.height}
                                            loading={item.loading}
                                            canDrag
                                            showTotal
                                            showTitle
                                            v-on:query={() => this.handlePageChange(index)}
                                            hPageSize='small'
                                            noDataText="当前查询无数据返回"
                                        ></a-table>
                                    </div>
                                ) : (
                                    ''
                                )
                            ) : (
                                <p>ERROR: {item.errorText}</p>
                            )}
                        </h-tab-pane>
                    );
                })}
            </h-tabs>
        );
    }
};
