<template>
    <div class="wrapper">
        <div>
            <!-- 应用类型拓扑 -->
            <ldp-node-topo
                v-if="nodes.length"
                ref="overviewTopology"
                type="service"
                :productId="productId"
                :nodes="nodes"
                :edges="edges"
                @handleSearchCluster="handleSearchCluster"
                @getMonitorEvents="getMonitorEvents" />
            <no-data v-else  text="暂无数据" />
            <div v-if="nodes.length" class="title-bottom" @click="handleOpenDrawer">全局总览</div>
            <a-loading v-if="loading" style="width: 100%; height: calc(100% - 92px); top: 92px;"></a-loading>
        </div>
        <alarm-drawer ref="alarmDrawer" :productId="productId" />
    </div>
</template>

<script>
import { getObservableAppTypeTopology } from '@/api/topoApi';
import noData from '@/components/common/noData/noData';
import aLoading from '@/components/common/loading/aLoading';
import ldpNodeTopo from '@/components/common/topo/ldpNodeTopo';
import alarmDrawer from '@/components/ldpProduct/alarmDrawer/alarmDrawer';
export default {
    name: 'LdpClusterObserver',
    components: { ldpNodeTopo, noData, aLoading, alarmDrawer },
    props: {
        productId: {
            type: String,
            default: ''
        }
    },
    data() {
        return {
            topoType: 'all',
            clusterName: '',
            nodeList: [],
            edgesList: [],
            timer: null,
            loading: false,
            timeLoading: false
        };
    },
    computed: {
        edges: {
            get: function() {
                if (this.clusterName) {
                    const newEdges = [];
                    this.edgesList.forEach(ele => {
                        if (ele.fromName === this.clusterName) {
                            newEdges.push(ele);
                        } else if (ele.toName === this.clusterName) {
                            newEdges.push(ele);
                        }
                    });
                    return newEdges;
                }
                return this.edgesList;
            }
        },
        nodes: {
            get: function() {
                if (this.clusterName) {
                    const list = [this.clusterName];
                    this.edgesList.forEach(ele => {
                        if (ele.fromName === this.clusterName) {
                            list.push(ele.toName);
                        } else if (ele.toName === this.clusterName) {
                            list.push(ele.fromName);
                        }
                    });
                    const setArr = new Set(list);
                    const newList = [];
                    for (const item of this.nodeList) {
                        if (!setArr.size) break;
                        if (setArr.has(item.nodeName)) {
                            newList.push(item);
                            setArr.delete(item.nodeName);
                        }
                    }
                    return newList;
                }
                return this.nodeList;
            }
        },
        showCluster: {
            get: function() {
                const list = [];
                this.nodeList.forEach(ele => {
                    list.push(ele.nodeName);
                });
                return [...new Set(list)];;
            }
        }
    },
    beforeDestroy() {
        this.clearPolling();
    },
    methods: {
        async init() {
            this.loading = true;
            this.clearData();
            try {
                // 设置轮询
                this.setPolling();
                // 获取topo结构
                await this.getObservableAppTypeTopology();
            } catch (error) {
                console.error(error);
                this.clearPolling();
            }
            this.loading = false;
        },
        clearData() {
            this.nodeList = [];
            this.edgesList = [];
            this.clusterName = '';
        },
        setPolling() {
            this.clearPolling();
            this.timer = setInterval(async () => {
                if (this.timeLoading) return;
                await this.getObservableAppTypeTopology();
            }, 10000);
        },
        clearPolling() {
            this.timer && clearInterval(this.timer);
        },
        // 切换查询集群名-前端过滤节点
        handleExchange() {
            this.getObservableAppTypeTopology();
        },
        // 搜索集群获取相关连接关系
        handleSearchCluster(nodeName) {
            this.clusterName = nodeName;
        },
        stopProcess() {
            this.nodeList = [];
            this.edgesList = [];
            this.clearPolling();
        },
        // 获取应用连接关系
        async getObservableAppTypeTopology() {
            this.timeLoading = true;
            const param = {
                productId: this.productId,
                type: 'service'
            };
            try {
                const res = await getObservableAppTypeTopology(param);
                if (this.productId !== param.productId) return;
                if (res.success) {
                    this.nodeList = res?.data?.nodes;
                    this.edgesList = res?.data?.edges || [];
                    this.$nextTick(() => {
                        this.$refs['overviewTopology'] && this.$refs['overviewTopology'].init();
                    });
                    // 如果请求成功，重新开启定时器
                    // if (!this.timer) {
                    //     this.setPolling();
                    // }
                } else {
                    this.stopProcess();
                }
            } catch (error){
                console.error(error);
                this.clearPolling();
            }
            this.timeLoading = false;
        },
        // 打开告警弹窗
        getMonitorEvents(id) {
            this.$refs['alarmDrawer'].init('service', id);
        },
        // 打开连接关系弹窗
        handleOpenDrawer() {
            this.$nextTick(() => {
                const nodeStatuses = this.$refs['overviewTopology'].getLegendSelectedStatus();
                if (this.$refs['overviewTopology']) {
                    this.$refs['overviewTopology'].viewAllConnection({
                        nodeType: 'service',
                        nodeStatuses: nodeStatuses
                    });
                }
            });
        }
    }
};
</script>
<style lang="less" scoped>
@import url("@/assets/css/input.less");

.wrapper {
    width: 100%;
    height: 100%;
    padding: 0 10px;
    background-color: var(--main-color);

    & > div {
        width: 100%;
        height: 100%;
        min-width: 900px;
    }

    .title-bottom {
        position: absolute;
        left: 1px;
        bottom: 2px;
        padding-left: 20px;
        width: calc(100% - 2px);
        color: #fff;
        font-size: 14px;
        line-height: 40px;
        background: var(--wrapper-color);
        border-radius: 4px 4px 0 0;
        border: var(--border);
        cursor: pointer;
    }
}
</style>
