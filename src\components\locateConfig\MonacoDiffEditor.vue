<template>
    <div ref="code-container" class="monaco-diff-editor"></div>
  </template>

<script>
import * as monaco from 'monaco-editor';

export default {
    props: {
        originalCode: {
            type: String,
            required: true
        },
        modifiedCode: {
            type: String,
            required: true
        },
        language: {
            type: String,
            required: 'json'
        }
    },
    data() {
        return {
            originalModel: null,
            modifiedModel: null,
            diffEditor: null
        };
    },
    mounted() {
        this.initializeMonaco();
    },
    beforeDestroy() {
        this.destroyMonaco();
    },
    watch: {
        originalCode(newVal) {
            this.originalModel.setValue(newVal);
        },
        modifiedCode(newVal) {
            this.modifiedModel.setValue(newVal);
        },
        language(newVal) {
            if (this.originalModel && this.modifiedModel) {
                this.updateModelLanguage(newVal);
            }
        }
    },
    methods: {
        // 初始化
        initializeMonaco() {
            this.diffEditor = monaco.editor.createDiffEditor(this.$refs['code-container'], {
                automaticLayout: true,
                language: this.language,
                theme: 'vs-dark', // 可根据需要设置主题
                folding: true, // 启用折叠功能
                colorDecorators: true, // 颜色装饰器
                originalEditable: false, // 允许对比中的原始内容可编辑
                renderSideBySide: true, // 是否并排渲染
                scrollBeyondLastLine: false, // 禁用超出最后一行的滚动
                enableSplitViewResizer: false, // 禁用分隔栏拖动
                renderOverviewRuler: false, // 关闭右侧预览
                minimap: {
                    enabled: false // 关闭右下角的小地图
                },
                wordWrap: 'on',  // 设置自动换行
                contextmenu: false // 关闭右键菜单
            });
            this.createModels();
        },
        // 创建容器
        createModels() {
            if (this.originalModel) {
                this.originalModel.dispose();
            }
            if (this.modifiedModel) {
                this.modifiedModel.dispose();
            }
            this.originalModel = monaco.editor.createModel(this.originalCode,  this.language);
            this.modifiedModel = monaco.editor.createModel(this.modifiedCode,  this.language);

            this.diffEditor.setModel({
                original: this.originalModel,
                modified: this.modifiedModel
            });

            // 确保两个编辑器的编辑功能被禁用
            setTimeout(() => {
                this.diffEditor.getOriginalEditor().updateOptions({ readOnly: true });
                this.diffEditor.getModifiedEditor().updateOptions({ readOnly: true });
            }, 0);
        },
        // 更新语言
        updateModelLanguage(language) {
            monaco.editor.setModelLanguage(this.originalModel, language);
            monaco.editor.setModelLanguage(this.modifiedModel, language);
        },
        copyOriginal() {
            this.triggerCopy(this.diffEditor.getOriginalEditor());
        },
        copyModified() {
            this.triggerCopy(this.diffEditor.getModifiedEditor());
        },
        async triggerCopy(editor) {
            // 聚焦编辑器
            editor.focus();
            // Select all text
            const model = editor.getModel();
            const fullRange = model.getFullModelRange();
            editor.setSelection(fullRange);

            try {
                // 把选中的文本转为字符串
                const selectedText = editor.getModel().getValueInRange(editor.getSelection());
                // 使用 Clipboard API 复制文本
                await navigator.clipboard.writeText(selectedText);
            } catch (err) {
                console.log('复制比对结果失败原因: ' + err?.message);
            }

            // 复制成功后取消全选
            const position = editor.getPosition();
            editor.setSelection({
                startLineNumber: position.lineNumber,
                startColumn: position.column,
                endLineNumber: position.lineNumber,
                endColumn: position.column
            });
        },
        // 销毁
        destroyMonaco() {
            if (this.diffEditor) {
                this.diffEditor.dispose();
            }
            if (this.originalModel) {
                this.originalModel.dispose();
            }
            if (this.modifiedModel) {
                this.modifiedModel.dispose();
            }
        }
    }
};
</script>

<style scoped>
.monaco-diff-editor {
    width: 100%;
    height: 100%;
}

/* 隐藏分隔栏 */
/deep/ .monaco-diff-editor .monaco-sash {
    cursor: default;
}
</style>
