<template>
    <div class="container-chart">
        <div v-if="option.xAxis.data.length" ref="echart" class="market-canvas"></div>
        <no-data v-else  text = "当前行情系统暂无时延走势"/>
        <div class="market-data">
            <p>{{ lastTimeData.time }} 系统全链路时延</p>
            <div class="market-data-wrapper">
                <div class="market-data-wrapper-left">
                    <h-tooltip content="秒级系统穿透时延标准差" placement="right">
                        <div class="box-time">
                            <p class="c-font c-explain">系统抖动范围</p>
                            <p class="c-weight">{{ lastTimeData.avg }} ns</p>
                        </div>
                    </h-tooltip>
                    <div class="box-time">
                        <p class="c-font c-explain">时延正常率</p>
                        <p class="c-weight">{{ lastTimeData.normal || '-' }}</p>
                    </div>
                </div>
                <div class="market-data-wrapper-right">
                    <div class="indicators"></div>
                    <div class="list-data">
                        <p class="p-max">
                            <span class="c-font">最大值:</span>
                            <span class="c-num">{{ lastTimeData.max }} ns</span>
                        </p>
                        <p class="p-95">
                            <span class="c-font">95分位数:</span>
                            <span class="c-num">{{ lastTimeData.p95 }} ns</span>
                        </p>
                        <p class="p-5">
                            <span class="c-font">5分位数:</span>
                            <span class="c-num">{{ lastTimeData.p5 }} ns</span>
                        </p>
                        <p class="p-min">
                            <span class="c-font">最小值:</span>
                            <span class="c-num">{{ lastTimeData.min }} ns</span>
                        </p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script>
import * as echarts from 'echarts';
import { getMarketTrendData } from '@/api/httpApi';
import _ from 'lodash';
import noData from '@/components/common/noData/noData';
const candleColor = '#4A92FF';
export default {
    props: ['id', 'name', 'spanData', 'operationName', 'link'],
    data() {
        const that = this;
        return {
            indicators: ['95分位数', '5分位数', '最小值', '最大值', '系统抖动范围', '时延正常率'],
            timer: null,
            status: false,
            mychart: null,
            list: [
                {
                    label: '快照行情',
                    key: 'SM'
                },
                {
                    label: '指数行情',
                    key: 'IM'
                },
                {
                    label: '逐笔委托',
                    key: 'OBOE'
                },
                {
                    label: '逐笔成交',
                    key: 'TYT'
                }
            ],
            option: {
                title: {
                    text: '',
                    textStyle: {
                        color: '#fff',
                        fontSize: 13,
                        fontWeight: 500
                    },
                    top: 6,
                    left: 10
                },
                tooltip: {
                    trigger: 'axis',
                    enterable: true,
                    formatter: function (params, ticket, callback) {
                        let htmlStr = '';
                        for (let i = 0;i < params.length;i++){
                            const param = params[i];
                            const xName = param.name;// x轴的名称
                            const color = param.color;// 图例颜色

                            if (i === 0){
                                htmlStr += xName + '<br/>';// x轴的名称
                            }

                            param.data.forEach((ele, index) => {
                                if (index === 0) return;
                                htmlStr += '<div style="padding-bottom: 4px;">';
                                // 为了保证和原来的效果一样，这里自己实现了一个点的效果
                                htmlStr += '<span style="margin-right:5px;display:inline-block;width:10px;height:10px;border-radius:5px;background-color:' + color + ';"></span>';

                                if (index === 6) {
                                    // 圆点后面显示的文本
                                    htmlStr += that.indicators[index - 1] + '：' + ele + '%';
                                } else {
                                    htmlStr += that.indicators[index - 1] + '：' + ele + ' ns';
                                }

                                htmlStr += '</div>';
                            });
                        }
                        return htmlStr;
                    },
                    backgroundColor: 'rgba(88,94,106,0.80)',
                    borderColor: 'rgba(88,94,106,0.80)',
                    textStyle: {
                        color: '#fff'
                    }
                },
                legend: {
                    data: [],
                    textStyle: {
                        color: '#fff',
                        fontSize: 11,
                        padding: [0, 0, 0, 10]
                    },
                    itemHeight: 1,
                    itemWidth: 16,
                    top: 10
                },
                grid: {
                    left: 50,
                    right: 1,
                    bottom: 30,
                    top: 39
                },
                xAxis: {
                    show: true,
                    axisLine: {
                        show: true,
                        lineStyle: {
                            color: '#31364a'
                        }
                    },
                    axisTick: {
                        show: true,
                        inside: false
                    },
                    axisLabel: {
                        showMinLabel: true,
                        color: '#fff'
                    },
                    data: []
                },
                yAxis: [{
                    show: true,
                    // min: -100,
                    // max: 150,
                    axisLine: {
                        show: true,
                        lineStyle: {
                            color: '#31364a'
                        },
                        onZero: true
                    },
                    axisLabel: {
                        color: '#fff'
                    },
                    splitLine: {
                        lineStyle: {
                            color: '#31364a'
                        }
                    }
                }, {
                    axisLine: {
                        show: true,
                        lineStyle: {
                            color: '#31364a'
                        }
                    }
                }],
                series: [
                    {
                        name: '',
                        type: 'candlestick',
                        legendHoverLink: true,
                        hoverAnimation: true,
                        barWidth: 10,
                        progressive: 3000,
                        data: [],
                        itemStyle: {
                            color: candleColor,
                            color0: candleColor,
                            borderColor: candleColor,
                            borderColor0: candleColor
                        }
                    }
                ]
            },
            lastTimeData: {
                avg: 0,
                normal: '',
                max: 0,
                min: 0,
                p95: 0,
                p5: 0,
                time: '09:15:00'
            }
        };
    },
    mounted() {
        this.init();
    },
    methods: {
        init() {
            this.timer && clearInterval(this.timer);
            const name = _.find(this.list, o => { return o.key === this.name; }).label;
            const legendName = `${this.spanData.name}-K线`;
            this.option.title.text = `${name}走势(ns)`;
            this.option.legend.data[0] = legendName;
            this.option.series[0].name = legendName;
            this.setChart();
            this.timer = setInterval(async() => {
                this.setChart();
            }, 1000);
        },
        async setChart() {
            if (this.status) return;
            const result = await this.getMarketTrendData();
            if (result && result.length) {
                this.option.xAxis.data = result[0].trendChart.xaxis;
                this.option.series[0].data = result[0].trendChart.yaxis;
                const list = result[0].trendChart.yaxis.at(-1);
                if (list) {
                    this.lastTimeData.p95 = list[0];
                    this.lastTimeData.p5 = list[1];
                    this.lastTimeData.min = list[2];
                    this.lastTimeData.max = list[3];
                    this.lastTimeData.avg = list[4];
                    this.lastTimeData.normal = list[5] + '%';
                    this.lastTimeData.time = this.option.xAxis.data.at(-1);
                }
            }
            if (!this.mychart) {
                this.mychart = echarts.init(this.$refs.echart, '#262b40');
            }
            this.mychart.setOption(this.option);
            window.addEventListener('resize', () => {
                this.mychart.resize();
            });
        },
        // 获取实时图表数据
        getMarketTrendData() {
            this.status = true;
            return new Promise((resolve, reject) => {
                const param = {
                    exchangeId: this.id, // 市场，必填
                    span: 'LFL',        // 链路名称
                    linkName: 'NSQ',    // 传输链路，必填
                    bizTypes: [         // 业务名称
                        this.name
                    ],
                    interval: 1000,
                    spanFlag: this.spanData.operationName[this.link],
                    indicators: [
                        'p95',
                        'p5',
                        'min',
                        'max',
                        'stdDeviation', // 抖动范围
                        'normalRatio' // 时延正常率
                    ]
                };
                getMarketTrendData(param).then(res => {
                    if (res.success) {
                        resolve(res.data);
                    } else {
                        resolve(false);
                        this.$hMessage.error(res.message);
                    }
                    this.status = false;
                });
            });
        }
    },
    destroyed() {
        this.timer && clearInterval(this.timer);
    },
    watch: {
        spanData(newV, oldV) {
            this.init();
        }
    },
    components: { noData }
};
</script>

<style lang="less" scoped>
    .container-chart {
        width: 100%;
        height: 260px;
        display: flex;

        .market-canvas {
            width: 100%;
            height: 100%;
            flex: 1;
        }

        .market-img {
            width: 100%;
            height: 100%;
            flex: 1;
            display: flex;
            justify-content: center;
            align-items: center;
            border-bottom: var(--border);

            & > img {
                width: 220px;
                height: 145px;
            }
        }

        .market-data {
            width: 35%;
            margin-left: 20px;

            & > p {
                margin-top: 13px;
                color: var(--font-color);
                font-size: var(--font-size);
            }

            .market-data-wrapper {
                display: flex;
                width: 100%;
                height: 192px;
                margin-top: 8px;
                border: 1px solid var(--wrapper-color);
                box-sizing: border-box;
                padding: 12px;

                .market-data-wrapper-left {
                    display: flex;
                    justify-content: space-between;
                    flex-direction: column;
                    width: 35%;
                    height: 100%;

                    /deep/ .h-tooltip-rel {
                        width: 100%;
                        height: 72px;
                    }

                    .box-time {
                        width: 100%;
                        height: 72px;
                        border-radius: var(--border-radius);
                        background: var(--primary-color);
                        padding-top: 11px;
                        cursor: pointer;

                        p {
                            text-align: center;
                            margin-top: 1px;
                        }

                        &:hover {
                            background: rgba(170, 246, 255, 0.1);
                            border: 1px solid #aaf6ff;

                            & > p {
                                color: #aaf6ff !important;
                            }
                        }
                    }
                }

                .market-data-wrapper-right {
                    position: relative;
                    flex: 1;

                    .indicators {
                        position: absolute;
                        top: 60px;
                        left: 15%;
                        width: 13px;
                        height: 46px;
                        background: var(--link-color);

                        &::before {
                            content: "";
                            display: inline-block;
                            position: absolute;
                            top: -20px;
                            left: 6px;
                            width: 1px;
                            height: 20px;
                            background: var(--link-color);
                        }

                        &::after {
                            content: "";
                            display: inline-block;
                            position: absolute;
                            bottom: -20px;
                            left: 6px;
                            width: 1px;
                            height: 20px;
                            background: var(--link-color);
                        }
                    }

                    .list-data {
                        position: relative;
                        left: 42%;
                        margin: 8px 0;

                        & > p {
                            padding: 9px 0;

                            span:first-child {
                                display: inline-block;
                                width: 70px;
                            }
                        }

                        // 定义混合变量
                        .bor {
                            content: "";
                            position: absolute;
                            left: -18%;
                            display: inline-block;
                            width: 13%;
                            height: 1px;
                            background: var(--border-color);
                        }

                        .p-max::before {
                            .bor();
                            top: 22px;
                            transform: rotate(-15deg);
                        }

                        .p-95::before {
                            .bor();
                            top: 51px;
                            transform: rotate(1deg);
                        }

                        .p-5::before {
                            .bor();
                            top: 94px;
                            transform: rotate(-10deg);
                        }

                        .p-min::before {
                            .bor();
                            top: 123px;
                            transform: rotate(8deg);
                        }
                    }
                }
            }
        }

        .c-title {
            padding: 24px 10px 10px;
        }

        .c-weight {
            color: var(--font-color);
            font-size: var(--font-size);
            font-weight: 500;
        }

        .c-font {
            color: var(--font-color);
            font-size: var(--font-size-base);
        }

        .c-num {
            color: var(--font-color);
            font-size: var(--font-size-base);
        }
    }
</style>
