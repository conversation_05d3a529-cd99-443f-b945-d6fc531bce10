<template>
  <div class="memory">
    <!-- Transmitters -->
    <info-grid :gridData="memoryTransmitters"></info-grid>

    <!-- Receiver -->
    <info-grid :gridData="memoryReceiver"></info-grid>
  </div>
</template>
<script>
import infoGrid from '@/components/common/infoBar/infoGrid';
const POINT_COUNT = 30;
export default {
    name: 'Memory',
    components: {
        infoGrid
    },
    props: {
        rcmInfo: {
            type: Object,
            default: () => {}
        }
    },
    data() {
        return {
            memoryTransmitters: {
                layout: [
                    { x: 0, y: 0, w: 5, h: 12, i: 'info1' },
                    { x: 5, y: 0, w: 7, h: 12, i: 'info2' }
                ],
                details: [
                    {
                        type: 'process-bar',
                        title: 'Transmitters',
                        info: [
                            {
                                key: 'TransmitterIndex',
                                label: 'index',
                                scale: '0',
                                text: '-'
                            },
                            {
                                key: 'TransmitterMessage',
                                label: 'message',
                                scale: '0',
                                text: '-'
                            },
                            {
                                key: 'TransmitterOther',
                                label: 'other',
                                scale: '0',
                                text: '-'
                            }
                        ]
                    },
                    {
                        type: 'chart',
                        info: {
                            basicOpiton: {
                                chartType: 'axis',
                                lineDeploy: [
                                    {
                                        name: 'index',
                                        type: 'line',
                                        unit: '%'
                                    },
                                    {
                                        name: 'message',
                                        type: 'line',
                                        unit: '%'
                                    },
                                    {
                                        name: 'other',
                                        type: 'line',
                                        unit: '%'
                                    }
                                ],
                                yAxiDeploy: [
                                    {
                                        name: '%',
                                        min: 0,
                                        max: 100
                                    }
                                ]
                            },
                            additionalOpiton: {
                                backgroundColor: '#2d334c',
                                grid: {
                                    left: 40
                                }
                            },
                            chartData: {
                                xData: [],
                                data: {
                                    index: [],
                                    message: [],
                                    other: []
                                }
                            }
                        }
                    }
                ]
            },

            memoryReceiver: {
                layout: [
                    { x: 0, y: 0, w: 5, h: 12, i: 'info1' },
                    { x: 5, y: 0, w: 7, h: 12, i: 'info2' }
                ],
                details: [
                    {
                        type: 'process-bar',
                        title: 'Receiver',
                        info: [
                            {
                                key: 'RecvIndex',
                                label: 'index',
                                scale: '0',
                                text: '-'
                            },
                            {
                                key: 'RecvMessage',
                                label: 'message',
                                scale: '0',
                                text: '-'
                            },
                            {
                                key: 'RecvOther',
                                label: 'other',
                                scale: '0',
                                text: '-'
                            }
                        ]
                    },
                    {
                        type: 'chart',
                        info: {
                            basicOpiton: {
                                chartType: 'axis',
                                lineDeploy: [
                                    {
                                        name: 'index',
                                        type: 'line',
                                        unit: '%'
                                    },
                                    {
                                        name: 'message',
                                        type: 'line',
                                        unit: '%'
                                    },
                                    {
                                        name: 'other',
                                        type: 'line',
                                        unit: '%'
                                    }
                                ],
                                yAxiDeploy: [
                                    {
                                        name: '%',
                                        min: 0,
                                        max: 100
                                    }
                                ]
                            },
                            additionalOpiton: {
                                backgroundColor: '#2d334c',
                                grid: {
                                    left: 40
                                }
                            },
                            chartData: {
                                xData: [],
                                data: {
                                    index: [],
                                    message: [],
                                    other: []
                                }
                            }
                        }
                    }
                ]
            }
        };
    },
    methods: {
        initData() {
            this.cleanChartData();
            this.getFileData();
        },
        // 构造页面数据
        getFileData() {
            const Memory = this.rcmInfo?.Memory || {};
            // 全局折线图x轴处理
            const newTime = this.$getCurrentLocalTime();
            this.handleChartData(newTime);

            this.handleTransmittersMemory(Memory, newTime);
            this.handleReceiverMemory(Memory, newTime);
        },
        // 处理Transmitters-Memory数据
        handleTransmittersMemory(memory, newTime) {
            const yDataTransmitters = {};
            this.memoryTransmitters.details[0].info.forEach(item => {
                const { scale, text, rate } = this.getUsesMemoryRate(memory, item?.key);
                item.scale = scale;
                item.text = text;
                // 折线图值
                yDataTransmitters[item.label] = rate;
            });
            // 折线图塞值
            const chartData = this.memoryTransmitters.details[1].info.chartData;
            const index = chartData.xData.lastIndexOf(newTime);
            Object.keys(chartData.data).forEach(item => {
                this.$set(chartData.data[item], index, yDataTransmitters?.[item]);
            });
        },
        // 处理Transmitters-Memory数据
        handleReceiverMemory(memory, newTime) {
            const yDataReceiver = {};
            this.memoryReceiver.details[0].info.forEach(item => {
                const { scale, text, rate } = this.getUsesMemoryRate(memory, item?.key);
                item.scale = scale;
                item.text = text;
                // 折线图值
                yDataReceiver[item.label] = rate;
            });
            // 折线图塞值
            const chartData = this.memoryReceiver.details[1].info.chartData;
            const index = chartData.xData.lastIndexOf(newTime);
            Object.keys(chartData.data).forEach(item => {
                this.$set(chartData.data[item], index, yDataReceiver?.[item]);
            });
        },
        // 计算已使用内存占比
        getUsesMemoryRate(data, key) {
            const memoryUsed = data?.[`${key}_MemoryUsed(MB)`] ?? '-';
            const memAllocButUnused = data?.[`${key}_MemAllocButUnused(MB)`] ?? '-';
            const totalCount = (memAllocButUnused === '-' || memoryUsed === '-') ? '-' : memAllocButUnused + memoryUsed;
            const division = (memoryUsed === '-') ? '-' : memoryUsed === 0 ? 0 : (memoryUsed / totalCount) * 100;
            const text = `${memoryUsed || '0'} / ${totalCount || '0'}`;
            const scale = (division === '-' || isNaN(division)) ? '' : String(division);
            const rate = (division === '-' || isNaN(division)) ? '' : division.toFixed(2) || '';
            return {
                scale, text, rate
            };
        },
        // 处理折线图数据方法
        handleChartData(newTime) {
            const chartTransmitters = this.memoryTransmitters.details[1].info.chartData;
            const chartReceiver = this.memoryReceiver.details[1].info.chartData;
            const chartDataList = [chartTransmitters, chartReceiver];
            Array.isArray(chartDataList) && chartDataList.forEach(chartData => {
                if (chartData.xData.length > POINT_COUNT) {
                    chartData.xData.shift();
                    Object.values(chartData.data).forEach(item => {
                        item.shift();
                    });
                }
                if (!chartData.xData.includes(newTime)) chartData.xData.push(newTime);
            });
        },
        // 清理重置折线图数据
        cleanChartData() {
            this.memoryTransmitters.details[1].info.chartData = {
                xData: [],
                data: {
                    index: [],
                    message: [],
                    other: []
                }
            };
            this.memoryReceiver.details[1].info.chartData = {
                xData: [],
                data: {
                    index: [],
                    message: [],
                    other: []
                }
            };
        }
    }
};
</script>

<style scoped lang="less">
.memory {
    /deep/ .info-process-bar {
        .process-label {
            width: 100px;
            text-align: center;
            color: var(--font-opacity-color);
        }
    }
}
</style>
