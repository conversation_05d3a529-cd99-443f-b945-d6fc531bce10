<template>
    <div ref="tabContainer" class="tab-container scrollbar">
      <div v-for="(item, index) in items" :key="index" ref="tabItem" class="tab-item">
         <slot :item="item"></slot>
      </div>
    </div>
  </template>

<script>
export default {
    props: {
        // 瀑布流数据
        items: {
            type: Array,
            default: function() {
                return [];
            }
        },
        // 1.指定 容器中内容展示列数
        columns: {
            type: Number,
            default: 0
        },
        // 2.固定宽度
        colWidth: {
            type: Number,
            default: 0
        },
        // 3.适应宽度
        autoFixed: {
            type: Boolean,
            default: false
        },
        // 容器中 列 间隔距离 默认为20
        columnGap: {
            type: Number,
            default: 10
        },
        // 容器中 行 间隔距离 默认为20
        rowGap: {
            type: Number,
            default: 10
        }
    },
    data() {
        return {
            cols: 2
        };
    },
    mounted() {
        window.addEventListener('resize', this.resize);
        this.$refs['tabContainer'] && setTimeout(() => {
            this.$nextTick(() => {
                this.waterFall();
            });
        }, 0);
    },
    beforeDestroy() {
        window.removeEventListener('resize', this.resize);
    },
    methods: {
        resize() {
            // 由于网格布局拖拽放大缩小页面不能自适应，设置一个定时器使得加载为一个异步过程
            this.$refs['tabContainer'] && setTimeout(() => {
                this.$nextTick(() => {
                    this.waterFall();
                });
            }, 0);
        },
        // 根据容器宽度调整 列
        setCols(){
            // 指定列数
            if (this.columns){
                this.cols = this.columns;
                return;
            }

            const wrapContentWidth = this.$refs['tabContainer']?.offsetWidth - 2;
            // 自适应宽度
            if (this.autoFixed){
                if (wrapContentWidth <= 200) {
                    this.cols = 1;
                    return;
                }
                if (wrapContentWidth > 200 && wrapContentWidth <= 500) {
                    this.cols = 2;
                    return;
                }

                if (wrapContentWidth > 500 && wrapContentWidth <= 1000) {
                    this.cols = 3;
                    return;
                }

                if (wrapContentWidth > 1000) {
                    this.cols = 4;
                    return;
                }
                return;
            }
            // 固定宽度
            if (this.colWidth){
                let count = Math.floor(wrapContentWidth / this.colWidth);
                const contentWidth = this.colWidth * count;
                const whiteArea = (count - 1) * this.columnGap;
                if (contentWidth + whiteArea > wrapContentWidth){
                    count = count - 1;
                }
                if (this.items.length < count){
                    count = this.items.length;
                }
                this.cols = count <= 0 ? 1 : count;
                return;
            }

        },
        waterFall() {
            // 根据容器宽度调整 列
            this.setCols();

            // 宽度
            const wrapContentWidth = this.$refs['tabContainer']?.offsetWidth - 2;
            const whiteArea = (this.cols - 1) * this.columnGap;
            const contentWidth = parseInt((wrapContentWidth - whiteArea) / this.cols, 10);

            const contentList = this.$refs['tabItem'];
            const lineConentHeightList = [];
            for (let i = 0; i < contentList.length; i++) {
                // 动态设置内容项宽度
                contentList[i].style.width = contentWidth + 'px';

                // 获取内容项高度 最小列的高度 = 当前自己的高度 + 拼接过来的高度 + 行间距
                const height = contentList[i].clientHeight;
                if (i < this.cols) {
                    contentList[i].style.top = 0;
                    contentList[i].style.left = contentWidth * i + this.columnGap * i + 'px';
                    lineConentHeightList.push(height);
                } else {
                    const minHeight = Math.min(...lineConentHeightList);
                    const index = lineConentHeightList.findIndex(listH => listH === minHeight);
                    contentList[i].style.top = minHeight + this.rowGap + 'px';
                    contentList[i].style.left = (contentWidth + this.columnGap) * index + 'px';
                    lineConentHeightList[index] += height + this.rowGap;
                }
            }
        }
    }
};
</script>

  <style lang="less" scoped>
    .tab-container {
        position: relative;
        height: calc(100% - 10px);
        overflow: hidden;

        .tab-item {
            position: absolute;
            height: auto;
            background: none;
            break-inside: avoid;
            text-align: center;
        }
    }

    .tab-container:hover {
        overflow-y: overlay;
    }

    .scrollbar::-webkit-scrollbar {
        width: 1px;
    }

    .scrollbar::-webkit-scrollbar-track-piece {
        background-color: #28314b;
    }

    /* 滚动条的内层滑轨背景颜色 */

    .scrollbar::-webkit-scrollbar-track {
        background-color: #28314b;
    }

    /* 滚动条的外层滑轨背景颜色 */

    .scrollbar::-webkit-scrollbar-thumb {
        background-color: #28314b;
    }

    /* 滚动条的内层滑块颜色 */

    .scrollbar::-webkit-scrollbar-button {
        background-color: #28314b;
        display: none;
    }

    /* 滑轨两头的监听按钮颜色 */
  </style>

