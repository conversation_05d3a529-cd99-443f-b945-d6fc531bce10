export const RUN_SQL_PLACEHOLDER = `1、只允许输入一条select语句，最多500个字符，以英文字符';' 结尾（可选）；
2、校验SQL可执行性，请点击“测试”按钮，并确保核心已经启动；
3、表名需要统一添加“View”后缀；`;

export const PRE_RUN_SQL_PLACEHOLDER = `1.只允许输入一条select语句，最多500个字符，以英文字符';' 结尾（可选）；
2.表名需要统一添加“View”后缀；
3.查询结果第一行记录字段作为“用户变量”；`;

export const VAR_TIP = `可引用变量来自“内置变量”和“预执行SQL”得到的变量：
1.可在下方“执行SQL”中引用；
2.引用时需“输入”变量完整内容（带前缀@）；
3.SQL具体使用方式请参照《SQL语法指南》。`;

export const SYSTEM_VAR_TIP = `当前仅支持lastmin(num,format)函数。
num：表示获取距离当前时间的前num分钟的时间；
format：表示当前时间字段的格式化模式，目前仅支持HHmmss(时分秒) 和 HHmmssSSS(时分秒毫秒)两种。
例如：
lastmin(5,HHmmss)表示获取当前时间的前5分钟的时刻，同时按照“时分秒”格式化时间字段。`;
