.apm-drop-menu {
    position: relative;
    display: inline-block;
    width: auto;
    height: 40px;

    & > span {
        display: inline-block;
        max-width: 200px;
        color: #fff;
        font-size: 14px;
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
        cursor: pointer;
    }

    .icon-drop {
        position: absolute;
        top: 0;
        cursor: pointer;
    }

    .box-drop {
        position: absolute;
        top: 40px;
        left: 0;
        background-color: hsl(0, 0%, 100%);
        border-radius: 4px;
        width: 380px;
        height: 100%;
        height: auto;
        overflow: hidden;
        font-size: 12px;
        line-height: normal;
        z-index: 1;
        transition: all 0.5s;

        & > .drop-close {
            position: absolute;
            right: 8px;
            top: 8px;
            color: #ccc;
            z-index: 2;
            cursor: pointer;

            &:hover {
                color: var(--link-color);
            }
        }

        & > div {
            margin: 28px 8px 10px;
            max-height: 210px;
            overflow: auto;

            & > .menu-list {
                width: 100%;
                max-height: 250px;
                padding-top: 4px;
                overflow: auto;
                font-size: 12px;

                .menu-title {
                    padding: 5px 0 10px 10px;
                    font-weight: 500;
                    color: #444;
                }

                .menu-key {
                    padding: 5px 8px 0 0;
                    color: #777;
                    text-align: right;
                }

                .menu-values {
                    padding: 0 8px;
                    color: #333;
                    border-left: 1px solid #ececec;

                    & > span {
                        display: inline-block;
                        padding: 4px;
                        line-height: 20px;
                        color: #333;
                        border-radius: 2px;
                        margin: 0 6px 4px 0;
                        cursor: pointer;

                        &:hover {
                            color: var(--link-color);
                        }
                    }

                    .drop-span-active {
                        background-color: #e8f4ff;
                        color: var(--link-color);
                    }
                }
            }
        }
    }
}
