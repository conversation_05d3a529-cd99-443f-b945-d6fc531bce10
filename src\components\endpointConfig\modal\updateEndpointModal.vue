<!-- 新增、修改接入点信息弹窗 -->
<template>
    <div>
        <h-msg-box-safe
            v-model="modalData.status"
            :mask-closable="false"
            :title="modalTitle"
            width="700"
            maxHeight="400">
            <h-form ref="formValidate" :rules="rules" :model="formValidate" :label-width="120">
                <h-form-item v-if="showAppNode" label="接入应用节点" prop="appInstanceId" required>
                    <h-select v-model="formValidate.appInstanceId" placeholder="请选择" :clearable="false" :positionFixed="true" @on-change="handleSelectChange">
                        <h-option v-for="item in filterInstanceList" :key="item.id" :value="item.id">{{ item.instanceName }}</h-option>
                    </h-select>
                </h-form-item>
                <h-form-item label="接入点名：" prop="name" required :validRules="stringRule">
                    <h-input v-model.trim="formValidate.name" placeholder="请输入接入点名" @on-change="resetConnectStatus"></h-input>
                </h-form-item>
                <h-form-item label="接入点IP：" prop="endpointIp" required :validRules="ipRule">
                    <h-input v-model.trim="formValidate.endpointIp" placeholder="请输入接入点IP" @on-change="resetConnectStatus"></h-input>
                </h-form-item>
                <h-form-item label="接入点端口：" prop="endpointPort" required :validRules="portRule">
                    <h-input v-model.trim="formValidate.endpointPort" placeholder="请输入接入点端口" type="int" :maxlength="5" @on-change="resetConnectStatus"></h-input>
                </h-form-item>
                <h-form-item label="接入协议：" prop="protocol" required>
                    <h-select v-model="formValidate.protocol" placeholder="请选择" :clearable="false" :positionFixed="true" @on-change="onChangeProtocol">
                        <h-option v-for="item in protocolList" :key="item" :value="item">{{item}}</h-option>
                    </h-select>
                </h-form-item>
                <h-form-item label="字符集：" prop="charset" required>
                    <h-select v-model="formValidate.charset" placeholder="请选择" :clearable="false" :positionFixed="true" @on-change="resetConnectStatus">
                        <h-option v-for="item in charsetList" :key="item.value" :value="item.value">{{item.label}}</h-option>
                    </h-select>
                </h-form-item>
                <h-form-item v-if="showFileUpload && isT2Protocol" class="upload" label="T2证书" prop="protocolCertificateAddr">
                    <h-upload type="drag" action="" style="width: 100%;" :before-upload="handleUpload">
                        <div style="padding: 10px 20px 0; width: 514px;">
                            <h-icon name="document-file" size="30" style="color: var(--link-color);"></h-icon>
                            <p>点击或拖拽上传证书</p>
                        </div>
                        <div v-if="file !== null">
                            上传证书文件：{{ file.name }}
                            <h-button v-if="loadingStatus === 0" type="text" :loading="!loadingStatus">上传中</h-button>
                            <span v-if="loadingStatus === 2" style="color: var(--success-color); margin: 0 10px;">上传成功</span>
                            <span v-if="loadingStatus === 3" style="color: var(--error-color); margin: 0 10px;">上传失败</span>
                        </div>
                        <div v-if="formValidate.protocolCertificateAddr">
                            已上传证书路径：{{ formValidate.protocolCertificateAddr }}
                        </div>
                    </h-upload>
                </h-form-item>
                <h-form-item v-else-if="showFileUpload" class="upload" label="发包配置文件" prop="protocolCertificateAddr">
                    <div class="upload-config">
                        <h-radio-group v-model="configType">
                            <h-radio label="default" text="平台出厂配置"></h-radio>
                            <h-radio label="custom" text="自定义业务配置"></h-radio>
                        </h-radio-group>
                        <h-upload
                            v-if="configType === 'custom'"
                            action=""
                            accept=".cfg"
                            :before-upload="handleUpload">
                            <div class="upload-box">
                                <div class="upload-box-text">
                                    {{ packetConfigFile || "点击上传文件" }}
                                </div>
                                <h-icon name="android-folder-open"></h-icon>
                            </div>
                        </h-upload>
                    </div>
                </h-form-item>
                <h-form-item label="连接测试：">
                    <h-button
                        :loading="btnLoading"
                        @click="handleConnectTest">
                        {{btnLoading ? '连接中' : '连接'}}
                    </h-button>
                    <span
                        v-if="connectStatus"
                        :style="`margin-left: 8px; color: ${connectObj.color};`">
                        {{connectObj.text}}&nbsp;
                        <h-icon :name="connectObj.icon" size="12"></h-icon></span>
                </h-form-item>
            </h-form>

            <template v-slot:footer>
                <a-button @click="modalData.status = false">取消</a-button>
                <a-button type="primary" :loading="loading" @click="submitConfig">确定</a-button>
            </template>
        </h-msg-box-safe>
    </div>
</template>

<script>
import _ from 'lodash';
import { fileUpload, getEndpointTest } from '@/api/productApi';
import { stringLengthRule, validatePort } from '@/utils/validate';
import aButton from '@/components/common/button/aButton';
import { ENDPOINT_TYPE, PROTOCOL, PROTOCOL_LIST_MAP, CREATE_ENDPOINTS_MAP, ACCEPT_FILE_EXTENDSTIONS, CONNECT_STATUS } from '../constant';

// 上传文件状态：loading 0：加载中，stateless 1：无状态，success 2：上传成功，fail 3：上传失败
const uploadStatus = {
    loading: 0,
    stateless: 1,
    success: 2,
    fail: 3
};
export default {
    name: 'UpdateEndpointModal',
    components: {  aButton },
    props: {
        modalInfo: {
            type: Object,
            default: null
        },
        instanceList: {
            type: Array,
            default: () => []
        },
        /**
        * 接入点类型
        */
        endpointType: {
            type: String,
            default: () => ENDPOINT_TYPE.MDB
        }
    },
    computed: {
        /**
         * 接入点列表，受接入点类型影响，
         * 例如mdb接入点：只保留前置节点
         * ustTable接入点：只保留核心节点
         * 其它待后续补充
         */
        filterInstanceList() {
            return _.filter(this.instanceList,  (o) => {
                if (this.endpointType === ENDPOINT_TYPE.MDB) {
                    return o?.instanceIdentities?.includes('front');
                }
                if (this.endpointType === ENDPOINT_TYPE.UST_TABLE) {
                    return o?.instanceIdentities?.includes('bizproc');
                }
                if (this.endpointType === ENDPOINT_TYPE.SEND_PACKET_RECEIVED) {
                    return o?.instanceType !== 'locate_ar';
                }
                return true;
            }) || [];
        },
        connectObj() {
            return CONNECT_STATUS[this.connectStatus] || {};
        },
        /**
         * 弹窗标题
         */
        modalTitle() {
            const type = this.modalData.type === 'add' ? '添加' : '修改';
            return `${type}${this.endpointType === ENDPOINT_TYPE.SEND_PACKET_RECEIVED ? '节点' : '网关节点'}`;
        },
        isT2Protocol() {
            return this.formValidate.protocol === PROTOCOL.T2;
        },
        /**
         * 接入协议列表
         */
        protocolList() {
            return PROTOCOL_LIST_MAP[this.endpointType] || [];
        },
        /**
         * 字符集列表
         */
        charsetList() {
            if (this.endpointType === ENDPOINT_TYPE.MDB || this.endpointType === ENDPOINT_TYPE.LOCATE) {
                return [
                    { value: 'gbk', label: 'GBK' },
                    { value: 'utf-8', label: 'UTF-8' }
                ];
            }
            return [{ value: 'utf-8', label: 'UTF-8' }];
        },
        /**
         * 是否需要对上传文件的提示
         */
        needFileDesc() {
            return this.endpointType === ENDPOINT_TYPE.SEND_PACKET_RECEIVED;
        },
        /**
         * 是否显示文件上传功能
         */
        showFileUpload() {
            return this.endpointType === ENDPOINT_TYPE.MDB || this.endpointType === ENDPOINT_TYPE.SEND_PACKET_RECEIVED || this.endpointType === ENDPOINT_TYPE.LOCATE;
        },
        /**
         * 是否显示接入应用节点，locate接入点不需要选择应用节点
         */
        showAppNode() {
            return this.endpointType !== ENDPOINT_TYPE.LOCATE;
        }
    },
    watch: {
        configType(newVal) {
            if (newVal === 'default') {
                // 默认配置文件路径
                this.formValidate.protocolCertificateAddr = 'default';
            } else {
                this.formValidate.protocolCertificateAddr = this.packetConfigFile;
            }
        }
    },
    data() {
        return {
            modalData: this.modalInfo,
            ipRule: ['ip4'],
            stringRule: [{ test: stringLengthRule(30), trigger: 'blur' }],
            portRule: [{ test: validatePort, trigger: 'blur' }],
            formValidate: {
                name: '',
                endpointIp: '',
                endpointPort: ''
            },
            loading: false,
            file: null,
            loadingStatus: uploadStatus.stateless,
            btnLoading: false,
            connectStatus: '',
            firstRender: true,
            rules: {
                protocolCertificateAddr: { required: true, message: '请上传文件' }
            },
            configType: '',
            packetConfigFile: ''
        };
    },
    mounted() {
        this.formValidate = { ...this.formValidate, ...this.modalData };
        this.formValidate.appInstanceId = this.modalData.appInstanceId || this.filterInstanceList?.[0]?.id;
        this.formValidate.charset = this.modalData.charset || this.charsetList[0].value;
        if (this.endpointType === ENDPOINT_TYPE.SEND_PACKET_RECEIVED) {
            this.configType = 'default';
        }
        setTimeout(() => {
            if (this.modalData.type !== 'add') {
                this.formValidate.name = this.modalData?.name;
                this.formValidate.endpointIp = this.modalData?.endpointIp;
            }
        });
    },
    methods: {
        submitConfig() {
            this.$refs['formValidate'].validate((valid) => {
                if (valid) {
                    this.updateEndpoint();
                }
            });
        },
        /**
         * 切换接入协议
         */
        onChangeProtocol() {
            this.file = null;
            this.resetConnectStatus();
        },
        /**
        * 重置连接测试状态
        */
        resetConnectStatus() {
            if (this.connectStatus != '') {
                this.connectStatus = '';
            }
        },
        // 切换接入应用节点
        handleSelectChange(id) {
            if (!this.firstRender || this.modalInfo.type === 'add') {
                const instanceObj = _.find(this.filterInstanceList, ['id', id]);
                this.formValidate.name = instanceObj?.instanceName;
                instanceObj?.manageProxyIp && (this.formValidate.endpointIp = instanceObj?.manageProxyIp);
                // 抓包发包类型支持获取默认端口号
                if (instanceObj?.manageProxyPort && this.endpointType === ENDPOINT_TYPE.SEND_PACKET_RECEIVED) {
                    this.formValidate.endpointPort = instanceObj?.manageProxyPort;
                }
            } else {
                this.firstRender = false;
            }
        },
        // 验证上传文件后缀错误
        handleFormatError(file) {
            this.$hNotice.warning({
                title: '文件格式不正确',
                desc: '文件 ' + file.name + ' 格式不正确，请上传' + ACCEPT_FILE_EXTENDSTIONS[this.endpointType].join(',.') + '格式的文件。'
            });
        },
        // 验证上传文件大小限制
        handleSizeError() {
            this.$hNotice.warning({
                title: '文件大小超出限制',
                desc: '文件大小超出限制，请上传 1MB 以内大小的文件。'
            });
        },
        // 证书、配置文件上传
        handleUpload(file) {
            this.file = file;
            this.loadingStatus = uploadStatus.stateless;
            this.getActionData(file);
            return false;
        },
        // 解析获取文件内容
        async getActionData(file) {
            // 获取文件后缀名，当前只支持xxx后缀的文件
            const suffix = file.name?.substring(file.name?.lastIndexOf('.') + 1);
            if (!ACCEPT_FILE_EXTENDSTIONS[this.endpointType].includes(suffix)) {
                this.loadingStatus = uploadStatus.fail;
                return this.handleFormatError(file);
            }
            // 限制文件大小 1MB 内
            if (file?.size > 1024 * 1024) {
                this.loadingStatus = uploadStatus.fail;
                return this.handleSizeError();
            }
            const formData = new FormData();
            formData.append('file', file);
            try {
                const res = await fileUpload(formData);
                if (res.code === '200') {
                    if (this.endpointType === ENDPOINT_TYPE.SEND_PACKET_RECEIVED) {
                        this.packetConfigFile  = res.data.filePath;
                        this.$hMessage.success('上传成功');
                    }
                    this.formValidate.protocolCertificateAddr = res.data.filePath;
                    this.loadingStatus = uploadStatus.success;
                } else {
                    this.loadingStatus = uploadStatus.fail;
                }
            } catch (res) {
                this.loadingStatus = uploadStatus.fail;
            }
            this.$refs['formValidate'].validateField('protocolCertificateAddr');
        },
        async updateEndpoint() {
            this.loading = true;
            try {
                const params = {
                    ...this.formValidate,
                    protocolCertificateAddr: this.formValidate.protocolCertificateAddr === 'default'
                        ? '' : this.formValidate.protocolCertificateAddr,
                    productInstNo: this.modalData.productInstNo
                };
                const apiName = CREATE_ENDPOINTS_MAP[this.endpointType];
                if (!apiName) {
                    console.warn('不支持的接入类型, ', this.endpointType);
                    return;
                }
                const res = await apiName(params);
                if (res.success) {
                    this.$hMessage.success(`${this.modalData.type === 'add' ? '添加' : '修改'}成功！`);
                    this.$emit('update');
                    this.modalData.status = false;
                }
            } finally {
                this.loading = false;
            }
        },
        // 连接测试
        handleConnectTest() {
            this.$refs['formValidate'].validate((valid) => {
                if (valid) {
                    this.getEndpointTest();
                }
            });
        },
        // 连接测试接口调用
        async getEndpointTest() {
            this.connectStatus = '';
            this.btnLoading = true;
            try {
                const params = {
                    ...this.formValidate,
                    productInstNo: this.modalData.productInstNo
                };
                const res = await getEndpointTest(params);
                if (res.code === '200') {
                    this.connectStatus = 'success';
                } else if (res.code.length === 8) {
                    this.connectStatus = 'failed';
                }
            } finally {
                this.btnLoading = false;
            }
        }
    }

};
</script>

<style lang="less" scoped>
/deep/ .h-upload-click-wrapper {
    width: 100%;
}

/deep/ .h-form-item-error .h-radio .h-radio-inner {
    border: 1px solid #d7dde4;
}

.upload-box {
    display: flex;
    cursor: pointer;
    border: 1px solid #ddd;
    border-radius: 4px;
    justify-content: space-between;

    &:hover {
        border-color: #54a4ff;
    }

    .upload-box-text {
        flex: 1;
        padding: 0 10px;
        overflow: hidden; //超出的文本隐藏
        text-overflow: ellipsis; //溢出用省略号显示
        white-space: nowrap; //溢出不换行
    }

    .h-icon {
        padding: 0 10px;
        border-left: 1px solid #ddd;
    }
}
</style>
