.obs-head {
    .h-select > .h-select-left {
        color: var(--font-color);
        background-color: var(--input-bg-color);
        border: var(--border);
    }

    .h-select-content-input,
    .h-select-input {
        color: var(--font-color);
    }

    .head-title {
        height: 42px;
        color: var(--font-color);
        font-size: var(--title-font-size);
        line-height: 42px;

        &::before {
            display: inline-block;
            position: relative;
            left: -5px;
            top: 3px;
            content: "";
            width: 5px;
            height: 17px;
            background: var(--link-color);
        }
    }
}
