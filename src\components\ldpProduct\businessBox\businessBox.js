/*
 * @Description:
 * @Author: <PERSON><PERSON>
 * @Date: 2023-03-28 13:13:35
 * @LastEditTime: 2023-05-22 14:28:08
 * @LastEditors: <PERSON><PERSON>
 */
import apmScroll from '@/components/common/bestScroll/apmScroll';
import businessPoptip from './businessPoptip';
import './businessBox.less';
export default {
    name: 'businessBox',
    props: {
        placement: {
            type: String,
            default: 'right'
        },
        panel: {
            type: Object,
            default: function() {
                return {};
            }
        },
        nodes: {
            type: Array,
            default: function() {
                return null;
            }
        }
    },
    components: { apmScroll, businessPoptip },
    data() {
        return {
        };
    },
    watch: {
        panel(newVal) {
            this.$nextTick(() => {
                this.$refs['apmScroll'].refresh();
            });
        }
    },
    mounted() {

    },
    methods: {
        getStatusClass(key) {
            switch (key) {
                case 'runing':
                    return 'bussiness-success';

                case 'warning':
                    return 'bussiness-warn';

                case 'exception':
                    return 'bussiness-error';

                case 'stop':
                    return 'bussiness-stop';

                default:
                    return 'bussiness-none';
            }
        },
        getBussinessPosition(id, type) {
            let position = this.placement;
            const top = this.$refs[`bussiness${id}`]?.getBoundingClientRect()?.top;
            const left = this.$refs[`bussiness${id}`]?.getBoundingClientRect()?.left;
            if (type) {
                position = left < 500 ? 'top-start' : 'top-end';
            } else {
                if (this.placement === 'right') {
                    position = top <= 450 ? 'right' : 'right-end';
                } else if (this.placement === 'left') {
                    position = top <= 450 ? 'left' : 'left-end';
                }
            }
            return position;
        },
        renderNodes(nodes, type) {
            return <ul>
                {
                    nodes.map(node => {
                        let flagClass = '';
                        if (node?.target?.runningInfo?.status !== 'stop') {
                            const clusterRole = node?.target?.runningInfo?.clusterRole;
                            if (clusterRole === 'ARB_ACTIVE') flagClass = 'main-flag';
                            else if (clusterRole === 'ARB_INACTIVE') flagClass = 'stand-flag';
                        }
                        return  <li ref={'bussiness' + node.id} class={[
                            'bussiness-btn',
                            node?.target?.baseInfo?.hosting ? this.getStatusClass(node?.target?.runningInfo?.status) : 'bussiness-none',
                            flagClass
                        ]}
                        key={node.id}>
                            <business-poptip placement={this.getBussinessPosition(node.id, type)} node={node} />
                        </li>;
                    })
                }
            </ul>;
        }
    },
    render() {
        const groups = this.panel.content || [];
        return (
            Array.isArray(this.nodes) ? <div class="business-box" style={{ padding: '10px 20px' }}>
                {this.renderNodes(this.nodes, 'product')}
            </div>
                : <div class="business-box">
                    <p class="business-box-title">{this.panel.name}</p>
                    {
                        groups.length && (
                            <apm-scroll ref="apmScroll" style={{ height: 'calc(100% - 21px)' }}>
                                <div class="business-content-list">
                                    {groups.map((group) => (
                                        <div class="business-box-content" key={group.name}>
                                            <p class="business-box-content-header">{group.name}</p>
                                            {Array.isArray(group.nodes) && this.renderNodes(group.nodes)}
                                        </div>
                                    ))}
                                </div>
                            </apm-scroll>
                        )}
                </div>
        );
    }
};
