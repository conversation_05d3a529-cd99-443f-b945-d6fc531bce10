<template>
    <div>
        <!-- 数据表信息修改弹窗 -->
        <h-msg-box-safe v-model="modalData.status" :escClose="true" :mask-closable="false" title="更新数据表信息" width="50"
            @on-open="getCollections" @submit.native.prevent>
            <h-form ref="formValidate" :model="formValidate" :label-width="120">
                <h-form-item v-if="modalData.type === 0" label="数据表扩展属性" prop="expandAttributes">
                    <h-select v-model="formValidate.expandAttributes" allowCreate filterable multiple showBottom
                        placeholder="请选择数据表扩展属性" :transfer="true" :maxlength="30">
                        <h-option v-for="item in attributeList" :key="item" :value="item">{{
                            item
                        }}</h-option>
                    </h-select>
                </h-form-item>
                <h-form-item v-else label="数据表说明" prop="tableDescribe">
                    <h-input v-model="formValidate.tableDescribe" placeholder="请输入数据表说明"></h-input>
                </h-form-item>
            </h-form>

            <template v-slot:footer>
                <a-button @click="modalData.status = false">取消</a-button>
                <a-button type="primary" :loading="loading" @click="submitConfig">确定</a-button>
            </template>
        </h-msg-box-safe>
    </div>
</template>

<script>
import { modifyTableMetadata } from '@/api/httpApi';
import aButton from '@/components/common/button/aButton';
export default {
    props: {
        modalInfo: {
            type: Object,
            default: null
        }
    },
    data() {
        return {
            modalData: this.modalInfo,
            formValidate: {
                expandAttributes: [],
                tableDescribe: ''
            },
            loading: false,
            attributeList: []
        };
    },
    methods: {
        getCollections() {
            this.attributeList = [...this.modalData.expandAttributes];
            this.formValidate.expandAttributes = [...this.modalData.expandAttributes];
            this.formValidate.tableDescribe = this.modalData.tableDescribe;
        },
        async submitConfig() {
            this.$refs['formValidate'].validate(async (valid) => {
                if (valid) {
                    if (this.formValidate.tableDescribe?.length > 50) {
                        this.$hMessage.error('字符长度数不得超过50！');
                        return;
                    }
                    await this.modifyTableMetadata();
                    this.modalInfo.status = false;
                }
            });
        },
        // 保存表格元数据信息修改
        async modifyTableMetadata() {
            this.loading = true;
            const expandAttributes = this.formValidate.expandAttributes || [];
            const param = {
                expandAttributes: expandAttributes,
                tableDescribe: this.formValidate.tableDescribe,
                ...this.modalData.endpointInfo,
                databaseName: this.modalData.databaseName,
                tableName: this.modalData.tableName
            };
            try {
                const res = await modifyTableMetadata(param);
                if (!res.errcode) {
                    this.$emit('updata-info', this.formValidate);
                    this.$hMessage.success('保存成功');
                    this.loading = false;
                }
            } catch (err) {
                console.error(err);
                this.loading = false;
            }
        }
    },
    components: { aButton }
};
</script>
