<template>
    <div>
        <h-msg-box-safe
            v-model="modelData.status"
            :escClose="true"
            :mask-closable="false"
            footerHide
            title="修改操作执行详情"
            width="60"
        >
            <h-form
                :model="modalInfo"
                :label-width="80"
                >
                <h-form-item label="操作请求：" prop="requestUrl">
                    <h-input v-model="operationRequest" type="text" disabled></h-input>
                </h-form-item>
                <h-form-item label="请求时间：" prop="requestTime">
                    <span>{{modalInfo.requestTime}}</span>
                </h-form-item>
                <h-form-item label="请求入参：" prop="requestParams">
                    <h-input v-model="modalInfo.requestParams" type="textarea" disabled :autosize="{ minRows: 3, maxRows: 4 }"></h-input>
                </h-form-item>
                <h-form-item label="请求返回：" prop="response">
                    <h-input v-model="modalInfo.response" type="textarea" disabled :autosize="{ minRows: 3, maxRows: 4 }"></h-input>
                </h-form-item>
            </h-form>
        </h-msg-box-safe>
    </div>
</template>

<script>
export default {
    name: 'OperationDetailsModal',
    props: {
        modalInfo: {
            type: Object,
            default: null
        }
    },
    data() {
        return {
            modelData: this.modalInfo
        };
    },
    methods: {

    },
    computed: {
        operationRequest () {
            return `${this.modalInfo.requestProtocol} ${this.modalInfo.requestUrl}`;
        }
    }
};
</script>

<style lang="less" scoped>
.msg-body {
    display: flex;
    justify-content: center;
    align-items: center;
    flex-direction: column;

    .msg-tip {
        padding: 20px;
    }
}
</style>
