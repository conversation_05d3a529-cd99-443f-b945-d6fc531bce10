#!/usr/bin/env node

const fs = require('fs');
const path = require('path');

/**
 * 国际化文案组织脚本
 * 将提取的文案按路由维度重新组织，生成更合理的国际化键值对
 */

class I18nOrganizer {
    constructor() {
        this.extractedDir = path.join(__dirname, '../src/locales/extracted');
        this.outputDir = path.join(__dirname, '../src/locales/organized');
        this.routeConfig = this.loadRouteConfig();
        
        // 加载提取的数据
        this.mappingData = this.loadMappingData();
        
        // 常用词汇映射
        this.commonMappings = {
            // 操作类
            '查询': 'query',
            '搜索': 'search',
            '添加': 'add',
            '新增': 'add',
            '创建': 'create',
            '编辑': 'edit',
            '修改': 'edit',
            '更新': 'update',
            '删除': 'delete',
            '移除': 'remove',
            '保存': 'save',
            '取消': 'cancel',
            '确认': 'confirm',
            '提交': 'submit',
            '重置': 'reset',
            '刷新': 'refresh',
            '导出': 'export',
            '导入': 'import',
            '上传': 'upload',
            '下载': 'download',
            '复制': 'copy',
            '粘贴': 'paste',
            '清空': 'clear',
            '选择': 'select',
            '全选': 'selectAll',
            
            // 状态类
            '成功': 'success',
            '失败': 'failed',
            '错误': 'error',
            '警告': 'warning',
            '提示': 'tip',
            '信息': 'info',
            '加载中': 'loading',
            '运行中': 'running',
            '已停止': 'stopped',
            '已暂停': 'paused',
            '已完成': 'finished',
            '待处理': 'pending',
            '处理中': 'processing',
            
            // 数据类
            '数据': 'data',
            '信息': 'info',
            '详情': 'detail',
            '列表': 'list',
            '表格': 'table',
            '记录': 'record',
            '日志': 'log',
            '报告': 'report',
            '统计': 'statistics',
            '分析': 'analysis',
            '监控': 'monitor',
            '观测': 'observation',
            
            // 配置类
            '配置': 'config',
            '设置': 'setting',
            '参数': 'param',
            '选项': 'option',
            '属性': 'property',
            
            // 时间类
            '时间': 'time',
            '日期': 'date',
            '开始': 'start',
            '结束': 'end',
            '开始时间': 'startTime',
            '结束时间': 'endTime',
            '创建时间': 'createTime',
            '更新时间': 'updateTime',
            
            // 基础类
            '名称': 'name',
            '标题': 'title',
            '描述': 'description',
            '备注': 'remark',
            '说明': 'description',
            '类型': 'type',
            '状态': 'status',
            '版本': 'version',
            '编号': 'number',
            '序号': 'index',
            
            // 提示类
            '请选择': 'pleaseSelect',
            '请输入': 'pleaseInput',
            '请确认': 'pleaseConfirm',
            '暂无数据': 'noData',
            '暂无': 'none',
            '无': 'none',
            '空': 'empty',
            
            // 单位类
            '秒': 'second',
            '分钟': 'minute',
            '小时': 'hour',
            '天': 'day',
            '次': 'times',
            '个': 'count',
            '条': 'item',
            '行': 'row',
            '列': 'column',
            
            // 业务类
            '产品': 'product',
            '实例': 'instance',
            '节点': 'node',
            '核心': 'core',
            '集群': 'cluster',
            '服务': 'service',
            '应用': 'application',
            '功能': 'function',
            '模块': 'module',
            '组件': 'component',
            '页面': 'page',
            '管理': 'manage',
            '系统': 'system'
        };
    }

    /**
     * 加载路由配置
     */
    loadRouteConfig() {
        try {
            const routerPath = path.join(__dirname, '../src/router/router.js');
            const routerContent = fs.readFileSync(routerPath, 'utf-8');
            return this.parseRouteConfig(routerContent);
        } catch (error) {
            console.warn('无法加载路由配置:', error.message);
            return {};
        }
    }

    /**
     * 解析路由配置
     */
    parseRouteConfig(content) {
        const routes = {};
        
        // 提取路由名称和路径的映射关系
        const routePattern = /name:\s*['"`]([^'"`]+)['"`][\s\S]*?path:\s*['"`]([^'"`]+)['"`]/g;
        let match;
        
        while ((match = routePattern.exec(content)) !== null) {
            const [, name, path] = match;
            routes[name] = path;
        }
        
        return routes;
    }

    /**
     * 加载映射数据
     */
    loadMappingData() {
        try {
            const mappingPath = path.join(this.extractedDir, 'mapping.json');
            const content = fs.readFileSync(mappingPath, 'utf-8');
            return JSON.parse(content);
        } catch (error) {
            console.error('无法加载映射数据:', error.message);
            return {};
        }
    }

    /**
     * 根据文件路径推断路由名称
     */
    inferRouteFromFilePath(filePath) {
        // 处理views目录下的文件
        if (filePath.includes('views/index/') || filePath.includes('views\\index\\')) {
            const fileName = path.basename(filePath, path.extname(filePath));
            
            // 特殊处理一些文件
            const specialMappings = {
                'managementQuery': 'managementQuery',
                'sqlCores': 'sqlCores',
                'sqlTable': 'sqlTable',
                'ldpDataObservation': 'ldpDataObservation',
                'ldpTable': 'ldpTable',
                'analyseConfig': 'analyseConfig',
                'analyseData': 'analyseData',
                'mdbDataObservation': 'mdbDataObservation',
                'rcmDeploy': 'rcmDeploy',
                'mcDeploy': 'mcDeploy'
            };
            
            return specialMappings[fileName] || fileName;
        }
        
        // 处理components目录下的文件
        if (filePath.includes('components/') || filePath.includes('components\\')) {
            const parts = filePath.split(/[/\\]/);
            const componentIndex = parts.findIndex(part => part === 'components');
            if (componentIndex >= 0 && componentIndex + 1 < parts.length) {
                return parts[componentIndex + 1];
            }
        }
        
        return 'common';
    }

    /**
     * 生成更好的国际化key
     */
    generateBetterKey(text, context = '') {
        // 移除特殊字符和空格
        let cleanText = text.replace(/[^\u4e00-\u9fff\w\s]/g, ' ').trim();
        
        // 如果文本太长，尝试提取关键词
        if (cleanText.length > 20) {
            cleanText = this.extractKeywords(cleanText);
        }
        
        // 分词并转换
        const words = cleanText.split(/\s+/).filter(word => word.length > 0);
        const englishWords = words.map(word => this.translateWord(word));
        
        // 生成小驼峰格式的key
        const key = englishWords
            .map((word, index) => {
                if (index === 0) {
                    return word.toLowerCase();
                }
                return word.charAt(0).toUpperCase() + word.slice(1).toLowerCase();
            })
            .join('');
        
        return key || 'unknownText';
    }

    /**
     * 提取关键词
     */
    extractKeywords(text) {
        // 优先提取已知的关键词
        const keywords = [];
        
        for (const [chinese, english] of Object.entries(this.commonMappings)) {
            if (text.includes(chinese)) {
                keywords.push(chinese);
                text = text.replace(chinese, '');
            }
        }
        
        // 如果没有找到关键词，取前几个字符
        if (keywords.length === 0) {
            keywords.push(text.substring(0, 6));
        }
        
        return keywords.join(' ');
    }

    /**
     * 翻译单词
     */
    translateWord(word) {
        // 直接映射
        if (this.commonMappings[word]) {
            return this.commonMappings[word];
        }
        
        // 部分匹配
        for (const [chinese, english] of Object.entries(this.commonMappings)) {
            if (word.includes(chinese)) {
                return english;
            }
        }
        
        // 如果是纯中文，使用通用标识
        if (/^[\u4e00-\u9fff]+$/.test(word)) {
            return 'text';
        }
        
        // 如果是英文或数字，直接返回
        if (/^[a-zA-Z0-9]+$/.test(word)) {
            return word;
        }
        
        return 'text';
    }

    /**
     * 按路由组织数据
     */
    organizeByRoute() {
        const organized = {
            common: {},
            pages: {},
            components: {}
        };
        
        // 遍历所有提取的数据
        Object.entries(this.mappingData).forEach(([filePath, texts]) => {
            const routeName = this.inferRouteFromFilePath(filePath);
            
            // 确定分类
            let category = 'common';
            if (filePath.includes('views/') || filePath.includes('views\\')) {
                category = 'pages';
            } else if (filePath.includes('components/') || filePath.includes('components\\')) {
                category = 'components';
            }
            
            // 初始化路由对象
            if (!organized[category][routeName]) {
                organized[category][routeName] = {};
            }
            
            // 处理每个文案
            texts.forEach(({ chinese, file }) => {
                const betterKey = this.generateBetterKey(chinese, routeName);
                
                // 避免重复key
                let finalKey = betterKey;
                let counter = 1;
                while (organized[category][routeName][finalKey]) {
                    finalKey = `${betterKey}${counter}`;
                    counter++;
                }
                
                organized[category][routeName][finalKey] = chinese;
            });
        });
        
        return organized;
    }

    /**
     * 生成最终的国际化文件
     */
    generateFinalI18nFiles() {
        // 确保输出目录存在
        if (!fs.existsSync(this.outputDir)) {
            fs.mkdirSync(this.outputDir, { recursive: true });
        }
        
        const organizedData = this.organizeByRoute();
        
        // 生成中文版本
        const zhCN = {
            common: organizedData.common,
            pages: organizedData.pages,
            components: organizedData.components
        };
        
        // 生成英文版本（暂时使用中文，需要后续翻译）
        const enUS = JSON.parse(JSON.stringify(zhCN));
        
        // 写入文件
        this.writeFile('zh-CN.js', `export default ${JSON.stringify(zhCN, null, 2)};`);
        this.writeFile('en-US.js', `export default ${JSON.stringify(enUS, null, 2)};`);
        
        // 生成索引文件
        this.generateIndexFile();
        
        // 生成统计报告
        this.generateOrganizedReport(organizedData);
    }

    /**
     * 生成索引文件
     */
    generateIndexFile() {
        const indexContent = `import zhCN from './zh-CN';
import enUS from './en-US';

export default {
    'zh-CN': zhCN,
    'en-US': enUS
};`;
        
        this.writeFile('index.js', indexContent);
    }

    /**
     * 写入文件
     */
    writeFile(filename, content) {
        const filePath = path.join(this.outputDir, filename);
        fs.writeFileSync(filePath, content, 'utf-8');
        console.log(`✅ 生成文件: ${filePath}`);
    }

    /**
     * 生成组织后的报告
     */
    generateOrganizedReport(organizedData) {
        const report = {
            summary: {
                totalCategories: Object.keys(organizedData).length,
                totalRoutes: Object.values(organizedData).reduce((sum, category) => sum + Object.keys(category).length, 0),
                totalTexts: Object.values(organizedData).reduce((sum, category) => 
                    sum + Object.values(category).reduce((catSum, route) => catSum + Object.keys(route).length, 0), 0),
                organizeTime: new Date().toISOString()
            },
            categories: {}
        };
        
        Object.entries(organizedData).forEach(([categoryName, category]) => {
            report.categories[categoryName] = {
                routeCount: Object.keys(category).length,
                textCount: Object.values(category).reduce((sum, route) => sum + Object.keys(route).length, 0),
                routes: Object.fromEntries(
                    Object.entries(category).map(([routeName, texts]) => [
                        routeName,
                        { count: Object.keys(texts).length }
                    ])
                )
            };
        });
        
        this.writeFile('organized-report.json', JSON.stringify(report, null, 2));
        
        console.log('\n📊 组织统计:');
        console.log(`总分类数: ${report.summary.totalCategories}`);
        console.log(`总路由数: ${report.summary.totalRoutes}`);
        console.log(`总文案数: ${report.summary.totalTexts}`);
        
        Object.entries(report.categories).forEach(([categoryName, category]) => {
            console.log(`\n📁 ${categoryName}:`);
            console.log(`  路由数: ${category.routeCount}`);
            console.log(`  文案数: ${category.textCount}`);
            
            // 显示前5个路由
            const topRoutes = Object.entries(category.routes)
                .sort((a, b) => b[1].count - a[1].count)
                .slice(0, 5);
            
            topRoutes.forEach(([routeName, { count }]) => {
                console.log(`    ${routeName}: ${count} 条`);
            });
        });
    }

    /**
     * 执行组织
     */
    run() {
        console.log('🚀 开始组织国际化文案...');
        console.log(`输入目录: ${this.extractedDir}`);
        console.log(`输出目录: ${this.outputDir}`);
        
        this.generateFinalI18nFiles();
        
        console.log('✨ 组织完成!');
    }
}

// 执行脚本
if (require.main === module) {
    const organizer = new I18nOrganizer();
    organizer.run();
}

module.exports = I18nOrganizer;
