import { mdbLogin, mdbLoginout, mdbUpdatePassword } from '@/api/mdbPrivilegeApi';
import Message from 'h_ui/dist/lib/Message';

export const state = () => ({
});

export const mutations = {
    setUserId(state, userId) {
        localStorage.setItem('mdbUserId', userId);
    },
    setToken(state, token) {
        localStorage.setItem('mdbToken', token);
    },
    removeToken(state) {
        localStorage.removeItem('mdbToken');
    },
    removeUserId(state) {
        localStorage.removeItem('mdbUserId');
    }
};

export const actions = {
    // 登录
    mdbLogin(context, param) {
        return new Promise((resolve, reject) => {
            mdbLogin(param).then(res => {
                if (res.code === '200') {
                    context.commit('setUserId', res.data.id || '');
                    context.commit('setToken', res.headers.authorization || '');
                    resolve(true);
                } else if (res.code.length === 8){
                    Message.error(res.message);
                    resolve(false);
                } else  {
                    resolve(false);
                }
            }).catch(err => {
                reject(err);
            });
        });
    },
    // 登出
    mdbLoginout(context) {
        return new Promise((resolve, reject) => {
            mdbLoginout().then(res => {
                if (res.code === '200') {
                    context.commit('removeToken');
                    context.commit('removeUserId');
                    resolve(true);
                }  else if (res.code.length === 8){
                    Message.error(res.message);
                    resolve(false);
                } else {
                    resolve(false);
                }
            }).catch(err => {
                reject(err);
            });
        });
    },
    // 更新密码
    mdbUpdatePassword(context, param){
        return new Promise((resolve, reject) => {
            mdbUpdatePassword(param).then(async res => {
                if (res.code === '200') {
                    context.commit('removeToken');
                    context.commit('removeUserId');
                    resolve(true);
                } else if (res.code.length === 8){
                    Message.error(res.message);
                    resolve(false);
                } else {
                    resolve(false);
                }
            }).catch(err => {
                reject(err);
            });
        });
    },
    // 自动续约token
    updateMdbToken(context, token){
        context.commit('setToken', token || '');
    }
};
