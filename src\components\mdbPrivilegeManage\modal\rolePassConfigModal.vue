<!-- 角色权限配置 -->
<template>
    <div>
        <h-msg-box-safe
            v-model="modalData.status"
            :mask-closable="false"
            :title="`权限配置-${modalData.roleName}`"
            width="70"
            height="500"
            allowCopy
            @on-cancel="handleCancel"
        >
            <h-form
                ref="formValidate"
                :model="formValidate"
                :label-width="80"
                :hidePopperTime="3000"
            >
                <h-form-item
                    label="赋权方式"
                    prop="permissionsType"
                    required
                    style="margin-bottom: 10px;"
                >
                    <h-radio-group
                        v-model="formValidate.permissionsType"
                        type="button"
                        :diasbled="loading"
                        @on-change="initData"
                    >
                        <h-radio label="OPERATION">按操作赋权</h-radio>
                        <h-radio label="TABLE">按表赋权</h-radio>
                    </h-radio-group>
                    <p
                        v-show="formValidate.permissionsType === 'OPERATION'"
                        class="text"
                    >
                        为角色赋予MDB集群对应权限（select/update/delete/insert）。集群中新增表时，角色可控表同步增加。
                    </p>
                    <p
                        v-show="formValidate.permissionsType === 'TABLE'"
                        class="text"
                    >
                        为角色赋予详细表权限（select/update/delete/insert）。集群中新增表时，角色可控表不变。
                    </p>
                </h-form-item>
                <h-form-item
                    v-if="formValidate.permissionsType === 'OPERATION'"
                    label="权限"
                    prop="permissions"
                    style="margin-bottom: 0;"
                >
                    <cluster-role-permissions-info
                        ref="cluster-role-permissions-info"
                        :productId="modalData.productId"
                        :roleId="modalData.roleId"
                        :isReadonly="loading"
                        type="edit"
                    ></cluster-role-permissions-info>
                </h-form-item>
                <h-form-item
                    v-if="formValidate.permissionsType === 'TABLE'"
                    label="权限"
                    prop="permissions"
                    style="margin-bottom: 0;"
                >
                    <role-permissions-info
                        ref="role-permissions-info"
                        :productId="modalData.productId"
                        :roleId="modalData.roleId"
                        :isReadonly="loading"
                         type="edit"
                    ></role-permissions-info>
                </h-form-item>
            </h-form>
            <template v-slot:footer>
                <a-button :disabled="loading" @click="handleCancel">取消</a-button>
                <a-button type="primary" :loading="loading" @click="submitConfig">确定</a-button>
            </template>
        </h-msg-box-safe>
    </div>
</template>

<script>
import aButton from '@/components/common/button/aButton';
import rolePermissionsInfo from '@/components/mdbPrivilegeManage/modal/rolePermissionsInfo';
import clusterRolePermissionsInfo from '@/components/mdbPrivilegeManage/modal/clusterRolePermissionsInfo';
import { setRolesAuthorization } from '@/api/mdbPrivilegeApi';

export default {
    name: 'RolePassConfigModal',
    components: { aButton, rolePermissionsInfo, clusterRolePermissionsInfo },
    props: {
        modalInfo: {
            type: Object,
            default: null
        }
    },
    data() {
        return {
            modalData: this.modalInfo,
            formValidate: {
                permissionsType: this.modalInfo?.permissionsType || 'TABLE'
            },
            loading: false
        };
    },
    mounted() {
        // 初始化
        this.initData();
    },
    methods: {
        initData(){
            this.$nextTick(() => {
                if (this.formValidate.permissionsType === 'TABLE') {
                this.$refs?.['role-permissions-info'] && this.$refs['role-permissions-info'].initData();
                }
                if (this.formValidate.permissionsType === 'OPERATION') {
                this.$refs?.['cluster-role-permissions-info'] && this.$refs['cluster-role-permissions-info'].initData();
                }
            });
        },
        async submitConfig() {
            this.loading = true;
            let list = [];
            if (this.formValidate.permissionsType === 'OPERATION'){
                list = this.$refs['cluster-role-permissions-info'].getTableEditInfos();
            } else if (this.formValidate.permissionsType === 'TABLE'){
                list = this.$refs['role-permissions-info'].getTableEditInfos();
            }
            // 未做任何更改
            if (!list.length){
                this.modalData.status = false;
                this.loading = false;
                return;
            }

            const param = {
                productId: this.modalData.productId,
                roleId: this.modalData.roleId,
                clusterPrivileges: list,
                type: this.formValidate.permissionsType
            };
            const res = await setRolesAuthorization(param);
            if (res.code === '200'){
                this.$hMessage.success('权限配置成功！');
            } else if (res.code.length === 8){
                this.$hMessage.error(res.message || '权限配置失败！');
            }
            // 权限配置完成刷新表格
            this.$emit('update');
            this.loading = false;
            this.modalData.status = false;
        },
        handleCancel() {
            this.$refs['role-permissions-info'] && this.$refs['role-permissions-info'].clearAllData();
            this.$refs['cluster-role-permissions-info'] && this.$refs['cluster-role-permissions-info'].clearAllData();
            this.modalData.status = false;
        }
    }
};
</script>

<style lang="less" scoped>
/deep/ .h-modal-body {
    padding: 16px 20px;
}

.text {
    font-size: 12px;
    color: #9296a1;
    line-height: 20px;
    font-weight: 400;
}
</style>
