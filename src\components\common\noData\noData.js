import './noData.less';
export default {
    name: 'noData',
    props: {
        width: {
            type: String,
            default: '150'
        },
        height: {
            type: String,
            default: '100'
        },
        imgSrc: {
            type: String,
            default: ''
        },
        text: {
            type: String,
            default: '暂无数据'
        },
        isWhite: {
            type: Boolean,
            default: false
        }
    },
    data() {
        return {

        };
    },
    render() {
        return <div class="no-data">
            <img width={this.width} height={this.height} src={this.imgSrc || `${this.IMG_HOME}static/${this.isWhite ? 'noDataWhite' : 'noData'}.png` } alt="图片无法显示" />
            <div class='text'>{this.text}</div>
        </div>;
    }
};
