
import _ from 'lodash';
import * as echarts from 'echarts';

// 颜色字典
const colors = {
    avg: [253, 221, 96],
    max: [255, 110, 118],
    min: [74, 146, 255],
    p50: [90, 216, 166],
    p60: [88, 217, 249],
    p70: [4, 192, 145],
    p80: [255, 138, 69],
    p90: [141, 72, 227],
    p95: [221, 121, 255],
    p96: [83, 74, 255],
    p97: [194, 200, 213],
    p98: [255, 137, 167],
    p99: [255, 222, 173]
};
const labelColor = '#fff'; // 标签文字颜色
const axisColor = '#31364a'; // 坐标轴颜色
// 标准样式
const option = {
    backgroundColor: '#262b40',
    title: {
        text: '',
        textStyle: {
            color: labelColor,
            fontSize: 12
        },
        top: 0
    },
    grid: {
        left: 50,
        right: 1,
        bottom: 30,
        top: 40
    },
    // 提示框
    tooltip: {
        trigger: 'axis',
        backgroundColor: 'rgba(88,94,106,0.40)',
        borderColor: 'rgba(88,94,106,0.40)',
        textStyle: {
            color: labelColor,
            fontSize: 12
        },
        formatter (params) {
            let relVal = params[0].name;
            for (let i = 0, l = params.length; i < l; i += 1) {
                relVal += `<br/>${params[i].marker}${params[i].seriesName}：${params[i].value} μs`;
            }
            return relVal;
        },
        // eslint-disable-next-line max-params
        position (point, params, dom, rect, size) {
            // 鼠标坐标和提示框位置的参考坐标系是：以外层div的左上角那一点为原点，x轴向右，y轴向下
            // 提示框位置
            let x = 0; // x坐标位置
            let y = 0; // y坐标位置

            // 当前鼠标位置
            const pointX = point[0];
            const pointY = point[1];

            // 外层div大小
            // var viewWidth = size.viewSize[0];
            // var viewHeight = size.viewSize[1];

            // 提示框大小
            const boxWidth = size.contentSize[0];
            const boxHeight = size.contentSize[1];

            // boxWidth > pointX 说明鼠标左边放不下提示框
            if (boxWidth > pointX) {
                x = 5;
            } else {
                // 左边放的下
                x = pointX - boxWidth;
            }

            // boxHeight > pointY 说明鼠标上边放不下提示框
            if (boxHeight > pointY) {
                y = 5;
            } else {
                // 上边放得下
                y = pointY - boxHeight;
            }

            return [x, y];
        }
    },
    legend: {
        textStyle: {
            color: labelColor,
            fontSize: 11,
            padding: [0, 0, 0, 10]
        },
        itemHeight: 1,
        itemWidth: 16
    },
    xAxis: {
        type: 'category',
        boundaryGap: false, // 坐标轴两边留白策略
        axisLine: {
            show: true,
            lineStyle: {
                color: axisColor
            }
        },
        axisTick: {
            show: false // 是否显示坐标轴刻度
        },
        axisLabel: {
            color: labelColor,
            textStyle: {
                width: '0'
            },
            // 自定义富文本样式
            rich: {
                a: {
                    align: 'right'

                },
                b: {
                    align: 'left'
                }
            }
        },
        data: []
    },
    yAxis: [
        {
            type: 'value',
            axisLine: {
                show: true,
                lineStyle: {
                    color: axisColor
                }
            },
            axisLabel: {
                color: labelColor
            },
            splitLine: {
                lineStyle: {
                    color: axisColor
                }
            }
        },
        {
            axisLine: {
                show: true,
                lineStyle: {
                    color: axisColor
                }
            }
        }
    ],
    toolbox: {
        right: 10
    },
    series: [{
        name: '',
        type: 'line',
        showSymbol: false,
        data: []
    }]
};

// 传入option参数新增合并或替换默认option属性
export const echartOptionAssign = (optionParam = {}, originOption = option) => {
    const newOption = originOption;
    if (typeof optionParam !== 'object' || typeof originOption !== 'object') {
        return optionParam || originOption;
    }
    for (const key in optionParam) {
        if (originOption.hasOwnProperty(key)) {
            newOption[key] = echartOptionAssign(optionParam[key], originOption[key]);
        } else {
            newOption[key] = optionParam[key];
        }
    }
    return newOption;
};

// 根据线条标签名匹配特定颜色（用于区域面积样式）
export const echartLineColor = (name) => {
    const [r, g, b] = colors[name];
    return [`rgba(${r}, ${g}, ${b}, 1)`, `rgba(${r}, ${g}, ${b}, .5)`, `rgba(${r}, ${g}, ${b}, .1)`];
};

/**
 * @description 折线图数据配置
 * @param {string} title 折线图标题
 * @param {array} xData 折线图横坐标数据
 * @param {array} yData 折线图纵坐标数据 [{name: '', label: 标签名 vaule: [], lineType: 'dotted'(可选，线型)}, ...] name：图例名，value: 数据
 * @param {object} optionParam（可选） 传入的option配置 若存在即采用，不存在采用原始配置
 */
// eslint-disable-next-line max-params
export const echartSeries = (titleText, xData, yData, optionParam) => {
    const newOption = echartOptionAssign(optionParam) || _.cloneDeep(option);
    newOption.title.text = titleText;
    newOption.xAxis.data = xData;
    const yList = [];
    // 数组类型需要保护
    Array.isArray(yData) && yData.forEach((item) => {
    // specifyColor定义yData的item项是否定义指定颜色, false不指定，采用echart默认样式，无阴影
        const specifyColor = Object.keys(colors).includes(item.name);
        yList.push({
            name: item.label,
            type: 'line',
            smooth: true,
            showSymbol: false,
            // sampling: 'lttb', // 降采样策略, 优化图表的绘制效率
            data: item.data,
            lineStyle: {
                width: 1,
                color: echartLineColor(item.name)?.[0],
                type: item.lineType || 'solid'
            },
            itemStyle: {
                color: echartLineColor(item.name)?.[0]
            },
            areaStyle: specifyColor ? {
                opacity: 0.8,
                color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                    {
                        offset: 0.1,
                        color: echartLineColor(item.name)[0]
                    },
                    {
                        offset: 0.5,
                        color: echartLineColor(item.name)[1]
                    },
                    {
                        offset: 1,
                        color: echartLineColor(item.name)[2]
                    }
                ])
            } : undefined
        });
    });
    newOption.series = yList;
    return newOption;
};

