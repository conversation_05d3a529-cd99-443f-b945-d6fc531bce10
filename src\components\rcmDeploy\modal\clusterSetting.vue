<template>
    <div class="normal-form">
        <h-form ref="clusterFormValidate" :model="formValidate" :rules="ruleValidate" :label-width="160" cols="2">
            <h-form-item label="同步网卡地址:" prop="syncIp" :validRules="ip4Rule" :required="required">
                <h-input v-if="!readOnly"  v-model="formValidate.syncIp"></h-input>
                <p v-else>{{ formValidate.syncIp }}</p>
            </h-form-item>
            <h-form-item label="同步通讯地址:" prop="syncAddr" :validRules="ip4Rule" :required="required">
                <h-input  v-if="!readOnly" v-model="formValidate.syncAddr"></h-input>
                <p v-else>{{ formValidate.syncAddr }}</p>
            </h-form-item>
            <h-form-item label="同步通讯端口:" prop="syncPort" :validRules="portRule" :required="required">
                <h-input v-if="!readOnly" v-model="formValidate.syncPort" :maxlength="5" type="int"></h-input>
                <p v-else>{{ formValidate.syncPort }}</p>
            </h-form-item>
            <h-form-item label="同步心跳间隔(毫秒):" prop="syncHeartbeatIntervalMilli" :required="required">
                <h-input  v-if="!readOnly" v-model="formValidate.syncHeartbeatIntervalMilli" :specialFilter="true"
                    :specialDecimal="2"></h-input>
                <p v-else>{{ formValidate.syncHeartbeatIntervalMilli }}</p>
            </h-form-item>
            <h-form-item label="同步心跳超时(毫秒):" prop="syncHeartbeatTimeoutMilli" :required="required">
                <h-input  v-if="!readOnly" v-model="formValidate.syncHeartbeatTimeoutMilli" :specialFilter="true"
                    :specialDecimal="2"></h-input>
                    <p v-else>{{ formValidate.syncHeartbeatTimeoutMilli }}</p>
            </h-form-item>
            <h-form-item label="同步Ack间隔(毫秒):" prop="syncAckIntervalMilli" :required="required">
                <h-input  v-if="!readOnly" v-model="formValidate.syncAckIntervalMilli" :specialFilter="true" :specialDecimal="2"></h-input>
                <p v-else>{{ formValidate.syncAckIntervalMilli }}</p>
            </h-form-item>
            <h-form-item label="同步Ack超时(毫秒):" prop="syncAckTimeoutMilli" :required="required">
                <h-input  v-if="!readOnly" v-model="formValidate.syncAckTimeoutMilli" :specialFilter="true" :specialDecimal="2"></h-input>
                <p v-else>{{ formValidate.syncAckTimeoutMilli }}</p>
            </h-form-item>
            <h-form-item label="同步发送窗口大小:" prop="syncSendWindowSize" :required="required">
                <h-input  v-if="!readOnly" v-model="formValidate.syncSendWindowSize" :specialFilter="true" :specialDecimal="2"></h-input>
                <p v-else>{{ formValidate.syncSendWindowSize }}</p>
            </h-form-item>
            <h-form-item label="同步最大传输单元:" prop="syncMtu" :required="required">
                <h-input  v-if="!readOnly" v-model="formValidate.syncMtu" :specialFilter="true" :specialDecimal="2"></h-input>
                <p v-else>{{ formValidate.syncMtu }}</p>
            </h-form-item>
            <h-form-item label="中心名:" prop="zone">
                <h-select v-if="!readOnly" v-model="formValidate.zone" positionFixed placement="top" :clearable="false" >
                    <h-option v-for="item in zones" :key="item.id" :value="item.name">{{ item.name }}</h-option>
                </h-select>
                <p v-else>{{ formValidate.zone || '-' }}</p>
            </h-form-item>
            <h-form-item label="同步模式:" :required="required">
                <h-select  v-if="!readOnly" v-model="formValidate.syncMode" :clearable="false" :positionFixed="true" placement="top">
                    <h-option v-for="item in asycModeList" :key="'asycModeList' + item.value" :value="item.value">{{
                        item.label }}
                    </h-option>
                </h-select>
                <p v-else>{{ Number(formValidate.syncMode) === 1 ? '消息复制': '状态机复制' }}</p>
            </h-form-item>
        </h-form>
    </div>
</template>

<script>
import { validatePort } from '@/utils/validate';
import { getZones } from '@/api/rcmApi';

export default {
    props: {
        saveValidateData: {
            type: Object,
            default: {}
        },
        readOnly: {
            type: Boolean,
            default: false
        },
        rcmId: {
            type: String,
            default: null
        }
    },
    name: 'ClusterSetting',
    data() {
        return {
            asycModeList: [{
                value: 0,
                label: '状态机复制'
            }, {
                value: 1,
                label: '消息复制'
            }],
            formValidate: this.saveValidateData,
            ip4Rule: ['ip4'],
            portRule: [{ test: validatePort, trigger: 'blur' }],
            ruleValidate: {
            },
            zones: [],
            required: true
        };
    },
    mounted() {
        this.required = !this.readOnly;
        this.init();
    },
    methods: {
        init(){
            this.formValidate = { ...this.saveValidateData };
            this.queryZones();
        },
        async queryZones() {
            if (this.rcmId) {
                const res = await getZones({ rcmId: this.rcmId });
                if (res.code === '200') {
                    this.zones = res.data || [];
                }
            }
        },
        getFileData() {
            let data = '';
            this.$refs['clusterFormValidate'].validate((valid) => {
                if (valid) {
                    data =  {
                        ...this.formValidate,
                        syncPort: Number(this.formValidate.syncPort),
                        syncHeartbeatIntervalMilli: Number(this.formValidate.syncHeartbeatIntervalMilli),
                        syncHeartbeatTimeoutMilli: Number(this.formValidate.syncHeartbeatTimeoutMilli),
                        syncAckIntervalMilli: Number(this.formValidate.syncAckIntervalMilli),
                        syncAckTimeoutMilli: Number(this.formValidate.syncAckTimeoutMilli),
                        syncSendWindowSize: Number(this.formValidate.syncSendWindowSize),
                        syncMtu: Number(this.formValidate.syncMtu),
                        syncMode: Number(this.formValidate.syncMode)
                    };
                }
            });
            return data;
        }
    }
};
</script>
