<template>
  <div class="main">
    <a-title title="交易节点委托时延明细" style="min-width: 1000px;">
      <slot>
        <h-select
            v-show="productList.length > 1"
            v-model="productInstNo"
            class="title-single-select"
            placeholder="请选择"
            :positionFixed="true"
            :clearable="false"
            @on-change="getProductManageInfo"
        >
          <h-option
            v-for="item in productList"
            :key="item.id"
            :value="item.productInstNo"
            >{{ item.productName }}</h-option
          >
        </h-select>
      </slot>
    </a-title>
    <tab-title-table
      v-if="!loading && tabTableKey"
      ref="tab"
      :tabTableList="tabTableList"
      :tabTableKey="tabTableKey"
      style="margin-top: 10px;"
      @query="handleQuery"
      @save="handleSave"
      @tagClose="handleTagClose"
      @tagClick="handleTagClick"
      @queryProposal="computerEntrustProposal"
      @changeTab="handleChangeTab"
      @handleSelectChange="handleSelectChange"
      @generateQueryData="generateQueryData"
    >
    </tab-title-table>
    <a-loading
        v-if="loading"
        style="top: 55px; height: calc(100% - 55px);" />
    <div
        v-if="!tabTableKey"
        style="width: 99%; height: calc(100% - 70px); position: absolute; top: 60px;">
        <apm-blank name="Config">
            <slot>
                <div>当前产品尚未配置模型，请前往“产品服务配置”进行模型配置</div>
                <br />
                <a-button type="primary" @click="$hCore.navigate(`/productServiceList`);">前往配置</a-button>
            </slot>
        </apm-blank>
    </div>
    <save-modal
      v-if="modalInfo.status"
      :modalInfo="modalInfo"
      @update="handleDataQuery"
    />
    <h-drawer
      v-model="visible"
      placement="down"
      :title="drawerTitle"
      :height="60"
    >
      <all-link-topo
        v-if="penetrationViewMode"
        ref="topo"
        :template="penetrationViewMode"
        style="height: calc(100% - 30px);"/>

      <app-topo
        v-else-if="applicationViewMode"
        ref="appTopo"
        :template="applicationViewMode"
        style="height: calc(100% - 30px);"
      />
      <div class="footer" style="width: 100%; height: 30px;">
        <a-button type="dark" @click="handlePageTopo('prev')">上一条</a-button>
        <a-button type="dark" @click="handlePageTopo('next')">下一条</a-button>
      </div>
    </h-drawer>
  </div>
</template>

<script>
import _ from 'lodash';
import { mapState, mapActions } from 'vuex';
import tabTitleTable from '@/components/common/bestTable/ldpTabTable';
import aTitle from '@/components/common/title/aTitle';
import aButton from '@/components/common/button/aButton';
import aLoading from '@/components/common/loading/aLoading';
import appTopo from '@/components/common/topo/appTopo';
import apmBlank from '@/components/common/apmBlank/apmBlank';
import saveModal from '@/components/productTimeAnalysis/saveModal.vue';
import allLinkTopo from '@/components/common/topo/allLinkTopo';
import {
    isJSON,
    cutZero,
    formatDate,
    getTodayDate,
    getYesterdayDate
} from '@/utils/utils';
import { getTraceModels } from '@/api/productApi';
import {
    queryTradeDetail,
    deleteQuickQuery,
    getQuickQueryList,
    saveQuickQueryList,
    computerEntrustProposal
} from '@/api/httpApi';
export default {
    name: 'ProductTimeDetail',
    data() {
        return {
            loading: false,
            productInstNo: '',
            productInfo: {},
            tabTableKey: '',
            tabTableList: [],
            modalInfo: {
                status: false,
                type: 'entrustDetail'
            },
            topoIndex: 0,
            visible: false,
            drawerTitle: '',
            traceModelConfigs: [],
            applicationViewMode: '',
            penetrationViewMode: ''
        };
    },
    async mounted() {
        await this.getProductList({ filter: 'supportEntrustLatency' });
        if (this.productList.length) {
            const id = localStorage.getItem('productInstNo');
            this.productInstNo =
            _.find(this.productList, ['productInstNo', id])?.productInstNo ||
            this.productList?.[0]?.productInstNo;
        }
    },
    methods: {
        ...mapActions({ getProductList: 'product/getProductListLight' }),
        // 查询产品详细信息
        async getProductManageInfo(e) {
            this.productInfo = _.find(this.productList, [
                'productInstNo',
                this.productInstNo
            ]);
            localStorage.setItem('productInstNo', this.productInstNo);
            this.loading = true;
            try {
                await this.getTraceModels();
            } catch (error) {
                console.log(error);
            }
            this.loading = false;

            // 表格重查询数据
            this.$nextTick(async () => {
                // 获取快捷查询列表
                this.tabTableList.forEach(async (ele, idx) => {
                    await this.getQuickQueryList(this.modalInfo.type, idx);
                    ele.tabTableData[0].tableData = [];
                });
                this.$refs['tab'] && this.$refs['tab'].$_handleResetPageData();
            });
        },
        // 根据产品节点获取链路模型
        async getTraceModels() {
            try {
                const res = await getTraceModels({ productId: this.productInstNo });
                const list = res?.data || [];
                this.tabTableList = [];
                this.traceModelConfigs = list;
                list.forEach((item, index) => {
                    const bizTags = item.bizTags || [];
                    const data = {
                        fixFormItems: [
                            {
                                type: 'select',
                                label: '链路类型',
                                key: 'bizTraceType',
                                options: [],
                                value: '',
                                required: true
                            },
                            {
                                type: 'date',
                                key: 'date',
                                label: '查询日期',
                                value: new Date(),
                                required: true
                            },
                            {
                                type: 'timerange',
                                key: 'time',
                                label: '查询时间',
                                value: ['09:15:00', '15:00:00'],
                                required: true,
                                placement: 'bottom-end'
                            },
                            {
                                type: 'timescreen',
                                label: '时延筛选',
                                key: 'queryFieldName',
                                options: [],
                                placement: 'bottom',
                                value: ''
                            }
                        ],
                        freeFormItems: bizTags
                    };

                    const bizTraceTypes = item?.bizTraceTypes || [];
                    const newBizTraceTypes = _.filter(bizTraceTypes, { enable: true });
                    data.fixFormItems[0].options = newBizTraceTypes.map(({ bizTraceType, bizTraceTypeAlias, spans }) => ({
                        label: bizTraceTypeAlias,
                        value: bizTraceType,
                        spans
                    }));
                    data.fixFormItems[0].value = newBizTraceTypes?.[0]?.bizTraceType;

                    const groupedArray = [];
                    for (let i = 0; i < bizTags.length; i += 2) {
                        groupedArray.push(bizTags.slice(i, i + 2));
                    }
                    this.$set(this.tabTableList, index, {
                        label: item.bizTypeAlias,
                        key: item.bizType,
                        name: 'entrustDetail',
                        ...data,
                        tabTableData: [
                            {
                                label: '查询结果',
                                name: 'result',
                                type: 'table',
                                tableData: [],
                                columns: [
                                    {
                                        title: '委托时间',
                                        key: 'clientReqTimestamp',
                                        minWidth: 100,
                                        disabled: true,
                                        sortable: 'custom'
                                    },
                                    {
                                        title: '业务字段',
                                        key: 'action',
                                        fixed: 'right',
                                        width: 80,
                                        disabled: true,
                                        render: (h, params) => {
                                            return h('div', [
                                                h(
                                                    'Poptip',
                                                    {
                                                        class: 'apm-poptip',
                                                        props: {
                                                            title: '业务字段',
                                                            placement: 'left-end',
                                                            positionFixed: true,
                                                            trigger: 'hover'
                                                        }
                                                    },
                                                    [
                                                        h(
                                                            'Button',
                                                            {
                                                                props: {
                                                                    size: 'small',
                                                                    type: 'text'
                                                                }
                                                            },
                                                            '查看'
                                                        ),
                                                        h(
                                                            'div',
                                                            {
                                                                slot: 'content',
                                                                class: ''
                                                            },
                                                            [
                                                                groupedArray.map((item) => {
                                                                    return h(
                                                                        'p',
                                                                        {
                                                                            style: {
                                                                                paddingBottom: '6px'
                                                                            }
                                                                        },
                                                                        [
                                                                            ...item.map((ele, idx) => {
                                                                                const result = params.row[ele.key] || '-';
                                                                                return h(
                                                                                    'span',
                                                                                    {
                                                                                        style: {
                                                                                            display: 'inline-block',
                                                                                            width: '200px'
                                                                                        }
                                                                                    },
                                                                                    [
                                                                                        h(
                                                                                            'span',
                                                                                            {
                                                                                                style: {
                                                                                                    paddingLeft: idx ? '20px' : 0,
                                                                                                    color: 'var(--font-opacity-color)'
                                                                                                }
                                                                                            },
                                                                                            `${ele.label}：`
                                                                                        ),
                                                                                        h(
                                                                                            'em',
                                                                                            {
                                                                                                style: {
                                                                                                    fontStyle: 'normal'
                                                                                                }
                                                                                            },
                                                                                            `${result}`
                                                                                        )
                                                                                    ]
                                                                                );
                                                                            })
                                                                        ]
                                                                    );
                                                                })
                                                            ]
                                                        )
                                                    ]
                                                )
                                            ]);
                                        }
                                    },
                                    {
                                        title: '拓扑结构',
                                        key: 'firewallDownLinkLatency',
                                        fixed: 'right',
                                        minWidth: 80,
                                        disabled: true,
                                        render: (h, params) => {
                                            return (
                                                'div',
                                                [
                                                    h(
                                                        'Button',
                                                        {
                                                            props: {
                                                                size: 'small',
                                                                type: 'text'
                                                            },
                                                            on: {
                                                                click: () => {
                                                                    this.topoIndex = params.index;
                                                                    this.handleDrawer(params.row);
                                                                }
                                                            }
                                                        },
                                                        '查看'
                                                    )
                                                ]
                                            );
                                        }
                                    }
                                ],
                                hasPage: true,
                                total: 0
                            }
                        ],
                        proposalList: [], // 时延筛选
                        tableLoading: false,
                        queryId: '',
                        queryList: [],
                        spans: item.spans
                    });
                });

                this.tabTableKey = this.tabTableList?.[0]?.key;

                this.handleSelectChange('bizTraceType', this.tabTableList?.[0]?.fixFormItems[0]?.value);
            } catch (error) {
                console.log(error);
            }
        },
        // 获取所有快捷查询
        async getQuickQueryList(type, idx) {
            this.tabTableList[idx].queryList = [];
            this.tabTableList[idx].queryId = '';
            const res = await getQuickQueryList({
                type: `${type}-${this.tabTableList?.[idx]?.key}`,
                productInstNo: this.productInstNo
            });
            if (res?.success) {
                this.tabTableList[idx].queryList = res.data || [];
                this.$nextTick(() => {
                    this.tabTableList[idx].queryList?.length &&
                    this.$refs['tab'] &&
                    this.$refs['tab'].scrollReset();
                });
            } else {
                this.$hMessage.error('获取快捷查询失败!');
            }
        },
        // 根据id获取对应的查询条件
        getQueryCondition(id) {
            const tab = _.find(this.tabTableList, ['key', this.tabTableKey]);
            const data = _.find(tab.queryList, (o) => {
                return o?.id === id;
            });
            return data;
        },
        // 详情查询
        async handleDetailQuery(val) {
            const tab = _.find(this.tabTableList, ['key', this.tabTableKey]);
            try {
                tab.tableLoading = true;
                const param = {
                    productInstNo: this.productInstNo,
                    bizTraceType: val.bizTraceType,
                    bizType: this.tabTableKey,
                    startDate: `${formatDate(val.date)} ${val.time[0]}`,
                    endDate: `${formatDate(val.date)} ${val.time[1]}`,
                    queryFieldName: val.queryFieldName,
                    queryFieldCondition: val.queryFieldCondition,
                    queryFieldValue: this.handleUnitToNs(val.beforeFiledValue, val.unit),
                    page: val?.page || 1,
                    pageSize: val?.pageSize || 10
                };
                const res = await queryTradeDetail({ ...val, ...param });
                tab.tableLoading = false;
                if (res.success) {
                    tab.tabTableData[0].columns = [
                        tab.tabTableData[0].columns[0],
                        tab.tabTableData[0].columns[1],
                        tab.tabTableData[0].columns[2]
                    ];
                    if (!res?.data?.list?.length) {
                        const list = tab.fixFormItems?.[3]?.options || [];
                        for (const ele of list) {
                            const label = ele.label;
                            label && tab.tabTableData[0].columns.push({
                                title: label,
                                key: ele.value,
                                minWidth: label?.length * 20 || 80
                            });
                        }
                    } else {
                        const tab = _.find(this.tabTableList, ['key', this.tabTableKey]);
                        const allSpans = tab.spans || [];
                        const spanNameDesc = _.chain(allSpans)
                            .keyBy('spanName') // 将 spanName 作为键
                            .mapValues('spanAlias') // 将 spanAlias 作为值
                            .value();
                        res.data.list && res.data.list.forEach((item, idx) => {
                            for (const ele of Object.keys(item.spanLatency)) {
                                if (!idx) {
                                    const label = spanNameDesc?.[ele];
                                    label && tab.tabTableData[0].columns.push({
                                        title: ele || '',
                                        key: ele,
                                        align: 'right',
                                        minWidth: 120,
                                        renderHeader: (h, params) => {
                                            return h('div', {}, [
                                                h('span', {
                                                    style: {
                                                        color: '#fff',
                                                        verticalAlign: '2px'
                                                    }
                                                }, ele),
                                                h('poptip',
                                                    {
                                                        props: {
                                                            trigger: 'hover',
                                                            title: label,
                                                            placement: 'bottom-end',
                                                            positionFixed: true
                                                        }
                                                    },
                                                    [

                                                        h('icon', {
                                                            props: {
                                                                name: 'android-more-vertical',
                                                                color: '#fff'
                                                            }
                                                        })
                                                    ])
                                            ]);
                                        }
                                    });
                                }
                                item.spanLatency[ele] = cutZero((item.spanLatency[ele] / 1000 || 0).toFixed(3)) + ' μs';
                            }
                            res.data.list[idx] = {
                                ...res.data.list[idx],
                                ...item.spanLatency
                            };
                        });
                    }
                    const viewMode = this.getApplicationViewMode(param.bizTraceType);
                    if (viewMode?.meta?.modelPattern === 'link') {
                        this.penetrationViewMode = viewMode;
                        this.applicationViewMode = '';
                    } else {
                        this.applicationViewMode = viewMode;
                        this.penetrationViewMode = '';
                    }
                    // this.applicationViewMode = this.getApplicationViewMode(param.bizTraceType);
                    tab.tabTableData[0].tableData = res.data.list || [];
                    tab.tabTableData[0].total = res.data.totalCount || 0;
                } else {
                    this.$hMessage.error('查询失败!');
                }
            } catch (err) {
                tab.tableLoading = false;
                this.$hMessage.error(err.message);
                tab.tabTableData[0].tableData = [];
                console.log(err);
            }
        },
        // 点击查询按钮
        async handleQuery(val, sortKey, sortType) {
            await this.handleDetailQuery(val, sortKey, sortType);
            // 更新表格列和侧边栏多选项数据
            this.$refs['tab'].$_init();
        },
        // 保存
        handleSave(val) {
            this.modalInfo.time = val.time;
            delete val.date;
            delete val.time;
            this.modalInfo.status = true;
            this.modalInfo.queryBody =
            JSON.stringify({
                ...val
            }) || ''; // 当前form表单内容
        },
        // 保存并查询
        async handleDataQuery(val) {
            const index = _.findIndex(this.tabTableList, ['key', this.tabTableKey]);
            const props = {
                ...val,
                type: `${this.modalInfo.type}-${this.tabTableKey}`,
                productInstNo: this.productInstNo
            };
            const res = await saveQuickQueryList(props);
            if (res?.success) {
                await this.getQuickQueryList(this.modalInfo.type, index);
                this.$nextTick(() => {
                    this.$refs['tab'].handleTagClick(res?.data?.id);
                });
            } else {
                this.$hMessage.error('保存快捷查询失败!');
            }
        },
        // 删除
        async handleTagClose(id) {
            const index = _.findIndex(this.tabTableList, ['key', this.tabTableKey]);
            const name = this.getQueryCondition(id)?.name || '';
            this.$hMsgBoxSafe.confirm({
                title: `删除`,
                content: `您确定删除 '${name}'查询标签吗？`,
                onOk: async () => {
                    const res = await deleteQuickQuery(id);
                    if (res?.success) {
                        await this.getQuickQueryList(this.modalInfo.type, index);
                        // 表格重查询数据
                        this.$nextTick(() => {
                            this.$refs['tab'].$_handleResetPageData();
                        });
                    } else {
                        this.$hMessage.error('删除快捷查询失败!');
                    }
                }
            });
        },
        // 点击tag获取查询条件
        handleTagClick(id, callback) {
            const res = this.getQueryCondition(id);
            const val = res?.queryBody || '';
            const data = val?.length ? JSON.parse(val) : {};
            data.fixForms.time = [res.startTime, res.endTime];
            if (res.timeType === 'yesterday') {
                data.fixForms.date = getYesterdayDate();
            } else {
                data.fixForms.date = getTodayDate();
            }
            callback(data);
        },
        // 时间单位转换ns
        handleUnitToNs(time, unit) {
            return time * Math.pow(1000, 3 - unit);
        },
        // topo翻页（上一页）
        handlePrev() {
            if (this.topoIndex === 0) {
                this.$hMessage.warning('当前数据为第一条');
            } else {
                this.topoIndex -= 1;
            }
        },
        // topo翻页（下一页）
        handleNext() {
            const tab = _.find(this.tabTableList, ['key', this.tabTableKey]);
            if (this.topoIndex === tab.tabTableData[0].tableData.length - 1) {
                this.$hMessage.warning('当前数据为最后一条');
            } else {
                this.topoIndex += 1;
            }
        },
        // 弹出底部栏
        handleDrawer(data) {
            this.visible = true;
            this.drawerTitle = `委托${data.entrustNo}全链路拓扑`;
            let topoRef = '';
            let loopSpanInfo;
            if (this.penetrationViewMode) {
                topoRef = 'topo';
                loopSpanInfo = [];
                Object.keys(data?.spanLatency).forEach((ele) => {
                    const time = data?.spanLatency?.[ele];
                    loopSpanInfo.push({
                        instanceName: ele,
                        data: isNaN(time)
                            ? time
                            : cutZero((time / 1000).toFixed(3)) + 'μs'
                    });
                });
            } else if (this.applicationViewMode) {
                topoRef = 'appTopo';
                loopSpanInfo = {};
                Object.keys(data?.spanLatency).forEach((ele) => {
                    const time = data?.spanLatency?.[ele];
                    loopSpanInfo[ele] = isNaN(time)
                        ? time
                        : cutZero((time / 1000).toFixed(3)) + 'μs';
                });
            }
            topoRef && setTimeout(() => {
                this.$refs[topoRef].init();
                this.$refs[topoRef].handleUpdateEdgeLabel(loopSpanInfo, {});
            }, 500);
        },
        // 拓扑翻页
        handlePageTopo(type) {
            type === 'prev' ? this.handlePrev() : this.handleNext();
        },
        // 获取指标查询建议数据
        async computerEntrustProposal(span) {
            const tab = _.find(this.tabTableList, ['key', this.tabTableKey]);
            tab.proposalList = [];
            const param = {
                productInstNo: this.productInstNo,
                date: formatDate(new Date()),
                span,
                indicators: ['min', 'avg', 'p50', 'max']
            };
            const { data } = await computerEntrustProposal(param);
            if (Array.isArray(data) && data.length) {
                tab.proposalList = data;
            }
        },
        // 切换tab页
        handleChangeTab(tab) {
            this.tabTableKey = tab;
        },
        // 切换链路类型，变更时延筛选
        handleSelectChange(key, val) {
            if (key === 'bizTraceType') {
                const tab = _.find(this.tabTableList, ['key', this.tabTableKey]);
                const spans = _.find(tab.fixFormItems[0].options, ['value', val])?.spans || [];
                // const allSpans = tab.spans || [];
                // const result = _.chain(allSpans)
                //     .keyBy('spanName') // 将 spanName 作为键
                //     .mapValues('spanAlias') // 将 spanAlias 作为值
                //     .value();
                tab.fixFormItems[3].options = spans.map(v => ({
                    label: v,
                    value: v
                }));
            }
        },
        // 设置查询参数，同步导出页面
        generateQueryData(val, callback) {
            return callback({
                productInstNo: this.productInstNo,
                bizTraceType: val.bizTraceType,
                bizType: this.tabTableKey,
                startDate: `${formatDate(val.date)} ${val.time[0]}`,
                endDate: `${formatDate(val.date)} ${val.time[1]}`,
                queryFieldName: val.queryFieldName,
                queryFieldCondition: val.queryFieldCondition,
                queryFieldValue: this.handleUnitToNs(val.beforeFiledValue, val.unit),
                page: val?.page || 1,
                pageSize: val?.pageSize || 10,
                ...val
            });
        },
        // 查找应用模型配置
        getApplicationViewMode(bizTraceType) {
            let info = '';
            const topoStr = _.find(this.traceModelConfigs, ['bizType', this.tabTableKey])?.latencyTopology || '';
            const list = topoStr && isJSON(topoStr) ? JSON.parse(topoStr) : '';
            if (Array.isArray(list)) {
                for (const ele of list) {
                    if (ele?.meta?.bizWorkloadTraceType === bizTraceType) {
                        info = ele;
                        break;
                    }
                }
            }
            return info;
        }
    },
    computed: {
        ...mapState({
            productList: (state) => {
                return state.product.productListLight || [];;
            }
        })
    },
    watch: {
        topoIndex(newVal) {
            const tab = _.find(this.tabTableList, ['key', this.tabTableKey]);
            this.handleDrawer(tab.tabTableData[0].tableData[newVal]);
        }
    },
    components: { tabTitleTable, aTitle, aButton, appTopo, saveModal, aLoading, apmBlank, allLinkTopo }
};
</script>
<style lang="less">
// render函数生成的节点无法匹配scoped id， 需要用全局去处理
@import url("@/assets/css/poptip-1.less");
</style>
<style lang="less" scoped>
@import url("@/assets/css/input.less");

.main {
    width: 100%;
    height: 100%;
    overflow-x: auto;
    overflow-y: hidden;
}

/deep/ .h-poptip .inner-poptip {
    float: right;

    .h-poptip-body-content-inner {
        color: var(--font-color);
    }
}

.footer {
    width: 100%;
    height: 30px;
    display: flex;
    justify-content: center;

    & > button {
        margin: 0 3px;
    }
}
</style>
