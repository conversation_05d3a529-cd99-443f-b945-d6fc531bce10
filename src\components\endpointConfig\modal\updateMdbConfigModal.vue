<!-- MDB接入 sql配置弹窗 -->
<template>
    <div>
        <h-msg-box-safe
            v-model="modalInfo.status" :mask-closable="false" title="配置MDB-SQL服务"
            width="700" maxHeight="420" @on-open="getCollections">
            <div>
                <!-- <div class="line-count">
                    <h-button class="right-btn" :disabled="tableLoading" @click="getMdbSqlRemoteConfig">自动获取</h-button>
                </div> -->
                <h-edit-gird
                    ref="editGrid"
                    :canMove="false"
                    :columns="columns"
                    :data="data"
                    :height="250"
                    :loading='tableLoading'
                    size="small"
                    :showEditInput="true"
                    :disabled-hover="true"
                    :highlight-row="false"
                ></h-edit-gird>
            </div>
            <template v-slot:footer>
                <h-button @click="modalInfo.status = false">取消</h-button>
                <h-button type="primary" :loading="loading" @click="submitConfig">确定</h-button>
            </template>
        </h-msg-box-safe>
    </div>
</template>

<script>
import _ from 'lodash';
import { updateMdbSqlConfig, getMdbSqlRemoteConfig } from '@/api/memoryApi';
import { stringLengthRule } from '@/utils/validate';
export default {
    name: 'UpdateMdbConfigModal',
    components: {  },
    props: {
        modalInfo: {
            type: Object,
            default: null
        },
        instanceList: {
            type: Array,
            default: () => []
        }
    },
    data() {
        return {
            modalData: this.modalInfo,
            stringRule: [{ test: stringLengthRule(50), trigger: 'change, blur' }],
            tableLoading: false,
            loading: false,
            columns: [
                {
                    title: '节点',
                    key: 'instanceNo',
                    render: (h, params) => {
                        return h('div', params.row.instanceNo || '-');
                    }
                },
                {
                    title: '集群',
                    key: 'clusterName',
                    render: (h, params) => {
                        return h('div', params.row.clusterName || '-');
                    }
                },
                {
                    title: '系统号',
                    type: 'text',
                    key: 'systemNo',
                    rule: [
                        {
                            validator: function(rule, value, callback) {
                                if (value % 1 === 0 && value > 0 && value <= 65536) {
                                    callback();
                                } else {
                                    callback(new Error('请输入1-65536范围的整数'));
                                }
                            },
                            trigger: 'blur'
                        }
                    ]
                },
                {
                    title: '功能号',
                    type: 'text',
                    key: 'sqlFuncNo',
                    rule: [
                        {
                            validator: function(rule, value, callback) {
                                if (value.length < 255) {
                                    callback();
                                } else {
                                    callback(new Error('字符长度数不得超过255!'));
                                }
                            },
                            trigger: 'blur'
                        }
                    ]
                }
                // {
                //     title: '操作',
                //     key: 'action',
                //     width: 120,
                //     render: (h, params) => {
                //         return h('div', [
                //             h(
                //                 'h-button',
                //                 {
                //                     props: {
                //                         type: 'text',
                //                         size: 'small',
                //                         loading: this.loadingIns.includes(`${params.row.instanceNo}`)
                //                     },
                //                     on: {
                //                         click: async () => {
                //                             await this.singleMdbSqlConfig(params.row);
                //                         }
                //                     }
                //                 },
                //                 '自动获取'
                //             )
                //         ]);
                //     }
                // }
            ],
            data: [],
            loadingIns: []
        };
    },
    methods: {
        getCollections() {
            this.data = _.cloneDeep(this.modalData.configData);
        },
        submitConfig() {
            this.$refs.editGrid.validate((valid) => {
                if (valid) {
                    const data = this.$refs.editGrid.getAllData();
                    const newData = data.map(item => {
                        return {
                            productInstNo: item.productInstNo,
                            instanceNo: item.instanceNo,
                            clusterId: item.clusterId,
                            systemNo: item.systemNo ? Number(item.systemNo) : null,
                            sqlFuncNo: item.sqlFuncNo
                        };
                    });
                    this.updateMdbSqlConfig(newData);
                }
            });
        },
        async updateMdbSqlConfig(configs) {
            this.loading = true;
            try {
                const params = {
                    productInstNo: this.modalData.productInstNo,
                    configs
                };
                const res = await updateMdbSqlConfig(params);
                if (res.success) {
                    this.$emit('update');
                    this.$hMessage.success('配置成功');
                    this.modalData.status = false;
                }
            } finally {
                this.loading = false;
            }
        },
        // 重新自动获取
        async getMdbSqlRemoteConfig() {
            this.tableLoading = true;
            try {
                const res = await getMdbSqlRemoteConfig({ productInstNo: this.modalData.productInstNo });
                if (res.code === '200') {
                    this.data = res?.data?.configs || [];
                } else {
                    this.$hMessage.error('自动获取失败');
                }
            } finally {
                this.tableLoading = false;
                // this.$refs.editGrid不支持校验规则重置
                this.$nextTick(() => {
                    this.$refs.editGrid.validate((_valid) => {});
                });
            }
        },
        // 单行重新自动获取
        async singleMdbSqlConfig(row) {
            const params = {
                productInstNo: this.modalData.productInstNo,
                instanceNo: row.instanceNo
            };
            this.loadingIns.push(`${row?.instanceNo}`);
            try {
                // 数据中的 data 不会随着可编辑表格内数据的化而变化。getAllData： 通过 this.$refs.xxx.getAllData() 获取 table 内改变后的数据
                this.data = this.$refs.editGrid.getAllData();
                const res = await getMdbSqlRemoteConfig(params);
                if (res.code === '200') {
                    const singleConfigInfo = res?.data?.configs?.[0] || {};
                    this.data.forEach((item, index) => {
                        if (item?.instanceNo === singleConfigInfo?.instanceNo) {
                            this.$set(this.data, index, { ...singleConfigInfo });
                        }
                    });
                } else if (res?.code?.length === 8){
                    this.$hMessage.error(res?.message || '自动获取失败');
                }
            } finally {
                const index = this.loadingIns.indexOf(`${row?.instanceNo}`);
                if (index > -1) {
                    this.loadingIns.splice(index, 1);
                }
                // this.$refs.editGrid不支持校验规则重置
                this.$nextTick(() => {
                    this.$refs.editGrid.validate((_valid) => {});
                });
            }
        }
    }
};
</script>

<style lang="less" scoped>
/deep/ .h-modal-body {
    padding: 16px;
}

/deep/ .h-btn-text {
    color: var(--link-color);
}

/deep/ .h-btn-text:hover {
    color: var(--link-color);
    text-decoration: underline;
}

.line-count {
    position: relative;
    height: 30px;

    .right-btn {
        position: absolute;
        right: 8px;
        top: -10px;
    }
}
</style>
