// 加载方式选项
export const LOADING_TYPE_OPTIONS = [
    {
        value: 1,
        label: '追加'
    },
    {
        value: 2,
        label: '覆盖'
    },
    {
        value: 3,
        label: '追加+覆盖'
    }
];

// 执行状态选项
export const EXECUTE_STATUS_OPTIONS = [
    {
        value: 'pending',
        label: '待上场'
    },
    {
        value: 'running',
        label: '上场中'
    },
    {
        value: 'succeeded',
        label: '上场成功'
    },
    {
        value: 'failed',
        label: '上场失败'
    }
];

export const STATUS_COLOR_MAP = {
    pending: {
        color: '#999999',
        text: '待上场'
    },
    running: {
        color: '#2D8DE5',
        text: '上场中'
    },
    succeeded: {
        color: '#4ECA89',
        text: '上场成功'
    },
    failed: {
        color: '#F5222D',
        text: '上场失败'
    }
};

// 上场任务执行状态枚举
export const TASK_EXECUTE_STATUS = [
    {
        value: 'warn',
        label: '异常'
    },
    {
        value: 'succeeded',
        label: '成功'
    },
    {
        value: 'failed',
        label: '失败'
    }
];

// 创建二次上场任务步骤
export const CREATE_TASK_TIPS = {
    0: {
        content: '按需选择需要二次上场的表'
    },
    1: {
        content: '确定表上场的加载方式和规则（不写规则默认上场全表）'
    },
    2: {
        content: '请仔细核对二次上场内容、加载方式和上场规则。无误请点击“上场”。'
    }
};
