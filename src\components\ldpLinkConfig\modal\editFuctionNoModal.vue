<template>
    <div>
        <h-msg-box-safe
            v-model="modalData.status"
            :escClose="true"
            :mask-closable="false"
            :title="'修改'+ modalData.title"
            width="500"
        >
            <h-form
                ref="formValidate"
                :model="formValidate"
                :label-width="110"
                @submit.native.prevent
                >
                <h-form-item :label="modalData.title" prop="functionNo" required>
                    <h-input
                        v-model.trim="formValidate.functionNo"
                        type="int"
                        specialFilter
                        :specialLength="6"
                        :specialDecimal="0"
                        placeholder="请输入"
                    ></h-input>
                </h-form-item>
            </h-form>

            <template v-slot:footer>
                <a-button @click="modalData.status = false">取消</a-button>
                <a-button type="primary" :loading="loading" @click="submitConfig">确定</a-button>
            </template>
        </h-msg-box-safe>
    </div>
</template>

<script>
import aButton from '@/components/common/button/aButton';
export default {
    props: {
        modalInfo: {
            type: Object,
            default: null
        }
    },
    data() {
        return {
            modalData: this.modalInfo,
            loading: false,
            formValidate: {
                functionNo: this.modalInfo.functionNo
            }
        };

    },
    mounted(){
    },
    methods: {
        submitConfig() {
            this.$refs['formValidate'].validate(async (valid) => {
                if (valid) {
                    const params = {
                        productId: this.modalData?.productId,
                        functionCode: this.modalData?.functionCode,
                        functionNo: this.formValidate.functionNo,
                        instanceType: this.modalData?.instanceType
                    };
                    this.$emit('save', params);
                    this.modalData.status = false;
                }
            });
        }
    },
    components: { aButton }
};
</script>

