<template>
    <div class="tab-box">
        <a-loading v-if="loading"></a-loading>
        <div v-else>
            <obs-table ref="table1" maxHeight="220" showTitle :title="networkTitle" :tableData="networkTableData" :columns="networkColumns" :hasPage="false" />
            <description-bar :data="flowDes"></description-bar>
            <description-bar :data="accessDes"></description-bar>
            <obs-table ref="table3" maxHeight="220" showTitle :title="routeTitle" :tableData="routeTableData" :columns="routeColumns" :hasPage="false" />
        </div>
    </div>
</template>

<script>
import obsTable from '@/components/common/obsTable/obsTable';
import aLoading from '@/components/common/loading/aLoading';
import descriptionBar from '@/components/common/description/descriptionBar';
import { getManagerProxy } from '@/api/mcApi';
export default {
    props: {
        nodeData: {
            type: Object,
            default: () => { }
        },
        pollTime: {
            type: Number,
            default: 0
        }
    },
    components: { obsTable, aLoading, descriptionBar },
    data() {
        return {
            loading: true,
            // 接入流量控制
            flowDes: {
                title: {
                    label: '接入流量控制',
                    slots: []
                },
                details: [
                    {
                        dataDic: [
                            {
                                label: '流量控制开关状态',
                                key: 'FlowControlStatus'
                            },
                            {
                                label: '流量监控开关状态',
                                key: 'FlowMonitorStatus'
                            }
                        ],
                        data: {
                            FlowControlStatus: '-',
                            FlowMonitorStatus: '-'
                        }
                    }
                ]
            },
            // 接入控制
            accessDes: {
                title: {
                    label: '接入控制',
                    slots: []
                },
                details: [
                    {
                        dataDic: [
                            {
                                label: '接入控制开关状态',
                                key: 'AccessControlStatus'
                            }
                        ],
                        data: {
                            AccessControlStatus: '-'
                        }
                    }
                ]
            },
            // 网络服务配置
            networkTitle: {
                label: '网络服务配置'
            },
            networkColumns: [
                {
                    title: '功能号',
                    key: 'FunctionNo',
                    ellipsis: true
                },
                {
                    title: '地址',
                    key: 'Address',
                    ellipsis: true
                },
                {
                    title: '端口',
                    key: 'Port',
                    ellipsis: true
                }
            ],
            networkTableData: [],
            // 路由配置信息
            routeTitle: {
                label: '路由配置信息'
            },
            routeColumns: [
                {
                    title: '功能号',
                    key: 'Functions',
                    ellipsis: true
                },
                {
                    title: '目标系统号',
                    key: 'SystemNo',
                    ellipsis: true
                },
                {
                    title: '目标端ID',
                    key: 'BackendID',
                    ellipsis: true
                }
            ],
            routeTableData: []
        };
    },
    mounted() {
    },
    methods: {
        async initData() {
            this.loading = true;
            await this.getFileData();
            this.loading = false;
        },
        async getFileData() {
            const { getNetworkServiceConfig, flowControlStat, accessControlStat, routeStat } = await this.getAPi();

            // 接入流量控制
            this.flowDes.details[0].data = { ...flowControlStat };
            // 接入控制
            this.accessDes.details[0].data = { ...accessControlStat };

            // 路由配置信息
            this.routeTableData = [...routeStat];

            // 网络服务配置
            this.networkTableData =  [
                {
                    FunctionNo: 'Wan',
                    Address: getNetworkServiceConfig['Wan']?.Address || '-',
                    Port: getNetworkServiceConfig['Wan']?.Port || '-'
                },
                {
                    FunctionNo: 'Lan',
                    Address: getNetworkServiceConfig['Lan']?.Address || '-',
                    Port: getNetworkServiceConfig['Lan']?.Port || '-'
                }
            ];
        },
        // 接口请求
        async getAPi() {
            const data = {
                getNetworkServiceConfig: {},
                flowControlStat: {},
                accessControlStat: {},
                routeStat: []
            };
            const param = [
                {
                    manageProxyIp: this.nodeData.ip,
                    manageProxyPort: this.nodeData.manageProxyPort,
                    pluginName: 'ldp_front',
                    funcName: 'GetNetworkServiceConfig'
                },
                {
                    manageProxyIp: this.nodeData.ip,
                    manageProxyPort: this.nodeData.manageProxyPort,
                    pluginName: 'ldp_front',
                    funcName: 'FlowControlStat'
                },
                {
                    manageProxyIp: this.nodeData.ip,
                    manageProxyPort: this.nodeData.manageProxyPort,
                    pluginName: 'ldp_front',
                    funcName: 'AccessControlStat'
                },
                {
                    manageProxyIp: this.nodeData.ip,
                    manageProxyPort: this.nodeData.manageProxyPort,
                    pluginName: 'ldp_front',
                    funcName: 'RouteStat'
                }
            ];
            try {
                const res = await getManagerProxy(JSON.stringify(param));
                if (res.code === '200') {
                    !res.data?.[0]?.ErrorNo && Object.keys(res.data?.[0]).length  && (data.getNetworkServiceConfig = res.data[0]);
                    res.data?.[1]?.FlowControlStat && (data.flowControlStat = res.data[1].FlowControlStat);
                    res.data?.[2]?.AccessControlStat && (data.accessControlStat = res.data[2].AccessControlStat);
                    res.data?.[3]?.Routes?.length && (data.routeStat = res.data[3].Routes);
                }
                return data;
            } catch (err) {
                this.$emit('clear');
            }
        }
    }
};
</script>
