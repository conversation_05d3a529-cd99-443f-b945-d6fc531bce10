{"summary": {"totalFiles": 539, "processedFiles": 453, "skippedFiles": 0, "totalTexts": 5789, "duration": "1秒", "extractTime": "2025-07-31T06:01:07.993Z"}, "categories": {"common": {"totalTexts": 826, "modules": {}}, "api": {"totalTexts": 3, "modules": {}}, "components": {"totalTexts": 4431, "modules": {"accordObservation": 24, "analyse": 102, "analyseConfig": 92, "appBindingCore": 15, "brokerDataLimit": 68, "businessMonitor": 31, "common": 473, "coreReplayObservation": 180, "dataSecondAppearance": 71, "eccomProduct": 4, "endpointConfig": 120, "latencyTrendAnalysis": 29, "ldpDataObservation": 351, "ldpLinkConfig": 280, "ldpLogCenter": 137, "ldpMonitor": 148, "ldpProduct": 157, "ldpTable": 148, "locateConfig": 70, "managementQuery": 124, "marketAllLink": 58, "mcDataObservation": 215, "mcDeploy": 28, "mdbDataObservation": 79, "mdbPrivilegeManage": 172, "networkSendAndRecevied": 153, "productDataStorage": 82, "productServiceConfig": 177, "productTimeAnalysis": 20, "rcmBacklogMonitor": 10, "rcmDeploy": 300, "rcmObservation": 68, "secondAppearance": 52, "sms": 23, "sqlTable": 169, "transaction": 38, "tripartiteServiceConfig": 24, "ustTableVerification": 139}}, "mixins": {"totalTexts": 1, "modules": {}}, "store": {"totalTexts": 1, "modules": {}}, "utils": {"totalTexts": 73, "modules": {}}, "pages": {"totalTexts": 454, "modules": {"accordMonitor": 9, "accordObservation": 3, "analyseConfig": 11, "analyseData": 33, "apmMonitorConfig": 3, "appRunningState": 17, "brokerDataLimit": 4, "displaySettingDrawer": 2, "index": 26, "coreReplayObservation": 3, "constant": 9, "createRule": 22, "dataSecondAppearance": 5, "addModal": 1, "helpModal": 0, "routeConfig": 4, "routeInfoForm": 4, "util": 9, "latencyTrendAnalysis": 30, "ldpDataObservation": 2, "ldpLinkConfig": 10, "ldpLogCenter": 5, "clusterMonitor": 0, "ldpAppMonitor": 0, "ldpTable": 8, "locateConfig": 1, "managementQuery": 26, "marketAllLink": 7, "marketMonitor": 6, "marketNodeDelayList": 1, "marketPenetrateList": 0, "marketTimeDelay": 17, "mcDataObservation": 1, "mcDeploy": 0, "tableSelector": 5, "detailDrawer": 1, "exportHistory": 2, "exportTable": 8, "mdbDataObservation": 1, "mdbPrivilegeManage": 24, "businessMonitor": 4, "networkSendAndRecevied": 0, "noticeManagerList": 3, "productDataStorage": 6, "productServiceList": 2, "productTimeDetail": 11, "productTimeSummary": 3, "rcmBacklogMonitor": 3, "rcmDeploy": 2, "rcmObservation": 2, "publishStatusDetail": 12, "smsList": 3, "sqlCores": 15, "sqlTable": 18, "threadInfoOverview": 1, "topoMonitor": 9, "transaction": 33, "tripartiteServiceList": 1, "ustTableVerification": 6}}}, "topFiles": [{"file": "locales\\optimized\\en-US-optimized.js", "textsCount": 2056}, {"file": "locales\\optimized\\zh-CN-optimized.js", "textsCount": 2056}, {"file": "locales\\final\\en-US.js", "textsCount": 2039}, {"file": "locales\\final\\zh-CN.js", "textsCount": 2039}, {"file": "locales\\organized\\en-US.js", "textsCount": 2039}, {"file": "locales\\organized\\zh-CN.js", "textsCount": 2039}, {"file": "locales\\extracted\\en-US-extracted.js", "textsCount": 1489}, {"file": "locales\\extracted\\zh-CN-extracted.js", "textsCount": 1489}, {"file": "components\\common\\topo\\ldpNodeTopo.js", "textsCount": 148}, {"file": "locales\\zh-CN.js", "textsCount": 118}, {"file": "config\\exchangeConfig.js", "textsCount": 116}, {"file": "components\\coreReplayObservation\\coreReplayDetail.vue", "textsCount": 75}, {"file": "views\\index\\createRule\\createRule.vue", "textsCount": 71}, {"file": "views\\index\\latencyTrendAnalysis.vue", "textsCount": 70}, {"file": "views\\index\\analyseData.vue", "textsCount": 68}, {"file": "components\\ldpLogCenter\\ldpTodbErrorRetry.vue", "textsCount": 65}, {"file": "views\\index\\sqlTable.vue", "textsCount": 63}, {"file": "components\\mcDataObservation\\mcTopic.vue", "textsCount": 62}, {"file": "views\\index\\mdbPrivilegeManage.vue", "textsCount": 58}, {"file": "components\\ustTableVerification\\createVerificationTask.vue", "textsCount": 57}], "skippedFiles": []}