import '../public.less';
import _ from 'lodash';
import aTitle from '@/components/common/title/aTitle';
import aButton from '@/components/common/button/aButton';
import normalTable from '@/components/common/bestTable/normalTable/normalTable';
import bulkEditModal from '@/components/secondAppearance/sqlConditionList/modal/bulkEditModal.vue';
import publishModal from '@/components/secondAppearance/sqlConditionList/modal/publishModal.vue';
import editModal from '@/components/secondAppearance/sqlConditionList/modal/editModal.vue';
import importStatusTableIcon from '@/components/secondAppearance/importStatusTableIcon.vue';
import { EXECUTE_STATUS_OPTIONS } from '@/components/secondAppearance/constant';
import drawerContent from '@/components/secondAppearance/logDrawerContent.vue';
import { getLoadDataRules, getLoadDataStatus, batchLoadDataRule } from '@/api/brokerApi';

export default {
    name: 'SqlConditionList',
    props: {
        productId: {
            type: String,
            default: ''
        },
        clusterList: {
            type: Array,
            default: () => []
        }
    },
    components: {
        normalTable,
        aTitle,
        aButton,
        bulkEditModal,
        publishModal,
        editModal,
        importStatusTableIcon,
        drawerContent
    },
    computed: {
        formItems(){
            return [
                {
                    type: 'input',
                    label: '分片号',
                    key: 'shardingNo',
                    value: ''
                },
                {
                    type: 'select',
                    label: '集群名称',
                    key: 'clusterNames',
                    options: this.clusterList,
                    value: [],
                    multiple: true
                },
                {
                    type: 'select',
                    label: '执行状态',
                    key: 'execStatuses',
                    options: EXECUTE_STATUS_OPTIONS,
                    value: [],
                    multiple: true
                },
                {
                    type: 'input',
                    key: 'tableName',
                    label: '表名',
                    value: ''
                }
            ];
        }
    },
    data() {
        return {
            // 弹窗元数据
            modalInfo: {
                title: '', // 弹窗标题
                status: false, // 弹窗状态，是否关闭
                type: '', // 当前页面打开的弹窗类型:bulkEdit（批量编辑）、publish（上场）、edit（编辑）、viewLog（查看日志）
                data: '' // 表单数据
            },

            msgBoxData: {},
            loading: false,
            buttonLoading: false,
            checkedItems: [], // 选中的表格行
            columns: [
                {
                    type: 'selection',
                    width: 60,
                    align: 'center',
                    fixed: 'left'
                },
                {
                    title: '表名',
                    key: 'tableName',
                    width: 200,
                    ellipsis: true,
                    fixed: 'left'
                },
                {
                    width: 240,
                    title: '集群名称',
                    key: 'clusterName'
                },
                {
                    key: 'shardingNo',
                    width: 240,
                    title: '分片号'
                },
                {
                    width: 120,
                    title: '加载方式',
                    render: (_, params) => {
                        let loadingMethodDom = <div>暂无数据</div>;
                        switch (params.row.loadMode) {
                            case '1':
                                loadingMethodDom = <div>追加</div>;
                                break;
                            case '2':
                                loadingMethodDom = <div>覆盖</div>;
                                break;
                            case '3':
                                loadingMethodDom = <div>追加+覆盖</div>;
                                break;
                        }
                        return loadingMethodDom;
                    }
                },
                {
                    width: 120,
                    title: '执行状态',
                    render: (_, params) => {
                        const text = EXECUTE_STATUS_OPTIONS.find(status => status.value === params.row.execStatus)?.label || '';
                        let sqlTableIconType;
                        switch (params.row.execStatus){
                            case 'running':
                                sqlTableIconType = 'loading';
                                break;
                            case 'succeeded':
                                sqlTableIconType = 'success';
                                break;
                            case 'failed':
                                sqlTableIconType = 'error';
                                break;
                            case 'pending':
                                sqlTableIconType = 'offline';
                                break;
                        }
                        return <div>
                            <importStatusTableIcon type={sqlTableIconType}/>
                            {text}
                        </div>;
                    }
                },
                {
                    title: '条件',
                    key: 'loadSql',
                    ellipsis: true
                },
                {
                    title: '执行结果',
                    key: 'execDetail',
                    ellipsis: true
                },
                {
                    title: '操作',
                    key: 'action',
                    fixed: 'right',
                    width: 160,
                    render: (h, params) => {
                        return h('div', [
                            h('Button',
                                {
                                    props: {
                                        size: 'small',
                                        type: 'text',
                                        disabled: ['running'].includes(params.row.execStatus)
                                    },
                                    on: {
                                        click: () => {
                                            this.modalInfo.type = 'edit';
                                            this.modalInfo.status = true;
                                            this.modalInfo.title = '编辑';
                                            this.msgBoxData = _.cloneDeep(params.row);
                                        }
                                    }
                                },
                                '编辑'
                            ),
                            h('Button',
                                {
                                    props: {
                                        size: 'small',
                                        type: 'text',
                                        disabled: ['pending', 'running'].includes(params.row.execStatus)
                                    },
                                    on: {
                                        click: () => {
                                            this.logTableDrawerVisiable = true;
                                            this.logTableDrawerData = params.row;
                                            // this.modalInfo.type = 'viewLog';
                                            // this.modalInfo.status = true;
                                            // this.modalInfo.title =  `${params.row.tableName}表二次上场执行日志`;
                                            // this.msgBoxData = params.row;
                                        }
                                    }
                                },
                                '查看日志'
                            )
                        ]);
                    }
                }
            ],
            tableData: [],
            logTableDrawerData: {},
            logTableDrawerVisiable: false
        };
    },
    methods: {
        async initData() {
            this.$refs['sql-condition-table'].$_handleQuery();
            this.checkedItems = [];
        },
        // 调用接口，表数据查询
        async handleQuery(val){
            const param = {
                productId: this.productId,
                whereCondition: 'all',
                shardingNo: val?.shardingNo || '',
                clusterNames: val?.clusterNames.join(',') || [],
                tableName: val?.tableName || '',
                execStatuses: val?.execStatuses.join(',') || []
            };
            this.checkedItems = [];
            this.tableData = [];
            try {
                this.loading = true;
                const loadDataRulesData = (await getLoadDataRules(param))?.data || [];
                const loadDataRuleStatus = (await getLoadDataStatus(param))?.data || [];
                // 数据聚合  通过 tableName 进行关联
                const result = loadDataRulesData.map((data => {
                    let mergedItem = {};
                    const status = loadDataRuleStatus?.execs?.find(exec => {
                        return  exec.tableName === data.tableName && exec.clusterName === data.clusterName;
                    });
                    if (status){
                        mergedItem = { ...data, ...status };
                    } else {
                        mergedItem = { ...data, execStatus: 'pending', execIds: [], execDetail: '-' };
                    }
                    // 如果处于执行状态处于上场中，则禁用多选
                    if (mergedItem.execStatus === 'running'){
                        mergedItem._disabled = true;
                    }
                    return mergedItem;
                })).filter(item => {
                    // 根据执行状态对表格数据进行过滤
                    if (val?.execStatuses?.length === 0){
                        return true;
                    }
                    return val?.execStatuses.indexOf(item.execStatus) !== -1;
                });
                this.tableData = result;
            } catch (err) {
                console.error(err);
            }
            this.loading = false;
        },
        async handleBulkEdit(){
            if (this.checkedItems.length <= 0){
                this.$hMessage.warning('请选择需要编辑的表!');
                return;
            }
            this.modalInfo.type = 'bulkEdit';
            this.modalInfo.status = true;
            this.modalInfo.title = '批量编辑';
            this.msgBoxData = { loadMode: '1' }; // 弹窗选项初始化
        },
        async handlePublish(){
            if (this.checkedItems.length <= 0){
                this.$hMessage.warning('请选择需要上场的表!');
                return;
            }
            this.modalInfo.type = 'publish';
            this.modalInfo.status = true;
            this.modalInfo.title = '确定要对以下数据表进行二次上场吗？';
        },
        handleTableSelect(checkedItems){
            this.checkedItems = checkedItems;
        },
        // 调用接口，清空条件
        async handleClearLoadSql(){
            if (this.checkedItems.length <= 0){
                this.$hMessage.warning('请选择需要清空条件的表!');
                return;
            }
            const param = {
                productId: this.productId,
                whereCondition: 'all',
                tableNames: this.checkedItems.map(item => {
                    return {
                        tableName: item.tableName,
                        clusterName: item.clusterName
                    };
                }),
                loadSql: ''
            };
            try {
                this.buttonLoading = true;
                const res = await batchLoadDataRule(param);
                if (res.success){
                    this.$hMessage.success('操作成功');
                    this.handleQuery();
                }
            } catch (error) {
                console.error(error);
            }
            this.buttonLoading = false;
        }
    },
    render() {

        const tableDom = <normal-table
            isSimpleTable={true}
            ref="sql-condition-table"
            formTitle="筛选条件"
            tableTitle="持久化数据表"
            formItems={this.formItems}
            columns={this.columns}
            loading={this.loading}
            tableData={this.tableData}
            hasSetTableColumns={false}
            showTitle={true}
            hasPage={false}
            maxHeight="200"
            v-on:query={this.handleQuery}
            v-on:selection={this.handleTableSelect}>
            <div class='table-slot-box' slot='btns'>
                <a-button type="primary" onClick={this.handleBulkEdit}>批量编辑</a-button>
                <a-button type="dark" onClick={this.handlePublish}>上场</a-button>
                <a-button type="dark" onClick={this.handleClearLoadSql} title="点击清空选中的表条件" loading={this.buttonLoading}>清空条件</a-button>
            </div>
        </normal-table>;

        let msgBoxDom;
        switch (this.modalInfo.type) {
            case 'bulkEdit':
                msgBoxDom = <bulkEditModal
                    productId={this.productId}
                    modalInfo={this.modalInfo}
                    checkedItems={this.checkedItems}
                    msgBoxData={this.msgBoxData}
                    v-on:query={this.initData}
                />;
                break;
            case 'publish':
                msgBoxDom = <publishModal
                    productId={this.productId}
                    modalInfo={this.modalInfo}
                    checkedItems={this.checkedItems}
                    v-on:query={this.initData}
                />;
                break;
            case 'edit':
                msgBoxDom = <editModal
                    productId={this.productId}
                    modalInfo={this.modalInfo}
                    msgBoxData={this.msgBoxData}
                    v-on:query={() => {
                        this.initData();
                    }}
                />;
                break;

        }

        const logTableDrawer = <h-drawer
            v-model={this.logTableDrawerVisiable}
            width="742"
            title={'上场日志-' + this.logTableDrawerData.tableName}
        >
            <drawerContent
                drawerData={this.logTableDrawerData}
                productId={this.productId}
            />
        </h-drawer>;

        const styleDom = <style jsx>
            {
                `    
                .table-slot-box > .h-btn {
                    margin-right: 10px;
                }
                .best-table .best-form .h-checkbox-inner {
                    border: 1px solid #d7dde4;
                    background: #fff;
                }
            `
            }
        </style>;

        return (
            <div class="topic-box">
                {tableDom}
                {msgBoxDom}
                {styleDom}
                {logTableDrawer}
            </div>
        );
    },
    mounted() {
        this.$nextTick(() => {
            this.$hCore.on('loadDataFinish', (whereCondition) => {
                // 上场任务已完成，需要刷新表格数据
                if (whereCondition === 'all') {
                    this.$refs['sql-condition-table'].$_handleQuery();
                }
            });
        });
    },
    beforeDestroy () {
        this.$hCore.off('loadDataFinish');
    }
};
