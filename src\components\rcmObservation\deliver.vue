<template>
    <div>
      <!-- 接收端上下文投递信息 -->
      <obs-table :title="funcTable.title" showTitle :tableData="funcTable.tableData" :columns="funcTable.columns" notSetWidth autoHeadWidth :maxHeight="220" />
      <!-- 应用消息投递处理 -->
      <info-grid :gridData="NakingData"></info-grid>
    </div>
  </template>
<script>
import obsTable from '@/components/common/obsTable/obsTable';
import infoGrid from '@/components/common/infoBar/infoGrid';
import { transferVal } from '@/utils/utils';
export default {
    props: {
        rcmInfo: {
            type: Object,
            default: () => {}
        }
    },
    data() {
        return {
            // 接收端上下文投递信息
            funcTable: {
                title: {
                    label: '接收端上下文投递信息',
                    slots: [
                        {
                            type: 'text',
                            label: 'Count',
                            value: '-'
                        }
                    ]
                },
                tableData: [],
                columns: [
                    {
                        title: '发送端的ContextId',
                        key: 'ConnectId',
                        ellipsis: true
                    },
                    {
                        title: 'TransportId',
                        key: 'TransportId',
                        ellipsis: true
                    },
                    {
                        title: 'TopicName',
                        key: 'TopicName',
                        ellipsis: true
                    },
                    {
                        title: 'PartitionNo',
                        key: 'PartitionNo',
                        ellipsis: true
                    },
                    {
                        title: '接收端的ContextId',
                        key: 'revContextId',
                        ellipsis: true
                    },
                    {
                        title: 'NextMsgNo',
                        key: 'NextMsgNo',
                        ellipsis: true
                    },
                    {
                        title: 'NextMsgNoDeliver',
                        key: 'NextMsgNoDeliver',
                        ellipsis: true
                    }
                ]
            },
            // 应用消息投递处理
            NakingData: {
                title: {
                    label: '应用消息投递处理'
                },
                layout: [
                    { x: 0, y: 0, w: 6, h: 6.5, i: '消息排队' },
                    { x: 6, y: 0, w: 6, h: 6.5, i: '消息投递' },
                    { x: 0, y: 1, w: 6, h: 6.5, i: '缓存积压' },
                    { x: 6, y: 1, w: 6, h: 6.5, i: '消息持久化' }
                ],
                details: [
                    {
                        type: 'monitor',
                        title: {
                            label: '消息排队',
                            noTagColor: true
                        },
                        gridSpan: 3,
                        info: [
                            {
                                type: 'text',
                                label: 'NextMsgSqn',
                                key: 'NextMsgSqn',
                                value: '-',
                                labelAlias: '下一个排队消息序号'
                            },
                            {
                                type: 'text',
                                label: 'CommittedMsgSqn',
                                key: 'CommittedMsgSqn',
                                value: '-',
                                labelAlias: '提交的消息序号'
                            }
                        ]
                    },
                    {
                        type: 'monitor',
                        title: {
                            label: '消息投递',
                            noTagColor: true
                        },
                        gridSpan: 3,
                        info: [
                            {
                                type: 'text',
                                label: 'NextMsgSqnDeliver',
                                key: 'NextMsgSqnDeliver',
                                value: '-',
                                labelAlias: '下一个处理消息序号'
                            },
                            {
                                type: 'text',
                                label: 'MsgsDelivered',
                                key: 'MsgsDelivered',
                                value: '-',
                                labelAlias: '已经处理的消息数量'
                            },
                            {
                                type: 'text',
                                label: 'OnMessageCountFailed',
                                key: 'OnMessageCountFailed',
                                value: '-',
                                labelAlias: '消息回调失败次数统计'
                            }
                        ]
                    },
                    {
                        type: 'monitor',
                        title: {
                            label: '缓存积压',
                            noTagColor: true
                        },
                        gridSpan: 3,
                        info: [
                            {
                                type: 'text',
                                label: 'ToCommitBackLog',
                                key: 'ToCommitBackLog',
                                value: '-',
                                labelAlias: '集群确认消息积压'
                            },
                            {
                                type: 'text',
                                label: 'ToDelieverBackLog',
                                key: 'ToDelieverBackLog',
                                value: '-',
                                labelAlias: '应用投递消息积压'
                            },
                            {
                                type: 'text',
                                label: 'ToRecordBackLog',
                                key: 'ToRecordBackLog',
                                value: '-',
                                labelAlias: '待持久化消息积压'
                            }
                        ]
                    },
                    {
                        type: 'monitor',
                        title: {
                            label: '消息持久化',
                            noTagColor: true
                        },
                        gridSpan: 3,
                        info: [
                            {
                                type: 'text',
                                label: 'NextMsgSqnRecord',
                                key: 'NextMsgSqnRecord',
                                value: '-',
                                labelAlias: '下一个持久化消息序号'
                            },
                            {
                                type: 'text',
                                label: 'MsgsRecorded',
                                key: 'MsgsRecorded',
                                value: '-',
                                labelAlias: '已经持久化的消息数量'
                            }
                        ]
                    }
                ]
            }
        };
    },
    components: { infoGrid, obsTable },
    methods: {
        initData() {
            this.getFileData();
        },
        // 应用信息投递处理
        setNakingDetail(data){
            this.NakingData.details.forEach((item) => {
                item.info.forEach(o => {
                    o.type === 'text' && (o.value = data?.[o.key]);
                });
            });
            this.NakingData.details[2].info[0].value = transferVal(data?.NextMsgSqn - data?.CommittedMsgSqn) || '';
            this.NakingData.details[2].info[1].value = transferVal(data?.NextMsgSqn - data?.NextMsgSqnDeliver) || '';
            this.NakingData.details[2].info[2].value = transferVal(data?.NextMsgSqnDeliver - data?.NextMsgSqnRecord) || '';
        },

        // 构造页面数据
        getFileData() {
            // 接收端上下文投递信息
            this.funcTable.title.slots[0].value = (this.rcmInfo?.Receiver?.Rms || [])?.length;
            this.funcTable.tableData = (this.rcmInfo?.Receiver?.Rms || []).map(o => {
                return {
                    ...o,
                    revContextId: this.rcmInfo?.ContextId
                };
            });
            // 应用信息投递处理
            this.setNakingDetail(this.rcmInfo?.Deliver || {});
        }
    }
};
</script>
