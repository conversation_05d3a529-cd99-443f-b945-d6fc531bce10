#!/usr/bin/env node

const fs = require('fs');
const path = require('path');

/**
 * 简化版中文文案提取脚本
 * 专注于views目录下的Vue文件
 */

class SimpleI18nExtractor {
    constructor() {
        this.srcDir = path.join(__dirname, '../src');
        this.outputDir = path.join(__dirname, '../src/locales/extracted');
        this.extractedTexts = new Map();
        this.allTexts = new Set();
        
        // 中文字符正则表达式
        this.chineseRegex = /[\u4e00-\u9fff]+/g;
    }

    /**
     * 提取文件中的中文文案
     */
    extractFromFile(filePath) {
        try {
            const content = fs.readFileSync(filePath, 'utf-8');
            const texts = new Set();
            
            // 简化的正则表达式，提取引号内的中文文本
            const patterns = [
                // 双引号内的中文
                /"([^"]*[\u4e00-\u9fff][^"]*)"/g,
                // 单引号内的中文
                /'([^']*[\u4e00-\u9fff][^']*)'/g,
                // 模板字符串内的中文
                /`([^`]*[\u4e00-\u9fff][^`]*)`/g,
                // HTML标签内的中文文本
                />([^<>]*[\u4e00-\u9fff][^<>]*)</g
            ];
            
            patterns.forEach(pattern => {
                let match;
                while ((match = pattern.exec(content)) !== null) {
                    const text = match[1];
                    if (text && this.isValidChineseText(text)) {
                        texts.add(text.trim());
                    }
                }
            });
            
            return Array.from(texts);
        } catch (error) {
            console.warn(`提取文件 ${filePath} 失败:`, error.message);
            return [];
        }
    }

    /**
     * 验证是否为有效的中文文案
     */
    isValidChineseText(text) {
        text = text.trim();
        
        // 基本过滤条件
        if (!text || text.length < 1 || text.length > 50) return false;
        if (!/[\u4e00-\u9fff]/.test(text)) return false;
        if (/^[\d\s\-_\.\/\\]+$/.test(text)) return false;
        if (/^[a-zA-Z\s\-_\.]+$/.test(text)) return false;
        
        // 排除一些明显的非文案内容
        const excludePatterns = [
            /console\./,
            /import\s/,
            /export\s/,
            /function\s/,
            /^\$\w+/,
            /^@\w+/,
            /^#\w+/,
            /^\.\w+/,
            /^\/\//,
            /^\/\*/,
            /^\*\//,
            /^http/,
            /^www\./,
            /\.com$/,
            /\.js$/,
            /\.vue$/,
            /\.css$/
        ];
        
        return !excludePatterns.some(pattern => pattern.test(text));
    }

    /**
     * 根据文件路径推断路由信息
     */
    inferRouteFromPath(filePath) {
        const relativePath = path.relative(this.srcDir, filePath);
        
        // 处理views目录下的文件
        if (relativePath.startsWith('views/index/')) {
            const fileName = path.basename(filePath, path.extname(filePath));
            return fileName;
        }
        
        // 处理components目录下的文件
        if (relativePath.startsWith('components/')) {
            const parts = relativePath.split('/');
            if (parts.length >= 2) {
                return parts[1];
            }
        }
        
        // 其他文件
        const parts = relativePath.split('/');
        return parts[0] || 'common';
    }

    /**
     * 生成国际化key
     */
    generateI18nKey(text) {
        // 常见中文到英文的映射
        const mappings = {
            '查询': 'query',
            '搜索': 'search',
            '添加': 'add',
            '新增': 'add',
            '编辑': 'edit',
            '修改': 'edit',
            '删除': 'delete',
            '保存': 'save',
            '取消': 'cancel',
            '确认': 'confirm',
            '提交': 'submit',
            '重置': 'reset',
            '刷新': 'refresh',
            '导出': 'export',
            '导入': 'import',
            '配置': 'config',
            '设置': 'setting',
            '管理': 'manage',
            '监控': 'monitor',
            '观测': 'observation',
            '数据': 'data',
            '表格': 'table',
            '列表': 'list',
            '详情': 'detail',
            '信息': 'info',
            '状态': 'status',
            '名称': 'name',
            '时间': 'time',
            '开始': 'start',
            '结束': 'end',
            '成功': 'success',
            '失败': 'failed',
            '错误': 'error',
            '警告': 'warning',
            '提示': 'tip',
            '请选择': 'pleaseSelect',
            '请输入': 'pleaseInput',
            '暂无数据': 'noData',
            '加载中': 'loading',
            '操作': 'operation',
            '功能': 'function'
        };
        
        // 尝试直接映射
        if (mappings[text]) {
            return mappings[text];
        }
        
        // 部分匹配替换
        let key = text;
        for (const [chinese, english] of Object.entries(mappings)) {
            if (key.includes(chinese)) {
                key = key.replace(chinese, english);
            }
        }
        
        // 移除剩余的中文字符，用text替代
        key = key.replace(/[\u4e00-\u9fff]/g, 'text');
        
        // 转换为小驼峰
        key = key
            .replace(/[^\w\s]/g, '')
            .split(/\s+/)
            .filter(word => word.length > 0)
            .map((word, index) => {
                if (index === 0) {
                    return word.toLowerCase();
                }
                return word.charAt(0).toUpperCase() + word.slice(1).toLowerCase();
            })
            .join('');
        
        return key || 'unknownText';
    }

    /**
     * 处理指定目录
     */
    processDirectory(dir, maxDepth = 3, currentDepth = 0) {
        if (currentDepth >= maxDepth) return;
        
        try {
            console.log(`📁 扫描目录: ${path.relative(this.srcDir, dir) || '.'}`);
            const files = fs.readdirSync(dir);
            
            files.forEach(file => {
                const filePath = path.join(dir, file);
                const stat = fs.statSync(filePath);
                
                if (stat.isDirectory()) {
                    if (!['node_modules', 'dist', '.git', '.vscode'].includes(file)) {
                        this.processDirectory(filePath, maxDepth, currentDepth + 1);
                    }
                } else if (stat.isFile() && /\.vue$/.test(file)) {
                    console.log(`📄 处理文件: ${path.relative(this.srcDir, filePath)}`);
                    const texts = this.extractFromFile(filePath);
                    
                    if (texts.length > 0) {
                        console.log(`   ✅ 提取到 ${texts.length} 条文案`);
                        const routeKey = this.inferRouteFromPath(filePath);
                        
                        if (!this.extractedTexts.has(routeKey)) {
                            this.extractedTexts.set(routeKey, []);
                        }
                        
                        texts.forEach(text => {
                            if (!this.allTexts.has(text)) {
                                this.allTexts.add(text);
                                this.extractedTexts.get(routeKey).push({
                                    text,
                                    file: path.relative(this.srcDir, filePath),
                                    key: this.generateI18nKey(text)
                                });
                            }
                        });
                    }
                }
            });
        } catch (error) {
            console.error(`❌ 处理目录 ${dir} 失败:`, error.message);
        }
    }

    /**
     * 生成国际化文件
     */
    generateI18nFiles() {
        // 确保输出目录存在
        if (!fs.existsSync(this.outputDir)) {
            fs.mkdirSync(this.outputDir, { recursive: true });
        }
        
        const zhCN = {};
        const enUS = {};
        const mapping = {};
        
        // 按路由组织数据
        this.extractedTexts.forEach((texts, routeKey) => {
            zhCN[routeKey] = {};
            enUS[routeKey] = {};
            mapping[routeKey] = [];
            
            texts.forEach(({ text, file, key }) => {
                zhCN[routeKey][key] = text;
                enUS[routeKey][key] = text; // 英文版本暂时使用中文
                
                mapping[routeKey].push({
                    key,
                    chinese: text,
                    english: text,
                    file
                });
            });
        });
        
        // 写入文件
        this.writeFile('zh-CN-extracted.js', `export default ${JSON.stringify(zhCN, null, 2)};`);
        this.writeFile('en-US-extracted.js', `export default ${JSON.stringify(enUS, null, 2)};`);
        this.writeFile('mapping.json', JSON.stringify(mapping, null, 2));
        
        // 生成统计报告
        this.generateReport();
    }

    /**
     * 写入文件
     */
    writeFile(filename, content) {
        const filePath = path.join(this.outputDir, filename);
        fs.writeFileSync(filePath, content, 'utf-8');
        console.log(`✅ 生成文件: ${filePath}`);
    }

    /**
     * 生成提取报告
     */
    generateReport() {
        const report = {
            summary: {
                totalTexts: this.allTexts.size,
                totalRoutes: this.extractedTexts.size,
                extractTime: new Date().toISOString()
            },
            routes: {}
        };
        
        this.extractedTexts.forEach((texts, routeKey) => {
            report.routes[routeKey] = {
                count: texts.length,
                texts: texts.map(({ text, file }) => ({ text, file }))
            };
        });
        
        this.writeFile('extraction-report.json', JSON.stringify(report, null, 2));
        
        console.log('\n📊 提取统计:');
        console.log(`总计提取中文文案: ${this.allTexts.size} 条`);
        console.log(`涉及路由/模块: ${this.extractedTexts.size} 个`);
        
        // 显示前10个路由的统计
        const sortedRoutes = Array.from(this.extractedTexts.entries())
            .sort((a, b) => b[1].length - a[1].length)
            .slice(0, 10);
        
        console.log('\n🔝 文案最多的模块:');
        sortedRoutes.forEach(([route, texts]) => {
            console.log(`  ${route}: ${texts.length} 条`);
        });
    }

    /**
     * 执行提取
     */
    run() {
        console.log('🚀 开始提取中文文案...');
        console.log(`源目录: ${this.srcDir}`);
        console.log(`输出目录: ${this.outputDir}`);
        
        // 主要处理views目录
        const viewsDir = path.join(this.srcDir, 'views');
        if (fs.existsSync(viewsDir)) {
            this.processDirectory(viewsDir);
        }
        
        // 处理主要的components目录
        const componentsDir = path.join(this.srcDir, 'components');
        if (fs.existsSync(componentsDir)) {
            this.processDirectory(componentsDir, 2); // 限制深度避免过深
        }
        
        this.generateI18nFiles();
        
        console.log('✨ 提取完成!');
    }
}

// 执行脚本
if (require.main === module) {
    const extractor = new SimpleI18nExtractor();
    extractor.run();
}

module.exports = SimpleI18nExtractor;
