import './form.less';
import _ from 'lodash';
export default {
    name: 'apm-form',
    props: {
        formItems: {
            type: Array,
            default: () => []
        },
        formData: {
            type: Object,
            default: () => {}
        },
        rules: {
            type: Object,
            default: () => {}
        }
    },
    data() {
        return {
            form: {}
        };
    },
    computed: {
    },
    mounted() {
    },
    methods: {
        // 重置
        reset(isManualReset = false) {
            this.$refs['formValidate'].resetFields();
            // 手动设置初始值
            this.$nextTick(() => {
                isManualReset && this.$emit('reset');
            });
        },
        // 点击查询
        query() {
            let isQuery = false, queryData = {};
            this.$refs['formValidate'].validate(valid => {
                if (valid) {
                    queryData = { ...this.formData };
                    isQuery = true;
                }
            });
            return isQuery ? queryData : false;
        },
        // 查询时延筛选参照数据
        queryProposal(span) {
            span && this.$emit('queryProposal', span);
        },
        // 监听下拉框变化
        handleOnSelectChange(key, val, hasOnChange) {
            hasOnChange && this.$emit('on-select-change', key, val);
        }
    },
    render() {
        // 生成输入框
        const generateInput = (item) => {
            return (
                <h-input
                    v-model={this.formData[item.key]}
                    placeholder={item.placeholder || '请输入'}
                    icon={item.icon}
                ></h-input>
            );
        };
        // 生成普通下拉框
        const generateSelect = (item) => {
            return (
                <h-select v-model={this.formData[item.key]}
                    placeholder={item.placeholder || '请选择'}
                    positionFixed={true}
                    multiple={item.multiple || false}
                    v-on:on-change={(val) => { this.handleOnSelectChange(item.key, val, item?.hasOnChange || false); }}
                >
                    {
                        item.options.map(opt => (
                            <h-option key={opt.value} value={opt.value}>{opt.label}</h-option>
                        ))
                    }
                </h-select>
            );
        };
        // 生成可搜索的下拉框
        const generateSelectSearch = (item) => {
            return <h-select
                v-model={this.formData[item.key]}
                filterable
                placeholder={item.placeholder || '请选择'}
                positionFixed={true}
                loading={item.loading}
                remote
                remote-method={item.remoteMethod}
                remoteIcon="search"
                loading-text="加载中...">
                {
                    item.options.map(opt => { return <h-option key={opt.value} value={opt.value}>{opt.label}</h-option>; })
                }
            </h-select>;
        };
        // 生成日期筛选框
        const generateDate = (item) => {
            return <h-datePicker
                type="date"
                placeholder="选择日期"
                v-model={this.formData[item.key]}
                positionFixed={true}
                editable={item?.editable || false}
                placement={item?.placement || 'bottom-start'}
            ></h-datePicker>;
        };
        // 生成日期范围选择框
        const generateDaterange = (item) => {
            return <h-date-picker
                type="daterange"
                confirm
                placement={item?.placement || 'bottom-start'}
                editable={item?.editable || false}
                placeholder="选择日期"
                positionFixed={true}
                v-model={this.formData[item.key]}></h-date-picker>;
        };
        // 生成时间选择
        const generateDateTime = (item) => {
            return <h-date-picker
                type="datetime"
                placeholder="选择日期"
                v-model={this.formData[item.key]}
                positionFixed={true}
                editable={item?.editable || false}
                placement={item?.placement || 'bottom-start'}
            ></h-date-picker>;
        };
        // 生成时间范围选择框
        const generateTimerange = (item) => {
            return <h-time-picker
                confirm
                placement={item?.placement || 'bottom-start'}
                type="timerange"
                placeholder="选择时间"
                v-model={this.formData[item.key]}
                positionFixed={true}
                editable={item?.editable || false}
            ></h-time-picker>;
        };
        // 时延筛选
        const generateTimescreen = (item) => {
            const data = _.find(this.formItems, ['key', item.key]);
            return <div style="display: flex;">
                <h-select
                    v-model={this.formData[item.key]}
                    placeholder={item.placeholder || '请选择'}
                    positionFixed={true}
                    v-on:on-change={this.queryProposal}
                    style="padding-right: 4px;">
                    {
                        item.options.map(opt => {
                            return <h-option key={opt.value} value={opt.value}>{opt.label}</h-option>;
                        })
                    }
                </h-select>
                <h-select
                    v-model={this.formData['queryFieldCondition']}
                    positionFixed
                    clearable={false}
                    style="width: 50px; padding-right: 4px;">
                    <h-option value="gt">&gt;=</h-option>
                    <h-option value="lt">&lt;=</h-option>
                </h-select>
                <h-poptip
                    // trigger="focus"
                    placement={item.placement || 'bottom'}
                    transfer
                    title={data?.proposalList?.title}
                    positionFixed
                    width={545}>
                    <h-input
                        v-model={this.formData['beforeFiledValue']}
                        disabled={!this.formData[item.key]}
                        placeholder="请输入"
                        style="width: 60px; top: 0px; padding-right: 4px;"></h-input>
                    <div slot="content" class="api">
                        <h-table border columns={data.proposalList?.columns} data={data.proposalList?.tableList} size='small'></h-table>
                    </div>
                </h-poptip>

                <h-select
                    v-model={this.formData['unit']}
                    clearable={false}
                    positionFixed={true}
                    style="width: 50px;"
                >
                    <h-option value="0">s</h-option>
                    <h-option value="1">ms</h-option>
                    <h-option value="2">μs</h-option>
                    <h-option value="3">ns</h-option>
                </h-select>
            </div>;
        };

        const inputGenerators = {
            input: generateInput,
            select: generateSelect,
            selectSearch: generateSelectSearch,
            date: generateDate,
            daterange: generateDaterange,
            dateTime: generateDateTime,
            timerange: generateTimerange,
            timescreen: generateTimescreen
        };

        return <h-form
            ref="formValidate"
            props={{ model: this.formData }}
            label-width={85}
            cols={this.formCols || '3'}
            rules={this.rules}
            class="best-form"
        >
            {
                this.formItems.map(item => {
                    return <h-form-item
                        label={item?.label}
                        prop={item?.key || ''}
                        required={item?.required || false}
                        labelWidth={item?.labelWidth || undefined}
                        cols={item?.cols || undefined}
                        validRules={item?.validRules || undefined}
                    >
                        {(() => {
                            const generator = inputGenerators[item.type];
                            if (generator) {
                                return generator(item);
                            } else {
                                return '';
                            }
                        })}
                    </h-form-item>;
                })
            }
            <h-button v-on:click={() => this.reset(false)}>重置</h-button>
            <h-button v-on:click={this.query}>查询</h-button>
            <style jsx>
                {
                    `
                        .h-poptip-popper[x-placement^="bottom"] {
                            .h-poptip-arrow {
                                border-bottom-color: #fff !important;

                                &::after {
                                    border-bottom-color: #fff !important;
                                }
                            }
                        }
                    `
                }

            </style>;
        </h-form>;

    }
};
