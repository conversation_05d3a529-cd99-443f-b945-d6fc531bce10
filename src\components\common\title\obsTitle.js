/*
 * @Description:
 * @Author: <PERSON><PERSON>
 * @Date: 2023-10-07 09:50:09
 * @LastEditTime: 2024-01-29 16:20:46
 * @LastEditors: yingzx38608 <EMAIL>
 */
import './title.less';
import '@/assets/css/poptip-1.less';
import _ from 'lodash';
import aButton from '@/components/common/button/aButton';
import { transferVal } from '@/utils/utils';
export default {
    name: 'obs-title',
    components: { aButton, obsModal: () => import ('../obsModal/obsModal') },
    props: {
        title: {
            type: Object,
            default: () => {}
        }
    },
    data() {
        return {
            cascaderVals: {},
            selectVals: {},
            checkVals: {},
            switchVals: {},
            inputVal: '',   // input输入框仅支持添加一组
            modalLoading: false
        };
    },
    mounted() {
        this.init();
    },
    methods: {
        init() {
            Array.isArray(this.title.slots) &&
               this.title.slots.forEach((v) => {
                   if (v.type === 'select') {
                       this.setSelectVal(v.key, v?.defaultValue || '');
                   } else if (v.type === 'checkbox') {
                       this.setCheckVals(v.key, v?.defaultValue || false);
                   }
               });
        },
        // 设置checkbox 属性
        setCheckVals(key, val) {
            this.$set(this.checkVals, key, val);
        },
        getCheckVals(key) {
            return this.checkVals[key];
        },
        // 设置select属性
        setSelectVal(key, val) {
            // 手动设置val没变化，无法触发select的change事件
            if (val === this.selectVals[key]){
                this.handleSelectChange(val, key);
            }
            this.$set(this.selectVals, key, val);
        },
        getSelectVal(key) {
            return this.selectVals[key];
        },
        // 设置cascader属性
        setCascaderVal(key, val) {
            this.$set(this.cascaderVals, key, val);
        },
        getCascaderVal(key) {
            return this.cascaderVals[key];
        },
        // 设置input属性
        getInputVals() {
            return this.inputVal;
        },
        // ------------------------------------------  change事件 ---------------------------------------------
        handleButtonClick(key) {
            this.$emit('button-click', key);
        },
        handleSelectChange(val, key) {
            this.$emit('select-change', val, key);
        },
        handleCascaderChange(val, key) {
            this.$emit('cascader-change', val, key);
        },
        handleCheckChange(val, key) {
            this.$emit('check-change', val, key);
        },
        handleInputChange(e, searchKey) {
            this.inputVal = this.inputVal?.trim();
            this.$emit('input-change', this.inputVal, searchKey);
        },
        handleModalClick(e, key) {
            this.modalLoading = true;
            const slots = this.title?.slots || [];
            const data = _.find(slots, ['key', key]);
            data.modalData.status = !data.modalData.status;
            this.$emit('modal-click');
            this.modalLoading = false;
        },
        generatePoptip(v) {
            const { poptipInfo = {}, type, label, value, iconName, iconSize, iconColor, key } = v;
            const { width = 300, title, placement = 'top', content, contentDic } = poptipInfo;

            const renderContent = () => {
                if (typeof content === 'string') {
                    return <span>{content}</span>;
                } else if (typeof content === 'object') {
                    return Array.isArray(contentDic)
                        ? contentDic.map(item => {
                            const unit = item?.unit ?? '';
                            const key = item.key;
                            const label = transferVal(item?.label) || key;
                            const value = transferVal(content?.[key]) ? transferVal(content?.[key]) : '-';
                            const formatMethod = item.formatMethod || poptipInfo.formatMethod;
                            const formattedValue = transferVal(content?.[key]) && formatMethod ? formatMethod(value, unit) : { value, unit };
                            return <h-row>
                                <h-col span="10" title={label || '-'}>{label || '-'}</h-col>
                                <h-col span="4">&nbsp;</h-col>
                                <h-col span="10" title={`${formattedValue.value} ${formattedValue.unit ?? unit}`}>{formattedValue.value} {formattedValue.unit ?? unit}</h-col>
                            </h-row>;
                        })
                        : Object.keys(content).map(key => {
                            const label = transferVal(contentDic?.[key]) || key || '-';
                            const value = transferVal(content[key]) ? transferVal(content[key]) : '-';
                            const usedFormatMethod = poptipInfo?.formatMethod;
                            const formattedValue = transferVal(content[key]) && usedFormatMethod ? usedFormatMethod(value) : { value };
                            return <h-row>
                                <h-col span="10" title={label}>{label}</h-col>
                                <h-col span="4">&nbsp;</h-col>
                                <h-col span="10" title={formattedValue.value}>{formattedValue.value}</h-col>
                            </h-row>;
                        });
                }
                return null;
            };

            return (
                <h-poptip
                    trigger="hover"
                    width={width}
                    customTransferClassName="apm-poptip monitor-poptip"
                    transfer
                    title={title}
                    placement={placement}
                >
                    {
                        type !== 'iconButton' && (
                            <p style="cursor: pointer; margin-left: 10px">
                                {label ? `${label}: ` : ''}
                                {transferVal(value) || '-'}
                            </p>
                        )
                    }
                    {
                        type === 'iconButton' && <h-icon
                            class="icon-button"
                            name={iconName}
                            size={iconSize || 18}
                            style={{ color: iconColor || 'var(--font-color)', paddingRight: '10px' }}
                            v-on:on-click={() => this.handleButtonClick(key)}
                        />
                    }
                    <div slot="content" class="pop-content">
                        {renderContent()}
                    </div>
                </h-poptip>
            );
        }
    },
    /* eslint-disable complexity */
    render() {
        return (
            <div  class={ !this.title?.noTagColor ? 'obs-title' : ['obs-title', 'obs-title-default'] }>
                {/* 标题 */}
                {!this.title.poptipInfo && this.title.label && typeof this.title.label === 'string' && (
                    <span class="title-text" title={this.title.label}>
                        {this.title.label}
                    </span>
                )}
                {!this.title.poptipInfo && this.title.label && typeof this.title.label === 'object' && (
                    <div class="title-label">
                        {this.title?.label?.labelDic.map((v) => {
                            const label = transferVal(v?.label || v?.key) ? transferVal(v?.label || v?.key) : '';
                            const value = transferVal(this.title?.label?.labelInfo[v?.key]) ? transferVal(this.title?.label?.labelInfo[v?.key]) : '-';
                            const unit = v?.unit ?? '';
                            const formattedValue = transferVal(v?.value) && v?.formatMethod ? v.formatMethod(value, unit) : { value, unit };
                            return (
                                <p>
                                    <span>{label}：</span>
                                    <span>{formattedValue.value} {formattedValue.unit ?? unit}</span>
                                </p>
                            );
                        })}
                    </div>
                )}

                {/* 标题气泡 */}
                {this.title.poptipInfo && this.generatePoptip(this.title)}

                {/* 标题右侧slots */}
                <div class="title-box">
                    {this.title?.slots?.map((v) => {
                        if (v.type === 'text' && !v.poptipInfo) {
                            return (
                                <p>
                                    {v.label ? v.label + ': ' : ''}
                                    {transferVal(v.value) || '-'}
                                </p>
                            );
                        } else if (v.type === 'text' && v.poptipInfo) {
                            return this.generatePoptip(v);
                        } else if (v.type === 'select') {
                            return (
                                <h-select
                                    v-model={this.selectVals[v.key]}
                                    placeholder={v.placeholder || '请选择'}
                                    positionFixed={true}
                                    clearable={false}
                                    autoPlacement={true}
                                    transfer
                                    filterable={Boolean(v.filter)}
                                    v-on:on-change={(val) => this.handleSelectChange(val, v.key)}
                                    style={
                                        [
                                            v.minWidth ? { minWidth: v.minWidth } : '',
                                            v.maxWidth ? { maxWidth: v.maxWidth } : ''
                                        ]
                                    }
                                >
                                    {v.options.map((opt) => (
                                        <h-option key={opt.value} value={opt.value}>
                                            {opt.label}
                                        </h-option>
                                    ))}
                                </h-select>
                            );
                        } else if (v.type === 'cascader') { // HUI建议：数据量控制在 100 个节点 以内，层级控制在 3 层 以内
                            return (
                                <h-cascader
                                    v-model={this.cascaderVals[v.key]}
                                    data={v.cascaderData}
                                    placeholder={v.placeholder || '请选择'}
                                    positionFixed={true}
                                    clearable={v.clearable}
                                    autoPlacement={true}
                                    isInnerOverflow={true}
                                    v-on:on-change={(val) => this.handleCascaderChange(val, v.key)}
                                    style={v.minWidth ? { minWidth: v.minWidth, paddingRight: '10px' } : { paddingRight: '10px' }}
                                >
                                </h-cascader>
                            );
                        } else if (v.type === 'button') {
                            return (
                                <a-button
                                    type={v?.buttonType || 'dark'}
                                    onClick={() => this.handleButtonClick(v.key)}
                                >
                                    {v?.iconName && <h-icon class='icon-button' name={v?.iconName} size={v?.iconSize || 18} style={{ color: v?.iconColor || 'var(--font-color)' }}></h-icon> }
                                    {v?.value || ''}
                                </a-button>
                            );
                        } else if (v.type === 'iconButton') {
                            return v.poptipInfo ? this.generatePoptip(v) : (
                                <h-icon
                                    class='icon-button'
                                    name={v?.iconName}
                                    size={v?.iconSize || 18}
                                    style={{ color: v?.iconColor || 'var(--font-color)', paddingRight: '10px' }}
                                    v-on:on-click={() => this.handleButtonClick(v?.key)}
                                />
                            );
                        } else if (v.type === 'checkbox') {
                            return (
                                <h-checkbox
                                    v-model={this.checkVals[v.key]}
                                    v-on:on-change={(val) => this.handleCheckChange(val, v.key)}
                                    style="padding-right: 10px;"
                                >
                                    {v.label}
                                </h-checkbox>
                            );
                        } else if (v.type === 'input') { // input输入框仅支持添加一组
                            return (
                                <h-input
                                    v-model={this.inputVal}
                                    icon="search"
                                    placeholder={v.placeholder || '请输入'}
                                    v-on:on-blur={(e) => this.handleInputChange(e, v.searchKey)}
                                    v-on:on-click={(e) => this.handleInputChange(e, v.searchKey)}
                                    style="padding-right: 10px;"
                                ></h-input>
                            );
                        } else if (v.type === 'modal') {
                            return (
                                <div>
                                    <a-button
                                        type={v?.buttonType || 'text'}
                                        onClick={(e) => this.handleModalClick(e, v.key)}
                                    >
                                        {v?.iconName && <h-icon class='icon-button' name={v?.iconName} size={v?.iconSize || 18} style={{ color: v?.iconColor || 'var(--font-color)' }}></h-icon> }
                                        {v?.value || ''}
                                    </a-button>
                                    <obs-modal key={v.key} modalInfo={v.modalData} loading={this.modalLoading}></obs-modal>
                                </div>
                            );
                        } else if (v.type === 'switch') {
                            return (
                                <div>
                                    <h-switch
                                        v-model={this.switchVals[v.key]}
                                        size="large"
                                        v-on:on-change={(val) => this.handleCheckChange(val, v.key)}
                                    >
                                        <div slot="open">{v.openText}</div>
                                        <div slot="close">{v.closeText}</div>
                                    </h-switch>
                                </div>
                            );
                        }
                        return '';
                    })}
                </div>
                {this.$slots.extraTitleBox}
            </div>
        );
    }
};
