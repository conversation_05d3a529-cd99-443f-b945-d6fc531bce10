@import url("@/assets/css/input.less");
@import url("@/assets/css/steps.less");
@import url("@/assets/css/checkbox.less");

.create-step {
    position: relative;
    height: 100%;
    width: 100%;
    min-width: 900px;
    background: var(--wrapper-color);

    .steps-box {
        padding: 10px 15px;
    }

    .content-box {
        height: calc(100% - 100px);
        padding: 10px 15px 0;
        overflow: auto;

        .tips-box {
            width: 100%;
            background: #2d334c;
            border-radius: 4px;
            padding: 15px;
            line-height: 20px;

            .tip-title {
                color: var(--font-color);
                padding-bottom: 10px;
                font-weight: 600;
            }

            .tip-content {
                color: #cacfd4;
            }
        }
    }

    .error-threshold-box {
        width: 100%;
        display: flex;
        align-items: center;
        background: #2d334c;
        border-radius: 4px;
        margin: 10px 0;
        padding: 15px;
        overflow: hidden; //超出的文本隐藏
        text-overflow: ellipsis; //溢出用省略号显示
        white-space: nowrap; //溢出不换行

        /deep/ .h-input-group-append {
            color: var(--font-color);
            background-color: var(--input-bg-color);
            border: var(--border);
        }

        .divider {
            height: 24px;
            width: 1px;
            border-right: 1px solid #444a60;
            margin: 0 5px;
        }

        .tip-text {
            color: #9296a1;
        }
    }

    /deep/ .h-select-dropdown {
        margin: 0;
    }

    /deep/ .h-form-item {
        margin-bottom: 34px;
    }

    .form-box {
        width: 700px;
        padding: 10px 15px;
        color: var(--font-color);

        p {
            margin-left: 40px;
        }

        .form-block {
            margin-left: 40px;
            background: #2d334c;
            padding: 10px;
            margin-bottom: 20px;
            border-radius: 4px;

            .h-form-item {
                width: 90%;
                margin-bottom: 10px;
            }
        }
    }

    .form-box-2 {
        width: 100%;
        padding: 10px 15px;
        color: var(--font-color);

        /deep/ .h-input-wrapper {
            textarea {
                resize: none;
                line-height: 20px;
            }

            textarea::placeholder {
                line-height: 20px;
                color: #9296a1;
                font-family: PingFangSC-Regular;
            }
        }
    }

    .confirm-box {
        width: 100%;
        padding: 15px 0;

        .confirm-title {
            color: var(--font-color);
            font-size: 14px;
            padding: 10px;

            &::before {
                display: inline-block;
                position: relative;
                left: -10px;
                top: 3px;
                content: "";
                width: 4px;
                height: 16px;
                background: var(--link-color);
            }

            span {
                font-size: 12px;
                padding-left: 10px;
                color: #cacfd4;
            }
        }

        .confirm-tips {
            height: 32px;
            line-height: 32px;
            background: #1f3759;
            border-radius: 4px;
            padding: 0 5px;
            color: var(--font-color);
        }

        .tags-input {
            color: var(--font-color);
            padding-left: 15px;

            .h-select {
                padding-left: 10px;
                width: 400px;

                /deep/ .h-select-dropdown-content .h-select-content-input {
                    color: inherit;
                }

                /deep/ .h-checkbox-inner {
                    border: 1px solid #d7dde4;
                    border-radius: 2px;
                    background-color: #fff;
                }
            }
        }
    }

    .buttom-box {
        position: absolute;
        bottom: 0;
        width: 100%;
        height: 48px;
        line-height: 48px;
        padding: 0 15px;
        border-top: 1px solid #444a60;

        .h-btn {
            margin-right: 5px;
        }

        .h-btn:first-child {
            margin-left: 15px;
        }
    }

    .table-select-title {
        // 清除搜索框校验样式
        /deep/ .h-form-item-error .h-input {
            border-color: #9ea7b4 !important;
        }

        /deep/ .h-form-item-error .h-input-icon {
            color: #9ea7b4;
        }
    }
}
