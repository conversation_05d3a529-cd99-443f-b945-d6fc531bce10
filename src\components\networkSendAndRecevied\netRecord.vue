<template>
    <div ref="result-box" class="net-send">
        <menu-layout ref="menu" customMenu :defaultWidth="260" @menu-fold="menuFold">
            <template v-slot:menu>
                <div class="menu">
                    <div
                        class="header-menu"
                        >
                        <span>LDPMSG</span>
                        <span class="icon-box">
                        <a-button
                            type="dark"
                            style="padding: 7px 5px 5px; width: 32px; height: 32px;"
                            @click="handleImportUseCase">
                            <svg t="1739501210920" class="icon" viewBox="0 0 1024 1024" version="1.1"
                                xmlns="http://www.w3.org/2000/svg" p-id="2516" width="14" height="14">
                                <path d="M512 224h-64v320h320v-64H557.3l339.9-339.9L852 94.8l-340 340z" fill="#ffffff" p-id="2517"></path>
                                <path d="M864 513.2V848c0 26.5-18.8 48-41.8 48H201.8c-23 0-41.8-21.5-41.8-48V178.4c0-26.5 18.8-48 41.8-48H512v-64H201.8c-58.3 0-105.8 50.2-105.8 112V848c0 61.8 47.5 112 105.8 112h620.4c58.3 0 105.8-50.2 105.8-112V513.2h-64z"
                                    fill="#ffffff" p-id="2518"></path>
                            </svg>
                        </a-button>
                        </span>
                    </div>
                    <div class="record-list">
                        <editable-list
                            ref="editList"
                            :menuList="menuList"
                            :menuId="menuId"
                            @save="handleMenuNameSave"
                            @delete="handleMenuDelete"
                            @check-menu="checkMenu"
                        />
                    </div>
                </div>
            </template>
            <template v-slot:right>
                <div class="record-content">
                    <obs-title
                        :title="jsonTitle"
                        @button-click="handleButtonClick"
                    />
                    <a-loading v-if="jsonLoading" class="json-box"></a-loading>
                    <div v-else ref="json-box" class="json-box">
                        <div class="json-box-title"
                            @click="toggleJsonCollapse('Headers')">
                            <span>Headers</span>
                            <h-icon :name="jsonShow.Headers ? 'packup' : 'unfold'"></h-icon>
                        </div>
                        <div v-show="jsonShow.Headers" class="json-box-editor">
                            <vue-json-editor
                                v-if="jsonBox"
                                v-model="jsonData.headers"
                                class="json-record"
                                :showBtns="false"
                                :mode="'code'"
                                lang="zh"
                                :expandedOnStart="true"
                                :style="{ height: jsonHeight }"
                                @json-change="onJsonChange('Headers')"
                                @has-error="onJsonError('Headers')">
                            </vue-json-editor>
                        </div>
                        <div class="json-box-title" style="margin-top: 10px;"
                            @click="toggleJsonCollapse('Body')">
                            <span>Body</span>
                            <h-icon :name="jsonShow.Body ? 'packup' : 'unfold'"></h-icon>
                        </div>
                        <div v-show="jsonShow.Body" class="json-box-editor">
                            <vue-json-editor
                                v-if="jsonBox"
                                v-model="jsonData.body"
                                class="json-record"
                                :showBtns="false"
                                :mode="'code'"
                                lang="zh"
                                :expandedOnStart="true"
                                :style="{ height: jsonHeight }"
                                @json-change="onJsonChange('Body')"
                                @has-error="onJsonError('Body')">
                            </vue-json-editor>
                        </div>
                    </div>
                </div>
            </template>
        </menu-layout>

        <a-loading v-if="loading"></a-loading>

        <!-- 导入用例弹窗 -->
        <import-use-case-modal
            v-model="useCaseImportInfo.status"
            :modalData="useCaseImportInfo"
            @update="initData"
        >
        </import-use-case-modal>
    </div>
</template>

<script>
import ImportUseCaseModal from '@/components/networkSendAndRecevied/modal/importUseCaseModal';
import obsTitle from '@/components/common/title/obsTitle';
import menuLayout from '@/components/common/menuLayout/menuLayout';
import editableList from '@/components/networkSendAndRecevied/modal/editableList.vue';
import vueJsonEditor from 'vue-json-editor-fix-cn';
import aButton from '@/components/common/button/aButton';
import aLoading from '@/components/common/loading/aLoading';
import { isJSON } from '@/utils/utils';
import { getUseCaseList, getUseCaseInfo, deleteUseCase, saveUseCaseName, saveUseCaseInfo } from '@/api/networkApi';
export default {
    props: {
        productInstNo: {
            type: String,
            default: ''
        }
    },
    components: { ImportUseCaseModal, menuLayout, aLoading, editableList, obsTitle, vueJsonEditor, aButton },
    data() {
        return {
            loading: false,
            jsonLoading: false,
            tabName: 'LDP_MSG',
            menuFoldStatus: false,
            menuId: '',
            menuList: [],
            jsonTitle: {
                label: '用例内容',
                slots: [
                    {
                        type: 'button',
                        buttonType: 'dark',
                        key: 'saveRecord',
                        value: '保存'
                    }
                ]
            },
            jsonData: {
                headers: {},
                body: {}
            },
            headersJsonFormat: true,
            bodyJsonFormat: true,
            useCaseImportInfo: {
                status: false
            },
            jsonShow: {
                Headers: false,
                Body: true
            },
            jsonHeight: '200px',
            jsonBox: true
        };
    },
    mounted() {
        window.addEventListener('resize', this.adjustJsonHeight);
        this.adjustJsonHeight();
    },
    beforeDestroy() {
        window.removeEventListener('resize', this.adjustJsonHeight);
    },
    methods: {
        menuFold(status) {
            this.menuFoldStatus = status;
        },
        // 初始化
        async initData(id) {
            this.loading = true;
            // 清理页面数据
            this.cleanData();
            await this.setUseCaseData(id);
            this.$nextTick(() => {
                this.$refs['editList'].init();
                this.loading = false;
            });
        },
        cleanData() {
            this.menuId = '';
            this.jsonData = {
                headers: {},
                body: {}
            };
        },
        // json编辑器展开收起
        toggleJsonCollapse(item) {
            this.jsonShow[item] = !this.jsonShow[item];

            if (item === 'Body' && this.jsonShow.Headers) {
                // 自动滚动到body位置
                this.$nextTick(() => {
                    const jsonBox = this.$refs['json-box'];
                    if (jsonBox) {
                        const scrollTop = parseInt(this.jsonHeight, 10);
                        jsonBox.scrollTop = scrollTop;
                    }
                });
            }
        },
        // 计算json编辑器高度
        adjustJsonHeight() {
            const totalHeight = this.$refs['json-box']?.offsetHeight;
            const expendHeight = (totalHeight - 100) + 'px';
            this.jsonHeight = expendHeight;
        },
        // 设置用例列表数据
        async setUseCaseData(menuId) {
            let useCaseData = [];
            const params = {
                productId: this.productInstNo,
                protocol: this.tabName
            };
            try {
                const res = await getUseCaseList(params);
                if (res.code === '200') {
                    useCaseData = res.data || [];
                }
            } catch (err) {
                console.error(err);
            }
            this.menuList = useCaseData.map(item => {
                return {
                    id: item?.id,
                    label: item.name,
                    editing: false
                };
            });
            // 默认选中列表第一项
            this.menuId = menuId ? menuId : this.menuList?.[0]?.id;
            this.jsonTitle.label = this.menuList?.[0]?.label ? `用例：${this.menuList?.[0]?.label}` : '用例内容';
            await this.queryUseCaseInfo(this.menuId);
        },
        // 获取发包用例详情
        async queryUseCaseInfo(useCaseId) {
            // json数据重置
            this.jsonData = {
                headers: {},
                body: {}
            };

            if (!useCaseId) return;
            this.jsonLoading = true;
            const params = {
                productId: this.productInstNo,
                useCaseId: useCaseId,
                protocol: this.tabName
            };
            try {
                const res = await getUseCaseInfo(params);
                if (res.code === '200') {
                    // 用例列表
                    this.jsonData.headers = (res?.data?.header && isJSON(res?.data?.header)) ? JSON.parse(res?.data?.header) : {};
                    this.jsonData.body = (res?.data?.body && isJSON(res?.data?.body)) ? JSON.parse(res?.data?.body) : {};
                } else if (res.code?.length === 8) {
                    this.$hMessage.error(res.message);
                }
            } catch (err) {
                console.error(err);
            } finally {
                this.jsonLoading = false;
            }

            // 销毁重建json编辑框 - 避免ctrl+z时撤回展示其他产品数据
            this.jsonBox = false;
            this.$nextTick(() => {
                this.jsonBox = true;
            });
        },
        // 用例切换
        checkMenu(item) {
            // 重置json格式校验状态
            this.headersJsonFormat = true;
            this.bodyJsonFormat = true;

            this.menuId = item.id;
            this.jsonTitle.label = item?.label ? `用例：${item?.label}` : '用例内容';
            this.queryUseCaseInfo(this.menuId);
        },
        // 用例新增、编辑
        async handleMenuNameSave(item, name) {
            const menuId = item.id ? item.id : '';
            if (menuId) {
                await this.handleUseCaseRename(menuId, name);
            } else {
                // 新增用例
                await this.saveUseCase('add', name);
            }
        },
        // 用例名重命名
        async handleUseCaseRename(id, name) {
            const params = {
                id: id,
                name: name
            };
            try {
                const res = await saveUseCaseName(params);
                if (res.code === '200') {
                    this.$hMessage.success('用例名修改成功!');
                    // 重新获取用例列表
                    this.initData(id);
                } else if (res.code?.length === 8) {
                    this.$hMessage.error(res.message);
                }
            } catch (err) {
                console.error(err);
            }
        },
        // 用例删除
        handleMenuDelete(item) {
            this.$hMsgBoxSafe.confirm({
                title: '删除',
                content: `您确定删除 '${item.label}' 用例？`,
                onOk: async () => {
                    const res = await deleteUseCase({
                        useCaseIds: [item.id]
                    });
                    if (res.code === '200') {
                        this.$hMessage.success('用例删除成功!');
                        this.initData();
                    } else if (res.code?.length === 8) {
                        this.$hMessage.error({
                            content: res.message,
                            closable: true
                        });
                    }
                }
            });
        },
        // json格式报错
        onJsonError(type) {
            if (type === 'Headers') {
                this.headersJsonFormat = false;
            } else if (type === 'Body') {
                this.bodyJsonFormat = false;
            }
        },
        onJsonChange(type) {
            if (type === 'Headers') {
                this.headersJsonFormat = true;
            } else if (type === 'Body') {
                this.bodyJsonFormat = true;
            }
        },
        // 用例内容新增、修改接口
        async saveUseCase(type, name) {
            // 用例内容校验 - json格式
            if (!this.headersJsonFormat) {
                this.$hMessage.error('Headers为空或格式不正确');
                return;
            }
            if (!this.bodyJsonFormat) {
                this.$hMessage.error('Body内容为空或格式不正确');
                return;
            }
            const caseName = type === 'add' ? name : this.menuList.find(o => o.id === this.menuId)?.label;
            // 校验通过请求接口
            const params = {
                productId: this.productInstNo,
                id: type === 'add' ? '' : this.menuId,
                name: caseName,
                protocol: this.tabName,
                header: type === 'add' ? '' : JSON.stringify(this.jsonData.headers),
                body: type === 'add' ? '' : JSON.stringify(this.jsonData.body)
            };
            try {
                const res = await saveUseCaseInfo(params);
                if (res.code === '200') {
                    this.$hMessage.success('用例保存成功!');
                    // 重新获取用例列表
                    this.initData(res.data?.id || this.menuId);
                } else if (res.code?.length === 8) {
                    this.$hMessage.error(res.message);
                }
            } catch (err) {
                console.error(err);
            }
        },

        // 保存用例内容（包含新增用例）
        handleButtonClick() {
            if (!this.menuId) {
                this.$hMessage.error('请先选择用例');
                return;
            }
            this.saveUseCase();
        },
        // 导入用例
        handleImportUseCase() {
            this.useCaseImportInfo.status = true;
            this.useCaseImportInfo.productId = this.productInstNo;
            this.useCaseImportInfo.protocol = this.tabName;
        }
    }
};
</script>

<style lang="less" scoped>
@import url("@/assets/css/menu.less");
@import url("@/assets/css/json-view");
@import url("@/assets/css/json-editor");

.net-send {
    height: 100%;

    /deep/ .h-tabs-nav-wrap {
        float: none !important;
    }

    /deep/ .h-tabs-nav-right {
        position: absolute;
        right: 0;
        top: 5px;
    }

    .menu {
        height: 100%;
        overflow: hidden;

        .header-menu {
            padding: 0 10px 0 18px;

            &::before {
                position: absolute;
                top: 14px;
                left: 5px;
                content: "";
                width: 4px;
                height: 16px;
                background: var(--link-color);
            }

            .icon-box {
                float: right;
            }
        }
    }

    .record-list {
        height: calc(100% - 44px);
    }

    .record-content {
        height: 100%;
        background-color: var(--wrapper-color);
        border-radius: var(--border-radius);
        overflow: auto;
    }

    .json-box {
        height: calc(100% - 48px);
        border-radius: 4px;
        padding: 0 10px;
        overflow: auto;

        .json-box-title {
            height: 40px;
            background: #323953;
            border-radius: 4px 4px 0 0;
            padding: 10px;
            color: var(--font-color);
            font-weight: 500;

            span {
                font-size: 14px;
            }

            &:hover {
                cursor: pointer;
            }
        }
    }

    .json-record {
        height: calc(100% - 50px);
    }
}
</style>
