import {  initRouterLayout } from 'hui-core';

function Index() {
    return import(/* webpackChunkName: "index" */ '@/views/index.vue');
}
function IndexAnalyseConfig() {
    return import(
    /* webpackChunkName: "index-analyseConfig" */ '@/views/index/analyseConfig.vue'
    );
}
function IndexAnalyseData() {
    return import(
    /* webpackChunkName: "index-analyseData" */ '@/views/index/analyseData.vue'
    );
}
function LdpMonitor() {
    return import (
        '@/views/index/ldpMonitor/index.vue'
    );
}
function LdpAppMonitor() {
    return import (
        '@/views/index/ldpMonitor/ldpAppMonitor.vue'
    );
}
function LdpClusterMonitor() {
    return import (
        '@/views/index/ldpMonitor/clusterMonitor.vue'
    );
}
function IndexAppMonitor() {
    return import(
    /* webpackChunkName: "index-appMonitor" */ '@/views/index/ldpMonitor/appMonitor.vue'
    );
}
function IndexBusinessMonitor() {
    return import(
    /* webpackChunkName: "index-businessMonitor" */ '@/views/index/monitor/businessMonitor.vue'
    );
}
function IndexLdpLinkConfig() {
    return import(
    /* webpackChunkName: "index-ldpLinkConfig" */ '@/views/index/ldpLinkConfig.vue'
    );
}
function IndexLdpDataObservation() {
    return import(
    /* webpackChunkName: "index-ldpDataObservation" */ '@/views/index/ldpDataObservation.vue'
    );
}
function IndexLdpTable() {
    return import(
    /* webpackChunkName: "index-ldpTable" */ '@/views/index/ldpTable.vue'
    );
}
function IndexSqlTable() {
    return import (
        '@/views/index/sqlTable.vue'
    );
}
function IndexSqlCores() {
    return import (
        '@/views/index/sqlCores.vue'
    );
}
function IndexManagementQuery() {
    return import(
    /* webpackChunkName: "index-ldpTable" */ '@/views/index/managementQuery.vue'
    );
}
function IndexMarketAllLink() {
    return import(
    /* webpackChunkName: "index-marketAllLink" */ '@/views/index/marketAllLink.vue'
    );
}
function IndexMarketMonitor() {
    return import(
    /* webpackChunkName: "index-marketMonitor" */ '@/views/index/marketMonitor.vue'
    );
}
function IndexMarketNodeDelayList() {
    return import(
    /* webpackChunkName: "index-marketNodeDelayList" */ '@/views/index/marketNodeDelayList.vue'
    );
}
function IndexMarketPenetrateList() {
    return import(
    /* webpackChunkName: "index-marketPenetrateList" */ '@/views/index/marketPenetrateList.vue'
    );
}
function IndexMarketTimeDelay() {
    return import(
    /* webpackChunkName: "index-marketTimeDelay" */ '@/views/index/marketTimeDelay.vue'
    );
}
function IndexMonitor() {
    return import(
    /* webpackChunkName: "index-monitor" */ '@/views/index/monitor/index.vue'
    );
}
function IndexNoticeManagerList() {
    return import(
    /* webpackChunkName: "index-noticeManagerList" */ '@/views/index/noticeManagerList.vue'
    );
}
function IndexProductDataStorage() {
    return import(
    /* webpackChunkName: "index-productDataStorage" */ '@/views/index/productDataStorage.vue'
    );
}
function IndexProductTimeDetail() {
    return import(
    /* webpackChunkName: "index-productTimeDetail" */ '@/views/index/productTimeDetail.vue'
    );
}
function IndexProductTimeSummary() {
    return import(
    /* webpackChunkName: "index-productTimeSummary" */ '@/views/index/productTimeSummary.vue'
    );
}
function IndexSmsList() {
    return import(
    /* webpackChunkName: "index-smsList" */ '@/views/index/smsList.vue'
    );
}
function IndexTransaction() {
    return import(
    /* webpackChunkName: "index-transaction" */ '@/views/index/transaction.vue'
    );
}
function IndexRcmDeploy() {
    return import(
    /* webpackChunkName: "index-rcmDeploy" */ '@/views/index/rcmDeploy.vue'
    );
}
function IndexRcmBacklogMonitor() {
    return import(
    /* webpackChunkName: "index-rcmBacklogMonitor" */ '@/views/index/rcmBacklogMonitor.vue'
    );
}
function IndexMcDeploy() {
    return import(
    /* webpackChunkName: "index-mcDeploy" */ '@/views/index/mcDeploy.vue'
    );
}
function IndexMcDataObservation() {
    return import(
    /* webpackChunkName: "index-mcDataObservation" */ '@/views/index/mcDataObservation.vue'
    );
}
function MdbDataObservation() {
    return import(
        '@/views/index/mdbDataObservation.vue'
    );
}
function IndexMdbPrivilegeManage() {
    return import(
        /* webpackChunkName: "index-mdbPrivilegeManage" */ '@/views/index/mdbPrivilegeManage.vue'
    );
}
function IndexBrokerDataLimit() {
    return import(
        '@/views/index/brokerDataLimit.vue'
    );
}
function IndexUstTableVerification() {
    return import(
    /* webpackChunkName: "index-ustTableVerification" */ '@/views/index/ustTableVerification.vue'
    );
}

function IndexCoreReplayObservation() {
    return import(
    /* webpackChunkName: "index-coreReplayObservation" */ '@/views/index/coreReplayObservation.vue'
    );
}

function IndexProductServiceList() {
    return import(
    /* webpackChunkName: "index-productServiceList" */ '@/views/index/productServiceConfig/productServiceList.vue'
    );
}
function IndexTripartiteServiceList() {
    return import(
    /* webpackChunkName: "index-tripartiteServiceList" */ '@/views/index/tripartiteServiceConfig/tripartiteServiceList.vue'
    );
}
function IndexLatencyTrendAnalysis() {
    return import (
        '@/views/index/latencyTrendAnalysis'
    );
}
function TestLXY() {
    return import(
    /* webpackChunkName: "testLXY" */ '@/views/testLXY.vue'
    );
}

function IndexNetworkSendAndRecevied() {
    return import(
    /* webpackChunkName: "index-networkSendAndRecevied" */ '@/views/index/networkSendAndRecevied.vue'
    );
}

function IndexTopoMonitor() {
    return import(
    /* webpackChunkName: "index-topoMonitor" */ '@/views/index/topoMonitor.vue'
    );
}

function IndexAppRunningState() {
    return import(
    /* webpackChunkName: "index-topoMonitor" */ '@/views/index/appRunningState.vue'
    );
}

function IndexAccordMonitor() {
    return import(
        '@/views/index/accordMonitor.vue'
    );
}

function IndexAccordObservation() {
    return import(
        '@/views/index/accordObservation.vue'
    );
}

function IndexRcmObservation() {
    return import(
        '@/views/index/rcmObservation.vue'
    );
}

function IndexLocateConfig() {
    return import(
        '@/views/index/locateConfig.vue'
    );
}

function IndexLdpLogCenter() {
    return import(
        '@/views/index/ldpLogCenter.vue'
    );
}

function IndexThreadInfoOverview() {
    return import(
        '@/views/index/threadInfoOverview.vue'
    );
}

function IndexcoreFuncHandleObservation() {
    return import('@/views/index/coreFuncHandleObservation/index.vue');
}

function IndexCreateRule() {
    return import('@/views/index/createRule/createRule.vue');
}
function IndexDataSecondAppearance() {
    return import('@/views/index/dataSecondAppearance.vue');
}

function IndexEmergencyManagementConfig() {
    return import('@/views/index/emergencyManagementConfig/index.vue');
}

function indexMDBDataExport() {
    return import('@/views/index/mdbDataExport/index.vue');
}

function indexCreateMDBDataExport() {
    return import('@/views/index/mdbDataExport/createExportTask/index.vue');
}

export default [
    {
        path: '/',
        component: initRouterLayout(layout => {
            return import('@/layouts/' + layout + '.vue');
        }),
        children: [
            {
                name: 'index',
                path: '',
                component: Index,
                children: [
                    {
                        name: 'index-analyseConfig',
                        path: 'analyseConfig',
                        component: IndexAnalyseConfig
                    },
                    {
                        name: 'index-analyseData',
                        path: 'analyseData',
                        component: IndexAnalyseData
                    },
                    {
                        name: 'index-appMonitor',
                        path: 'appMonitor',
                        component: IndexAppMonitor
                    },
                    {
                        name: 'index-businessMonitor',
                        path: 'businessMonitor',
                        component: IndexBusinessMonitor
                    },
                    {
                        name: 'index-ldpDataObservation',
                        path: 'ldpDataObservation',
                        component: IndexLdpDataObservation
                    },
                    {
                        name: 'index-ldpLinkConfig',
                        path: 'ldpLinkConfig',
                        component: IndexLdpLinkConfig
                    },
                    {
                        name: 'index-ldpTable',
                        path: 'ldpTable',
                        component: IndexLdpTable
                    },
                    {
                        name: 'index-sqlTable',
                        path: 'sqlTable',
                        component: IndexSqlTable
                    },
                    {
                        name: 'index-sqlCores',
                        path: 'sqlCores',
                        component: IndexSqlCores
                    },
                    {
                        name: 'index-managementQuery',
                        path: 'managementQuery',
                        component: IndexManagementQuery
                    },
                    {
                        name: 'index-mdbDataObservation',
                        path: 'mdbDataObservation',
                        component: MdbDataObservation
                    },
                    {
                        name: 'index-marketAllLink',
                        path: 'marketAllLink',
                        component: IndexMarketAllLink
                    },
                    {
                        name: 'index-marketMonitor',
                        path: 'marketMonitor',
                        component: IndexMarketMonitor
                    },
                    {
                        name: 'index-marketNodeDelayList',
                        path: 'marketNodeDelayList',
                        component: IndexMarketNodeDelayList
                    },
                    {
                        name: 'index-marketPenetrateList',
                        path: 'marketPenetrateList',
                        component: IndexMarketPenetrateList
                    },
                    {
                        name: 'index-marketTimeDelay',
                        path: 'marketTimeDelay',
                        component: IndexMarketTimeDelay
                    },
                    {
                        name: 'index-monitor',
                        path: 'monitor',
                        component: IndexMonitor,
                        children: [
                            {
                                name: 'buseinessMonitor',
                                path: 'businessMonitor',
                                component: IndexBusinessMonitor
                            }
                        ]
                    },
                    {
                        name: 'ldp-monitor',
                        path: 'ldpMonitor',
                        component: LdpMonitor,
                        children: [
                            {
                                name: 'ldpAppMonitor',
                                path: 'ldpAppMonitor',
                                component: LdpAppMonitor
                            },
                            {
                                name: 'clusterMonitor',
                                path: 'clusterMonitor',
                                component: LdpClusterMonitor
                            }
                        ]
                    },
                    {
                        name: 'index-noticeManagerList',
                        path: 'noticeManagerList',
                        component: IndexNoticeManagerList
                    },
                    {
                        name: 'index-productDataStorage',
                        path: 'productDataStorage',
                        component: IndexProductDataStorage
                    },
                    {
                        name: 'index-productTimeDetail',
                        path: 'productTimeDetail',
                        component: IndexProductTimeDetail
                    },
                    {
                        name: 'index-productTimeSummary',
                        path: 'productTimeSummary',
                        component: IndexProductTimeSummary
                    },
                    {
                        name: 'index-smsList',
                        path: 'smsList',
                        component: IndexSmsList
                    },
                    {
                        name: 'index-transaction',
                        path: 'transaction',
                        component: IndexTransaction
                    },
                    {
                        name: 'index-rcmDeploy',
                        path: 'rcmDeploy',
                        component: IndexRcmDeploy
                    },
                    {
                        name: 'index-rcmBacklogMonitor',
                        path: 'rcmBacklogMonitor',
                        component: IndexRcmBacklogMonitor
                    },
                    {
                        name: 'index-mcDeploy',
                        path: 'mcDeploy',
                        component: IndexMcDeploy
                    },
                    {
                        name: 'index-mcDataObservation',
                        path: 'mcDataObservation',
                        component: IndexMcDataObservation
                    },
                    {
                        name: 'index-brokerDataLimit',
                        path: 'brokerDataLimit',
                        component: IndexBrokerDataLimit
                    },
                    {
                        name: 'index-ustTableVerification',
                        path: 'ustTableVerification',
                        component: IndexUstTableVerification
                    },
                    {
                        name: 'index-ccoreReplayObservation',
                        path: 'coreReplayObservation',
                        component: IndexCoreReplayObservation
                    },
                    {
                        name: 'index-mdbPrivilegeManage',
                        path: 'mdbPrivilegeManage',
                        component: IndexMdbPrivilegeManage
                    },
                    {
                        name: 'index-networkSendAndRecevied',
                        path: 'networkSendAndRecevied',
                        component: IndexNetworkSendAndRecevied
                    },
                    {
                        name: 'index-productServiceList',
                        path: 'productServiceList',
                        component: IndexProductServiceList
                    },
                    {
                        name: 'index-tripartiteServiceList',
                        path: 'tripartiteServiceList',
                        component: IndexTripartiteServiceList
                    },
                    {
                        name: 'index-latencyTrendAnalysis',
                        path: 'latencyTrendAnalysis',
                        component: IndexLatencyTrendAnalysis
                    },
                    {
                        name: 'index-topoMonitor',
                        path: 'topoMonitor',
                        component: IndexTopoMonitor
                    },
                    {
                        name: 'index-appRunningState',
                        path: 'appRunningState',
                        component: IndexAppRunningState
                    }, {
                        name: 'index-accordMonitor',
                        path: 'accordMonitor',
                        component: IndexAccordMonitor
                    }, {
                        name: 'index-accordObservation',
                        path: 'accordObservation',
                        component: IndexAccordObservation
                    }, {
                        name: 'index-rcmObservation',
                        path: 'rcmObservation',
                        component: IndexRcmObservation
                    }, {
                        name: 'index-locateConfig',
                        path: 'locateConfig',
                        component: IndexLocateConfig
                    }, {
                        name: 'index-ldpLogCenter',
                        path: 'ldpLogCenter',
                        component: IndexLdpLogCenter
                    },
                    {
                        name: 'index-threadInfoOverview',
                        path: 'threadInfoOverview',
                        component: IndexThreadInfoOverview
                    },
                    {
                        name: 'index-coreFuncHandleObservation',
                        path: 'coreFuncHandleObservation',
                        component: IndexcoreFuncHandleObservation
                    },
                    {
                        name: 'index-createRule',
                        path: 'createRule',
                        component: IndexCreateRule
                    },
                    {
                        name: 'index-emergencyManagementConfig',
                        path: 'emergencyManagementConfig',
                        component: IndexEmergencyManagementConfig
                    },
                    {
                        name: 'index-dataSecondAppearance',
                        path: 'dataSecondAppearance',
                        component: IndexDataSecondAppearance
                    },
                    {
                        name: 'index-mdbDataExport',
                        path: 'mdbDataExport',
                        component: indexMDBDataExport
                    },
                    {
                        name: 'index-createMdbDataExport',
                        path: 'createMdbDataExport',
                        component: indexCreateMDBDataExport
                    }
                ]
            },
            {
                name: 'testLXY',
                path: 'testLXY',
                component: TestLXY
            }
        ]
    }
];

