<!-- 表记录总数校验 比对结果详情弹窗 -->
<template>
    <div>
        <h-msg-box
            ref="drawer-box"
            v-model="modalData.status"
            title="执行结果"
            top="50"
            width="700"
            maxHeight="400"
            allowCopy
            :mask-closable="false"
            @on-close="handleDrawerClose">
            <div v-if="modalData.compareResult" ref="basic-info" class="basic-info">
                <h-row>
                    <h-col class="col-title">
                        <span class="info-label">
                            {{modalData.compareResult === 'exception' ? '异常' : '不通过'}}原因:
                        </span>
                    </h-col>
                    <h-col class="col-content">
                        <span class="info-value">{{ modalData.errorInfo || '-' }}</span>
                    </h-col>
                </h-row>
            </div>
            <!-- 表记录总数 -->
            <div v-if="modalData.ruleType === 'tableRecordCount'">
                <p class="">已完成的校验内容结果如下：</p>
                <h-simple-table
                    :columns="columns"
                    :data="tableData"
                    showTitle
                    :loading="loading"
                    :height="tableHeight"
                    class="table-info">
                </h-simple-table>
            </div>
            <div v-if="modalData.ruleType === 'tableField'" class="no-data-box">
                <apm-blank
                    name="DataWhite"
                    width="120"
                    height="80">
                    <slot>
                        <div class="slot-box">
                            「表字段」校验暂不支持线上查看结果，可点击
                            <span @click="downloadCompareResult">下载结果</span>
                            至本地查看
                        </div>
                    </slot>
                </apm-blank>
            </div>

            <template v-slot:footer>
                <a-button @click="modalData.status = false">关闭</a-button>
                <a-button type="primary" @click="downloadCompareResult">下载结果</a-button>
            </template>
        </h-msg-box>
    </div>
</template>

<script>
import apmBlank from '@/components/common/apmBlank/apmBlank';
import aButton from '@/components/common/button/aButton';
import { getTableCountDetail, downloadCompareResult } from '@/api/dataVerification';
export default {
    name: 'VerifyDetailModal',
    components: { aButton, apmBlank },
    props: {
        productId: {
            type: String,
            default: ''
        },
        modalInfo: {
            type: Object,
            default: null
        }
    },
    data() {
        function rowRender(h, params, key) {
            const value = typeof params.row?.[key] === 'number' ? params.row?.[key] ?? '-' : params.row?.[key] || '-';

            return h('div', {
                attrs: {
                    title: value
                },
                style: {
                    width: '100%',
                    overflow: 'hidden', // 超出的文本隐藏
                    'text-overflow': 'ellipsis', // 溢出用省略号显示
                    'white-space': 'nowrap' // 溢出不换行
                }
            }, value);
        }
        return {
            modalData: this.modalInfo,
            loading: false,
            columns: [
                {
                    title: '源表',
                    key: 'sourceTable',
                    minWidth: 130,
                    ellipsis: true,
                    render: (h, params) => {
                        return rowRender(h, params, 'tableName');
                    }
                },
                {
                    title: '目标表',
                    key: 'targetTable',
                    minWidth: 130,
                    ellipsis: true,
                    render: (h, params) => {
                        return rowRender(h, params, 'tableName');
                    }
                },
                {
                    title: '源表记录数',
                    key: 'mainTableRecordCount',
                    minWidth: 100,
                    render: (h, params) => {
                        return rowRender(h, params, 'mainTableRecordCount');
                    }
                },
                {
                    title: '目标表记录数',
                    key: 'compareTableRecordCount',
                    minWidth: 110,
                    render: (h, params) => {
                        return rowRender(h, params, 'compareTableRecordCount');
                    }
                },
                {
                    title: '差值',
                    key: 'diffCount',
                    minWidth: 90,
                    ellipsis: true,
                    render: (h, params) => {
                        const difference = this.handleDifference(params.row.mainTableRecordCount, params.row.compareTableRecordCount);
                        return h('div', {
                            attrs: {
                                title: difference ?? '-'
                            }
                        }, difference ?? '-');
                    }
                }
            ],
            tableData: [],
            tableHeight: 300,
            errorInfo: ''
        };
    },
    mounted() {
        this.handleDrawerOpen();
    },
    methods: {
        handleDifference(val1, val2) {
            // 检查两个值是否为数字
            const isValidNumber = (num) => typeof num === 'number' && !isNaN(num);
            if (!isValidNumber(val1) || !isValidNumber(val2)) {
                return '-';
            }
            // 计算差值 - 绝对值
            return Math.abs(val1 - val2);
        },
        // 打开弹窗
        async handleDrawerOpen() {
            this.loading = true;
            if (this.modalData.ruleType === 'tableRecordCount') {
                await this.getTableCountDetail();
            }
            this.loading = false;
        },
        // 表记录总数比对详情
        async getTableCountDetail() {
            const param = {
                productId: this.productId,
                ruleId: this.modalData?.ruleId,
                execId: this.modalData?.execId
            };
            try {
                this.loading = true;
                const res = await getTableCountDetail(param);
                if (res.code === '200') {
                    this.tableData = res.data?.tables || [];
                    this.errorInfo = res.errorInfo;
                }
            } catch (err) {
                console.error(err);
            }
            this.loading = false;
        },
        // 关闭侧弹窗 清理数据
        handleDrawerClose() {
            this.modalData.status = false;
            this.columns = [];
            this.tableData = [];
        },
        // 下载对比详情
        async downloadCompareResult() {
            const fileName = this.modalData.ruleName || '';
            const time = this.modalData.startTime || this.modalData.executeTime;
            try {
                const res = await downloadCompareResult({ execId: this.modalData?.execId });
                const objUrl = window.URL.createObjectURL(new Blob([res], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' }));
                // 通过创建a标签实现
                const link = document.createElement('a');
                link.href = objUrl;
                // 对下载的文件命名
                link.setAttribute('download', `${fileName}-${time}-结果详情.xls`);
                document.body.appendChild(link);
                link.click();
                document.body.removeChild(link);
                window.URL.revokeObjectURL(objUrl);
            } catch (err) {
                console.error(err);
            } finally {
                this.handleDrawerClose();
            }
        }
    }
};
</script>

<style lang="less" scoped>
/deep/ .h-modal-body {
    padding: 10px 32px;

    p {
        margin-bottom: 5px;
    }
}

.basic-info {
    background: #f7f7f7;
    padding: 10px 15px;
    margin-bottom: 10px;
    overflow: auto;

    .h-row {
        min-height: 20px;
        display: flex;

        .col-title {
            width: 70px;
            padding-right: 5px;
            text-align: right;
        }

        .col-content {
            flex: 1;
        }
    }

    .info-label {
        color: #505050;
    }

    .info-value {
        color: #000;
    }
}

.no-data-box {
    height: 200px;

    .slot-box {
        font-size: 12px;
        color: #505050;

        span {
            color: var(--link-color);
        }

        span:hover {
            cursor: pointer;
        }
    }
}
</style>
