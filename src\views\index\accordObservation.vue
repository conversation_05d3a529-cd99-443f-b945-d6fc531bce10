<!--
 * @Description: 核心数据同步
-->
<template>
    <div class="main">
        <header>
            <a-title title="核心数据同步">
                <slot>
                    <h-select
                        v-show="productList.length > 1"
                        v-model="productInstNo"
                        class="title-single-select"
                        placeholder="请选择"
                        :positionFixed="true"
                        :clearable="false"
                        @on-change="checkProduct">
                        <h-option
                            v-for="item in productList"
                            :key="item.id"
                            :value="item.productInstNo"
                            >{{ item.productName }}</h-option>
                    </h-select>
                </slot>
            </a-title>
        </header>
        <div ref="tab-wrapper" class="tab-wrapper">
            <h-tabs
                v-model="tabName"
                :showArrow="showArrow"
                @on-click="handleTabChange(tabName)">
                <h-tab-pane
                    v-for="item in editableTabs" :key="item.name"
                    :label="item.describe"
                    :name="item.name">
                </h-tab-pane>
            </h-tabs>

            <div class="data-accord">
                <!-- 集群数据同步差量组件 -->
                <data-accord-observation
                    v-if="indicators.length"
                    ref="data-accord"
                    :curTab="tabName"
                    :productInstNo="productInstNo"
                    :indicators="indicators" />

                <!-- 无数据 -->
                <no-data v-if="!indicators.length" />

                <a-loading
                    v-if="loading"
                    style="width: 100%; height: 100%;">
                </a-loading>
            </div>
        </div>
    </div>
</template>
<script>
import _ from 'lodash';
import { mapState, mapActions } from 'vuex';
import noData from '@/components/common/noData/noData';
import aTitle from '@/components/common/title/aTitle';
import aLoading from '@/components/common/loading/aLoading';
import { getProductObservation } from '@/api/topoApi';
import { getObservableTransactionInfo } from '@/api/httpApi';
import dataAccordObservation from '@/components/accordObservation/dataAccordObservation';
const TIMER_INTERVAL = 10;
export default {
    components: { aTitle, noData, aLoading, dataAccordObservation },
    data() {
        return {
            productInstNo: '',
            loading: false,
            tabName: '',
            editableTabs: [{
                name: 'overview',
                describe: '总览'
            }],
            indicators: [],
            timer: null,
            showArrow: false
        };
    },
    async mounted() {
        await this.getProductList({ filter: 'excludeLdpApm' });
        const productInstNo = localStorage.getItem('productInstNo');
        this.productInstNo =  _.find(this.productList, ['productInstNo', productInstNo])?.productInstNo ||
            this.productList?.[0]?.productInstNo;
        window.addEventListener('resize', this.resize);
    },
    beforeDestroy() {
        this.clearPolling();
        window.removeEventListener('resize', this.resize);
    },
    computed: {
        ...mapState({
            productList: state => {
                return state.product.productListLight;
            }
        })
    },
    methods: {
        ...mapActions({ getProductList: 'product/getProductListLight' }),
        // 切换产品
        async checkProduct(productId) {
            if (!this.productList.length) return;

            this.productInfo = productId ? _.find(this.productList, ['productInstNo', productId]) : this.productList[0];
            this.productInstNo = this.productInfo.productInstNo;
            localStorage.setItem('productInstNo', this.productInfo.productInstNo);
            this.loading = true;
            // 获取展示tab页
            await this.handleShardTabs();
            this.handleTabChange();
            this.loading = false;
            // 开启定时器
            this.setPolling(TIMER_INTERVAL);
        },
        // 切换tab页
        handleTabChange(tabName) {
            this.tabName = _.find(this.editableTabs, ['name', tabName])?.name || this.editableTabs?.[0]?.name;
            this.$nextTick(() => {
                this.$refs?.['data-accord'] && this.$refs['data-accord'].init();
            });
        },
        // 获取全部集群同步数据
        async getObservableTransactionInfo() {
            const indicators = [];
            const params = {
                productId: this.productInstNo
            };
            try {
                const res = await getObservableTransactionInfo(params);
                if (res.code === '200') {
                    const list = res?.data || [];
                    this.editableTabs.forEach(item => {
                        item.appClusters.forEach(cluster => {
                            const indicator = list.find(o => o.clusterId === cluster.id);
                            if (indicator) {
                                indicators.push({
                                    ...indicator,
                                    clusterName: cluster.clusterName || '未知',
                                    shardingNo: cluster.shardingNo,
                                    serviceCode: item.name
                                });
                            }
                        });
                    });
                }
            } catch (err) {
                this.clearPolling();
            }
            return indicators;
        },
        // 处理分片维度分类-tab
        async handleShardTabs() {
            // 应用节点观测仪表盘
            const param = {
                productId: this.productInstNo,
                observationMode: 'service',
                instanceIdentity: 'bizproc'
            };
            try {
                const res = await getProductObservation(param);
                const panels = [];
                if (res.success) {
                    if (Array.isArray(res?.data?.services) && res?.data?.services?.length) {
                        res.data.services.forEach(ele => {
                            const data = {
                                name: ele.serviceCode,
                                describe: ele.serviceName || '未知',
                                appClusters: ele.appClusters || [],
                                timerSwitch: true,
                                timerInterval: 10,
                                clusterIds: ele.appClusters.map(o => o.id)
                            };
                            panels.push(data);
                        });
                    }
                }
                this.editableTabs = panels;
                this.tabName = _.find(this.editableTabs, ['name', this.tabName])?.name || this.editableTabs?.[0]?.name;
                this.indicators = await this.getObservableTransactionInfo();
                this.resize();
                // 更新子组件数据
                this.$nextTick(() => {
                    this.$refs?.['data-accord'] && this.$refs['data-accord'].getPerformanceData();
                });

            } catch (error) {
                this.clearPolling();
                console.error(error);
            };
        },
        // 监听页面宽度-判断是否展示tabs的翻页箭头
        resize() {
            // tabs估算宽度
            const tabsWidth = (this.editableTabs?.length || 1) * 80;
            const containerWidth = this.$refs['tab-wrapper']?.scrollWidth;
            this.showArrow = tabsWidth > containerWidth;
        },
        // 定时器
        setPolling(timerInterval) {
            this.clearPolling();
            this.timer = setInterval(() => {
                this.handleShardTabs();
            }, (timerInterval) * 1000);
        },
        // 清除轮询
        clearPolling() {
            this.timer && clearInterval(this.timer);
        }
    }
};
</script>

<style lang="less" scoped>
@import url("@/assets/css/tab.less");

.tab-wrapper {
    position: relative;
    width: 100%;
    height: 100%;

    /deep/ .h-tabs {
        height: 45px;
        background: var(--main-color);
    }

    /deep/ .h-tabs-bar {
        border-bottom: var(--border);
        margin-bottom: 0;
    }

    /deep/ .h-tabs-tab {
        padding: 10px 6px;
    }

    /deep/ .h-tabs-content-wrap {
        height: 0;
    }

    /deep/.h-tabs-nav-container {
        margin-bottom: -4px;
    }

    .data-accord {
        height: calc(100% - 45px);
    }
}
</style>
