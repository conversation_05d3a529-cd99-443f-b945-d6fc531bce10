<template>
  <div ref="table-selector" class="table-selector">
    <div class="bordered-card">
        <div class="bordered-card-title">
            可选表（{{ availableTables.length }}）
        </div>
        <div>
            <h-input v-model="searchAvailable" placeholder="输入表名/集群/分片搜索"></h-input>
            <a-simple-table
                showTitle
                border
                rowSelect
                :hasPage="false"
                :columns="columns"
                :height="tableHeight"
                :tableData="filteredAvailableTables"
                @selection="getAvailableSelections">
            </a-simple-table>
        </div>
    </div>
    <div class="table-transfer-buttons">
        <a-button type="dark" @click="addSelected">
            <h-icon name="ios-arrow-forward" size="18"></h-icon>
        </a-button>
        <a-button type="dark" @click="removeSelected">
            <h-icon name="ios-arrow-back" size="18"></h-icon>
        </a-button>
    </div>
    <div class="bordered-card">
        <div class="bordered-card-title">
            已选表（{{ selectedTables.length }}）
        </div>
        <div>
            <h-input v-model="searchSelected" placeholder="输入表名/集群/分片搜索"></h-input>
            <a-simple-table
                showTitle
                border
                rowSelect
                :hasPage="false"
                :columns="columns"
                :height="tableHeight"
                :tableData="filteredSelectedTables"
                @selection="getSelectedSelections">
            </a-simple-table>
        </div>
    </div>
  </div>
</template>

<script>
import aSimpleTable from '@/components/common/table/aSimpleTable';
import aButton from '@/components/common/button/aButton';

export default {
    name: 'TableSelector',
    components: {
        aSimpleTable, aButton
    },
    props: {
        // 支持的可选表数组
        supportAppearTables: {
            type: Array,
            default: () => []
        },
        // 已选表数组
        selectedAppearTables: {
            type: Array,
            default: () => []
        }
    },
    data() {
        return {
            tableHeight: 0, // 表格高度
            availableTables: [], // 可选表数据
            selectedTables: [], // 已选表数据
            availableSelections: [], // 可选表选中行数据
            selectedSelections: [], // 已选表选中行数据
            searchAvailable: '', // 可选表搜索字段
            searchSelected: '', // 已选表搜索字段
            columns: [ // 表格列配置
                {
                    type: 'selection',
                    width: 60,
                    align: 'center'
                },
                {
                    title: '表名',
                    key: 'tableName',
                    ellipsis: true,
                    minWidth: 120
                },
                {
                    title: '集群',
                    key: 'clusterName',
                    ellipsis: true,
                    minWidth: 100
                },
                {
                    title: '分片',
                    key: 'shardingNo',
                    width: 90,
                    ellipsis: true
                }
            ]
        };
    },
    watch: {
        // 监听支持的可选表数据变化，初始化availableTables
        supportAppearTables: {
            immediate: true,
            handler(newVal) {
                this.availableTables = [...newVal];
            }
        },
        // 监听已选表数据变化，初始化selectedTables
        selectedAppearTables: {
            immediate: true,
            handler(newVal) {
                this.selectedTables = [...newVal];
            }
        }
    },
    computed: {
        // 过滤可选表数据，返回搜索匹配的可选表
        filteredAvailableTables() {
            // 搜索操作改变表格行数据，需清除选中行数据
            this.availableSelections = [];

            return this.filterTables(this.availableTables, this.searchAvailable);
        },
        // 过滤已选表数据，返回搜索匹配的已选表
        filteredSelectedTables() {
            // 搜索操作改变表格行数据，需清除选中行数据
            this.selectedSelections = [];

            return this.filterTables(this.selectedTables, this.searchSelected);
        }
    },
    methods: {
        // 获取可选表选中行数据
        getAvailableSelections(rows) {
            this.availableSelections = rows;
        },
        // 获取已选表选中行数据
        getSelectedSelections(rows) {
            this.selectedSelections = rows;
        },
        // 获取表格高度
        fetTableHeight() {
            this.tableHeight = this.$refs['table-selector']?.offsetHeight - 100;
        },
        // 过滤表格数据，返回匹配搜索关键词的表
        filterTables(tables, search) {
            if (!search) return [...tables];

            return tables.filter(item =>
                item.tableName?.includes(search) ||
                item.clusterName?.includes(search) ||
                item.shardingNo?.toString()?.includes(search)
            );
        },
        // 将选中的可选表添加到已选表
        addSelected() {
            this.transferTables(this.availableSelections, this.availableTables, this.selectedTables);
            this.availableSelections = [];
        },
        // 将选中的已选表移回到可选表
        removeSelected() {
            this.transferTables(this.selectedSelections, this.selectedTables, this.availableTables);
            this.selectedSelections = [];
        },
        // 从一个表数组转移选中的表到另一个表数组
        transferTables(selections, fromTables, toTables) {
            if (!selections.length) return;
            selections.forEach(selection => {
                toTables.push(selection);
                const index = fromTables.findIndex(table =>
                    table?.tableName === selection?.tableName &&
                    table?.clusterName === selection?.clusterName &&
                    table?.shardingNo === selection?.shardingNo
                );
                if (index > -1) fromTables.splice(index, 1);
            });
            // 发出事件通知父组件表数据更新
            this.$emit('update-tables', {
                availableTables: [...this.availableTables],
                selectedTables: [...this.selectedTables]
            });
        }
    },
    mounted() {
        // 监听窗口resize事件，获取表格高度
        window.addEventListener('resize', this.fetTableHeight);
        this.$nextTick(() => {
            setTimeout(this.fetTableHeight, 300);
        });
    },
    beforeDestroy() {
        // 组件销毁前移除窗口resize事件监听
        window.removeEventListener('resize', this.fetTableHeight);
    }
};
</script>

<style lang="less" scoped>
.table-selector {
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: space-between;
    /* stylelint-disable-next-line font-family-no-missing-generic-family-keyword */
    font-family: PingFangSC-Regular;
}

.bordered-card {
    flex: 1 1 0%; // 关键：允许收缩并设置基础尺寸为0
    min-width: 0; // 关键：覆盖默认min-width:auto行为
    height: 100%;
    background: var(--wrapper-color);
    border: var(--border);
    border-radius: 4px;
    padding: 0 12px;
    flex-shrink: 0;
    flex-grow: 1;

    .bordered-card-title {
        height: 44px;
        line-height: 44px;
        font-size: 14px;
        color: var(--font-color);
        padding-left: 12px;

        &::before {
            display: inline-block;
            position: relative;
            left: -10px;
            top: 4px;
            content: "";
            width: 4px;
            height: 16px;
            background: var(--link-color);
        }
    }

    /deep/ .h-checkbox-indeterminate .h-checkbox-inner {
        &::after {
            left: 3px;
            top: 6px;
        }
    }
}

.table-transfer-buttons {
    height: 100%;
    width: 70px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    gap: 15px;
    flex-shrink: 0;
}

.table-transfer-buttons /deep/ .h-btn {
    width: 36px;
    height: 36px;
    padding: 5px;
}
</style>
