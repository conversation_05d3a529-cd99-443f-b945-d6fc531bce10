import * as echarts from 'echarts';
import { ganttOption } from '@/components/common/chart/chartConfig';
export default {
    name: 'ganttC<PERSON>',
    props: {
        chartRef: {
            type: String,
            default: 'gantt'
        },
        chartData: {
            type: Array,
            default: () => []
        }
    },
    data() {
        return {
            myChart: null,
            option: {}
        };
    },
    mounted() {
        if (this.$refs[this.chartRef]) {
            this.myChart = echarts.init(this.$refs[this.chartRef], '#262B40');
            this.initOption();
            this.resizeObserver = new ResizeObserver(() => {
                this.resize();
            });
            this.resizeObserver.observe(this.$refs[this.chartRef]);
        }
    },
    beforeDestroy() {
        this.resizeObserver && this.resizeObserver.disconnect(this.$refs[this.chartRef]);
    },
    methods: {
        initOption() {
            this.option = ganttOption;
            this.refreshData(this.chartData);
        },
        refreshData(chartData) {
            this.option.yAxis.data = chartData.map(o => { return o?.name; });
            this.option.series[0].data = chartData;
            this.myChart && this.myChart.setOption(this.option);
        },
        resize() {
            this.myChart && this.myChart.resize();
        }
    },
    watch: {
        chartData: {
            handler(newVal) {
                if (newVal) {
                    this.refreshData(newVal);
                } else {
                    this.myChart.dispose();
                }
            },
            deep: true
        }
    },
    render() {
        return (
            <div ref={this.chartRef} style="width: 100%; height: 100%"></div>
        );
    }
};
