<template>
    <div>
        <h-msg-box-safe v-model="modalData.status" title="同步配置" :closable="true" :mask-closable="false" width="550" maxHeight="350" allowCopy>
            <div class="content-body">
                <div class="header-info">
                    <h-icon name="warning_fill" color="var(--warning-color)" size=24></h-icon>
                    <span class="title-info">你确定要进行节点配置同步吗？</span>
                </div>
                <div class="obj-body">
                    <p>
                        <span class="obj-body-text">配置类型：</span>
                        <span :title="modalData.configType"  class="obj-body-value">
                            {{modalData.configType}}
                        </span>
                    </p>
                    <p>
                        <span  class="obj-body-text">源节点：</span>
                        <span :title="modalData.appInstanceName" class="obj-body-value">
                            {{modalData.appInstanceName}}
                        </span>
                    </p>
                    <p>
                        <span  class="obj-body-text">目标节点：</span>
                        <span :title="modalData.compareAppInstanceName" class="obj-body-value">
                            {{modalData.compareAppInstanceName || '-'}}
                        </span>
                    </p>
                </div>
                <div v-if="modalData.hasNoTip || modalData.type === 'locate_rules'">
                    <span class="obj-tip">! 本次修改仅对节点内存中的配置生效，重启后将重新加载本地配置文件</span>
                </div>
            </div>
            <template v-slot:footer>
                <div>
                    <a-button @click="modalData.status = false">取消</a-button>
                    <a-button type="primary" :disabled="!modalData.compareAppInstanceName" @click="submitConfig">确认</a-button>
                </div>
            </template>
        </h-msg-box-safe>
    </div>
</template>

<script>
import aButton from '@/components/common/button/aButton';
export default {
    name: 'ApmMsgBox',
    components: { aButton },
    props: {
        modalInfo: {
            type: Object,
            default: null
        }
    },
    data() {
        return {
            loading: false,
            modalData: this.modalInfo,
            configType: '',
            appInstanceName: '',
            compareAppInstanceName: ''
        };
    },
    mounted(){
        this.loading = true;
        setTimeout(() => {
            this.loading = false;
        }, 300);
    },
    methods: {
        submitConfig() {
            this.$emit('msgbox-config', this.modalData?.sourceNodes, this.modalData?.targetNodes);
            this.modalData.status = false;
        }
    }
};
</script>

<style lang="less" scoped >
.header-info {
    font-weight: 500;
    display: flex;

    & > .h-icon {
        position: relative;
        top: 2px;
    }

    .title-info {
        line-height: 36px;
        font-size: 14px;
        font-weight: 600;
        vertical-align: top;
        padding: 0 5px;
    }
}

/deep/ .h-modal-body {
    padding: 5px 32px;
}

.content-body {
    .text-body {
        font-size: 12px;
        color: #24262b;
        text-align: left;
        line-height: 20px;
        padding-left: 18px;
        position: relative;
    }

    .obj-body {
        color: var(--font-color);
        line-height: 30px;
        width: 100%;

        p {
            display: flex;

            span {
                text-align: left;
                overflow: hidden;
                white-space: nowrap;
                text-overflow: ellipsis;
            }
        }

        .obj-body-text {
            width: 100px;
            text-align: left;
        }
    }

    .obj-tip {
        font-size: 14px;
        font-weight: 600;
        color: var(--error-color);
    }
}
</style>
