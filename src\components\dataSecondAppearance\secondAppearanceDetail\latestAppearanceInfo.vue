<template>
    <div ref="table-box" class="table-box">
        <obs-table
            v-if="hasLatestTask"
            showTitle
            isSimpleTable
            border
            canDrag
            :title="tableTitle"
            :columns="columns"
            :tableData="tableData"
            :loading="tableLoading"
            :height="tableHeight">
            <template slot="extraTitleBox">
                <div class="title-slots">
                    <div class="title-content">
                        <span class="main-title">最近一次上场</span>
                        <span class="separator1"></span>

                        <span class="indicator"
                            :style="{ backgroundColor: statusIndicator.color }">
                        </span>
                        <span>{{ statusIndicator.text }}</span>
                        <span class="separator2"></span>
                        <span>
                            上场时间：{{ taskDetail.startTime || ' '}} ~ {{taskDetail.endTime || '  '}}；
                            需上场表数量 {{taskDetail.tableTotalCount !== undefined ? taskDetail.tableTotalCount : '-'}}
                            （成功 {{taskDetail.tableSuccessCount!== undefined ? taskDetail.tableSuccessCount: '-'}} /
                            失败 {{taskDetail.tableFailCount!== undefined ? taskDetail.tableFailCount: '-'}}）
                        </span>
                        <!-- 失败原因 -->
                        <h-poptip
                            v-if="execBatchStatus === 'failed'"
                            trigger="click"
                            customTransferClassName="apm-poptip monitor-poptip"
                            autoPlacement
                            :content="taskDetail.failInfo"
                            transfer>
                                <span style="color: var(--link-color); cursor: pointer;">&nbsp;&nbsp;失败原因</span>
                        </h-poptip>
                    </div>
                    <a-button
                        type="primary"
                        :disabled="createBtnDisabled"
                        @click="handleCreateTask">创建任务
                    </a-button>
                </div>
            </template>
        </obs-table>
        <apm-blank v-else name="Data">
            <slot>
                <div>暂无数据</div>
                <br />
                <a-button
                    type="primary"
                    @click="handleCreateTask">
                    创建任务
                </a-button>
            </slot>
        </apm-blank>
    </div>
</template>

<script>
import obsTable from '@/components/common/obsTable/obsTable';
import aButton from '@/components/common/button/aButton';
import apmBlank from '@/components/common/apmBlank/apmBlank';
import importStatusTableIcon from '@/components/secondAppearance/importStatusTableIcon.vue';
import { getLatestAppearData } from '@/api/brokerApi';
import { STATUS_COLOR_MAP, LOADING_TYPE_OPTIONS } from '@/components/dataSecondAppearance/constant.js';

export default {
    name: 'LatestAppearanceInfo',
    components: {
        obsTable, aButton, apmBlank
    },
    props: {
        productId: {
            type: String,
            default: ''
        }
    },
    data() {
        return {
            hasLatestTask: false,
            // 上场任务详情
            execBatchStatus: '',
            taskDetail: {
                startTime: '',
                endTime: '',
                tableTotalCount: '',
                tableSuccessCount: '',
                tableFailCount: '',
                failInfo: ''
            },

            // 表格
            tableLoading: false,
            tableHeight: 300,
            tableTitle: {},
            columns: [
                {
                    title: '表名',
                    key: 'tableName',
                    sortable: true,
                    ellipsis: true
                },
                {
                    title: '集群',
                    key: 'clusterName',
                    ellipsis: true,
                    sortable: true
                },
                {
                    title: '分片',
                    key: 'shardingNo',
                    ellipsis: true,
                    sortable: true
                },
                {
                    title: '加载方式',
                    key: 'importMode',
                    ellipsis: true,
                    render: (_, params) => {
                        const importMode = LOADING_TYPE_OPTIONS.find(o => o.value === params.row.importMode)?.label || '';
                        return <div class="h-table-cell-ellipsis" title={importMode}>
                            {importMode}
                        </div>;
                    }
                },
                {
                    title: '上场规则',
                    key: 'importSql',
                    ellipsis: true
                },
                {
                    title: '上场状态和结果',
                    minWidth: 180,
                    ellipsis: true,
                    render: (_, { row }) => {
                        let iconType, text;
                        switch (row.importStatus) {
                            case 'running':
                                iconType = 'loading';
                                text = '上场中';
                                break;
                            case 'succeeded':
                                iconType = 'success';
                                text = `记录总数 ${row.recordTotalCount}（成功 ${row.recordSuccessCount} / 失败 ${row.recordFailCount}）`;
                                break;
                            case 'failed':
                                iconType = 'error';
                                break;
                            case 'pending':
                                iconType = 'offline';
                                text = '待上场';
                                break;
                        }
                        return <div title={ text } class="h-table-cell-ellipsis">
                            <importStatusTableIcon type={iconType}/>
                            { text }
                            { row.importStatus === 'failed' && row.failInfo
                                ? <h-poptip
                                    customTransferClassName='apm-poptip'
                                    transfer
                                    autoPlacement
                                    content={row.failInfo}
                                    trigger="click">
                                    <span class="click-text hover-underline">错误信息</span>
                                </h-poptip>
                                : '' }
                        </div>;
                    }
                }
            ],
            tableData: []
        };
    },
    computed: {
        statusIndicator() {
            return STATUS_COLOR_MAP[this.execBatchStatus] || { color: '', text: '' };
        },
        // 上场任务执行前或执行中，禁止创建任务
        createBtnDisabled() {
            return ['pending', 'running'].includes(this.execBatchStatus);
        }
    },
    async mounted() {
        window.addEventListener('resize', this.fetTableHeight);
        this.fetTableHeight();
        // 开启定时器
        this.handleSetInterval();
    },
    beforeDestroy() {
        // 清除定时器和窗口大小监听事件
        if (this.timer) {
            clearInterval(this.timer);
            this.timer = null;
        }
        window.removeEventListener('resize', this.fetTableHeight);
    },
    methods: {
        /**
        * @description 初始化数据，设置 loading 状态，调用查询数据接口
        */
        async initData() {
            this.tableLoading = true;
            try {
                await this.queryImportDataStatus();
            } catch (err) {
                console.error(err);
            } finally {
                this.tableLoading = false;
            }
        },
        /**
        * @description 定时查询上场状态并更新UI
        *
        * 该方法会每隔5秒调用后端接口查询当前上场状态，并根据状态更新UI界面。
        * 用于在“pending”（待上场）或“running”（运行中）状态时，不断刷新当前状态信息。
        */
        handleSetInterval() {
            // 每隔 5s 调用后端接口查询上场状态,并更新 UI 界面
            this.timer = setInterval(async () => {
                if (['pending', 'running'].includes(this.execBatchStatus)) {
                    await this.queryImportDataStatus();
                }
            }, 5000);
        },
        /**
        * @description 调用接口，查询数据上场状态，更新任务详情
        */
        async queryImportDataStatus() {
            let hasLatestTask = false;
            const params = { productId: this.productId };
            try {
                const res = await getLatestAppearData(params);
                if (res.code === '200') {
                    const data = res?.data;
                    if (data) {
                        hasLatestTask = true;
                        const {
                            importStatus,
                            startTime,
                            endTime,
                            tableTotalCount,
                            tableSuccessCount,
                            tableFailCount,
                            failInfo,
                            tables
                        } = data;
                        this.execBatchStatus = importStatus;
                        this.taskDetail = { startTime, endTime, tableTotalCount, tableSuccessCount, tableFailCount, failInfo };
                        this.tableData = tables || [];
                    } else {
                        this.taskDetail = {};
                    }
                } else {
                    this.handleErrorState(res.message);
                }
            } catch (e) {
                this.handleErrorState();
            } finally {
                this.hasLatestTask = hasLatestTask;
            }
        },
        /**
        * @description 错误状态处理函数
        * @param {String} message - 错误信息
        */
        handleErrorState(message) {
            this.taskDetail = {};
            this.tableData = [];
            message && this.$hMessage.error(message);
            if (this.timer) {
                clearInterval(this.timer);
                this.timer = null;
            }
        },
        /**
        * @description 动态设置表格高度的处理函数
        */
        fetTableHeight() {
            this.tableHeight = this.$refs['table-box']?.offsetHeight - 60;
        },
        /**
        * @description 页面跳转函数，触发创建任务事件
        */
        handleCreateTask() {
            this.$emit('page-jump', 'create');
        }

    }
};
</script>
<style lang="less">
@import url("@/assets/css/poptip-1.less");

.apm-poptip .h-poptip-inner .h-poptip-body {
    .h-poptip-body-content {
        max-width: 400px;
        max-height: 200px;
        white-space: normal;
        word-break: break-all;
    }

    .h-poptip-body-content-inner {
        color: var(--font-color);
    }
}

.hover-underline:hover {
    text-decoration: underline;
    text-decoration-color: #2d8de5;
}
</style>

<style lang="less" scoped>
/* 组件样式 */
.table-box {
    height: calc(100% - 15px);
    padding: 0 5px;
}

.title-slots {
    display: inline-flex;
    align-items: center;
    justify-content: space-between;
    width: calc(100% - 30px);
    font-size: 12px;
    margin-left: 10px;

    .title-content {
        display: inline-flex;
        align-items: center;
        justify-content: center;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
    }

    .main-title {
        font-size: 14px;
    }

    .indicator {
        width: 6px;
        height: 6px;
        border-radius: 50%;
        margin-right: 9px;
    }

    .separator1 {
        border-left: 1px solid #474e6f;
        height: 20px;
        margin: 0 15px 0 12px;
    }

    .separator2 {
        border-left: 1px solid #474e6f;
        height: 12px;
        margin: 0 12px;
    }
}

.click-text {
    color: var(--link-color);

    &:hover {
        cursor: pointer;
    }
}
</style>
