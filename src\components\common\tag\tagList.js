import './tagList.less';
import aTag from '@/components/common/tag/aTag';
export default {
    name: 'tag-list',
    props: {
        queryList: {
            type: Array,
            default: function() {
                return [];
            }
        },
        keyName: {
            type: String,
            default: 'id'
        },
        selectedId: {
            type: String,
            default: ''
        },
        closable: {
            type: Boolean,
            default: true
        },
        tagSize: {
            type: String,
            default: 'normal' // 选项 normal, small
        }
    },
    components: { aTag },
    data() {
        return {
            canLeftScorll: false,
            canRightScorll: false
        };
    },
    mounted() {
        window.addEventListener('resize', this.resize);
    },
    methods: {
        resize() {
            this.queryList?.length && this.scrollReset();
        },
        handleClose(id) {
            this.$emit('on-close', id);
        },
        handleClick(id) {
            this.$emit('on-click', id);
        },
        // 左滑动逻辑
        scrollLeft() {
            const allLength = this.$refs['apm-list'].scrollWidth;
            const boxLength = this.$refs['apm-list-box'].clientWidth;
            if (allLength < boxLength) return;
            const listEl = this.$refs['apm-list'];
            // eslint-disable-next-line radix
            const leftMove = Math.abs(parseInt(window.getComputedStyle(listEl, null)?.left));
            if (leftMove + boxLength - 200 < boxLength) {
                // 到最开始的时候
                listEl.style.left = '0px';
                this.canLeftScorll = false;
                this.canRightScorll = true;
            } else {
                listEl.style.left = '-' + (leftMove - 200) + 'px';
                this.canLeftScorll = true;
                this.canRightScorll = true;
            }
        },
        // 右滑动逻辑
        scrollRight() {
            const allLength = this.$refs['apm-list'].scrollWidth;
            const boxLength = this.$refs['apm-list-box'].clientWidth;
            if (allLength < boxLength) return;

            const listEl = this.$refs['apm-list'];
            // eslint-disable-next-line radix
            const leftMove = Math.abs(parseInt(window.getComputedStyle(listEl, null)?.left));
            if (leftMove + boxLength + 200 > allLength) {
                listEl.style.left = '-' + (allLength - boxLength) + 'px';
                this.canRightScorll = false;
                this.canLeftScorll = true;
            } else {
                listEl.style.left = '-' + (leftMove + 200) + 'px';
                this.canRightScorll = true;
                this.canLeftScorll = true;
            }
        },
        // 滚到最开始的位置
        scrollReset(){
            if (!Array.isArray(this.queryList) || !this.queryList.length) return;
            const allLength = this.$refs['apm-list'].scrollWidth;
            const boxLength = this.$refs['apm-list-box'].clientWidth;
            if (allLength < boxLength) {
                this.canLeftScorll = false;
                this.canRightScorll = false;
            } else {
                this.canLeftScorll = false;
                this.canRightScorll = true;
            }
            const listEl = this.$refs['apm-list'];
            // 到最开始的时候
            listEl.style.left = '0px';
        },
        generateTagList() {
            return this.queryList?.length ? (<div class="monitor-list">
                <h-icon name="android-arrow-drople1" size={22} v-on:on-click={this.scrollLeft} class={this.canLeftScorll ? 'icon' : 'disabled-icon'}></h-icon>
                <div id="apm-list-box" ref="apm-list-box" class="list-box">
                    <div ref="apm-list" class="list">
                        {
                            this.queryList.map(list => {
                                return <a-tag
                                    color={this.selectedId === list[this.keyName] ? 'blue' : 'var(--box-color)'}
                                    closable={list.hasOwnProperty('closable') ? list.closable : this.closable}
                                    id={list[this.keyName]}
                                    size={this.tagSize}
                                    name={list.name || list.instanceName}
                                    v-on:on-close={this.handleClose}
                                    v-on:on-click={this.handleClick}
                                ></a-tag>;
                            })
                        }
                    </div>
                </div>
                <h-icon name="android-arrow-dropri" size={22} v-on:on-click={this.scrollRight} class={this.canRightScorll ? 'icon' : 'disabled-icon'}></h-icon>
            </div>) : null;
        }
    },
    render() {
        return this.generateTagList();
    },
    beforeDestroy() {
        window.removeEventListener('resize', this.resize);
    }
};
