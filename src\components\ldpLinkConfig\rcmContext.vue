<template>
    <div ref="tab-box" class="tab-box">
        <!-- 上下文列表 -->
        <obs-table
            ref="table"
            :title="tableTitle"
            :tableData="tableData"
            :columns="columns"
            :loading="loading"
            :height="tableHeight"
            :showSizer="true"
            :hasPage="true"
            showTitle
            showTotal
            :total="total"
            @query="queryRcmContext"
            @select-change="handleSelectChange" />
    </div>
</template>

<script>
import obsTable from '@/components/common/obsTable/obsTable';
import { getRcmInstanceList, queryContext } from '@/api/rcmApi';

export default {
    name: 'RcmContext',
    components: { obsTable },
    props: {
        productInfo: {
            type: Object,
            default: () => {}
        }
    },
    data() {
        return {
            tableTitle: {
                label: '上下文列表',
                slots: [
                    {
                        key: 'rcmId',
                        type: 'select',
                        defaultValue: '',
                        options: []
                    }
                ]
            },
            tableData: [],
            columns: [
                {
                    title: 'ID',
                    key: 'id',
                    width: 100,
                    ellipsis: true
                },
                {
                    title: '上下文名称',
                    key: 'name',
                    ellipsis: true
                },
                {
                    title: '模式',
                    key: 'mode',
                    ellipsis: true
                },
                {
                    title: '发送主题',
                    key: 'txTopics',
                    render: (h, params) => {
                        const txTopics = [];
                        for (const txTopic of params.row.txTopics) {
                            txTopics.push(txTopic.topic + ' [' + txTopic.partition + ']');
                        }
                        return h(
                            'div',
                            {
                                class: 'h-table-cell-ellipsis',
                                attrs: {
                                    title: txTopics?.join(',') || '-'
                                }
                            },
                            txTopics?.join(',') || '-'
                        );
                    }
                },
                {
                    title: '接收主题',
                    key: 'rxTopics',
                    render: (h, params) => {
                        const rxTopics = [];
                        for (const rxTopic of params.row.rxTopics) {
                            rxTopics.push(rxTopic.topic + ' [' + rxTopic.partition + ']');
                        }
                        return h(
                            'div',
                            {
                                class: 'h-table-cell-ellipsis',
                                attrs: {
                                    title: rxTopics?.join(',') || '-'
                                }
                            },
                            rxTopics?.join(',') || '-'
                        );
                    }
                },
                {
                    title: '引用模板',
                    key: 'ref',
                    ellipsis: true,
                    render: (h, params) => {
                        return h('span', [params.row.ref || '-']);
                    }
                },
                {
                    title: '标签',
                    key: 'tags',
                    ellipsis: true,
                    render: (h, params) => {
                        return h('span', [params.row.tags?.join(',') || '-']);
                    }
                }
            ],
            total: 0,
            tableHeight: 0,
            loading: '',

            rcmIdList: [],
            rcmId: ''
        };
    },
    mounted() {
        this.fetTableHeight();
        window.addEventListener('resize', this.fetTableHeight);
    },
    beforeDestroy() {
        window.removeEventListener('resize', this.fetTableHeight);
    },
    methods: {
        // 设置table高度
        fetTableHeight() {
            this.tableHeight = this.$refs['tab-box']?.offsetHeight - 102;
        },
        async initData() {
            this.clearPageData();
            this.loading = true;
            try {
                await this.getRcmIdList();
            } finally {
                this.loading = false;
            }

            this.fetTableHeight();
        },
        // 重置表格数据
        clearPageData() {
            this.page = 1;
            this.total = 0;
            this.tableData = [];
        },
        // 获取rcm配置列表
        async getRcmIdList() {
            let rcmIdList = [];
            try {
                const res = await getRcmInstanceList({
                    productId: this.productInfo?.id
                });
                if (res.code === '200') {
                    rcmIdList = (res?.data || []).map(item => {
                        return {
                            value: item?.id,
                            label: item?.name
                        };
                    });
                }
            } catch (err) {
                console.error(err);
            }
            this.tableTitle.slots[0].options = rcmIdList;
            this.$refs['table'].setSelectVal('rcmId', rcmIdList?.[0]?.value);
        },
        handleSelectChange(value) {
            this.clearPageData();
            this.rcmId = value;
            this.queryRcmContext();
        },
        // 获取上下文列表
        async queryRcmContext() {
            try {
                const param = {
                    rcmId: this.rcmId,
                    ...this.$refs['table']?.getPageData()
                };
                this.loading = true;
                const res = await queryContext(param);
                this.loading = false;
                if (res.code === '200') {
                    this.tableData = res.data.list || [];
                    this.total = res.data.totalCount;
                } else if (res.code.length === 8) {
                    this.$hMessage.error(res.message);
                }
            } catch (err) {
                this.loading = false;
                this.$hMessage.error(err.message);
                this.tableData = [];
                this.total = 0;
            }
        }
    }
};
</script>

<style lang="less" scoped>
.tab-box {
    position: relative;
    width: 100%;
    height: calc(100% - 15px);
}
</style>
