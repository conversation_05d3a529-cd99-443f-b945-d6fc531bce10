<template>
    <div class="diff-result-box">
        <div class="diff-table" :style="[{height: visable ? '212px' : '0px'}]">
            <obs-title :title="obsTitle"  />
            <a-button  class="setting-btn" type="dark" :disabled="loading || !tableData.length" @click="onSelectDiff">一键修正</a-button>
            <u-table
                :border="true"
                :canDrag="true"
                :showTitle="true"
                :height="150"
                :columns="columns"
                :data="tableData"
                :span-method="handleSpan"
                :loading="loading"
            ></u-table>
        </div>
        <h-row>
            <div class="x-resizer"></div>
            <span class="cate-switch" @click="changeShowHidden">
                <h-icon :name="visable ? 'packup' : 'unfold'" size='14' color="#cacfd4"></h-icon>
            </span>
        </h-row>
        <node-diff ref="nodeDiff" class="node-diff"/>
         <!-- 确认是否配置同步 -->
         <sync-config-modal v-if="selectDiffModal.status" :modalInfo="selectDiffModal" @msgbox-config="syncConfigModal"></sync-config-modal>
         <!-- 同步  新的结果弹窗 -->
         <terminate-requests-modal v-if="syncConfigResultInfo.status" :modalInfo="syncConfigResultInfo" @refresh="syncConfigDiff"></terminate-requests-modal>
    </div>
</template>

<script>
import importStatusTableIcon from '@/components/secondAppearance/importStatusTableIcon.vue';
import aButton from '@/components/common/button/aButton';
import obsTitle from '@/components/common/title/obsTitle';
import nodeDiff from '@/components/locateConfig/nodeDiff';
import terminateRequestsModal from '@/components/locateConfig/modal/terminateRequestsModal';
import syncConfigModal from '@/components/locateConfig/modal/syncConfigModal';
import { setConfigLocateCompare } from '@/api/locateApi';
import { LOCATETYPE } from './constant';
export default {
    name: 'DiffResult',
    props: {
        productId: {
            type: String,
            default: ''
        }
    },
    data() {
        return {
            visable: true,
            loading: false,
            obsTitle: {
                label: '配置对比'
            },
            columns: [
                {
                    title: '配置文件类型',
                    key: 'configType',
                    ellipsis: true,
                    render: (h, params) => {
                        const type = LOCATETYPE.find(o => o.value ===  params?.row?.configType)?.label;
                        return h('span', {
                            attrs: {
                                title: type
                            }
                        }, type);
                    }
                },
                {
                    title: '源节点',
                    key: 'appInstanceName',
                    ellipsis: true
                },
                {
                    title: 'MD5',
                    key: 'md5',
                    ellipsis: true,
                    render: (h, params) => {
                        return h('span', {
                            attrs: {
                                title: params?.row?.md5 || '-'
                            }
                        }, params?.row?.md5 || '-');
                    }
                },
                {
                    title: '目标节点',
                    key: 'compareAppInstanceName',
                    ellipsis: true
                },
                {
                    title: 'MD5',
                    key: 'compareMd5',
                    ellipsis: true,
                    render: (h, params) => {
                        return h('span', {
                            attrs: {
                                title: params?.row?.compareMd5 || '-'
                            }
                        }, params?.row?.compareMd5 || '-');
                    }
                },
                {
                    title: '是否一致',
                    key: 'hasConsistent',
                    ellipsis: true,
                    width: 110,
                    render: (h, params) => {
                        const type = params?.row?.compareMd5 ? params?.row?.hasConsistent ? 'success' : 'error' : '';
                        return <div>
                            {params?.row?.compareMd5 ? <importStatusTableIcon type={type}/> : '-'}
                        </div>;
                    }
                },
                {
                    title: '操作',
                    key: 'action',
                    width: 110,
                    render: (h, params) => {
                        return h('div', [
                            h(
                                'Button',
                                {
                                    props: {
                                        type: 'text',
                                        size: 'small'
                                    },
                                    on: {
                                        click: () => {
                                            this.viewDiff(params.row);
                                        }
                                    }
                                },
                                '查看'
                            )
                        ]);
                    }
                }
            ],
            tableData: [],
            mergeArr: [],
            historyCompareParms: {},
            curRowId: '',
            selectDiffModal: {
                status: false
            },
            configSourceType: {
                locate_config: 'local',
                locate_rules: 'zookeeper'
            },
            tabType: '',
            syncConfigResultInfo: {
                status: false
            }
        };
    },
    watch: {
        productId() {
            this.visable = true;
            this.clearData();
            this.curRowId = '';
            this.historyCompareParms = {};
        }
    },
    methods: {
        // 列表显示隐藏
        changeShowHidden() {
            this.visable = !this.visable;
        },
        // 对比结果---更新使用上一次的查询对比的参数
        async initData(type, params){
            this.loading = true;
            if (!type) this.clearData();
            if (type === 'init') {
                this.historyCompareParms = params;
                this.$refs['nodeDiff'] && this.$refs['nodeDiff'].clearData();
            }
            try {
                const res = await setConfigLocateCompare(this.historyCompareParms);
                if (res.code === '200'){
                    this.tableData = res?.data || [];
                    this.mergeArr = this.getMergeParam(this.tableData, ['configType', 'appInstanceName', 'md5']);
                } else {
                    this.tableData = [];
                    this.mergeArr = [];
                }
            } catch (e){
                this.tableData = [];
                this.mergeArr = [];
                this.loading = false;
            }
            const row = this.tableData.filter(o => (o?.appInstanceId + o?.compareAppInstanceId) === this.curRowId)?.[0] || this.tableData?.[0] || {};
            this.viewDiff(row);
            this.loading = false;
        },
        // 清空数据
        clearData(){
            this.tableData = [];
            this.mergeArr = [];
            this.$refs['nodeDiff'] && this.$refs['nodeDiff'].clearData();
        },
        // 获取表格合并参数
        getMergeParam(tableData, keyArr) {
            // 拼接判断项
            let keyVal = keyArr.map(item => tableData?.[0]?.[item]).join();
            let count = 1;
            const result = [0];
            for (let i = 1; i < tableData.length; i++) {
                const iKeyVal = keyArr.map(item => tableData?.[i]?.[item]).join();
                if (iKeyVal === keyVal) {
                    count++;
                } else {
                    result.push(count);
                    result.push(i);
                    keyVal = iKeyVal;
                    count = 1;
                }
            }
            result.push(count);
            return result;
        },
        // 表格合并回调
        handleSpan({ row, column, rowIndex, columnIndex }){
            if ([0, 1, 2].includes(columnIndex)) {
                const mergeArr = this.mergeArr || [];
                for (let i = 0; i < mergeArr.length; i += 2) {
                    if (mergeArr[i + 1] > 1) {
                        if (rowIndex === mergeArr[i]) {
                            return [mergeArr[i + 1], 1];
                        } else if (rowIndex < mergeArr[i] + mergeArr[i + 1]) {
                            return [0, 0];
                        }
                    } else if (rowIndex === mergeArr[i]) {
                        return [1, 1];
                    }
                }
            }
        },
        // 查看diff
        viewDiff(row){
            this.curRowId = row?.appInstanceId + row?.compareAppInstanceId;
            this.$refs['nodeDiff'] && this.$refs['nodeDiff'].initData(row);
        },
        // 打开配置对比
        onSelectDiff() {
            this.selectDiffModal.status = true;
            this.selectDiffModal.hasNoTip = false;
            this.selectDiffModal.configType =  LOCATETYPE.find(o => o.value === this.tableData?.[0]?.configType)?.label;
            this.selectDiffModal.appInstanceName =  this.tableData?.[0]?.appInstanceName;
            this.selectDiffModal.compareAppInstanceName = (this.tableData.filter(v => !v?.hasConsistent && v?.compareMd5 && v?.md5) || []).map(o => o?.compareAppInstanceName)?.join(',');
            this.selectDiffModal.targetNodes = [...this.tableData];
        },
        // 同步对比结果
        syncConfigModal(sourceNodes, targetNodes){
            this.syncConfigResultInfo.status = true;
            this.syncConfigResultInfo.tableData = [...targetNodes].filter(v => !v?.hasConsistent).map(o => {
                return {
                    appInstanceId: o?.appInstanceId,
                    appInstanceName: o?.appInstanceName,
                    id: o?.id,
                    compareId: o?.compareId,
                    compareAppInstanceId: o?.compareAppInstanceId,
                    compareAppInstanceName: o?.compareAppInstanceName,
                    // 保存接口所需数据
                    configSourceType: o?.compareConfigSourceType,
                    configContext: o?.configContext,
                    status: 0
                };
            });
        },
        // 配置同步
        async syncConfigDiff(){
            setTimeout(async () => {
                await this.initData('reload');
            }, 500);
        }
    },
    components: { obsTitle, nodeDiff, syncConfigModal, aButton, terminateRequestsModal }
};
</script>
<style lang="less" scoped>
@import url("@/assets/css/uTable.less");

.x-resizer {
    position: absolute;
    left: 0;
    right: 0;
    top: 0;
    height: 1px;
    user-select: none;
    background-color: var(--base-color);

    &:hover {
        background-color: var(--base-color);
    }
}

.cate-switch {
    position: absolute;
    width: 44px;
    height: 12px;
    left: calc(50% - 21px);
    top: 2px;
    line-height: 12px;
    text-align: center;
    background: var(--base-color);
    border-radius: 0;
    z-index: 4;
    cursor: pointer;
    transform: perspective(0.5em) rotateX(-10deg);

    &:hover {
        background: rgba(209, 216, 229, 0.4);
    }
}

.diff-result-box {
    display: flex;
    flex-direction: column;
    height: 100%;
}

.obs-table {
    height: auto;
    margin-top: 10px;
}

.diff-table {
    position: relative;
    background-color: var(--wrapper-color);
    overflow-y: hidden;
    transition: height 1s;

    .setting-btn {
        position: absolute;
        right: 10px;
        top: 8px;
    }
}

.node-diff {
    flex: 1;
    overflow: auto;
    margin-top: 15px;
}

/deep/ .u-table td,
.u-table th.u-table-head-border-bold {
    border-bottom: 1px solid var(--monitor-color);
}
</style>
