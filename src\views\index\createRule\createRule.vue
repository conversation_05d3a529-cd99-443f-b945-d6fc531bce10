<template>
    <div class="main create-rule-container">
        <div class="title">
            <a-title
                :title="null"
                :class="'apm-title-create'">
                <template v-slot>
                    <div
                        class="create-title">
                        <h-icon
                            name="arrow-left-c"
                            @on-click="() => goBack(true)">
                        </h-icon>
                        <span>创建监控规则-自定义SQL</span>
                    </div>
                    <div
                        class="product-select">
                        <h-select
                            v-model="productInstNo"
                            placeholder="请选择"
                            placement="bottom"
                            :positionFixed="true"
                            disabled
                            :clearable="false"
                        >
                            <h-option
                                v-for="item in productList"
                                :key="item.id"
                                :value="item.productInstNo">
                                {{ item.productName }}
                            </h-option>
                        </h-select>
                    </div>
                </template>
            </a-title>
        </div>
        <div class="create-rule">
            <div class="create-rule-step">
                <h-steps :current="current">
                    <h-step title="获取监控指标"></h-step>
                    <h-step status="wait" title="配置规则内容"></h-step>
                </h-steps>
            </div>
            <div class="create-rule-wrap">
                <div v-show="current === 0" class="create-rule-content">
                    <div class="create-rule-content-tips">
                        可直接选择核心并填写对应需执行的SQL确定比对数据。另可通过“预执行SQL”得到变量后将变量作为执行SQL的入参以匹配不同使用场景。“可引用变量”中含内置变量“lastmin(num,format)”可在执行SQL中引用。另需注意：此处执行SQL在提交时不做SQL语法校验，若后续执行有误，需删除规则重新创建；约定策略见下图。
                    </div>
                    <h-form
                        ref="step1Form"
                        class="create-rule-content-form"
                        :model="step1FormData"
                        :labelWidth="103"
                    >
                        <h-form-item label="预执行内容" prop="resource" required>
                            <h-radio-group v-model="step1FormData.resource">
                                <h-radio label="none">无</h-radio>
                                <h-radio label="run">预执行SQL</h-radio>
                            </h-radio-group>
                        </h-form-item>
                        <div v-if="step1FormData.resource === 'run'" class="create-rule-content-form-pre">
                            <div class="create-rule-content-form-pre-title">
                                预执行SQL
                            </div>
                            <h-alert class="create-rule-content-form-pre-alert">
                                <h-icon name="ios-information-outl" />
                                “预执行SQL”执行后可得到对应变量，变量可在下方“执行SQL”被引用。点击
                                <a :href="fileUrl" download="SQL语法说明.md">
                                    下载SQL语法指南
                                </a>
                            </h-alert>
                            <h-form-item prop="runCore" label="核心" placeholder="请选择核心">
                                <h-select v-model="step1FormData.runCore" :clearable="false" style="width: 300px;">
                                    <h-option v-for="item in cores" :key="item.id" :value="item.id">
                                        {{item.instanceNo}}
                                    </h-option>
                                </h-select>
                            </h-form-item>
                            <h-form-item prop="runSql" label="SQL">
                                <h-input
                                    v-model="step1FormData.runSql"
                                    :maxlength="500"
                                    :placeholder="preRunSqlPlaceHolder"
                                    type="textarea"
                                 />
                            </h-form-item>
                            <div class="create-rule-content-form-pre-btn">
                                <a-button :disabled="getVarBtnDisabled" :loading="varLoading" type="dark" @click="getVar">获取变量</a-button>
                                <span class="create-rule-content-form-pre-btn-tip">点击“获取变量”，生成对应可引用变量，在下方显示</span>
                            </div>
                        </div>
                        <div class="create-rule-content-form-var">
                            <div class="create-rule-content-form-var-label h-form-item-label">
                                <h-poptip customTransferClassName="apm-poptip create-rule-poptip" placement="top-start" transfer trigger="hover" positionFixed>
                                    <h-icon name="ios-information" />
                                    <div slot="content" style="width: 243px; white-space: pre-wrap; color: #fff;" class="create-rule-content-form-value-label-tip">{{varTip}}
                                    </div>
                                </h-poptip>
                                可引用变量</div>
                            <div class="create-rule-content-form-var-list">
                                <div v-for="item in varList" :key="item.value" class="create-rule-content-form-var-list-item">
                                    <h-poptip v-if="item.desc" customTransferClassName="apm-poptip create-rule-poptip" placement="top-start" transfer trigger="hover" positionFixed>
                                        {{item.value}}
                                        <div slot="content" style="width: 243px; white-space: pre-wrap; color: #fff;" class="create-rule-content-form-value-label-tip">{{item.desc}}
                                        </div>
                                    </h-poptip>
                                   <template v-else> {{item.value}} </template>
                                </div>
                            </div>
                        </div>
                        <div class="create-rule-content-form-run">
                            <div class="create-rule-content-form-run-label h-form-item-required">
                                <span class="h-form-item-label" style="width: 103px;">
                                    正式执行内容
                                </span>
                            </div>
                            <div class="create-rule-content-form-run-container">
                                <div class="create-rule-content-form-run-container-download">
                                    <a :href="fileUrl" download="SQL语法说明.md">
                                        <h-icon name="t-b-download"></h-icon>
                                        SQL语法指南
                                    </a>
                                </div>
                                <div class="create-rule-content-form-run-wrap">
                                    <h-alert class="create-rule-content-form-run-wrap-alert">
                                        <h-icon name="ios-information-outl" />
                                        统一策略：取结果集的第一行第一列，结果集必须是数字
                                    </h-alert>
                                <div class="create-rule-content-form-run-item">
                                    <div class="create-rule-content-form-run-item-left">
                                        <div class="create-rule-content-form-run-item-left-title">
                                            <div class="create-rule-content-form-run-item-left-title-label">SQL结果</div>
                                            <div class="create-rule-content-form-run-item-left-title-input">
                                                <h-form-item :label="null" prop="leftMetricName" required :valid-rules="metricNameRule" class="no-label">
                                                    <h-input v-model="step1FormData.leftMetricName" clearable :maxlength="20" placeholder="请输入指标名" />
                                                </h-form-item>
                                            </div>
                                        </div>
                                        <h-form-item prop="leftCore" label="核心" placeholder="请选择核心" required>
                                            <h-select v-model="step1FormData.leftCore" :clearable="false">
                                                <h-option v-for="item in leftCores" :key="item.id" :value="item.id">
                                                    {{item.instanceNo}}
                                                </h-option>
                                            </h-select>
                                        </h-form-item>
                                        <h-form-item
                                            prop="leftSql"
                                            label="执行SQL"
                                            required
                                            :valid-rules="sqlRule"
                                        >
                                            <h-input v-model="step1FormData.leftSql" :placeholder="runSqlPlaceholder" type="textarea" />
                                        </h-form-item>
                                        <div class="create-rule-content-form-run-item-test">
                                            <a-button :disabled="leftTestDisabled" :loading="leftTestLoading" type="dark" @click="() => testSql('left')">测试</a-button>
                                        </div>
                                        <h-form-item prop="leftTestResult" label="测试结果" style="margin-bottom: 0;">
                                            <h-input v-model="step1FormData.leftTestResult" class="create-rule-content-form-run-item-test-result" disabled placeholder="暂无结果" type="textarea" :maxlength="50" />
                                        </h-form-item>
                                    </div>

                                    <div class="create-rule-content-form-run-item-right">
                                        <div class="create-rule-content-form-run-item-right-title">
                                            <div class="create-rule-content-form-run-item-right-title-label">SQL结果</div>
                                            <div class="create-rule-content-form-run-item-right-title-input">
                                                <h-form-item :label="null" prop="rightMetricName" :valid-rules="metricNameRule" required class="no-label">
                                                    <h-input v-model="step1FormData.rightMetricName" clearable :maxlength="20" placeholder="请输入指标名" />
                                                </h-form-item>
                                            </div>
                                        </div>
                                        <h-form-item prop="rightCore" label="核心" required>
                                            <h-select v-model="step1FormData.rightCore" :clearable="false" placeholder="请选择核心">
                                                <h-option v-for="item in rightCores" :key="item.id" :value="item.id">
                                                    {{item.instanceNo}}
                                                </h-option>
                                            </h-select>
                                        </h-form-item>
                                        <h-form-item
                                            prop="rightSql"
                                            label="执行SQL"
                                            required
                                            :valid-rules="sqlRule"
                                        >
                                            <h-input
                                                v-model="step1FormData.rightSql"
                                                type="textarea"
                                                :placeholder="runSqlPlaceholder"
                                            />
                                        </h-form-item>
                                        <div class="create-rule-content-form-run-item-test">
                                            <a-button :disabled="rightTestDisabled" :loading="rightTestLoading" type="dark" @click="() => testSql('right')">测试</a-button>
                                        </div>
                                        <h-form-item prop="rightTestDisabled" label="测试结果" style="margin-bottom: 0;">
                                            <h-input v-model="step1FormData.rightTestResult" class="create-rule-content-form-run-item-test-result" disabled placeholder="暂无结果" type="textarea" :maxlength="50" />
                                        </h-form-item>
                                    </div>
                                </div>

                                </div>
                            </div>
                        </div>
                    </h-form>
                </div>

                <div v-show="current === 1" class="create-rule-content" data-step2>
                    <div class="create-rule-content-tips">
                        针对上一步获取的比对数据所形成的告警指标，设置对应的告警阈值。
                    </div>
                    <threshold-config ref="configRef" showLabel />
                    <h-form
                        ref="step2Form"
                        class="create-rule-content-form form-border"
                        :model="step2FormData"
                        :labelWidth="103"
                    >
                        <h-form-item
                            prop="ruleName"
                            label="规则名称"
                            required
                        >
                            <h-input
                                v-model="step2FormData.ruleName"
                                style="width: 415px;"
                                :maxlength="20"
                                clearable
                                placeholder="请输入规则名（不超过20字符）"
                            />
                        </h-form-item>
                        <h-form-item
                            prop="ruleDesc"
                            label="规则说明"
                        >
                            <h-input
                                v-model="step2FormData.ruleDesc"
                                type="textarea"
                                placeholder="请输入规则说明（不超过200字符）"
                                :maxlength="200"
                            />
                        </h-form-item>
                    </h-form>
                </div>
            </div>
        </div>
        <div class="create-rule-footer">
            <a-button v-if="current === 1" type="dark" @click="preStep">上一步</a-button>
            <span v-if="current ===1" class="create-rule-footer-line" />
            <a-button v-if="current ===0" type="primary" @click="nextStep">下一步：配置规则内容</a-button>
            <a-button v-if="current ===1" :loading="submitLoading" type="primary" @click="create">创建监控规则</a-button>
            <a-button type="dark" class="create-rule-footer-cancel" @click="() => goBack(true)">取消</a-button>
        </div>
    </div>
</template>
<script>
import aButton from '@/components/common/button/aButton';
import { mapState, mapActions } from 'vuex';
import { getUstTableEndpointConfigs } from '@/api/memoryApi';
import thresholdConfig from '@/components/productServiceConfig/rule/thresholdConfig.vue';
import { getSqlListFromSqlStr } from '@/utils/utils';
import { execSql, saveRule } from '@/api/ruleApi';
import { RUN_SQL_PLACEHOLDER, PRE_RUN_SQL_PLACEHOLDER, VAR_TIP, SYSTEM_VAR_TIP } from './constant';
import aTitle from '@/components/common/title/aTitle';

import '@/assets/css/poptip-1.less';
import _ from 'lodash';
import './createRule.less';

export default {
    data() {
        return {
            fileUrl: `${this.IMG_HOME}static/usttable-sql-guide.md`,
            current: 0,
            runSqlPlaceholder: RUN_SQL_PLACEHOLDER,
            preRunSqlPlaceHolder: PRE_RUN_SQL_PLACEHOLDER,
            varTip: VAR_TIP,
            productInfo: {},
            productInstNo: null,
            cores: [],
            leftTestLoading: false,
            rightTestLoading: false,
            submitLoading: false,
            varLoading: false,
            savePreRunSql: null,
            step1FormData: {
                resource: 'none',
                leftMetricName: '指标1',
                rightMetricName: '指标2',
                leftCore: null,
                leftSql: '',
                rightSql: '',
                rightCore: null,
                runCore: null,
                runSql: null,
                leftTestResult: null,
                rightTestResult: null
            },
            step2FormData: {
                ruleName: null,
                ruleDesc: null
            },
            varList: [{ value: '@lastmin(num,format)', desc: SYSTEM_VAR_TIP }],
            sqlRule: [{ test: this.sqlValidate, trigger: 'change,blur' }],
            metricNameRule: [{ test: this.metricNameRuleFn, trigger: 'change,blur' }]
        };
    },
    mounted() {
        this.init();
    },
    computed: {
        ...mapState({
            productList: state => {
                return state.product.productListLight || [];
            }
        }),
        getVarBtnDisabled() {
            if (!this.step1FormData.runCore || !this.step1FormData.runSql) return true;
            return false;
        },
        /**
         * 左边执行核心
         */
        leftCores() {
            if (!this.cores.length) return [];
            return this.cores.filter(item => item.id !== this.step1FormData.rightCore);
        },
        /**
         * 右边执行核心
         */
        rightCores() {
            if (!this.cores.length) return [];
            return this.cores.filter(item => item.id !== this.step1FormData.leftCore);
        },
        /**
         * 左边禁止测试
         */
        leftTestDisabled() {
            if (!this.step1FormData.leftCore) return true;
            const val = this.step1FormData.leftSql ?? '';
            const prefix = val.replace(/\n|\t| /g, '')?.slice(0, 6)?.toLowerCase?.();
            const sqlList = getSqlListFromSqlStr(val);
            if (val.length > 500) {
                return true;
            }
            if (prefix !== 'select' || sqlList.length > 1) {
                return true;
            }
            return this.matchSqlAndVar(val);
        },
        /**
         * 右边禁止测试
         */
        rightTestDisabled() {
            if (!this.step1FormData.rightCore) return true;
            const val = this.step1FormData.rightSql ?? '';
            const prefix = val.replace(/\n|\t| /g, '')?.slice(0, 6)?.toLowerCase?.();
            const sqlList = getSqlListFromSqlStr(val);
            if (val.length > 500) {
                return true;
            }
            if (prefix !== 'select' || sqlList.length > 1) {
                return true;
            }
            return this.matchSqlAndVar(val);
        }
    },
    methods: {
        ...mapActions({ getProductList: 'product/getProductListLight' }),
        /**
         * 验证执行sql和变量的匹配
         */
        matchSqlAndVar(val) {
            // if (val.includes('@')) {
            //     // 校验变量
            //     const args = val.split(' ');
            //     for (const arg of args) {
            //         if (arg && arg.length > 1) {
            //             // 不校验系统变量
            //             if (
            //                 !arg.startsWith('@lastmin') &&
            //                 arg.startsWith('@') &&
            //                 !this.varList.find(item => item.value === arg)
            //             ) {
            //                 return true;
            //             }
            //         }
            //     }
            // }
            return false;
        },
        async init() {
            try {
                this.loading = true;
                await this.getProductList();
                const productInstNo = localStorage.getItem('productInstNo');
                this.productInstNo = _.find(this.productList, ['productInstNo', productInstNo])?.productInstNo || this.productList?.[0]?.productInstNo;
                const productInfo = _.find(this.productList, ['productInstNo', this.productInstNo]);
                this.productInfo = productInfo || {};
                this.queryCore();
            } catch (e) {
                console.error(e);
            } finally {
                this.loading = false;
            }
        },
        metricNameRuleFn({ field }, val, callback) {
            const isLeft = field === 'leftMetricName';
            const anotherName = this.step1FormData[isLeft ? 'rightMetricName' : 'leftMetricName'];

            this.$refs['step1Form'].resetValidateField(isLeft ? 'rightMetricName' : 'leftMetricName');
            if (!val) return callback(new Error('输入不能为空'));
            if (anotherName === val) {
                return callback(new Error('指标名重复'));
            }
            return callback();
        },
        /**
         *  获取变量
         */
        async getVar() {
            try {
                this.varLoading = true;
                this.varList = [{ value: '@lastmin(num,format)', desc: SYSTEM_VAR_TIP }];
                const instance = this.cores.find(item => item.id === this.step1FormData.runCore);
                const res = await execSql({
                    productId: this.productInstNo,
                    preExecSql: this.step1FormData.runSql,
                    preInstanceId: instance?.appInstanceId
                });
                if (res?.success) {
                    this.$hMessage.success('获取变量成功');
                    this.savePreRunSql = this.step1FormData.runSql;
                    this.varList = [...this.varList, ...(res?.data?.data?.tableColumns || []).map(item => ({ value: `@${item}`, desc: null }))];
                } else {
                    this.$hMessage.error(res?.data?.data?.execResult ?? '变量获取失败，请重试！');
                }
            } catch (error) {
                console.log('获取变量失败,', error);
            } finally {
                this.varLoading = false;
            }
        },
        /**
         * 测试sql
         */
        async testSql(position) {
            const isLeft = position === 'left';
            try {
                this[isLeft ? 'leftTestLoading' : 'rightTestLoading'] = true;
                const testKey = isLeft ? 'leftTestResult' : 'rightTestResult';
                this.step1FormData[testKey] = null;
                const instance = this.cores.find(item => item.id === this.step1FormData[isLeft ? 'leftCore' : 'rightCore']);
                const preRunInstance = this.cores.find(item => item.id === this.step1FormData.runCore);
                const param = {
                    productId: this.productInstNo,
                    execSql: isLeft ? this.step1FormData.leftSql : this.step1FormData.rightSql,
                    instanceId: instance?.appInstanceId,
                    preInstanceId: preRunInstance?.appInstanceId
                };
                if (this.savePreRunSql) {
                    param.preExecSql = this.savePreRunSql;
                }
                const res = await execSql(param);
                this.step1FormData[testKey] = res?.data?.data?.execResult || res?.message || '测试失败，请重试';
            } catch (error) {
                console.log('测试sql失败', error);
            } finally {
                this[isLeft ? 'leftTestLoading' : 'rightTestLoading'] = false;
            }
        },
        /**
         * 点击下一步
         */
        nextStep() {
            this.$refs['step1Form'].validate(valid => {
                if (valid) {
                    this.current = 1;
                    this.$refs['configRef'].setValue({
                        leftMetricName: this.step1FormData.leftMetricName,
                        rightMetricName: this.step1FormData.rightMetricName,
                        operator: 'gt',
                        threshold: 1000
                    });
                }
            });
        },
        /**
         * 返回上一步
         */
        preStep() {
            this.current = 0;
        },
        /**
         * 创建监控规则
         */
        create() {
            this.$refs['step2Form'].validate();
            this.$refs['configRef'].validate(valid => {
                if (valid) {
                    this.$refs['step2Form'].validate(step2Valid => {
                        if (step2Valid) {
                            this.submit();
                        }
                    });
                }
            });

        },
        /**
         * 提交规则验证
         */
        async submit() {
            try {
                const condition = this.$refs['configRef'].getValue();
                const leftInstanceId = this.cores.find(item => item.id === this.step1FormData.leftCore)?.appInstanceId;
                const rightInstanceId = this.cores.find(item => item.id === this.step1FormData.rightCore)?.appInstanceId;
                const preRunInstance = this.cores.find(item => item.id === this.step1FormData.runCore);
                const request = {
                    monitorRuleDesc: this.step2FormData.ruleDesc,
                    monitorRuleName: this.step2FormData.ruleName,
                    monitorRuleCode: 'dataCheckRuleMonitor',
                    productId: this.productInstNo,
                    monitorRuleParams: {
                        metrics: [
                            {
                                metricName: this.step1FormData.leftMetricName,
                                preSql: this.step1FormData.resource === 'run' ? this.step1FormData.runSql : '',
                                sql: this.step1FormData.leftSql,
                                instanceId: leftInstanceId,
                                preInstanceId: this.step1FormData.resource === 'run' ? preRunInstance?.appInstanceId : ''
                            },
                            {
                                metricName: this.step1FormData.rightMetricName,
                                preSql: this.step1FormData.resource === 'run' ? this.step1FormData.runSql : '',
                                sql: this.step1FormData.rightSql,
                                instanceId: rightInstanceId,
                                preInstanceId: this.step1FormData.resource === 'run' ? preRunInstance?.appInstanceId : ''
                            }
                        ],
                        condition: {
                            leftMetricName: this.step1FormData.leftMetricName,
                            rightMetricName: this.step1FormData.rightMetricName,
                            operator: condition.operator,
                            threshold: condition.threshold
                        }
                    }
                };
                this.submitLoading = true;
                const res = await saveRule(request);
                if (res?.code === '200' && res?.success) {
                    this.$hMessage.success('创建成功');
                    this.goBack();
                }
            } catch (error) {
                console.log('规则创建失败', error);
            } finally {
                this.submitLoading = false;
            }
        },
        /**
         * 查询核心
         */
        async queryCore() {
            try {
                const res = await getUstTableEndpointConfigs({ productId: this.productInstNo });
                if (res?.success && res?.code === '200') {
                    this.cores = (res.data?.configs || []);
                    this.step1FormData.leftCore = this.cores[0]?.id;
                    this.step1FormData.runCore = this.cores[0]?.id;
                    this.step1FormData.rightCore = this.cores[1]?.id;
                } else {
                    this.cores = [];
                    this.step1FormData.leftCore = null;
                    this.step1FormData.rightCore = null;
                }
            } catch (error) {
                console.log('查询核心失败', error);
            }
        },
        /**
         * sql校验
         */
        sqlValidate(rule, val, callback) {
            const prefix = val.replace(/\n|\t| /g, '')?.slice(0, 6)?.toLowerCase?.();
            const sqlList = getSqlListFromSqlStr(val);
            if (val.length > 500) {
                return callback(new Error('最大输入长度500个字符'));
            }
            if (prefix !== 'select' || sqlList.length > 1) {
                return callback(new Error('只允许写一条select语句'));
            }
            // if (val.includes('@')) {
            //     // 校验变量
            //     const args = val.split(' ');
            //     for (const arg of args) {
            //         if (arg && arg.length > 1) {
            //             // 不校验系统变量
            //             if (!arg.startsWith('@lastmin') && arg.startsWith('@') && !this.varList.find(item => item.value === arg)) {
            //                 return callback(new Error(`变量 ${arg} 不存在`));
            //             }
            //         }
            //     }
            // }
            return callback();
        },
        /**
         * 返回规则列表
         */
        goBack(needVerify) {
            if (needVerify === true) {
                this.$hMsgBoxSafe.confirm({
                    title: `确认离开页面？`,
                    content: `离开后当前操作将不会保存，数据会丢失，请谨慎操作！`,
                    onOk: () => {
                        this.$hCore.navigate(`/productServiceList`, {
                            menuId: 'monitorRule'
                        });
                    },
                    cancelText: '留下'
                });
                return;
            }
            this.$hCore.navigate(`/productServiceList`, {
                menuId: 'monitorRule'
            });
        }
    },
    components: { aButton, thresholdConfig, aTitle }
};
</script>

<style scoped lang="less">
@import url(@/assets/css/steps.less);
@import url("@/assets/css/input.less");

.create-rule-step {
    /deep/ .h-steps-head-inner {
        display: flex;
        align-items: center;
        justify-content: center;
    }
}

.title {
    min-width: 800px;

    .product-select {
        float: right;
        margin-right: 15px;
        top: 5px;
        min-width: 200px;
        width: auto;
    }

    .apm-title-create {
        &::before {
            display: none;
        }
    }
}

.create-title {
    float: left;

    .h-icon {
        cursor: pointer;
        margin-right: 8px;

        &:hover {
            color: var(--link-color);
        }
    }
}
</style>
