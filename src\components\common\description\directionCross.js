import './directionCross.less';
import { transferVal } from '@/utils/utils';
export default {
    name: 'description',
    props: {
        title: {
            type: String,
            default: ''
        },
        dataDic: {
            type: Array,
            default: () => []
        },
        data: {
            type: Object,
            default: () => {}
        }
    },
    data() {
        return {
        };
    },
    render() {
        return <div class="descriptions-cross">
            { this.title && <div class="descriptions-title"><span>{this.title}</span></div>}
            <div class="descriptions-group">
                {
                    this.dataDic.map(v => {
                        return <div
                            class="descriptions-item"
                        >
                            <div class="descriptions-item-label">{ v.label || v.key }</div>
                            <div class="descriptions-item-value" title={this.data[v.key]}>{ transferVal(this.data[v.key]) || '-'}</div>
                        </div>;
                    })
                }
            </div>
        </div>;
    }
};
