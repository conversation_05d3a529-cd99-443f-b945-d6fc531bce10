<template>
    <div class="tab-box">
        <a-loading v-if="loading"></a-loading>
        <div v-else>
            <description-bar :data="nodeDes"></description-bar>
            <description-bar :data="netDes"></description-bar>
            <description-bar :data="redoDes"></description-bar>
            <description-bar :data="sendDes"></description-bar>
            <description-bar :data="resendDes"></description-bar>
            <obs-table maxHeight="220" :title="title" :tableData="tableData" showTitle :columns="columns" :hasPage="false" />
        </div>
    </div>
</template>

<script>
import obsTable from '@/components/common/obsTable/obsTable';
import descriptionBar from '@/components/common/description/descriptionBar';
import aLoading from '@/components/common/loading/aLoading';
import { getManagerProxy } from '@/api/mcApi';
export default {
    props: {
        nodeData: {
            type: Object,
            default: () => { }
        },
        pollTime: {
            type: Number,
            default: 0
        }
    },
    components: { obsTable, aLoading, descriptionBar },
    data() {
        return {
            loading: true,
            // 核心信息
            nodeDes: {
                title: {
                    label: '核心信息',
                    slots: []
                },
                details: [
                    {
                        dataDic: [
                            {
                                label: '核心产品名',
                                key: 'XstProductName',
                                span: 5

                            },
                            {
                                label: '核心集群名',
                                key: 'XstShortAppName',
                                span: 5
                            },
                            {
                                label: '核心应用名',
                                key: 'XstAppName',
                                span: 5
                            },
                            {
                                label: '核心应用编号',
                                key: 'XstAppNo',
                                span: 5
                            },
                            {
                                label: '主备状态',
                                key: 'HostStatus',
                                span: 4
                            }
                        ],
                        data: {
                            XstProductName: '-',
                            XstShortAppName: '-',
                            XstAppName: '-',
                            XstAppNo: '-',
                            HostStatus: '-'
                        }
                    }
                ]
            },
            // 网关信息
            netDes: {
                title: {
                    label: '网关信息',
                    slots: []
                },
                details: [
                    {
                        dataDic: [
                            {
                                label: '回库地址',
                                key: 'Address'
                            },
                            {
                                label: '连接的网关',
                                key: 'IsUstGW'
                            },
                            {
                                label: '网关发送主备模式',
                                key: 'GWMasterSlaveOn'
                            },
                            {
                                label: '注册名',
                                key: 'RegName'
                            }
                        ],
                        data: {
                            Address: '-',
                            IsUstGW: '-',
                            GWMasterSlaveOn: '-',
                            RegName: '-'
                        }
                    }
                ]
            },
            // 读Redo配置
            redoDes: {
                title: {
                    label: '读Redo配置',
                    slots: []
                },
                details: [
                    {
                        dataDic: [
                            {
                                label: 'redo类型',
                                key: 'RedoTypes',
                                span: 24
                            },
                            {
                                label: 'redo文件路径',
                                key: 'RedoPath',
                                span: 24
                            },
                            {
                                label: '共享内存文件名',
                                key: 'ShmName',
                                span: 24
                            },
                            {
                                label: '业务框架在zk上的回库节点路径',
                                key: 'ZkPath',
                                span: 24
                            },
                            {
                                label: '回库在zk上的节点路径',
                                key: 'TodbZKPath',
                                span: 24
                            }
                        ],
                        data: {
                            RedoTypes: '-',
                            RedoPath: '-',
                            ShmName: '-',
                            ZkPath: '-',
                            TodbZKPath: '-'
                        }
                    }
                ]
            },
            // 发送配置
            sendDes: {
                title: {
                    label: '发送配置',
                    slots: []
                },
                details: [
                    {
                        dataDic: [
                            {
                                label: '多线程模式',
                                key: 'MultiThreadsMode'
                            },
                            {
                                label: '线程数量',
                                key: 'ThreadsCount'
                            }
                        ],
                        data: {
                            MultiThreadsMode: '-',
                            ThreadsCount: '-'
                        }
                    }
                ]
            },
            // 重传配置
            resendDes: {
                title: {
                    label: '重传配置',
                    slots: []
                },
                details: [
                    {
                        dataDic: [
                            {
                                label: '重传发送间隔',
                                key: 'ResendIntervalSec'
                            },
                            {
                                label: '最大重传次数',
                                key: 'MaxResendCount'
                            }
                        ],
                        data: {
                            ResendIntervalSec: '-',
                            MaxResendCount: '-'
                        }
                    }
                ]
            },
            // 应用工作目录
            title: {
                label: '错误号白名单信息'
            },
            columns: [
                {
                    title: '错误号',
                    key: 'ErrorNo',
                    ellipsis: true
                },
                {
                    title: '错误号数量',
                    key: 'MatchCount',
                    ellipsis: true
                }
            ],
            tableData: []
        };
    },
    mounted() {
    },
    methods: {
        async initData() {
            this.loading = true;
            await this.getFileData();
            this.loading = false;
        },
        async getFileData() {
            const { getMonitor, getErrNoWhiteListInfo } = await this.getAPi();
            this.nodeDes.details[0].data = { ...getMonitor };
            this.netDes.details[0].data = { ...getMonitor };
            this.redoDes.details[0].data = { ...getMonitor };
            this.sendDes.details[0].data = { ...getMonitor };
            this.resendDes.details[0].data = { ...getMonitor };
            this.tableData = [...getErrNoWhiteListInfo];
        },
        // 接口请求
        async getAPi() {
            const data = {
                getMonitor: {},
                getErrNoWhiteListInfo: []
            };
            const param = [
                {
                    manageProxyIp: this.nodeData.ip,
                    manageProxyPort: this.nodeData.manageProxyPort,
                    pluginName: 'ldp_todb',
                    funcName: 'GetMonitor'
                },
                {
                    manageProxyIp: this.nodeData.ip,
                    manageProxyPort: this.nodeData.manageProxyPort,
                    pluginName: 'ldp_todb',
                    funcName: 'GetErrNoWhiteListInfo'
                }
            ];
            try {
                const res = await getManagerProxy(JSON.stringify(param));
                if (res.code === '200') {
                    !res.data?.[0]?.ErrorNo && Object.keys(res.data?.[0]).length && (data.getMonitor = res.data[0]);
                    res.data?.[1]?.ErrNoWhiteListInfo && (data.getErrNoWhiteListInfo = res.data[1].ErrNoWhiteListInfo);
                }
                return data;
            } catch (err) {
                this.$emit('clear');
            }
        }
    }
};
</script>
