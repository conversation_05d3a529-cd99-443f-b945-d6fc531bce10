# 🚀 国际化快速使用指南

## 📋 概述

本指南帮助您快速开始使用提取的国际化文案。我们已经从项目的523个文件中提取了3387条中文文案，并生成了完整的国际化键值对。

## 🎯 推荐使用版本

**使用目录**: `src/locales/i18n/`

这是经过优化的最终版本，具有以下特点：
- ✅ 3387条高质量文案
- ✅ 语义化的英文key
- ✅ 按路由维度组织
- ✅ 排除了干扰文件

## 🔧 快速开始

### 1. 更新导入路径

在您的项目中找到国际化配置文件，更新导入路径：

```javascript
// 原来的导入（如果有）
import customLocales from '@/locales';

// 更新为新的路径
import customLocales from '@/locales/i18n';
```

### 2. 在main.js中配置（如果还没有配置）

```javascript
import Vue from 'vue';
import VueI18n from 'vue-i18n';
import customLocales from '@/locales/i18n';

Vue.use(VueI18n);

const i18n = new VueI18n({
  locale: 'zh-CN', // 默认语言
  messages: customLocales
});

new Vue({
  i18n,
  // ... 其他配置
}).$mount('#app');
```

### 3. 在组件中使用

#### Vue模板中使用
```vue
<template>
  <div>
    <!-- 通用操作按钮 -->
    <h-button type="primary">{{ $t('common.query') }}</h-button>
    <h-button type="success">{{ $t('common.add') }}</h-button>
    <h-button type="warning">{{ $t('common.edit') }}</h-button>
    <h-button type="danger">{{ $t('common.delete') }}</h-button>
    
    <!-- 页面特定文案 -->
    <h2>{{ $t('pages.createRule.ruleManage') }}</h2>
    
    <!-- 组件特定文案 -->
    <div>{{ $t('components.ldpDataObservation.dataMonitor') }}</div>
    
    <!-- 表单提示 -->
    <h-select :placeholder="$t('common.pleaseSelect')">
      <h-option value="1">选项1</h-option>
    </h-select>
    
    <!-- 空数据提示 -->
    <div v-if="!data.length" class="no-data">
      {{ $t('common.noData') }}
    </div>
  </div>
</template>
```

#### JavaScript中使用
```javascript
export default {
  methods: {
    // 显示消息
    showMessage() {
      this.$hMessage.success(this.$t('common.success'));
      this.$hMessage.error(this.$t('common.failed'));
      this.$hMessage.warning(this.$t('common.warning'));
    },
    
    // 确认对话框
    confirmDelete() {
      this.$hMsgBoxSafe.confirm({
        title: this.$t('common.confirm'),
        content: this.$t('common.confirmDelete'),
        onOk: () => {
          // 删除逻辑
          this.$hMessage.success(this.$t('common.deleteSuccess'));
        }
      });
    },
    
    // 表单验证
    getValidationRules() {
      return {
        name: [
          { required: true, message: this.$t('common.pleaseInput') + this.$t('common.name') }
        ]
      };
    }
  }
};
```

## 📚 常用文案路径

### 通用操作类
```javascript
'common.query'          // 查询
'common.add'            // 添加
'common.edit'           // 编辑
'common.delete'         // 删除
'common.save'           // 保存
'common.cancel'         // 取消
'common.confirm'        // 确认
'common.submit'         // 提交
'common.reset'          // 重置
'common.refresh'        // 刷新
'common.export'         // 导出
'common.import'         // 导入
```

### 状态提示类
```javascript
'common.success'        // 成功
'common.failed'         // 失败
'common.error'          // 错误
'common.warning'        // 警告
'common.loading'        // 加载中
'common.running'        // 运行中
'common.stopped'        // 已停止
'common.finished'       // 已完成
```

### 表单提示类
```javascript
'common.pleaseSelect'   // 请选择
'common.pleaseInput'    // 请输入
'common.noData'         // 暂无数据
'common.operationSuccess' // 操作成功
'common.confirmDelete'  // 确认删除吗？
```

### 业务相关类
```javascript
'common.securities'     // 证券
'common.futures'        // 期货
'common.option'         // 期权
'common.riskControl'    // 风控
'common.monitor'        // 监控
'common.data'           // 数据
'common.config'         // 配置
'common.manage'         // 管理
```

## 🗂️ 文案分类结构

### 1. common - 通用文案
适用于整个应用的通用文案，如按钮文字、状态提示等。

### 2. pages - 页面文案
按页面组织的特定文案：
- `pages.createRule.*` - 创建规则页面
- `pages.smsList.*` - 短信列表页面
- `pages.analyseData.*` - 数据分析页面
- 等等...

### 3. components - 组件文案
按组件组织的特定文案：
- `components.ldpDataObservation.*` - LDP数据观测组件
- `components.rcmDeploy.*` - RCM部署组件
- `components.ldpTable.*` - LDP表格组件
- 等等...

## 🔍 查找文案

### 1. 查看映射文件
打开 `src/locales/optimized-complete/mapping.json` 查看完整的文案映射关系。

### 2. 搜索原文案
如果您知道原来的中文文案，可以在映射文件中搜索对应的key。

### 3. 按分类查找
根据文件所在位置，在对应的分类中查找：
- 如果是通用文案，查看 `common.*`
- 如果是特定页面，查看 `pages.{页面名}.*`
- 如果是特定组件，查看 `components.{组件名}.*`

## 📝 迁移示例

### 迁移前（硬编码中文）
```vue
<template>
  <div>
    <h-button>查询</h-button>
    <h-button>添加</h-button>
    <div>请选择节点</div>
    <span v-if="!data.length">暂无数据</span>
  </div>
</template>

<script>
export default {
  methods: {
    showSuccess() {
      this.$hMessage.success('操作成功');
    }
  }
};
</script>
```

### 迁移后（使用国际化）
```vue
<template>
  <div>
    <h-button>{{ $t('common.query') }}</h-button>
    <h-button>{{ $t('common.add') }}</h-button>
    <div>{{ $t('common.pleaseSelect') }}{{ $t('common.node') }}</div>
    <span v-if="!data.length">{{ $t('common.noData') }}</span>
  </div>
</template>

<script>
export default {
  methods: {
    showSuccess() {
      this.$hMessage.success(this.$t('common.operationSuccess'));
    }
  }
};
</script>
```

## 🛠️ 维护和扩展

### 添加新文案
1. 在对应的分类中添加新的键值对
2. 同时更新中文和英文版本
3. 使用小驼峰命名规范

### 运行提取脚本
如果有新的文案需要提取：
```bash
node scripts/extract-i18n-optimized.js
```

### 验证文案质量
```bash
node scripts/validate-i18n.js
```

## ❓ 常见问题

### Q: 找不到对应的文案key怎么办？
A: 查看 `mapping.json` 文件，搜索原中文文案找到对应的key。

### Q: 如何添加新的文案？
A: 在对应的分类中添加新的键值对，遵循小驼峰命名规范。

### Q: 英文翻译什么时候提供？
A: 目前英文版本使用的是中文文案，需要后续提供专业翻译。

### Q: 如何切换语言？
A: 使用 `this.$i18n.locale = 'en-US'` 切换到英文。

## 📞 支持

如果在使用过程中遇到问题，请查看：
1. `FINAL_INTERNATIONALIZATION_REPORT.md` - 完整报告
2. `src/locales/optimized-complete/extraction-report.json` - 提取报告
3. `src/locales/MIGRATION_EXAMPLE.vue` - 迁移示例

---

🎉 **开始使用国际化，让您的应用支持多语言！**
