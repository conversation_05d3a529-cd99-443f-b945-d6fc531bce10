<template>
    <div class="tab-box">
        <a-loading v-if="loading"></a-loading>
        <div v-else>
            <description-bar :data="nodeDes"></description-bar>
            <obs-table ref="table2" maxHeight="220" :title="pluginTitle" :tableData="pluginTableData" showTitle :columns="pluginColumns" :hasPage="false" />
            <obs-table ref="table1" maxHeight="220" :title="dirTitle" :tableData="dirTableData" showTitle :columns="dirColumns" :hasPage="false" />
            <description-bar :data="configDes"></description-bar>
            <obs-table ref="table3" maxHeight="220" :title="threadTitle" :tableData="threadTableData" showTitle :columns="threadColumns" :hasPage="false" />
        </div>
    </div>
</template>

<script>
import obsTable from '@/components/common/obsTable/obsTable';
import aLoading from '@/components/common/loading/aLoading';
import descriptionBar from '@/components/common/description/descriptionBar';
import { getManagerProxy } from '@/api/mcApi';
export default {
    props: {
        nodeData: {
            type: Object,
            default: () => { }
        },
        pollTime: {
            type: Number,
            default: 0
        }
    },
    components: { obsTable, aLoading, descriptionBar },
    data() {
        return {
            loading: true,
            // 应用节点
            nodeDes: {
                title: {
                    label: '应用节点',
                    slots: []
                },
                details: [
                    {
                        dataDic: [
                            {
                                label: '应用名称',
                                key: 'AppName'
                            },
                            {
                                label: '应用版本',
                                key: 'Version'
                            },
                            {
                                label: '应用类型',
                                key: 'Type'
                            },
                            {
                                label: '应用开发平台',
                                key: 'Platform'
                            },
                            {
                                label: '产品名称',
                                key: 'ProductName'
                            },
                            {
                                label: '应用简称',
                                key: 'ShortAppName'
                            },
                            {
                                label: '应用编号',
                                key: 'AppNo'
                            },
                            {
                                label: '应用实例',
                                key: 'AppInstance'
                            },
                            {
                                label: 'pid文件',
                                key: 'PidFile',
                                span: 24
                            }
                        ],
                        data: {
                            AppName: '-',
                            Version: '-',
                            Type: '-',
                            Platform: '-',
                            ShortAppName: '-',
                            ProductName: '-',
                            AppNo: '-',
                            AppInstance: '-',
                            PidFile: '-'
                        }
                    }
                ]
            },
            // 应用插件
            pluginTitle: {
                label: '应用插件'
            },
            pluginColumns: [
                {
                    title: '插件名称',
                    key: 'PluginName',
                    ellipsis: true
                },
                {
                    title: '实例号',
                    key: 'InstanceNo',
                    ellipsis: true
                },
                {
                    title: '插件版本',
                    key: 'Version',
                    ellipsis: true
                },
                {
                    title: '发布日期',
                    key: 'date',
                    ellipsis: true
                },
                {
                    title: '管理功能数量',
                    key: 'MgrListCounts',
                    ellipsis: true,
                    render: (h, params) => {
                        const popData = [...params.row.MgrList];
                        return h('div', [
                            h(
                                'Poptip',
                                {
                                    class: 'apm-poptip',
                                    props: {
                                        title: '管理功能列表',
                                        placement: 'top',
                                        positionFixed: true
                                    }
                                },
                                [
                                    h(
                                        'span',
                                        [params.row.MgrList.length]
                                    ),
                                    h(
                                        'div',
                                        {
                                            slot: 'content',
                                            class: 'pop-content'
                                        },
                                        [
                                            ...popData.map((value, index) => {
                                                return h(
                                                    'p',
                                                    [
                                                        h(
                                                            'span',
                                                            {
                                                                class: 'pop-label'
                                                            },
                                                            `${index}`
                                                        ),
                                                        h(
                                                            'span',
                                                            {
                                                                class: 'pop-value',
                                                                attrs: {
                                                                    title: `${value}`
                                                                }
                                                            },
                                                            `${value}`
                                                        )
                                                    ]
                                                );
                                            })
                                        ]
                                    )
                                ]
                            )
                        ]);
                    }
                }
            ],
            pluginTableData: [],
            // 应用工作目录
            dirTitle: {
                label: '应用工作目录'
            },
            dirColumns: [
                {
                    title: '目录',
                    key: 'dir',
                    ellipsis: true
                },
                {
                    title: '目录地址',
                    key: 'dirPath',
                    ellipsis: true
                }
            ],
            dirTableData: [],
            // 线程信息
            threadTitle: {
                label: '线程信息'
            },
            threadColumns: [
                {
                    title: '线程拥有者',
                    key: 'Owner',
                    ellipsis: true
                },
                {
                    title: '线程名',
                    key: 'Name',
                    ellipsis: true
                },
                {
                    title: '线程类型',
                    key: 'Priority',
                    ellipsis: true
                },
                {
                    title: '绑核',
                    key: 'CpuNo',
                    ellipsis: true
                }
            ],
            threadTableData: [],
            // 配置中心
            configDes: {
                title: {
                    label: '配置中心',
                    slots: []
                },
                details: [
                    {
                        dataDic: [
                            {
                                label: '配置地址',
                                key: 'ConfigServer',
                                span: 6
                            },
                            {
                                label: '配置文件',
                                key: 'ConfigFileName',
                                span: 10
                            },
                            {
                                label: 'zk配置地址',
                                key: 'ZKConfigPath',
                                span: 8
                            }
                        ],
                        data: {
                            ConfigServer: '-',
                            ConfigFileName: '-',
                            ZKConfigPath: '-'
                        }
                    }
                ]
            }
        };
    },
    mounted() {
    },
    methods: {
        async initData() {
            this.loading = true;
            await this.getFileData();
            this.loading = false;
        },
        async getFileData() {
            const { getAppInfo, getPluginList, queryMgrList, getThreadInfo } = await this.getAPi();

            // 应用节点
            this.nodeDes.details[0].data = {
                AppName: getAppInfo.AppName,
                Version: this.nodeData.version,
                Type: this.nodeData.instanceType,
                Platform: this.nodeData.developPlatform,
                ShortAppName: getAppInfo.ShortAppName,
                ProductName: getAppInfo.ProductName,
                AppNo: getAppInfo.AppNo,
                AppInstance: getAppInfo.AppInstance,
                PidFile: getAppInfo.PidFile
            };

            // 配置中心
            this.configDes.details[0].data = {
                ConfigServer: getAppInfo.ConfigServer,
                ConfigFileName: getAppInfo.ConfigFileName,
                ZKConfigPath: getAppInfo.ZKConfigPath
            };

            // 线程信息
            this.threadTableData = [...getThreadInfo];

            // 应用工作目录
            this.dirTableData = [
                { dir: 'HomeDirectory', dirPath: getAppInfo.HomeDirectory || '-' },
                { dir: 'WorkingDirectory', dirPath: getAppInfo.WorkingDirectory || '-' },
                { dir: 'MessageDirectory', dirPath: getAppInfo.MessageDirectory || '-' },
                { dir: 'ComponentsDirectory', dirPath: getAppInfo.ComponentsDirectory || '-' },
                { dir: 'PluginsDirectory', dirPath: getAppInfo.PluginsDirectory || '-' },
                { dir: 'AppDirectory', dirPath: getAppInfo.AppDirectory || '-' },
                { dir: 'AppModulesDirectory', dirPath: getAppInfo.AppModulesDirectory || '-' }
            ];
            // 应用插件
            this.pluginTableData = [];
            getPluginList.forEach(v => {
                // const text = "ldp_mproxy V1.0.8.2 Feb 26 2023 12:16:09";
                // 使用正则表达式匹配版本号和时间信息
                const versionMatch = v?.Version?.match(/V\d+\.\d+\.\d+\.\d+/) || [];
                const timeMatch = v?.Version?.match(/[A-Za-z]{3} +\d{1,2} +\d{4} +\d{2}:\d{2}:\d{2}/) || [];
                const mgrList = queryMgrList.filter(v1 => String(v1.PluginName + v1.InstanceNo) === String(v.PluginName + v.InstanceNo))?.[0]?.MgrList || [];
                this.pluginTableData.push({
                    ...v,
                    Version: versionMatch?.[0] || '-',
                    date: timeMatch?.[0] || '-',
                    MgrList: mgrList
                });
            });
        },
        // 接口请求
        async getAPi() {
            const data = {
                getAppInfo: {},
                getPluginList: [],
                queryMgrList: [],
                getThreadInfo: []
            };
            const param = [
                {
                    manageProxyIp: this.nodeData.manageProxyIp,
                    manageProxyPort: this.nodeData.manageProxyPort,
                    pluginName: 'ldp_mproxy',
                    funcName: 'GetAppInfo'
                },
                {
                    manageProxyIp: this.nodeData.manageProxyIp,
                    manageProxyPort: this.nodeData.manageProxyPort,
                    pluginName: 'ldp_mproxy',
                    funcName: 'GetPluginList'
                },
                {
                    manageProxyIp: this.nodeData.manageProxyIp,
                    manageProxyPort: this.nodeData.manageProxyPort,
                    pluginName: 'ldp_mproxy',
                    funcName: 'QueryMgrList'
                },
                {
                    manageProxyIp: this.nodeData.manageProxyIp,
                    manageProxyPort: this.nodeData.manageProxyPort,
                    pluginName: 'ldp_mproxy',
                    funcName: 'GetThreadInfo'
                }
            ];
            try {
                const res = await getManagerProxy(JSON.stringify(param));
                if (res.code === '200') {
                    !res.data?.[0]?.ErrorNo && Object.keys(res.data?.[0]).length  && (data.getAppInfo = res.data[0]);
                    res.data?.[1]?.Response?.length && (data.getPluginList = res.data[1].Response);
                    res.data?.[2]?.Response?.length && (data.queryMgrList = res.data[2].Response);
                    res.data?.[3]?.ThreadInfo?.length && (data.getThreadInfo = res.data[3].ThreadInfo);
                }
                return data;
            } catch (err) {
                this.$emit('clear');
            }
        }
    }
};
</script>
<style lang="less">
// render函数生成的节点无法匹配scoped id， 需要用全局去处理
@import url("@/assets/css/poptip-1.less");
</style>
<style lang="less" scoped>
.tab-box {
    .product-info {
        width: 100%;
        padding: 10px 0;
        word-wrap: break-word;
        overflow: auto;

        & > p {
            display: inline-block;
            color: var(--font-color);
            padding-top: 10px;
            padding-right: 20px;
            line-height: 15px;

            & > span {
                padding-left: 13px;
                color: var(--font-opacity-color);
            }
        }
    }

    /deep/ .vue-grid-item.vue-grid-placeholder {
        background: var(--base-color);
    }

    /deep/ .h-poptip-popper {
        width: 250px;

        .pop-content {
            max-height: 120px;

            .pop-label {
                display: inline-block;
                width: 25%;
                margin-right: 10px;
                overflow: hidden;
                white-space: nowrap;
                text-overflow: ellipsis;
            }

            .pop-value {
                display: inline-block;
                width: 70%;
                overflow: hidden;
                white-space: nowrap;
                text-overflow: ellipsis;
            }
        }
    }
}
</style>
