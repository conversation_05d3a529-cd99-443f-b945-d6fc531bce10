<!-- 添加、编辑mdb角色 -->
<template>
    <div>
        <h-msg-box-safe v-model="modalData.status" :mask-closable="false"
            :title="modalData.type === 'add' ? '添加角色' : '编辑角色'" width="600" maxHeight="350" @on-open="getCollections">
            <a-tips v-if="modalData.type === 'add'" tipText="请先完成角色的基础信息配置，完成后可为角色配置权限。"></a-tips>
            <h-form ref="formValidate" :model="formValidate" :label-width="80">
                <h-form-item label="角色名：" prop="roleName" required>
                    <h-input v-model.trim="formValidate.roleName" type="text" placeholder="请输入角色名" clearable
                        :maxlength="30"></h-input>
                </h-form-item>
                <h-form-item label="描述：" prop="roleDescribe">
                    <h-input v-model.trim="formValidate.roleDescribe" type="textarea" :rows="2" placeholder="请输入描述"
                        :maxlength="500" :canResize="false"></h-input>
                </h-form-item>
            </h-form>
            <template v-slot:footer>
                <a-button @click="modalData.status = false">取消</a-button>
                <a-button type="primary" :loading="loading" @click="submitConfig">确认</a-button>
            </template>
        </h-msg-box-safe>
    </div>
</template>

<script>
import _ from 'lodash';
import aButton from '@/components/common/button/aButton';
import aTips from '@/components/common/apmTips/aTips';
export default {
    name: 'AddOrEditUser',
    props: {
        modalInfo: {
            type: Object,
            default: null
        }
    },
    data() {
        return {
            modalData: this.modalInfo,
            formValidate: {},
            loading: false,
            showPassWord: false
        };
    },
    methods: {
        getCollections() {
            this.$refs['formValidate'].resetFields();
            this.formValidate = _.cloneDeep(this.modalData);
        },
        submitConfig() {
            this.$refs['formValidate'].validate((valid) => {
                if (valid) {
                    this.$emit('add-or-edit-role', this.formValidate);
                    this.modalData.status = false;
                }
            });
        }
    },
    components: { aButton, aTips }
};
</script>

<style lang="less" scoped>
/deep/.h-modal-body {
    padding: 16px;
}

/deep/ .h-input-icon {
    cursor: pointer;
}

.tips-content {
    margin-bottom: 15px;
}
</style>
