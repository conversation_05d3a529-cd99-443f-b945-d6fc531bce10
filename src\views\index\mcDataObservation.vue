<template>
    <div class="main">
        <div class="main-top">
            <span class="currenttime">今天是: {{ currentTime }}</span>
            <p>{{ $getProductType(productInfo.productType) }}</p>
            <h-select v-show="productList.length > 1" v-model="productInstNo" class="securities"
                placeholder="请选择" :positionFixed="true" :clearable="false"
                @on-change="checkProduct">
                <h-option v-for="item in productList" :key="item.id" :value="item.productInstNo || ''">
                    {{ item.productName}}
                </h-option>
            </h-select>
        </div>
        <a-loading v-if="loading"></a-loading>
        <div v-if="menuList.length" class="container">
            <menu-layout ref="menu" :menuList="menuList" menuItemId="id" titleAttribute="clusterName" menuTitle="已托管MC3.0集群"
                @check-menu="checkModel">
                <template v-slot:right>
                    <h-tabs v-if="menu.id && editableTabs.length" v-model="tabName" class="product-box" @on-click="tabClick(tabName)">
                        <h-tab-pane v-for="item in editableTabs" :key="item.name" :label="item.describe" :name="item.name">
                            <component :is='item.name' :ref="item.name" :nodeData="menu"
                                :productId="productInstNo" :timerInterval="item.timerInterval" @clear="clearPolling">
                            </component>
                        </h-tab-pane>
                    </h-tabs>
                    <no-data v-else></no-data>
                </template>
            </menu-layout>
        </div>
        <div v-else style="height: calc(100% - 110px);">
            <no-data></no-data>
        </div>
    </div>
</template>

<script>
import _ from 'lodash';
import { formatDate } from '@/utils/utils';
import { mapState, mapActions } from 'vuex';
import { getProductClusters, getProductInstances } from '@/api/productApi';
import { getDashboardConfig } from '@/api/mcApi';
import noData from '@/components/common/noData/noData';
import aLoading from '@/components/common/loading/aLoading';
import menuLayout from '@/components/common/menuLayout/menuLayout';
import mcCluster from '@/components/mcDataObservation/mcCluster.vue';
import mcTopic from '@/components/mcDataObservation/mcTopic.vue';
import mcOverview from '@/components/mcDataObservation/mcOverview.vue';
import mcPublish from '@/components/mcDataObservation/mcPublish.vue';
import mcSubscribe from '@/components/mcDataObservation/mcSubscribe.vue';
import backtrackQuery from '@/components/mcDataObservation/backtrackQuery.vue';
import deadLetterQueue from '@/components/mcDataObservation/deadLetterQueue.vue';
import consumeBacklog from '@/components/mcDataObservation/consumeBacklog.vue';

export default {
    components: { aLoading, noData, menuLayout,
        mcSubscribe, mcOverview, mcPublish, mcTopic, mcCluster,
        backtrackQuery, deadLetterQueue, consumeBacklog
    },
    data() {
        return {
            editableTabs: [],
            currentTime: new Date(),
            productInstNo: '',
            productInfo: {},
            productType: '',
            tabName: 'mcOverview', // tab默认选择
            menu: {},
            menuList: [],
            loading: false
        };
    },
    mounted() {
        this.init();
    },
    beforeDestroy() {
        this.clearPolling();
    },
    computed: {
        ...mapState({
            productList: (state) => {
                return state.product.productListLight || [];
            }
        })
    },
    methods: {
        ...mapActions({ getProductList: 'product/getProductListLight' }),
        // 初始化页面
        async init() {
            try {
                this.loading = true;
                await this.getProductList({ filter: 'includeMCCluster' });
                const productInstNo = localStorage.getItem('productInstNo');
                this.productInstNo = _.find(this.productList, ['productInstNo', productInstNo])?.productInstNo || this.productList?.[0]?.productInstNo;
                this.currentTime = formatDate(new Date());
                setTimeout(() => {
                    this.loading = false;
                }, 1000);
            } catch (e) {
                this.loading = false;
                console.error(e);
            }
        },
        // 手动清空数据
        clearData() {
            this.menu = {};
            this.menuList = [];
            this.productInfo = {};
            this.productType = '';
            this.editableTabs = [];
        },
        // 切换产品
        async checkProduct(val) {
            this.clearData();
            this.loading = true;
            if (this.productList.length) {
                this.loading = true;
                this.productInfo = val ? _.find(this.productList, ['productInstNo', val]) : this.productList[0];
                localStorage.setItem('productInstNo', this.productInfo.productInstNo);
                await this.getProductClusters();

                // 判断左侧菜单少于一个选项时，隐藏菜单
                this.$nextTick(() => {
                    if (this.$refs['menu']) {
                        this.$refs['menu'].setMenuStatus(this.menuList?.length <= 1);
                    }
                });
                this.menuList?.[0]?.id && this.$refs['menu'].initMenu(this.menuList[0]);
            }
            setTimeout(() => {
                this.loading = false;
            }, 1000);
        },
        // 获取mc应用集群信息
        async getProductClusters() {
            const res = await getProductClusters({
                productId: this.productInfo.id,
                clusterInstanceType: 'mc'
            });
            if (res.code === '200') {
                this.menuList = res?.data?.appClusters;
            }
        },
        // 获取应用节点列表
        async getProductInstances(id) {
            let instances = [];
            const res = await getProductInstances({ productId: this.productInfo.id, clusterId: id });
            if (res.code === '200') {
                instances = res?.data?.instances;
            }
            return instances;
        },
        // 切换导航产品
        async checkModel(item) {
            this.menu = item;
            await this.getDashboardConfig();
            const instances = await this.getProductInstances(item.id);
            this.menu.productInstances = instances;
            this.menu.version = '-';
            const tab = this.editableTabs?.filter(v => v.name === this.tabName) || [];
            this.tabName = tab.length ? this.tabName : this.editableTabs?.[0]?.name || 'mcOverview';
            this.$nextTick(async () => {
                await this.tabClick(this.tabName);
            });
        },
        // 获取Dashboard
        async getDashboardConfig(){
            const res = await getDashboardConfig({
                productId: this.productInfo.id,
                type: 'mcCluster'
            });
            if (res.code === '200'){
                this.editableTabs = res?.data?.filter(v => v?.visible === true) || [];
            }
        },
        // 切换tab
        async tabClick(name) {
            this.clearPolling();
            const currentTab = _.find(this.editableTabs, ['name', name]);
            if (currentTab?.timerSwitch && ['mcOverview', 'consumeBacklog'].includes(name)) {
                await this.setPolling(name, currentTab?.timerInterval || 5);
            }
            this.$nextTick(async () => {
                this.$refs?.[name]?.[0] && await this.$refs[name][0].initData();
            });
        },
        // 轮询调用接口构建页面
        async setPolling(name, timerInterval) {
            this.timer = setInterval(() => {
                this.$refs?.[name]?.[0] && this.$refs[name][0].getFileData();
            }, timerInterval * 1000);
        },
        // 清理定时器
        clearPolling() {
            this.timer && clearInterval(this.timer);
        }
    }
};
</script>

<style lang="less" scoped>
@import url("@/assets/css/input.less");
@import url("@/assets/css/tab.less");
@import url("@/assets/css/menu.less");

.main {
    .main-top {
        min-width: 900px;
    }

    .container {
        height: calc(100% - 54px);
        border-radius: var(--border-radius);
        background: none;
        margin-top: 10px;

        /deep/ .h-tabs-nav-right {
            position: absolute;
            right: 0;
            top: 5px;
        }

        & > .apm-box {
            height: 100%;
            min-width: 900px;
            margin-top: 0;
            background: none;
        }

        /deep/ .right-box {
            background: none;
            padding: 0 10px;
        }

        /deep/ .h-tabs-bar {
            margin-bottom: 5px;
        }

        /deep/ .h-tabs-content-wrap {
            height: calc(100% - 50px);
        }
    }
}
</style>

