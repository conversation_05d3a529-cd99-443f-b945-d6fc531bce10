import { byteLength } from '@/utils/utils';

export function intValidate(rule, val, callback) {
    if (val.match(/^[ ]*$/)) return callback(new Error('输入不能为空'));
    if (isNaN(val) || val.search('e') !== -1) return callback(new Error('请输入数字类型'));
    if (Number(val) % 1 !== 0) return callback(new Error('请输入整型数字'));
    if (val < -2147483648 || val > 2147483647) {
        return callback(new Error('超出输入范围，输入范围在-2147483648 ~ 2147483647'));
    }
    return callback();
}
export function unsignedIntValidate(rule, val, callback) {
    if (val.match(/^[ ]*$/)) return callback(new Error('输入不能为空'));
    if (isNaN(val) || val.search('e') !== -1) return callback(new Error('请输入数字类型'));
    if (Number(val) % 1 !== 0) return callback(new Error('请输入整型数字'));
    if (val < 0 || val > 4294967295) {
        return callback(new Error('超出输入范围，输入范围在0 ~ 4294967295'));
    }
    return callback();
}
export function shortIntValidate(rule, val, callback) {
    if (val.match(/^[ ]*$/)) return callback(new Error('输入不能为空'));
    if (isNaN(val) || val.search('e') !== -1) return callback(new Error('请输入数字类型'));
    if (Number(val) % 1 !== 0) return callback(new Error('请输入整型数字'));
    if (val < -32768 || val > 32767) {
        return callback(new Error('超出输入范围，输入范围在-32768 ~ 32767'));
    }
    return callback();
}
export function shortUnsignedIntValidate(rule, val, callback) {
    if (val.match(/^[ ]*$/)) return callback(new Error('输入不能为空'));
    if (isNaN(val) || val.search('e') !== -1) return callback(new Error('请输入数字类型'));
    if (Number(val) % 1 !== 0) return callback(new Error('请输入整型数字'));
    if (val < 0 || val > 65535) {
        return callback(new Error('超出输入范围，输入范围在0 ~ 65535'));
    }
    return callback();
}
export function longIntValidate(rule, val, callback) {
    if (val.match(/^[ ]*$/)) return callback(new Error('输入不能为空'));
    if (isNaN(val) || val.search('e') !== -1) return callback(new Error('请输入数字类型'));
    if (Number(val) % 1 !== 0) return callback(new Error('请输入整型数字'));
    if (val < -(2 ** 63) || val > 2 ** 63 - 1) {
        return callback(new Error('超出输入范围，输入范围在-9223372036854775808 ~ 9223372036854775807'));
    }
    return callback();
}
export function longUnsignedIntValidate(rule, val, callback) {
    if (val.match(/^[ ]*$/)) return callback(new Error('输入不能为空'));
    if (isNaN(val) || val.search('e') !== -1) return callback(new Error('请输入数字类型'));
    if (Number(val) % 1 !== 0) return callback(new Error('请输入整型数字'));
    if (val < 0 || val > 2 ** 64 - 1) {
        return callback(new Error('超出输入范围，输入范围在0 ~ 18446744073709551615'));
    }
    return callback();
}
export function charValidate(rule, val, callback) {
    if (val.charCodeAt() > 256 || val.charCodeAt() < 0 || val.length > 1) return callback(new Error('所占字节数超出输入范围'));
    return callback();
}
export function floatValidate(rule, val, callback) {
    if (val.match(/^[ ]*$/)) return callback(new Error('输入不能为空'));
    if (isNaN(val) || val.search('e') !== -1) return callback(new Error('请输入数字类型'));
    if (val < -3.40282 * 10 ** 38 || val > 3.40282 * 10 ** 38) {
        return callback(new Error('超出输入范围，输入范围为-3.40282e38 ~ 3.40282e38'));
    }
    return callback();
}
export function doubleValidate(rule, val, callback) {
    if (val.match(/^[ ]*$/)) return callback(new Error('输入不能为空'));
    if (isNaN(val) || val.search('e') !== -1) return callback(new Error('请输入数字类型'));
    if (val < -1.79769 * 10 ** 308 || val > 1.79769 * 10 ** 308) {
        return callback(new Error('超出输入范围，输入范围为-1.79769e308 ~ 1.79769e308'));
    }
    return callback();
}
export function longDoubleValidate(rule, val, callback) {
    if (val.match(/^[ ]*$/)) return callback(new Error('输入不能为空'));
    if (isNaN(val) || val.search('e') !== -1) return callback(new Error('请输入数字类型'));
    if (val < -1.19 * Math.pow(10, 4932) || val > 1.19 * Math.pow(10, 4932)) {
        return callback(new Error('超出输入范围，输入范围为-1.19e4932 ~ 1.19e4932'));
    }
    return callback();
}
export function boolValidate(rule, val, callback) {
    if (isNaN(val)) return callback(new Error('请输入0或1'));
    if (val !== '0' && val !== '1') return callback(new Error('请输入0或1'));
    return callback();
}

export function validatePort(rule, value, callback) {
    const port = Number(value);
    if (isNaN(port) || port <= 0 || port > 65535) {
        return callback(new Error('请输入1到65535之间的端口号'));
    }
    callback();
};

export function validatePass(rule, value, callback){
    if (value === '') {
        callback(new Error('请填写密码'));
    } else {
        const passwordRegex = /^(?:(?=.*[a-zA-Z])(?=.*\d)(?=.*[!@$*_#]))[a-zA-Z\d!@$*_#]{8,20}$/;
        const res = passwordRegex.test(value);
        if (!res) {
            callback(new Error('至少要包含字母 (a~zA~Z)、数字(0~9)、特殊符号(!、$、#、@、*、_)3种字符,长度8~20位'));
        }
        callback();
    }
}

/**
 * 内存表数据校验规则
 * @param {String} type 字符类型
 * @param {Number} size 字符大小
 * @returns 校验方法
 */
export function matchValidate(type, size) {
    function stringValidate(rule, val, callback) {
        if (byteLength(val) > size - 1) {
            return callback(new Error('所占字节数超出输入范围'));
        } else {
            return callback();
        }
    }
    const condition = new Map([
        ['int', [{ test: intValidate, trigger: 'blur, change' }]],
        ['unsigned int', [{ test: unsignedIntValidate, trigger: 'blur, change' }]],
        ['short', [{ test: shortIntValidate, trigger: 'blur, change' }]],
        ['short int', [{ test: shortIntValidate, trigger: 'blur, change' }]],
        ['unsigned short', [{ test: shortUnsignedIntValidate, trigger: 'blur, change' }]],
        ['short unsigned int', [{ test: shortUnsignedIntValidate, trigger: 'blur, change' }]],
        ['long', [{ test: longIntValidate, trigger: 'blur, change' }]],
        ['long int', [{ test: longIntValidate, trigger: 'blur, change' }]],
        ['unsigned long', [{ test: longUnsignedIntValidate, trigger: 'blur, change' }]],
        ['long unsigned int', [{ test: longUnsignedIntValidate, trigger: 'blur, change' }]],
        ['long long', [{ test: longIntValidate, trigger: 'blur, change' }]],
        ['long long int', [{ test: longIntValidate, trigger: 'blur, change' }]],
        ['unsigned long long', [{ test: longUnsignedIntValidate, trigger: 'blur, change' }]],
        ['long long unsigned int', [{ test: longUnsignedIntValidate, trigger: 'blur, change' }]],
        ['float', [{ test: floatValidate, trigger: 'blur, change' }]],
        ['double', [{ test: doubleValidate, trigger: 'blur, change' }]],
        ['long double', [{ test: longDoubleValidate, trigger: 'blur, change' }]],
        ['bool', [{ test: boolValidate, trigger: 'blur, change' }]],
        ['char', [{ test: charValidate, trigger: 'blur, change' }]],
        ['signed char', [{ test: charValidate, trigger: 'blur, change' }]],
        ['unsigned char', [{ test: charValidate, trigger: 'blur, change' }]],
        ['char[]', [{ test: stringValidate, trigger: 'blur, change' }]]
    ]);
    const validateFunc = condition.get(type) || condition.get('char[]');
    return validateFunc;
}

/**
 * 限制字符长度校验方法
 * @param {Number} length 字符最大长度
 * @returns 校验方法
 */
export const stringLengthRule = function (length) {
    function stringValidate(_rule, value, callback) {
        if (value?.length > length) {
            return callback(new Error(`字符长度数不得超过${length}!`));
        }
        callback();
    }
    return stringValidate;
};

// 多Ip+Port地址校验方法
export const multipleAddressRule = function (_rule, values, callback) {
    if (values.length) {
        const regx = /^(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])\.(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])\.(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])\.(\d{1,2}|1\d\d|2[0-4]\d|25[0-5]):([0-9]|[1-9]\d|[1-9]\d{2}|[1-9]\d{3}|[1-5]\d{4}|6[0-4]\d{3}|65[0-4]\d{2}|655[0-2]\d|6553[0-5])(,(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])\.(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])\.(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])\.(\d{1,2}|1\d\d|2[0-4]\d|25[0-5]):([0-9]|[1-9]\d|[1-9]\d{2}|[1-9]\d{3}|[1-5]\d{4}|6[0-4]\d{3}|65[0-4]\d{2}|655[0-2]\d|6553[0-5]))*$/;
        if (!regx.test(values)) {
            return callback(new Error('使用IP:PORT形式，多个地址使用英文逗号分隔'));
        }

        const list = values.split(',');
        list.forEach((item, index) => {
            const listBackups = [...list];
            listBackups.splice(index, 1);
            if (listBackups.indexOf(item) > -1) {
                return callback(new Error(`${item}重复`));
            }
        });
    }
    callback();
};

// 管理功能校验方法
// eslint-disable-next-line max-params
export function matchMangementValidate (type, validateValues){
    if (!type) return [];

    function numValidate(rule, val, callback) {
        if (val && isNaN(val) || val.search('e') !== -1) return callback(new Error('请输入数字'));
        // if (val < validateValues.minValue || val > validateValues.maxValue) {
        //     return callback(new Error(`超出输入范围:${validateValues.minValue} ~ ${validateValues.maxValue}`));
        // }
        return callback();
    }

    function strValidate(rule, val, callback) {
        // if (validateValues.maxLen && val.length > validateValues.maxLen) {
        //     return callback(new Error(`超出字段长度：${validateValues.maxLen}`));
        // }
        return callback();
    }

    function boolValidate (rule, val, callback) {
        if (val && val !== 'true' && val !== 'false') return callback(new Error('布尔类型只支持true、false'));
        return callback();
    }

    function arrayValidate(rule, val, callback) {
        if (!val) return callback();

        try {
            const parsedVal = JSON.parse(val);

            // 检查是否为数组
            if (!Array.isArray(parsedVal)) {
                return callback(new Error('请输入有效的数组格式'));
            }

            // // 如果有最大长度限制
            // if (validateValues?.maxLength && parsedVal.length > validateValues.maxLength) {
            //     return callback(new Error(`数组长度不能超过${validateValues.maxLength}个元素`));
            // }

            // // 如果有最小长度限制
            // if (validateValues?.minLength && parsedVal.length < validateValues.minLength) {
            //     return callback(new Error(`数组长度不能少于${validateValues.minLength}个元素`));
            // }

            return callback();
        } catch (error) {
            return callback(new Error('请输入有效的数组格式'));
        }
    }

    function objectValidate(rule, val, callback) {
        if (!val) return callback();

        try {
            // 如果是字符串，尝试解析为JSON
            const parsedVal = JSON.parse(val);

            // 检查是否为对象且不是数组
            if (typeof parsedVal !== 'object' || parsedVal === null || Array.isArray(parsedVal)) {
                return callback(new Error('请输入有效的对象格式'));
            }

            // // 如果有最大属性数量限制
            // if (validateValues?.maxProperties && Object.keys(parsedVal).length > validateValues.maxProperties) {
            //     return callback(new Error(`对象属性数量不能超过${validateValues.maxProperties}个`));
            // }

            // // 如果有最小属性数量限制
            // if (validateValues?.minProperties && Object.keys(parsedVal).length < validateValues.minProperties) {
            //     return callback(new Error(`对象属性数量不能少于${validateValues.minProperties}个`));
            // }

            // 如果有必需属性检查
            // if (validateValues?.requiredProperties && Array.isArray(validateValues.requiredProperties)) {
            //     const missingProps = validateValues.requiredProperties.filter(prop => !(prop in parsedVal));
            //     if (missingProps.length > 0) {
            //         return callback(new Error(`缺少必需属性：${missingProps.join(', ')}`));
            //     }
            // }

            return callback();
        } catch (error) {
            return callback(new Error('请输入有效的对象格式'));
        }
    }

    const condition = new Map([
        ['number', [{ test: numValidate, trigger: 'blur, change' }]],
        ['string', [{ test: strValidate, trigger: 'blur, change' }]],
        ['bool', [{ test: boolValidate, trigger: 'blur, change' }]],
        ['array', [{ test: arrayValidate, trigger: 'blur, change' }]],
        ['object', [{ test: objectValidate, trigger: 'blur, change' }]]
    ]);
    const validateFunc = condition.get(type);
    return validateFunc;
}
