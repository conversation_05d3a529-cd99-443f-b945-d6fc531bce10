import './description.less';
import obsTitle from '@/components/common/title/obsTitle';
import description from '@/components/common/description/description';
export default {
    name: 'description-bar',
    components: { obsTitle, description },
    props: {
        data: {
            title: {
                type: Object,
                default: () => {}
            },
            details: {
                type: Array,
                default: () => []
            }
        }
    },
    data() {
        return {
        };
    },
    methods: {
        handleButtonClick(key) {
            this.$emit('button-click', key);
        },
        handleSelectChange(val, key) {
            this.$emit('select-change', val, key);
        },
        setSelectVal(key, val) {
            this.$refs['obs-title'].setSelectVal(key, val);
        }
    },
    render() {
        return <div class="descriptions-bar">
            {this.data?.title && <obs-title ref='obs-title'  title={this.data?.title}
                v-on:button-click={this.handleButtonClick}
                v-on:select-change={this.handleSelectChange}/>
            }
            {
                this.data?.details?.map(v => {
                    return <description title={v.label} type={v.type || 'description'} dataDic={v.dataDic} data={v.data} v-on:button-click={this.handleButtonClick}></description>;
                })
            }

        </div>;
    }
};
