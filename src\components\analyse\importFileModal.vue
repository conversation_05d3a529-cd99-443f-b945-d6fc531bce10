<!--
 * @Description:
 * @Author: <PERSON><PERSON>
 * @Date: 2023-03-20 15:40:01
 * @LastEditTime: 2023-03-20 17:55:59
 * @LastEditors: <PERSON>ale Ying
-->
<template>
    <div>
        <!-- 下发配置列表 -->
        <h-msg-box-safe
            v-model="modalData.status"
            :escClose="true"
            :mask-closable="false"
            title="导入场景配置文件"
            width="600"
        >
            <p style="color: var(--error-color); margin-left: 100px;">请确保当前场景已导出保存，新导入的场景将覆盖当前使用的场景信息！</p>
            <div style=" display: flex; justify-content: center; align-items: center;">
            <h-upload
                ref="upload"
                multiple type="drag"
                :action="url"
                :on-success="handleSuccess"
                :on-error="handleError">
                <div style="padding: 20px 10px;">
                    <h-icon name="upload" size="52" style="color: #39f;"></h-icon>
                    <p>点击或将文件拖拽到这里上传</p>
                </div>
            </h-upload>
            </div>
            <template v-slot:footer>
                <a-button @click="modalData.status = false">取消</a-button>
            </template>
        </h-msg-box-safe>
    </div>
</template>

<script>
import aButton from '@/components/common/button/aButton';
export default {
    props: {
        modalInfo: {
            type: Object,
            default: null
        }
    },
    data() {
        return {
            modalData: this.modalInfo,
            url: `${window['LOCAL_CONFIG']['API_HOME'] || '/'}/scene/test/upload`
        };
    },
    methods: {
        // 上传成功
        handleSuccess(response, file, fileList) {
            if (response.success) {
                location.reload();
            } else {
                this.$hMessage.error(response.message);
                this.$refs.upload.clearFiles();
            }
        },
        // 上传失败
        handleError(error, response, file) {
            this.$hMessage.error('文件内容不合法');
            this.$refs.upload.clearFiles();
        }
    },
    components: {
        aButton
    }
};
</script>
