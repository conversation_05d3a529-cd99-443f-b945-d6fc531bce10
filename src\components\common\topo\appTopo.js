import Vis from 'vis';
import _ from 'lodash';
import { v4 as uuidv4 } from 'uuid';
import { byteLength } from '@/utils/utils';
const w = 100, h = 100;
const whiteBg = {
    textColor: '#555',
    nodeColor: '#FFF',
    boxColor: '#ccc',
    fillColor: '#777',
    selectColor: '#4686f2'
};
const EDGES_COLOR = {
    red: {
        color: '#62CAFF',
        highlight: '#74E0ED',
        hover: '#74E0ED',
        inherit: 'from',
        opacity: 1.0
    },
    blue: {
        color: '#FF6E76',
        highlight: '#FF7B7B',
        hover: '#FF7B7B',
        inherit: 'from',
        opacity: 1.0
    },
    default: {
        color: '#C2C8D5',
        highlight: '#DCE3F1',
        hover: '#DCE3F1',
        inherit: 'from',
        opacity: 1.0
    }
};
const HIGHLIGHT_COLOR = {
    '#62CAFF': '#74E0ED',
    '#FF6E76': '#FF7B7B',
    '#C2C8D5': '#DCE3F1'
};
export default {
    name: 'topo',
    props: {
        isWhiteBg: {
            type: Boolean,
            default: false
        },
        template: { // 模型数据
            type: Object,
            default: {}
        },
        monitor: {  // 监控属性
            type: Boolean,
            default: false
        },
        analyse: {  // 分析属性
            type: Boolean,
            default: false
        },
        clickable: {    // 可点击
            type: Boolean,
            default: false
        },
        selectedGroup: {
            type: String,
            default: ''
        },
        hoveredGroup: {
            type: String,
            default: ''
        }
    },
    data() {
        return {
            hoverGroup: '',
            network: null,
            ctx: null,
            nodes: [],
            edges: [],
            options: {
                edges: {
                    width: 1,
                    length: 40,
                    shadow: true,
                    color: EDGES_COLOR['default'],
                    font: {
                        color: '#FFF',
                        size: 11, // px
                        strokeWidth: 0.5,
                        vadjust: -8
                    }
                    // smooth: {
                    //     enabled: true, 		  // 设置连线是直线还是湾线还是贝塞尔
                    // 	   type: 'cubicBezier',
                    //     roundness: 1,
                    //     forceDirection: false
                    // }
                },
                nodes: {
                    borderWidth: 1,
                    color: {
                        border: '#34577F',
                        background: '#262B40',
                        highlight: {
                            background: 'rgb(111, 114, 251)',
                            border: 'rgb(108, 59, 238)'
                        },
                        hover: {
                            background: 'rgb(111, 114, 251)',
                            border: 'rgb(108, 59, 238)'
                        }
                    },
                    font: {
                        color: '#fff'
                    },
                    shape: 'circle'
                },
                physics: {
                    enabled: false
                },
                interaction: {
                    dragNodes: false, // 是否能拖动节点
                    dragView: true, // 是否能拖动画布
                    hover: true, // 鼠标移过后加粗该节点和连接线
                    multiselect: false, // 按 ctrl 多选
                    selectable: true, // 是否可以点击选择
                    selectConnectedEdges: false, // 选择节点后是否显示连接线
                    hoverConnectedEdges: false, // 鼠标滑动节点后是否显示连接线
                    zoomView: true // 是否能缩放画布
                }
            },
            groups: [],
            alertStatus: {}
            // spanRelation: [],
            // rangeY: [0, 0], // 拓扑图高度范围
            // rangeLink: [], // 链路坐标范围
            // topHierarchy: [],
            // bottomHierarchy: [],
        };
    },
    methods: {
        init() {
            // 初始化白底option配置
            if (this.isWhiteBg) {
                this.options.edges.font.color = whiteBg.textColor;
                this.options.nodes.color.background = whiteBg.nodeColor;
                this.options.nodes.font.color = whiteBg.textColor;
            }
            this.destroy();
            const linkObj = this.template;
            if (!linkObj || !Object.keys(linkObj).length) return;
            this.nodes = [...this.generatePoint(linkObj)?.nodes];
            this.groups = [...this.generatePoint(linkObj)?.groups];
            // 制造边界假点
            const maxX = Math.max.apply(Math, this.nodes.map(o => { return o.x; }));
            const maxY = Math.max.apply(Math, this.nodes.map(o => { return o.y; }));
            const minX = Math.min.apply(Math, this.nodes.map(o => { return o.x; }));
            const minY = Math.min.apply(Math, this.nodes.map(o => { return o.y; }));
            const borders = [false, false]; // 0 为左上边界，1 为右下边界
            this.nodes.forEach(item => {
                if (item.x === minX && item.y === minY) {
                    borders[0] = true;
                    item.hidden = false; // 原假点放开
                }
                if (item.x === maxX && item.y === maxY) {
                    borders[1] = true;
                    item.hidden = false; // 原假点放开
                }
            });
            // 遍历生成假点定位
            borders.forEach((ele, idx) => {
                const point = {
                    id: uuidv4(),
                    label: '',
                    borderWidth: 0,
                    opacity: 0,
                    chosen: false,
                    hidden: false
                };
                if (!ele) {
                    point.x = idx ? maxX : minX;
                    point.y = idx ? maxY : minY;
                    this.nodes.push(point);
                }
            });
            this.edges = this.generateEdges(linkObj.edges || []);
            this.drawCanvas();
        },
        generatePoint(linkObj) {
            const unit = 100;
            const nodes = [];
            const groups = [];
            linkObj.groupRelations.forEach((item, index) => {
                const [x, y] = ['x', 'y'];
                const minObj = new Map([
                    [x, (item.column - 1) * unit],
                    [y, (item.row - 1) * unit]
                ]);
                const pointMap = this.calculatePointPosition(item.pointNum, minObj);
                item.nodeIds.forEach((ele, index) => {
                    let point;
                    if (ele) {
                        point = {
                            id: ele,
                            label: _.find(linkObj.nodes, ['id', ele]).name,
                            groups: item.groupId,
                            hidden: false,
                            chosen: false
                        };

                    } else {
                        point = {
                            id: uuidv4(),
                            label: '',
                            groups: item.groupId,
                            borderWidth: 0,
                            // opacity: 0,
                            chosen: false,
                            hidden: true
                        };
                    }
                    point = { ...point, ...pointMap.get(index) };
                    nodes.push(point);
                });
                const name = _.find(linkObj.groups, ['id', item.groupId]).name;
                groups.push({
                    pointNum: item.pointNum,
                    min: minObj,
                    name,
                    selected: this.selectedGroup === name,
                    attributes: linkObj.groups[index].attributes
                });
            });
            return { nodes, groups };
        },
        // 计算自定义组点个数中各个点位置（支持个数4,6,8...）
        calculatePointPosition(pointNum, minObj) {
            const unit = 100;
            const pointMap = new Map();
            for (let i = 0; i < pointNum; i++) {
                const incrementX = i < pointNum / 2 ? unit * i : unit * (pointNum - 1 - i);
                const incrementY = i < pointNum / 2 ? 0 : unit;
                const obj = {
                    x: minObj.get('x') + incrementX,
                    y: minObj.get('y') + incrementY
                };
                pointMap.set(i, obj);
            }
            return pointMap;
        },
        drawCanvas() {
            const networkData = {
                nodes: this.nodes,
                edges: this.edges
            };
            this.$options.network = new Vis.Network(
                this.$refs['model-box'],
                networkData,
                this.options
            );

            if (this.clickable) {
                const cvs = this.$refs['model-box'].childNodes[0].canvas;
                cvs.onmousemove = (option) => {
                    const domX = option.offsetX, domY = option.offsetY;
                    const { x: canvasX, y: canvasY } = this.$options.network.DOMtoCanvas({ x: domX, y: domY });
                    this.hoverGroup = '';
                    for (const ele of this.groups) {
                        const groupX = ele.min.get('x'), groupY = ele.min.get('y');
                        if (canvasX > groupX && canvasX < groupX + (ele.pointNum / 2 - 1) * w && canvasY > groupY && canvasY < groupY + h) {
                            this.$emit('on-hover', ele.name);
                            this.hoverGroup = this.hoveredGroup;
                        }
                    }
                    this.$options.network.redraw();
                };
            }

            this.$options.network.on('click', (params) => {
                if (params.edges.length && this.$options.network.getSelectedEdges().length) this.selectEdge(params);
                // 是否支持组可点击
                if (this.clickable) {
                    const pos = params.pointer.canvas;
                    for (const ele of this.groups) {
                        const groupX = ele.min.get('x'), groupY = ele.min.get('y');
                        if (pos.x > groupX && pos.x < groupX + (ele.pointNum / 2 - 1) * w && pos.y > groupY && pos.y < groupY + h) {
                            this.$emit('on-click', ele.name);
                            break;
                        }
                    }
                }
            });

            this.$options.network.on('afterDrawing', (param) => {
                const cvs = this.$refs['model-box'].childNodes[0].canvas;
                const ctx = cvs.getContext('2d');
                this.groups.forEach(ele => {
                    let name = this.$store?.state?.apmDirDesc?.appTypeDictDesc?.[ele.name] || ele.name;
                    if (Array.isArray(ele?.attributes?.spanGroup)) {
                        for (const item of ele.attributes.spanGroup) {
                            if (this.alertStatus?.[item.spanName]) {
                                name = '⚠️' + name;
                                break;
                            }
                        }
                    }

                    this.isDrowGaphical(ctx, {
                        widthMultiple: ele.pointNum / 2 - 1,
                        minX: ele.min.get('x'),
                        minY: ele.min.get('y'),
                        name: name,
                        selected: ele.name === this.selectedGroup,
                        hovered: ele.name === this.hoverGroup
                    });
                });
            });
        },
        // 生成edges
        generateEdges(edges, type = true) {
            const list = [];
            edges.forEach(ele => {
                // 判断是否需要display，需要的话就判断
                if (type ? ele.display : true) {
                    const obj = {
                        from: ele.from,
                        to: ele.to,
                        label: '',
                        arrows: {
                            to: {
                                enabled: ele.style.withArrows
                            }
                        },
                        chosen: false,
                        dashes: ele.style?.lineType === 'dotted',
                        attributes: ele?.attributes,
                        color: EDGES_COLOR[ele.style?.lineColour || 'default']
                    };
                    list.push(obj);
                }
            });
            return list;
        },
        // 画线框
        isDrowGaphical(ctx, obj) {
            const realityWidth = obj.widthMultiple * w;
            const boxColor = this.isWhiteBg ? whiteBg.boxColor : 'rgba(52,141,249, .6)';
            const fillColor = this.isWhiteBg ? whiteBg.fillColor : '#fff';
            const selectColor = this.isWhiteBg ? whiteBg.selectColor : '#74E0ED';

            const shadowColor = obj.hovered ? 'rgba(52, 141, 249, 1)' : obj.selected ? selectColor : boxColor;
            this.roundRectInnerShadow(ctx, { x: obj.minX, y: obj.minY, w: realityWidth, h, r: 10, shadowColor, shadowBlur: 20, lineWidth: 12 });

            this.roundRect(ctx, { x: obj.minX, y: obj.minY, w: realityWidth, h, r: 5 });

            ctx.fillStyle = obj.selected ? selectColor : fillColor;
            ctx.strokeStyle = fillColor;
            ctx.lineWidth = 5;
            ctx.font = '14px orbitron';
            ctx.fillText(obj.name, obj.minX + (realityWidth / 2 - byteLength(obj.name) * 3.55), obj.minY + h / 2);
        },
        // canvas圆角
        roundRect(ctx, data) {
            const { x, y, w, h, r } = { ...data };
            ctx.beginPath();
            ctx.moveTo(x + r, y);

            // 右上角弧线
            ctx.arcTo(x + w, y, x + w, y + r, r);

            // 右下角弧线
            ctx.arcTo(x + w, y + h, x + w - r, y + h, r);

            // 左下角弧线
            ctx.arcTo(x, y + h, x, y + h - r, r);

            // 左上角弧线
            ctx.arcTo(x, y, x + r, y, r);
        },
        roundRectInnerShadow(ctx, data) {
            const { x, y, w, h, r, shadowColor, shadowBlur, lineWidth } = { ...data };
            const r1 = r || 5; // 圆角半径
            const shadowColor1 = shadowColor || '#00f'; // 阴影颜色
            const lineWidth1 = lineWidth || 20; // 边框越大，阴影越清晰
            const shadowBlur1 = shadowBlur || 30; // 模糊级别，越大越模糊，阴影范围也越大。

            ctx.save();
            ctx.beginPath();

            // 裁剪区(只保留内部阴影部分)
            this.roundRect(ctx, { x, y, w, h, r: r1 });
            ctx.clip();

            ctx.strokeStyle = 'rgba(52,141,249,0.9)';
            // 边框+阴影
            ctx.beginPath();
            ctx.lineWidth = lineWidth1;
            ctx.shadowColor = shadowColor1;
            ctx.shadowBlur = shadowBlur1;
            // 因线是由坐标位置向两则画的，所以要移动起点坐标位置，和加大矩形。
            this.roundRect(ctx, { x: x - lineWidth1 / 2, y: y - lineWidth1 / 2, w: w + lineWidth1, h: h + lineWidth1, r: r1 });
            ctx.stroke();

            // 取消阴影
            ctx.shadowBlur = 0;

            ctx.restore();
        },
        // 手动更新edge上的数据
        handleUpdateEdgeLabel(param, alertStatus) {
            this.alertStatus = alertStatus;
            if (this.$options.network) {
                const edg = this.$options.network.body.data.edges;
                const allEdges = edg.get();
                const edges = this.generateEdges(this?.template?.edges || [], false);
                const bool = allEdges.length === edges.length;
                const selectedList = _.find(this.groups, ['name', this.selectedGroup])?.attributes?.spanGroup || [];
                const operationList = _.map(selectedList, 'spanName');
                if (bool) {
                    allEdges.forEach(ele => {
                        const key = ele?.attributes?.spanName || '';
                        ele.label = param[key] || '';
                        ele.arrows.to.enabled = ele.label ? false : ele.arrows.to.enabled;
                        if (operationList.indexOf(key) > -1) {
                            ele.color = {
                                color: HIGHLIGHT_COLOR[ele?.color?.color] || ele?.color?.color
                            };
                        } else {
                            ele.color = {
                                color: ele?.color?.color
                            };
                        }
                        Object.entries(this.generateEdgeStyle(alertStatus[key], ele.color.color)).forEach(item => {
                            ele[item[0]] = item[1];
                        });
                    });
                    edg.update(allEdges);
                } else {
                    edges.forEach(ele => {
                        const key = ele?.attributes?.spanName || '';
                        ele.label = param[key] || '';
                        ele.arrows.to.enabled = ele.label ? false : ele.arrows.to.enabled;
                    });
                    this.edges = edges;
                    this.drawCanvas();
                }
            }
        },
        // 边选中事件监听
        selectEdge(param) {
            this.$emit('selectEdge', param, this.edges);
        },
        // 更改edge样式
        generateEdgeStyle(type, color) {
            return {
                width: type ? 2 : 1,
                color: {
                    color: type ? '#FDC30A' : color
                }
            };
        },
        // 销毁
        destroy() {
            this.$options.network && this.$options.network.destroy();
        }
    },
    mounted() {
        this.init();
        window.addEventListener('resize', this.init);
    },
    beforeDestroy() {
        window.removeEventListener('resize', this.init);
    },
    render() {
        return <div ref="model-box" style="width: 100%; height: 100%; cursor: pointer;"></div>;
    },
    watch: {
        template: {
            handler(newVal, oldVal) {
                if (this.monitor) return; // 如果是监控的话必须通过手动调用
                this.init();
            },
            deep: true
        }
    },
    network: null // 放在这，避免响应式化
};
