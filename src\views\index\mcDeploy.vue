<template>
    <div class="main">
        <div class="title">
            <a-title title="MC3.0配置管理">
                <slot>
                    <h-select
                        v-show="productList.length > 1"
                        v-model="productInstNo"
                        placeholder="请选择"
                        class="title-select"
                        placement="bottom"
                        :positionFixed="true"
                        :clearable="false" @on-change="checkProduct">
                        <h-option
                            v-for="item in productList" :key="item.id"
                            :value="item.productInstNo">{{ item.productName }}
                        </h-option>
                    </h-select>
                </slot>
            </a-title>
        </div>
        <a-loading v-if="loading"></a-loading>

        <div v-if="menuList.length" class="container">
            <menu-layout ref="menu" :menuList="menuList" menuItemId="id" titleAttribute="clusterName"
                menuTitle="已托管MC3.0集群" @check-menu="checkModel">
                <template v-slot:right>
                    <h-tabs
                        v-model="tabName"
                        class="product-box"
                        @on-click="tabClick(tabName)">
                        <h-tab-pane label="主题" name="mcTopic">
                            <mc-topic-deploy ref="mcTopic" :nodeData="menu"></mc-topic-deploy>
                        </h-tab-pane>
                    </h-tabs>
                </template>
            </menu-layout>
        </div>
        <div v-else style="height: calc(100% - 110px);">
            <no-data></no-data>
        </div>
    </div>
</template>

<script>
import _ from 'lodash';
// import { formatDate } from '@/utils/utils';
import { mapState, mapActions } from 'vuex';
import { getProductClusters } from '@/api/productApi';
import aTitle from '@/components/common/title/aTitle';
import noData from '@/components/common/noData/noData';
import aLoading from '@/components/common/loading/aLoading';
import menuLayout from '@/components/common/menuLayout/menuLayout';
import mcTopicDeploy from '@/components/mcDeploy/mcTopicDeploy.vue';
export default {
    components: { aTitle, aLoading, noData, menuLayout, mcTopicDeploy },
    data() {
        return {
            productInstNo: '',
            productInfo: {},
            tabName: 'mcTopic', // tab默认选择
            menu: {},
            menuList: [],
            loading: false
        };
    },
    mounted() {
        this.init();
    },
    beforeDestroy() {
    },
    computed: {
        ...mapState({
            productList: (state) => {
                return state.product.productListLight || [];
            }
        })
    },
    methods: {
        ...mapActions({ getProductList: 'product/getProductListLight' }),
        // 初始化页面
        async init() {
            try {
                this.loading = true;
                await this.getProductList({ filter: 'includeMCCluster' });
                const productInstNo = localStorage.getItem('productInstNo');
                this.productInstNo = _.find(this.productList, ['productInstNo', productInstNo])?.productInstNo || this.productList?.[0]?.productInstNo;
                setTimeout(() => {
                    this.loading = false;
                }, 100);
            } catch (e) {
                this.loading = false;
                console.error(e);
            }
        },
        // 手动清空数据
        clearData() {
            this.menu = {};
            this.menuList = [];
            this.productInfo = {};
        },
        // 切换产品
        async checkProduct(val) {
            this.clearData();
            this.loading = true;
            if (this.productList.length) {
                this.loading = true;
                this.productInfo = val ? _.find(this.productList, ['productInstNo', val]) : this.productList[0];
                localStorage.setItem('productInstNo', this.productInfo.productInstNo);
                await this.getProductClusters();
                this.menuList?.[0]?.id && this.$refs['menu'].initMenu(this.menuList[0]);
            }
            setTimeout(() => {
                this.loading = false;
            }, 100);
        },
        // 获取mc应用集群信息
        async getProductClusters() {
            const res = await getProductClusters({
                productId: this.productInfo.id,
                clusterInstanceType: 'mc'
            });
            if (res.code === '200') {
                this.menuList = res?.data?.appClusters;
            }
        },
        // 切换导航产品
        checkModel(item) {
            this.menu = item;
            this.$nextTick(() => {
                this.tabClick(this.tabName);
            });
        },
        // 切换tab
        tabClick(name) {
            this.$nextTick(() => {
                this.$refs?.[name]?.[0] && this.$refs[name][0].initData();
            });
        }
    }
};
</script>

<style lang="less" scoped>
@import url("@/assets/css/tab.less");
@import url("@/assets/css/menu.less");

.main {
    .title {
        min-width: 900px;

        p {
            display: inline-block;
            color: var(--font-color);
            font-size: var(--title-font-size);
            position: absolute;
            left: 50%;
            transform: translateX(-50%);
        }

        .title-select {
            float: right;
            margin-right: 4px;
            top: 5px;
            min-width: 200px;
            width: auto;
        }
    }

    .container {
        height: calc(100% - 60px);
        border-radius: var(--border-radius);
        background: none;
        margin-top: 10px;

        /deep/ .h-tabs-nav-wrap {
            float: none !important;
        }

        /deep/ .h-tabs-nav-right {
            position: absolute;
            right: 0;
            top: 5px;
        }

        & > .apm-box {
            height: 100%;
            min-width: 900px;
            margin-top: 0;
            background: none;
        }

        /deep/ .right-box {
            background: none;
            padding: 0 10px;
        }

        /deep/ .h-tabs-bar {
            margin-bottom: 5px;
        }

        /deep/ .h-tabs-content-wrap {
            height: calc(100% - 50px);
        }
    }
}
</style>

