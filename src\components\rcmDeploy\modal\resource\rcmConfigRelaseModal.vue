
<template>
    <div>
        <h-msg-box-safe v-model="modalData.status" :escClose="true" :mask-closable="false" :closable="!loading" :title="modalData.title" width="400" :height="isPublish ? 180 : 130">
            <div class="modal-box">
                <p>您确认要{{ isPublish ? '发布' : '还原'}}配置吗？</p>
                <p v-if="isPublish">本地版本：{{ modalData.localVersion || '-' }} --> 远程版本：{{ modalData.remoteVersion || '-' }}</p>
                <p v-else>
                    {{modalData.historyVersion ? '历史' : '远程'}}版本：{{ modalData.historyVersion || modalData.remoteVersion || '-' }} --> 本地版本：{{ modalData.localVersion || '-' }}
                </p>
                <h-row v-if="isPublish" style="margin-top: 8px;">
                    <h-col span="3">
                        <span>备注：</span>
                    </h-col>
                   <h-col span="21">
                        <h-input v-model="remark" :rows="4" type="textarea" :canResize="false" :maxlength="500"/>
                   </h-col>
                </h-row>
            </div>
            <template v-slot:footer>
                <h-button :loading="loading" type="primary" @click="submitConfig">{{loading ? `执行${isPublish ? '发布' : '还原'}中...` : '确定'}}</h-button>
                <h-button :disabled="loading" @click="modalData.status = false">取消</h-button>
            </template>
        </h-msg-box-safe>
    </div>
</template>

<script>
import { rcmConfigRelase, rcmHistoryResume } from '@/api/rcmApi';
export default ({
    props: {
        modalInfo: {
            type: Object,
            default: null
        }
    },
    data() {
        return {
            modalData: this.modalInfo,
            loading: false,
            remark: null
        };
    },
    methods: {
        // 配置发布
        async rcmConfigRelase() {
            this.loading = true;
            const res = await rcmConfigRelase({
                id: this.modalData.rcmId,
                instanceId: this.modalData.instanceId,
                remark: this.remark
            });
            this.loading = false;
            if (res.code === '200') {
                this.$hMessage.success({
                    content: 'RCM配置发布成功',
                    duration: 3
                });
                this.modalData.status = false;
                this.$emit('init');
            } else if (res.code.length === 8) {
                this.$hMsgBoxSafe.error({
                    title: '发布失败',
                    width: 600,
                    content: `<pre style="white-space: pre-wrap;">${res.message}</pre>`
                });
            }
        },
        // 配置还原
        async rcmHistoryResume() {
            this.loading = true;
            const res = await rcmHistoryResume({
                id: this.modalData.rcmId,
                targetId: this.modalData.contrastId,
                remark: this.remark
            });
            this.loading = false;
            if (res.code === '200') {
                this.$hMessage.success({
                    content: 'RCM配置还原成功',
                    duration: 3
                });
                this.modalData.status = false;
                this.$emit('init');
            }
        },
        submitConfig() {
            if (this.isPublish) {
                this.rcmConfigRelase();
            } else {
                this.rcmHistoryResume();
            }
        }
    },
    computed: {
        isPublish() {
            return this.modalData.title === '配置发布';
        }
    }
});
</script>
<style lang="less" scoped>
.modal-box {
    height: auto;

    & > p {
        line-height: 24px;
        color: #555;
        font-size: 13px;
    }
}
</style>
