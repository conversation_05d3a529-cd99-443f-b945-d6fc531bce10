/**
 * 核心功能号api
 */
import fetch from './fetch';
import { objectToQueryString } from '@/utils/utils';

const prefixV1 = window['LOCAL_CONFIG']['API_HOME'] || '/';
const prefixV2 = window['LOCAL_CONFIG']['API_HOME2'] || '/';
/**
 * 查询观测dashboard tag
 */
export function queryDashbordsTags(param) {
    return fetch().get(`${prefixV1}/observation/dashboards/tags?${objectToQueryString(param)}`);
}

/**
 * 观测dashboard配置_V2
 */
export function queryDashbordsConfig(param) {
    return fetch().get(`${prefixV2}/observation/dashboards/configs?${objectToQueryString(param)}`);
}

/**
 *  根据分类查询功能号列表
 */
export function queryCoreFuncList(param) {
    return fetch().get(`${prefixV1}/product/observation/function-numbers?${objectToQueryString(param)}`);
}

/**
 *  查询功能号列表-设置抽屉
 */
export function queryCoreFuncs(param) {
    return fetch().get(`${prefixV1}/product/observation/function-numbers/settings?${objectToQueryString(param)}`);
}

/**
 * 设置功能号是否展示
 */
export function setFuncEnable(param) {
    return fetch().post(`${prefixV1}/product/observation/function-numbers/settings`, param);
}
