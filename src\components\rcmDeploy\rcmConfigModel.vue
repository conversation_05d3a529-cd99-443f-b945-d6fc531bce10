<!--
 * @Description:
 * @Author: <PERSON><PERSON>
 * @Date: 2023-06-12 10:42:02
 * @LastEditTime: 2023-08-11 10:27:48
 * @LastEditors: <PERSON><PERSON>
-->
<template>
    <div class="rcm-config-box">
        <div class="config-line">
            <span class="model-title">主题模板</span>
            <div class="model-tags">
                <tag-list
                    ref="topic-tags"
                    tagSize="small"
                    keyName="key"
                    :queryList="transportTempList"
                    :selectedId="selectedId"
                    @on-click="name => handleClick('transport', name)"
                    @on-close="name => handleClose('transport', name)" />
            </div>
            <a-button type="dark" @click="openCreateTempModal('topic')">创建模板</a-button>
        </div>
        <div class="config-line">
            <span class="model-title">通用上下文模板</span>
            <div class="model-tags">
                <tag-list
                    ref="single-tags"
                    keyName="key"
                    tagSize="small"
                    :queryList="singleCtxList"
                    :selectedId="selectedId"
                    @on-click="name => handleClick('singletonContext', name)"
                    @on-close="name => handleClose('singletonContext', name)" />
            </div>
            <a-button type="dark" @click="openCreateTempModal('normal')">创建模板</a-button>
        </div>
        <div class="config-line">
            <span class="model-title">集群上下文模板</span>
            <div class="model-tags">
                <tag-list
                    ref="cluster-tags"
                    keyName="key"
                    tagSize="small"
                    :queryList="clusterCtxList"
                    :selectedId="selectedId"
                    @on-click="name => handleClick('clusterContext', name)"
                    @on-close="name => handleClose('clusterContext', name)" />
            </div>
            <a-button type="dark" @click="openCreateTempModal('cluster')">创建模板</a-button>
        </div>
        <div class="config-detail-wrapper">
            <div class="wrapper-left">
                <a-title title="继承关系" />
                <div class="rcm-topo-box">
                    <rcm-inherit-topo v-if="selectedId" ref="rcmTopo" />
                    <no-data v-else text="未选中模板" />
                </div>
            </div>
            <div class="wrapper-right">
                <a-title title="配置详情">
                    <slot>
                        <a-button
                            type="dark"
                            :loading="loading"
                            style="position: absolute; top: 5px; right: 10px;"
                            @click="handleSave">保存</a-button>
                    </slot>
                </a-title>
                <div v-if="selectedId" class="json-box">
                    <div>
                        <h-form ref="formValidate" :model="formData" :label-width="160" cols="2">
                            <h-form-item label="模板名称:" prop="name" required>
                                <h-input v-model.trim="formData.name" :maxlength="50" disabled></h-input>
                            </h-form-item>
                            <h-form-item label="继承模板:" prop="ref">
                                <h-select v-model="formData.ref" placeholder="请选择" :positionFixed="true" :disabled="formData.name ==='Default'" @on-change="handleChange">
                                    <h-option v-for="item in refList" :key="item.name" :value="item.name" :disabled="name === item.name">{{ item.name }}</h-option>
                                </h-select>
                            </h-form-item>
                        </h-form>
                        <theme-setting v-if="type === 'transport'" ref="transport" :saveValidateData="formData" />
                        <cluster-setting v-if="type === 'clusterContext'" ref="clusterContext" :rcmId="rcmId" :saveValidateData="formData" />
                        <normal-setting v-if="type === 'singletonContext'" ref="singletonContext" :rcmId="rcmId" :saveValidateData="formData" :hasAppName="false" />
                    </div>
                </div>
                <no-data v-else class="json-box" text="暂无配置数据" />
            </div>
        </div>
        <create-template-modal v-if="modalInfo.status" :rcmId="rcmId" :modalInfo="modalInfo" @refresh="initData" />
        <a-loading v-if="loading1" />
    </div>
</template>
<script>
import _ from 'lodash';
import tagList from '@/components/common/tag/tagList';
import aButton from '@/components/common/button/aButton';
import aTitle from '@/components/common/title/aTitle';
import noData from '@/components/common/noData/noData';
import rcmInheritTopo from '@/components/common/topo/rcmInheritTopo';
import createTemplateModal from '@/components/rcmDeploy/modal/template/createTemplateModal.vue';
import clusterSetting from '@/components/rcmDeploy/modal/clusterSetting';
import normalSetting from '@/components/rcmDeploy/modal/normalSetting';
import themeSetting from '@/components/rcmDeploy/modal/themeSetting';
import { mapState, mapActions } from 'vuex';
import aLoading from '@/components/common/loading/aLoading';
import { getRcmTempDetail, createTopicTemp, createContextTemp, delRcmTemp } from '@/api/rcmApi';
export default {
    name: 'RcmConfigModel',
    props: {
        rcmId: {
            type: String,
            default: ''
        }
    },
    data() {
        return {
            selectedId: '',
            modalInfo: {
                type: '',
                status: false,
                list: []
            },
            name: '',   // 模板名单独保存
            type: '',
            formData: {},
            loading: false,
            loading1: false,
            isChange: false
        };
    },
    computed: {
        ...mapState({
            transportTempList: state => {
                const list = [];
                state.rcm.transportTempList.forEach(ele => {
                    const item = _.cloneDeep(ele);
                    item.key = 'transport-' + ele.name;
                    if (ele.name === 'Default') item.closable = false;
                    list.push(item);
                });
                return list;
            },
            singleCtxList: state => {
                const list = [];
                state.rcm.singleCtxList.forEach(ele => {
                    const item = _.cloneDeep(ele);
                    item.key = 'single-' + ele.name;
                    if (ele.name === 'Default') item.closable = false;
                    list.push(item);
                });
                return list;
            },
            clusterCtxList: state => {
                const list = [];
                state.rcm.clusterCtxList.forEach(ele => {
                    const item = _.cloneDeep(ele);
                    item.key = 'cluster-' + ele.name;
                    if (ele.name === 'Default') item.closable = false;
                    list.push(item);
                });
                return list;
            }
        }),
        // 引用
        refList: function() {
            return this.type === 'transport' ? this.transportTempList : _.uniqBy([...this.singleCtxList, ...this.clusterCtxList], item => item.name);
        }
    },
    watch: {
        rcmId(newVal) {
            this.resetData();
        }
    },
    mounted() {
    },
    methods: {
        ...mapActions({ getRcmProductTemplates: 'rcm/getRcmProductTemplates' }),
        resetData() {
            this.selectedId = '';
            this.type = '';
            this.$refs['rcmTopo'] && this.$refs['rcmTopo'].destroy();
            this.resetScroll();
        },
        async initData() {
            this.loading1 = true;
            await this.getRcmProductTemplates({
                id: this.rcmId,
                templateTypes: 'transport,singletonContext,clusterContext'
            });
            this.loading1 = false;
            this.resetScroll();
        },
        // 重置滚动条
        resetScroll() {
            this.$nextTick(() => {
                this.$refs['topic-tags'] && this.$refs['topic-tags'].scrollReset();
                this.$refs['single-tags'] && this.$refs['single-tags'].scrollReset();
                this.$refs['cluster-tags'] && this.$refs['cluster-tags'].scrollReset();
                this.selectedId && this.handleClick(this.type, this.selectedId);
            });
        },
        // 切换继承模板下拉
        async handleChange(e) {
            if (!e || this.isChange) return;
            const { data } = await getRcmTempDetail({
                id: this.rcmId,
                templateType: this.type,
                name: e
            });
            const result = data.template[this.type];
            delete result.ref;
            delete result.name;
            for (const i in result) {
                if (Object.prototype.hasOwnProperty.call(result, i)) {
                    this.$set(this.formData, i, result[i]);
                }
            }
            this.$refs[this.type].init();
        },
        // 点击模板
        async handleClick(type, name) {
            this.isChange = true;
            this.selectedId = name;
            const arr = name.split('-');
            arr.shift();
            const keyName = arr.join('-');
            this.name = keyName;
            this.type = type;
            const { data } = await getRcmTempDetail({
                id: this.rcmId,
                templateType: type,
                name: keyName
            });
            this.formData = {};
            if (data) {
                const result = data?.template ? { ...data.template[type] } : {};
                for (const key in result) {
                    if (Object.prototype.hasOwnProperty.call(result, key)) {
                        this.$set(this.formData, key, result[key]);
                    }
                }
                this.$nextTick(() => {
                    this.$refs[type].init();
                    data?.viewMode && this.$refs['rcmTopo'].init(data.viewMode, keyName);
                });
            }
            setTimeout(() => {
                this.isChange = false;
            }, 1500);
        },
        // 删除模板
        handleClose(type, name) {
            const obj = {
                transport: '主题',
                singletonContext: '通用上下文',
                clusterContext: '集群上下文'
            };
            const arr = name.split('-');
            arr.shift();
            const keyName = arr.join('-');
            this.$hMsgBoxSafe.confirm({
                title: `删除`,
                content: `您确定删除名为 ${keyName} 的${obj[type]}模板？`,
                onOk: async () => {
                    const res = await delRcmTemp({
                        id: this.rcmId,
                        templateType: type,
                        name: keyName
                    });
                    if (res.code === '200') {
                        this.$hMessage.success('删除成功');
                        if (name === this.selectedId) this.selectedId = '';
                        this.initData();
                    }
                }
            });

        },
        // 配置保存
        handleSave() {
            this.$refs['formValidate'].validate((valid) => {
                const result = this.$refs[this.type]?.getFileData();
                if (valid && result) {
                    this.loading = true;
                    if (this.type === 'transport') {
                        this.updateTopicTemp(result);
                    } else {
                        this.updateCtxTemp(result);
                    }
                }
            });

        },
        // 打开弹窗
        openCreateTempModal(name) {
            this.modalInfo.type = name;
            this.modalInfo.status = true;
            this.modalInfo.list = name === 'topic' ? this.transportTempList : _.uniqBy([...this.singleCtxList, ...this.clusterCtxList], item => item.name);
        },
        // 更新主题模板
        async updateTopicTemp(result) {
            const param = { ...this.formData, ...result, oldName: this.name, rcmId: this.rcmId };
            try {
                const res = await createTopicTemp(param);
                if (res.code === '200') {
                    this.selectedId = this.type + '-' + param.name;
                    this.initData();
                    this.$hMessage.success('主题模板更新成功');
                }
                this.loading = false;
            } catch (err) {
                this.loading = false;
                console.log(err);
            }

        },
        // 更新上下文模板
        async updateCtxTemp(result) {
            try {
                const param = {
                    ...this.formData,
                    ...result,
                    oldName: this.name,
                    rcmId: this.rcmId,
                    mode: this.type === 'singletonContext' || this.name === 'Default' ? 'singleton' : 'cluster' };
                const res = await createContextTemp(param);
                if (res.code === '200') {
                    this.selectedId = (this.type === 'clusterContext' ? 'cluster' : 'single') + '-' + param.name;
                    this.initData();
                    this.$hMessage.success('上下文模板更新成功');
                }
                this.loading = false;
            } catch (err) {
                this.loading = false;
                console.log(err);
            }
        }
    },
    components: { tagList, aLoading, aButton, noData, aTitle, rcmInheritTopo, clusterSetting, normalSetting, themeSetting, createTemplateModal }
};
</script>

<style lang="less" scoped>
@import url("@/assets/css/input.less");

.rcm-config-box {
    width: 100%;
    height: 100%;

    .config-line {
        display: flex;
        align-items: center;
        margin-bottom: 12px;
        height: 32px;

        & > .model-title {
            display: inline-block;
            width: 125px;
            padding-left: 15px;
            color: #fff;
        }

        & > .model-tags {
            display: inline-block;
            position: relative;
            width: calc(100% - 250px);
            height: 100%;
        }
    }

    .config-detail-wrapper {
        display: flex;
        width: 100%;
        height: calc(100% - 140px);

        & > .wrapper-left {
            width: 40%;
            height: 100%;
            margin-right: 10px;

            & > .rcm-topo-box {
                width: 100%;
                height: calc(100% - 40px);
            }
        }

        & > .wrapper-right {
            width: 60%;
            height: 100%;

            & > .json-box {
                height: calc(100% - 40px);
                overflow: auto;
                padding-top: 10px;

                & > div {
                    min-width: 500px;
                }

                /deep/ .h-form-item-label {
                    color: #fff;
                }
            }
        }
    }
}
</style>
