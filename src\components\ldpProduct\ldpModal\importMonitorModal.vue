<!--
 * @Description: 导入监控规则弹窗
 * @Author: <PERSON><PERSON>
 * @Date: 2022-03-08 10:28:21
 * @LastEditTime: 2023-04-18 14:14:55
 * @LastEditors: <PERSON><PERSON>
-->
<template>
    <div>
        <h-msg-box-safe v-model="modalData.status" :mask-closable="false" title="导入告警规则" width="50"
            @on-open="getCollections">
            <h-form ref="formValidate" :model="formValidate" :label-width="100">
                <h-form-item label="产品节点：" prop="sourceProductId" required>
                    <h-select v-model="formValidate.sourceProductId" placeholder="请选择" :clearable="false"
                        :setDefSelect="true" @on-change="getProductMonitorConfig">
                        <h-option v-for="item in newProductList" :key="item.id" :value="item.id">{{ item.productName }}
                        </h-option>
                    </h-select>
                </h-form-item>
                <h-form-item label="告警规则：">
                    <span>{{ total }}条</span>
                </h-form-item>
            </h-form>
            <template v-slot:footer>
                <a-button @click="modalData.status = false">取消</a-button>
                <a-button type="primary" :loading="loading" @click="submitConfig">确定</a-button>
            </template>
        </h-msg-box-safe>
    </div>
</template>

<script>
import _ from 'lodash';
import { importMonitorConfig, getProductMonitorConfig } from '@/api/httpApi';
import aButton from '@/components/common/button/aButton';
export default {
    props: {
        modalInfo: {
            type: Object,
            default: null
        },
        productList: {
            type: Array,
            default: []
        }
    },
    computed: {
        newProductList: function() {
            return _.filter(this.productList, function (o) {
                return o.bizSysType === '1';
            }) || [];
        }
    },
    data() {
        return {
            modalData: this.modalInfo,
            loading: false,
            formValidate: {
                sourceProductId: '',
                productId: ''
            },
            total: 0
        };
    },
    methods: {
        getCollections() {
            this.formValidate.productId = this.modalData.productId;
        },
        async getProductMonitorConfig(id) {
            const { data } = await getProductMonitorConfig({ productId: id });
            this.total = data?.alertRuleExecute?.count || 0;
        },
        submitConfig() {
            const that = this;
            this.$refs['formValidate'].validate(async (valid) => {
                if (valid) {
                    that.loading = true;
                    try {
                        const res = await importMonitorConfig({
                            ...this.formValidate
                        });
                        if (res.success) {
                            this.$emit('handleImportChange');
                            that.$hMessage.success('导入成功!');
                            that.modalInfo.status = false;
                        } else {
                            this.$hMessage.error(res.message);
                        }
                        that.loading = false;
                    } catch (err) {
                        that.loading = false;
                    }
                }
            });
        }
    },
    components: {  aButton }
};
</script>
