import fetch from './fetch';
import { objectToQueryString } from '@/utils/utils';

// const prefix = "/ldplt/api/v1";
const prefix = window['LOCAL_CONFIG']['API_HOME'] || '/';
const v2 = window['LOCAL_CONFIG']['API_HOME2'] || '/';

/**
 * 新版-获取Dashboard通用接口
 * 先查询tag,将tag作为入参查询dashboard配置
 */
export function getDashboardTag(param) {
    return fetch().get(`${prefix}/observation/dashboards/tags?${objectToQueryString(param)}`);
}
export function getDashboardConfigV2(param) {
    return fetch().get(`${v2}/observation/dashboards/configs?${objectToQueryString(param)}`);
}

export function getSingleLinkData(param) {
    return fetch().post(`${prefix}/singleLink/transactionDelay/search`, param);
}

export function getEntrustSingleLinkData(param) {
    return fetch().post(`${prefix}/entrust/latency/singleLink/transactionDelay/search`, param);
}

export function getDelayTrendData(param) {
    return fetch().post(`${prefix}/delayTrend/singleLink/search`, param);
}
export function getDelayAnalysisTrendData(param) {
    return fetch().post(`${prefix}/entrust/latency/analysis/delay/trend/search`, param);
}

export function getAllLinkData(param) {
    return fetch().post(`${prefix}/allLink/marketMonitoring/search`, param);
}
export function getAllLinkData16(param) {
    return fetch().post(`${prefix}/entrust/latency/singleLink/transactionDelay/search`, param);
}

export function postTopHits(param) {
    return fetch().post(`${prefix}/delayTrend/topHits/search`, param);
}
export function postTopHits16(param) {
    return fetch().post(`${prefix}/entrust/latency/analysis/topHits/search`, param);
}

export function getConfigTemplateList() {
    return fetch().get(`${prefix}/config/linkTemplate/search`);
}

export function uploadConfigFile(param) {
    return fetch().post(`${prefix}/config/linkTemplate/upload`, param);
}

export function delConfigFile(param) {
    return fetch().post(`${prefix}/config/linkTemplate/delete`, param);
}

export function getCollectionList() {
    return fetch().get(`${prefix}/collection/info/search`);
}

export function postCollectionPerform(param) {
    return fetch().post(`${prefix}/collection/perform`, param);
}

export function getTemplateFile(param) {
    return fetch().get(
        `${prefix}/config/linkTemplate/export?templateId=${param.templateId}`
    );
}

/**
 * 初始化场景查询
 */
export function querySceneInfo(id) {
    return fetch().get(`${prefix}/scene/test/${id || ''}`);
}

/**
 * 新建测试场景
 */
export function addScene(param) {
    return fetch().post(`${prefix}/scene/test/create`, param);
}

/**
 * 新建/更新测试用例
 */
export function saveCase(param) {
    return fetch().post(`${prefix}/scene/test/case`, param);
}

/**
 * 删除测试用例
 */
export function delCase(param) {
    return fetch().post(`${prefix}/scene/test/case/delete`, param);
}

/**
 * 配置测试用例账号信息
 */
export function configCaseAccount(param) {
    return fetch().post(`${prefix}/scene/test/case/config/account`, param);
}

/**
 * 通过id查询实例列表
 */
export function queryInstance(param) {
    return fetch().post(`${prefix}/scene/test/case/instances`, param);
}

/**
 * 执行测试用例
 */
export function performCase(param) {
    return fetch().post(`${prefix}/scene/test/case/perform`, param);
}

/**
 * 修改实例信息
 */
export function updateCaseInfo(param) {
    return fetch().post(`${prefix}/scene/test/case/instances/info`, param);
}

/**
 * 删除实例信息
 */
export function deleteCaseInfo(param) {
    return fetch().post(`${prefix}/scene/test/case/instances/delete`, param);
}

/**
 * 查询实例详情
 */
export function queryInstanceDetail(param) {
    return fetch().post(`${prefix}/scene/test/case/instances/details`, param);
}

/**
 * 导出测试场景
 */
export function exportSceneApi(id) {
    return fetch().get(`${prefix}/scene/test/export?testSceneId=${id}`);
}

/**
 * 停止测试
 */
export function caseStopApi(param) {
    return fetch().post(`${prefix}/scene/test/case/stop`, param);
}

/**
 * 查询实例列表
 */
export function getInstanceList(param) {
    return fetch().post(`${prefix}/scene/test/case/instances/page`, param);
}

/**
 * 查询报表详情列表
 */
export function getInstanceReportList(param) {
    return fetch().post(`${prefix}/scene/test/case/instances/report`, param);
}

/**
 * 查询报表视图
 */
export function getReportDistribution(param) {
    return fetch().post(`${prefix}/delayTrend/order/distribution`, param);
}

/**
 * 查询报表对比
 */
export function instanceCompare(param) {
    return fetch().post(`${prefix}/scene/test/case/instances/compare`, param);
}

/**
 * 修改场景参数
 */
export function updateSceneInfo(param) {
    return fetch().post(`${prefix}/scene/test/update`, param);
}

/**
 * 获取行情链路模型配置文件
 */
export function getMarketModelConfig(param) {
    return fetch().get(`${prefix}/market/model/config?exchangeId=${param.exchangeId}`, param);
}

/**
 * 获取行情走势K线数据
 */
export function getMarketTrendData(param) {
    return fetch().post(`${prefix}/market/monitor/biz/kLine`, param);
}

/**
 * 获取节点运行状态
 */
export function getMarketNodeStatus(param) {
    return fetch().get(`${prefix}/market/node/operation/status`, param);
}

/**
 * 获取行情全链路时延趋势数据
 */
export function getMarketAllLinkDelay(param) {
    return fetch().post(`${prefix}/market/analysis/allLink/delay`, param);
}

/**
 * 获取行情全链路透视时延数据
 */
export function getMarketAllLinkPerspectiveDelay(param) {
    return fetch().post(
        `${prefix}/market/analysis/allLink/perspective/delay`,
        param
    );
}

/**
 * 获取行情链路质量信息
 */
export function getMarketLinkQuality(param) {
    return fetch().get(
        `${prefix}/market/link/quality?exchangeId=${param.exchangeId}`
        // param
    );
}

/**
 * 获取可选产品类型列表简要信息
 */
export function getProductManageSimpleInfo(param) {
    return fetch().get(`${prefix}/product/manage/simple/info`, param);
}

/**
 * 获取产品核心交易IP和TGWIP
 */
export function getProductManageInfo(param) {
    return fetch().post(`${prefix}/product/manage/info`, param);
}

/**
 * 产品汇总查询
 */
export function queryTradeSummary(param) {
    return fetch().post(`${prefix}/entrust/latency/summary/query`, param);
}

/**
 * 产品详情查询
 */
export function queryTradeDetail(param) {
    return fetch().post(`${prefix}/entrust/latency/detail/query`, param);
}

/**
 * 产品列表查询
 */
export function getProductManageList() {
    return fetch().get(`${prefix}/product/manage/list`);
}

/**
 * 过滤风控的产品列表节点
 */
export function getTestManageList() {
    return fetch().get(`${prefix}/product/manage/test/list`);
}

/**
 * 删除产品节点实例
 */
export function delProductNode(param) {
    return fetch().post(`${prefix}/product/manage/delete`, param);
}

/**
 * 对接/修改产品节点
 */
export function addOrUpdateProduct(param) {
    return fetch().post(`${prefix}/product`, param);
}

/**
 * 获取时延拓扑图列表
 */
export function getLatencyTopoGraphs() {
    return fetch().get(`${prefix}/latency/topological-graphs`);
}

/**
 * 设置时延配置
 */
export function setLatencyConfig(param) {
    return fetch().post(`${prefix}/product/latency/configs`, param);
}

/**
 * 删除应用节点实例
 */
export function delManageInstance(param) {
    return fetch().post(`${prefix}/product/manage/instance/delete`, param);
}

/**
 * 获取可选产品节点列表信息
 */
export function getProductManageTypeList() {
    return fetch().get(`${prefix}/product/manage/simple/info`);
}

/**
 * 添加应用节点实例
 */
export function addManageInstance(param) {
    return fetch().post(`${prefix}/product/manage/instance/add`, param);
}

/**
 * 修改应用节点实例
 */
export function updateManageInstance(param) {
    return fetch().post(`${prefix}/product/manage/instance/update`, param);
}

/**
 * 查询监控趋势图
 */
export function getMonitorDelayTrend(param) {
    return fetch().post(`${prefix}/latency/monitor/delay/trend`, param);
}
export function getMonitorDelayTrend16(param) {
    return fetch().post(`${prefix}/entrust/latency/monitor/delay/trend`, param);
}

/**
 * 查询聚合指标数据
 */
export function getMonitorSpanIndicators(param) {
    return fetch().post(`${prefix}/latency/monitor/span/indicators`, param);
}
export function getMonitorSpanIndicators16(param) {
    return fetch().post(`${prefix}/entrust/latency/monitor/span/indicators`, param);
}

/**
 * 分析-时延走势参照分析
 */
export function getGelayTrendLinkCompareData(param) {
    return fetch().post(`${prefix}/delayTrend/link/compare/search`, param);
}

/**
 * 委托参照分析数据查询
 */
export function getEntrustTrendLinkCompareData(param) {
    return fetch().post(`${prefix}/entrust/latency/analysis/delay/trend/compare/search`, param);
}

/**
 * 查询干系人列表
 */
export function getAddressee(param) {
    return fetch().post(`${prefix}/addressee/query`, param);
}

/**
 * 添加干系人
 */
export function addAddressee(param) {
    return fetch().post(`${prefix}/addressee/add`, param);
}

/**
 * 修改干系人信息
 */
export function updateAddressee(param) {
    return fetch().post(`${prefix}/addressee/update`, param);
}

/**
 * 删除干系人
 */
export function deleteAddressee(param) {
    return fetch().post(`${prefix}/addressee/delete`, param);
}

/**
 * 告警信息列表查询
 */
export function getAlertMessageList(param) {
    return fetch().post(`${prefix}/alertMessage/query`, param);
}

/**
 * 通知模版查询列表接口
 */
export function getMsgTemplateList() {
    return fetch().get(`${prefix}/msgTemplate/query`);
}

/**
 * 保存通知模版
 */
export function saveMsgTemplate(param) {
    return fetch().post(`${prefix}/msgTemplate/save`, param);
}

/**
 * 删除通知模版
 */
export function delMsgTemplate(param) {
    return fetch().post(`${prefix}/msgTemplate/delete`, param);
}

/**
 * 通知外发历史查询
 */
export function getOutNoticeList(param) {
    return fetch().post(`${prefix}/notifyMessage/query`, param);
}

/**
 * 创建分析报表
 */
export function createAnalyseReport(param) {
    return fetch().post(`${prefix}/delayTrend/report/create`, param);
}
export function createAnalyseReport16(param) {
    return fetch().post(`${prefix}/entrust/latency/report/create`, param);
}

/**
 * 行情产品汇总接口
 */
export function getMarketProductSummary(param) {
    return fetch().post(`${prefix}/analysis/latency/market/polymerization/indicators`, param);
}

/**
 * 行情产品监控走势图
 */
export function getMarketMonitorData(param) {
    return fetch().post(`${prefix}/monitor/latency/market/polymerization/trend`, param);
}

/**
 * 行情产品节点穿透时延监控
 */
export function getMarketMonitorIndicators(param) {
    return fetch().post(`${prefix}/monitor/latency/market/polymerization/indicators`, param);
}

/**
 * 产品数据管理，获取索引信息
 */
export function getManageIndexList(param) {
    return fetch().post(`${prefix}/data/management/index/info`, param);
}

/**
 * 产品数据管理，清空索引数据
 */
export function clearManageIndexData(param) {
    return fetch().post(`${prefix}/data/management/index/clear`, param);
}

/**
 * 产品数据管理，索引数据归档
 */
export function exportManageIndexData(param) {
    return fetch().post(`${prefix}/data/management/index/export`, param);
}

/**
 * 产品数据管理，索引数据删除
 */
export function delManageIndexData(param) {
    return fetch().post(`${prefix}/data/management/index/delete`, param);
}

/**
 * 按条件单清理
 */
export function clearManagementData(param) {
    return fetch().post(`${prefix}/data/management/index/condition/clear`, param);
}

/**
 * 获取ES信息
 */
export function getEsUrl() {
    return fetch().get(`${prefix}/data/management/elasticsearch/info`);
}

/**
 * 查询自动化清理配置
 */
export function getCleanConfig() {
    return fetch().get(`${prefix}/data/management/clean/config`);
}

/**
 * 自动化清理配置
 */
export function setCleanConfig(param) {
    return fetch().post(`${prefix}/data/management/clean/config`, param);
}

/**
 * 查询自动化清理记录
 */
export function getCleanRecords(param) {
    return fetch().post(`${prefix}/data/management/clean/record`, param);
}

/**
 * 监控告警查询接口
 */
export function getProductMonitorConfig(param) {
    return fetch().post(`${prefix}/product/manage/monitorConfig/query`, param);
}

/**
 * 字典列表查询接口
 */
export function getMonitorDictQuery() {
    return fetch().get(`${prefix}/dict/query`);
}

/**
 * 监控告警配置新增
 */
export function addProductMonitorConfig(param) {
    return fetch().post(`${prefix}/product/manage/monitorConfig/add`, param);
}

/**
 * 监控告警开启关闭功能
 */
export function monitorConfigSwitchChange(param) {
    return fetch().post(`${prefix}/product/manage/monitorConfig/switch`, param);
}

/**
 * 监控告警规则修改
 */
export function updateMonitorConfig(param) {
    return fetch().post(`${prefix}/product/manage/monitorConfig/update`, param);
}

/**
 * 监控告警规则删除
 */
export function deleteMonitorConfig(param) {
    return fetch().post(`${prefix}/product/manage/monitorConfig/delete`, param);
}

/**
 * 监控告警配置导入规则
 */
export function importMonitorConfig(param) {
    return fetch().post(`${prefix}/product/manage/monitorConfig/import`, param);
}

/**
 * 监控告警配置清空规则
 */
export function clearMonitorConfig(param) {
    return fetch().post(`${prefix}/product/manage/monitorConfig/clean`, param);
}

/**
 * APM字典描述信息
 */
export function getApmDirDesc() {
    return fetch().get(`${prefix}/getApmDir`);
}

/**
 * 配置接入点
 */
export function setPenetrateLatencyConfig(param) {
    return fetch().post(`${prefix}/product/manage/penetrateLatencyConfig/setting`, param);
}

/**
 * 穿透时延度量服务配置-创建时延采集应用
 */
export function setPenetrateLatencyConfig1(param) {
    return fetch().post(`${prefix}/product/latency/application`, param);
}

/**
 * 删除快捷查询
 */
export function deleteQuickQuery(id) {
    return fetch().get(`${prefix}/entrust/latency/quickQuery/delete?id=${id}`);
}

/**
 * 获取快捷查询列表  委托汇总（entrustSummary）或者明细（entrustDetailes）
 */
export function getQuickQueryList(param) {
    return fetch().post(`${prefix}/entrust/latency/quickQuery/list`, param);
}

/**
 * 保存快捷查询条件
 */
export function saveQuickQueryList(param) {
    return fetch().post(`${prefix}/entrust/latency/quickQuery/save`, param);
}

/**
 * 查询监控应用列表接口
 */
export function getMonitorApplications(param) {
    return fetch().get(`${prefix}/monitor/applications?${objectToQueryString(param)}`);
}

/**
 * 查询监控仪表盘查询接口
 */
export function getMonitorDashboards(param) {
    return fetch().get(`${prefix}/monitor/dashboards?${objectToQueryString(param)}`);
}

/**
 * 查询心跳状态接口
 */
export function getMonitorHeartbeats(param) {
    return fetch().get(`${prefix}/monitor/heartbeats?${objectToQueryString(param)}`);
}

/**
 * 告警状态查询接口
 */
export function getAlertsStatus(param) {
    return fetch().get(`${prefix}/alerts/status?${objectToQueryString(param)}`);
}

/**
 * 查询告警事件信息列表
 */
export function getAlarmList(param) {
    return fetch().get(`${prefix}/events?${objectToQueryString(param)}`);
}

/**
 * 修改告警事件信息列表
 */
export function setAlarmList(param) {
    return fetch().post(`${prefix}/events`, param);
}

/** ------------------------------------------   内存表   ----------------------------------------------------------------* */
/**
 * 内存表查询数据库信息
 */
export function getDatabases(param) {
    return fetch().get(`${prefix}/memory-table/databases?${objectToQueryString(param)}`);
}
/**
 * 内存表查询数据库信息
 */
export function getTables(param) {
    return fetch({ includeResponseHeaders: true }).get(`${prefix}/memory-table/tables?${objectToQueryString(param)}`);
}
/**
 * 内存表获取单个表基础信息
 */
export function getTableMetadata(param) {
    return fetch({ includeResponseHeaders: true }).get(`${prefix}/memory-table/table/metadata?${objectToQueryString(param)}`);
}
/**
 * 表元数据修改
 */
export function modifyTableMetadata(param) {
    return fetch().post(`${prefix}/memory-table/table/metadata`, param);
}

/**
 * 内存表数据查询
 */
export function getRecords(param) {
    return fetch().post(`${prefix}/memory-table/records`, param);
}

/**
 * 添加到修改单预览列表
 */
export function addPreview(param) {
    return fetch().post(`${prefix}/memory-table/modification-order/preview`, param);
}
/**
 * 清空修改单预览数据
 */
export function clearPreview(param) {
    return fetch().post(`${prefix}/memory-table/modification-order/preview/delete`, param);
}

/**
 * 修改单预览查询
 */
export function getPreview(param) {
    return fetch().get(`${prefix}/memory-table/modification-order/preview?${objectToQueryString(param)}`);
}

/**
 * 预览修改单条目删除
 */
export function clearPreviewItem(param) {
    return fetch().post(`${prefix}/memory-table/modification-order/preview/item/delete`, param);
}

/**
 * 修改单列表查询
 */
export function getModificationOrders(param) {
    return fetch().get(`${prefix}/memory-table/modification-orders?${objectToQueryString(param)}`);
}

/**
 * 历史修改单详情表格查询
 */
export function getModificationLists(param) {
    return fetch().get(`${prefix}/memory-table/modification-order?${objectToQueryString(param)}`);
}

/**
 * 表格查看修改列表执行明细
 */
export function getProcessDetail(param) {
    return fetch().get(`${prefix}/request/record?${objectToQueryString(param)}`);
}

/**
 * 修改单执行
 */
export function submitModificationOrder(param) {
    return fetch().post(`${prefix}/memory-table/modification-order`, param);
}

/**
 * 修改单执行明细
 */
export function executeModificationOrder(param) {
    return fetch().get(`${prefix}/memory-table/modification-order/process?${objectToQueryString(param)}`);
}

/**
 * 获取LDP管理功能元信息
 */
export function getManageMetas(param) {
    return fetch().get(`${prefix}/ldp/manage-api/metas?${objectToQueryString(param)}`);
}

/**
 * 管理功能查询
 */
export function getManageInfo(param) {
    return fetch().get(`${prefix}/ldp/manage-api?${objectToQueryString(param)}`);
}

/**
 * 获取全产品节点的管理功能信息
 */
export function getManageInfoAllProduct(param) {
    return fetch().get(`${prefix}/ldp/manage-api/brief-Info?${objectToQueryString(param)}`);
}

/**
 * 管理功能批量导出
 */
export function exportManageApi(param) {
    return fetch().post(`${prefix}/ldp/manage-api/export/start`, param);
}

/**
 * 获取管理功能导出进度查询
 */
export function getManageExportProgress(param) {
    return fetch().get(`${prefix}/ldp/manage-api/export/progress?${objectToQueryString(param)}`);
}

/**
 * 管理功能下载
 */
export function downloadManageFile(param) {
    return fetch({ responseType: 'blob' }).get(`${prefix}/file/download?${objectToQueryString(param)}`);
}

/**
 * 终止管理功能导出
 */
export function stopDownloadManage(param) {
    return fetch().post(`${prefix}/ldp/manage-api/export/stop`, param);
}

/**
 * 管理功能修改
 */
export function editManageInfo(param) {
    return fetch().post(`${prefix}/ldp/manage-api`, param);
}

/**
 * 获取资金账号列表
 */
export function getAccountList(param) {
    return fetch().get(`${prefix}/product/manage/accounts?${objectToQueryString(param)}`);
}

/**
 * 时延指标查询建议
 */
export function computerEntrustProposal(param) {
    return fetch().post(`${prefix}/entrust/latency/query/proposal`, param);
}

/** ------------------------------------------   应用集群观测  ----------------------------------------------------------------* */
/**
 * 应用集群观测仪表盘
 */
export function getClusterObservablesDashboards(param){
    return fetch().get(`${prefix}/observables/dashboards/appCluster?${objectToQueryString(param)}`);
}

/**
 * 应用节点观测仪表盘
 */
export function getInstanceObservablesDashboards(param){
    return fetch().get(`${prefix}/observables/dashboards/instance?${objectToQueryString(param)}`);
}

/**
 * 产品数据上场观测数据
 */
export function getInstanceObservablesImportData(param){
    return fetch().get(`${prefix}/observation/import-data?${objectToQueryString(param)}`);
}

/**
 * 应用集群信息查询
 */
export function getClusterObservablesNode(param){
    return fetch().get(`${prefix}/observables/dashboards/appCluster/info?${objectToQueryString(param)}`);
}

/**
 * 应用分组信息查询
 */
export function getInstanceObservablesNode(param){
    return fetch().get(`${prefix}/observables/dashboards/instance/info?${objectToQueryString(param)}`);
}

/**
 * 应用集群信息查询列表
 */
export function getClustersObservablesNode(param){
    return fetch().get(`${prefix}/observables/dashboards/appClusters/info?${objectToQueryString(param)}`);
}

/**
 * 应用分组信息查询列表
 */
export function getInstancesObservablesNode(param){
    return fetch().get(`${prefix}/observables/dashboards/instances/info?${objectToQueryString(param)}`);
}

/**
 * 应用集群角色查询接口
 */
export function getClusterRoles(param){
    return fetch().get(`${prefix}/product/instances/cluster-role?${objectToQueryString(param)}`);
}

/**
 * 应用实例数据上场状态
 */
export function getImportDataInfo(param) {
    return fetch().get(`${prefix}/product/instances/import-data/info?${objectToQueryString(param)}`);
}

/**
 * 核心数据同步观测
 */
export function getObservableTransactionInfo(param) {
    return fetch().get(`${prefix}/product/observation/transaction-sync?${objectToQueryString(param)}`);
}

/**
 * 应用节点表加载详情
 */
export function getLoadDataDetail(param) {
    return fetch().get(`${prefix}/observation/import-table/detail?${objectToQueryString(param)}`);
}

/**
 * 导出表格接口，获取导出查询字段
 */
export function getFileExportConfig(param) {
    return fetch().get(`${prefix}/entrust/latency/export/config?${objectToQueryString(param)}`);
}

/**
 * 导出表格接口查询导出状态
 */
export function getFileExportStatus(param) {
    return fetch().get(`${prefix}/entrust/latency/export/status?${objectToQueryString(param)}`);
}

/**
 * 执行导出表格文件接口
 */
export function exportFileData(param) {
    return fetch().post(`${prefix}/entrust/latency/export`, param);
}

/**
 * 终止导出表格文件接口
 */
export function discontinueExportFileData(param) {
    return fetch().post(`${prefix}/entrust/latency/export/discontinue`, param);
}

/**
 * 导出历史查询
 */
export function getFileExportHistoryList(param) {
    return fetch().get(`${prefix}/entrust/latency/export/history?${objectToQueryString(param)}`);
}

/**
 * 下载导出文件
 */
export function downloadExportFile(param) {
    return fetch({ responseType: 'blob' }).get(`${prefix}/entrust/latency/export/download?${objectToQueryString(param)}`);
}

/**
 * 删除文件
 */
export function deleteExportFile(param) {
    return fetch().post(`${prefix}/entrust/latency/export/history/delete`, param);
}

/**
 * 查询监控规则列表
 */
export function getMonitorRules(param) {
    return fetch().get(`${prefix}/monitor/rules?${objectToQueryString(param)}`);
}

/**
 * 保存监控规则
 */
export function saveMonitorRules(param) {
    return fetch().post(`${prefix}/monitor/rule`, param);
}

/**
 * 查询监控事件列表
 */
export function getMonitorEvents(param) {
    return fetch().get(`${prefix}/monitor/events?${objectToQueryString(param)}`);
}

/**
 * 下载管理功能 GetFuncDealInfo数据
 */
export function downloadFuncDetailInfoData(param) {
    return fetch().post(`${prefix}/ldp/manage-api/nodes/download`, param);
}
