<template>
    <div class="main">
        <div class="title">
            <a-title title="降级熔断配置">
                <slot>
                    <h-select
                        v-show="productList.length > 1"
                        v-model="productInstNo"
                        placeholder="请选择"
                        class="title-select"
                        placement="bottom"
                        :positionFixed="true"
                        :clearable="false" @on-change="checkProduct">
                        <h-option v-for="item in productList" :key="item.id" :value="item.productInstNo">{{ item.productName }}</h-option>
                    </h-select>
                </slot>
            </a-title>
        </div>
        <a-loading v-if="loading"></a-loading>
        <h-tabs v-model="tabName" class="product-box" @on-click="tabClick(tabName)">
            <h-tab-pane label="发布名单列表" name="publish">
                <publish-list ref="publish" :productId="productInstNo"></publish-list>
            </h-tab-pane>
            <h-tab-pane label="黑名单配置" name="black">
                <black-config-list ref="black" :productId="productInstNo"></black-config-list>
            </h-tab-pane>
            <h-tab-pane label="白名单配置" name="white">
                <white-config-list ref="white" :productId="productInstNo"></white-config-list>
            </h-tab-pane>
            <h-tab-pane label="限流名单配置" name="limit">
                <limit-config-list ref="limit" :productId="productInstNo"></limit-config-list>
            </h-tab-pane>
            <h-tab-pane label="组配置" name="group">
                <group-list ref="group" :productId="productInstNo"></group-list>
            </h-tab-pane>
            <h-button slot="extra" type="primary" @click="() => {publishStatus = true;}">发布</h-button>
        </h-tabs>
         <h-msg-box
            v-model="publishStatus"
            class="publish-box"
            width="460"
            :closable="false"
            :mask-closable="false">
            <div style="text-align: center;">
                <p style="text-align: left; font-size: 16px; font-weight: 500;">
                    <h-icon name="feedback" size="26" color="#ff9901" style="position: relative; top: 4px;"></h-icon>确定要发布列表中已启用的黑名单、白名单、限流名单吗？
                </p>
            </div>
            <p slot="footer">
                <h-button v-if="!publishLoading" @click="() => { publishStatus = false; }">取消</h-button>
                <h-button :loading="publishLoading" type="primary" @click="handlePublishConfig">
                    {{publishLoading ? '发布中' : '发布'}}
                </h-button>
            </p>
        </h-msg-box>
    </div>
</template>

<script>
import _ from 'lodash';
import { mapState, mapActions } from 'vuex';
import { publishRules } from '@/api/brokerApi';
import aTitle from '@/components/common/title/aTitle';
import aLoading from '@/components/common/loading/aLoading';
import publishList from '@/components/brokerDataLimit/publishList/publishList';
import blackConfigList from '@/components/brokerDataLimit/blackConfigList';
import whiteConfigList from '@/components/brokerDataLimit/whiteConfigList';
import limitConfigList from '@/components/brokerDataLimit/limitConfigList';
import groupList from '@/components/brokerDataLimit/groupList';
export default {
    name: 'BorkerDataLimit',
    data() {
        return {
            productInstNo: '', // 选中的产品
            tabName: 'publish', // tab默认选择
            loading: false,
            publishLoading: false,
            publishStatus: false
        };
    },
    mounted() {
        this.init();
    },
    computed: {
        ...mapState({
            productList: state => {
                return state.product.productListLight || [];
            }
        })
    },
    methods: {
        ...mapActions({ getProductList: 'product/getProductListLight' }),
        // 初始化页面
        async init() {
            try {
                this.loading = true;
                await this.getProductList();
                const productInstNo = localStorage.getItem('productInstNo');
                this.productInstNo = _.find(this.productList, ['productInstNo', productInstNo])?.productInstNo || this.productList?.[0]?.productInstNo;
            } catch (e) {
                console.error(e);
            } finally {
                this.loading = false;
            }
        },
        // 切换导航产品
        async checkProduct(item) {
            localStorage.setItem('productInstNo', item);
            setTimeout(() => {
                this.loading = false;
                this.tabClick(this.tabName);
            }, 500);
        },
        // 切换tab
        tabClick(name) {
            this.$refs[name] && this.$refs[name].initData();
        },
        // 名单发布
        async handlePublishConfig() {
            try {
                this.publishLoading = true;
                const res = await publishRules({
                    productId: this.productInstNo
                });
                if (res.success) {
                    this.$hMessage.success('配置发布成功');
                    this.publishStatus = false;
                    if (this.tabName === 'publish') {
                        this.tabClick('publish');
                    }
                } else if (res.code.length === 8) {
                    this.$hMessage.error(res.message);
                }
            } catch (error) {
                console.error(error);
            }
            this.publishLoading = false;
        }
    },
    components: { aTitle, aLoading, publishList, blackConfigList, groupList, whiteConfigList, limitConfigList }
};
</script>

<style lang="less" scoped>
@import url("@/assets/css/tab.less");
@import url("@/assets/css/input.less");
@import url("@/assets/css/menu.less");

.main {
    .title {
        p {
            display: inline-block;
            color: var(--font-color);
            font-size: var(--title-font-size);
            position: absolute;
            left: 50%;
            transform: translateX(-50%);
        }

        .title-select {
            float: right;
            margin-right: 4px;
            top: 5px;
            min-width: 200px;
            width: auto;
        }
    }

    .product-box {
        /deep/ .h-tabs-nav-wrap {
            float: none !important;
        }

        /deep/ .h-tabs-nav-right {
            position: absolute;
            right: 0;
            top: 5px;
        }
    }
}
</style>
