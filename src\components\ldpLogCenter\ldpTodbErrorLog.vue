<template>
    <div class="product-box">
        <!-- 加载状态 -->
        <a-loading v-if="loading" style="z-index: 9;"></a-loading>

        <div class="query-table-box">
            <query-table
                ref="query-table"
                showTitle
                btnTitle="更多条件"
                hasSetTableColumns
                setTableColumnsType="hasSorted"
                :formItems="moreFormItems"
                :formCols="3"
                :columns="clusterColumns"
                :tableData="clusterTableData"
                :loading="clusterLoading"
                :total="totalCount"
                :hasPage="false"
                @query="handleQuery"
                @on-row-click="handleDrawerOpen">
                <template #tableTitle>
                    <div class="table-title">回库错误日志</div>
                    <div class="table-describe">
                        点击行查看对应日志记录详情
                    </div>
                </template>
                <template #operateBlock>
                    <h-form
                        ref="formItems"
                        :model="formItems"
                        inline
                        class="operate-form"
                        @submit.native.prevent>
                        <h-form-item label="" prop="instance" required>
                            <h-select
                                v-model="formItems.instance"
                                :clearable="true"
                                showTitle
                                placeholder="节点"
                                style="width: 120px;"
                                @on-drop-change="handleDropChange"
                                @on-change="handleSelectChange"
                                >
                                <h-option
                                    v-for="item in todoInstanceList"
                                    :key="item.id"
                                    :value="item.id"
                                    >{{ item.instanceName }}
                                </h-option>
                            </h-select>
                        </h-form-item>
                        <h-form-item label="" prop="clusterName">
                            <h-select
                                v-model="formItems.clusterName"
                                isString
                                multiple
                                multClearable
                                collapseTags
                                showTitle
                                :clearable="true"
                                style="width: 130px;"
                                placeholder="集群名称">
                                <h-option
                                    v-for="item in clusterList"
                                    :key="item.clusterName"
                                    :value="item.clusterName">
                                    {{ item.clusterName }}
                                </h-option>
                            </h-select>
                        </h-form-item>
                        <h-form-item prop="locateField" :label-width="0" class="select-box">
                            <h-select v-model="formItems.locateField" style="width: 100px;" :clearable="true" placeholder="账户类型" >
                                <h-option v-for="item in locateFieldList" :key="item.FieldName" :value="item.FieldName">{{item.FieldDesc}}</h-option>
                            </h-select>
                        </h-form-item>
                        <h-form-item label="" prop="locateValue" :validRules="regRule" class="input-box">
                            <h-input
                                v-model.trim="formItems.locateValue"
                                :clearable="true"
                                :disabled="!formItems.locateField"
                                placeholder="多个账号以英文逗号隔开"
                                style="width: 140px;"
                            >
                           </h-input>
                        </h-form-item>
                <h-form-item>
                    <a-button
                        type="primary"
                        :disabled="buttonLoading"
                        @click="handleOutQuery"
                        >查询
                    </a-button>
                </h-form-item>
           </h-form>
                </template>
            </query-table>
        </div>
        <div class="page-box">
            <div>
                共 {{ totalCount }} 条记录
            </div>
            <div class="page-box-button">
                <a-button
                    type="dark"
                    size="small"
                    :disabled="!historyFormItems.instance || clusterEndMsgNos.length <= 1 || buttonLoading"
                    @click="()=>handlePrev('log')">上一页
                </a-button>
                &nbsp;
                <a-button
                    type="dark"
                    size="small"
                    :disabled="clusterTableData.length < pageSize || buttonLoading"
                    @click="()=>handleNext('log')">下一页
                </a-button>
            </div>
        </div>

        <log-detail-drawer
            v-if="drawerInfo.status"
            :modalInfo="drawerInfo"
            :instanceData="instanceData"
            :timeout="timeout"
        />
    </div>
</template>

<script>
import logDetailDrawer from '@/components/ldpLogCenter/modal/logDetailDrawer.vue';
import aLoading from '@/components/common/loading/aLoading';
import aButton from '@/components/common/button/aButton';
import queryTable from '@/components/common/bestTable/queryTable/queryTable';
import { getProductInstances, getProductClusters } from '@/api/productApi';
import { getManagerProxy } from '@/api/mcApi';
export default {
    name: 'LdpTodbErrorLog',
    components: {
        queryTable, aLoading, aButton, logDetailDrawer
    },
    props: {
        productInstNo: {
            type: String,
            default: ''
        },
        timeout: {
            type: Number,
            default: 3
        }
    },
    computed: {
        instanceData() {
            return this.allTodoInstanceList.find(o => o?.id === this.historyFormItems?.instance);
        },
        proxyTimeout() {
            return (this.timeout || 3) < 30 ? 30 : this.timeout || 3;
        }
    },
    data() {
        const requiredValidate = (rule, value, callback) => {
            if (!value) {
                callback(new Error('选择不能为空'));
            } else if (Array.isArray(value) && !value?.length) {
                callback(new Error('选择不能为空'));
            } else {
                callback();
            }
        };
        return {
            loading: false,
            locateFieldList: [],
            formItems: {
                instance: '',
                locateValue: '',
                clusterName: '',
                locateField: ''
            },
            // 防止翻页过程中，应用节点等查询条件被清空，从而请求报错
            historyFormItems: {},
            moreFormItems: [
                {
                    type: 'select',
                    key: 'isResent',
                    label: '回库状态',
                    options: [
                        {
                            value: '1',
                            label: '已成功'
                        },
                        {
                            value: '0',
                            label: '失败'
                        }
                    ],
                    value: ''
                }
                // {
                //     type: 'input',
                //     key: 'TransNo',
                //     label: '事务号',
                //     value: ''
                // },
                // {
                //     type: 'daterange',
                //     key: 'time',
                //     label: '事务时间',
                //     value: [],
                //     placement: 'bottom-end',
                //     placeholder: '请选择时间范围'
                // },
                // {
                //     type: 'input',
                //     key: 'QueueAlias',
                //     label: '线程组',
                //     value: ''
                // },
                // {
                //     type: 'input',
                //     key: 'FunctionNo',
                //     label: '功能号',
                //     value: ''
                // }
            ],
            regRule: [
                { test: /^(?:[^,]*(?:,[^,]*){0,49})$/, message: '支持多账号以英文逗号分隔且最多50个', trigger: 'blur' },
                {
                    test: (rule, value, callback) => {
                        const locateField = this.formItems.locateField;
                        const locateValue = this.formItems.locateValue;

                        // 如果账户类型不为空，账户信息不能为空
                        if (locateField && !locateValue) {
                            callback(new Error('请输入账户'));
                            return;
                        }

                        callback();
                    },
                    trigger: 'blur,change'
                }
            ],
            ruleValidate: {
                instance: [{ validator: requiredValidate, trigger: 'change' }],
                clusterName: [{ validator: requiredValidate, trigger: 'change' }]
            },
            // 回库节点列表
            allTodoInstanceList: [],
            todoInstanceList: [],
            // 核心集群列表
            clusterList: [],
            clusterLoading: false,
            clusterColumns: [
                {
                    title: '定位字段',
                    key: 'LocateField',
                    ellipsis: true,
                    isChecked: false
                },
                {
                    title: '账户类型',
                    key: 'LocateFieldName',
                    ellipsis: true,
                    render: (h, params) => {
                        const LocateFieldName = params?.row?.FieldDesc || this.historyFormItems.locateFieldList.find(o => o?.FieldName === params?.row?.LocateField)?.FieldDesc || '-';
                        return h('span', {
                            attrs: {
                                title: LocateFieldName
                            }
                        }, LocateFieldName);
                    }
                },
                {
                    title: '账户',
                    key: 'LocateValue',
                    ellipsis: true
                },
                {
                    title: '事务号',
                    key: 'TransNo',
                    ellipsis: true
                },
                {
                    title: '集群名称',
                    key: 'ClusterName',
                    ellipsis: true
                },
                {
                    title: '线程组',
                    key: 'QueueAlias',
                    ellipsis: true
                },
                {
                    title: '线程号',
                    key: 'ThreadNo',
                    ellipsis: true
                },
                {
                    title: '功能号',
                    key: 'FunctionNo',
                    ellipsis: true
                },
                {
                    title: '事务时间',
                    key: 'Time',
                    ellipsis: true,
                    render: (h, params) => {
                        const time = this.formatHHMMSS(params?.row?.Time?.toString());
                        return h('span', {
                            attrs: {
                                title: time
                            }
                        }, time);
                    }
                },
                {
                    title: '事务长度',
                    key: 'TransLength',
                    ellipsis: true
                },
                {
                    title: '错误号',
                    key: 'ErrorNo',
                    ellipsis: true,
                    render: (h, params) => {
                        const errorNoValue = params.row?.ErrorNo === -1 ? '-' : (params.row?.ErrorNo ?? '-');
                        return h('div', {
                            attrs: {
                                title: errorNoValue
                            }
                        }, errorNoValue);
                    }
                },
                {
                    title: '错误消息',
                    key: 'ErrorMsg',
                    ellipsis: true,
                    render: (h, params) => {
                        const errorMsgValue = params.row?.ErrorMsg || '-';
                        return h('span', {
                            attrs: {
                                title: errorMsgValue
                            }
                        }, errorMsgValue);
                    }
                },
                {
                    title: '回库状态',
                    key: 'IsResent',
                    ellipsis: true,
                    minWidth: 120,
                    renderHeader: (h, params) => {
                        return h('div', {
                            style: {
                                display: 'flex'
                            }
                        },
                        [
                            h('span', {
                                style: {
                                    color: '#fff',
                                    verticalAlign: '2px'
                                }
                            }, '回库状态'),
                            h('poptip',
                                {
                                    class: 'apm-poptip',
                                    props: {
                                        trigger: 'hover',
                                        placement: 'top-end',
                                        positionFixed: true
                                    }
                                },
                                [

                                    h('icon', {
                                        style: { padding: '0 15px 0 5px' },
                                        props: {
                                            name: 'prompt1',
                                            color: '#9296A1',
                                            size: '14'
                                        }
                                    }),
                                    h('div',
                                        {
                                            slot: 'content',
                                            class: 'pop-content',
                                            style: {
                                                width: '300px',
                                                padding: '10px',
                                                'line-height': '20px'
                                            }
                                        },
                                        [
                                            h('div',
                                                [
                                                    h('p',
                                                        '回库状态：'
                                                    ),
                                                    h('p',
                                                        {
                                                            style: {
                                                                'white-space': 'normal'
                                                            }
                                                        },
                                                        '表示在用户进行"重试"操作之后，本地记录的错误事务，是否重新发送到网关，完成回库。'
                                                    )
                                                ]
                                            )
                                        ]
                                    )
                                ])
                        ]);
                    },
                    render: (h, params) => {
                        const IsResentLabel = params.row?.IsResent ? '已成功' : '未成功';
                        return h('div', {
                            attrs: {
                                title: IsResentLabel
                            }
                        }, IsResentLabel);
                    }
                }
            ],
            clusterTableData: [],
            totalCount: 0,
            // 表格翻页
            clusterEndMsgNos: [], // 结束操作编号, 翻页使用
            buttonLoading: false,
            pageSize: 10,

            // 记录字段信息
            drawerInfo: {
                status: false
            }
        };
    },
    mounted() {
    },
    beforeDestroy() {
    },
    watch: {
        'formItems.locateField': {
            handler(newVal, oldVal) {
                // 如果账户类型为空，自动清空账户信息
                if (!newVal && this.formItems.locateValue) {
                    this.formItems.locateValue = '';
                }

                // 触发账户信息字段的验证
                this.$nextTick(() => {
                    this.$refs['formItems'].validateField('locateValue');
                });
            },
            immediate: false
        }
    },
    methods: {
        async initData(params) {
            this.clearData();
            this.$refs?.['formItems'] && this.$refs['formItems'].resetValidate();
            this.loading = true;
            try {
                await this.getProductInstances();
                await this.getProductClusters();
                await this.getTodoList();

                // 跳转、历史缓存节点和集群不存在
                const id = this.todoInstanceList.find(o => o?.id === (params?.instanceId))?.id ||
                            this.todoInstanceList.find(o => (o?.id === this.formItems?.instance))?.id ||
                            this.todoInstanceList?.[0]?.id || '';
                const clusters = this.clusterList.map(o => o?.clusterName);
                const clusterName = params?.clusterName?.split(',')?.filter(c => clusters?.includes(c))?.join(',') ||
                    this.formItems?.clusterName?.split(',')?.filter(c => clusters?.includes(c))?.join(',') || '';
                if (params) {
                    this.formItems = {
                        instance: id,
                        locateField: params?.locateField,
                        locateValue: params?.locateValue,
                        clusterName: clusterName
                    };
                } else {
                    this.formItems = {
                        ...this.formItems,
                        instance: id,
                        clusterName: clusterName
                    };
                }

                await this.getLocateFieldList();

            } catch (e) {
                console.log(e);
            } finally {
                this.loading = false;
                this.$nextTick(() => {
                    this.handleOutQuery();
                });
            }
        },
        // 下拉重新获取
        async handleDropChange(stu){
            if (!stu) return;
            await this.getProductInstances();
            await this.getTodoList();
        },
        async handleSelectChange(stu){
            if (!stu) {
                this.locateFieldList = [];
                return;
            }
            await this.getLocateFieldList();
        },
        // 清空数据
        clearData(){
            this.historyFormItems = {};
            this.clearClusterData();
        },
        // 清空回库结果集
        clearClusterData(){
            this.clusterTableData = [];
            this.clusterEndMsgNos = [];
            this.totalCount = 0;
        },
        // 查询按钮
        handleOutQuery(){
            this.$refs?.['query-table'] && this.$refs['query-table'].$_handleQuery();
        },
        handleQuery(val) {
            // 参数校验
            this.$refs['formItems'].validate((valid) => {
                if (valid) {
                    // 保存查询条件
                    this.historyFormItems = {
                        ...this.formItems,
                        ...val,
                        locateFieldList: [...this.locateFieldList]
                    };
                    this.clearClusterData();
                    this.getBizErrorMsgInfo();
                }
            });
        },
        // 上一页
        handlePrev() {
            this.getBizErrorMsgInfo('minus');
        },
        // 下一页
        handleNext() {
            this.getBizErrorMsgInfo('plus');
        },
        // 点击表格行 打开弹窗
        handleDrawerOpen(row) {
            this.drawerInfo = {
                status: true,
                ...row
            };
        },
        // 格式化事务时间
        formatHHMMSS(inputString) {
            if (!inputString) return '';
            const timeStr = inputString?.split('-')[1]?.split('.')?.[0]; // 获取 '112903'
            // 确保输入是一个6位的数字
            const timeString = String(timeStr).padStart(6, '0');

            // 截取并格式化为hh:mm:ss
            const hours = timeString.substring(0, 2);
            const minutes = timeString.substring(2, 4);
            const seconds = timeString.substring(4, 6);

            return `${hours}:${minutes}:${seconds}`;
        },
        // 接口请求------获取回库应用节点列表且角色为主
        async getProductInstances() {
            try {
                const res = await getProductInstances({ productId: this.productInstNo });
                if (res.code === '200') {
                    this.allTodoInstanceList = res?.data?.instances?.filter(o => o?.instanceIdentities?.includes('todb') &&
                        o?.clusterRole === 'ARB_ACTIVE') || [];
                } else {
                    this.allTodoInstanceList = [];
                }
            } catch (err) {
                this.allTodoInstanceList = [];
            }
        },
        // 获取应用集群信息
        async getProductClusters() {
            try {
                const res = await getProductClusters({
                    productId: this.productInstNo
                });
                if (res.code === '200') {
                    this.clusterList = (res?.data?.appClusters || []).filter(o => o?.clusterInstanceIdentities?.includes('bizproc'));
                } else {
                    this.clusterList = [];
                }
            } catch (err) {
                this.clusterList = [];
            }
        },
        // 接口请求------是否有ldp_todb_v2插件
        async getTodoList(){
            if (!this.allTodoInstanceList?.length) {
                this.todoInstanceList = [];
                return;
            };
            const list = [];
            /**
             * QueryMgrList查询插件管理功能列表
             * 功能说明: 查询指定的插件提供的管理功能列表
             */
            const paramList = this.allTodoInstanceList.map(o => {
                return {
                    manageProxyIp: o.ip,
                    manageProxyPort: o.manageProxyPort,
                    pluginName: 'ldp_mproxy',
                    funcName: 'QueryMgrList'
                };
            });
            const res = await getManagerProxy(JSON.stringify(paramList));
            Array.isArray(this.allTodoInstanceList) && this.allTodoInstanceList.forEach((item, index) => {
                if (res?.code === '200' && res.data?.[index]?.Response?.length) {
                    const data = res?.data?.[index]?.Response?.filter(o =>
                        o?.PluginName === 'ldp_todb' &&
                        ['GetBizErrorMsgInfo', 'GetMsgBodyInfo'].every(v => o?.MgrList.includes(v)))?.[0];
                    data && list.push(item);
                }
            });

            this.todoInstanceList = [...list];
        },
        // 接口请求------回库日志结果
        async getBizErrorMsgInfo(type) {
            this.clusterLoading = true;
            this.buttonLoading = true;
            const nextMsgNo = type === 'minus'
                ? this.clusterEndMsgNos?.[this.clusterEndMsgNos?.length  - 3] || 0
                :  this.clusterEndMsgNos?.[this.clusterEndMsgNos?.length - 1] || 0;
            const param = [
                {
                    manageProxyIp: this.instanceData?.ip,
                    manageProxyPort: this.instanceData?.manageProxyPort,
                    pluginName: 'ldp_todb',
                    funcName: 'GetBizErrorMsgInfo',
                    params: {
                        StartMsgNo: nextMsgNo,
                        MsgCount: this.pageSize,
                        TimeoutMilli: (this.timeout ?? 3) * 1000,
                        ClusterName: this.historyFormItems?.clusterName,
                        LocateField: this.historyFormItems?.locateField,
                        LocateValue: this.historyFormItems?.locateValue,
                        Delimiter: ',',
                        // 新增回库状态-是否回库完成
                        IsResent: this.historyFormItems?.isResent || '2'
                    }
                }
            ];
            try {
                const res = await getManagerProxy(JSON.stringify(param), this.proxyTimeout * 1000);
                if (res.code === '200') {
                    const resData = res?.data?.[0] || {};
                    if (resData?.ErrorNo) {
                        this.$hMessage.error({
                            content: resData?.ErrorMsg || '查询失败',
                            duration: 2.5
                        });
                        return;
                    }
                    if (type === 'minus') {
                        this.clusterEndMsgNos.pop();
                    } else {
                        this.clusterEndMsgNos.push(resData?.NextMsgNo);
                    }
                    this.clusterTableData = resData?.BizErrorMsgInfo || [];
                    this.totalCount = resData?.TotalCount || 0;
                }
            } catch (err) {
                console.log(err);
            } finally {
                this.clusterLoading = false;
                this.buttonLoading = false;
            }
        },
        // localField列表
        async getLocateFieldList(){
            const instance = this.allTodoInstanceList.find(o => o?.id === this.formItems?.instance);
            const name = await this.getModuleInfo(instance);
            if (!name) {
                this.locateFieldList = [];
                return;
            };
            const param = [{
                manageProxyIp: instance?.ip,
                manageProxyPort: instance?.manageProxyPort,
                pluginName: 'ldp_todb',
                funcName: name + '_GetLocateFieldInfo'
            }];
            const res = await getManagerProxy(JSON.stringify(param));
            if (res?.code === '200'){
                this.locateFieldList = res?.data?.[0]?.LocateField || [];
                // this.locateFieldList = [{
                //     FieldName: 'fund_account',
                //     FieldDesc: '股东代码'
                // }];
                const hasLocateField = this.locateFieldList.find(o => o?.FieldName === this.formItems?.locateField) || '';
                if (!hasLocateField){
                    this.formItems.locateField = '';
                    this.formItems.locateValue = '';
                }
            } else {
                this.locateFieldList = [];
            }
        },
        // 用于区分ldp_mdbredo  ldpgwredo
        async getModuleInfo(instance){
            let name = '';
            const param = [{
                manageProxyIp: instance?.ip,
                manageProxyPort: instance?.manageProxyPort,
                pluginName: 'ldp_todb',
                funcName: 'GetModuleInfo'
            }];

            if (!instance?.ip || !instance?.manageProxyPort) return '';

            const res = await getManagerProxy(JSON.stringify(param));
            if (res?.code === '200' && Array.isArray(res?.data?.[0]?.ModuleInfo)){
                name = res?.data?.[0]?.ModuleInfo?.[0]?.Name;
            }

            return name;
        }
    }
};
</script>
<style lang="less" scoped>
.product-box {
    min-width: 800px;
    height: calc(100% - 13px);
    border-radius: 4px;
    background: var(--wrapper-color);

    .query-table-box {
        height: calc(100% - 50px);

        /* stylelint-disable-next-line selector-class-pattern */
        /deep/.h-form-item-required .h-form-item-requiredIcon {
            display: none;
        }

        .table-title {
            font-size: 14px;
            color: var(--font-color);
            padding-right: 10px;
        }

        .table-describe {
            font-size: 12px;
            color: var(--font-opacity-color);

            span {
                font-weight: 600;
            }
        }

        .operate-form {
            margin-right: 5px;

            /deep/ .h-form-item {
                margin-bottom: 0;
                margin-right: 3px;
            }
        }

        .select-box {
            margin-right: -3px !important;

            /deep/ .h-select-selection {
                border-radius: 4px 0 0 4px;
            }

            /deep/ .h-select-single .h-select-selection .h-select-placeholder,
            /deep/ .h-select-single .h-select-selection .h-select-selected-value {
                padding-right: 15px;
            }
        }

        .input-box {
            /deep/ .h-input {
                border-radius: 0 4px 4px 0;
            }
        }
    }

    .page-box {
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 0 20px;
        color: var(--font-color);
        margin-top: 10px;
    }

    /deep/ .verify-tip > .verify-tip-inner {
        max-width: 200px !important;
    }

    @media (max-width: 1000px) {
        /deep/ .table-describe {
            display: none;
        }
    }
}
</style>
