<template>
    <div v-if="modalData.status">
        <!-- 批量编辑弹窗 -->
        <h-msg-box-safe v-model="modalData.status" :mask-closable="false" width="605" height="285">
            <template v-slot:header>
                <div class="publish-title-icon">
                    <h-icon name="prompt" style="font-size: 36px;"></h-icon>
                </div>
                <div class="publish-title">确定要对以下数据表进行二次上场吗？</div>
            </template>
            <div class="table-list">
                <h-simple-table :columns="tableColumn" :data="tableData" height="240"></h-simple-table>
            </div>
            <template v-slot:footer>
                <a-button @click="modalData.status = false">取消</a-button>
                <a-button type="primary" :loading="loading" @click="handleSubmit">确定执行</a-button>
            </template>
        </h-msg-box-safe>
    </div>
</template>

<script>
import aButton from '@/components/common/button/aButton';
import { batchLoadData } from '@/api/brokerApi';

export default {
    props: {
        productId: {
            type: String,
            default: ''
        },
        modalInfo: {
            type: Object,
            default: null
        },
        checkedItems: {
            type: Array,
            default: () => []
        }
    },
    watch: {
        'modalInfo.status': {
            handler(){
                this.tableData = this.checkedItems;
            }
        }
    },
    components: { aButton },
    data() {
        return {
            modalData: this.modalInfo,
            loading: false,
            tableColumn: [
                {
                    title: '表名',
                    key: 'tableName'
                },
                {
                    title: '分片号',
                    key: 'shardingNo'
                },
                {
                    title: '加载方式',
                    key: 'loadMode',
                    ellipsis: true,
                    render: (_, params) => {
                        let loadingMethodDom = <div>暂无数据</div>;
                        switch (params.row.loadMode) {
                            case '1':
                                loadingMethodDom = <div>追加</div>;
                                break;
                            case '2':
                                loadingMethodDom = <div>覆盖</div>;
                                break;
                            case '3':
                                loadingMethodDom = <div>追加+覆盖</div>;
                                break;
                        }
                        return loadingMethodDom;
                    }
                },
                {
                    title: '条件',
                    key: 'loadSql',
                    render: (_, params) => {
                        return <div>{params.row.loadSql ? params.row.loadSql : '-'}
                        </div>;
                    }
                }
            ],
            tableData: this.checkedItems
        };
    },
    methods: {
        // 调用接口 表上场
        async handleSubmit(){
            const param = {
                productId: this.productId,
                whereCondition: 'all',
                tableNames: this.checkedItems.map(item => {
                    return {
                        tableName: item.tableName,
                        clusterName: item.clusterName
                    };
                })
            };
            try {
                this.loading = true;
                const res = await batchLoadData(param);
                if (res.success){
                    this.$hMessage.success('操作成功');
                    this.$emit('query');
                    this.modalData.status = false;
                    this.$hCore.trigger('LoadDataStart');
                }
            } catch (error) {
                console.error(error);
            }
            this.loading = false;
        }
    }
};
</script>

<style lang="less" scoped>
    /deep/ .h-modal-body {
        padding: 0;
    }

    /deep/ .h-modal-header {
        border-bottom: none;
    }

    .publish-title-icon {
        position: absolute;
        top: 16px;
        left: 32px;
        color: #ff9901;
    }

    .publish-title {
        position: absolute;
        top: 26px;
        left: 32px;
        font-size: 16px;
        color: #495060;
        font-weight: bold;
        margin-left: 38px;
        display: inline-block;
    }

    .table-list {
        margin-top: 42px;
        margin-left: 50px;
        width: 503px;
    }
</style>
