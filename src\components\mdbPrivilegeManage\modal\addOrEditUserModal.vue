<!-- 添加、编辑mdb用户 -->
<template>
    <div>
        <h-msg-box-safe v-model="modalData.status" :mask-closable="false"
            :title="modalData.type === 'add' ? '添加用户' : '编辑用户'" width="600" maxHeight="380" @on-open="getCollections">
            <h-form ref="formValidate" :model="formValidate" :label-width="80" :rules="ruleInline">
                <h-form-item label="用户名" prop="userName" required>
                    <h-input v-model.trim="formValidate.userName" type="text" placeholder="请输入用户名" clearable
                        :maxlength="30"></h-input>
                </h-form-item>
                <h-form-item v-if="modalData.type === 'add'" label="密码" prop="password" required>
                    <h-input v-model.trim="formValidate.password" :type="showPassWord ? 'text':'password'" placeholder="请输入或自动生成密码" :maxlength="20"
                    :icon="showPassWord ? 'browse_fill' : 'eye-disabled'" autocomplete="new-password" style="width: 81%;" @on-click="isShowPassWord"></h-input>
                    <a-button @click="generatePassword">自动生成</a-button>
                    <p class='input-text'>长度8-20位，必须包含字母 (a~zA~Z)、数字(0~9)、特殊符号(!、$、#、@、*、_)3种</p>
                </h-form-item>
                <h-form-item label="描述" prop="userDescribe">
                    <h-input v-model.trim="formValidate.userDescribe" type="textarea" :rows="2" placeholder="请输入描述"
                        :maxlength="500" :canResize="false"></h-input>
                </h-form-item>
                <h-form-item label="关联角色" prop="roles" required>
                    <h-select v-model="formValidate.roles" placeholder="暂未关联角色" :positionFixed="true" multiple filterable showBottom>
                        <h-option v-for="item in formValidate.roleList" :key="item.id" :value="item.id" :disabled="!item.enableAuth"
                    ><span :title="!item.enableAuth ? '暂未配置权限' : item.roleName">{{item.roleName}}</span></h-option>
                    </h-select>
                    <p class='input-text'>每个用户至多关联10个角色</p>
                </h-form-item>
            </h-form>
            <template v-slot:footer>
                <a-button @click="modalData.status = false">取消</a-button>
                <a-button type="primary" :loading="loading" @click="submitConfig">确认</a-button>
            </template>
        </h-msg-box-safe>
    </div>
</template>

<script>
import _ from 'lodash';
import { validatePass } from '@/utils/validate';
import aButton from '@/components/common/button/aButton';

export default {
    name: 'AddOrEditUser',
    props: {
        modalInfo: {
            type: Object,
            default: null
        }
    },
    data() {
        const validateRoles = (rule, value, callback) => {
            if (value?.length > 10) {
                callback(new Error('每个用户至多关联10个角色'));
            }
            callback();
        };
        return {
            modalData: this.modalInfo,
            formValidate: {
                roles: []
            },
            ruleInline: {
                password: [
                    { required: true, message: '请填写密码', trigger: 'blur' },
                    {
                        type: 'string',
                        min: 8,
                        max: 20,
                        message: '请输入密码8~20位',
                        trigger: 'blur'
                    },
                    { validator: validatePass, trigger: 'blur' }
                ],
                roles: [
                    { validator: validateRoles, trigger: 'blur' }
                ]
            },
            loading: false,
            showPassWord: false
        };
    },
    methods: {
        getCollections() {
            this.$refs['formValidate'].resetFields();
            this.formValidate = _.cloneDeep(this.modalData);
            // 创建时默认选中read only 配置了权限
            if (this.modalData?.type === 'add'){
                const id = this.formValidate.roleList.find(o => o?.id?.includes('read-only') && o?.enableAuth)?.id || '';
                id && (this.formValidate.roles = [id]);
            }
        },
        submitConfig() {
            this.$refs['formValidate'].validate((valid) => {
                if (valid) {
                    const roles = [];
                    this.formValidate.roleList.forEach(o => {
                        if (this.formValidate.roles.includes(o.id)) {
                            roles.push({
                                id: o.id,
                                roleName: o.roleName
                            });
                        }
                    });
                    this.$emit('add-or-edit-user', {
                        ...this.formValidate,
                        roles: roles,
                        roleNames: roles?.map(v => v.roleName)
                    });
                }
            });
        },
        isShowPassWord() {
            this.showPassWord = !this.showPassWord;
        },
        checkboxStatus(id) {
            const roles = this.formValidate.roles || [];
            return roles.length >= 10 && roles.indexOf(id) === -1;
        },
        // 自动生成密码
        generatePassword() {
            const letters = 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ';
            const numbers = '0123456789';
            const specialChars = '!@#$*_';

            const minLength = 8;
            const maxLength = 20;
            const length = Math.floor(Math.random() * (maxLength - minLength + 1)) + minLength;

            let password = '';

            password += letters[Math.floor(Math.random() * letters.length)];
            password += numbers[Math.floor(Math.random() * numbers.length)];
            password += specialChars[Math.floor(Math.random() * specialChars.length)];

            const allChars = letters + numbers + specialChars;

            for (let i = password.length; i < length; i++) {
                const char = allChars[Math.floor(Math.random() * allChars.length)];
                password += char;
            }
            password = password.split('').sort(() => Math.random() - 0.5).join('');

            this.formValidate.password = password;
        }
    },
    components: { aButton }
};
</script>
<style lang="less" scoped>
/deep/ .h-icon .h-icon-search .h-input-icon {
    cursor: pointer;

    &:hover {
        color: var(--link-color);
    }
}

/deep/ .h-checkbox-group {
    width: 100%;
    max-height: 120px;
    overflow: scroll;
}

.input-text {
    font-size: 12px;
    color: #9296a1;
    line-height: 20px;
    font-weight: 400;
}
</style>
