<template>
    <div class="tab-box">
        <a-loading v-show="loading"></a-loading>
        <div v-show="!loading">
            <!-- 数据库处理性能总览 -->
            <info-grid :gridData="globalData"></info-grid>
            <!-- 内存表运行 -->
            <obs-table
                ref="table"
                :title="dirTitle"
                :tableData="dirTableData"
                :columns="dirColumns"
                notSetWidth
                autoHeadWidth
                highlightRow
                rowSelectOnly
                :maxHeight="200"
                @select-change="topSelectChange" />
            <!-- 数据库配置 -->
            <info-grid ref="singleRcm" :gridData="singleData" @select-change="topSelectChange"></info-grid>
        </div>
    </div>
</template>

<script>
import _ from 'lodash';
import { formatChartNumber, formatNumber } from '@/utils/utils';
import { getManagerProxy } from '@/api/mcApi';
import aLoading from '@/components/common/loading/aLoading';
import obsTable from '@/components/common/obsTable/obsTable';
import infoGrid from '@/components/common/infoBar/infoGrid';
export default {
    props: {
        nodeData: {
            type: Object,
            default: () => { }
        }
    },
    components: { aLoading, infoGrid, obsTable },
    data() {
        return {
            timer: null,
            pointNum: 60,
            loading: false,
            // 数据处理性能
            globalData: {
                title: {
                    label: '数据库内存总览'
                },
                layout: [
                    { x: 0, y: 0, w: 4, h: 9, i: 'info1' },
                    { x: 4, y: 0, w: 8, h: 9, i: 'info2' }
                ],
                details: [
                    {
                        type: 'obj',
                        title: '内存使用分布',
                        infoDic: [
                            {
                                label: 'TotalMemoryMB',
                                key: 'TotalMemoryMB'
                            },
                            {
                                label: 'TotalRecordCount',
                                key: 'TotalRecordCount'
                            }
                        ],
                        info: {}
                    },
                    {
                        type: 'chart',
                        info: {
                            height: '170',
                            basicOpiton: {
                                chartType: 'axis',
                                lineDeploy: [
                                    {
                                        name: 'TotalMemoryMB',
                                        type: 'line'
                                    },
                                    {
                                        name: 'TotalRecordCount',
                                        type: 'line',
                                        yAxisIndex: 1
                                    }
                                ],
                                yAxiDeploy: [
                                    {
                                        name: 'TotalMemoryMB',
                                        alignTicks: true,
                                        axisLabel: {
                                            formatter: formatChartNumber
                                        }
                                    },
                                    {
                                        name: 'TotalRecordCount',
                                        show: true,
                                        alignTicks: true,
                                        axisLabel: {
                                            formatter: formatChartNumber
                                        }
                                    }
                                ]
                            },
                            additionalOpiton: {
                                backgroundColor: '#2d334c',
                                grid: {
                                    left: 50,
                                    right: 55
                                }
                            },
                            chartData: {
                                xData: [],
                                data: {
                                    TotalMemoryMB: [],
                                    TotalRecordCount: []
                                }
                            }
                        }
                    }
                ]
            },
            // 数据内存表运行信息
            dirTitle: {
                label: '内存表运行',
                slots: [
                    {
                        key: 'funcSelect',
                        type: 'select',
                        defaultValue: '10',
                        // filter: true,
                        options: [
                            {
                                value: '10',
                                label: 'Top10'
                            },
                            {
                                value: '30',
                                label: 'Top30'
                            },
                            {
                                value: '50',
                                label: 'Top50'
                            }
                        ]
                    }
                ]
            },
            dirColumns: [
                {
                    title: 'Name',
                    key: 'Name'
                },
                {
                    title: '总记录数',
                    // key: 'RecordCount',
                    render: (h, params) => {
                        return h(
                            'div',
                            formatNumber(params.row.RecordCount + params.row.TempRecordCount)
                        );
                    }
                },
                {
                    title: 'RecordCount',
                    key: 'RecordCount',
                    render: (h, params) => {
                        return h(
                            'div',
                            formatNumber(params.row.RecordCount)
                        );
                    }
                },
                {
                    title: 'TempRecordCount',
                    key: 'TempRecordCount',
                    render: (h, params) => {
                        return h(
                            'div',
                            formatNumber(params.row.TempRecordCount)
                        );
                    }
                },
                {
                    title: 'TotalMemoryMB',
                    key: 'TotalMemoryMB'
                },
                {
                    title: '占已使用内存比率',
                    key: 'UseMemoryPercent',
                    render: (h, params) => {
                        const ratio = params.row.TotalMemoryMB / this.globalData.details[0].info?.TotalMemoryMB || 0;
                        return h(
                            'div',
                            (Number(ratio) * 100).toFixed(2)  + '%'
                        );
                    }
                }
            ],
            dirTableData: [],
            // 单内存表数据观测
            singleData: {
                title: {
                    label: '内存表名',
                    value: '',
                    poptipInfo: {
                        placement: 'top-start',
                        title: '-',
                        contentDic: {},
                        content: {
                            Name: '-',
                            Category: '-',
                            WriteRedo: '-',
                            RecordDataSize: '-',
                            RecordDataSizeAligned: '-',
                            RefIndexCount: '-',
                            RefRecordCount: '-'
                        }
                    },
                    slots: [
                        {
                            key: 'memory',
                            type: 'select',
                            defaultValue: '',
                            filter: true,
                            placeholder: '请搜索内存表',
                            options: []
                        }
                    ]
                },
                layout: [
                    { x: 0, y: 0, w: 4, h: 9, i: 'info1' },
                    { x: 4, y: 0, w: 8, h: 9, i: 'info2' },
                    { x: 0, y: 9, w: 4, h: 9, i: 'info3' },
                    { x: 4, y: 9, w: 8, h: 9, i: 'info4' },
                    { x: 0, y: 18, w: 4, h: 9, i: 'info5' },
                    { x: 4, y: 18, w: 8, h: 9, i: 'info6' },
                    { x: 0, y: 27, w: 9, h: 14, i: 'info7' },
                    { x: 9, y: 27, w: 3, h: 14, i: 'info8' }
                ],
                details: [
                    {
                        type: 'obj',
                        title: '表记录数增长统计',
                        infoDic: [
                            {
                                label: 'RecordCount',
                                key: 'RecordCount'
                            },
                            {
                                label: 'TempRecordCount',
                                key: 'TempRecordCount'
                            }
                        ],
                        info: {}
                    },
                    {
                        type: 'chart',
                        info: {
                            height: '170',
                            basicOpiton: {
                                chartType: 'axis',
                                lineDeploy: [
                                    {
                                        name: 'RecordCount',
                                        type: 'line'
                                    },
                                    {
                                        name: 'TempRecordCount',
                                        type: 'line'
                                    }
                                ],
                                yAxiDeploy: [
                                    {
                                        alignTicks: true,
                                        axisLabel: {
                                            formatter: formatChartNumber
                                        }
                                    }
                                ]
                            },
                            additionalOpiton: {
                                backgroundColor: '#2d334c'
                            },
                            chartData: {
                                xData: [],
                                data: {
                                    RecordCount: [],
                                    TempRecordCount: []
                                }
                            }
                        }
                    },
                    {
                        type: 'obj',
                        title: '表内存使用增长统计',
                        infoDic: [
                            {
                                label: 'TotalMemoryMB',
                                key: 'TotalMemoryMB'
                            }
                        ],
                        info: {}
                    },
                    {
                        type: 'chart',
                        info: {
                            height: '170',
                            basicOpiton: {
                                chartType: 'axis',
                                lineDeploy: [
                                    {
                                        name: 'TotalMemoryMB',
                                        type: 'line'
                                    }
                                ],
                                yAxiDeploy: [
                                    {
                                        alignTicks: true,
                                        axisLabel: {
                                            formatter: formatChartNumber
                                        }
                                    }
                                ]
                            },
                            additionalOpiton: {
                                backgroundColor: '#2d334c'
                            },
                            chartData: {
                                xData: [],
                                data: {
                                    TotalMemoryMB: []
                                }
                            }
                        }
                    },
                    {
                        type: 'obj',
                        title: '表内存使用分布',
                        infoDic: [
                            {
                                label: 'RecordMemoryMB',
                                key: 'RecordMemoryMB'
                            },
                            {
                                label: 'TempRecordMemoryMB',
                                key: 'TempRecordMemoryMB'
                            },
                            {
                                label: 'VarFieldMemoryMB',
                                key: 'VarFieldMemoryMB'
                            },
                            {
                                label: 'IndexMemoryMB',
                                key: 'IndexMemoryMB'
                            }
                        ],
                        info: {}
                    },
                    {
                        type: 'chart',
                        info: {
                            height: '170',
                            basicOpiton: {
                                chartType: 'axis',
                                lineDeploy: [
                                    {
                                        name: 'RecordMemoryMB',
                                        type: 'line'
                                    },
                                    {
                                        name: 'TempRecordMemoryMB',
                                        type: 'line'
                                    },
                                    {
                                        name: 'VarFieldMemoryMB',
                                        type: 'line'
                                    },
                                    {
                                        name: 'IndexMemoryMB',
                                        type: 'line'
                                    }
                                ],
                                yAxiDeploy: [
                                    {
                                        alignTicks: true,
                                        axisLabel: {
                                            formatter: formatChartNumber
                                        }
                                    }
                                ]
                            },
                            additionalOpiton: {
                                backgroundColor: '#2d334c'
                            },
                            chartData: {
                                xData: [],
                                data: {
                                    RecordMemoryMB: [],
                                    TempRecordMemoryMB: [],
                                    VarFieldMemoryMB: [],
                                    IndexMemoryMB: []
                                }
                            }
                        }
                    },
                    {
                        type: 'table',
                        title: '表数据存储-文件',
                        info: {
                            tableData: [],
                            columns: [{
                                key: 'Type',
                                title: 'Type',
                                render: (h, params) => {
                                    const file = params.row.TableFile || params.row.IndexFile;
                                    const list = file?.split('.') || [];
                                    return h(
                                        'div',
                                        list?.[1] || '-'
                                    );
                                }
                            }, {
                                key: 'TableFile',
                                title: 'File',
                                render: (h, params) => {
                                    const file = params.row.TableFile || params.row.IndexFile;
                                    return h(
                                        'div',
                                        file || '-'
                                    );
                                }
                            },
                            {
                                key: 'FileSizeMB',
                                title: 'FileSizeMB'
                            }, {
                                key: 'FileVersion',
                                title: 'FileVersion'
                            }]
                        }
                    },
                    {
                        type: 'obj',
                        title: '表索引信息',
                        infoDic: [
                            {
                                label: 'IndexCount',
                                key: 'IndexCount'
                            }
                        ],
                        info: {}
                    }
                ]
            },
            // 原始数据样本
            memoryTables: []
        };
    },
    mounted() {
    },
    methods: {
        cleanData() {
            const chartData = this.globalData.details[1].info.chartData;
            chartData.xData = [];
            chartData.data.TotalMemoryMB = [];
            chartData.data.TotalRecordCount = [];
            this.singleData.details.forEach(ele => {
                if (ele.type === 'chart') {
                    const cData = ele.info.chartData;
                    cData.xData = [];
                    Object.keys(cData.data).forEach(item => {
                        cData.data[item] = [];
                    });
                }
            });
        },
        async initData() {
            this.loading = true;
            this.cleanData();
            await this.getFileData();
            this.loading = false;
            this.$refs['table'].setSelectVal('funcSelect', '10');
            this.$refs['singleRcm'].setSelectVal('memory', this.singleData.title.slots[0].options[0]?.key);
        },
        async getFileData() {
            try {
                const newTime1 = this.handleChartBefore(this.globalData.details[1].info.chartData);
                // 判断x轴的点是否已经存在
                if (!newTime1) return;
                const newTime2 = this.handleChartBefore(this.singleData.details[1].info.chartData, 1);

                // 请求接口
                const { memoryTables } = await this.getAPi();

                this.memoryTables = memoryTables?.Tables || [];
                if (memoryTables.Tables) {
                    // 渲染总览图表
                    this.handleGlobalData(memoryTables.Tables, newTime1);

                    // 内存表运行表格
                    this.generalTableData(undefined, memoryTables.Tables);

                    // 获取所有table
                    this.getAllTables(memoryTables.Tables);

                    // 单个内存表
                    this.generalSingleData(newTime2);
                }
            } catch (error) {
                this.$emit('clear');
            }
        },
        // 接口请求
        async getAPi() {
            const data = {
                memoryTables: {}
            };
            const param = [
                {
                    manageProxyIp: this.nodeData.ip,
                    manageProxyPort: this.nodeData.manageProxyPort,
                    pluginName: 'ldp_bizproc',
                    funcName: 'uftmdb_data_GetMdbTableInfo'
                }
            ];
            const res = await getManagerProxy(JSON.stringify(param));
            if (res.code === '200') {
                !res.data?.[0]?.ErrorNo && Object.keys(res.data?.[0]).length  && (data.memoryTables = res.data[0]);
            }
            return data;
        },
        // 预处理请求前数据
        handleChartBefore(chartData, type) {
            if (chartData.xData.length > this.pointNum) {
                chartData.xData.shift();
            }
            const newTime = this.$getCurrentLocalTime();
            const index = chartData.xData.indexOf(newTime);
            if (index === -1) {
                // 内存表总览走势图
                chartData.xData.push(newTime);
                if (type) {
                    const times = chartData.xData;
                    this.singleData.details[3].info.chartData.xData = times;
                    this.singleData.details[5].info.chartData.xData = times;
                }
                return newTime;
            }
            return '';
        },
        // 处理性能总览数据
        handleGlobalData(tables, newTime) {
            let TotalMemoryMB = 0, TotalRecordCount = 0;
            tables.forEach(ele => {
                TotalMemoryMB += ele.TotalMemoryMB;
                TotalRecordCount += ele.RecordCount;
            });

            const chartData = this.globalData.details[1].info.chartData;
            const index = chartData.xData.indexOf(newTime);
            this.$set(chartData.data.TotalMemoryMB, index, TotalMemoryMB);
            this.$set(chartData.data.TotalRecordCount, index, TotalRecordCount);

            this.globalData.details[0].info = {
                TotalMemoryMB,
                TotalRecordCount
            };
        },
        // 监控下拉组件变化
        topSelectChange(val, key) {
            if (key === 'funcSelect') {
                this.dirTitle.slots[0].defaultValue = val;
                this.generalTableData(val);
            } else if (key === 'memory') {
                this.singleData.title.slots[0].defaultValue = val;
                const data = _.find(this.dirTableData, ['Name', val]);
                for (const prop in this.singleData.title.poptipInfo.content) {
                    if (data?.hasOwnProperty(prop)) {
                        this.singleData.title.poptipInfo.content[prop] = data[prop];
                    }
                }
                this.singleData.title.poptipInfo.title = val;
                this.singleData.title.value = val;
                this.clearSingleData();
            }
        },
        // 清理singleData数据
        clearSingleData() {
            this.singleData.details.forEach(ele => {
                if (ele.info.chartData) {
                    ele.info.chartData.xData = [];
                    Object.keys(ele.info.chartData.data).forEach(item => {
                        ele.info.chartData.data[item] = [];
                    });
                } else if (ele.info.tableData) {
                    ele.info.tableData = [];
                } else {
                    ele.info = {};
                }
            });
        },
        // 处理生成内存表运行表格数据
        generalTableData(val, tables) {
            const sortedData = _.orderBy(tables || this.memoryTables, ['RecordCount'], 'desc');
            this.dirTableData = _.slice(sortedData, 0, Number(val || this.dirTitle.slots[0].defaultValue));
        },
        // 获取所有内存表记录
        getAllTables(tables) {
            const options = [];
            tables.forEach(ele => {
                options.push({
                    key: ele.Name,
                    value: ele.Name });
            });
            this.singleData.title.slots[0].options = options;
        },
        // 处理单个内存表
        generalSingleData(newTime) {
            const data = _.find(this.dirTableData, ['Name', this.singleData.title.slots[0].defaultValue]);
            if (!data) return;
            // 表记录增长统计
            this.singleData.details[0].info = data;

            const chartData1 = this.singleData.details[1].info.chartData;
            const index = chartData1.xData.indexOf(newTime);
            this.$set(chartData1.data.TempRecordCount, index, data.TempRecordCount);
            this.$set(chartData1.data.RecordCount, index, data.RecordCount);
            // 表内存使用增长统计
            this.singleData.details[2].info = data;
            const chartData2 = this.singleData.details[3].info.chartData;
            this.$set(chartData2.data.TotalMemoryMB, index, data.TotalMemoryMB);

            // 表内存使用分布
            this.singleData.details[4].info = data;
            const chartData3 = this.singleData.details[5].info.chartData;
            Object.keys(chartData3.data).forEach(ele => {
                this.$set(chartData3.data[ele], index, data[ele]);
            });

            // 表数据存储｜表索引信息
            this.singleData.details[6].info.tableData = data.Files;
            this.singleData.details[7].info = data;
        }
    }
};
</script>

