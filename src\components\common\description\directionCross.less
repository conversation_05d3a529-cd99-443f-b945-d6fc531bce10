.descriptions-cross {
    display: flex;
    width: 100%;
    background-color: var(--wrapper-color);
    border-radius: var(--border-radius);
    margin: 10px 8px 0;
    min-height: 100px;

    .descriptions-title {
        display: flex;
        width: 30px;
        min-width: 30px;
        font-size: var(--font-size);
        color: var(--font-color);
        border-radius: 4px 0 0 4px;
        background-color: var(--box-color);
        justify-content: center;
        align-items: center;

        > span {
            text-align: center;
            width: 24px;
        }
    }

    .descriptions-group {
        display: flex;
        flex-wrap: wrap;
        padding: 5px;

        .descriptions-item {
            min-width: 90px;
            padding: 5px;
            text-align: center;

            .descriptions-item-label {
                color: var(--font-opacity-color);
            }

            .descriptions-item-value {
                height: 16px;
                overflow: hidden;
                color: var(--font-color);
                font-size: var(--font-size);
                text-overflow: ellipsis;
                white-space: nowrap;
            }
        }
    }
}

