import './public.less';
import { mapState } from 'vuex';
import { getDowngradeConfigList, deleteRuleConfig, createOrUpdateConfig } from '@/api/brokerApi';
import aTitle from '@/components/common/title/aTitle';
import aButton from '@/components/common/button/aButton';
import normalTable from '@/components/common/bestTable/normalTable/normalTable';
import addOrEditDataModal from '@/components/brokerDataLimit/modal/addOrEditDataModal.vue';
export default {
    name: 'BlackConfigList',
    props: {
        productId: {
            type: String,
            default: ''
        }
    },
    data() {
        return {
            ruleType: 'tps',
            loading: false,
            modalInfo: {
                status: false,
                title: ''
            },
            formItems: [
                {
                    type: 'input',
                    key: 'accountId',
                    label: '资金账号',
                    value: '',
                    placeholder: '请输入资金账号'
                },
                {
                    type: 'input',
                    key: 'functionNo',
                    label: '功能号',
                    value: '',
                    placeholder: '请输入功能号'
                },
                {
                    type: 'input',
                    key: 'shardingNo',
                    label: '分片号',
                    value: '',
                    placeholder: '请输入分片号'
                },
                {
                    type: 'input',
                    key: 'groupName',
                    label: '组名称',
                    value: '',
                    placeholder: '请输入组名称'
                },
                {
                    type: 'select',
                    label: '开启状态',
                    key: 'enable',
                    options: [{
                        value: 'all',
                        label: '全部'
                    }, {
                        value: 1,
                        label: '启用'
                    }, {
                        value: 0,
                        label: '停用'
                    }],
                    value: 'all'
                }
            ],
            columns: [
                {
                    title: '名称',
                    key: 'ruleName',
                    width: 150,
                    ellipsis: true
                },
                {
                    title: '资金账号',
                    ellipsis: true,
                    render: (h, params) => {
                        return h('span', {
                            attrs: {
                                title: params.row.ruleMap.accountIds
                            }
                        }, params.row.ruleMap.accountIds);
                    }
                },
                {
                    title: '功能号',
                    key: 'functionNos',
                    ellipsis: true,
                    render: (h, params) => {
                        return h('span', {
                            attrs: {
                                title: params.row.ruleMap.functionNos
                            }
                        }, params.row.ruleMap.functionNos);
                    }
                },
                {
                    title: '组名称',
                    key: 'groupName',
                    ellipsis: true,
                    render: (h, params) => {
                        const groups = params.row.groups.map(v => { return v.groupName; }).join(',');
                        return h('span', {
                            attrs: {
                                title: groups
                            }
                        }, groups);
                    }
                },
                {
                    title: '分片号',
                    key: 'shardingNos',
                    ellipsis: true,
                    render: (h, params) => {
                        return h('span', {
                            attrs: {
                                title: params.row.ruleMap.shardingNos
                            }
                        }, params.row.ruleMap.shardingNos);
                    }
                },
                {
                    title: '限流值（条/秒）',
                    key: 'fc',
                    ellipsis: true,
                    render: (h, params) => {
                        return h('span', {
                            attrs: {
                                title: params.row.ruleMap.fc
                            }
                        }, params.row.ruleMap.fc);
                    }
                },
                {
                    title: '最近一次编辑时间',
                    key: 'gmtModified',
                    width: 160,
                    ellipsis: true
                },
                {
                    title: '是否启用',
                    key: 'enable',
                    ellipsis: true,
                    render: (h, params) => {
                        return h('h-switch', {
                            props: {
                                value: params.row.enable,
                                size: 'large'
                            },
                            on: {
                                'on-change': (val) => { this.enableChange(val, params.index); }
                            },
                            scopedSlots: {
                                open: () => '启用',
                                close: () => '停用'
                            }
                        });
                    }
                },
                {
                    title: '操作',
                    key: 'action',
                    fixed: 'right',
                    width: 110,
                    render: (h, params) => {
                        return h('div', [
                            h('Button',
                                {
                                    props: {
                                        size: 'small',
                                        type: 'text'
                                    },
                                    on: {
                                        click: () => { this.handleConfigModal('edit', params.row); }
                                    }
                                },
                                '编辑'
                            ),
                            h('Button',
                                {
                                    props: {
                                        size: 'small',
                                        type: 'text'
                                    },
                                    on: {
                                        click: () => {
                                            this.$hMsgBoxSafe.confirm({
                                                title: `删除`,
                                                content: `您确认要删除名为"${params.row.ruleName}"的配置吗？`,
                                                onOk: async () => {
                                                    this.handleDeleteRule(params.row.id);
                                                }
                                            });
                                        }
                                    }
                                },
                                '删除'
                            )
                        ]);
                    }
                }
            ],
            tableData: [],
            total: 0
        };
    },
    mounted() {
    },
    computed: {
        ...mapState({
            transportTempList: state => {
                return state.rcm.transportTempList || [];
            }
        })
    },
    methods: {
        // 初始化数据
        async initData() {
            this.$refs['table'].$_handleQuery();
        },
        // 查询
        async handleQuery(val){
            const param = {
                productId: this.productId,
                accountId: val?.accountId || '',
                functionNo: val?.functionNo || '',
                shardingNo: val?.shardingNo || '',
                groupName: val?.groupName || '',
                enable: val?.enable === 'all' ? '' : Boolean(val?.enable),
                ruleType: this.ruleType
            };
            this.tableData = [];
            try {
                this.loading = true;
                const res = await getDowngradeConfigList(param);
                this.tableData = res?.data || [];
            } catch (err) {
                this.$hMessage.error(err.message);
            }
            this.loading = false;
        },
        // 状态启用停用
        async enableChange(value, index) {
            const param = this.tableData[index];
            param.enable = value;
            try {
                const res = await createOrUpdateConfig(param);
                if (!res.success) {
                    this.$hMessage.error(res.message);
                    this.$refs['table'].$_handleQuery();
                }
            } catch (error) {
                console.error(error);
            }
        },
        // 创建或编辑数据
        handleConfigModal(type, data) {
            this.modalInfo.status = true;
            this.modalInfo.title = type === 'create' ? '创建名单' : '修改名单';
            this.modalInfo.ruleType = this.ruleType;
            this.modalInfo.type = type;
            this.modalInfo.data = data;
        },
        // 删除行
        async handleDeleteRule(id) {
            try {
                const res = await deleteRuleConfig({ id });
                if (res.success) {
                    this.$hMessage.success('删除成功');
                    this.$refs['table'].$_handleQuery();
                } else {
                    this.$hMessage.error('删除失败');
                }
            } catch (error) {
                console.error(error);
            }
        }
    },
    render() {
        return <div class="topic-box">
            <normal-table
                ref="table"
                formTitle="筛选条件"
                tableTitle="名单列表"
                formItems={this.formItems}
                columns={this.columns}
                loading={this.loading}
                tableData={this.tableData}
                hasSetTableColumns={false}
                showTitle={true}
                hasPage={false}
                v-on:query={this.handleQuery}>
                <div class='table-slot-box' slot='btns'>
                    <a-button type="primary" onClick={() => { this.handleConfigModal('create'); }}>创建</a-button>
                </div>
            </normal-table>
            <style jsx>
                {
                    `    
                        .table-slot-box > .h-btn {
                            margin-right: 10px;
                        }
                        .publish-box .h-modal-body {
                        }
                    `
                }
            </style>
            {
                this.modalInfo.status ? <add-or-Edit-data-modal
                    modalInfo={this.modalInfo}
                    productId={this.productId}
                    v-on:query={() => { this.$refs['table'].$_handleQuery(); }} /> : ''
            }
        </div>;
    },
    components: { addOrEditDataModal, normalTable, aTitle, aButton }
};
