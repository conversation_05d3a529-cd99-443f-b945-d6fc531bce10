<template>
    <div class="tab-box">
        <a-loading v-if="loading"></a-loading>
        <div v-else>
            <!-- 事务处理吞吐/死锁检测 -->
            <info-grid :gridData="handlingData" />
            <!-- 事务控制器运行列表 -->
            <obs-table
                ref="table1"
                height="300"
                :title="dirTitle"
                :tableData="dirTableData"
                :columns="dirColumns"
                :notSetWidth="true"
                :autoHeadWidth="true"
                :hasPage="false"
                @check-change="handleCheckChange" />
            <!-- 事务控制器Undo详情 -->
            <info-grid ref="undo" :gridData="undoData" @select-change="handelSelectChange" />
        </div>
    </div>
</template>

<script>
import _ from 'lodash';
import { getManagerProxy } from '@/api/mcApi';
import aLoading from '@/components/common/loading/aLoading';
import obsTable from '@/components/common/obsTable/obsTable';
import infoGrid from '@/components/common/infoBar/infoGrid';
const Big_CommitSqn = '18446744073709551615';
export default {
    props: {
        nodeData: {
            type: Object,
            default: () => { }
        },
        timerInterval: {
            type: Number,
            default: 5
        }
    },
    components: { aLoading, obsTable, infoGrid },
    data() {
        return {
            loading: false,
            processList: [],    // 原样本数据
            handlingData: {     // 事务处理吞吐/死锁检测
                layout: [
                    { x: 0, y: 0, w: 6, h: 14, i: 'info1' },
                    { x: 6, y: 0, w: 6, h: 14, i: 'info2' }
                ],
                details: [
                    {
                        type: 'table',
                        title: {
                            label: '事务处理吞吐 - Top10'
                        },
                        info: {
                            tableData: [],
                            columns: [{
                                key: 'TCId',
                                title: 'TCId'
                            }, {
                                key: 'ProcessNo',
                                title: 'ProcessNo'
                            }, {
                                key: 'TransCtrlNo',
                                title: 'TransCtrlNo'
                            }, {
                                key: 'TransTPS',
                                title: 'TransTPS'
                            }]
                        }
                    },
                    {
                        type: 'table',
                        title: {
                            label: '死锁检测'
                        },
                        info: {
                            tableData: [],
                            columns: [{
                                key: 'TCId',
                                title: 'TCId'
                            }, {
                                key: 'ProcessNo',
                                title: 'ProcessNo'
                            }, {
                                key: 'WaitTCId',
                                title: 'WaitTCId'
                            }]
                        }
                    }
                ]
            },
            // 事务控制器运行列表
            dirTitle: {
                label: '事务控制器运行列表',
                slots: [
                    {
                        type: 'checkbox',
                        label: '只看被阻塞控制器',
                        key: 'choke',
                        defaultValue: false
                    },
                    {
                        type: 'checkbox',
                        label: '只看工作控制器',
                        key: 'work',
                        defaultValue: false
                    },
                    {
                        type: 'text',
                        label: 'Count',
                        value: '-'
                    }
                ]
            },
            dirColumns: [
                {
                    title: 'TCId',
                    key: 'TCId'
                },
                {
                    title: 'ProcessNo',
                    key: 'ProcessNo'
                },
                {
                    title: 'TransCtrlNo',
                    key: 'TransCtrlNo'
                },
                {
                    title: 'WaitRecord',
                    key: 'WaitRecord'
                },
                {
                    title: 'WaitTCId',
                    key: 'WaitTCId'
                },
                {
                    title: 'CommitSqn',
                    key: 'CommitSqn'
                },
                {
                    title: 'IsCommitting',
                    key: 'IsCommitting'
                },
                {
                    title: 'IsInTrans',
                    key: 'IsInTrans'
                },
                {
                    title: 'IsInWriteTrans',
                    key: 'IsInWriteTrans'
                },
                {
                    title: 'IsInSavePoint',
                    key: 'IsInSavePoint'
                }
            ],
            dirTableData: [],
            // 事务控制器Undo详情
            undoData: {
                title: {
                    label: '事务控制器Undo详情',
                    slots: [
                        {
                            type: 'text',
                            label: 'TCId',
                            value: ' '
                        },
                        {
                            type: 'select',
                            options: [],
                            key: 'undoCtrl',
                            defaultValue: ''
                        }]
                },
                layout: [
                    { x: 0, y: 0, w: 4, h: 8, i: 'info1' },
                    { x: 4, y: 0, w: 3, h: 8, i: 'info2' },
                    { x: 7, y: 0, w: 5, h: 8, i: 'info3' }
                ],
                details: [
                    {
                        type: 'obj',
                        title: 'Undo文件信息',
                        infoDic: [
                            {
                                key: 'UndoFile'
                            },
                            {
                                key: 'FileVersion'
                            }
                        ],
                        info: {}
                    },
                    {
                        type: 'obj',
                        title: 'Undo文件记录数信息',
                        infoDic: [
                            {
                                key: 'LogItemCount'
                            },
                            {
                                key: 'FileSizeMB'
                            },
                            {
                                key: 'MemoryMB'
                            }
                        ],
                        info: {}
                    },
                    {
                        type: 'process-bar',
                        title: 'Undo事务号: -',
                        info: [
                            {
                                key: 'AppliedBytes',
                                scale: '0',
                                text: 'AppliedBytes / LogItemBytes'
                            }
                        ]
                    }
                ]
            }
        };
    },
    mounted() {
    },
    methods: {
        async initData() {
            this.loading = true;
            this.clearData();
            await this.getFileData();
            this.loading = false;
            this.$nextTick(async () => {
                this.$refs['undo'].setSelectVal('undoCtrl', this.undoData.title.slots[1].options[0]?.label);
            });
        },
        async getFileData() {
            try {
                // 事务处理吞吐
                const { getThroughputInfo } = await this.getAPi();

                Array.isArray(getThroughputInfo.TransCtrls) &&  getThroughputInfo.TransCtrls.forEach((ele, index) => {
                    const newCommitSqn = ele.CommitSqn === Big_CommitSqn ? 0 : ele.CommitSqn || 0;
                    const nowCommitSqn = this.handlingData.details[0].info.tableData?.[index]?.CommitSqn || 0;
                    const oldCommitSqn = nowCommitSqn === Big_CommitSqn ? 0 : nowCommitSqn;
                    ele.TransTPS = ((parseInt(newCommitSqn, 10) -  parseInt(oldCommitSqn, 10)) / this.timerInterval).toFixed(2);
                });

                this.processList = getThroughputInfo.TransCtrls || [];

                const sortedData = _.orderBy(getThroughputInfo.TransCtrls, ['TransTPS'], 'desc');
                this.handlingData.details[0].info.tableData = _.slice(sortedData, 0, 10);

                // 检验死锁列表
                const deadList = [];
                getThroughputInfo.TransCtrls.forEach(item => {
                    for (const prop of getThroughputInfo.TransCtrls) {
                        if (item.TCId !== prop.TCId && prop.ProcessNo === item.ProcessNo && item.TCId === prop.WaitTCId && item.WaitTCId === prop.TCId) {
                            deadList.push(item);
                            break;
                        }
                    }
                });

                this.handlingData.details[1].info.tableData = deadList;

                // 事务控制器运行列表
                this.handleCtrlTableData();
                this.dirTitle.slots[2].value = this.dirTableData.length;

                // 事务控制器Undo详情
                this.undoData.title.slots[1].options = [];
                getThroughputInfo.TransCtrls.forEach(ele => {
                    this.undoData.title.slots[1].options.push({
                        label: ele.TCId,
                        value: ele.TCId
                    });
                });

                this.generateUndoData();
            } catch (error) {
                this.$emit('clear');
            }
        },
        // 接口请求
        async getAPi() {
            const data = {
                getThroughputInfo: {}
            };
            const param = [
                {
                    manageProxyIp: this.nodeData.ip,
                    manageProxyPort: this.nodeData.manageProxyPort,
                    pluginName: 'ldp_bizproc',
                    funcName: 'uftmdb_data_GetMdbTransCtrlInfo'
                }
            ];
            const res = await getManagerProxy(JSON.stringify(param));
            if (res.code === '200') {
                !res.data?.[0]?.ErrorNo && Object.keys(res.data?.[0]).length && (data.getThroughputInfo = res.data[0]);
            }
            return data;
        },
        // 控制器选择回调
        handleCheckChange(val, key) {
            if (key === 'choke') {
                this.dirTitle.slots[0].defaultValue = val;
            } else {
                this.dirTitle.slots[1].defaultValue = val;
            }
            this.handleCtrlTableData();
            this.dirTitle.slots[2].value = this.dirTableData.length;
        },
        clearData() {
            this.dirTitle.slots[0].defaultValue = '';
            this.dirTitle.slots[1].defaultValue = '';
        },
        // 处理控制器列表过滤数据
        handleCtrlTableData() {
            let list = this.processList;
            for (let i = 0; i < 2; i++) {
                if (this.dirTitle.slots[i].defaultValue) {
                    list = _.filter(list, item => i === 0 ? item.WaitRecord !== Big_CommitSqn : item.CommitSqn !== Big_CommitSqn);
                }
            }
            this.dirTableData = list;
        },
        // 切换选择事务控制器
        handelSelectChange(val, key) {
            this.undoData.title.slots[1].defaultValue = val;
            this.generateUndoData();
        },
        // 处理undo数据观测
        generateUndoData() {
            const data = _.find(this.handlingData.details[0].info.tableData, ['TCId', this.undoData.title.slots[1].defaultValue]) || {};
            if (data.Undo) {
                this.undoData.details.forEach((ele, index) => {
                    if (index === 2) {
                        ele.title = `Undo事务号:${data.CommitSqn || '-'}`;
                        ele.info[0].scale = ((data.Undo.AppliedBytes / data.Undo.LogItemBytes) * 100).toFixed(2) || '0';
                        ele.info[0].text = `${data.Undo.AppliedBytes || 0} / ${data.Undo.LogItemBytes || 0}`;
                    } else {
                        ele.info = { ...data.Undo };
                    }
                });
            }
        }
    }
};
</script>

