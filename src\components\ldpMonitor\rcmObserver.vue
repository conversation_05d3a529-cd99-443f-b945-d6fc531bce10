<template>
    <div class="wrapper">
        <div>
            <!-- 应用类型拓扑 -->
            <ldp-node-topo
                v-if="nodes.length"
                ref="contextTopology"
                type="context"
                :productId="productId"
                :nodes="nodes"
                :edges="edges"
                :rcmId="rcmId"
                :rcmName="rcmName"
                :queryParam="deepQueryParam"
                @rcmObsGoLink="goLink"
                @handleOpenManageInfo="handleOpenManageInfo" />
            <no-data v-else  text="暂无数据"/>
            <a-loading
                v-if="loading"
                style="width: 100%; height: calc(100% - 92px); top: 92px;">
            </a-loading>
        </div>
        <!--管理功能原配置查看-->
        <h-drawer
            v-model="visable"
            width="480"
            :closable="false">
            <template v-slot:header>
                <div class="h-drawer-header">
                <p :title="drawerTitle">{{drawerTitle}}</p>
                <h-button v-if="drawerContextMode === 0" type="text" @click="()=>goLink()">查看详情</h-button>
                </div>
            </template>
            <json-viewer class="json-drawer" :value="rightValue" :expand-depth="10" expanded copyable>
                <template v-slot:copy="{copied}">
                    <span v-if="copied">复制成功</span>
                    <span v-else>复制</span>
                </template>
            </json-viewer>
        </h-drawer>
    </div>
</template>

<script>
import { getObservableAppTypeTopology } from '@/api/topoApi';
import { getRcmOverview, getCtxRunningInfo } from '@/api/rcmApi';
import jsonViewer from 'vue-json-viewer';
import noData from '@/components/common/noData/noData';
import aLoading from '@/components/common/loading/aLoading';
import ldpNodeTopo from '@/components/common/topo/ldpNodeTopo';
export default {
    name: 'LdpRcmObserver',
    components: { noData, aLoading, ldpNodeTopo, jsonViewer },
    props: {
        productId: {
            type: String,
            default: ''
        }
    },
    data() {
        return {
            visable: false,
            loading2: false,
            drawerTitle: '',
            drawerContextMode: undefined,
            rightValue: {},
            rcmList: [],
            rcmId: '',
            rcmName: '',
            edgesList: [],
            nodeList: [],
            appClustersList: [],
            tagList: [],
            timer: null,
            loading: false,
            isFirstRender: true,
            timeLoading: false
        };
    },
    computed: {
        edges: {
            get: function() {
                return this.edgesList;
            }
        },
        nodes: {
            get: function() {
                return this.nodeList;
            }
        }
    },
    methods: {
        async init() {
            this.loading = true;
            this.clearData();
            try {
                this.getObservableAppTypeTopology();
                this.setPolling();
            } catch (error) {
                console.error(error);
                this.clearPolling();
            }
            this.loading = false;
        },
        clearData() {
            this.nodeList = [];
            this.edgesList = [];
        },
        setPolling() {
            this.clearPolling();
            this.timer = setInterval(async () => {
                if (this.timeLoading) return;
                await this.getObservableAppTypeTopology();
            }, 10000);
        },
        clearPolling() {
            this.timer && clearInterval(this.timer);
        },
        // 获取rcm产品实例总览基础信息
        async getRcmOverview() {
            const param = {
                id: this.rcmId
            };
            const res = await getRcmOverview(param);
            if (res.code === '200' && res.data?.singletonContextStatistics) {
                return (res.data?.singletonContextStatistics?.contextNum || 0) + (res.data?.clusterContextStatistics?.contextNum || 0);
            }
            return 0;
        },
        // 切换Rcm节点
        async handleRcmChange(v, list) {
            if (!v) return;
            this.rcmId = v;
            this.rcmList = list;
            this.rcmName = this.rcmList.find(e => e.id === v)?.name;
        },

        // 获取上下文连接关系
        async getObservableAppTypeTopology(data) {
            this.timeLoading = true;
            /**
             * data.instanceNodeIds存在表示为下钻跳转查询
             * 若为下钻跳转，查询拓扑需要传instanceNodeIds参数;
             * 若手动切换tab或手动切换查询条件，查询拓扑instanceNodeIds参数需要清空
             * 判断逻辑：
             * 若data存在且传参instanceNodeIds也存在，为下钻查询，instanceNodeIds值为data.instanceNodeIds
             * 若data不存在为轮询查询，instanceNodeIds值保持为上次轮询的值
             * 查询方法传参data存在且传参中instanceNodeIds字段不存在，表示为手动切换筛选条件查询，此情况需要清空instanceNodeIds
             */
            if (data && !data.instanceNodeIds && this.deepQueryParam?.instanceNodeIds) {
                delete this.deepQueryParam.instanceNodeIds;
            }

            const param = {
                ...this.deepQueryParam,
                productId: this.productId,
                type: 'context',
                ...data
            };

            this.deepQueryParam = { ...this.deepQueryParam, ...param };

            if (!param.rcmId) return;
            // 判断集群同步模式
            if (param.topoType === 'all' && param.topics.length === 0 && param.tags.length === 0 && !param.nodeIds) {
                this.nodeList = [];
                this.edgesList = [];
                return;
            }
            if (param.topoType === 'cluster' && (!String(param?.syncMode) && (!param?.clusterName)) && !param.nodeIds) {
                this.nodeList = [];
                this.edgesList = [];
                return;
            }
            try {
                const res = await getObservableAppTypeTopology(param);
                if (this.productId !== param.productId) return;
                if (res.success) {
                    this.nodeList = res?.data?.nodes || [];
                    this.edgesList = res?.data?.edges || [];
                    this.$nextTick(() => {
                        this.$refs['contextTopology'] && this.$refs['contextTopology'].init();
                    });
                } else {
                    this.stopProcess();
                }
            } catch (error){
                console.error(error);
                this.clearPolling();
            }
            this.timeLoading = false;
        },
        stopProcess() {
            this.nodeList = [];
            this.edgesList = [];
            this.clearPolling();
        },
        // 打开管理功能详情抽屉
        async handleOpenManageInfo(name, mode) {
            this.visable = true;
            this.rightValue = await this.getCtxRunningInfo(name);
            this.drawerTitle = name;
            this.drawerContextMode = mode;
        },
        // 接口获取rcm节点信息
        async getCtxRunningInfo(name) {
            const res = await getCtxRunningInfo({
                rcmId: this.rcmId,
                name: name,
                productInstNo: this.productId
            });
            return res?.data || {};
        },
        // 跳转到rcm观测页面
        goLink(name) {
            const query = {
                rcmId: this.rcmId,
                contextName: name || this.drawerTitle,
                rcmName: this.rcmList.find(item => item.id === this.rcmId)?.name
            };
            this.$hCore.navigate('/rcmObservation', { history: true }, query);
        }
    }, beforeDestroy() {
        this.clearPolling();
    }
};
</script>
<style lang="less" scoped>
.wrapper {
    width: 100%;
    height: 100%;
    padding: 0 10px;
    background-color: var(--main-color);

    & > div {
        width: 100%;
        height: 100%;
        min-width: 900px;
    }
}

.json-drawer {
    height: 100%;
    overflow: auto;

    /deep/ .jv-code {
        height: 100%;
    }
}

.h-drawer-header {
    p {
        width: 80%;
    }

    .h-btn {
        position: absolute;
        top: 22px;
        right: 0;
    }
}
</style>
