
/**
 * 处理树状结构的数据 用于 tree-table 展示格式
 * @param {Object} schema - 输入要处理的数据
 * @param {String} parentKey
 * @returns {Array} 处理后用于 tree-table 展示的数据
 */
export function transformSchema(schema, parentKey = '') {
    if (!schema?.properties) return [];

    const treeData = [];
    const properties = schema?.properties;

    for (const key in properties) {
        // 安全防护：过滤原型链上的属性
        if (Object.prototype.hasOwnProperty.call(properties, key)) {
            const prop = properties[key];
            const currentKey = parentKey ? `${parentKey}.${key}` : key;

            // 处理数组类型
            if (prop.type === 'array') {
                // 获取数组元素类型
                const itemType = prop.items?.type;
                const arrayType = itemType === 'object' ? 'object' : itemType;

                const node = {
                    id: currentKey,
                    paramName: key,
                    paramType: `${arrayType || 'any'} []`,
                    paramDesc: prop.remark || '',
                    enumValues: prop.enumValues
                };

                // 处理数组元素的嵌套结构
                if (prop.items && prop.items.properties) {
                    node.children = transformSchema(
                        prop.items,
                        parentKey ? `${parentKey}.${key}[]` : `${key}[]`
                    );
                }
                treeData.push(node);
            } else if (prop.type === 'object' && prop.properties) {
                // 处理对象类型
                const node = {
                    id: currentKey,
                    paramName: key,
                    paramType: 'object',
                    paramDesc: prop.remark || '',
                    enumValues: prop.enumValues,
                    children: transformSchema(prop, currentKey)
                };
                treeData.push(node);
            } else {
                // 处理基本类型
                treeData.push({
                    id: currentKey,
                    paramName: key,
                    paramType: prop.type,
                    paramDesc: prop.remark || '',
                    enumValues: prop.enumValues
                });
            }
        }
    }

    return treeData;
}
