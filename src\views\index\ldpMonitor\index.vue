<template>
    <div class="main">
        <observe-head
            ref="obsHead"
            :title="productTypeAlias"
            :productInstNo="productInstNo"
            :productList="productList"
            :timeNode="timeNode"
            :selectedSpan="selectedSpan"
            @check-product="checkProduct"
            @selected-span="checkTimeSpan"></observe-head>
        <div v-if="productInstNo" ref="wrapper" class="wrapper-flex">
            <apm-monitor-bar ref="monitor-bar" :barList="barList[selectedSpan]" style="flex-shrink: 0;" @jump="jump" />
            <router-view :productInstNo="productInstNo" :productInfo="productInfo" :date="currentTime"></router-view>
        </div>
        <a-loading v-if="loading"></a-loading>
    </div>
</template>
<script>
import _ from 'lodash';
import { mapState, mapActions } from 'vuex';
import { formatDate } from '@/utils/utils';
import aLoading from '@/components/common/loading/aLoading';
import observeHead from '@/components/ldpProduct/observeHead/observeHead';
import apmMonitorBar from '@/components/ldpProduct/apmMonitorBar/apmMonitorBar';
export default {
    data() {
        return {
            currentTime: formatDate(new Date()),
            productInstNo: '',
            productInfo: {},
            selectedSpan: 'marketing',
            barList: {},
            loading: false,
            timeNode: []
        };
    },
    async mounted() {
        this.loading = true;
        await this.getProductList();
        this.loading = false;
        const productInstNo = localStorage.getItem('productInstNo');
        this.productInstNo =  _.find(this.productList, ['productInstNo', productInstNo])?.productInstNo ||
            this.productList?.[0]?.productInstNo;
        this.checkProduct(this.productInstNo);
        this.currentTime = formatDate(new Date());
    },
    computed: {
        ...mapState({
            productList: state => {
                return _.filter(state.product.productListLight, function (o) {
                    return o.productType !== 'NSQ1.0';
                }) || [];
            }
        }),
        productTypeAlias: function () {
            return this.$getProductType(this.productInfo.productType);
        }
    },
    methods: {
        ...mapActions({ getProductList: 'product/getProductListLight' }),
        jump(key) {
            localStorage.setItem('ldpMonitorPath', `${this.selectedSpan}#${key}`);
            this.$hCore.navigate(key);
        },
        // 切换产品
        checkProduct(e) {
            if (this.productList.length) {
                this.productInfo = e ? _.find(this.productList, ['productInstNo', e]) : this.productList[0];
                this.productInstNo = this.productInfo.productInstNo;
                localStorage.setItem('productInstNo', this.productInfo.productInstNo);
            }
        },
        getTimeNode(features) {
            return [
                {
                    startTime: '00:00',
                    endTime: '09:15',
                    key: 'marketStart',
                    name: '盘前',
                    clickable: false
                },
                {
                    startTime: '09:15',
                    endTime: '15:00',
                    key: 'marketing',
                    name: '盘中',
                    clickable: false
                },
                {
                    startTime: '15:00',
                    endTime: '23:59:59',
                    key: 'marketEnd',
                    name: '盘后',
                    clickable: false
                }
            ];
        },
        // 切换选择时间条
        checkTimeSpan(val) {
            this.selectedSpan = val;
            const selectedKey = _.find(this.barList[this.selectedSpan], ['key', localStorage.getItem('ldpMonitorPath')])?.key || this.barList[this.selectedSpan]?.[0]?.key;
            this.$refs['monitor-bar'].handleClick(selectedKey);
        }
    },
    beforeRouteUpdate(to, from, next) {
        this.checkProduct(this.productInstNo);
        next();
    // 在当前路由改变，但是该组件被复用时调用
    // 举例来说，对于一个带有动态参数的路径 `/users/:id`，在 `/users/1` 和 `/users/2` 之间跳转的时候，
    // 由于会渲染同样的 `UserDetails` 组件，因此组件实例会被复用。而这个钩子就会在这个情况下被调用。
    // 因为在这种情况发生的时候，组件已经挂载好了，导航守卫可以访问组件实例 `this`
    },
    components: { observeHead, apmMonitorBar, aLoading }
};
</script>
<style lang="less" scoped>
@import url("@/assets/css/input.less");
@import url("@/assets/css/monitor.less");
@import url("@/assets/css/tab.less");

.tab-box {
    width: calc(100% - 33px);
    height: 100%;

    /deep/.h-tabs {
        height: 100%;
        background: var(--main-color);
    }

    /deep/ .h-tabs-nav-container {
        height: 42px;
    }

    /deep/ .h-tabs-bar {
        border-bottom: var(--border);
    }

    /deep/ .h-tabs-tab {
        padding: 10px 6px;
    }

    /deep/.h-tabs .h-tabs-content-wrap {
        height: 100%;
        overflow: hidden;
    }

    /deep/.h-tabs .h-tabs-tabpane {
        height: calc(100% - 60px);
        cursor: pointer;
        overflow: hidden;
    }

    /deep/.h-tabs .h-tabs-content {
        height: 100%;
    }

    .grid-box {
        height: calc(100% - 60px);
    }
}
</style>
