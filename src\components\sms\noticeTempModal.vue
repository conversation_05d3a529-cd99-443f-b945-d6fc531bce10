<template>
    <div>
        <!-- 下发配置列表 -->
        <h-msg-box-safe v-model="modalData.status" :escClose="true" :mask-closable="false" title="告警通知模板编辑" width="70" height="60" @on-open="getCollections">
            <h-tabs v-model="tabValue" type="card" closable :isRemoveTab="false" showArrow @on-before-tab-remove="handleTabRemove">
                <h-tab-pane v-for="(item, idx) in tabList" :key="idx" :label="item.msgTemplateName">
                    <h-form :ref="`formValidate${idx+1}`" :model="item" :label-width="80">
                        <h-form-item label="模板名称" prop="msgTemplateName" required>
                            <h-input v-model="item.msgTemplateName" />
                        </h-form-item>
                        <h-form-item label="模板内容" prop="msgTemplateContent" required>
                            <h-input v-model="item.msgTemplateContent" type="textarea" :autosize="{ minRows: 2, maxRows: 5 }" placeholder="请输入...">
                            </h-input>
                        </h-form-item>
                        <h-form-item label="通知干系人">
                            <h-switch v-model="item.addresseeSwitch" size="large">
                                <template  v-slot:open>开启</template>
                                <template v-slot:close>关闭</template>
                            </h-switch>
                            <a-table :columns="columns1" :tableData="item.addressees"  :hasPage="false" :hasDarkClass="false" style="margin-top: 10px;"></a-table>
                            <a-button class="table-add" @click="addAddressee(idx)"><h-icon name="plus-round" color="#2D8DE5" size="15" /></a-button>
                        </h-form-item>
                        <h-form-item label="外发投资者">
                            <h-switch v-model="item.investorSwitch" size="large">
                                <template  v-slot:open>开启</template>
                                <template v-slot:close>关闭</template>
                            </h-switch>
                            <!-- <p>告警通知参数存在手机号${phone}, 则发送模板短信至该手机</p> -->
                        </h-form-item>
                    </h-form>
                </h-tab-pane>
                <template v-slot:extra>
                    <h-icon name="plus-round" color="#2D8DE5" style="cursor: pointer;" @on-click="handleTabsAdd" />
                </template>
            </h-tabs>
            <template v-slot:footer>
                <a-button @click="modalData.status = false">取消</a-button>
                <a-button type="primary" :loading="loading" @click="submitConfig">确认</a-button>
            </template>
        </h-msg-box-safe>
        <h-msg-box-safe v-model="status" title="选择干系人" :mask-closable="false" width="280" maxHeight="200">
            <h-checkbox-group v-model="checkAllGroup">
                <h-checkbox v-for="item in addresseeList" :key="item.id" :label="item.id" :text="item.addresseeName"></h-checkbox>
            </h-checkbox-group>
            <template v-slot:footer>
                <a-button type="primary" @click="confirmAddressee">保存</a-button>
            </template>
        </h-msg-box-safe>
    </div>
</template>

<script>
import { getAddressee, getMsgTemplateList, saveMsgTemplate, delMsgTemplate } from '@/api/httpApi';
import aButton from '@/components/common/button/aButton';
import aTable from '@/components/common/table/aTable';
import _ from 'lodash';
export default ({
    props: {
        modalInfo: {
            type: Object,
            default: null
        }
    },
    data() {
        return {
            status: false,
            loading: false,
            modalData: this.modalInfo,
            formItem: {
            },
            columns1: [
                {
                    title: '干系人',
                    key: 'addresseeName'
                },
                {
                    title: '通知到手机',
                    key: 'smsSwitch',
                    align: 'center',
                    render: (h, params) => {
                        return h('Checkbox', {
                            props: {
                                value: params.row.smsSwitch
                            },
                            on: {
                                'on-change': (val) => {
                                    this.tabList[this.tabValue].addressees[params.index].smsSwitch = val;
                                }
                            }
                        });
                    }
                },
                {
                    title: '通知到邮箱',
                    key: 'emailSwitch',
                    align: 'center',
                    render: (h, params) => {
                        return h('Checkbox', {
                            props: {
                                value: params.row.emailSwitch
                            },
                            on: {
                                'on-change': (val) => {
                                    this.tabList[this.tabValue].addressees[params.index].emailSwitch = val;
                                }
                            }
                        });
                    }
                },
                {
                    title: '操作',
                    key: 'action',
                    fixed: 'right',
                    width: 80,
                    render: (h, params) => {
                        return h('Button',
                            {
                                props: {
                                    size: 'small',
                                    type: 'text'
                                },
                                style: {
                                    color: '#2D8DE5'
                                },
                                on: {
                                    click: () => {
                                        this.$hMsgBoxSafe.confirm({
                                            title: `删除`,
                                            content: `您确定删除名为"${params.row.addresseeName}"的干系人吗？`,
                                            onOk: () => {
                                                this.tabList[this.tabValue].addressees.splice(params.index, 1);
                                            }
                                        });
                                    }
                                }
                            },
                            '删除'
                        );
                    }
                }
            ],
            addresseeList: [],
            tabList: [],
            tabValue: 0,
            checkAllGroup: []
        };
    },
    methods: {
        getCollections() {
            this.getMsgTemplateList();
        },
        // 添加通知人员
        async addAddressee(idx) {
            this.checkAllGroup = [];
            const { data } = await getAddressee({ page: 1, pageSize: 10000 });
            const list = data?.list || [];
            this.addresseeList = list.filter(item => {
                return !this.tabList[idx]?.addressees.some(ele => ele.addresseeId === item.id);
            });
            if (this.addresseeList.length) {
                this.status = true;
            } else {
                this.$hMessage.warning('您当前无可添加干系人！');
            }
        },
        // 查询告警模版列表
        async getMsgTemplateList() {
            const { data } = await getMsgTemplateList();
            this.tabList = data || [];
        },
        // 添加tab
        handleTabsAdd() {
            // 判断是否有未提交数据
            for (const i of this.tabList) {
                if (!i.id) {
                    this.$hMessage.warning('您有未提交保存的模板，请先提交该模版数据！');
                    return;
                }
            }
            const param = {
                addresseeSwitch: true,
                investorSwitch: true,
                addressees: [],
                msgTemplateContent: '',
                msgTemplateName: '通知模板' + (this.tabList.length + 1)
            };
            this.tabList.push(param);
        },
        // 删除tab
        handleTabRemove(index, name) {
            this.$hMsgBoxSafe.confirm({
                title: `删除`,
                content: `您确定删除名为"${this.tabList[index].msgTemplateName}"的告警模板？`,
                onOk: () => {
                    const id = this.tabList[index]?.id;
                    // 如果存在id则掉删除接口，否则直接删除
                    if (id) {
                        delMsgTemplate({ id }).then(res => {
                            if (res.success) {
                                this.getMsgTemplateList();
                            } else {
                                this.$hMessage.error(res.message || '删除失败');
                            }
                        });
                    } else {
                        this.tabList.splice(index, 1);
                    }
                    this.tabValue = 0;
                }
            });
        },
        // 确认干系人
        confirmAddressee() {
            this.checkAllGroup.forEach(ele => {
                const list = this.tabList[this.tabValue].addressees;
                const item = _.find(list, ['addresseeId', ele]);
                if (!item) {
                    const param = _.find(this.addresseeList, ['id', ele]);
                    list.push({
                        addresseeId: param.id,
                        addresseeName: param.addresseeName,
                        smsSwitch: true,
                        emailSwitch: true
                    });
                }
            });
            this.status = false;
        },
        submitConfig() {
            this.loading = true;
            saveMsgTemplate(this.tabList[this.tabValue]).then(async res => {
                if (res.success) {
                    this.$hMessage.success('操作成功');
                    await this.getMsgTemplateList();
                } else {
                    this.$hMessage.error(res.message || '操作失败');
                }
                this.loading = false;
            });
        }
    },
    components: { aButton, aTable }
});
</script>

<style lang="less" scoped>
.table-add {
    width: 100%;
    border: none;

    &:hover {
        background: #dbdbdb7a;
    }
}

/deep/ .h-modal-body {
    padding: 16px;
}

/deep/ .h-tabs-tabpane {
    padding: 0 4px;
}
</style>
