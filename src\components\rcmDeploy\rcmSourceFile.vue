<!--
 * @Description: RCM发布配置
 * @Author: <PERSON><PERSON>
 * @Date: 2023-06-14 15:38:22
 * @LastEditTime: 2023-08-11 10:09:10
 * @LastEditors: <PERSON><PERSON>
-->
<template>
    <div ref="rcmSourceContainer" class="rcm-source-container">
        <h-row :gutter="16">
            <h-col span="12">
                <a-title title="待发布配置">
                    <h-poptip
                        v-if="localStatus"
                        positionFixed
                        trigger="hover"
                        transfer
                        customTransferClassName="apm-poptip monitor-poptip">
                        <h-icon name="prompt_fill" color="#ff9901" class="alert-icon"></h-icon>
                        <div slot="content">
                            本地变更：本地配置版本已经发生变更。<br>
                            【{{ baseVersion || '-' }}】→【{{ localData.version || '-' }}】
                        </div>
                    </h-poptip>
                    <a-button
                        v-if="localData.version && isLeftShowFile"
                        class="edit-btn"
                        type="dark"
                        @click="onEdit">编辑</a-button>
                    <a-button
                        v-if="localData.version"
                        class="source-btn"
                        type="dark"
                        @click="openRelaseModal('relase')">发布</a-button>
                </a-title>
            </h-col>
            <h-col span="12">
                <a-title title="已发布配置">
                    <h-poptip
                        v-if="remoteStatus"
                        content="已发布配置告警说明"
                        positionFixed
                        trigger="hover"
                        transfer
                        customTransferClassName="apm-poptip monitor-poptip">
                        <h-icon name="prompt_fill" color="#ff9901" class="alert-icon"></h-icon>
                        <div slot="content">
                            远程变更：远程zk配置版本已发生变更。<br>
                            【{{ baseVersion || '-' }} 】→【{{ remoteData.version || '-' }}】
                        </div>
                    </h-poptip>
                    <a-button
                        v-if="contrastData.version"
                        class="source-btn"
                        type="dark"
                        @click="openRelaseModal">还原</a-button>
                </a-title>
            </h-col>
        </h-row>
        <h-row :gutter="16">
            <h-col span="12" class="source-line">
                <span class="source-version">
                    本地：<i>{{ localData.version || '-' }}</i>
                </span>
                <span class="source-version">
                    最后更新版本：<i>{{ baseVersion || '-' }}</i>
                </span>
            </h-col>
            <h-col span="12" class="source-line">
                <span class="source-version">远程：
                    <i :class="{ 'high-version': (remoteData.version && !historyVersion), 'remote-version': true }"
                        @click="resetRemoteData">{{ remoteData.version || '-' }}</i>
                </span>
                <span class="source-version">历史：
                    <i :class="{ 'high-version': historyVersion }">{{ historyVersion || '-' }}</i>
                    <h-icon name="navicon-round" :size="14" color="#cacfd4" style="padding-left: 5px;"
                        @on-click="openHistoryModal">
                    </h-icon>
                </span>
            </h-col>
        </h-row>
        <h-row :gutter="16" class="rcm-text-diff">
            <h-col span="12">
                <div v-if="isLeftShowFile && !loading1" class="json-box">
                    <json-viewer :value="leftValue" :expand-depth="10" expanded copyable>
                        <template v-slot:copy="{ copied }">
                            <span v-if="copied">复制成功</span>
                            <span v-else>复制</span>
                        </template>
                    </json-viewer>
                </div>
                <h-row v-else style="height: 100%;">
                    <no-data text="无法在线预览RCM配置文件！请下载源文件进行本地查看。"></no-data>
                </h-row>
                <a-loading v-if="loading1" width="50" height="50"></a-loading>
            </h-col>
            <h-col span="12">
                <div v-if="isRightShowFile && !loading2" class="json-box">
                    <json-viewer :value="rightValue" :expand-depth="10" expanded copyable>
                        <template v-slot:copy="{ copied }">
                            <span v-if="copied">复制成功</span>
                            <span v-else>复制</span>
                        </template>
                    </json-viewer>
                </div>
                <h-row v-else style="height: 100%;">
                    <no-data text="无法在线预览RCM配置文件！请下载源文件进行本地查看。"></no-data>
                </h-row>
                <a-loading v-if="loading2" width="50" height="50"></a-loading>
            </h-col>
        </h-row>
        <h-row :gutter="16">
            <h-col span="12" class="source-line">
                <span class="source-version" style="width: 100%; word-break: break-all;">配置地址：
                    {{ localData.configAddress || '-' }}
                    <h-icon v-if="localData.configAddress" name="t-b-download" :size="12" color="#cacfd4"
                        style="padding-left: 5px;"
                        @on-click="downloadRcmFile(localData.id, localData.version)"></h-icon>
                </span>
            </h-col>
            <h-col span="12" class="source-line">
                <span class="source-version" style="width: 100%; word-break: break-all;">配置地址：
                    {{ contrastData.configAddress || '-' }}
                    <h-icon v-if="contrastData.configAddress" name="t-b-download" :size="12" color="#cacfd4"
                        style="padding-left: 5px;"
                        @on-click="downloadRcmFile(contrastData.id, contrastData.version)"></h-icon>
                </span>
            </h-col>
        </h-row>
        <history-list-modal v-if="historyInfo.status" :modalInfo="historyInfo" :historyList="historyList"
            @restContrastData="restContrastData" />
        <rcm-config-relase-modal v-if="relaseInfo.status" :modalInfo="relaseInfo" @init="initData" />
        <edit-config-modal v-if="editConfigModal.status" :modalInfo="editConfigModal" :rcmId="rcmId"
            @reload="initData" />
    </div>
</template>

<script>
import { exportFile, getByteSize } from '@/utils/utils';
import jsonViewer from 'vue-json-viewer';
import aTitle from '@/components/common/title/aTitle';
import aButton from '@/components/common/button/aButton';
import noData from '@/components/common/noData/noData';
import aLoading from '@/components/common/loading/aLoading';
import rcmConfigRelaseModal from '@/components/rcmDeploy/modal/resource/rcmConfigRelaseModal';
import historyListModal from '@/components/rcmDeploy/modal/resource/historyListModal';
import editConfigModal from './modal/resource/editConfigModal.vue';
import { getLocalRemoteConfig, getRcmProductsHistory, downloadRcmFile, getRcmFileContent } from '@/api/rcmApi';
export default {
    props: {
        rcmId: {
            type: String,
            default: ''
        },
        instanceId: {
            type: String,
            default: ''
        },
        localStatus: {
            type: Boolean,
            default: false
        },
        remoteStatus: {
            type: Boolean,
            default: false
        }
    },
    name: 'RcmSourceFile',
    data() {
        return {
            loading1: false,
            loading2: false,
            localData: {},
            remoteData: {},
            historyList: [],
            historyInfo: {
                status: false
            },
            historyVersion: '',
            relaseInfo: {
                status: false,
                title: '',
                rcmId: '',
                instanceId: '',
                remoteVersion: '',
                localVersion: '',
                contrastId: '',
                historyVersion: ''
            },
            contrastData: {},
            editConfigModal: {
                status: false
            }
        };
    },
    mounted() {
    },
    computed: {
        leftValue: {
            get() {
                let jsonStr = '';
                const data = this.localData?.config || '';
                try {
                    jsonStr = JSON.parse(data);
                } catch (error) {
                    console.log(error);
                }
                return jsonStr;
            },
            set() { }
        },
        rightValue: {
            get() {
                let jsonStr = '';
                const data = this.contrastData?.config || '';
                try {
                    jsonStr = JSON.parse(data);
                } catch (error) {
                    console.log(error);
                }
                return jsonStr;
            },
            set() { }
        },
        baseVersion: function () {
            return this.historyList?.[0]?.version || null;
        },
        isLeftShowFile: function () {
            const leftStr = this.localData?.config || '';
            return getByteSize(leftStr) < 1024 * 1024;
        },
        isRightShowFile: function () {
            const rightStr = this.contrastData?.config || '';
            return getByteSize(rightStr) < 1024 * 1024;
        }
    },
    methods: {
        initData() {
            this.historyVersion = '';
            this.historyList = [];
            this.contrastData = {};
            this.localData = {};
            this.remoteData = {};
            this.getLocalRemoteConfig(true);
            this.getLocalRemoteConfig(false);
            this.getRcmProductsHistory();
        },
        // 获取本地和远程rcm配置信息
        async getLocalRemoteConfig(isLocal) {
            this.loading = true;
            this[isLocal ? 'loading1' : 'loading2'] = true;
            const param = {
                id: this.rcmId,
                local: isLocal
            };
            const res = await getLocalRemoteConfig(param);
            if (res.code === '200' && this.rcmId === param.id) {
                if (isLocal) {
                    this.localData = { ...res.data };
                } else {
                    this.remoteData = { ...res.data };
                    this.contrastData = { ...this.remoteData };
                }
            } else if (res.code.length === 8 && this.rcmId === param.id) {
                this.$hMsgBoxSafe.error({
                    title: '服务异常',
                    content: res.message
                });
            }
            // 文件过大，数据显示有延迟
            setTimeout(() => {
                this[isLocal ? 'loading1' : 'loading2'] = false;
            }, 1500);
        },
        // 获取历史发布版本
        async getRcmProductsHistory() {
            const param = {
                instanceId: this.instanceId
            };
            const res = await getRcmProductsHistory(param);
            if (res.code === '200' && this.instanceId === param.instanceId) {
                this.historyList = res.data || [];
            }
        },
        // 打开历史发布信息配置
        async openHistoryModal() {
            this.historyInfo.status = true;
        },
        // 重置contrastData
        async restContrastData(idx) {
            this.historyVersion = this.historyList[idx].version;
            this.loading = true;
            try {
                const { data } = await getRcmFileContent({ id: this.historyList[idx].id });
                this.contrastData = { ...this.historyList[idx], config: data };
            } catch (err) {
                console.log(err);
            }
            // 文件过大，数据显示有延迟
            setTimeout(() => {
                this.loading = false;
            }, 1000);
        },
        // 重置resetRemoteData
        resetRemoteData() {
            this.historyVersion = '';
            this.contrastData = { ...this.remoteData };
        },
        // 打开发布，还原弹窗
        openRelaseModal(type) {
            this.relaseInfo.status = true;
            this.relaseInfo.title = type === 'relase' ? '配置发布' : '配置还原';
            this.relaseInfo.remoteVersion = this.remoteData.version;
            this.relaseInfo.localVersion = this.localData.version;
            this.relaseInfo.rcmId = this.rcmId;
            this.relaseInfo.instanceId = this.instanceId;
            this.relaseInfo.contrastId = this.contrastData.id;
            this.relaseInfo.historyVersion = this.historyVersion;
        },
        // 下载
        async downloadRcmFile(id, version) {
            const res = await downloadRcmFile({ id });
            res.code === '200' && exportFile(JSON.parse(res.data), version, 'json');
        },
        /**
         * 点击编辑
         */
        onEdit() {
            this.editConfigModal = {
                status: true,
                data: JSON.stringify(this.leftValue, null, 2)
            };
        }
    },
    components: { aTitle, aButton, noData, jsonViewer, aLoading, historyListModal, rcmConfigRelaseModal, editConfigModal }
};
</script>
<style lang="less">
@import url("@/assets/css/poptip-1.less");

.h-poptip-body-content-inner {
    color: var(--font-color);
}
</style>

<style lang="less" scoped>
@import url("@/assets/css/json-view");

.rcm-source-container {
    display: flex;
    flex-direction: column;
    width: 100%;
    height: 100%;
    box-sizing: border-box;
    padding: 0 10px;

    & > .rcm-text-diff {
        height: calc(100% - 145px);

        & > .h-col {
            height: 100%;
            position: relative;

            & > .json-box {
                height: 100%;

                /deep/ .jv-container {
                    height: 100%;
                    margin: 0 4px;
                }
            }
        }
    }

    .source-btn {
        position: absolute;
        top: 5px;
        right: 10px;
    }

    .edit-btn {
        position: absolute;
        top: 5px;
        right: 78px;
    }

    .alert-icon {
        padding: 5px;
        cursor: pointer;
    }
}

.source-line {
    display: flex;
    justify-content: space-between;
    padding: 10px 20px !important;
    box-sizing: border-box;
    cursor: pointer;

    .source-version {
        font-size: 14px;
        color: #cacfd4;

        & > i {
            font-style: normal;
            color: #fff;
        }
    }

    .remote-version {
        &:hover {
            color: #3fb1ee !important;
        }

        &:active {
            color: var(--link-color) !important;
        }
    }

    .high-version {
        color: var(--link-color) !important;
    }

    /deep/ .h-icon {
        cursor: pointer;
    }
}
</style>
