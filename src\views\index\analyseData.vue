<template>
    <div class="main">
        <!-- 头部标题 -->
        <a-title :title="`时延场景测试分析（${selectInstance.instanceName || ''} 测试实例详情）`">
            <slot>
                <div class="slot-box">
                    <a-button type="dark" @click="instanceInfo.status = true">选择实例</a-button>
                </div>
            </slot>
        </a-title>
        <!-- 实例详情 -->
        <h-row class="box-detail">
            <h-col span="5">
                <div class="text-label text-ellipsis">测试实例
                    <h-icon v-if="selectInstance.id" class="icon-title" name="t-b-modify" @on-click="updateCaseName(selectInstance.id, selectInstance.instanceName)"></h-icon>
                </div>
                <div class="text-value text-ellipsis" :title="selectInstance.instanceName">{{selectInstance.instanceName}}
                </div>
            </h-col>
            <h-col span="5">
                <div class="text-label text-ellipsis">来源用例</div>
                <div class="text-value text-ellipsis" :title="selectInstance.testCaseName">{{selectInstance.testCaseName}}
                </div>
            </h-col>
            <h-col span="4">
                <div class="text-label text-ellipsis">归属场景</div>
                <div class="text-value text-ellipsis" :title="selectInstance.sceneName">{{selectInstance.sceneName}}
                </div>
            </h-col>
            <h-col span="5">
                <div class="text-label text-ellipsis">开始时间</div>
                <div class="text-value text-ellipsis" :title="selectInstance.startTime">{{selectInstance.startTime}}
                </div>
            </h-col>
            <h-col span="5">
                <div class="text-label text-ellipsis">结束时间</div>
                <div class="text-value text-ellipsis" :title="selectInstance.endTime">{{selectInstance.endTime}}
                </div>
            </h-col>
        </h-row>
        <h-row class="box-detail">
            <h-col span="5">
                <div class="text-label text-ellipsis">产品节点</div>
                <div class="text-value text-ellipsis" :title="selectInstance.productInstName">{{ selectInstance.productInstName }}
                </div>
            </h-col>
            <h-col span="5">
                <div class="text-label text-ellipsis">测试状态</div>
                <div class="text-value text-ellipsis">{{selectInstance.testStatusDesc}}
                </div>
            </h-col>
            <h-col span="4">
                <div class="text-label text-ellipsis">报表状态</div>
                <div class="text-value text-ellipsis">{{selectInstance.reportStatusDesc}}
                </div>
            </h-col>
            <h-col span="5">
                <div class="text-label text-ellipsis">测试参数</div>
                <div class="text-value">
                    <h-icon v-if="selectInstance.id" class="icon-title" name="document" @on-click="openCaseConfigModal"></h-icon>
                </div>
            </h-col>
            <h-col span="5">
                <div class="text-label text-ellipsis">备注
                    <h-icon v-if="selectInstance.id" class="icon-title" name="t-b-modify" @on-click="updateCaseRemark(selectInstance.id, selectInstance.remark)"></h-icon>
                </div>
                <div v-if="selectInstance.remark" class="text-value text-ellipsis" :title="selectInstance.remark.title">{{selectInstance.remark.title}}
                </div>
            </h-col>
        </h-row>
        <!-- 头部标题 -->
        <a-title :title="`报表详情（单位：${unitName}）`">
            <slot>
                <div class="slot-box">
                    <!--市场类型选择-->
                    <h-select v-model="loopType" class="sec-loop" :clearable="false" @on-change="handleLoop">
                        <h-option v-for="item in loopList" :key="item.bizMsgType" :value="item.bizMsgType">{{ getLoopName(item.bizMsgType)
                            }}</h-option>
                    </h-select>
                    <a-button v-if="selectInstance.id" type="dark" @click="openReportConfig">配置报表</a-button>
                </div>
            </slot>
        </a-title>
        <!-- 报表详情 -->
        <a-table
            :maxHeight="270"
            :tableData="tableData"
            :columns="columns"
            :hasPage="false"
            :highlightRow="true"
            :rowSelectOnly="true"
            @onCurrentChange="handleSelected"/>
        <!-- 报表视图 -->
        <div v-if="!isNoChart" id="report-chart" class="report-chart"></div>
        <no-data v-else text="暂无图表" class="report-chart report-nodata" />
        <a-loading v-if="loading" class="report-chart report-nodata" />

        <!-- 更新实例名称 -->
        <update-case-name-modal v-if="caseNameInfo.status" :modalInfo="caseNameInfo" @update="queryInstance" />

        <!-- 配置用例 -->
        <case-config-modal v-if="caseConfigInfo.status" :modalInfo="caseConfigInfo" />

        <!-- 实例列表 -->
        <sec-instance-list-modal v-if="instanceInfo.status" :modalInfo="instanceInfo" @update="secInstance" />

        <!-- 用例备注 -->
        <case-remark-modal v-if="remarkInfo.status" :modalInfo="remarkInfo" @update="queryInstance" />

        <!-- 配置报表 -->
        <report-config-Modal v-if="reportConfigInfo.status" :loopList="loopList" :loopType="loopType" :modalInfo="reportConfigInfo" @update="updateTable" />
    </div>
</template>

<script>
import * as echarts from 'echarts';
import _ from 'lodash';
import { mapState } from 'vuex';
import { querySceneInfo, queryInstanceDetail, getInstanceList, getReportDistribution } from '@/api/httpApi';
import secInstanceListModal from '@/components/analyse/secInstanceListModal';
import caseConfigModal from '@/components/analyse/caseConfigModal.vue';
import updateCaseNameModal from '@/components/analyse/updateCaseNameModal.vue';
import caseRemarkModal from '@/components/analyse/caseRemarkModal.vue';
import reportConfigModal from '@/components/analyse/reportConfigModal.vue';
import aLoading from '@/components/common/loading/aLoading';
import aTitle from '@/components/common/title/aTitle';
import aButton from '@/components/common/button/aButton';
import aTable from '@/components/common/table/aTable';
import noData from '@/components/common/noData/noData';
import { loopList } from '@/config/exchangeConfig';
export default {
    data() {
        const that = this;
        return {
            sceneId: '',  // 场景id
            loopType: '', // 委托回路类型
            loading: false,
            columns: [
                {
                    width: 50,
                    align: 'center',
                    type: 'radio'
                },
                {
                    minWidth: 100,
                    title: '跨度/指标',
                    key: 'spanName',
                    render: (h, params) => {
                        return h('span', {}, this.spanLatencyDictDesc[params.row.spanName] || '');
                    }
                },
                {
                    minWidth: 100,
                    title: '最小（min）',
                    key: 'min',
                    render: (h, params) => {
                        return h('span', {}, this.filterData(params));
                    }
                },
                {
                    minWidth: 105,
                    title: '中位数（p50）',
                    key: 'p50',
                    render: (h, params) => {
                        return h('span', {}, this.filterData(params));
                    }
                },
                {
                    minWidth: 100,
                    title: '平均（avg）',
                    key: 'avg',
                    render: (h, params) => {
                        return h('span', {}, this.filterData(params));
                    }
                },
                {
                    minWidth: 110,
                    title: '95分位（p95）',
                    key: 'p95',
                    render: (h, params) => {
                        return h('span', {}, this.filterData(params));
                    }
                },
                {
                    minWidth: 110,
                    title: '99分位（p99）',
                    key: 'p99',
                    render: (h, params) => {
                        return h('span', {}, this.filterData(params));
                    }
                },
                {
                    minWidth: 110,
                    title: '最大（max）',
                    key: 'max',
                    render: (h, params) => {
                        return h('span', {}, this.filterData(params));
                    }
                },
                {
                    minWidth: 160,
                    title: '标准差（stdDeviation）',
                    key: 'stdDeviation',
                    render: (h, params) => {
                        return h('span', {}, this.filterData(params));
                    }
                }
            ],
            tableData: [],
            option: {
                title: {
                    text: 'Linear Regression',
                    left: 'center',
                    top: '-5',
                    textStyle: {
                        color: '#ccc'
                    }
                },
                grid: {
                    left: '80',
                    right: '20',
                    bottom: '20',
                    top: '30'
                },
                tooltip: {
                    show: true,
                    enterable: true,
                    // triggerOn: 'click',
                    // eslint-disable-next-line max-params
                    position: function (point, params, dom, rect, size) {
                        // 鼠标坐标和提示框位置的参考坐标系是：以外层div的左上角那一点为原点，x轴向右，y轴向下
                        // 提示框位置
                        let x = 0; // x坐标位置
                        let y = 0; // y坐标位置

                        // 当前鼠标位置
                        const pointX = point[0];
                        const pointY = point[1];

                        // 提示框大小
                        const boxWidth = size.contentSize[0];
                        const boxHeight = size.contentSize[1];

                        // boxWidth > pointX 说明鼠标左边放不下提示框
                        if (boxWidth > pointX) {
                            x = 5;
                        } else {
                            // 左边放的下
                            x = pointX - boxWidth;
                        }

                        // boxHeight > pointY 说明鼠标上边放不下提示框
                        if (boxHeight > pointY) {
                            y = 5;
                        } else {
                            // 上边放得下
                            y = pointY - boxHeight;
                        }

                        return [x, y];
                    },
                    backgroundColor: 'rgba(88,94,106,0.50)',
                    borderColor: 'rgba(88,94,106,0.40)',
                    textStyle: {
                        color: '#fff',
                        fontSize: 12
                    },
                    trigger: 'axis',
                    axisPointer: {
                        type: 'cross'
                    },
                    formatter: function (obj) {
                        const data = that.indicatorsData[obj[0].dataIndex];
                        return (
                            '<div style="border-bottom: 1px solid #baf; font-size: 14px;padding-bottom: 7px;margin-bottom: 7px">' +
                            '指标详情' +
                            '</div>' +
                            'GTID' +
                            '：' + data.gtid +
                            '<br>' +
                            '资金账号' +
                            '：' +
                            data.accountId +
                            '<br>' +
                            '交易所申报编号' +
                            '：' +
                            (data.exchangeOrderId || '-') +
                            '<br>' +
                            '柜台委托号' +
                            '：' +
                            (data.entrustNo || '-') +
                            '<br>' +
                            '时延数据' +
                            '：' +
                            (obj[0].data[1] || '-') +
                            '<br>'
                        );
                    }
                },
                textStyle: {
                    color: '#ccc'
                },
                dataZoom: [
                    {
                        type: 'inside',
                        start: 0,
                        end: 100
                    },
                    {
                        type: 'slider',
                        bottom: 5,
                        height: 20,
                        start: 0,
                        end: 100,
                        show: true
                        // showDataShadow: 'true'
                    }
                ],
                xAxis: {
                    splitLine: {
                        lineStyle: {
                            color: '#31364a'
                        }
                    }
                },
                yAxis: {
                    splitLine: {
                        lineStyle: {
                            color: '#31364a'
                        }
                    }
                },
                series: [{
                    type: 'scatter',
                    symbolSize: 7,
                    data: []
                }]
            },
            instanceInfo: {
                status: false
            },
            selectInstance: {},
            caseConfigInfo: {
                status: false,
                config: {},
                testCaseId: '',
                sceneId: ''
            },
            caseNameInfo: {
                status: false,
                testCaseInstanceId: '',
                name: ''
            },
            remarkInfo: {
                status: false,
                testCaseInstanceId: '',
                info: {}
            },
            reportConfigInfo: {
                status: false,
                columns: [],
                tableData: [],
                unit: '1'
            },
            indicatorsData: [],
            myChart: null,
            isNoChart: true
        };
    },
    async mounted() {
        // 如果有参数Id，则返回该实例
        if (this.$route.query?.id) {
            this.queryInstanceDetail(this.$route.query?.id);
        } else {
            const stu = await this.querySceneInfo();
            stu && this.getInstanceFirst();
        }
        this.initChart();
    },
    methods: {
        // 查询场景信息
        async querySceneInfo(id) {
            return new Promise((resolve, reject) => {
                try {
                    querySceneInfo(id).then(res => {
                        if (res.success) {
                            this.sceneId = res.data.sceneId;
                            resolve(true);
                        } else {
                            resolve(false);
                        }
                    });
                } catch (err) {
                    reject(err);
                    this.$hMessage.error('查询场景信息失败!');
                }
            });
        },
        // 根据回路类型获取回路名
        getLoopName(type) {
            return _.find(loopList, ['value', type]).label;
        },
        // 查询第一个实例默认选中
        getInstanceFirst() {
            const param = {
                page: 1,
                pageSize: 1
            };
            getInstanceList(param).then(res => {
                if (res.success && res.data.list.length) {
                    this.secInstance(res.data.list[0]);
                }
            });
        },
        // 根据id查询实例信息
        async queryInstanceDetail(id) {
            const param = {
                testCaseInstanceId: id // 实例id
            };
            try {
                const res = await queryInstanceDetail(param);
                if (res.success) {
                    this.secInstance(res.data);
                }
            } catch (err) {
                this.$hMessage.error('获取实例信息失败！');
            }

        },
        // 查询实例详情
        queryInstance() {
            this.queryInstanceDetail(this.selectInstance.id);
        },
        secInstance(data) {
            this.instanceInfo.status = false;
            this.selectInstance = data;
            this.initChart();
        },
        // 打开测试参数弹窗
        openCaseConfigModal() {
            if (!this.selectInstance?.apiDemoConfig?.config){
                this.$hMessage.info('未配置相关用例');
                return;
            }
            this.caseConfigInfo.status = true;
            this.caseConfigInfo.config = this.selectInstance?.apiDemoConfig?.config;
            this.caseConfigInfo.testCaseId = this.selectInstance.testCaseId;
            this.caseConfigInfo.sceneId = this.sceneId;
            this.caseConfigInfo.disabled = true;
        },
        // 更新测试实例名称
        updateCaseName(id, name) {
            this.caseNameInfo.status = true;
            this.caseNameInfo.testCaseInstanceId = id;
            this.caseNameInfo.name = name;
        },
        // 更新测试用例备注信息
        updateCaseRemark(id, info) {
            this.remarkInfo.status = true;
            this.remarkInfo.testCaseInstanceId = id;
            this.remarkInfo.info = info;
        },
        // 打开配置报表窗口
        openReportConfig() {
            this.reportConfigInfo.status = true;
            this.reportConfigInfo.columns = this.columns;
            this.reportConfigInfo.tableData = this.tableData;
        },
        // 修改报表列表展示
        updateTable(data) {
            const list = data.columns.map(o => {
                return o.key;
            });
            this.columns = [...this.columns.slice(0, 2), ..._.filter(this.columns, o => { return list.includes(o.key); })];
            this.reportConfigInfo.unit = data.unit;
            this.loopType = data.loopType;
            this.reportConfigInfo.status = false;
            this.tableData = [];
            this.$nextTick(() => {
                this.tableData = data.tableData;
            });
        },
        // 根据单位过滤数据
        filterData(params) {
            let data;
            switch (this.reportConfigInfo.unit) {
                case '0':
                    data = params.row[params.column.key];
                    break;

                case '1':
                    data = params.row[params.column.key] / 1000;
                    break;

                case '2':
                    data = params.row[params.column.key] / 1000000;
                    break;

                default:
                    data = '无';
                    break;
            }
            return data;
        },
        // 选中报表span
        async handleSelected(row) {
            this.loading = true;
            const res = await getReportDistribution({
                testInstanceId: this.selectInstance.id,
                span: row.spanName,
                loopType: this.loopType,
                pageSize: 10000
            });
            this.loading = false;
            if (res.success) {
                const { xaxis, yaxis } = res.data?.trendChart;
                this.initChart(row.spanName, xaxis, yaxis);
            } else {
                this.$hMessage.error(res.message);
            }
        },
        initChart(span, xaxis, yaxis) {
            const list = [];
            Array.isArray(xaxis) && xaxis.forEach((ele, index) => {
                let yData = 0;
                if (this.reportConfigInfo.unit === '1') {
                    yData = (Number(yaxis[index].duration) / 1000).toFixed(3);
                } else if (this.reportConfigInfo.unit === '2') {
                    yData = (Number(yaxis[index].duration) / 1000000).toFixed(3);
                }
                list.push([ele, yData]);
            });
            if (list.length) {
                this.isNoChart = false;
                this.$nextTick(() => {
                    this.myChart = echarts.init(document.getElementById('report-chart'), '#262b40');
                    this.option.series[0].data = list;
                    this.option.yAxis.name = `单位（${this.unitName}）`;
                    this.indicatorsData = yaxis;
                    this.option.title.text = `${this.spanLatencyDictDesc[span] || ''} 指标详情`;
                    this.myChart.setOption(this.option, false);
                    window.addEventListener = () => {
                        this.myChart.resize();
                    };
                });
            } else {
                this.isNoChart = true;
            }
        },
        // 切换回路类型
        handleLoop(type) {
            this.tableData = _.find(this.loopList, o => {
                return o.bizMsgType === type;
            }).ldpDelaySpanIndicatorsList;
        },
        // 获取单位信息
        getUnitName(num) {
            let str;
            switch (num) {
                case '0':
                    str = '纳秒';
                    break;
                case '1':
                    str = '微秒';
                    break;

                case '2':
                    str = '毫秒';
                    break;

                default:
                    str = '纳秒';
                    break;
            }
            return str;
        }
    },
    computed: {
        ...mapState({
            // TODO：参数已被删除，从/ldplt/api/v1/product/latency/trace-models接口，spans参数块，使用spanName转义spanAlias即可
            spanLatencyDictDesc: state => {
                return state?.apmDirDesc?.spanLatencyDictDesc || {};
            }
        }),
        unitName: function() {
            return this.getUnitName(this.reportConfigInfo.unit);
        },
        // 委托回路列表
        loopList: function () {
            const list = this.selectInstance?.report?.reports || [];
            const idx = _.findIndex(list, o => {
                return o.bizMsgType === 'RtnCfmXchg';
            });
            this.loopType = idx > -1 ? 'RtnCfmXchg' : list[0]?.bizMsgType;
            this.tableData = [];
            this.$nextTick(() => {
                this.tableData = list[idx > -1 ? idx : 0]?.ldpDelaySpanIndicatorsList || [];
            });
            return list;
        }
    },
    components: { secInstanceListModal, caseConfigModal, updateCaseNameModal, caseRemarkModal, reportConfigModal, aTitle, aButton, aTable, noData, aLoading }
};
</script>

<style lang="less" scoped>
// 解决echart提示框复制问题
* {
    user-select: text !important;
}

.main {
    display: block;
    overflow-x: hidden;
    overflow-y: auto;

    // 实例详情
    .box-detail {
        margin: 15px 25px;
        color: var(--font-color);

        & > .h-col {
            display: flex;
            align-items: center;

            .text-label {
                width: 80px;
                color: var(--font-opacity-color);
                flex-shrink: 0;
                cursor: pointer;
            }

            @media screen and (max-width: 980px) {
                .text-label {
                    width: 70px;
                }
            }

            .text-label,
            .text-value {
                line-height: 24px;
            }
        }
    }

    // title slot布局
    .slot-box {
        float: right;
        padding-right: 10px;

        & > .sec-loop {
            width: 150px;
        }
    }

    .text-ellipsis {
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
    }

    .report-chart {
        width: 100%;
        height: 350px;
    }

    .report-nodata {
        position: absolute;
        top: 470px;
    }
}
</style>
