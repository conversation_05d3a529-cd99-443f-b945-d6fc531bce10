<template>
    <div>
        <h-msg-box-safe v-model="modalData.status" :mask-closable="false"
            title="同步结果" width="600" maxHeight="350">
            <a-tips type="warning" :tipText="`任务同步成功：${modalData.successCount}，失败：${modalData.failureCount}，失败明细如下：`"></a-tips>
            <a-table
                ref="table"
                maxHeight="300"
                showTitle
                :hasDarkClass="false"
                :tableData='modalData.failedTasks'
                :columns="columns"
                :hasPage="false"
            />
            <template v-slot:footer>
                <a-button @click="modalData.status = false">关闭</a-button>
            </template>
        </h-msg-box-safe>
    </div>
</template>

<script>
import aButton from '@/components/common/button/aButton';
import aTips from '@/components/common/apmTips/aTips';
import aTable from '@/components/common/table/aTable';
export default {
    name: 'ModifyReplayModal',
    props: {
        modalInfo: {
            type: Object,
            default: null
        }
    },
    data() {
        return {
            modalData: this.modalInfo,
            columns: [
                {
                    title: '任务标识',
                    key: 'replayFlag',
                    ellipsis: true,
                    maxWidth: 160
                },
                {
                    title: '失败原因',
                    key: 'errorMessage',
                    ellipsis: true
                }
            ]
        };
    },
    methods: {},
    components: { aButton, aTips, aTable }
};
</script>

<style lang="less" scoped>
/deep/.h-modal-body {
    padding: 16px;
}

.tips-content {
    margin-bottom: 15px;
}
</style>
