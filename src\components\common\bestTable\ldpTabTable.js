import _ from 'lodash';
import aForm from '@/components/common/form/aForm';
import aTable from '@/components/common/table/aTable';
import aTitle from '@/components/common/title/aTitle';
import aButton from '@/components/common/button/aButton';
import aTag from '@/components/common/tag/aTag';
import noData from '@/components/common/noData/noData';
import lineChart from '@/components/common/lineChart/lineChart';
import tagList from '@/components/common/tag/tagList';
import dataExportModal from '@/components/common/bestTable/dataExportModal';
import exportHistoryModal from '@/components/common/bestTable/exportHistoryModal';
import './ldpTabTable.less';
export default {
    name: 'productTimeTabTitleTable',
    props: {
        // 默认选中tab
        tabTableKey: {
            type: String,
            default: ''
        },
        tabTableList: {
            type: Array,
            default: () => []
        },
        hasSetTableColumns: {
            type: Boolean,
            default: false
        },
        hasExportFunc: {
            type: Boolean,
            default: true
        }
    },
    data() {
        return {
            tabName: '',
            formStatus: true,
            visible: false,
            styles: {
                height: 'calc(100% - 55px)',
                paddingBottom: '53px'
            },
            tableHeight: 0,
            dataExportInfo: {
                status: false,
                queryBody: {},
                exportScene: ''
            },
            exportHistoryInfo: {
                status: false,
                exportScene: ''
            }
        };
    },
    watch: {
        tabName(newVal) {
            this.$emit('changeTab', newVal);
            this.$nextTick(() => {
                this.fetTableHeight();
            });
        }
    },
    mounted() {
        this.$_init();
        window.addEventListener('resize', this.fetTableHeight);
    },
    beforeDestroy() {
        window.removeEventListener('resize', this.fetTableHeight);
    },
    methods: {
        resetHeight() {
            return new Promise((resolve, reject) => {
                this.tableHeight = 0;
                resolve();
            });
        },
        // 设置table高度
        fetTableHeight() {
            this.resetHeight().then(res => {
                if (window.innerHeight <= 500){
                    this.formStatus = false;
                }
                const index = _.findIndex(this.tabTableList, ['key', this.tabName]);
                if (index > -1) {
                    const height = this.formStatus
                        ? (Math.ceil(this.tabTableList[index].fixFormItems.length / 3) + Math.ceil(this.tabTableList[index].freeFormItems.length / 3)) * 45
                        : 0;
                    this.tableHeight = this.$refs[`table-box${index}`] && this.$refs[`table-box${index}`].getBoundingClientRect().height - height - 200;
                }
            });
        },
        changeFormStatus(){
            this.formStatus = !this.formStatus;
            if (window.innerHeight <= 500){
                this.formStatus = false;
                this.$hMessage.info('当前窗口高度小于500,请放大窗口查看!');
            }
            this.fetTableHeight();
        },
        $_init() {
            this.tabName = this.tabTableKey;
            this.fetTableHeight();
        },
        setTableColumns() {
            this.visible = true;
        },
        // 获取查询参数
        getQueryParam() {
            const index = _.findIndex(this.tabTableList, ['key', this.tabName]);
            const param1 = this.$refs[`fixForms${index}`].query();
            const param2 = this.$refs[`freeForms${index}`].query();
            if (!param1 || !param2) return;
            const pageParam = this.tabTableList[index].tabTableData[0].hasPage ? this.$refs[`table${index}`]?.getPageData() : {};
            return { ...param1, ...param2, ...pageParam };
        },
        $_handleQuery(val) {
            if (val && Object.keys(val).length){
                this.$emit('query', { ...val.fixForms, ...val.freeForms });
            } else {
                const tab = _.find(this.tabTableList, ['key', this.tabName]);
                tab.queryId = '';
                const params = this.getQueryParam();
                if (!params) return;
                this.$emit('query', params);
            }
            this.fetTableHeight();
        },
        $_handleReset() {
            const index = _.findIndex(this.tabTableList, ['key', this.tabName]);
            this.tabTableList[index].queryId = '';
            this.$refs[`fixForms${index}`].reset();
            this.$refs[`freeForms${index}`].reset();
        },
        // 点击查询
        handleClickQuery() {
            const index = _.findIndex(this.tabTableList, ['key', this.tabName]);
            const param1 = this.$refs[`fixForms${index}`].query();
            const param2 = this.$refs[`freeForms${index}`].query();
            if (!param1 || !param2) return;
            this.$refs[`table${index}`].resetPage();
            this.$_handleQuery();
        },
        // 切换产品-初始化查询
        $_handleResetPageData() {
            this.tabTableList.forEach((ele, index) => {
                this.$refs[`table${index}`].resetPage();
                this.$refs[`table${index}`].resetPageSize();
                this.$refs[`table${index}`].resetSortData();
                this.$refs[`tag-list${index}`] && this.$refs[`tag-list${index}`].scrollReset();
            });
        },
        // 保存
        handleClickSave(){
            const index = _.findIndex(this.tabTableList, ['key', this.tabName]);
            const param1 = this.$refs[`fixForms${index}`].query();
            const param2 = this.$refs[`freeForms${index}`].query();
            if (!param1 || !param2) return;
            this.$emit('save', { fixForms: { ...param1 }, freeForms: { ...param2 } });
        },
        // 删除tag
        handleTagClose(id) {
            this.$emit('tagClose', id);
        },
        // 切换tag重置查询数据
        handleTagClick(id){
            const index = _.findIndex(this.tabTableList, ['key', this.tabName]);
            this.$refs[`table${index}`].resetPage();
            this.$refs[`table${index}`].resetPageSize();
            this.$refs[`table${index}`].resetSortData();
            this.tabTableList[index].queryId = id || '';
            let param = {};
            this.$emit('tagClick', this.tabTableList[index].queryId, val => {
                param = val;
            });
            this.$refs[`fixForms${index}`].echoFormData({ ...param.fixForms });
            this.$refs[`freeForms${index}`].echoFormData({ ...param.freeForms });
            this.$_handleQuery({ ...param });
        },
        // 当前tag下重置分页数据
        handleTagQuery(){
            let param = {};
            const index = _.findIndex(this.tabTableList, ['key', this.tabName]);
            if (this.tabTableList[index].queryId){
                this.$emit('tagClick', this.tabTableList[index].queryId, val => {
                    param = val;
                });
                const pageParam = this.tabTableList[index].tabTableData[0].hasPage ? this.$refs[`table${index}`]?.getPageData() : {};
                this.$_handleQuery({ ...param, ...pageParam });
            } else {
                this.$_handleQuery();
            }
        },
        // 打开数据导出弹窗
        handleDataExportModal() {
            const index = _.findIndex(this.tabTableList, ['key', this.tabName]);
            const param1 = this.$refs[`fixForms${index}`].query();
            const param2 = this.$refs[`freeForms${index}`].query();
            const val = { ...param1, ...param2 };

            this.$emit('generateQueryData', val, (param) => {
                this.dataExportInfo.queryBody = param;
            });
            this.dataExportInfo.status = true;
            this.dataExportInfo.exportScene = `${this.tabTableList[index].name}Export-${this.tabTableList[index].key}`;
        },
        // 打开导出历史弹窗
        handleExportHistoryModal() {
            const index = _.findIndex(this.tabTableList, ['key', this.tabName]);
            this.exportHistoryInfo.status = true;
            this.exportHistoryInfo.exportScene = `${this.tabTableList[index].name}Export-${this.tabTableList[index].key}`;
        },
        // 重置事件滚动
        scrollReset(){
            const index = _.findIndex(this.tabTableList, ['key', this.tabName]);
            this.$refs[`tag-list${index}`] && this.$refs[`tag-list${index}`].scrollReset();
        },
        // 监听下拉框变化
        handleSelectChange(key, val) {
            this.$emit('handleSelectChange', key, val);
        },
        // 获取时延指标查询建议
        queryProposal(span) {
            this.$emit('queryProposal', span);
        },
        // 动态计算form高度
        formHeight(item) {
            return ((Math.ceil(item.fixFormItems.length / 3) + Math.ceil(item.freeFormItems.length / 3)) * 45) + 20 + 'px';
        }
    },
    components: { aForm, aTable, aButton, aTitle, aTag, noData, tagList, lineChart, dataExportModal, exportHistoryModal },
    render() {
        return <main class="best-table" style="min-width: 1000px; height: 100%; overflow-x: auto;">
            <div class="btn-list">
                <a-button
                    type="primary"
                    onClick={this.handleClickQuery}
                    disabled={_.find(this.tabTableList, ['key', this.tabName])?.tableLoading}>查询</a-button>
                <a-button type="dark" onClick={this.$_handleReset}>重置</a-button>
                {this.hasSetTableColumns && <a-button type="dark" onClick={this.setTableColumns}>配置表格</a-button>}
                <a-button type="dark" onClick={this.changeFormStatus}>
                    {this.formStatus ? '收起查询' : '展开查询'}
                    <h-icon name={this.formStatus ? 'packup' : 'unfold'}></h-icon></a-button>
                <a-button type="dark" onClick={this.handleClickSave}>保存</a-button>
                {this.hasExportFunc && <a-button type="dark" onClick={this.handleDataExportModal}>导出数据</a-button>}
                {this.hasExportFunc && <a-button type="dark" onClick={this.handleExportHistoryModal}>导出历史</a-button>}
            </div>
            <h-tabs class="business-tab" v-model={this.tabName} showArrow>
                {
                    this.tabTableList.map((item, idx) => {
                        return <h-tab-pane label={item.label} name={item.key}>
                            <div class="form-box"
                                style={{ height: this.formStatus ? this.formHeight(item) : 0 }}>
                                <a-form
                                    ref={`fixForms${idx}`}
                                    formItems={item.fixFormItems}
                                    proposalList={item.proposalList}
                                    v-on:queryProposal={this.queryProposal}
                                    v-on:handleSelectChange={this.handleSelectChange} />
                                <hr style="margin: 10px 0; border-color: #585e6ae6;" />
                                <a-form
                                    ref={`freeForms${idx}`}
                                    formItems={item.freeFormItems}
                                    proposalList={item.proposalList}
                                    v-on:queryProposal={this.queryProposal}
                                    v-on:handleSelectChange={this.handleSelectChange} />
                            </div>
                            <div>
                                <a-title title="快捷查询">
                                    <slot>
                                        <tag-list
                                            ref={`tag-list${idx}`}
                                            queryList={item.queryList}
                                            selectedId={item.queryId}
                                            v-on:on-close={this.handleTagClose}
                                            v-on:on-click={this.handleTagClick}
                                            style="width: calc(100% - 100px); left: 100px;"
                                        >
                                        </tag-list>
                                    </slot>
                                </a-title>
                            </div>
                            <h-tabs v-model={item.tabName}  animated={false}>
                                {
                                    item.tabTableData.map((ele, index) => {
                                        switch (ele.type) {
                                            case 'table':
                                                return  <h-tab-pane key={ele.name} label={ele.label} name={ele.name}>
                                                    <div ref={`table-box${idx}`} class='table-box'>
                                                        <a-table
                                                            ref={`table${idx}`}
                                                            tableData={ele.tableData}
                                                            columns={ele.columns}
                                                            hasPage={ele.hasPage}
                                                            total={ele.total}
                                                            loading={item.tableLoading}
                                                            v-on:query={this.handleTagQuery}
                                                            height={this.tableHeight}
                                                        />
                                                    </div>
                                                </h-tab-pane>;
                                            default:
                                                return '';
                                        }
                                    })
                                }
                            </h-tabs>
                        </h-tab-pane>;
                    })
                }
            </h-tabs>

            <style jsx>
                {
                    `
                        .form-box {
                            margin-top: 10px;
                            overflow-y: hidden;
                            transition: height 1s;
                        }
                        .bg-none {
                            width: 100%;
                            height: calc(100% - 200px);
                            display: flex;
                            justify-content: center;
                            align-items: center;
                        }
                    `
                }
            </style>
            {/* 导出数据弹窗 */}
            {
                this.dataExportInfo ? <data-export-modal modalInfo={this.dataExportInfo} /> : ''
            }
            {/* 数据导出历史 */}
            <export-history-modal modalInfo={this.exportHistoryInfo} />
        </main>;
    }
};
