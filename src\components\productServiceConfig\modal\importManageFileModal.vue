<template>
    <div>
        <!-- 弹出消息框，用于导入管理功能元信息 -->
        <h-msg-box
            v-model="modalData.status"
            :closable="false"
            :mask-closable="false"
            title="导入管理功能元信息"
            width="600"
            maxHeight="400">
            <!-- 已上传文件列表 -->
            <div v-if="isUpload" class="uploaded-list">
                <div v-if="importStatusIcons" class="upload-status">
                    <h-icon
                        :size="importStatusIcons.size"
                        :name="importStatusIcons.name"
                        :color="importStatusIcons.color" />
                    <div>{{ importStatusIcons.text }}</div>
                </div>
                <div v-show="returnFiles.length" class="file-list">
                    <div v-for="(file, index) in returnFiles" :key="file.fileName" class="file-item">
                        <div>
                            <h-icon
                                name="accessory"
                                color="#9296A1">
                            </h-icon>
                            <span :title="file.fileName" class="file-name">{{ file.fileName }}</span>
                            <span :title="file.uploadDetail" class="descripe">{{ file.uploadDetail }}</span>
                        </div>
                        <h-icon
                            class="delete-icon"
                            :name="getIconProps(file.uploadStatus, 'name')"
                            :color="getIconProps(file.uploadStatus, 'color')"
                            @on-click="removeFile(index)">
                        </h-icon>
                    </div>
                </div>
            </div>
            <!-- 展示待上传文件列表 -->
            <div v-else>
                <h-upload
                    action=""
                    accept=".adoc"
                    multiple
                    :showUploadList="false"
                    :before-upload="handleUpload"
                >
                    <a-button type="dark">选择adoc文件</a-button>
                </h-upload>
                <div v-show="waitFiles.length" class="file-list wait-list">
                    <div v-for="(file, index) in waitFiles" :key="file.uid" class="file-item">
                        <div>
                            <h-icon
                                name="accessory"
                                color="#9296A1">
                            </h-icon>
                            <span :title="file.fileName" class="file-name">{{ file.name }}</span>
                        </div>
                        <h-icon
                            class="delete-icon"
                            name="t-b-delete"
                            color="#F5222D"
                            @on-click="removeFile(index)">
                        </h-icon>
                    </div>
                </div>
                <div v-show="!waitFiles.length" class="no-files-box">
                    暂无文件
                </div>
            </div>

            <template v-slot:footer>
                <div v-if="isUpload">
                    <a-button type="dark" @click="submitComplete">完成</a-button>
                </div>
                <div v-else>
                    <a-button type="dark" @click="modalData.status = false">取消</a-button>
                    <a-button type="primary" :loading="loading" @click="submitConfig">导入</a-button>
                </div>
            </template>
        </h-msg-box>
    </div>
</template>

<script>
import aButton from '@/components/common/button/aButton';
import { manageAdocFileUpload } from '@/api/productApi';
const importStatusIcons = {
    importSuccess: { name: 'success_fill', color: '#52C41A', text: '导入成功', size: 32 },
    importFailure: { name: 'closecircled', color: '#F5222D', text: '导入失败', size: 28 },
    partialSuccess: { name: 'prompt_fill', color: '#FF9901', text: '部分成功', size: 32 }
};
export default {
    props: {
        modalInfo: {
            type: Object,
            default: () => ({})
        },
        productId: {
            type: String,
            default: ''
        }
    },
    data() {
        return {
            modalData: this.modalInfo, // 接收父组件传入的弹框数据
            loading: false,
            waitFiles: [],
            returnFiles: [],
            hasCenterClass: true,
            isUpload: false,
            overallState: '',
            isNoticeVisible: false // 用于跟踪警告窗口的显示状态
        };
    },
    computed: {
        importStatusIcons() {
            return importStatusIcons[this.overallState] || null;
        }
    },
    methods: {
        getIconProps(status, key) {
            const uploadStatus = {
                success: { name: 'success1', color: '#4ECA89' },
                failure: { name: 'error', color: '#F5222D' }
            };
            return uploadStatus?.[status]?.[key];
        },
        // 文件上传处理
        handleUpload(file) {
            const suffix = file.name?.split('.').pop();
            if (suffix !== 'adoc') {
                this.handleFormatError(file);
                return false;
            }
            // 文件大小校验 最大1MB
            if (file.size > 1024 * 1024) {
                this.handleSizeError(file);
                return false;
            }

            // 文件上传数量限制 最大30个
            if (this.waitFiles.length >= 30) {
                this.handleQuantityError();
                return false;
            }

            const existingFileIndex = this.waitFiles.findIndex(existingFile => existingFile.name === file.name);
            if (existingFileIndex !== -1) {
                // 文件已存在，进行覆盖操作
                this.waitFiles.splice(existingFileIndex, 1, file);
            } else {
                this.waitFiles.push(file);
            }
            this.hasCenterClass = false;

            return false; // 阻止自动上传
        },
        // 删除文件
        removeFile(index) {
            this.waitFiles.splice(index, 1);
        },
        // 文件格式错误处理
        handleFormatError(file) {
            this.$hNotice.warning({
                title: '文件格式不正确',
                desc: '文件 ' + file.name + ' 格式不正确，请上传adoc格式的文件。'
            });
        },
        // 文件大小超出限制处理
        handleSizeError(file) {
            this.$hNotice.warning({
                title: '文件大小超出限制',
                desc: `${file.name}大小超出限制，请上传1MB以内大小的文件。`
            });
        },
        handleQuantityError() {
            if (!this.isNoticeVisible) {
                this.isNoticeVisible = true; // 标识窗口已显示
                this.$hNotice.warning({
                    title: '文件个数超出限制',
                    desc: '最多只能上传30个文件。',
                    onClose: () => {
                        this.isNoticeVisible = false; // 警告窗口关闭后重置状态
                    }
                });
            }
        },
        // 验证文件是否已上传
        verifyFileUploaded() {
            if (this.waitFiles.length === 0) {
                this.$hMessage.warning('请选择管理功能adoc文件');
                return false;
            }
            return true;
        },
        // 提交配置
        submitConfig() {
            const fileUploaded = this.verifyFileUploaded();
            if (!fileUploaded) {
                return;
            }
            this.$hMsgBoxSafe.confirm({
                title: '确定要导入管理功能元信息？',
                content: '导入新文件时，原有管理功能元信息将被覆盖，请谨慎操作！',
                onOk: () => {
                    // 文件上传逻辑
                    const formData = new FormData();
                    formData.append('productId', this.productId); // 与 Postman 入参一致
                    this.waitFiles.forEach((file) => {
                        formData.append(`files`, file);
                    });

                    this.uploadFiles(formData);
                }
            });
        },
        async uploadFiles(formData) {
            this.loading = true;
            try {
                const res = await manageAdocFileUpload(formData);
                if (res.code === '200') {
                    this.handleImportSuccess(res.data);
                } else if (res.code.length === 8) {
                    this.$hMessage.error(res.message);
                }
            } catch (err)  {
                console.error(err);
            } finally {
                this.loading = false;
            }
        },
        /**
         * 处理导入返回数据
         */
        handleImportSuccess(data) {
            this.waitFiles = [];
            this.returnFiles = data || [];
            this.isUpload = true;

            const allFilesCount = this.returnFiles?.length;
            const successCount = this.returnFiles.filter(o => o?.uploadStatus === 'success')?.length;
            if (allFilesCount === successCount) {
                this.overallState = 'importSuccess';
            } else if (successCount === 0) {
                this.overallState = 'importFailure';
            } else if (successCount < allFilesCount) {
                this.overallState = 'partialSuccess';
            }
        },
        /**
         * 处理点击完成按钮
         */
        submitComplete() {
            this.modalData.status = false;
            this.$emit('update');
        }
    },
    components: { aButton }
};
</script>

<style scoped lang="less">
@import url("@/assets/css/msgBox.less");

.no-files-box {
    display: flex;
    justify-content: center;
    align-items: center;
    margin-top: 10px;
    background: #262d43;
    color: var(--font-color-2);
    height: 200px;
}

.wait-list {
    min-height: 200px;
    max-height: 300px;
}

.file-list {
    background: #262d43;
    border-radius: 4px;
    padding: 8px;
    margin-top: 10px;
    max-height: 240px;
    overflow: auto;

    .common-text-overflow {
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        vertical-align: middle;
    }

    .file-item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        height: 32px;
        line-height: 32px;
        color: var(--font-color);
        padding: 5px 0;

        & > div {
            display: flex;
            width: calc(100% - 30px);

            .file-name {
                max-width: 400px;
                .common-text-overflow;
            }

            .descripe {
                flex: 1;
                display: inline-block;
                color: #9296a1;
                padding-left: 5px;
                .common-text-overflow;
            }
        }
    }

    .delete-icon:hover {
        cursor: pointer;
    }
}

.upload-status {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    color: var(--font-color);

    .h-icon {
        height: 36px;
        line-height: 36px;
    }
}
</style>
