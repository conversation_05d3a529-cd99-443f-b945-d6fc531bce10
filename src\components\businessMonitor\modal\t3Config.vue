<template>
    <div>
        <h-msg-box
            v-model="modalData.status"
            title="T3监视器"
            :escClose="true"
            :mask-closable="false"
            :width="800"
            :top="0"
            :maxHeight="470"
            @on-open="getInit">
            <div>
                <a-tips tipText="根据需要选择链路指标，生成T3监视器配置。“时间范围”以时延日志打点时间为准。"></a-tips>
                <div style="margin: 10px 0;">
                    <!--业务选择-->
                    <h-select
                        v-model="bizType"
                        class="title-select"
                        :clearable="false"
                        style="width: 150px;"
                        positionFixed
                        setDefSelect>
                        <h-option
                            v-for="item in traceModelConfigs"
                            :key="item.bizType"
                            :value="item.bizType">
                            {{ item.bizTypeAlias }}</h-option>
                    </h-select>
                    <!--TraceType选择-->
                    <h-select
                        v-model="bizTraceType"
                        class="title-select"
                        :clearable="false"
                        style="width: 150px;"
                        positionFixed
                        setDefSelect>
                        <h-option
                            v-for="item in bizTraceTypes"
                            :key="item.bizTraceType"
                            :value="item.bizTraceType"
                            setDefSelect>
                            {{ item.bizTraceTypeAlias }}</h-option>
                    </h-select>
                    <!--度量数据类型选择-->
                    <h-select
                        v-model="linkSpanList"
                        class="title-select"
                        style="width: 175px;"
                        placeholder="请选择span"
                        multClearable
                        positionFixed
                        isCheckall
                        widthAdaption
                        filterable
                        showBottom
                        collapseTags
                        multiple>
                        <h-option
                            v-for="item in spanList"
                            :key="item.value"
                            :value="item.value">
                            {{ item.label }}</h-option>
                    </h-select>
                    <!--度量数据类型选择-->
                    <h-select
                        v-model="statMode"
                        class="title-select"
                        :clearable="false"
                        positionFixed
                        style="width: 100px;">
                        <h-option
                            v-for="item in secondTypeList"
                            :key="item.key"
                            :value="item.key">
                            {{ item.value }}
                        </h-option>
                    </h-select>
                    <!--统计周期选择-->
                    <h-select
                        v-model="interval"
                        class="title-select"
                        :clearable="false"
                        positionFixed
                        style="width: 90px;">
                        <h-option
                            v-for="item in intervalList"
                            :key="item.value"
                            :value="item.value">
                            {{ item.label }}</h-option>
                    </h-select>
                    <h-button
                        type="primary"
                        style="margin-left: 0;"
                        @click="getT3ConfigData">生成配置</h-button>
                </div>
                <div class="t3-config-box">
                    <div class="t3-config-title">T3监视器配置</div>
                    <p v-if="ip" style="margin: 10px 0;">
                        <span>主机地址： {{ ip || '-' }}
                            <h-button
                                v-if="ip"
                                class="btn-cp"
                                type="text"
                                :data-clipboard-text="ip"
                                @click="onCopied">复制</h-button>
                        </span>
                        <span style="padding-left: 100px;">端口： {{ port || '-' }}
                            <h-button
                                v-if="port"
                                class="btn-cp"
                                type="text"
                                :data-clipboard-text="port"
                                @click="onCopied">复制</h-button>
                            </span>
                    </p>
                    <p v-if="ip">
                        T3API： {{ t3Api || '-' }}
                        <h-button
                            v-if="t3Api"
                            class="btn-cp"
                            type="text"
                            :data-clipboard-text="t3Api"
                            @click="onCopied">复制</h-button>
                    </p>
                    <p v-else style="line-height: 40px; text-align: center; color: #8d8f96;">点击“生成配置”获取对应T3监视器配置</p>
                </div>

                <div v-show="t3ConfigJson" class="t3-config-box" style="margin-top: 10px;">
                    <div class="t3-config-title">JSON示例</div>
                    <json-viewer
                        class="json-box"
                        :value="t3ConfigJson"
                        :expand-depth="10"
                        copyable
                        :expanded="true"></json-viewer>
                </div>
            </div>
            <template v-slot:footer>
                <h-button @click="modalData.status = false">关闭</h-button>
            </template>
        </h-msg-box>
    </div>
</template>

<script>
import _ from 'lodash';
import Clipboard from 'clipboard';
import { getT3ConfigData } from '@/api/productApi';
import jsonViewer from 'vue-json-viewer';
import aTips from '@/components/common/apmTips/aTips';
export default {
    components: { aTips, jsonViewer },
    props: {
        modalInfo: {
            type: Object,
            default: null
        },
        traceModelConfigs: {
            type: Array,
            default: () => { return []; }
        }
    },
    data() {
        return {
            modalData: this.modalInfo,
            bizType: '',
            bizTraceType: '',
            linkSpanList: [],
            interval: 5000,
            intervalList: [
                {
                    label: '5秒',
                    value: 5
                }, {
                    label: '10秒',
                    value: 10
                }, {
                    label: '30秒',
                    value: 30
                }, {
                    label: '60秒',
                    value: 60
                }
            ],
            statMode: 'p50',
            secondTypeList: [{
                key: 'p50',
                value: '中位数'
            },
            {
                key: 'avg',
                value: '平均值'
            }, {
                key: 'max',
                value: '最大值'
            }, {
                key: 'min',
                value: '最小值'
            }],
            timeValue: 1800,
            // 监控时间区间选择
            timeIntervalList: [{
                label: '最近五分钟',
                value: 300
            }, {
                label: '最近十五分钟',
                value: 900
            }, {
                label: '最近三十分钟',
                value: 1800
            }],
            t3ConfigJson: '',
            ip: '',
            port: '',
            t3Api: ''
        };
    },
    computed: {
        bizTraceTypes() {
            let list = [];
            if (this.bizType) {
                const traceTypes = _.find(this.traceModelConfigs, ['bizType', this.bizType])?.bizTraceTypes || [];
                list = _.filter(traceTypes, ['enable', true]);
            }
            return list;
        },
        spanList() {
            const list = [];
            if (this.bizType && this.bizTraceType) {
                const bizTraceData = _.find(this.traceModelConfigs, ['bizType', this.bizType]);
                const bizTraceTypes = bizTraceData?.bizTraceTypes || [];
                const spanDicts = bizTraceData?.spans || [];
                const spans = _.find(bizTraceTypes, ['bizTraceType', this.bizTraceType])?.spans || [];
                spans.forEach(ele => {
                    list.push({
                        label: ele + `（${_.find(spanDicts, ['spanName', ele])?.spanAlias || '-'}）`,
                        value: ele
                    });
                });
            }
            return list;
        }
    },
    methods: {
        getInit() {
            this.bizType = this.modalData.bizType;
            this.bizTraceType = this.modalData.bizTraceType;
            this.interval = this.modalData.interval;
            this.statMode = this.modalData.statMode;
        },
        async getT3ConfigData() {
            if (!this.bizType) {
                this.$hMessage.warning('请选择业务类型!');
                return;
            }
            if (!this.bizTraceType) {
                this.$hMessage.warning('请选择链路类型!');
                return;
            }
            if (!this.linkSpanList.length) {
                this.$hMessage.warning('请选择SPAN链路!');
                return;
            }
            const param = {
                productId: this.modalData.productId,
                bizType: this.bizType,
                bizTraceType: this.bizTraceType,
                statMode: this.statMode,
                interval: this.interval,
                linkSpanList: this.linkSpanList.join(',')
            };
            try {
                const { data } = await getT3ConfigData(param); // 获取数据
                this.ip = data.ip; // 更新组件状态
                this.port = data.port;
                this.t3Api = data.t3Api;
                this.t3ConfigJson = data.body;
            } catch (error) {
                console.error(error); // 错误处理
            }
        },
        // 复制地址
        onCopied() {
            const clipBoard = new Clipboard('.btn-cp');
            clipBoard.on('success', (e) => {
                clipBoard.destroy();
                this.$hMessage.success('复制成功');
            });
            clipBoard.on('error', (e) => {
                this.$hMessage.error('该浏览器不支持复制');
                clipBoard.destroy();
            });
        }
    }
};
</script>

<style scoped lang="less">
/deep/ .h-modal-body {
    padding: 16px;
}

.t3-config-box {
    padding: 10px;
    background: #f3f3f3;
    border-radius: 4px;

    .btn-cp {
        color: #298dff;

        &:hover {
            text-decoration: underline;
        }

        &:active {
            color: #1d6abf;
        }
    }

    .t3-config-title {
        padding: 0 0 10px 10px;
        font-size: 14px;
        font-weight: 600;

        &::before {
            display: inline-block;
            position: relative;
            left: -10px;
            top: 3px;
            content: "";
            width: 5px;
            height: 17px;
            background: var(--link-color);
        }
    }
}
</style>
