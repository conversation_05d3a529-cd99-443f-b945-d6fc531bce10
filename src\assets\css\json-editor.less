/* stylelint-disable selector-class-pattern */
.json-record {
    .area-action {
        padding: 20px 0;
        text-align: left;
    }

    /deep/ .jsoneditor-vue {
        height: 100%;

        .jsoneditor {
            border: none;
        }
    }

    /deep/ .jsoneditor-outer .ace-jsoneditor {
        font-size: 14px !important;
        min-height: 50px;
    }

    /deep/ .ace_tooltip {
        visibility: hidden;
    }

    // .jsoneditor textarea {
    //     height: 21px !important;
    // }

    // /deep/ .ace_layer .ace_line_group,
    // /deep/ .ace_gutter .ace_gutter-active-line,
    // /deep/ .ace_layer .ace_gutter-cell,
    // /deep/ .ace_layer .ace_active-line {
    //     height: 21px !important;
    // }

    /deep/ .ace_line {
        .ace_variable {
            color: var(--font-opacity-color) !important;
        }

        .ace_string {
            color: #42b983 !important;
        }

        .ace_constant,
        .ace_numeric {
            color: #fc1e70 !important;
        }

        .ace_paren,
        .ace_lparen {
            color: var(--font-opacity-color) !important;
        }

        .ace_indent-guide {
            background: none;
            border-right: 1px solid var(--base-color);
        }
    }

    /deep/ .ace-jsoneditor .ace_marker-layer .ace_selection {
        background-color: var(--base-color);
    }

    /deep/ .ace_layer .ace_cursor {
        border-left: 2px solid var(--font-color) !important;
    }

    /deep/ .ace_marker-layer .ace_bracket {
        border: 1px solid #6e6e6e;
    }

    /deep/ .jsoneditor-outer {
        margin: 0;
        padding: 0;
    }

    /deep/ .jsoneditor-poweredBy {
        display: none;
    }

    /deep/ .jsoneditor {
        // border-radius: 5px;
    }

    /deep/ .jsoneditor-menu {
        display: none;
    }

    /deep/ .ace_gutter {
        background: #2d334c !important;
        color: var(--font-color-2) !important;
    }

    /deep/ .ace_scroller {
        background: #2d334c !important;
    }

    /deep/ .ace-jsoneditor .ace_marker-layer .ace_active-line {
        background: #1f3759;
    }

    /deep/ .ace-jsoneditor .ace_gutter-active-line {
        background: #1f3759;
    }

    /deep/ div.jsoneditor-menu > button {
        display: none;
    }

    /deep/ .jsoneditor-modes {
        display: none;
    }
}
