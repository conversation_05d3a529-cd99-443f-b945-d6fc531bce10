
/deep/ .h-menu-dark {
    background: var(--wrapper-color);
}

/deep/ .h-menu-dark.h-menu-vertical .h-menu-opened .h-menu-submenu-title {
    background: var(--wrapper-color);
}

/deep/ .h-menu-submenu-title {
    padding: 10px;
    background: var(--wrapper-color);
}

/deep/ .h-menu-submenu-title-icon {
    position: absolute;
    right: 15px;
    top: 9px;
}

/deep/ .h-menu {
    width: 100% !important;
    height: calc(100% - 85px);
    padding: 0;
    overflow: auto;
}


/deep/ .h-menu-item {
    height: 33px;
    color: var(--font-color);
    font-size: var(--font-size-base);
    line-height: 33px;
    padding: 0 5px 0 29px;
    white-space: nowrap;
    text-overflow: ellipsis;
    cursor: pointer;
}

/deep/.h-menu-dark.h-menu-vertical .h-menu-submenu .h-menu-item:hover {
    background: var(--link-opacity-color) !important;

    /deep/ &::after {
        position: absolute;
        top: 0;
        left: 0;
        content: "";
        width: 4px;
        height: 33px;
        background: var(--link-color);
    }
}

/deep/.h-menu-dark.h-menu-vertical .h-menu-item-active:not(.h-menu-submenu),
.h-menu-dark.h-menu-vertical .h-menu-submenu-title-active:not(.h-menu-submenu),
/deep/.h-menu-dark.h-menu-vertical .h-menu-submenu .h-menu-item-active,
.h-menu-dark.h-menu-vertical .h-menu-submenu .h-menu-item-active:hover {
    color: var(--font-color);
    background: var(--link-opacity-color) !important;
    border-right: none;

    /deep/ &::after {
        position: absolute;
        top: 0;
        left: 0;
        content: "";
        width: 4px;
        height: 33px;
        background: var(--link-color);
    }
}

/deep/.h-menu-dark.h-menu-vertical .h-menu-opened .h-menu,
.h-menu-dark.h-menu-vertical .h-menu-opened .h-menu-submenu .h-menu-submenu-title {
    background: var(--wrapper-color);
}
