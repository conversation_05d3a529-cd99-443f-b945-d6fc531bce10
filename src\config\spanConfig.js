export const spanNameDesc = {
    // 总时延
    'Rsp.c1-c2': '客户端总时延',
    'RtnCfmMbr.c1-c2': '客户端总时延',
    'RtnCfmXchg.c1-c2': '客户端总时延',
    'RtnTrd.c1-c2': '客户端总时延',
    // api
    'Req.api1-api2': '交易api上行时延',
    'Rsp.api2-api3': '交易api下游处理时延',
    'RtnCfmMbr.api2-api3': '交易api下游处理时延',
    'RtnCfmXchg.api2-api3': '交易api下游处理时延',
    'RtnTrd.api2-api3': '交易api下游处理时延',
    'Rsp.api1-api4': '交易api总时延',
    'RtnCfmMbr.api1-api4': '交易api总时延',
    'RtnCfmXchg.api1-api4': '交易api总时延',
    'RtnTrd.api1-api4': '交易api总时延',
    'Rsp.api3-api4': '交易api下行时延',
    'RtnCfmMbr.api3-api4': '交易api下行时延',
    'RtnTrd.api3-api4': '交易api下行时延',
    'RtnCfmXchg.api3-api4': '交易api下行时延',
    // 收益互换
    'Req.sw1-sw2': '收益互换核心上行时延',
    'Rsp.sw3-sw4': '收益互换核心下行时延',
    'RtnCfmMbr.sw3-sw4': '收益互换核心下行时延',
    'RtnCfmXchg.sw3-sw4': '收益互换核心下行时延',
    'RtnTrd.sw3-sw4': '收益互换核心下行时延',
    'RtnTrd.sw1-sw4': '收益互换核心总时延',
    'Rsp.sw1-sw4': '收益互换核心总时延',
    'RtnCfmMbr.sw1-sw4': '收益互换核心总时延',
    'RtnCfmXchg.sw1-sw4': '收益互换核心总时延',
    'Rsp.sw2-sw3': '收益互换核心总时延',
    'RtnCfmMbr.sw2-sw3': '收益互换核心总时延',
    'RtnCfmXchg.sw2-sw3': '收益互换核心总时延',
    'RtnTrd.sw2-sw3': '收益互换核心总时延',
    // 交易前置
    'Req.f1-f2': '交易前置机上行时延',
    'Req.f5-f6': '交易前置机转发交易核心上行时延',
    'Rsp.f2-f3': '交易前置机下游处理时延',
    'RtnCfmMbr.f2-f3': '交易前置机下游处理时延',
    'RtnCfmXchg.f2-f3': '交易前置机下游处理时延',
    'RtnTrd.f2-f3': '交易前置机下游处理时延',
    'Rsp.f1-f4': '交易前置机总时延',
    'RtnCfmMbr.f1-f4': '交易前置机总时延',
    'RtnCfmXchg.f1-f4': '交易前置机总时延',
    'RtnTrd.f1-f4': '交易前置机总时延',
    'Rsp.f3-f4': '交易前置机下行时延',
    'RtnCfmMbr.f3-f4': '交易前置机下行时延',
    'RtnCfmXchg.f3-f4': '交易前置机下行时延',
    'RtnTrd.f3-f4': '交易前置机下行时延',
    // 风控前置
    'Req.af1-af2': '风控前置机上行时延',
    'Req.af5-af6': '风控前置机转发交易网关上行时延',
    'Rsp.af2-af3': '风控前置机下游处理时延',
    'RtnCfmMbr.af2-af3': '风控前置机下游处理时延',
    'RtnCfmXchg.af2-af3': '风控前置机下游处理时延',
    'RtnTrd.af2-af3': '风控前置机下游处理时延',
    'Rsp.af1-af4': '风控前置机总时延',
    'RtnCfmMbr.af1-af4': '风控前置机总时延',
    'RtnCfmXchg.af1-af4': '风控前置机总时延',
    'RtnTrd.af1-af4': '风控前置机总时延',
    'Rsp.af3-af4': '风控前置机下行时延',
    'RtnCfmMbr.af3-af4': '风控前置机下行时延',
    'RtnCfmXchg.af3-af4': '风控前置机下行时延',
    'RtnTrd.af3-af4': '风控前置机下行时延',
    // 风控核心
    'Req.a1-a2': '风控核心上行时延',
    'Rsp.a2-a3': '风控核心下游处理时延',
    'RtnCfmMbr.a2-a3': '风控核心下游处理时延',
    'RtnCfmXchg.a2-a3': '风控核心下游处理时延',
    'RtnTrd.a2-a3': '风控核心下游处理时延',
    'Rsp.a1-a4': '风控核心总时延',
    'RtnCfmMbr.a1-a4': '风控核心总时延',
    'RtnCfmXchg.a1-a4': '风控核心总时延',
    'RtnTrd.a1-a4': '风控核心总时延',
    'Rsp.a3-a4': '风控核心下行时延',
    'RtnCfmMbr.a3-a4': '风控核心下行时延',
    'RtnTrd.a3-a4': '风控核心下行时延',
    'RtnCfmXchg.a3-a4': '风控核心下行时延',
    // 交易核心
    'Req.k1-k2': '交易核心上行时延',
    'Rsp.k1-k4': '交易核心总时延',
    'RtnCfmMbr.k1-k4': '交易核心总时延',
    'RtnCfmXchg.k1-k4': '交易核心总时延',
    'RtnTrd.k1-k4': '交易核心总时延',
    'Rsp.k3-k4': '交易核心下行时延',
    'RtnCfmMbr.k3-k4': '交易核心下行时延',
    'RtnCfmXchg.k3-k4': '交易核心下行时延',
    'RtnTrd.k3-k4': '交易核心下行时延',
    'Rsp.k2-k3': '交易核心下游处理时延',
    'RtnTrd.k2-k3': '交易核心下游处理时延',
    'RtnCfmMbr.k2-k3': '交易核心下游处理时延',
    'RtnCfmXchg.k2-k3': '交易核心下游处理时延',
    // 竞价核心
    'Req.k3-k4': '竞价核心接收额度管理核心上行时延',
    'Rsp.k5-k6': '竞价核心下行时延',
    'RtnCfmXchg.k5-k6': '竞价核心下行时延',
    'RtnTrd.k5-k6': '竞价核心下行时延',
    'Rsp.k1-k6': '竞价核心总时延',
    'RtnCfmXchg.k1-k6': '竞价核心总时延',
    'RtnTrd.k1-k6': '竞价核心总时延',
    'Rsp.k4-k5': '竞价核心下游时延',
    'RtnCfmXchg.k4-k5': '竞价核心下游时延',
    'RtnTrd.k4-k5': '竞价核心下游时延',
    // 交易报盘
    'Req.o1-o2': '交易报盘上行时延',
    'Rsp.o1-o4': '交易报盘总时延',
    'RtnCfmXchg.o1-o4': '交易报盘总时延',
    'RtnTrd.o1-o4': '交易报盘总时延',
    'Rsp.o2-o3': '交易报盘下游处理时延',
    'RtnCfmXchg.o2-o3': '交易报盘下游处理时延',
    'RtnTrd.o2-o3': '交易报盘下游处理时延',
    'Rsp.o3-o4': '交易报盘下行时延',
    'RtnCfmXchg.o3-o4': '交易报盘下行时延',
    'RtnTrd.o3-o4': '交易报盘下行时延',
    // 报盘总线
    'Req.ob1-ob2': '报盘总线上行时延',
    'Rsp.ob1-ob4': '报盘总线总时延',
    'RtnCfmXchg.ob1-ob4': '报盘总线总时延',
    'RtnTrd.ob1-ob4': '报盘总线总时延',
    'Rsp.ob2-ob3': '报盘总线下游处理时延',
    'RtnCfmXchg.ob2-ob3': '报盘总线下游处理时延',
    'RtnTrd.ob2-ob3': '报盘总线下游处理时延',
    'Rsp.ob3-ob4': '报盘总线下行时延',
    'RtnCfmXchg.ob3-ob4': '报盘总线下行时延',
    'RtnTrd.ob3-ob4': '报盘总线下行时延',
    // 额度管理核心
    'Req.q1-q2': '额度管理核心上行时延',
    // 委托查询跨度
    'RtnQuery.f1-f2': '交易前置机上行时延',
    'RtnQuery.f2-f3': '交易前置机下游处理时延',
    'RtnQuery.f1-f4': '交易前置机总时延',
    'RtnQuery.f3-f4': '交易前置机下行时延',
    'RtnQuery.k1-k2': '竞价核心处理总时延'
};
