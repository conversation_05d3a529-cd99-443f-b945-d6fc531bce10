export default {
  "common": {},
  "pages": {
    "accordMonitor": {
      "pleaseselect": "请选择",
      "config": "配置",
      "timesTotalcount": "总上场次数：{{ totalCount }}",
      "data": "主备核心数据同步差量告警：",
      "item": "条",
      "refresh": "数据自动刷新频率：",
      "second": "秒"
    },
    "accordObservation": {
      "data": "核心数据同步",
      "text": "总览",
      "text1": "未知"
    },
    "analyseConfig": {
      "title": ">\r\n        <!-- 头部标题 -->\r\n        <a-title :title=",
      "infoImport": "场景信息为空,请新建或导入场景信息",
      "text": ",            // 场景ID\r\n            sceneName:",
      "text1": ",          // 场景Name\r\n            apiDemoInfo:",
      "export": "导出场景数据成功!",
      "export1": "导出场景失败!",
      "textScenename": "场景：${sceneName}",
      "export2": "导出场景",
      "import": "导入场景",
      "text2": "新建场景"
    },
    "analyseData": {
      "instance": ">测试实例\r\n                    <h-icon v-if=",
      "text": ">来源用例</div>\r\n                <div class=",
      "text1": ">归属场景</div>\r\n                <div class=",
      "timeStart": ">开始时间</div>\r\n                <div class=",
      "timeEnd": ">结束时间</div>\r\n                <div class=",
      "productNode": ">产品节点</div>\r\n                <div class=",
      "status": ">测试状态</div>\r\n                <div class=",
      "status1": ">报表状态</div>\r\n                <div class=",
      "param": ">测试参数</div>\r\n                <div class=",
      "remark": ">备注\r\n                    <h-icon v-if=",
      "text2": "/>\r\n        <!-- 报表视图 -->\r\n        <div v-if=",
      "none": "暂无图表",
      "text3": ",  // 场景id\r\n            loopType:",
      "textText": "跨度/指标",
      "textMin": "最小（min）",
      "textP50": "中位数（p50）",
      "textAvg": "平均（avg）",
      "textP95": "95分位（p95）",
      "textP99": "99分位（p99）",
      "textMax": "最大（max）",
      "textStddeviation": "标准差（stdDeviation）",
      "detail": "指标详情",
      "text4": "资金账号",
      "number": "交易所申报编号",
      "text5": "柜台委托号",
      "data": "时延数据",
      "none1": "无",
      "second": "纳秒",
      "second1": "微秒",
      "second2": "毫秒",
      "detailTextUnitname": "报表详情（单位：${unitName}）",
      "textThisUnitname": "单位（${this.unitName}）",
      "detail1": "${this.spanLatencyDictDesc[span] || ''} 指标详情",
      "select": "选择实例",
      "instance1": "测试实例",
      "text6": "来源用例",
      "text7": "归属场景",
      "starttime": "开始时间",
      "endtime": "结束时间",
      "product": "产品节点",
      "status2": "测试状态",
      "status3": "报表状态",
      "param1": "测试参数",
      "remark1": "备注",
      "config": "配置报表"
    },
    "apmMonitorConfig": {
      "observationSelect": "当前节点不支持观测！请重新选择节点",
      "node": "已托管应用节点"
    },
    "appRunningState": {
      "status": "应用状态墙",
      "config": ">基础配置</p>\r\n            <div style=",
      "config1": ">告警规则配置</p>\r\n\r\n            <div class=",
      "index": ">序号</h-col>\r\n                <h-col span=",
      "timeStart": ">开始时间</h-col>\r\n                <h-col span=",
      "timeEnd": ">结束时间</h-col>\r\n                <h-col span=",
      "save": "保存成功",
      "save1": "保存失败",
      "save2": "您确定保存当前所有配置吗？",
      "service": "服务视图",
      "text": "部署视图",
      "config2": "基础配置",
      "node": "显示仲裁节点",
      "config3": "告警规则配置",
      "index1": "序号",
      "status1": "节点状态",
      "add": "新增规则",
      "save3": "保存配置",
      "text1": "关闭"
    },
    "brokerDataLimit": {
      "config": "降级熔断配置",
      "list": "发布名单列表",
      "config1": "黑名单配置",
      "config2": "白名单配置",
      "config3": "限流名单配置",
      "config4": "组配置",
      "text": "发布中",
      "text1": "发布",
      "product": ", // 选中的产品\r\n            tabName:",
      "list1": "确定要发布列表中已启用的黑名单、白名单、限流名单吗？",
      "cancel": "取消",
      "publis": "{{publishLoading ? '发布中' : '发布'}}"
    },
    "displaySettingDrawer": {
      "functionSpanVIf": ">\r\n                        已展示功能号：<span v-if=",
      "query": "输入功能号查询",
      "name": "功能号名称",
      "function": "功能号",
      "text": "是否展示",
      "textSelect": "为保证查看体验，建议选择不超过30个。",
      "query1": "查询功能号异常",
      "success": "设置成功",
      "failed": "设置失败",
      "failed1": "设置是否展示失败",
      "count": "${this.enableTotalCount}个",
      "function1": "已展示功能号："
    },
    "index": {
      "core": "核心功能号处理",
      "select": "请选择分片",
      "select1": "请选择集群",
      "text": "分片",
      "cluster": "集群",
      "rowTps": "执行吞吐(tps)",
      "rowNs": "平均执行耗时(ns)",
      "errorTimes": "错误次数(次)",
      "error": "错误率(%)",
      "column": "队列积压",
      "query": "查询分类失败",
      "query1": "查询分片信息异常",
      "query2": "查询集群信息",
      "textLoading": "分片、集群加载中",
      "setting": "功能号显示设置",
      "edit": "配置应急修改",
      "name": "输入节点名称",
      "failed": "初始化配置应急失败",
      "text1": "盘前",
      "text2": "盘中",
      "text3": "盘后",
      "text4": "，在",
      "text5": "和",
      "text6": "之间跳转的时候，\r\n    // 由于会渲染同样的",
      "export": "MDB数据导出",
      "export1": "导出历史",
      "export2": "导出MDB数据",
      "text7": ">\r\n        <!-- 头部 -->\r\n        <div class=",
      "item": "按SQL条件",
      "text8": "按资金账户",
      "record": "上场记录"
    },
    "createRule": {
      "monitor": "获取监控指标",
      "config": "配置规则内容",
      "row": "预执行内容",
      "descriptionMd": "SQL语法说明.md",
      "core": "核心",
      "select": "请选择核心",
      "pleaseinput": "请输入指标名",
      "row1": "执行SQL",
      "text": "测试结果",
      "none": "暂无结果",
      "name": "规则名称",
      "pleaseinputText": "请输入规则名（不超过20字符）",
      "description": "规则说明",
      "descriptionText": "请输入规则说明（不超过200字符）",
      "textA": ">上一步</a-button>\r\n            <span v-if=",
      "createMonitor": ">创建监控规则</a-button>\r\n            <a-button type=",
      "text1": "指标1",
      "text2": "指标2",
      "empty": "输入不能为空",
      "text3": "指标名重复",
      "success": "获取变量成功",
      "failedText": "变量获取失败，请重试！",
      "failed": "获取变量失败,",
      "failedText1": "测试失败，请重试",
      "failed1": "测试sql失败",
      "create": "创建成功",
      "create1": "规则创建失败",
      "query": "查询核心失败",
      "count": "最大输入长度500个字符",
      "item": "只允许写一条select语句",
      "text4": "留下",
      "saveData": "离开后当前操作将不会保存，数据会丢失，请谨慎操作！",
      "createText": "创建监控规则-自定义SQL",
      "row2": "预执行SQL",
      "row3": "“预执行SQL”执行后可得到对应变量，变量可在下方“执行SQL”被引用。点击",
      "download": "下载SQL语法指南",
      "text5": "获取变量",
      "textText": "点击“获取变量”，生成对应可引用变量，在下方显示",
      "text6": "可引用变量",
      "row4": "正式执行内容",
      "text7": "SQL语法指南",
      "rowColumn": "统一策略：取结果集的第一行第一列，结果集必须是数字",
      "text8": "SQL结果",
      "text9": "测试",
      "dataSetting": "针对上一步获取的比对数据所形成的告警指标，设置对应的告警阈值。",
      "text10": "上一步",
      "textConfig": "下一步：配置规则内容",
      "create2": "创建监控规则"
    },
    "dataSecondAppearance": {
      "dataTimes": "pageName === 'detail' ? '数据二次上场' : ''",
      "data": "数据二次上场",
      "product": ", // 选中的产品\r\n            pageName:",
      "select": ", // tab默认选择\r\n            titleText:",
      "create": "创建二次上场任务"
    },
    "addModal": {
      "add": "添加路由",
      "add1": "添加路由失败",
      "text": "确定"
    },
    "helpModal": {
      "config": "配置说明"
    },
    "routeConfig": {
      "config": "路由配置",
      "versionVersion": ">\r\n                当前版本：{{ version ||",
      "function": "输入功能号",
      "edit": "修改配置",
      "config1": "配置json预览",
      "system": "目标系统号",
      "node": "节点",
      "text": "目标端ID",
      "text1": "操作",
      "delete": "删除",
      "update": "更新失败",
      "update1": "更新成功",
      "failed": "获取路由配置失败",
      "update2": "确定要更新配置吗？",
      "updateText": "参数配置更新后实时生效，重启后失效。",
      "delete1": "确定要删除该路由？",
      "addEditDeleteInfoConfigNone": "路由配置无version信息，不可添加、删除、修改配置。",
      "versionVersion1": "当前版本：{{ version || \"-\" }}",
      "update3": "更新配置"
    },
    "routeInfoForm": {
      "edit": "请选择要修改的路由",
      "pleaseinput": "请输入",
      "failed": "获取目标端id失败",
      "countFunction": "支持*、?、数字(不为负值)。多个功能号使用英文分号分隔",
      "countFunction1": "支持*、数字(不为负值、最大255)。多个功能号使用英文分号分隔",
      "textTextTextText": "支持*、数字(不为负值、最大65535)"
    },
    "latencyTrendAnalysis": {
      "analysis": "链路时延趋势分析",
      "select": "选择日期",
      "type": "业务类型",
      "status": "链路状态",
      "select1": "选择跨度",
      "nodata": "暂无数据",
      "application": "应用分段时延趋势",
      "statistics": "应用分段时延统计",
      "textP90": "90分位（p90）",
      "text": "查看时延分布",
      "application1": "应用分段时延分布",
      "statistics1": "应用逐笔时延统计",
      "query": "请选择查询分页",
      "text1": "中位数",
      "text2": "平均值",
      "text3": "最大值",
      "text4": "最小值",
      "text5": "90分位",
      "text6": "95分位",
      "text7": "99分位",
      "name": ";// x轴的名称\r\n\r\n                htmlStr +=",
      "text8": "委托笔数:",
      "text9": "时延:",
      "recordTime": "时间范围：${sTime} - ${eTime} 总记录笔数：${count || 0}笔",
      "configRowProductService": "当前产品尚未配置模型，请前往“产品服务配置”进行模型配置",
      "config": "前往配置",
      "name1": "';// x轴的名称\r\n\r\n                htmlStr += '",
      "text10": "查看"
    },
    "ldpDataObservation": {
      "text": "集合竞价",
      "text1": "早盘竞价交易",
      "text2": "盘休",
      "text3": "午盘竞价交易",
      "text4": "盘后定价",
      "observationSelect": "当前应用节点不支持观测！请重新选择节点",
      "dayCurrenttime": "今天是: {{ currentTime }}"
    },
    "ldpLinkConfig": {
      "config": "产品节点配置管理",
      "delete": "删除成功",
      "delete1": "删除失败",
      "text": ") {\r\n                // 如果是",
      "deleteProductNode": "您确定删除名为\"${this.productInfo.productName}\"产品节点吗？",
      "product": "对接产品节点",
      "product1": "已注册产品节点",
      "delete2": "删除产品节点",
      "info": "同步节点信息"
    },
    "ldpLogCenter": {
      "error": "回库错误重试运维",
      "error1": "回库错误重试",
      "error2": "回库错误日志",
      "dateCurrentdate": "日期：{{ currentDate }}",
      "query": "日志查询超时时间："
    },
    "clusterMonitor": {
      "status": "集群状态",
      "status1": "应用状态"
    },
    "ldpAppMonitor": {
      "core": "核心性能"
    },
    "ldpTable": {
      "query": "查询内存表",
      "query1": "数据查询",
      "edit": "数据修改",
      "text": "内存表结构",
      "select": "请从左侧菜单选择内存表进行查看！",
      "select1": "请选择并连接应用节点！"
    },
    "locateConfig": {
      "config": "Locate配置一致性校验",
      "config1": "节点配置管理",
      "config2": "节点配置校验"
    },
    "managementQuery": {
      "selectNoneNode": "instanceList.length ? '请选择节点': '当前暂无活跃节点'",
      "query": "输入节点名查询",
      "search": "搜索管理功能",
      "text": ">显示",
      "select": "请选择管理功能手动发起请求",
      "select1": "请选择管理功能",
      "select2": "请选择节点",
      "none": "当前暂无活跃节点",
      "none1": "暂无",
      "data": "其他数据",
      "text1": ";\r\n// JSON代码高亮需要由JavaScript插件支持\r\nimport",
      "rowFunctionManage": ", // 管理功能执行耗时\r\n            apmTime:",
      "failed": "初始化默认jsonPath配置失败:",
      "querySaveParamTimesFunction": "如需保存当前选中功能号输入参数，请手动触发一次查询请求！",
      "download": "下载",
      "failed1": "获取jsonPath配置失败:",
      "data1": "功能号无返回数据！",
      "export": "导出",
      "export1": "批量导出",
      "export2": "支持批量导出当前产品下的所有管理功能数据",
      "export3": "快捷导出",
      "exportProductCoreFunctionManage": "点击一键导出所选产品管理所有核心的「GetFuncDetailInfo」管理功能",
      "none2": "{{  child.chineseName || '暂无'}}",
      "query1": "未查询到管理功能",
      "textText": "显示\"中文译名\"",
      "text2": "整体耗时：",
      "text3": "APM耗时：",
      "node": "应用节点耗时：",
      "table": "配置表格",
      "text4": "关闭全部"
    },
    "marketAllLink": {
      "row": "行情全链路应用节点关系",
      "monitor": "终端客户全链路质量监控",
      "data": "行情数据时延趋势监控",
      "text": "NSQ链路",
      "text1": "FPGA链路",
      "text2": "FPGA全链路",
      "status": "应用节点状态",
      "text3": "时延正常率",
      "text4": "NSQ全链路"
    },
    "marketMonitor": {
      "info": "当前实例无采集时延走势信息",
      "none": "该模型文件暂无拓扑结构",
      "minute": "最近五分钟",
      "minute1": "最近十五分钟",
      "minute2": "最近三十分钟",
      "rowS": "快照行情(μs)",
      "rowS1": "指数行情(μs)",
      "textS": "逐笔委托(μs)",
      "textS1": "逐笔成交(μs)",
      "monitor": "{{ $getProductType(productInfo.productType) }}时延监控",
      "text": "显示拓扑"
    },
    "marketNodeDelayList": {
      "analysis": "行情产品节点穿透时延分析",
      "query": "汇总查询",
      "text": "交易日",
      "textMin": "最小(min)",
      "textP1": "1分位数(p1)",
      "textP50": "中位数(p50)",
      "textAvg": "平均数(avg)",
      "textP95": "95分位数(p95)",
      "textP99": "99分位数(p99)",
      "textMax": "最大(max)",
      "statistics": "统计总数",
      "date": "交易日期",
      "time": "交易时间",
      "query1": "查询失败!"
    },
    "marketPenetrateList": {
      "text": "下单数",
      "text1": "全链路时延",
      "row": "柜台上行时延",
      "row1": "柜台下行时延",
      "row2": "TGW上行时延",
      "row3": "TGW下行时延",
      "text2": "TGW时延",
      "text3": "交易所时延",
      "rowText": "行情-交易时延",
      "textRow": "交易-收到行情时延",
      "row4": "防火墙上行时延",
      "row5": "防火墙下行时延",
      "text4": "拓扑结构",
      "text5": "交易市场",
      "text6": "深圳交易所",
      "text7": "上海交易所",
      "core": "交易核心",
      "text8": "报盘网关",
      "row6": "行情网关",
      "text9": "资金账户",
      "text10": "证券代码",
      "text11": "席位号",
      "text12": "汇总间隔",
      "day": "天",
      "text13": "汇总方式",
      "query": "详情查询",
      "date": "日期",
      "text14": "业务字段"
    },
    "marketTimeDelay": {
      "select": "选择时间",
      "select1": "选择交易所",
      "select2": "选择数据类型",
      "text": "平均",
      "second": "每秒最大",
      "second1": "每秒最小",
      "analysisS": "本地全系统时延走势分析(μs)",
      "textMs": "交易所到消费端总时延(ms)",
      "query": "查询的数据不存在",
      "confirm": "确认"
    },
    "mcDataObservation": {
      "textCluster": "已托管MC3.0集群"
    },
    "mcDeploy": {
      "mc3Config": "MC3.0配置管理",
      "text": "主题"
    },
    "detailDrawer": {
      "detail": "详情",
      "service": "服务器",
      "text": "表名",
      "text1": "远程路径",
      "export": "导出状态和结果",
      "export1": "导出中",
      "export2": "导出成功",
      "export3": "导出失败",
      "export4": "待导出",
      "failed": "查看历史详情失败",
      "error": "错误信息"
    },
    "exportHistory": {
      "text": "任务ID",
      "export": "导出时间",
      "export1": "导出状态",
      "export2": "导出内存表数量",
      "delete": "删除远程服务器数据",
      "delete1": "删除APM服务器数据",
      "delete2": "已删除",
      "delete3": "删除历史任务失败,",
      "download": "下载成功!",
      "download1": "下载失败",
      "download2": "下载单张表失败,",
      "deleteExportDataService": "删除导出任务的同时，可同时删除该任务导出在服务器的内存表数据。请勾选需要删除的内容。",
      "deleteExportDataService1": "删除导出任务，可同时删除该任务导出在服务器的内存表数据。请勾选需要删除的内容。",
      "text1": "查看原因"
    },
    "exportTable": {
      "exportTimes": ">最近一次导出</span>\r\n              <span class=",
      "export": "最近一次导出",
      "nbspNbspFailed": "&nbsp;&nbsp;失败原因",
      "create": "创建任务"
    },
    "mdbDataObservation": {},
    "mdbPrivilegeManage": {
      "manage": "MDB权限管理",
      "text": "将本地用户权限下发至MDB",
      "edit": ">权限可编辑  &nbsp;&nbsp;&nbsp;&nbsp;<h-switch v-model=",
      "text1": "与",
      "text2": "）。若当前权限内容为",
      "textEditPage": "权限内容。如需编辑，请至当前页面顶部",
      "text3": "(最新下发：",
      "text4": "}}(最新下发：{{hisPermissionsInfo.headerTime ||",
      "failed": "下发失败",
      "success": "下发成功",
      "manage1": "用户管理",
      "manage2": "角色管理",
      "text5": "下发结果",
      "text6": "MDB权限与本地权限一致。",
      "text7": "MDB权限与本地权限不一致。",
      "noneText": "暂时无法获取MDB权限，请稍后查看。",
      "success1": "权限下发成功！",
      "text8": "权限下发异常！",
      "failed1": "权限下发失败！",
      "export": "导出成功!",
      "import": "导入权限成功",
      "import1": "导入权限失败",
      "failedTimes": "用户权限部分下发失败，请稍后重新尝试。本次下发详细如下：",
      "failedTimes1": "用户权限下发失败，请稍后重新尝试。本次下发详细如下：",
      "edit1": "权限可编辑状态修改成功!",
      "text9": "权限操作",
      "text10": "同步权限",
      "text11": "查看MDB权限",
      "import2": "导入权限",
      "export1": "导出权限",
      "edit2": "权限可编辑  &nbsp;&nbsp;&nbsp;&nbsp;"
    },
    "businessMonitor": {
      "text": "展示链路拓扑",
      "text1": "时延趋势",
      "second": "10秒",
      "second1": "30秒",
      "failed": "获取图表监控时延数据失败！",
      "failed1": "获取聚合数据失败！",
      "text2": "T3监视器"
    },
    "networkSendAndRecevied": {
      "application": "应用抓包",
      "text": "抓包回放",
      "application1": "应用发包",
      "text1": "发包用例"
    },
    "noticeManagerList": {
      "manage": "告警人员管理",
      "text": "告警人员",
      "name": "干系人名称",
      "text1": "手机号",
      "text2": "邮箱",
      "text3": "通知干系人",
      "edit": "修改",
      "deleteInfo": "您确定删除名为\"${param.addresseeName}\"的干系人信息？",
      "add": "添加"
    },
    "productDataStorage": {
      "data": "监控数据管理",
      "text": "索引名",
      "text1": "主分片数",
      "text2": "从分片数",
      "item": "文档条数",
      "empty": "空间占用",
      "text3": "委托交易时延",
      "query": "委托查询时延数据",
      "text4": "时延跨度",
      "monitor": "监控指标",
      "clearData": "您确定清空名为\"${params.row.index}\"的索引数据吗？",
      "deleteData": "您确定删除名为\"${params.row.index}\"的索引数据吗？",
      "text5": "已连接ElasticSearch: {{ esUrl }}",
      "item1": "按条件清理",
      "text6": "定时清理"
    },
    "productServiceList": {
      "config": "产品服务配置",
      "listProductService": ">产品服务列表</div>\r\n                    <h-menu v-if=",
      "list": "产品服务列表"
    },
    "productTimeDetail": {
      "node": "交易节点委托时延明细",
      "item": ">上一条</a-button>\r\n        <a-button type=",
      "type": "链路类型",
      "query": "查询日期",
      "query1": "查询时间",
      "text": "时延筛选",
      "query2": "查询结果",
      "time": "委托时间",
      "query3": "获取快捷查询失败!",
      "query4": "保存快捷查询失败!",
      "query5": "删除快捷查询失败!",
      "data": "当前数据为最后一条",
      "deleteNameQuery": "您确定删除 '${name}'查询标签吗？",
      "textDa": "委托${data.entrustNo}全链路拓扑",
      "item1": "上一条",
      "item2": "下一条"
    },
    "productTimeSummary": {
      "query": "链路时延汇总查询",
      "text": "平均数",
      "text1": "周",
      "text2": "月",
      "text3": "总委托数"
    },
    "rcmBacklogMonitor": {
      "monitor": "上下文积压监控",
      "list": "RCM配置列表",
      "text": ", // rcm文档id\r\n            productInstNo:"
    },
    "rcmDeploy": {
      "config": "RCM配置管理",
      "delete": "删除配置",
      "tip": "请查看待发布配置和已发布配置告警提示",
      "deleteInstanceNode": "您确定删除名为\"${this.instanceData.name}\"的节点实例？"
    },
    "rcmObservation": {
      "observation": "上下文运行观测",
      "search": "搜索上下文",
      "product": "请先去建立产品",
      "copy": "复制成功",
      "copy1": "复制"
    },
    "publishStatusDetail": {
      "noneRow": ">当前无上场任务执行</span>\r\n            <span class=",
      "detail": ">详情</h-button>\r\n            <div slot=",
      "info": ">上场信息</div>\r\n                    <div class=",
      "taskdetailTitleText": "${taskDetail.title}上场",
      "row": "{{taskDetail.title || '-'}}执行上场中",
      "none": "当前无上场任务执行",
      "info1": "上场信息",
      "starttime": "开始时间:",
      "endtime": "结束时间:",
      "text": "需上场表数量:",
      "success": "上场成功表数量:",
      "failed": "上场失败表数量:"
    },
    "smsList": {
      "manage": "告警通知管理",
      "param": "告警通知参数",
      "status": "通知状态",
      "text": "通知内容",
      "text1": "告警通知",
      "time": "告警通知时间",
      "text2": "匹配通知模板",
      "text3": "人",
      "text4": "自动通知",
      "text5": "开启",
      "status1": "匹配状态",
      "success": "成功",
      "failed": "失败",
      "text6": "已通知",
      "text7": "未通知",
      "time1": "通知时间",
      "text8": "通知模板",
      "select": "请选择通知模板",
      "text9": "全部",
      "text10": "匹配",
      "text11": "未匹配",
      "select1": "请选择匹配状态",
      "select2": "请选择通知状态",
      "text12": "通知外发历史",
      "createtime": "创建时间",
      "text13": "通知方式",
      "text14": "短信",
      "text15": "电子邮件",
      "text16": "通知地址",
      "text17": "通知提供方",
      "text18": "干系人",
      "select3": "请选择通知方式",
      "pleaseinput": "请输入通知内容",
      "time2": "发送时间",
      "success1": "发送成功",
      "failed1": "发送失败",
      "pleaseinput1": "请输入通知地址",
      "manage1": "管理通知模版"
    },
    "sqlCores": {
      "textS": ">超时:</span>\r\n                        <span slot=",
      "text": "帮助手册",
      "empty": "标签名不得为空",
      "text1": "当前标签名已存在",
      "pleaseinput": "请输入标签名",
      "edit": "编辑器内容超过1M不支持暂存!",
      "select": "请选择要运行的节点！",
      "empty1": "SQL输入不能为空！",
      "selectItem": "请选择要执行的sql语句，不得超过1条！",
      "config": "超时时间不能配置为空或0！",
      "time": "超时时间不得超过10分钟！",
      "dataRow": "该页数据不存在，请重新执行",
      "row": "执行",
      "text2": "历史SQL",
      "text3": "超时:"
    },
    "sqlTable": {
      "text": "输入表名过滤",
      "text1": ">仅显示",
      "text2": "索引",
      "text3": "关联关系",
      "description": "使用说明",
      "type": "索引类型",
      "text4": "索引字段",
      "text5": "关联对象身份",
      "type1": "关联类型",
      "text6": "包含",
      "text7": "关联",
      "text8": "关联字段",
      "text9": "关联索引",
      "selectNoneRowNodeService": "当前服务在此路由策略下无可执行节点，请重新选择",
      "node": "此分片表在以下主节点中存在",
      "noneRowNodeCore": "当前模式下无可执行的核心节点，请调整路由策略重试",
      "text10": "结果",
      "dataNoneRowNode": "仅表示行数据对应的SQL执行的节点，与执行结果无关",
      "text11": "终止",
      "row": "SQL执行异常",
      "row1": "'SQL：${param.sql} 执行异常，您确定要继续执行剩余SQL吗？",
      "textTextText": "仅显示\"不支持主备同步\"表",
      "import": "导入SQL"
    },
    "threadInfoOverview": {
      "config": "应用检查配置"
    },
    "topoMonitor": {
      "product": "当前产品不支持拓扑展示",
      "application": "应用拓扑结构",
      "text": "RCM拓扑结构"
    },
    "transaction": {
      "select": "选择产品",
      "name": ";// x轴的名称\r\n\r\n                    htmlStr +=",
      "query": "查询订单不存在",
      "failed": "获取同比分析数据失败！",
      "analysis": "自由分析",
      "analysis1": "应用时延分析",
      "createSuccess": "报表${res.data.instanceName}创建成功！",
      "query1": "查询",
      "create": "创建分析报表",
      "name1": "';// x轴的名称\r\n\r\n                    htmlStr += '"
    },
    "tripartiteServiceList": {
      "service": "三方服务集成管理",
      "listService": ">三方服务列表</div>\r\n                    <h-menu v-if=",
      "list": "三方服务列表"
    },
    "ustTableVerification": {
      "data": "数据校验",
      "create": "创建数据校验任务"
    },
    "common": {
      "info": "用户信息表",
      "detail": "订单详情表"
    }
  },
  "components": {
    "accordObservation": {
      "core": "主备核心事务差量",
      "text": "回库事务差量",
      "text1": "角色",
      "text2": "事务号",
      "core1": "与主核心事务差量",
      "textCount": "事务量差(个)",
      "datatitleText": "{{ dataTitle }}变化趋势",
      "core2": "备核心事务最大差量",
      "text3": "回库事务最大差量",
      "textI": "分片 ${indicator.shardingNo || '-'}"
    },
    "analyse": {
      "text": "新建测试用例",
      "name": "用例名称",
      "name1": "请输入测试用例名称",
      "cancel": ">取消</a-button>\r\n                <a-button type=",
      "text1": "字符长度数不得超过20！",
      "create": "用例创建成功!",
      "create1": "用例创建失败!",
      "name2": "场景名称",
      "name3": "请输入场景名称",
      "create2": "场景创建成功!",
      "create3": "场景创建失败!",
      "saveExportInfo": "请确保当前场景已导出保存，新建的场景将覆盖当前使用的场景信息！",
      "config": "账号配置",
      "pleaseinput": "请输入起始账号",
      "text2": "账号...",
      "confirm": "请确认输入内容为数字！",
      "update": "用例账号更新成功!",
      "update1": "用例账号更新!",
      "import": "导入",
      "text3": "起始账号",
      "count": "生成个数",
      "text4": "自动生成",
      "text5": "股东账号",
      "text6": "上交所",
      "text7": "深交所",
      "config1": "配置用例",
      "config2": "请输入用例配置...",
      "save": "用例配置保存失败!",
      "update2": "更新实例备注",
      "title": "标题",
      "name4": "请输入标题名称",
      "text8": "内容",
      "update3": "备注更新成功!",
      "update4": "备注更新失败!",
      "list": "事件列表",
      "detail": "事件详情",
      "tip": "提示内容",
      "copy": "复制id",
      "copy1": "该浏览器不支持复制",
      "copySuccess": "{{ copied ? '复制成功' : '复制id' }}",
      "import1": "导入账号信息",
      "upload": "点击或将文件拖拽到这里上传",
      "import2": "导入场景配置文件",
      "saveExportImportInfo": "请确保当前场景已导出保存，新导入的场景将覆盖当前使用的场景信息！",
      "text9": "委托回路",
      "text10": "可视跨度",
      "text11": "可视指标",
      "text12": "度量单位",
      "secondNs": "纳秒（ns）",
      "secondS": "微秒（μs）",
      "secondMs": "毫秒（ms）",
      "text13": "报表对比",
      "select": "选择对比实例",
      "select1": "选择测试实例",
      "search": "搜索测试报告...",
      "name5": "实例名称",
      "date": "测试日期",
      "select2": "请选择实例！",
      "update5": "更新测试实例名称",
      "name6": "请输入测试实例名称",
      "text14": "字符长度数不得超过30！",
      "update6": "实例名称更新成功!",
      "update7": "实例更新失败!"
    },
    "analyseConfig": {
      "remark": ">填写备注</a-button>\r\n            <h-row class=",
      "empty": "空",
      "text": "最大时延",
      "hour": "最小时延",
      "text1": "平均时延",
      "text2": "中位数时延",
      "text3": "99分位时延",
      "query": "查询实例列表失败!",
      "contentDelete": ",\r\n                content: `您确定删除",
      "detail": "您确定要跳转到该实例详情分析页面吗？",
      "instance": "您确定停止当前测试实例？",
      "success": "测试实例停止成功!",
      "text4": "99分位数",
      "instance1": "测试实例名",
      "info": "事件信息",
      "text5": "停止测试",
      "remark1": "填写备注",
      "none": "{{ (tabData.reportStatusDesc) || '暂无' }}",
      "text6": "发单工具",
      "pleaseinput": "请输入发单工具",
      "text7": "部署到",
      "pleaseinput1": "请输入部署地址",
      "instance2": "部署实例数",
      "pleaseinput2": "请输入部署示例数",
      "node": "应用节点",
      "text8": "接入IP",
      "text9": "接入端口",
      "text10": "测试用例",
      "param": "命令行参数",
      "pleaseinput3": "请输入执行命令",
      "pleaseinputText": "请输入1-10正整数",
      "pleaseinput4": "请输入1到65535之间的端口号",
      "type": ",            // 业务类型\r\n                    ip:",
      "query1": "未查询到场景信息!",
      "query2": "查询场景信息异常!",
      "query3": "查询场景信息失败!",
      "success1": "测试执行成功!",
      "row": "确定对${productName}-${testCaseName}执行测试？",
      "select": "选择发单工具",
      "system": "连接目标LDP业务系统",
      "select1": "选择测试用例",
      "text11": "新建用例",
      "delete": "删除用例",
      "config": "配置多账号",
      "row1": "④&nbsp;&nbsp;&nbsp;执行测试",
      "log": "打印时延日志",
      "row2": "执行测试"
    },
    "appBindingCore": {
      "item": "筛选条件",
      "info": "线程信息",
      "manage": "管理IP",
      "select": "请选择管理IP",
      "node": "应用节点名",
      "select1": "请选择应用节点名",
      "text": "线程拥有者",
      "pleaseinput": "请输入线程拥有者",
      "text1": "是否绑核",
      "select2": "请选择绑核",
      "text2": "是",
      "text3": "否",
      "text4": "线程名",
      "type": "线程类型",
      "text5": "绑核"
    },
    "coreReplayObservation": {
      "starttimeStarttime": ">开始时间：{{ startTime ||",
      "textText": ">\r\n                    当前交易日重演完成时，将展示此交易日中",
      "status": ">\r\n                    当前交易日消息收发的实时状态。应答比对：当前交易日",
      "startText": "当前交易日重演尚未开始，请稍候",
      "text": "}；  重演交易日文件目录：${replayFilePath ||",
      "text1": "重演结果",
      "text2": "相同表数量",
      "text3": "不同表数量",
      "text4": "应答一致",
      "text5": "应答不一致",
      "text6": "消息收发",
      "text7": "应答比对进度(%)",
      "text8": "需比对：",
      "text9": "已比对：",
      "text10": "发送进度(%)",
      "text11": "需发送：",
      "text12": "已发送：",
      "text13": "应答进度(%)",
      "text14": "需应答：",
      "text15": "已应答：",
      "monitor": "重演工具监控",
      "textTps": "发送吞吐(tps)",
      "textTps1": "应答吞吐(tps)",
      "textTps2": "应答比对吞吐(tps)",
      "text16": "内存(%)",
      "text17": "仅显示应答不一致",
      "text18": "分片号",
      "cluster": "集群名",
      "text19": "发送进度",
      "text20": "应答进度",
      "text21": "应答比对进度",
      "text22": "应答不一致数",
      "data": "数据比对进度",
      "data1": "数据不一致数",
      "status1": ", // 重演任务状态\r\n            dateStatus:",
      "dateStatus": ", // 重演日期状态\r\n            startTime:",
      "timeStart": ", // 开始时间\r\n            replayFilePath:",
      "success": "终止成功",
      "success1": "暂停成功",
      "success2": "恢复成功",
      "text23": "具体任务细节",
      "starttimeStarttime1": "开始时间：{{ startTime || \"-\" }}",
      "text24": "正在加载",
      "update": "数据更新中",
      "updateData": "{{ autoUpdateTime }}s 数据更新",
      "text25": "启动",
      "text26": "恢复",
      "text27": "暂停",
      "text28": "当前交易日重演完成时，将展示此交易日中\"重演\"与\"持久化\"中相同/不同表数量、应答的一致/不一致数量",
      "recordStatus": "当前交易日消息收发的实时状态。应答比对：当前交易日\"重演的应答\" 与 \"生产已记录的应答\"比对。",
      "text29": "当前交易日中，重演工具本身的发送和应答速度及资源使用情况",
      "core": "（ 核心总数：{{ instanceTotalCount }}",
      "finished": "已完成重演：{{ instanceCompletedCount }} ）",
      "error": "错误原因",
      "data2": "核心数据重演",
      "item": "更多条件",
      "list": ">重演任务列表</div>\r\n          <div class=",
      "row": "执行结果",
      "textH": ">一致</h-option>\r\n            <h-option key=",
      "select": "选择交易日",
      "name": "输入任务名称",
      "text30": "任务来源",
      "time": "执行开始时间",
      "select1": "请选择时间范围",
      "name1": "任务名称",
      "text31": "任务标识：",
      "status2": "执行状态",
      "time1": "执行结束时间",
      "text32": "(不一致数：",
      "text33": "不一致数",
      "edit": "修改成功",
      "success3": "启动成功",
      "success4": "重演任务同步成功！数量：${res?.data?.successCount || 0}",
      "delete": "确定要删除此任务？",
      "deleteRemoveDataList": "删除重演任务后，任务将从列表移除，同时任务所产生的数据将一并被删除。请谨慎操作。",
      "list1": "重演任务列表",
      "text34": "任务总数:",
      "create": "创建",
      "text35": "同步任务",
      "text36": "一致",
      "text37": "不一致",
      "text38": "原重演任务中的重演对象可能发生变化，请仔细核对。",
      "text39": "发送间隔",
      "text40": "重演内容",
      "param": "任务参数",
      "copy": "复制重演任务",
      "create1": "创建重演任务",
      "text41": "确定重演对象",
      "config": "配置任务参数",
      "info": "信息核对",
      "selectTextTextCore": "按需选择需要重演“交易日”与对应的“核心”。",
      "selectCount": "基于前两个步骤的选择，将对应产生的重演任务，请仔细核对任务内容。",
      "pleaseinput1000": "请输入0~1000",
      "text42": "重演任务",
      "createCopy": ",\r\n            // 复制、创建\r\n            type:",
      "create2": "创建并启动成功",
      "select2": "请至少选择一个核心",
      "config1": "根据实际情况配置重演任务相关参数。",
      "confirm": "确认离开页面？",
      "createCopy1": "{{ type === 'copy'? '复制重演任务' : '创建重演任务' }}",
      "core1": "核心集群",
      "text43": "发送间隔:",
      "second": "{{ formItems.sendIntervalMs }} 毫秒",
      "edit1": "修改路径：",
      "text44": "下一步：{{ stepList[currentStep + 1] }}",
      "create3": "创建并启动任务"
    },
    "endpointConfig": {
      "add": "添加网关节点",
      "text": "接入点名",
      "node": "接入应用节点",
      "text1": "接入点IP",
      "text2": "接入点端口",
      "text3": "接入协议",
      "text4": "字符集",
      "config": "批量配置",
      "text5": "连通性测试",
      "add1": "添加节点",
      "type": "应用节点类型",
      "application": "应用身份",
      "manage": "管理端口",
      "cluster": "应用集群",
      "system": "系统号",
      "text6": "支持抓包",
      "text7": "支持发包",
      "config1": "配置文件",
      "text8": ", // 超出的文本隐藏",
      "text9": ", // 溢出用省略号显示",
      "text10": "开发平台",
      "application1": "应用插件ID",
      "config2": "接入网关配置",
      "service": "后端服务：${ACCESS_CONFIG_TITLE[this.endpointType]}",
      "deleteNameNode": "您确定删除名为\"${name}\"的节点吗？"
    },
    "latencyTrendAnalysis": {
      "config": "链路配置",
      "analysis": ">分析目标</div>\r\n                <h-form-item label=",
      "select": "请选择链路",
      "statistics": "统计日期",
      "statistics1": "统计时间",
      "statistics2": "统计方式",
      "select1": "请选择统计方式",
      "statistics3": "统计口径",
      "select2": "请选择统计口径",
      "hour": "自定义范围不得超过18小时",
      "text": "全部市场",
      "text1": "日盘",
      "text2": "早盘",
      "text3": "午盘",
      "analysis1": "分析目标",
      "data": "数据范围",
      "data1": "数据过滤"
    },
    "ldpDataObservation": {
      "name": "应用名称",
      "version": "应用版本",
      "type": "应用类型",
      "application": "应用开发平台",
      "name1": "产品名称",
      "application1": "应用简称",
      "number": "应用编号",
      "instance": "应用实例",
      "text": "pid文件",
      "name2": "插件名称",
      "instance1": "实例号",
      "version1": "插件版本",
      "date": "发布日期",
      "function": "管理功能数量",
      "list": "管理功能列表",
      "text1": "目录",
      "text2": "目录地址",
      "config": "配置地址",
      "config1": "zk配置地址"
    },
    "ldpLinkConfig": {
      "info": "仲裁服务信息",
      "service": "服务提供者",
      "cluster": "服务集群地址",
      "service1": "集中仲裁服务路径",
      "type": "集群类型",
      "type1": "集群应用节点类型",
      "count": "集群内成员个数",
      "info1": "应用集群信息",
      "infoClusterApplication": "应用集群信息：${(this.tableData || []).length}",
      "application": "应用注册中心",
      "info2": "节点信息同步",
      "node": "应用节点注册中心路径",
      "application1": "短应用名",
      "number": "节点编号",
      "number1": "分片编号",
      "node1": "应用节点身份",
      "config": "zk配置节点",
      "info3": "应用节点信息",
      "infoNodeApplication": "应用节点信息：${(this.tableData || []).length}",
      "deleteInstanceNodeApplication": "您确定删除名为\"${name}\"的应用节点实例吗？",
      "text": "机房别名",
      "count1": "关联应用个数",
      "info4": "机房信息：${(this.tableData || []).length}",
      "info5": "产品信息",
      "text1": "单例上下文",
      "cluster1": "集群上下文",
      "text2": "模板",
      "application2": "应用",
      "application3": "关联应用",
      "service2": "服务",
      "type2": "服务类型",
      "text3": "主机名",
      "text4": "主机别名",
      "textText": "IP地址/域名",
      "text5": "机房",
      "text6": "机房名",
      "application4": "应用分片",
      "text7": "分片名",
      "config1": "已托管配置",
      "config2": "配置节点名称",
      "config3": "配置根目录",
      "config4": "配置提供者",
      "config5": "配置服务地址",
      "product": "产品实例：",
      "name": "产品名称：",
      "type3": "产品类型：",
      "system": "关联业务系统：",
      "config6": "产品配置中心：",
      "text8": "主题数：",
      "text9": "主题引用模板：",
      "text10": "单例上下文数：",
      "text11": "单例上下文引用模板：",
      "cluster2": "集群上下文数：",
      "cluster3": "集群上下文引用模板：",
      "text12": "主题模板数：",
      "text13": "通用上下文模板：",
      "cluster4": "集群上下文模板：",
      "versionFunction": "该功能为商业版特性,请咨询销售获取商业版本开通策略",
      "itemRow": "`告警规则执行 共 ${monitorTotal} 条`",
      "monitor": "当前产品暂无监控告警配置",
      "add": "添加规则成功！",
      "success": "告警规则清除成功！",
      "itemRow1": "告警规则执行 共 ${monitorTotal} 条",
      "add1": "添加规则",
      "import": "导入规则",
      "clear": "清空规则",
      "text14": "插件",
      "description": "功能说明",
      "function": "功能号名",
      "system1": "子系统号",
      "text15": "读写标识",
      "status": "可运行的业务状态",
      "text16": "定位模式",
      "data": "数据管理功能号",
      "function1": "平台功能号",
      "function2": "业务功能号",
      "data1": "数据分片",
      "data2": "数据源",
      "text17": "分片key",
      "text18": "定位串",
      "data3": "数据区间",
      "cluster5": "关联集群",
      "data4": "数据上场功能号",
      "times": "二次上场功能号",
      "function3": "SQL功能号",
      "text19": "内存表",
      "success1": "配置信息同步成功！",
      "dataInfo": "数据分片、数据管理功能号配置信息已同步！",
      "failed": "配置信息同步失败！",
      "config7": "去配置",
      "edit": "修改成功！",
      "data5": "同步服务数据",
      "list": "上下文列表",
      "name1": "上下文名称",
      "text20": "模式",
      "text21": "发送主题",
      "text22": "接收主题",
      "text23": "引用模板",
      "text24": "标签",
      "text25": "测试连通性",
      "config8": "SSH配置",
      "text26": "; // 渲染IP地址\r\n                        return h(",
      "text27": "SSH用户名",
      "text28": "; // 渲染SSH用户名\r\n                        return h(",
      "text29": "关联机房",
      "text30": "或",
      "service3": "服务器：${(this.tableData || []).length}"
    },
    "ldpLogCenter": {
      "status": ">\r\n        <!-- 加载状态 -->\r\n        <a-loading v-if=",
      "errorLog": ">回库错误日志</div>\r\n                    <div class=",
      "name": "集群名称",
      "type": "账户类型",
      "count": "多个账号以英文逗号隔开",
      "select": "选择不能为空",
      "status1": "回库状态",
      "success": "已成功",
      "time": "事务时间",
      "text": "线程组",
      "count1": "支持多账号以英文逗号分隔且最多50个",
      "text1": "账户",
      "text2": "线程号",
      "text3": "事务长度",
      "error": "错误号",
      "error1": "错误消息",
      "status2": "回库状态：",
      "errorRecordRow": "表示在用户进行\"重试\"操作之后，本地记录的错误事务，是否重新发送到网关，完成回库。",
      "success1": "未成功",
      "0Text": ")?.[0]; // 获取",
      "query": "查询失败",
      "text4": "股东代码",
      "detail": "点击行查看对应日志记录详情",
      "recordItem": "共 {{ totalCount }} 条记录",
      "text5": "上一页",
      "text6": "下一页",
      "text7": "全部账户：",
      "row": "进行中：",
      "text8": "正常完成：",
      "text9": "终止完成：",
      "row1": "进行中",
      "text10": "正常完成",
      "text11": "终止完成",
      "error2": "剩余错误账户数",
      "error3": "错误账户总数",
      "error4": "剩余回库错误事务数",
      "error5": "回库错误事务总数",
      "text12": "重试",
      "log": "日志",
      "status3": "最后重试执行状态",
      "text13": "全部重试",
      "text14": "全部终止",
      "text15": "全部账户",
      "text16": "需要重试的事务数",
      "success2": "重试已成功事务数",
      "row2": "执行进度",
      "row3": "执行总耗时",
      "start": "开始重试消息序号",
      "start1": "开始重试事务号",
      "end": "结束重试消息号",
      "end1": "结束的事务号",
      "text17": "最新重试消息号",
      "text18": "最新重试的事务号",
      "row4": "已存在进行中的重试任务！",
      "text19": "终止任务",
      "confirm": "您确认要终止当前正在执行中的重试任务吗？",
      "create": "创建错误重试任务",
      "confirm1": "您确认要对所有业务错误账户执行重试处理吗？",
      "none": "暂无集群需要重试！",
      "none1": "暂无进行中任务！",
      "create1": "创建重试任务成功!",
      "create2": "创建重试任务失败!",
      "success3": "终止任务成功!",
      "failed": "终止任务失败!",
      "query1": "核心集群回库错误查询",
      "info": "记录字段信息",
      "text20": "字段名",
      "text21": "字段值"
    },
    "ldpMonitor": {
      "text": ">全局总览</div>\r\n            <a-loading v-if=",
      "text1": "全局总览",
      "text2": "主",
      "text3": "同城",
      "text4": "异地",
      "textEleShardingno": "分片${ele.shardingNo || '-'}",
      "function": "功能号处理",
      "textTps": "吞吐(tps)",
      "textNs": "平均时延(ns)",
      "text5": "委托交易",
      "text6": "吞吐率",
      "error": "错误率",
      "query": "委托查询",
      "text7": "其他业务",
      "pleaseinput": "请输入表名",
      "text8": "未加载",
      "loading": "加载中",
      "text9": "加载完成",
      "failed": "加载失败",
      "record": "内存记录数",
      "record1": "持久化记录数",
      "data": "数据差量",
      "status": "加载状态",
      "detail": "表详情",
      "times": "上场次数:",
      "detail1": "表加载详情",
      "data1": "数据差量不为0",
      "infoApplication": ">应用连接信息</div>\r\n            <a-loading v-if=",
      "info": "应用连接信息",
      "queryItem": ">\r\n        <!-- 上下游组合条件查询 -->\r\n        <div v-if=",
      "type": "连接类型",
      "status1": "连接状态",
      "node": "仲裁节点",
      "search": "搜索应用节点",
      "text10": ">上下游关系</h-option>\r\n            <h-option value=",
      "queryError": "查询应用节点下拉列表-错误:",
      "node1": "过滤仲裁节点",
      "node2": "不过滤仲裁节点",
      "text11": "上下游关系",
      "cluster": "集群同步关系",
      "info1": "集群连接信息",
      "search1": "搜索集群",
      "setting": "拓扑显示设置",
      "node3": ">显示未托管节点</div>\r\n                <div class=",
      "config": ">RCM配置</div>\r\n                <div class=",
      "text12": ">默认标签</div>\r\n                <div class=",
      "query1": "查询rcm配置失败：",
      "query2": "查询标签失败：",
      "node4": "显示未托管节点",
      "text13": "上下文",
      "config1": "RCM配置",
      "text14": "默认标签",
      "text15": ">\r\n    <!-- 指标总览 -->\r\n    <info-sum-bar :data=",
      "text16": "委托业务性能总览",
      "minute": "最近5分钟",
      "minute1": "最近15分钟",
      "minute2": "最近30分钟",
      "hour": "最近1小时",
      "second": "5秒",
      "text17": "吞吐",
      "text18": "时延",
      "textQbs": "吞吐(qbs)",
      "textNs1": "时延(ns)",
      "text19": "请求数",
      "times1": "次",
      "detail2": "查看详情",
      "search2": "搜索主题",
      "text20": "同步模式",
      "copy": "状态机复制",
      "copy1": "消息复制"
    },
    "ldpTable": {
      "text": "操作内容",
      "text1": "操作用户",
      "row": "操作执行",
      "row1": "未执行",
      "text2": "失效",
      "row2": "执行中",
      "row3": "执行完毕",
      "data": "数据记录号",
      "type": "字段类型",
      "edit": "修改值",
      "text3": "原纪录值",
      "edit1": "修改状态",
      "edit2": "修改执行",
      "edit3": "该修改未执行",
      "text4": ", // 请求路径\r\n                requestProtocol:",
      "text5": ", // 请求协议\r\n                requestTime:",
      "time": ", // 请求时间\r\n                requestParams:",
      "param": ", // 请求参数\r\n                response:",
      "failed": "查看详情失败",
      "text6": "目标表:",
      "edit4": "批量修改操作:",
      "data1": "影响数据条数:",
      "count": "影响字段个数:",
      "text7": "用户名:",
      "text8": "用户角色:",
      "edit5": "修改单提交时间:",
      "edit6": "修改单状态:",
      "edit7": "修改成功数:",
      "edit8": "修改失败数:",
      "edit9": "修改开始时间:",
      "edit10": "修改结束时间:",
      "edit11": "修改总耗时:",
      "property": "内存表属性",
      "failed1": "表单验证失败!",
      "data2": "内存数据表管理",
      "text9": "断开",
      "text10": "连接",
      "select": "请选择连接的产品及应用节点",
      "linkbtnTextText": "{{ linkbtn ? '断开' : '连接' }}",
      "text11": ">\r\n        <!-- 筛选部分 -->\r\n        <div class=",
      "edit12": "历史修改单",
      "edit13": "修改预览",
      "editDetail": "boxInfo.tableName+ ' 表修改操作详情'",
      "record": "原记录值",
      "query": "修改单列表查询失败!",
      "clear": "清空操作失败!",
      "query1": "修改预览查询失败!",
      "edit14": "预览修改单条目删除成功!",
      "edit15": "预览修改单条目删除失败!",
      "edit16": "预览修改单条目不存在!",
      "edit17": "执行修改失败!",
      "editDelete": "您确定删除\"${params.row.fieldName}\"的修改操作吗？",
      "clear1": "您确定执行清空操作吗？",
      "clear2": "清空操作",
      "edit18": "提交修改",
      "itemText": "20条/页",
      "itemText1": "30条/页",
      "itemText2": "50条/页",
      "info": "数据表信息",
      "data3": "数据表字段",
      "data4": "数据类型",
      "edit19": "是否可修改",
      "query2": "查询结果可见",
      "selectall": "全选字段",
      "query3": "可作为查询条件",
      "data5": "数据表名：",
      "data6": "数据表扩展属性：",
      "data7": "数据表说明：",
      "config": "禁用struct字段可见属性配置"
    },
    "locateConfig": {
      "config": "配置对比",
      "config1": "配置文件类型",
      "node": "源节点",
      "node1": "目标节点",
      "text": "是否一致",
      "text1": "一键修正",
      "listConfigNode": ">\r\n        <!-- 定位节点配置列表 -->\r\n        <div class=",
      "save": ">保存</a-button>\r\n                <a-button v-if=",
      "list": "定位节点配置列表",
      "config2": "节点定位插件配置",
      "config3": "节点定位规则配置",
      "config4": "配置对象类型",
      "config5": "配置对象",
      "text2": "在线",
      "text3": "离线",
      "config6": "对比配置",
      "text4": "文件大小超出16MB",
      "config7": "配置文件格式：{{ localLanguage || '-' }}",
      "save1": "保存",
      "text5": "同步",
      "node2": "`对比源节点: ${sourceDiff.sourceNode || '-'}`",
      "node3": "`对比目标节点: ${targetDiff.targetNode || '-'}`",
      "node4": "对比源节点: ${sourceDiff.sourceNode || '-'}",
      "node5": "对比目标节点: ${targetDiff.targetNode || '-'}",
      "configLanguage": "配置文件格式：{{ language || '-' }}",
      "config8": "配置路径：{{ sourceDiff.sourcePath || '-' }}",
      "config9": "配置路径：{{ targetDiff.targetPath || '-' }}",
      "select": "选择对比配置的节点",
      "config10": "配置类型",
      "config11": "原始配置来源",
      "node6": "对比源节点",
      "node7": "对比目标节点",
      "node8": "节点定位AR"
    },
    "managementQuery": {
      "info": "基本信息",
      "nameFunction": ">功能名称：</span>\r\n            <p class=",
      "remarkFunction": ">功能备注：</span>\r\n            <p class=",
      "version": ">版本号：</span>\r\n            <p class=",
      "updateTime": ">更新时间：</span>\r\n            <p class=",
      "text": ">提供者：</span>\r\n            <p class=",
      "descriptionFunction": ">功能说明：</span>\r\n            <p class=",
      "description": "入参说明",
      "description1": "出参说明",
      "description2": "案例说明",
      "text1": ">入参案例</div>\r\n        <div class=",
      "text2": ">出参案例</div>\r\n        <div class=",
      "name": "入参名称",
      "param": "参数类型",
      "param1": "参数说明",
      "name1": "出参名称",
      "name2": "功能名称：",
      "remark": "功能备注：",
      "version1": "版本号：",
      "updatetime": "更新时间：",
      "text3": "提供者：",
      "description3": "功能说明：",
      "text4": "入参案例",
      "text5": "出参案例",
      "search": "请输入管理功能名称搜索",
      "function": "管理功能",
      "remove": "移除",
      "export": "正在导出",
      "export1": "导出停止",
      "failed": "终止失败",
      "export2": "导出终止异常",
      "exportText": "导出任务启动成功，请稍候",
      "export3": "导出任务启动失败",
      "searchFunctionManage": "搜索管理功能 ({{ leftData.length }})",
      "exportList": "待导出列表 ({{ rightData.length }})",
      "text6": "原因",
      "clear": "清空列表",
      "table": "表格",
      "config": "功能配置",
      "textNbsp": "结果展示方式\r\n      &nbsp;",
      "count": "最大展示Tab个数",
      "config1": "交互配置",
      "list": "只保留激活插件功能列表展开",
      "record": "记录最后一次输入参数",
      "function1": "结果页与管理功能联动",
      "type": "单击Get类型功能菜单时自动发起请求",
      "export4": "管理功能导出",
      "textExport": "根据需要，选择想要导出的管理功能。",
      "text7": "入参",
      "config2": "`配置（`+ modalData.title + ')'",
      "saveConfig": ">保存配置</a-button\r\n      >\r\n      <a-button type=",
      "pleaseinput": "请输入jsonPath",
      "text8": "别名",
      "table1": "表格展示名称",
      "text9": "是否支持排序",
      "tableRow": "表格字段支持行排序(仅当表格为一维数组时生效)",
      "config3": "配置（",
      "text10": "值",
      "data": "处理数据时出错:",
      "description4": "管理功能使用说明",
      "text11": "前置",
      "text12": "回库",
      "pleaseinput1": "placeholder=\"请输入\" disabled :class=\"["
    },
    "marketAllLink": {
      "text": "传输链路",
      "analysis": ">分析指标</div>\r\n                <h-form-item label=",
      "data": ">数据范围</div>\r\n                <h-form-item label=",
      "statistics": "统计范围",
      "text1": "自定义范围",
      "spansText": ",\r\n                spans: [ // 链路指标",
      "95Text": "95%分位数",
      "5Text": "5%分位数",
      "analysis1": "分析指标",
      "submit": "提交",
      "none": "当前行情系统暂无时延走势",
      "second": "秒级系统穿透时延标准差",
      "system": ">系统抖动范围</p>\r\n                            <p class=",
      "text2": ">时延正常率</p>\r\n                        <p class=",
      "text3": "95分位数",
      "text4": "5分位数",
      "system1": "系统抖动范围",
      "row": "快照行情",
      "row1": "指数行情",
      "text5": "逐笔委托",
      "text6": "逐笔成交",
      "name": ",        // 链路名称\r\n                    linkName:",
      "text7": ", // 抖动范围",
      "thisS": "${this.spanData.name}-K线",
      "nameTextNs": "${name}走势(ns)",
      "system2": "{{ lastTimeData.time }} 系统全链路时延",
      "text8": "最大值:",
      "text9": "95分位数:",
      "text10": "5分位数:",
      "text11": "最小值:",
      "statusApplication": ">应用状态：</span>\r\n                    <span class=",
      "text12": ">时延正常率</p>\r\n            <p v-for=",
      "status": "应用状态："
    },
    "mcDataObservation": {
      "text": "主题名",
      "select": "请选择主题名",
      "text1": "分区",
      "select1": "请选择分区",
      "query": "查询方式",
      "pleaseinputText": "请输入1~1000的正整数",
      "text2": "最近",
      "text3": "最早",
      "text4": "消息ID范围",
      "text5": "分区号",
      "text6": "消息ID",
      "text7": "生产者",
      "index": "发布序号",
      "time": "发布时间",
      "text8": "消息大小",
      "item": "过滤条件",
      "text9": "消息内容",
      "text10": "或者",
      "cluster": "消费者集群消息处理",
      "cluster1": "消费者集群",
      "instance": "实例名",
      "text11": "消息积压数",
      "index1": "消费消息序号",
      "index2": "最新消息序号",
      "times": "调用次数",
      "text12": "当日客户端平均处理耗时",
      "text13": "当日客户端平均排队耗时",
      "row": "当日客户端平均执行耗时",
      "text14": "当日客户端最大处理耗时",
      "text15": "当日客户端最大排队耗时",
      "row1": "当日客户端最大执行耗时",
      "text16": "当日客户端最小处理耗时",
      "text17": "当日客户端最小排队耗时",
      "row2": "当日客户端最小执行耗时",
      "text18": "消息处理变化趋势",
      "minute": "最新15分钟",
      "minute1": "历史15分钟",
      "count": "个",
      "textCount": "数量(个)",
      "text19": "最大",
      "text20": "最小",
      "text21": "当日客户端处理耗时",
      "text22": "当日客户端排队耗时",
      "row3": "当日客户端执行耗时",
      "text23": "耗时",
      "column": "死信队列",
      "refresh": "刷新",
      "text24": "死信主题",
      "text25": "消息主题",
      "text26": "消息分区",
      "time1": "上次接收时间",
      "text27": "死信消息数",
      "clear": "清空队列",
      "number": "线程编号",
      "count1": "当前队列消息个数",
      "column1": "历史队列最大消息数",
      "text28": "处理总数",
      "version": "产品版本",
      "type": "产品类型",
      "cluster2": "集群主机数",
      "number1": "编号",
      "text29": "主机地址",
      "text30": "发布线程",
      "text31": "会话线程",
      "text32": "订阅线程",
      "text33": "推送线程",
      "text34": "主题线程",
      "text35": "消息中心",
      "statistics": "全主题统计",
      "statistics1": "单主题统计",
      "node": "节点数",
      "text36": "会话数",
      "text37": "主题总数",
      "text38": "持久化主题数",
      "text39": "分区总数",
      "text40": "分区副本总数",
      "text41": "生产者总数",
      "text42": "生产消息总数",
      "text43": "消费者",
      "text44": "消费者总数",
      "text45": "订阅项总数",
      "text46": "推送消息总数",
      "column2": "集群线程队列消息变化趋势",
      "text47": "主题生产消费消息变化趋势",
      "text48": "发布消息变化差量",
      "text49": "订阅消息变化差量",
      "textCount1": "消息数(个)",
      "select2": "请选择主题",
      "select3": "请选择生产者",
      "select4": "请选择消费者",
      "cluster3": ", // 集群下拉框\r\n            brokerType:",
      "text50": "发布主题数",
      "text51": "发布分区数",
      "text52": "生产消息数",
      "info": "生产者信息",
      "text53": "订阅项数",
      "text54": "订阅主题数",
      "text55": "推送消息数",
      "text56": "补缺消息数",
      "text57": "客户端",
      "number2": "订阅编号",
      "time2": "订阅时间",
      "text58": "订阅方式",
      "text59": "广播消费",
      "cluster4": "集群消费",
      "text60": "订阅主题",
      "info1": "消费者信息",
      "info2": "分区信息",
      "description": "主题描述",
      "text61": "分区数",
      "text62": "分区副本数",
      "text63": "是否全局主题",
      "text64": "有序级别",
      "text65": "业务校验",
      "text66": "可靠级别",
      "text67": "消息有效期",
      "save": "服务端是否保存消费者偏移",
      "text68": "生产者数",
      "text69": "消费者数",
      "node1": "主分区所在节点",
      "node2": "副本分区所在节点",
      "text70": "持久化消息数",
      "name": "消费者实例名称",
      "text71": "主题前缀",
      "text72": "已推送消息数",
      "count2": "补缺个数",
      "item1": "过滤条件值",
      "text73": "发布的消息数",
      "text74": "分区有序",
      "text75": "全局有序",
      "text76": "文件",
      "text77": "内存",
      "info3": "主题信息"
    },
    "mcDeploy": {
      "pleaseinput": "请输入主题名",
      "update": "上次更新日期",
      "number": "主题编号",
      "text": "副本数",
      "update1": "上次更新时间",
      "text1": "动态主题同步"
    },
    "mdbDataObservation": {
      "data": "数据库名称",
      "text": ", // 文本\r\n                            label:",
      "data1": "数据库表个数",
      "text1": "主控进程号",
      "text2": "加锁进程号",
      "text3": "已处理事务号",
      "text4": "事务处理性能",
      "text5": "事务处理总数",
      "textTextSecond": "事务处理吞吐(笔/秒)",
      "text6": "事务处理吞吐",
      "textText": "总数(笔)",
      "textTextSecond1": "吞吐(笔/秒)",
      "text7": "内存使用分布",
      "text8": ", // 文本\r\n                        label:",
      "info": "内存数据库版本信息",
      "config": "工作进程配置",
      "text9": "AdminFile文件",
      "config1": "事务处理配置",
      "config2": "内存分配配置",
      "record": "总记录数",
      "text10": "占已使用内存比率",
      "search": "请搜索内存表",
      "record1": "表记录数增长统计",
      "statistics": "表内存使用增长统计",
      "text11": "表内存使用分布",
      "dataText": "表数据存储-文件",
      "info1": "表索引信息",
      "analysis": "性能分析开关",
      "detail": "执行详情",
      "text12": "事务控制器ID",
      "times": "执行次数",
      "text13": "平均耗时",
      "text14": "最小耗时",
      "text15": "前十最大耗时",
      "text16": "标准差",
      "textText1": "开启 (正常级别)",
      "textText2": "开启 (基本级别)",
      "textTop10": "事务处理吞吐 - Top10",
      "text17": "死锁检测",
      "text18": "只看被阻塞控制器",
      "text19": "只看工作控制器",
      "info2": "Undo文件信息",
      "info3": "Undo文件记录数信息",
      "text20": "Undo事务号: -",
      "text21": "Undo事务号:${data.CommitSqn || '-'}",
      "time": "执行时间",
      "text22": "事务控制器",
      "textMs": "耗时（ms）",
      "text23": "处理表",
      "text24": "处理表明细"
    },
    "mdbPrivilegeManage": {
      "add": ">添加角色</a-button>\r\n                <a-button type=",
      "text": "角色名：",
      "pleaseinput": "请输入角色名",
      "text1": "角色名",
      "config": "角色未配置权限",
      "description": "角色描述",
      "edit": "修改时间",
      "edit1": "编辑",
      "config1": "权限配置",
      "text2": "绑定用户",
      "create": "创建角色成功!",
      "edit2": "修改角色成功!",
      "delete": "删除成功!",
      "deleteSystem": "read only为系统内置角色,不可删除,请勿勾选！",
      "delete1": "批量删除角色",
      "delete2": "确认要批量删除已选中的角色吗？",
      "deleteConfirm": "确认要删除角色\"${params.row.roleName}\"吗？",
      "add1": "添加角色",
      "delete3": "删除角色",
      "text3": "用户",
      "add2": ">添加用户</a-button>\r\n                <a-button type=",
      "text4": "用户名：",
      "pleaseinput1": "请输入用户名",
      "text5": "用户名",
      "description1": "用户描述",
      "text6": "关联角色",
      "reset": "重置密码",
      "failed": "用户操作失败:",
      "edit3": "用户修改成功！",
      "edit4": "用户修改失败！",
      "delete4": "批量删除用户",
      "delete5": "确认要批量删除已选中的用户吗？",
      "deleteConfirm1": "确认要删除用户\"${params.row.userName}\"吗？",
      "reset1": "确定要重置密码吗？",
      "add3": "添加用户",
      "delete6": "删除用户"
    },
    "networkSendAndRecevied": {
      "text": "用例内容",
      "edit": "用例名修改成功!",
      "delete": "用例删除成功!",
      "empty": "Body内容为空或格式不正确",
      "save": "用例保存成功!",
      "select": "请先选择用例",
      "textT": "用例：${this.menuList?.[0]?.label}",
      "textItemLabel": "用例：${item?.label}",
      "delete1": "您确定删除 '${item.label}' 用例？",
      "text1": "抓包中",
      "function": "功能号：",
      "text2": "消息体",
      "list": "消息列表",
      "clear": "清空",
      "text3": "用户自定义",
      "time": "抓包时间",
      "type": "包类型",
      "info": "附加信息",
      "failed": "开启抓包失败",
      "failed1": "停止抓包失败",
      "reset": "消息列表已重置！",
      "queryRefreshListIndexNone": "当前页为首页无法支持翻页，请刷新列表或用序号跳转至您想查询的位置",
      "queryRefreshListIndexNone1": "当前页为最后一页无法支持翻页，请刷新列表或用序号跳转至您想查询的位置",
      "clear1": "确定清空抓包数据吗?",
      "none": "无托管应用节点",
      "count": "一个应用节点仅能开启一个抓包任务",
      "start": "开始抓包",
      "text4": "停止抓包",
      "textMsgtotalItem": "共 {{ msgTotal }} 条",
      "index": "跳转序号",
      "text5": "接入点",
      "none1": "图片无法显示",
      "textDiv": ">开</div>\r\n                            <div slot=",
      "text6": "输入用例名",
      "deleteTime": ": {}\r\n        * 2. 关闭时间戳开关：删除40号域，即删除",
      "text7": "请求",
      "text8": "响应",
      "empty1": "请求Body内容为空或格式不正确",
      "success": "发包成功！",
      "none2": "无可用接入点",
      "textConfigProduct": "请至“产品服务配置-产品服务网关”",
      "timeNbsp": "时间戳 &nbsp;",
      "text9": "开",
      "text10": "关",
      "text11": "发送",
      "list1": "&nbsp;&nbsp;&nbsp;用例列表",
      "text12": "引用",
      "none3": "无内容",
      "save1": "保存为用例",
      "text13": "发包调用总耗时：",
      "text14": "发包调用耗时：",
      "time1": "查看时间戳",
      "list2": "日志列表",
      "countText": "支持输入多个，采用英文分号区分",
      "pleaseinput": "请输入功能号以英文分号形式间隔",
      "data": "获取数据"
    },
    "productDataStorage": {
      "data": "数据冷备",
      "service": "归档服务器:",
      "text": "归档目录:",
      "data1": "产品元数据:",
      "data2": "产品遥测数据:",
      "data3": "数据日期范围:",
      "textA": ">下一步</a-button>\r\n                <a-button v-if=",
      "cancel": ">取消</a-button>\r\n                <a-button v-if=",
      "select": "选择归档条件",
      "confirm": "归档条件确认",
      "data4": "时延跟踪数据",
      "data5": "监控指标数据",
      "success": "归档指令发送成功！",
      "failed": "归档失败!",
      "confirmSelectItem": "{{ !nextStatus ? '选择归档条件':'归档条件确认'}}",
      "service1": "归档服务器：",
      "text1": "归档目录：",
      "data6": "产品元数据：",
      "data7": "产品遥测数据：",
      "data8": "数据日期范围：",
      "text2": "至",
      "text3": "下一步",
      "text4": "归档",
      "text5": "每日定时清理",
      "data9": "数据保留天数：",
      "day": "最大3600天",
      "select1": "选择清理时间",
      "text6": "是否启用：",
      "query": ">查询</a-button>\r\n                <a-button style=",
      "date": "清理日期：",
      "text7": "清理结果：",
      "dataDay": "数据保留天数1~3600天",
      "time": "清理时间",
      "text8": "清理结果",
      "failed1": "失败信息",
      "data10": "被清理数据范围",
      "text9": "索引号",
      "data11": "被清理数据大小",
      "success1": "定时清理配置成功！",
      "config": "清理配置",
      "textRow": "(启用后生效，每日定时执行清理操作)",
      "record": "定时清理记录",
      "reset": "重置",
      "product": "产品节点：",
      "data12": "数据日期：",
      "text10": "资金账号：",
      "date1": "请限制清理日期小于30天",
      "success2": "清理指令发送成功！",
      "text11": "清理",
      "data13": "数据清理配置",
      "product1": "产品节点:",
      "data14": "数据日期:",
      "data15": "数据类型:",
      "select2": "选择清理条件",
      "confirm1": "清理条件确认",
      "failed2": "数据清理失败!",
      "confirmSelectItem1": "{{ !nextStatus ? '选择清理条件':'清理条件确认'}}",
      "data16": "数据类型：",
      "deleteWarningDataItem": "警告: 该操作将物理删除符合条件的所有数据, 操作不可逆!",
      "confirm2": "确认清理",
      "data17": "待归档数据清单",
      "item": "文件条数",
      "empty": "占用空间",
      "textText": "总计:索引文件:",
      "countItem": "个; 文件条数:",
      "itemEmpty": "条; 存储空间:"
    },
    "productServiceConfig": {
      "text": "不支持该特性",
      "text1": "是否支持该特性",
      "text2": "不支持",
      "list": "已支持业务链路模型列表",
      "text3": "业务链路度量模型",
      "select": "选择模型",
      "type": "业务系统类型",
      "text4": "模型预览",
      "success": "模型配置成功",
      "failed": "切换模型失败",
      "text5": "确定要切换度量模型？",
      "updateLogConfigTimesProductNodeSystem": "切换度量模型，本产品节点下对应的「链路日志配置」将自动同步更新。本次变动需要重启采集子系统才可生效。",
      "data": "链路数据定义",
      "application": "应用全链路时延度量模型",
      "name": "模型名称",
      "system": "业务系统",
      "version": "业务系统版本",
      "version1": "模型版本",
      "text6": "简介",
      "text7": "跨度定义",
      "name1": "跨度名称",
      "text8": "字段明细",
      "data1": "链路数据",
      "text9": "链路模型",
      "log": "日志类型",
      "log1": "时延日志输出目录",
      "log2": "时延日志关键字",
      "success1": "同步配置成功",
      "deleteConfirmLogConfig": "您确认要删除\"${row.instanceName}\"的链路日志配置吗？",
      "config": "当前已配置链路模型:",
      "config1": "同步配置",
      "description": ">入参说明：</div>\r\n                    <div class=",
      "text10": ">入参示例：</div>\r\n                    <div class=",
      "description1": ">出参说明：</div>\r\n                    <div class=",
      "text11": ">出参示例：</div>\r\n                    <div class=",
      "update": "更新日期：",
      "none": "{{  child.funcNameCn || '暂无'}}",
      "description2": "入参说明：",
      "text12": "入参示例：",
      "description3": "出参说明：",
      "text13": "出参示例："
    },
    "productTimeAnalysis": {
      "query": "查询结果导出",
      "export": "导出文件名:",
      "query1": "请输入查询名称",
      "export1": "导出字段:",
      "save": "保存成功!",
      "query2": "保存查询",
      "query3": "查询名称",
      "text": "字符长度数不得超过15!",
      "empty": "不能为空",
      "text1": "每日",
      "text2": "昨日",
      "text3": "本周",
      "text4": "本月"
    },
    "rcmBacklogMonitor": {
      "list": "上下文消息积压列表",
      "text": "积压数大于0"
    },
    "rcmDeploy": {
      "text": ">主题模板</span>\r\n            <div class=",
      "text1": ">通用上下文模板</span>\r\n            <div class=",
      "cluster": ">集群上下文模板</span>\r\n            <div class=",
      "text2": "继承关系",
      "text3": "未选中模板",
      "detail": "配置详情",
      "name": "模板名称:",
      "text4": "继承模板:",
      "data": "暂无配置数据",
      "save": ",   // 模板名单独保存\r\n            type:",
      "text5": "通用上下文",
      "update": "主题模板更新成功",
      "update1": "上下文模板更新成功",
      "delete": "您确定删除名为 ${keyName} 的${obj[type]}模板？",
      "text6": "主题模板",
      "create": "创建模板",
      "text7": "通用上下文模板",
      "cluster1": "集群上下文模板",
      "instance": "上下文实例分组",
      "text8": "按主题",
      "application": "按应用",
      "text9": "按标签",
      "textHColHColSpan": ">总</h-col><h-col span=",
      "textHColHColSpan1": ">收</h-col><h-col span=",
      "textHR": ">总</h-row>\r\n                        <h-row style=",
      "delete1": "确定删除分组名为${name}下的所有上下文？",
      "text10": "总",
      "text11": "收",
      "text12": "发",
      "text13": "查看更多",
      "delete2": "批量删除",
      "name1": "模板名称",
      "name2": "请输入上下文名称",
      "text14": "上下文模式",
      "name3": "请输入应用名称",
      "query": "查询标签",
      "query1": "请选择查询标签",
      "text15": "中心名",
      "createSuccess": "创建/配置上下文成功!",
      "delete3": "您确认要批量删除已选中的上下文吗",
      "option": "所选项未绑定标签",
      "add": "添加标签",
      "delete4": "删除标签",
      "add1": "添加标签成功!",
      "delete5": "删除标签成功!",
      "deleteConfirm": "您确认要删除名为\"${params.row.name}\"的上下文吗？",
      "deleteRecordItem": "同时删除所有匹配记录（共${this.total}条）",
      "pleaseinputText": "请输入在1-65535之间的值",
      "name4": "主题名称",
      "name5": "请输入主题名称",
      "pleaseinput": "请输入分区号",
      "text16": "只支持输入数字",
      "text17": "通讯地址",
      "text18": "通讯端口",
      "createSuccess1": "创建/配置主题成功!",
      "select": "请选择一条或多条数据",
      "delete6": "批量删除主题",
      "delete7": "您确认要批量删除已选中的主题吗？",
      "deleteConfirm1": "您确认要删除名为\"${params.row.topic}\"的主题吗？",
      "text19": "端口",
      "delete8": "您确认要删除该行配置吗？",
      "text20": "传输地址：",
      "text21": "传输端口：",
      "cluster2": "集群同步地址：",
      "cluster3": "集群同步端口：",
      "text22": "补缺端口：",
      "text23": "上下文ID范围：",
      "text24": "主题ID范围：",
      "text25": "分区范围：",
      "add2": "新增",
      "config": "待发布配置",
      "config1": "已发布配置",
      "config2": "已发布配置告警说明",
      "text26": ">远程：\r\n                    <i :class=",
      "text27": ">历史：\r\n                    <i :class=",
      "downloadConfigNoneRow": "无法在线预览RCM配置文件！请下载源文件进行本地查看。",
      "service": "服务异常",
      "config3": "配置发布",
      "config4": "配置还原",
      "textConfig": "本地变更：本地配置版本已经发生变更。",
      "textConfig1": "远程变更：远程zk配置版本已发生变更。",
      "text28": "还原",
      "text29": "本地：",
      "update2": "最后更新版本：",
      "text30": "远程：",
      "text31": "历史："
    },
    "rcmObservation": {
      "text": "发送端的ContextId",
      "text1": "接收端的ContextId",
      "text2": "消息排队",
      "text3": "消息投递",
      "text4": "缓存积压",
      "text5": "消息持久化",
      "index": "下一个排队消息序号",
      "submit": "提交的消息序号",
      "index1": "下一个处理消息序号",
      "text6": "已经处理的消息数量",
      "failed": "消息回调失败次数统计",
      "confirm": "集群确认消息积压",
      "application": "应用投递消息积压",
      "text7": "待持久化消息积压",
      "index2": "下一个持久化消息序号",
      "text8": "已经持久化的消息数量",
      "info": ">\r\n    <!-- 会话信息 -->\r\n    <info-grid ref=",
      "info1": "会话信息",
      "text9": "主题分区",
      "text10": "消息接收",
      "text11": "消息应答",
      "index3": "下一个待接收消息序号",
      "index4": "收到的最大消息序号",
      "pending": "下一个待处理消息序号",
      "confirm1": "已经确认的消息序号",
      "text12": "通讯分片处理",
      "text13": "分片接收",
      "text14": "分片应答",
      "text15": "分片补缺",
      "text16": "乱序和丢包检测",
      "index5": "下一个待接收分片序号",
      "cancel": "下一个待取消息分片序号",
      "index6": "收到的最大分片序号",
      "confirm2": "已经确认的分片号",
      "text17": "最后发送ACK的原因",
      "row": "补缺执行",
      "statistics": "补缺统计",
      "text18": "丢包检测",
      "statistics1": "丢包统计",
      "info2": ">\r\n        <!-- 会话信息 -->\r\n        <info-grid ref=",
      "text19": "消息发送与应答",
      "number": "下个消息编号",
      "text20": "当前最大消息号",
      "number1": "上次应答消息编号",
      "text21": "消息缓存与持久化",
      "save": "缓存中保存的消息的最小消息号",
      "count": "下一个待持久化的消息号",
      "text22": "已持久化消息数",
      "text23": "消息积压",
      "text24": "缓存积压消息数",
      "text25": "网络积压消息数",
      "text26": "对端消息应答",
      "text27": "分片发送与应答",
      "count1": "下一个待分配的分片号",
      "text28": "当前待发送分片号",
      "index7": "最后一次收到的应答序号",
      "statistics2": "分片发送统计",
      "text29": "异步发送的分片数目",
      "failed1": "底层失败的分片数目",
      "text30": "对端分片应答"
    },
    "secondAppearance": {
      "record": "上场记录总数：",
      "success": "成功记录数：",
      "failed": "失败记录数：",
      "error": "错误信息："
    },
    "sms": {
      "addEditInfo": "modalData.type ? '修改干系人信息' : '添加干系人信息'",
      "pleaseinput": "请输入...",
      "edit": "修改干系人信息",
      "add": "添加干系人信息",
      "text": "手机号格式不正确",
      "empty": "邮箱不能为空",
      "text1": "邮箱格式不正确",
      "add1": "添加失败",
      "edit1": "修改失败",
      "edit2": "告警通知模板编辑",
      "text2": "模板内容",
      "text3": "外发投资者",
      "select": "选择干系人",
      "text4": "通知到手机",
      "text5": "通知到邮箱",
      "success": "操作成功",
      "failed": "操作失败",
      "delete": "您确定删除名为\"${params.row.addresseeName}\"的干系人吗？",
      "param": "告警通知参数存在手机号${phone}, 则发送模板短信至该手机"
    },
    "sqlTable": {
      "selectPleaseselectNodeClusterService": "!isCores ? '请选择节点或集群或服务' : '请选择节点'",
      "core": "title || (isCores ? 'MDB-SQL-多核心分发':'MDB-SQL')",
      "select": "请选择节点或集群或服务",
      "mdbSqlCore": "MDB-SQL-多核心分发",
      "success": "登录成功!",
      "text": "未知用户",
      "text1": "请重新登录!",
      "editText": "修改密码成功!请重新登录!",
      "service": "; // 服务\r\n                case",
      "text2": "登录",
      "text3": "查看密钥",
      "edit": "修改密码",
      "text4": "退出登录"
    },
    "transaction": {
      "modaldataKeyData": "`${modalData.key}时延数据列表`",
      "modaldataKeyData1": "${modalData.key}时延数据列表",
      "create": "创建报表",
      "h3Text": ">\r\n                <h3>报表",
      "create1": "报表创建失败!",
      "createSuccess": "报表\"{{instanceName}}\"创建成功!",
      "text": "交易所：",
      "text1": "席位号：",
      "starttime": "开始时间：",
      "endtime": "结束时间：",
      "create2": "{{instanceId ? '查看' :\r\n                    '创建'}}",
      "statistics": "统计分析",
      "select": "选择市场",
      "select1": "选择链路",
      "select2": "选择指标",
      "pleaseinput": "请输入席位号",
      "pleaseinput1": "请输入资金账号",
      "text2": "分位数指标",
      "analysis": "同比分析",
      "date": "基线日期",
      "time": "基线时间",
      "date1": "同比日期",
      "time1": "同比时间",
      "second": "每秒平均",
      "analysisData": "分析指标（最多只能勾选三个指标数据）",
      "second1": "每秒时延订单",
      "textS": "时延 (μs)",
      "text3": "时延订单"
    },
    "tripartiteServiceConfig": {
      "data": "华讯数据服务",
      "config": "配置服务接入代理",
      "instance": "采集器实例",
      "type": "接入服务类型",
      "service": "接入服务地址",
      "text": "Topic地址",
      "system": "已适配的业务系统",
      "product": "绑定产品节点",
      "create": "创建数据采集代理",
      "edit": "修改数据采集代理",
      "add": "新增成功!",
      "edit1": "修改成功!",
      "deleteInstance": "您确定删除 '${params.row?.instanceName}' 实例？"
    },
    "ustTableVerification": {
      "text": "校验范围",
      "cluster": "集群内校验",
      "data": "源数据库",
      "data1": "目标数据库",
      "type": "校验类型",
      "text1": "按表总量",
      "text2": "按表字段",
      "text3": "校验内容",
      "error": "错误阈值",
      "text4": "校验字段",
      "count": "formItems.thresholdUnit === '个'",
      "addDataItem": "`根据所选校验数据，将会新增${ tableData.length }条校验任务，请核对`",
      "textText": "输入框输入范围为1~1000的正整数",
      "select": "选择校验数据",
      "select1": "至少选择一张表",
      "text5": "表总量",
      "text6": "表字段",
      "text7": "任务",
      "addDataItem1": "根据所选校验数据，将会新增${ tableData.length }条校验任务，请核对",
      "editSelectDataNode": "源数据库与目标数据库选择\"${label}\"节点重复，请修改",
      "errorDataSetting": "“按表总量校验”不设置错误阈值。即不论校验过程是否出错，均完成全量数据校验",
      "error1": "按错误个数",
      "count1": "当表字段不一致个数达到阈值时，校验任务自动停止，校验不通过",
      "list": ">校验任务列表</div>\r\n                <div class=",
      "text8": "校验结果",
      "row": "当前执行进度",
      "row1": "执行耗时",
      "success": "启动成功！",
      "select2": "请选择一条或多条规则",
      "text9": "停止校验中！",
      "text10": "所选",
      "text11": "此",
      "delete": "任务删除成功！",
      "name": "任务名称:",
      "data2": "源数据库:",
      "data3": "目标数据库:",
      "type1": "校验类型:",
      "text12": "校验内容:",
      "text13": "查看校验任务内容",
      "deleteObjText": "确定要删除${obj}校验任务",
      "deleteRemoveListRow": "删除校验任务后，任务将从列表移除，同时任务所产生的执行历史不可追溯。请谨慎操作。",
      "detail": "${fileName}-${row.startTime}-比对详情.xls",
      "list1": "校验任务列表",
      "text14": "校验任务总数:",
      "text15": "停止"
    }
  }
};