import './aFormDashboard.less';
import _ from 'lodash';
import apmQueryGroup from '../apmQueryGroup/apmQueryGroup';
import { cutZero } from '@/utils/utils';
import aInput from '@/components/common/input/aInput';
export default {
    name: 'apm-form',
    props: {
        title: {
            type: String,
            default: '筛选条件'
        },
        formItems: {
            type: Array,
            default: () => []
        },
        formCols: {
            type: Number,
            default: 3
        },
        proposalList: {
            type: Array,
            default: () => []
        }
    },
    components: {
        apmQueryGroup, aInput
    },
    data() {
        const validateValMin = (rule, value, callback) => {
            if (value === '') {
                return callback(new Error('参数不能为空'));
            } else if (!(/^[1-9]\d*$/.test(value))) {
                return callback(new Error('请输入正整数'));
            } else if (value > Number.MAX_SAFE_INTEGER) {
                return callback(new Error('输入超出最大安全整数值'));
            } else if (value > Number(this.inputForms?.end)) {
                return callback(new Error('最小值不能大于最大值'));
            } else if (Number(this.inputForms?.end) - Number(value) >= 1000) {
                return callback(new Error('范围差值不能>=1000'));
            }
            callback();
        };
        const validateValMax = (rule, value, callback) => {
            if (value === '') {
                return callback(new Error('参数不能为空'));
            } else if (!(/^[1-9]\d*$/.test(value))) {
                return callback(new Error('请输入正整数'));
            } else if (value > Number.MAX_SAFE_INTEGER) {
                return callback(new Error('输入超出最大安全整数值'));
            } else if (value < Number(this.inputForms?.begin)) {
                return callback(new Error('最大值不能小于最小值'));
            } else if (Number(value) - Number(this.inputForms?.begin) >= 1000) {
                return callback(new Error('范围差值不能>=1000'));
            }
            callback();
        };
        return {
            forms: {},
            queryFieldCondition: 'gt',
            beforeFiledValue: 0,
            unit: '2',
            columns: [
                {
                    title: '周期',
                    key: 'date',
                    width: 110
                },
                {
                    title: '最小值',
                    key: 'min',
                    width: 100,
                    render: (h, params) => {
                        return this.dynamicTable(h, params);
                    }
                },
                {
                    title: '平均数',
                    key: 'avg',
                    width: 100,
                    render: (h, params) => {
                        return this.dynamicTable(h, params);
                    }
                },
                {
                    title: '中位数',
                    key: 'p50',
                    width: 100,
                    render: (h, params) => {
                        return this.dynamicTable(h, params);
                    }
                },
                {
                    title: '最大值',
                    key: 'max',
                    width: 100,
                    render: (h, params) => {
                        return this.dynamicTable(h, params);
                    }
                }
            ],
            proposalDict: {
                yesterday: '产品昨日时延',
                week: '产品上周时延',
                month: '产品上月时延'
            },
            customModeObj: {},
            customInputKey: 1,
            customValidate: {},
            inputForms: {},
            aInputMinValidate: [{ test: validateValMin, trigger: 'blur' }],
            aInputMaxValidate: [{ test: validateValMax, trigger: 'blur' }]
        };
    },
    computed: {
        // 时延筛选数据建议表格数据
        proposalTableList: function() {
            const list = [];
            this.proposalList.forEach(ele => {
                const multiple = Math.pow(1000, 3 - this.unit);
                const param = {
                    date: this.proposalDict[ele.date]
                };
                ['avg', 'min', 'p50', 'max'].forEach(item => {
                    param[item] = cutZero((ele[item] / multiple || 0).toFixed(3));
                });
                list.push(param);
            });
            return list;
        },
        // 时延筛选数据建议表格title
        proposalTitle: function() {
            const list = ['s', 'ms', 'μs', 'ns'];
            return '时延指标数据建议(单位:' + list[this.unit] + ')';
        },
        // 类型是queryGroup-组合查询组件的key值集合
        allQueryGroupKeys() {
            return this.formItems.reduce((pre, item) => {
                if (item.type === 'queryGroup') {
                    pre.push(item.key);
                }
                return pre;
            }, []);
        }
    },
    mounted() {
        this.init();
    },
    methods: {
        init() {
            this.formItems.forEach(ele => {
                if (ele.type === 'queryGroup') {
                    this.$refs[ele.key] && this.$refs[ele.key].reset();
                } else if (ele.key === 'queryType') {
                    const defaultVal = ele?.options?.[0]?.value;
                    this.$set(this.forms, [ele.key], defaultVal);
                    this.handleCustomMode(defaultVal);
                } else {
                    this.$set(this.forms, ele.key, _.cloneDeep(ele.value));
                }
            });
        },
        // 回显数据
        echoFormData(param){
            if (!Object.keys(param)?.length) return;
            Object.keys(param).forEach(key => {
                if (key === 'startDate' || key === 'endDate') return;
                if (key === 'unit'){
                    this.unit = param.unit;
                    return;
                }
                if (key === 'queryFieldCondition'){
                    this.queryFieldCondition = param.queryFieldCondition;
                    return;
                }
                if (key === 'beforeFiledValue'){
                    this.beforeFiledValue = param.beforeFiledValue;
                    return;
                }
                this.$set(this.forms, key, _.cloneDeep(param[key]));
            });
        },
        // 重置查询数据
        reset() {
            this.$refs['formValidate'].resetFields();
            this.queryFieldCondition = 'gt';
            this.beforeFiledValue = 0;
            this.unit = '2';
            this.handleCustomDefault();
        },
        // 点击查询
        query() {
            const aInputValid = this.handleCustomInputValid();
            // 处理自定义模式查询输入框校验
            let isQuery = false, queryData = {};
            this.$refs['formValidate'].validate(valid => {
                if (valid) {
                    const param = _.find(this.formItems, { type: 'timescreen' }) ? {
                        queryFieldCondition: this.queryFieldCondition,
                        beforeFiledValue: this.beforeFiledValue,
                        unit: this.unit
                    } : {};
                    queryData = { ...this.forms, ...param };
                    isQuery = true;
                }
            });
            Object.keys(this.customModeObj)?.length && (queryData = { ...queryData, ...this.inputForms, aInputValid });
            return isQuery ? this.faltenQueryGroup(queryData) : false;
        },
        // 扁平组合查询的查询数据
        faltenQueryGroup(data) {
            Object.keys(data).forEach(key => {
                if (this.allQueryGroupKeys.includes(key)) {
                    const groupValue = data[key] || {};
                    // eslint-disable-next-line no-param-reassign
                    data = {
                        ...data,
                        ...groupValue
                    };
                    data[key] = undefined;
                }
            });
            return data;
        },
        // 时延筛选数值框失去焦点
        fieldValueBlur() {
            this.beforeFiledValue = this.beforeFiledValue ? isNaN(this.beforeFiledValue) ? 0 : parseFloat(this.beforeFiledValue) : 0;
        },
        // 查询时延筛选参照数据
        queryProposal(span) {
            span && this.$emit('queryProposal', span);
        },
        // 自定义查询方式，下拉选项变化
        handleCustomMode(val) {
            this.handleCustomDefault();
            this.customInputKey++;
            const [selectVal, domNum, domType] = val?.split(':');
            this.customModeObj = {
                selectVal, domNum, domType
            };
        },
        // 设置自定义数量输入框默认值和校验值
        handleCustomDefault() {
            this.inputForms = {
                count: '1000'
            };
            this.customValidate = {
                inputKey: true
            };
        },
        // 自定义查询方式, input数据变化
        handleValueEdit(param) {
            const { event, key, keyVal, err } = param;
            if (err) {
                this.customValidate[key] = false;
            } else {
                this.customValidate[key] = true;
            }
            if (key === 'beginInputkey' || key === 'endInputkey') {
                this.$refs['aInputMin'].setValidate();
                this.$refs['aInputMax'].setValidate();
                this.$nextTick(() => {
                    this.customValidate['beginInputkey'] = this.$refs['aInputMin'].getValidateStatus();
                    this.customValidate['endInputkey'] = this.$refs['aInputMax'].getValidateStatus();
                });
            }
            this.inputForms[keyVal] = event.target._value;
        },
        handleCustomInputValid() {
            let valid = false;
            if (this.customModeObj?.domNum === '1') {
                valid = (this.customValidate['inputKey']);
            } else if (this.customModeObj?.domNum === '2') {
                valid = (this.customValidate['beginInputkey'] && this.customValidate['endInputkey']);
            }
            return valid;
        },
        // 监听下拉框变化
        handleSelectChange(key, val) {
            this.$emit('handleSelectChange', key, val);
        },
        // 动态渲染表格数据
        dynamicTable(h, param) {
            return h(
                'div',
                {
                    class: 'td-time',
                    on: {
                        click: () => {
                            this.beforeFiledValue = isNaN(param.row[param.column.key]) ? 0 : cutZero(Number(param.row[param.column.key]).toFixed(1));
                        }
                    }
                },
                cutZero(Number(param.row[param.column.key]).toFixed(1))
            );
        },
        // 处理input图标点击
        handleInputIconClick(icon, key) {
            // clearable需要聚焦才展示,可通过图标方式展示无需聚焦也展示清除图标
            icon === 'close' && (this.forms[key] = '');
        },
        // 设置某项formitem值
        handleSetItemVal(key, val) {
            this.forms[key] = val;
        }
    },
    render() {
        // 生成输入框
        const generateInput = (item) => {
            return (
                <h-input
                    v-model={this.forms[item.key]}
                    placeholder={item.placeholder || '请输入'}
                    icon={item.icon}
                    clearable={item.clearable}
                    v-on:on-click={() => this.handleInputIconClick(item.icon, item.key)}
                ></h-input>
            );
        };
        // 生成普通下拉框
        const generateSelect = (item) => {
            return (
                <h-select v-model={this.forms[item.key]}
                    placeholder={item.placeholder || '请选择'}
                    positionFixed={true}
                    multiple={item.multiple || false}
                    clearable={item?.clearable || false}
                    v-on:on-change={(val) => { this.handleSelectChange(item.key, val); }}
                >
                    {
                        item.options.map(opt => (
                            <h-option key={opt.value} value={opt.value}>{opt.label}</h-option>
                        ))
                    }
                </h-select>
            );
        };
        // 生成简单选择下拉框 - 虚拟滚动
        const generateSimpleSelect = (item) => {
            return (
                <h-simple-select v-model={this.forms[item.key]}
                    placeholder={item.placeholder || '请选择'}
                    multiple={item.multiple || false}
                    clearable={item?.clearable || false}
                    filterable={item.filterable || false}
                    filterMethod={item.filterMethod || undefined}
                    v-on:on-change={(val) => { this.handleSelectChange(item.key, val); }}
                >
                    <h-select-block data={item.optionData}></h-select-block>
                </h-simple-select>
            );
        };
        // 生成可搜索的下拉框
        const generateSelectSearch = (item) => {
            return <h-select
                v-model={this.forms[item.key]}
                filterable
                placeholder={item.placeholder || '请选择'}
                positionFixed={true}
                loading={item.loading}
                remote
                remote-method={item.remoteMethod}
                remoteIcon="search"
                loading-text="加载中...">
                {
                    item.options.map(opt => { return <h-option key={opt.value} value={opt.value}>{opt.label}</h-option>; })
                }
            </h-select>;
        };
        // 生成日期筛选框
        const generateDate = (item) => {
            return <h-datePicker
                type="date"
                placeholder="选择日期"
                v-model={this.forms[item.key]}
                positionFixed={true}
                editable={item?.editable || false}
                placement={item?.placement || 'bottom-start'}
                clearable={item?.clearable}
            ></h-datePicker>;
        };
        // 生成日期范围选择框
        const generateDaterange = (item) => {
            return <h-date-picker
                type="daterange"
                confirm
                placement={item?.placement || 'bottom-start'}
                editable={item?.editable || false}
                placeholder="选择日期"
                positionFixed={true}
                v-model={this.forms[item.key]}
                clearable={item?.clearable}
            ></h-date-picker>;
        };
        // 生成时间选择
        const generateDateTime = (item) => {
            return <h-date-picker
                type="datetime"
                placeholder="选择日期"
                v-model={this.forms[item.key]}
                positionFixed={true}
                editable={item?.editable || false}
                placement={item?.placement || 'bottom-start'}
                clearable={item?.clearable}
            ></h-date-picker>;
        };
        // 生成时间范围选择框
        const generateTimerange = (item) => {
            return <h-time-picker
                confirm={item?.confirm ?? true}
                placement={item?.placement || 'bottom-start'}
                type="timerange"
                placeholder="选择时间"
                v-model={this.forms[item.key]}
                positionFixed={true}
                editable={item?.editable || false}
                clearable={item?.clearable}
            ></h-time-picker>;
        };
        // 生成日期事件范围
        const generateDateTimerange = (item) => {
            return <h-date-picker
                confirm
                placement={item?.placement || 'bottom-start'}
                type="datetimerange"
                placeholder="选择日期时间"
                v-model={this.forms[item.key]}
                positionFixed={true}
                editable={item?.editable || false}
                clearable={item?.clearable}
            ></h-date-picker>;
        };
        // 时延筛选
        const generateTimescreen = (item) => {
            return <div style="display: flex;">
                <h-select
                    v-model={this.forms[item.key]}
                    placeholder={item.placeholder || '请选择'}
                    positionFixed={true}
                    v-on:on-change={this.queryProposal}
                    style="padding-right: 4px;">
                    {
                        item.options.map(opt => {
                            return <h-option key={opt.value} value={opt.value}>{opt.label}</h-option>;
                        })
                    }
                </h-select>
                <h-select
                    v-model={this.queryFieldCondition}
                    positionFixed
                    clearable={false}
                    style="width: 50px; padding-right: 4px;">
                    <h-option value="gt">&gt;=</h-option>
                    <h-option value="lt">&lt;=</h-option>
                </h-select>
                <h-poptip
                    // trigger="focus"
                    placement={item.placement || 'bottom'}
                    transfer
                    title={this.proposalTitle}
                    positionFixed
                    width={545}>
                    <h-input
                        v-model={this.beforeFiledValue}
                        disabled={!this.forms[item.key]}
                        placeholder="请输入"
                        v-on:on-blur={this.fieldValueBlur}
                        style="width: 60px; top: 0px; padding-right: 4px;"></h-input>
                    <div slot="content" class="api">
                        <h-table border columns={this.columns} data={this.proposalTableList} size='small'></h-table>
                    </div>
                </h-poptip>

                <h-select
                    v-model={this.unit}
                    clearable={false}
                    positionFixed={true}
                    style="width: 50px;"
                >
                    <h-option value="0">s</h-option>
                    <h-option value="1">ms</h-option>
                    <h-option value="2">μs</h-option>
                    <h-option value="3">ns</h-option>
                </h-select>
            </div>;
        };

        // 组合组件
        const generateQueryGroup = (item) => {
            return <apm-query-group ref={item.key} v-model={this.forms[item.key]} config={{ items: item.items }} />;
        };

        // 查询方式
        const generateCustomMode = (item) => {
            return <div style="display: flex;">
                <h-select
                    v-model={this.forms[item.key]}
                    placeholder={item.placeholder || '请选择'}
                    positionFixed={true}
                    setDefSelect={true}
                    clearable={item?.clearable || false}
                    v-on:on-change={this.handleCustomMode}
                    style="padding-right: 4px; max-width: 150px">
                    {
                        item.options.map(opt => {
                            return <h-option
                                key={opt.value}
                                value={opt.value}
                                conditionType={opt.conditionType}>
                                {opt.label}
                            </h-option>;
                        })
                    }
                </h-select>
                {
                    this.customModeObj?.domType === 'input' && this.customModeObj?.domNum === '1'
                        ? <div style="display: flex; color: var(--font-opacity-color); flex: 1;">
                            <a-input
                                key={`input1${this.customInputKey}`}
                                value={this.inputForms[item.inputkey]}
                                placeholder={item.placeholder || ''}
                                style="padding-right: 4px;"
                                icon={item.icon}
                                required={item.required}
                                validRules={item.singleInputValidRules}
                                on-on-blur={ (event, err) => { this.handleValueEdit({ event, key: 'inputKey', keyVal: item.inputkey, err }); }}
                            ></a-input>
                            <span>条</span>
                        </div> : ''
                }
                {
                    this.customModeObj?.domType === 'input' && this.customModeObj?.domNum === '2'
                        ? <div style="display: flex; color: var(--font-opacity-color); flex: 1;">
                            <a-input
                                ref="aInputMin"
                                key={`input1${this.customInputKey}`}
                                value={this.inputForms[item.beginInputkey]}
                                placeholder={item.placeholder || ''}
                                style="padding-right: 4px;"
                                icon={item.icon}
                                required={item.required}
                                validRules={this.aInputMinValidate}
                                on-on-blur={ (event, err) => { this.handleValueEdit({ event, key: 'beginInputkey', keyVal: item.beginInputkey, err }); }}
                            ></a-input>
                            <span style="margin: 0 5px 0 0; font-size: 16px;">~</span>
                            <a-input
                                ref="aInputMax"
                                key={`input2${this.customInputKey}`}
                                value={this.inputForms[item.endInputkey]}
                                placeholder={item.placeholder || ''}
                                style="padding-right: 4px;"
                                icon={item.icon}
                                required={item.required}
                                validRules={this.aInputMaxValidate}
                                on-on-blur={ (event, err) => { this.handleValueEdit({ event, key: 'endInputkey', keyVal: item.endInputkey, err }); }}
                            ></a-input>
                        </div> : ''
                }
            </div>;
        };

        const inputGenerators = {
            input: generateInput,
            select: generateSelect,
            simpleSelect: generateSimpleSelect,
            selectSearch: generateSelectSearch,
            date: generateDate,
            daterange: generateDaterange,
            dateTime: generateDateTime,
            timerange: generateTimerange,
            datetimerange: generateDateTimerange,
            timescreen: generateTimescreen,
            queryGroup: generateQueryGroup,
            customMode: generateCustomMode
        };

        return <div class="best-form">
            <div class="form-title">
                <div class="form-title-text">
                    {this.title}
                </div>
                <div class="form-title-right">
                    {this.$slots.btns}
                </div>
            </div>
            <h-form
                ref="formValidate"
                props={{ model: this.forms }}
                label-width={85}
                cols={this.formCols || '3'}
            >
                {
                    this.formItems.map(item => {
                        return <h-form-item
                            label={item.label}
                            prop={item.key || ''}
                            required={item.required || false}
                            labelWidth={item?.labelWidth || undefined}
                        >
                            {(() => {
                                const generator = inputGenerators[item.type];
                                if (generator) {
                                    return generator(item);
                                } else {
                                    return '';
                                }
                            })}
                        </h-form-item>;
                    })
                }
                <style jsx>
                    {
                        `
                        .h-poptip-popper[x-placement^="bottom"] {
                            .h-poptip-arrow {
                                border-bottom-color: #fff !important;

                                &::after {
                                    border-bottom-color: #fff !important;
                                }
                            }
                        }
                    `
                    }
                </style>
                <input type="text" hidden />
            </h-form>
        </div>;
    }
};
