<template>
    <div class="main">
        <header>
            <a-title title="上下文运行观测">
                <slot>
                    <div class="slot">
                        <div class="ctx-search">
                            <apm-select-search
                                ref="ctxSearch"
                                remoteMode
                                :width="ctxSearchWidth"
                                :maxWidth="ctxSearchMaxWidth"
                                clearable
                                focusWidth="440px"
                                labelInValue
                                :remoteMethod="onFetchCtx"
                                placeholder="搜索上下文"
                                minWidth="182px"
                                :useFuzzy="true"
                                :border="false"
                                @onChange="onChangeCtx"
                            />
                        </div>
                        <h-select
                            v-show="productList.length > 1"
                            v-model="productInstNo"
                            class="title-single-select"
                            placeholder="请选择"
                            :positionFixed="true"
                            :clearable="false"
                            @on-change="checkProduct">
                            <h-option
                                v-for="item in productList"
                                :key="item.id"
                                :value="item.productInstNo"
                                >{{ item.productName }}</h-option>
                        </h-select>
                        <span ref="fake-ctx-placeholder" class="fake-ctx-placeholder">{{tabCtxTitle}}</span>
                    </div>
                </slot>
            </a-title>
            <div class="title-ctx-box">
                <h-icon
                    v-if="contextName"
                    slot="extra"
                    name="navicon-round"
                    color="var(--font-color)"
                    size="18"
                    class="title-ctx-icon"
                    @on-click="handleDrawerVisable"
                ></h-icon>
            </div>
        </header>
        <div class="tab-box">
            <h-tabs
                v-model="tabName"
                @on-click="handleTabChange(tabName)">
                <h-tab-pane
                    v-for="item in tabList"
                    :key="item"
                    :label="item"
                    :name="item">
                    <component
                        :is="item"
                        :ref="item"
                        :rcmInfo="rcmInfo" />
                </h-tab-pane>
            </h-tabs>
        </div>
        <a-loading v-if="loading" style="width: 100%; height: calc(100% - 93px); top: 92px;"></a-loading>
        <h-drawer
            v-model="visable"
            width="480"
            :closable="false">
            <template v-slot:header>
                <p :title="tabCtxTitle">{{ tabCtxTitle }}</p>
            </template>
            <json-viewer
                class="json-drawer"
                :value="rcmInfo"
                :expand-depth="10"
                expanded
                copyable>
                <template v-slot:copy="{copied}">
                    <span v-if="copied">复制成功</span>
                    <span v-else>复制</span>
                </template>
            </json-viewer>
        </h-drawer>
    </div>
</template>
<script>
import _ from 'lodash';
import { mapState, mapActions } from 'vuex';
import jsonViewer from 'vue-json-viewer';
import aLoading from '@/components/common/loading/aLoading';
import aTitle from '@/components/common/title/aTitle';
import Transmitters from '@/components/rcmObservation/transmitters';
import Receiver from '@/components/rcmObservation/receiver';
import Memory from '@/components/rcmObservation/memory';
import Deliver from '@/components/rcmObservation/deliver';
import apmSelectSearch from '@/components/common/apmSelectSearch/apmSelectSearch';
import { getCtxRunningInfo } from '@/api/rcmApi';
import { getContextRelationList } from '@/api/productApi';
export default {
    components: { aTitle, aLoading, Transmitters, Receiver, Memory, Deliver, jsonViewer, apmSelectSearch },
    data() {
        return {
            productInstNo: '',
            contextName: '',
            loading: true, // tab loading
            loading2: false, // 搜索loading
            tabName: 'Transmitters',
            tabList: ['Transmitters', 'Receiver', 'Deliver', 'Memory'],
            showCtxs: [],
            nodeList: [],
            timer: null,
            visable: false,
            isFirstRender: true,
            drawerTitle: '',
            rcmInfo: {},
            resizeKey: null,
            ctxSearchWidth: 'auto' // 上下文搜索的真实宽度
        };
    },
    async mounted() {
        await this.getProductList({ filter: 'supportRcm' });
        if (!this.productList.length) {
            this.$hMessage.error('请先去建立产品');
            this.loading = false;
            this.loading2 = false;
            return;
        }
        const productInstNo = localStorage.getItem('productInstNo');
        this.productInstNo =  _.find(this.productList, ['productInstNo', productInstNo])?.productInstNo ||
            this.productList?.[0]?.productInstNo;
        const { query } = this.$route;
        if (query?.rcmId && query?.contextName) {
            this.contextName = `${query.rcmId}@${query.contextName}`;
            this.$refs['ctxSearch'].setValue(query.rcmId, `${query.rcmName} / ${query.contextName}`);
            this.handleRcmChange();
        }
        window.addEventListener('resize', this.onResize);
    },
    computed: {
        ...mapState({
            productList: state => {
                return state.product.productListLight || [];
            }
        }),
        tabCtxTitle: function() {
            if (!this.contextName) return '';
            const list = this.contextName.split('@');
            if (list[0]) {
                const rcmName = _.find(this.nodeList, ['rcmId', list[0]])?.rcmName;
                return `${rcmName} / ${list[1]}`;
            }
            return '';
        },
        /**
         * 上下文输入框的最大宽度，根据屏幕宽度计算，405px是左右元素的固定宽度
         * resizeKey 只是为了在改变窗口大小时触发computed
         */
        ctxSearchMaxWidth() {
            if (!this.contextName || this.resizeKey === undefined) return 'auto';
            const width = window.innerWidth;
            return `calc(${width}px - 405px)`;
        }
    },
    watch: {
        contextName() {
            if (!this.contextName) {
                this.ctxSearchWidth = '100%';
                return;
            }
            if (this.$refs['fake-ctx-placeholder']) {
                this.$nextTick(() => {
                    const realCtxWidth = this.$refs['fake-ctx-placeholder'].getBoundingClientRect().width;
                    // 动态容器宽度，53是容器内的padding值，文字宽度 + 53 = 容器宽度，达到动态宽度效果
                    this.ctxSearchWidth = `${realCtxWidth + 53}px`;
                });
            }
            // 兜底值，一般不会发生。
            this.ctxSearchWidth  = (this.tabCtxTitle.length * 7.5) + 'px';
        }
    },
    methods: {
        async init() {
            this.loading = true;
            !this.isFirstRender && this.clearData();
            try {
                this.nodeList = await this.getContextRelationList();
                this.setPolling(10);
            } catch (error) {
                console.error(error);
            } finally {
                this.loading = false;
                this.isFirstRender = false;
            }
        },
        ...mapActions({ getProductList: 'product/getProductListLight' }),
        // 切换产品
        async checkProduct(e) {
            if (this.productList.length) {
                this.productInfo = e ? _.find(this.productList, ['productInstNo', e]) : this.productList[0];
                this.productInstNo = this.productInfo.productInstNo;
                localStorage.setItem('productInstNo', this.productInfo.productInstNo);
                !this.isFirstRender && this.$refs.ctxSearch.reset();
                await this.init();
                this.handleTabChange('Transmitters');
            }
        },
        handleTabChange(tabName) {
            this.$nextTick(async () => {
                this.$refs?.[tabName]?.[0] && await this.$refs[tabName][0].initData();
            });
        },
        // 获取RCM上下文列表
        async getContextRelationList() {
            const res = await getContextRelationList({
                productId: this.productInstNo
            });
            return res?.data || [];
        },
        // 获取RCM配置信息
        async getCtxRunningInfo(name, callback) {
            if (!this.contextName) {
                this.rcmInfo = {};
                return;
            };
            const list = this.contextName.split('@');
            try {
                const res = await getCtxRunningInfo({
                    rcmId: list[0],
                    name: list[1],
                    productInstNo: this.productInstNo
                });
                if (res.success) {
                    this.rcmInfo = res?.data || {};
                } else {
                    this.clearPolling();
                }
            } catch (error) {
                this.clearPolling();
            }
        },
        // 切换rcm节点
        async handleRcmChange(e) {
            this.loading = true;
            await this.getCtxRunningInfo();
            this.handleTabChange(this.tabName);
            this.loading = false;
        },
        onChangeCtx({ label, rcmId }) {
            const targetLabel = label ? label.split(' / ')[1] : '';
            this.contextName =  rcmId ? `${rcmId}@${targetLabel}` : '';
            this.$nextTick(this.handleRcmChange);
        },
        /**
         * 模拟远程搜搜上下文
         */
        onFetchCtx(query) {
            return new Promise(resolve => {
                setTimeout(() => {
                    if (!query) {
                        resolve(this.nodeList.map(item => ({ ...item, label: `${item.rcmName} / ${item.contextName}`, value: `${item.rcmId} / ${item.rcmName} / ${item.contextName}` })));
                    } else {
                        resolve(this.nodeList.reduce((pre, item) => {
                            const temp = [...pre];
                            const label = `${item.rcmName} / ${item.contextName}`;
                            if (label.toLowerCase().includes(query.toLowerCase())) {
                                temp.push({ ...item, label, value: `${item.rcmId} / ${item.rcmName} / ${item.contextName}` });
                            }
                            return temp;
                        }, []));
                    }
                }, 200);
            });
        },
        onResize: _.throttle(function () {
            this.resizeKey = Date.now();
        }, 500),
        // 打开侧边栏
        handleDrawerVisable() {
            this.visable = true;
        },
        // 清理数据
        clearData() {
            this.rcmInfo = {};
            this.showCtxs = [];
            this.nodeList = [];
            this.contextName = '';
        },
        // 定时器
        setPolling(timerInterval){
            this.clearPolling();
            this.timer = setInterval(async () => {
                await this.getCtxRunningInfo();
                this.$refs?.[this.tabName]?.[0] && this.$refs[this.tabName][0].getFileData();
            }, timerInterval * 1000);
        },
        // 清楚定时器
        clearPolling() {
            this.timer && clearInterval(this.timer);
        }
    },
    beforeDestroy(){
        this.clearPolling();
        window.removeEventListener('resize', this.onResize);
    }
};
</script>
<style lang="less" scoped>
@import url("@/assets/css/input.less");
@import url("@/assets/css/tab.less");

.slot {
    flex: 1;
    display: flex;
    justify-content: space-between;

    .ctx-search {
        margin-right: 15px;
        // flex: 1;
        display: flex;
        align-items: center;

        &::before {
            content: "";
            width: 1px;
            background-color: #474e6f;
            height: 26px;
            display: block;
            margin-left: 15px;
            margin-right: 5px;
        }
    }
}

/deep/ .apm-title {
    display: flex;
    align-items: center;

    .fake-ctx-placeholder {
        position: absolute;
        top: 120px;
        left: 0;
        z-index: -1;
        font-size: 12px;
        opacity: 0;
    }

    .title-single-select {
        position: relative;
        top: 0;
    }

    .text {
        min-width: 105px;
    }

    &::before {
        min-width: 5px;
        top: -1px;
    }
}

.title-ctx-box {
    display: flex;
    justify-content: space-between;
    position: absolute;
    right: 20px;
    top: 55px;
    z-index: 2;
}

.title-ctx-select {
    width: 200px;
    display: inline-block;
}

.title-ctx-icon {
    width: 20px;
    margin-left: 4px;
    z-index: 1;
    cursor: pointer;
}

.tab-extra-span {
    display: inline-block;
    width: calc(100% - 230px);
    position: relative;
    top: 6px;
    color: var(--warning-color);
    white-space: nowrap;           /* 强制文本在一行内显示 */
    overflow: hidden;              /* 隐藏超出容器的内容 */
    text-overflow: ellipsis;       /* 当内容溢出时显示省略号 */
}

.tab-box {
    width: 100%;
    height: calc(100% - 45px);

    /deep/.h-tabs {
        height: 100%;
        background: var(--main-color);
    }

    /deep/ .h-tabs-nav-container {
        height: 42px;
    }

    /deep/ .h-tabs-bar {
        margin-bottom: 0;
        border-bottom: var(--border);
    }

    /deep/ .h-tabs-tab {
        padding: 10px 6px;
    }

    /deep/.h-tabs .h-tabs-content-wrap {
        height: calc(100% - 33px);
        overflow: hidden;
    }

    /deep/.h-tabs .h-tabs-tabpane {
        height: calc(100% - 10px);
        cursor: pointer;
        overflow: auto;
    }

    /deep/.h-tabs .h-tabs-content {
        height: 100%;
    }

    /deep/ .h-tabs-nav-wrap {
        width: 100%;
    }

    /deep/ .h-tabs-nav-right {
        width: calc(100% - 330px);
    }
}

.json-drawer {
    height: 100%;
    overflow: auto;

    /deep/ .jv-code {
        height: 100%;
    }
}

.h-drawer-header {
    p {
        width: 100%;
    }
}

/deep/ .h-drawer-content {
    background: #fff;
}

/deep/ .h-drawer-header {
    border-bottom: 1px solid #f7f7f7;
    padding: 14px 16px;
    line-height: 1;
}
</style>
