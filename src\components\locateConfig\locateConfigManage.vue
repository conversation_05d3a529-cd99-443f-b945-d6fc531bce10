<template>
    <div class="loacte-config-manage">
        <!-- 定位节点配置列表 -->
        <div class="locate-table" :style="[{height: visable ? '227px' : '0px'}]">
           <obs-table
            ref="table1"
            height="150"
            border
            showTitle
            :title="obsTitle"
            :tableData="tableData"
            :columns="columns"
            :hasPage="false"
            :loading="loading"
            @select-change="tableSelectChange"
        />
        </div>
        <h-row>
            <div class="x-resizer"></div>
            <span class="cate-switch" @click="changeShowHidden">
                <h-icon :name="visable ? 'packup' : 'unfold'" size='14' color="#cacfd4"></h-icon>
            </span>
        </h-row>
        <!-- 配置信息 -->
        <div class="locate-box">
            <h-select v-model="locateType" :clearable="false" :disabled="!curRowId || dataLoading" :positionFixed="true" @on-change="handleLocateChange">
                <h-option value="local">local</h-option>
                <h-option v-show="selectlocateValue !== 'locate_config' && curRowMode !== 'mc'" value="zookeeper">zookeeper</h-option>
            </h-select>
            <div class="locate-box-path" :title="localPath">{{ localPath }}</div>
            <div class="locate-box-format">配置文件格式：{{ localLanguage || '-' }}</div>
        </div>
        <div class="loacte-editor">
            <h-spin v-if="dataLoading" fix>
                <h-icon name="load-c" size="18" class="demo-spin-icon-load"></h-icon>
                <div>加载中</div>
            </h-spin>
            <div class="loacte-editor-code">
                <monaco-code-editor ref="monaco-code-editor"
                :value="configContext"
                :language="localLanguage"/>
            </div>
            <div class="loacte-editor-btn">
                <a-button type="dark" size="small" :disabled="!curRowId" @click="saveConfigModal">保存</a-button>
                <a-button v-if="locateType !== 'zookeeper'" type="dark" size="small" :disabled="!curRowId" @click="syncConfigModal">同步</a-button>
            </div>
        </div>
         <!-- 配置比对 -->
        <select-diff-modal
            v-if="selectDiffModal.status"
            :selectDiffModal="selectDiffModal"
            :productId="productId"
            @start-diff="callStartSelectDiff"
        />
         <!-- 保存 -->
         <save-config-modal v-if="saveConfigInfo.status" :modalInfo="saveConfigInfo" @msgbox-config="saveConfig"></save-config-modal>
         <!-- 同步  确认打开新的结果弹窗 -->
         <sync-config-modal v-if="syncConfigInfo.status" :modalInfo="syncConfigInfo" @msgbox-config="syncConfigResultModal"></sync-config-modal>
         <!-- 同步  同步进度表 -->
         <terminate-requests-modal v-if="syncConfigResultInfo.status" :modalInfo="syncConfigResultInfo" @refresh="syncConfigDiffReview"></terminate-requests-modal>
    </div>
</template>

<script>
import aButton from '@/components/common/button/aButton';
import obsTable from '@/components/common/obsTable/obsTable';
import MonacoCodeEditor from './MonacoCodeEditor.vue';
import saveConfigModal from '@/components/locateConfig/modal/saveConfigModal';
import syncConfigModal from '@/components/locateConfig/modal/syncConfigModal';
import terminateRequestsModal from '@/components/locateConfig/modal/terminateRequestsModal';
import selectDiffModal from '@/components/locateConfig/selectDiffModal';
import { getConfigLocateCompareEdit, getConfigAppLocate, getConfigLocateDetail } from '@/api/locateApi';
import { LOCATETYPE, LOCATEOWNERTYPE } from './constant';
import { getByteSize } from '@/utils/utils';
export default {
    name: 'LocateConfigManage',
    props: {
        productId: {
            type: String,
            default: ''
        }
    },
    data() {
        return {
            visable: true,
            loading: false,
            dataLoading: false,
            btnLoading: false,
            obsTitle: {
                label: '定位节点配置列表',
                slots: [
                    {
                        key: 'funcSelect',
                        type: 'select',
                        options: [
                            {
                                value: 'locate_config',
                                label: '节点定位插件配置'
                            },
                            {
                                value: 'locate_rules',
                                label: '节点定位规则配置'
                            }
                        ]
                    }
                ]
            },
            columns: [
                {
                    title: '应用节点名',
                    key: 'appInstanceName',
                    ellipsis: true
                },
                {
                    title: '开发平台',
                    key: 'platform',
                    ellipsis: true
                },
                {
                    title: '配置对象类型',
                    key: 'ownerType',
                    ellipsis: true,
                    render: (h, params) => {
                        const type = LOCATEOWNERTYPE.find(o => o.value ===  params?.row?.ownerType)?.label;
                        return h('span', {
                            attrs: {
                                title: type
                            }
                        }, type);
                    }
                },
                {
                    title: '配置对象',
                    key: 'ownerInstance',
                    ellipsis: true
                },
                {
                    title: '配置文件类型',
                    key: 'configType',
                    ellipsis: true,
                    render: (h, params) => {
                        const type = LOCATETYPE.find(o => o.value ===  params?.row?.configType)?.label;
                        return h('span', {
                            attrs: {
                                title: type
                            }
                        }, type);
                    }
                },
                {
                    title: 'MD5',
                    key: 'md5',
                    ellipsis: true,
                    render: (h, params) => {
                        return h('span', {
                            attrs: {
                                title: params?.row?.md5 || '-'
                            }
                        }, params?.row?.md5 || '-');
                    }
                },
                {
                    title: '节点状态',
                    key: 'hasAlive',
                    ellipsis: true,
                    render: (h, params) => {
                        const type = params?.row?.hasAlive ? '在线' : '离线';
                        return h('span', {
                            attrs: {
                                title: type
                            }
                        }, type);
                    }
                },
                {
                    title: '操作',
                    key: 'action',
                    fixed: 'right',
                    width: 150,
                    render: (h, params) => {
                        return h('div', [
                            h(
                                'Button',
                                {
                                    props: {
                                        type: 'text',
                                        size: 'small'
                                    },
                                    on: {
                                        click: () => {
                                            this.onSelectView(params.row);
                                        }
                                    }
                                },
                                '查看'
                            ),
                            h(
                                'Button',
                                {
                                    props: {
                                        type: 'text',
                                        size: 'small'
                                    },
                                    on: {
                                        click: () => {
                                            this.onSelectDiff(params.row);
                                        }
                                    }
                                },
                                '对比配置'
                            )
                        ]);
                    }
                }
            ],
            tableData: [],
            selectlocateValue: '',
            // 获取节点定位配置详情
            locateType: 'local',
            localPath: '',
            localLanguage: '',
            configContext: '',

            selectDiffModal: {
                status: false
            },
            configSourceType: {
                locate_config: 'local',
                locate_rules: 'zookeeper'
            },
            saveConfigInfo: {
                status: false
            },
            syncConfigInfo: {
                status: false
            },
            syncConfigResultInfo: {
                status: false
            },
            // 当前选中行
            curRowId: '',
            curRowMode: '',
            // 当前选中行细节
            curRowDetail: {}
        };
    },
    watch: {
        productId() {
            this.curRowId = '';
            this.curRowMode = '';
            this.visable = true;
            this.clearData();
            this.clearDetailData();
        }
    },
    methods: {
        // 列表显示隐藏
        changeShowHidden() {
            this.visable = !this.visable;
        },
        // 初始化参数
        initData(){
            this.loading = true;
            this.clearData();
            this.clearDetailData();
            this.$nextTick(() => {
                this.$refs['table1'].setSelectVal('funcSelect', this.selectlocateValue || 'locate_config');
            });
            this.loading = false;
        },
        // 清空数据
        clearData(){
            this.tableData = [];
        },
        // 清空节点定位配置详情
        clearDetailData(){
            this.locateType = 'local';
            this.localPath = '';
            this.localLanguage = '';
            this.configContext = '';
            this.curRowDetail = {};
        },
        // ----------------------------------------------------  接口 ---------------------------------------------//
        // 获取节点列表
        async getInstances(configType) {
            this.loading = true;
            const param = {
                productId: this.productId,
                configType: configType
            };
            try {
                const res = await getConfigAppLocate(param);
                if (res.code === '200'){
                    this.tableData = res?.data || [];
                } else {
                    this.tableData = [];
                }
            } catch (e){
                this.tableData = [];
            } finally {
                this.loading = false;
            }
        },
        // 获取节点定位配置详情
        async getConfigLocateDetail(){
            this.dataLoading = true;
            const param = {
                id: this.curRowId,
                configSourceType: this.locateType,
                mode: this.curRowMode
            };
            try {
                const res = await getConfigLocateDetail(param);
                if (res.code === '200'){
                    this.curRowDetail = res?.data || {};
                }
            } catch (e){
                this.curRowDetail = {};
            } finally {
                this.dataLoading = false;
            }
        },
        // -----------------------------------------------------切换------------------------------------------------//
        // 表格下拉框切换
        async tableSelectChange(value) {
            this.clearDetailData();
            this.selectlocateValue = value;
            await this.getInstances(value);

            // 默认展示之前选中的行  不存在就展示第一条
            this.curRowId = this.tableData.filter(o => o?.id === this.curRowId)?.[0]?.id || this.tableData?.[0]?.id || '';
            this.curRowMode = this.tableData.filter(o => o?.id === this.curRowId)?.[0]?.mode || this.tableData?.[0]?.mode || '';
            this.curRowId && this.handleLocateChange();
        },
        // loacte切换
        async handleLocateChange(val){
            if (!this.curRowId) return;
            this.localPath = '';
            this.localLanguage = '';
            this.configContext = '';
            this.curRowDetail = {};
            await this.getConfigLocateDetail();
            this.localPath = (this.curRowDetail?.appInstanceName || '') + '    -   ' + (this.curRowDetail?.storagePath || '');
            this.localLanguage = this.curRowDetail?.contextType;
            this.configContext = this.localLanguage === 'json' ? JSON.stringify(JSON.parse(this.curRowDetail?.context || '{}'), null, 4) : this.curRowDetail?.context;
        },
        // 查看
        onSelectView(params){
            this.clearDetailData();
            this.curRowId = params?.id;
            this.curRowMode = params?.mode;
            this.locateType = 'local';
            this.handleLocateChange();
        },
        // ------------------------------------------------------ 弹窗 ---------------------------------------------//
        // 打开配置对比
        onSelectDiff(params) {
            this.selectDiffModal.status = true;
            this.selectDiffModal.defaultInstanceId = params.appInstanceId;
            this.selectDiffModal.type = this.selectlocateValue;
            this.selectDiffModal.mode = params?.mode;
            this.selectDiffModal.instances = [...this.tableData];
        },
        // 对比跳转
        callStartSelectDiff(param){
            this.$emit('diff-view', param);
        },
        // 保存
        saveConfigModal(){
            if (getByteSize(this.$refs['monaco-code-editor']?.getContent() || '') > 16 * 1024 * 1024){
                this.$hMessage.warning('文件大小超出16MB');
                return;
            }
            this.saveConfigInfo.status = true;
            this.saveConfigInfo.type = this.selectlocateValue;
            this.saveConfigInfo.locateType = this.locateType;
            this.saveConfigInfo.appInstanceName = this.curRowDetail?.appInstanceName;
            this.saveConfigInfo.configType = LOCATETYPE.find(o => o.value === this.curRowDetail?.configType)?.label;
        },
        // 保存接口
        async saveConfig(){
            const res = await getConfigLocateCompareEdit({
                configSourceType: this.locateType,
                id: this.curRowDetail?.id,
                configContext: this.$refs['monaco-code-editor']?.getContent() || ''
            });
            if (res?.code === '200') {
                this.$hMessage.success('保存成功');
                // 重新刷新页面
                await this.getInstances(this.selectlocateValue);
                await this.handleLocateChange();
            } else if (res?.code?.length === 8){
                this.$hMessage.error({
                    content: res?.message || '保存失败',
                    duration: 2.5
                });
            }
        },
        // 同步对比结果
        syncConfigModal(){
            if (getByteSize(this.$refs['monaco-code-editor']?.getContent() || '') > 16 * 1024 * 1024){
                this.$hMessage.warning('文件大小超出16MB');
                return;
            }
            this.syncConfigInfo.status = true;
            this.syncConfigInfo.type = this.selectlocateValue;
            this.syncConfigInfo.configType =  LOCATETYPE.find(o => o.value === this.curRowDetail?.configType)?.label;
            this.syncConfigInfo.appInstanceName =  this.curRowDetail?.appInstanceName;
            this.syncConfigInfo.compareAppInstanceName = (this.tableData.filter(o => o?.id !== this.curRowDetail?.id) || []).map(o => o?.appInstanceName)?.join(',');
            this.syncConfigInfo.sourceNodes = [this.curRowDetail];
            this.syncConfigInfo.targetNodes = this.tableData.filter(o => o?.id !== this.curRowDetail?.id) || [];
        },
        // 打开同步更新弹窗--点击同步 默认先保存已经编辑的文档
        async syncConfigResultModal(sourceNodes, targetNodes){
            await this.saveConfig();
            this.$nextTick(() => {
                this.syncConfigResultInfo.status = true;
                this.syncConfigResultInfo.tableData = [...targetNodes].map(o => {
                    return {
                        appInstanceId: sourceNodes?.[0]?.appInstanceId,
                        appInstanceName: sourceNodes?.[0]?.appInstanceName,
                        id: sourceNodes?.[0]?.id,
                        compareId: o?.id,
                        compareAppInstanceId: o?.appInstanceId,
                        compareAppInstanceName: o?.appInstanceName,
                        // 保存接口所需数据
                        configSourceType: this.locateType,
                        configContext: this.$refs['monaco-code-editor']?.getContent() || '',
                        status: 0
                    };
                });
            });
        },
        // 配置同步
        syncConfigDiffReview(){
            this.$nextTick(async () => {
                // 重新刷新页面
                await this.getInstances(this.selectlocateValue);
                await this.handleLocateChange();
            });
        }
    },
    components: { obsTable, MonacoCodeEditor, selectDiffModal, aButton, saveConfigModal, syncConfigModal, terminateRequestsModal }
};
</script>
<style lang="less" scoped>
@import url("@/assets/css/uTable.less");

.loacte-config-manage {
    display: flex;
    flex-direction: column;
    height: 100%;
}

.locate-table {
    overflow-y: hidden;
    transition: height 1s;
}

.obs-table {
    margin-top: 10px;
    height: auto;
}

.diff-table {
    position: relative;
    margin-bottom: 15px;
    background-color: var(--wrapper-color);

    .setting-btn {
        position: absolute;
        right: 10px;
        top: 8px;
    }
}

.locate-box {
    display: flex;
    width: 100%;
    margin: 10px 0;
    padding: 0 10px;
    background: var(--primary-color);
    height: 42px;
    align-items: center;
    color: var(--font-color);

    .h-select {
        width: 120px;
        flex-shrink: 0;
    }

    &-path {
        flex: 1;
        padding: 10px;
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
    }

    &-format {
        padding: 10px;
        flex-shrink: 0;
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
        width: 150px;
        text-align: right;
    }
}

.x-resizer {
    position: absolute;
    left: 0;
    right: 0;
    top: 0;
    height: 1px;
    user-select: none;
    background-color: var(--base-color);

    &:hover {
        background-color: var(--base-color);
    }
}

.cate-switch {
    position: absolute;
    width: 44px;
    height: 12px;
    left: 50%;
    top: 2px;
    line-height: 12px;
    text-align: center;
    background: var(--base-color);
    border-radius: 0;
    z-index: 4;
    cursor: pointer;
    transform: perspective(0.5em) rotateX(-10deg);

    &:hover {
        background: rgba(209, 216, 229, 0.4);
    }
}

.loacte-editor {
    flex: 1;
    position: relative;
    overflow: auto;

    .loacte-editor-code {
        height: calc(100% - 35px);
    }

    .loacte-editor-btn {
        margin-top: 10px;
        text-align: center;

        .h-btn {
            margin: 0 10px;
        }

        .h-btn-small {
            padding: 2px 15px;
        }
    }

    .h-spin-fix {
        position: absolute;
        top: 0;
        left: 0;
        /* stylelint-disable-next-line plugin/z-index-value-constraint */
        z-index: 999;
        width: 100%;
        height: 100%;
        background-color: var(--wrapper-color);
    }
}

</style>
