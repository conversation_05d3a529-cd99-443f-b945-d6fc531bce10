/**
 * 构造菜单
 * @param {IMenu[]} menus
 */
export function generateMenu(menus) {
    return menus.reduce((pre, item) => {
        const hash = item.uri?.split('.html#')?.[1];
        if (!hash) return pre;
        item.uri = hash;
        if (item.child?.length) {
            item.child = generateMenu(item.child);
        }
        return [
            ...pre,
            item
        ];
    }, []);
}

/**
 * 获取路由hash值和参数
 */
export function getHashAndParam() {
    const { hash } = window.location;
    const [uri, query] = hash.split('?');
    const path = uri.replace('#', '');
    if (!query) {
        return {
            uri: path,
            query: null
        };
    }
    const param = {};
    const queryArg = query.split('&');
    queryArg.forEach(item => {
        const [key, value] = item.split('=');
        param[key] = value;
    });
    return {
        uri: path,
        query: param
    };
}
