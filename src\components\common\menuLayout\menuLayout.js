import './menuLayout.less';
import aButton from '@/components/common/button/aButton';
export default {
    name: 'apm-box',
    props: {
        customMenu: {
            type: Boolean,
            default: false
        },
        menuTitle: {
            type: String,
            default: undefined
        },
        // 底部button文本
        footer: {
            type: String,
            default: undefined
        },
        defaultWidth: {
            type: Number,
            default: 240
        },
        // 收起时左侧盒子最小宽度
        menuMinWidth: {
            type: Number,
            default: 0
        },
        showSearch: {
            type: Boolean,
            default: false
        },
        placeholder: {
            type: String,
            default: '查询'
        },
        // 菜单列表
        menuList: {
            type: Array,
            default: () => {
                return [];
            }
        },
        // 菜单项title所绑定的menuList的属性名
        titleAttribute: {
            type: String,
            default: ''
        },
        // 菜单项绑定id属性名
        menuItemId: {
            type: String,
            default: ''
        },
        // 菜单项图标绑定属性名
        liClassAttribute: {
            type: String,
            default: ''
        },
        productAlerts: {
            type: Array,
            default: () => {
                return [];
            }
        },
        // 模糊搜索是否启用正则
        useRegExpSearch: {
            type: Boolean,
            default: true
        }
    },
    data() {
        return {
            lastLeftX: this.defaultWidth,
            leftBarWidth: this.defaultWidth,
            iconType: false, // 0:收起, 1:展开
            startX: 0,
            searchText: '',
            menuListCopy: this.menuList,
            selectedMenu: null
        };
    },
    methods: {
        // 动态绑定菜单节点样式
        bindClass(environ, selected) {
            const classInfo = { 'menu-selected': selected,  'pro-environ': environ === 'produce', 'test-environ': environ === 'test' };
            return classInfo;
        },

        // 左侧box显示与隐藏
        changeShowHidden() {
            this.iconType = !this.iconType;
            this.$emit('menu-fold', this.iconType);
            this.lastLeftX = this.iconType ? this.menuMinWidth : this.leftBarWidth;
        },

        // 设置菜单展开收起状态
        setMenuStatus(isFold) {
            this.iconType = isFold;
            this.$emit('menu-fold', this.iconType);
            this.lastLeftX = this.iconType ? this.menuMinWidth : this.leftBarWidth;
        },

        // 左侧宽度拖动
        mouseDown(e) {
            if (this.iconType) return;
            this.startX = e.clientX;
            this.mouseMove(e);
            this.mouseUp();
            document.addEventListener('mousemove', this.mouseMove);
            document.addEventListener('mouseup', this.mouseUp);
        },
        mouseUp() {
            this.leftBarWidth = this.lastLeftX;
            document.removeEventListener('mousemove', this.mouseMove);
            document.removeEventListener('mouseup', this.mouseUp);
        },
        mouseMove(e) {
            e.preventDefault();
            e.stopPropagation();
            if (e.clientX < 200) return;
            const offset = e.clientX - this.startX;
            if (offset) {
                this.lastLeftX = offset + this.leftBarWidth;
            }
        },

        // 切换菜单
        checkMenu(item) {
            this.selectedMenu = item;
            this.$emit('check-menu', item);
        },
        // 底部btn点击事件
        footBtnClick(item) {
            this.$emit('footer-click', item);
        },
        // 菜单模糊搜索
        fuzzySearch(val) {
            this.menuListCopy = val ? this.getSearchList(val) : this.menuList;
        },
        // 无筛选初始化菜单
        initMenu(item) {
            this.checkMenu(item);
            this.fuzzySearch('');
        },
        getSearchList(val) {
            return this.menuList.filter(item => {
                if (this.useRegExpSearch) {
                    return item[this.titleAttribute].search(val) !== -1;
                }
                const title = (item[this.titleAttribute] || '').toLowerCase();
                return title.includes((val || '').toLowerCase());
            });
        },
        // 重制菜单数据
        remakeMenuList(list) {
            this.menuListCopy = list;
            const regVal = this.searchText ? (this.useRegExpSearch ? new RegExp(`${this.searchText}`, 'i') : this.searchText) : '';
            this.fuzzySearch(regVal);
        }
    },
    computed: {
        leftStyle() {
            return { width: this.lastLeftX + 'px' };
        },
        liStyle() {
            const style = this.iconType ? { display: 'none' } : null;
            return style;
        }
    },
    watch: {
        searchText(val) {
            const regVal = val ? (this.useRegExpSearch ? new RegExp(`${val}`, 'i') : val) : '';
            this.fuzzySearch(regVal);
        }
    },
    render() {
        const { leftStyle, liStyle, menuTitle, menuListCopy, liClassAttribute, titleAttribute, iconType, showSearch, placeholder, selectedMenu } = this;
        return <div class="apm-box">
            <div class="left-box" style={leftStyle}>
                {this.$slots.menu}
                {
                    this.customMenu ? ''
                        : <div class="menu">
                            {menuTitle ? <div class="header-menu" title={menuTitle}>&nbsp;&nbsp;&nbsp;{menuTitle}</div> : ''}
                            {showSearch ? <h-input v-model={this.searchText} placeholder={placeholder} icon="android-search" style={liStyle}></h-input> : ''}
                            <ul class="menu-ul" style={this.footer || this.$slots.menuFooter ? 'height: calc(100% - 100px)' : 'height: calc(100% - 55px)'}>
                                {menuListCopy.map((item) => {
                                    return <li key={item.id} class={this.bindClass(item[liClassAttribute], this.menuItemId ? item?.[this.menuItemId] === selectedMenu?.[this.menuItemId]
                                        : item?.[this.titleAttribute] === selectedMenu?.[this.titleAttribute])} onclick={() => this.checkMenu(item)}>
                                        {this.productAlerts.indexOf(item.id) > -1
                                            ? <h-icon name="prompt_fill" color="#ff9901" size="14" style="margin-right: 3px">
                                            </h-icon>
                                            : ''}
                                        <span style={liStyle}>{item[titleAttribute]}</span>
                                    </li>;
                                })}
                            </ul>
                            {this.footer ? <aButton type="danger" class="menu-footer" style={liStyle} onclick={() => this.footBtnClick(selectedMenu)}>{this.footer}</aButton> : ''}
                            {this.$slots.menuFooter}
                        </div>
                }
                <div class="x-resizer" onMousedown={this.mouseDown}></div>
                <span class="cate-switch" title={iconType ? '展开' : '收起'} onClick={this.changeShowHidden}>
                    <h-icon name={iconType ? 'ios-arrow-forward' : 'ios-arrow-back'} size='14' color="#cacfd4"></h-icon>
                </span>
            </div>

            <div class="right-box">
                {this.$slots.right}
            </div>
        </div>;
    }
};
