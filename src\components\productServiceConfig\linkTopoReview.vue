<template>
  <div v-if="supportFeature" ref="table-box" class="topo-config">
    <!-- 占位符，用于解决从别的页面跳转到该tab时，切换浏览器窗口或者打开ppt后无法定位到该tab的hui bug -->
    <input id="product-service-placeholder" />
    <description-bar
      ref="modelDes"
      :data="modelDes"
      @select-change="handleSelectModelChange"
    ></description-bar>
    <div class="tabs-box">
      <h-tabs v-if="modeltabs.length" v-model="tabName" class="product-box">
        <h-tab-pane
          v-for="tab in modeltabs"
          :key="tab.bizTraceType"
          :label="tab.bizTraceTypeAlias"
          :name="tab.bizTraceType"
        >
            <all-link-topo
                v-if="viewMode && viewModeType === 'link'"
                :ref="tab.bizTraceType"
                :key="tab.bizTraceType"
                :template="viewMode"
            />
            <app-topo
                v-else-if="viewMode && viewModeType === 'group'"
                :ref="tab.bizTraceType"
                :key="tab.bizTraceType"
                :template="viewMode"
            />
          <no-data v-else text="该模型文件暂无拓扑结构" />
        </h-tab-pane>
        <a-button slot="extra" type="dark" @click="handleDrawerOpen">链路数据</a-button>
      </h-tabs>
      <no-data v-else text="该模型文件暂无拓扑结构" />
    </div>
    <!-- 侧边弹窗 -->
    <h-drawer
        ref='drawer-box'
        v-model="drawerVisible"
        width="50"
        title="链路数据定义"
        @on-close="handleDrawerClose"
    >
    <obs-table
        ref="table1"
        :height="tableHeight"
        :hasDarkClass="false"
        :title="spanTableTitle"
        :tableData="spanTableData"
        showTitle
        :columns="spanColumns"
        :hasPage="false"
      />
      <br/>
      <obs-table
        ref="table2"
        :height="tableHeight"
        :hasDarkClass="false"
        :title="bizTagsTableTitle"
        :tableData="bizTagsTableData"
        showTitle
        :columns="bizTagsColumns"
        :hasPage="false"
      />
    </h-drawer>
    <a-loading v-if="loading"></a-loading>
  </div>
  <no-data v-else text="不支持该特性" />
</template>

<script>
import _ from 'lodash';
import { isJSON } from '@/utils/utils';
import obsTable from '@/components/common/obsTable/obsTable';
import aLoading from '@/components/common/loading/aLoading';
import noData from '@/components/common/noData/noData';
import aButton from '@/components/common/button/aButton';
import appTopo from '@/components/common/topo/appTopo';
import allLinkTopo from '@/components/common/topo/allLinkTopo';
import descriptionBar from '@/components/common/description/descriptionBar';
import {
    getProductFeatures,
    getLatencyTraceModels
} from '@/api/productApi';

export default {
    props: {
        productInfo: {
            type: Object,
            default: {}
        }
    },
    components: { aButton, aLoading, appTopo, descriptionBar, obsTable, noData, allLinkTopo },
    data() {
        return {
            loading: false,
            modelDes: {
                title: {
                    label: '应用全链路时延度量模型',
                    slots: [
                        {
                            type: 'select',
                            key: 'model-Select',
                            palceHodler: '请选择',
                            options: []
                        }
                    ]
                },
                details: [
                    {
                        dataDic: [
                            {
                                label: '模型名称',
                                key: 'modelName'
                            },
                            {
                                label: '产品类型',
                                key: 'productType'
                            },
                            {
                                label: '业务系统',
                                key: 'bizSysTypes'
                            },
                            {
                                label: '业务类型',
                                key: 'bizType'
                            },
                            {
                                label: '业务系统版本',
                                key: 'bizSysVersion'
                            },
                            {
                                label: '模型版本',
                                key: 'modelVersion'
                            },
                            {
                                label: '简介',
                                key: 'modelBrief',
                                span: 12
                            }
                        ],
                        data: {
                            modelName: '-',
                            productType: '-',
                            bizType: '-',
                            bizSysTypes: '-',
                            bizSysVersion: '-',
                            modelVersion: '-',
                            modelBrief: '-'
                        }
                    }
                ]
            },
            tabName: '',
            modeltabs: [],
            spanTableTitle: {
                label: '跨度定义'
            },
            spanColumns: [
                {
                    key: 'applicationName',
                    title: '应用节点类型',
                    ellipsis: true,
                    render: (h, params) => {
                        const res = this.$store?.state?.apmDirDesc?.appTypeDictDesc[params.row?.applicationName] || params.row?.applicationName || '';
                        return h(
                            'span',
                            {
                                attrs: {
                                    title: res
                                }
                            },
                            res
                        );
                    }
                },
                {
                    key: 'spanName',
                    title: '跨度定义',
                    ellipsis: true,
                    render: (h, params) => {
                        return h(
                            'span',
                            {
                                attrs: {
                                    title: params.row?.spanName?.toUpperCase()
                                }
                            },
              params.row?.spanName?.toUpperCase()
                        );
                    }
                },
                {
                    key: 'spanAlias',
                    title: '跨度名称',
                    ellipsis: true
                }
            ],
            bizTagsTableTitle: {
                label: '业务字段'
            },
            bizTagsTableData: [],
            bizTagsColumns: [
                {
                    key: 'key',
                    title: '业务字段',
                    ellipsis: true
                },
                {
                    key: 'label',
                    title: '字段明细',
                    ellipsis: true
                }
            ],
            tableData: [], // 获取链路模型表格数据
            supportFeature: false,
            traceModels: [],
            tempList: [],
            modelData: {},
            viewModeType: '',
            drawerVisible: false,
            tableHeight: 0
        };
    },
    mounted() {
        window.addEventListener('resize', this.fetTableHeight);
    },
    beforeDestroy() {
        window.removeEventListener('resize', this.fetTableHeight);
    },
    computed: {
        // 应用链路模型、穿透模型
        viewMode: function () {
            let info = '';
            for (const ele of this.tempList) {
                this.viewModeType = ele?.meta?.modelPattern || 'group';
                if (ele?.meta?.bizWorkloadTraceType === this.tabName) {
                    info = ele;
                    break;
                }
            }
            return info;
        },
        spanTableData: function(){
            const bizTraceTypeSpans = (_.find(this.modelData?.bizTraceTypes, ['bizTraceType', this.tabName])?.spans || [])?.sort();
            const res = [];

            // TODO: 暂时默认 spanName、applicationName、spanAlias是一对一关系不变
            bizTraceTypeSpans.forEach(o => {
                const spanData = _.filter(this.modelData?.spans, ['spanName', o])?.[0];
                if (spanData){
                    res.push({
                        applicationName: spanData?.applicationName,
                        spanName: spanData?.spanName,
                        spanAlias: spanData?.spanAlias
                    });
                }
            });
            return res;
        }
    },
    methods: {
    // 初始化页面
        async initData(param) {
            const { modelId } = param;
            this.clearData();
            this.loading = true;
            try {
                await this.getProductFeatures();
                if (this.supportFeature) {
                    await this.queryTableData();
                    const id = modelId ? _.filter(this.traceModels || [], ['modelId', modelId])?.[0]?.modelId || this.modelDes.title.slots[0]?.options?.[0]?.value : this.modelDes.title.slots[0]?.options?.[0]?.value;
                    this.$nextTick(() => {
                        id && this.$refs['modelDes'] && this.$refs['modelDes'].setSelectVal('model-Select', id);
                    });
                }
            } catch (err) {
                console.error(err);
            } finally {
                this.loading = false;
            }
        },
        // 清理数据
        clearData() {
            this.supportFeature = false;
            this.tableData = [];
            this.modelDes.title.slots[0].options = [];
            this.modelDes.details[0].data = {};
            this.traceModels = [];
            this.tempList = [];
            this.bizTagsTableData = [];
            this.tabName = '';
            this.modelData = {};
            this.viewModeType = '';
        },
        // 获取表格数据
        async queryTableData() {
            await this.getLatencyTraceModels();
        },
        // 获取产品特性
        async getProductFeatures() {
            const param = { productId: this.productInfo.id };
            const res = await getProductFeatures(param);
            if (this.productInfo.id !== param?.productId) return;
            this.supportFeature = Boolean(res?.data?.latencyMeasurement);
        },
        // 获取链路模型表格数据
        async getLatencyTraceModels() {
            const param = { productType: this.productInfo.productType };
            const res = await getLatencyTraceModels(param);
            if (res?.code === '200') {
                this.traceModels = res?.data || [];
                this.modelDes.title.slots[0].options = (res?.data || []).map((o) => {
                    return {
                        label: o?.modelName,
                        value: o?.modelId
                    };
                });
            } else {
                this.modelDes.title.slots[0].options = [];
            }
        },
        handleSelectModelChange(val, key) {
            if (key === 'model-Select') {
                // 基础信息
                this.modelId = val || '';
                this.modelData = _.find(this.traceModels, ['modelId', val]) || {};
                const bizSysTypes = (this.modelData?.bizSysTypes || []).map(o => {
                    return this.$store?.state?.apmDirDesc?.bizSysTypeDict?.[o] || o || '';
                });
                this.modelDes.details[0].data = {
                    modelName: this.modelData?.modelName,
                    productType: this.$getProductType(this.modelData?.productType) || this.modelData?.productType,
                    bizSysTypes: bizSysTypes?.join(','),
                    bizType: this.$store?.state?.apmDirDesc?.bizTypeDict?.[this.modelData?.bizType] || this.modelData?.bizType,
                    bizSysVersion: this.modelData?.bizSysVersion,
                    modelVersion: this.modelData?.modelVersion,
                    modelBrief: this.modelData?.modelBrief
                };

                // 查询产品时延链路模型配置
                this.tempList =
                this.modelData?.latencyTopology && isJSON(this.modelData?.latencyTopology)
                    ? JSON.parse(this.modelData?.latencyTopology)
                    : [];
                this.modeltabs = this.modelData?.bizTraceTypes || [];
                // 业务字段
                this.bizTagsTableData = this.modelData?.bizTags || [];
                this.$nextTick(() => {
                    // 跨度定义
                    this.tabName = this.modeltabs?.[0]?.bizTraceType || '';
                });
            }
        },
        // 打开弹窗
        handleDrawerOpen() {
            this.drawerVisible = true;
            this.fetTableHeight();
        },
        // 关闭弹窗
        handleDrawerClose() {
            this.drawerVisible = false;
        },
        // 设置table高度
        fetTableHeight() {
            this.tableHeight = (this.$refs['drawer-box']?.$el?.offsetTop - 200) / 2;
        }
    }
};
</script>

<style lang="less" scoped>
#product-service-placeholder {
    position: absolute;
    width: 0;
    height: 0;
    display: block;
    top: 0;
    left: 0;
    border: none;
    opacity: 0;
    outline: none;
}

.topo-config {
    position: relative;
    width: 100%;
    height: calc(100% - 70px);

    .obs-title-1 {
        background: var(--wrapper-color);
        margin-top: 10px;
    }

    .tabs-box {
        background: var(--wrapper-color);
        margin-top: 10px;
        height: calc(100% - 70px);
    }

    .product-box {
        /deep/ .h-tabs-nav-wrap {
            float: none !important;
        }

        /deep/ .h-tabs-nav-right {
            position: absolute;
            right: 0;
            top: 5px;
        }
    }
}
</style>
