import _ from 'lodash';
import { getSqlListFromSqlStr } from '@/utils/utils';
import { codemirror, CodeMirror } from 'vue-codemirror';
import sqlFormatter from 'sql-formatter';
import './sqlEdit.less';
import 'codemirror/lib/codemirror.css';
// 语言语法
import 'codemirror/mode/sql/sql';
// sql语言提示
import 'codemirror/addon/hint/sql-hint.js';

// active-line.js
import 'codemirror/addon/selection/active-line.js';

// placeholder
import 'codemirror/addon/display/placeholder.js';

// styleSelectedText
import 'codemirror/addon/selection/mark-selection.js';
import 'codemirror/addon/search/searchcursor.js';

// 自动提示
import 'codemirror/addon/hint/show-hint.js';
import 'codemirror/addon/hint/show-hint.css';

// highlightSelectionMatches
import 'codemirror/addon/scroll/annotatescrollbar.js';
import 'codemirror/addon/search/matchesonscrollbar.js';
import 'codemirror/addon/search/match-highlighter.js';
// keyMap
import 'codemirror/mode/clike/clike.js';
import 'codemirror/keymap/sublime.js';

// foldGutter
import 'codemirror/addon/fold/foldgutter.css';
import 'codemirror/addon/fold/brace-fold.js';
import 'codemirror/addon/fold/comment-fold.js';
import 'codemirror/addon/fold/foldcode.js';
import 'codemirror/addon/fold/foldgutter.js';
import 'codemirror/addon/fold/indent-fold.js';
import 'codemirror/addon/fold/markdown-fold.js';
import 'codemirror/addon/fold/xml-fold.js';

// theme css
import 'codemirror/theme/xq-dark.css';
import 'codemirror/theme/xq-light.css';

import { KEYWORDS, FUNCTIONS } from './editor-box';

// 匹配 （from || join）关键字
const TABLE_SUGGESET_POS_REG = /(^|\s+)(from|join)\s+(\S*)$/i;

// 匹配表名 别名

const TABLES_PATTENS = [
    /\sjoin\s+([\w._`]+)\s*(?:as)?\s*([\w_`]+)?/gi,
    /\sfrom\s+([\w._`]+)\s*(?:as)?\s*([\w_`]+)?/gi,
    /\sinto\s+([\w._`]+)\s*(?:as)?\s*([\w_`]+)?/gi,
    /update\s+([\w._`]+)\s*(?:as)?\s*([\w_`]+)?/gi,
    /drop\s+([\w._`]+)\s*(?:as)?\s*([\w_`]+)?/gi,
    /create\s+([\w._`]+)\s*(?:as)?\s*([\w_`]+)?/gi
];
export default {
    name: 'sql-edit',
    components: { codemirror },
    props: {
        // 是否触发change事件
        isChange: {
            type: Boolean,
            default: false
        },
        tableList: {
            type: Array,
            default: () => []
        }
    },
    data() {
        return {
            fileName: '',
            sqlCode: '',
            tableStruct: {},
            editorOptions: {
                // codemirror options
                tabSize: 4,
                mode: 'text/x-mysql',
                theme: 'xq-light',
                fullScreen: false,
                lineNumbers: true,
                line: true,
                // 代码折叠
                foldGutter: true,
                gutters: ['CodeMirror-linenumbers', 'CodeMirror-foldgutter'],
                // 高级配置（需要引入对应的插件包）,codemirror advanced options(You need to manually introduce the corresponding codemirror function script code)
                // sublime、emacs、vim三种键位模式，支持你的不同操作习惯
                keyMap: 'sublime',
                // 按键映射，比如Ctrl键映射autocomplete，autocomplete是hint代码提示事件
                extraKeys: {
                    F9: () => {
                        this.formatSqlFun();
                    },
                    'Ctrl-S': () => {
                        this.saveFile();
                    },
                    'Ctrl-space': 'autocomplete'
                },
                showHitObj: null, // 延迟显示联想列表
                currentWord: '', // 关键字记录
                updateTimer: null,
                viewportMargin: 100,
                maxHighlightLength: 10000,
                sqlSuggestList: [],
                placeholder: '支持拖拽导入SQL文件',
                // 联想功能
                // hint: （cm) => {
                //   return this.Hint();
                // }
                // hint.js options
                hintOptions: {
                    // 当匹配只有一项的时候是否自动补全
                    completeSingle: false
                },
                // 联想表名
                sqlSuggestTableNameLists: [],
                // 联想表
                sqlSuggestTableLists: [],
                // 联想列名
                sqlSuggestColumns: [],
                runCur: null
                // 选中文本自动高亮，及高亮方式
                // styleSelectedText: true,
                // highlightSelectionMatches: { showToken: /\w/, annotateScrollbar: true },
                // more codemirror options...
                // 如果有hint方面的配置，也应该出现在这里
            }
        };
    },
    computed: {
        codemirror() {
            return this.$refs.codeEditor.codemirror;
        }
    },
    mounted() {
        this.$nextTick(() => {
            if (this.$refs.codeEditor) {
                // 绑定键盘事件
                this.$refs.codeEditor.codemirror.on('keyup', this.handleEditorKeyUp);
            }
        });
    },
    methods: {
        // 查询表字段
        getTableFields(tableName) {
            const that = this;
            return new Promise((resolve, reject) => {
                let result;
                this.$emit('query', tableName, val => {
                    result = val.map(v => v.name);
                    that.tableStruct[tableName] = result;
                    resolve(result);
                });
            });
        },
        // 获取sql执行内容
        getSqlList() {
            const sql = this.$refs.codeEditor.codemirror.getSelection() || this.sqlCode;
            return getSqlListFromSqlStr(sql);
        },
        // sql文件导出
        saveFile() {
            this.fileName = '';
            this.$hMsgBoxSafe.confirm({
                title: '您确认保存当前编辑器中的内容吗？',
                onOk: () => {
                    if (this.fileName === '') {
                        this.$hMessage.error('文件名不得为空');
                        return;
                    }
                    const parseData = this.sqlCode;
                    // 通过encodeURIComponent解决中文乱码问题
                    const uri = 'data:text/csv;charset=utf-8,\ufeff' + encodeURIComponent(parseData);
                    // 模拟a标签下载
                    const link = document.createElement('a');
                    link.href = uri;
                    link.download = `${this.fileName}.sql`;
                    document.body.appendChild(link);
                    link.click();
                    document.body.removeChild(link);
                },
                render: (h) => {
                    return h('Input', {
                        props: {
                            value: this.fileName,
                            autofocus: true,
                            placeholder: '请输入文件名'
                        },
                        on: {
                            input: val => {
                                this.fileName = val;
                            }
                        }
                    });
                }
            });
        },
        // 联想表名和关键字
        async autocompleteLocalAndTable(editor) {
            const cur = editor.getCursor();
            const token = this.getToken(editor, cur);
            const word = token.string.toLowerCase();
            let list = [];
            const list1 = this.getSuggestListByRequsest(this.sqlSuggestTableNameLists, word);
            const list2 = this.getSuggestListByLocal(KEYWORDS.concat(FUNCTIONS), word);
            list = list1.concat(list2);
            if (token.string.indexOf('.') > -1) {
                let table;
                if (token.string[0] === '`') {
                    table = token.string.substring(1).split('.')[0];
                } else {
                    table = token.string.split('.')[0];
                }
                if (this.sqlSuggestTableNameLists.indexOf(table) > -1) {
                    const arr = this.tableStruct?.[table] || await this.getTableFields(table) || [];
                    const list3 = this.getSuggestListByLocal(arr.map(item => {
                        return table + '.' + item;
                    }), word);
                    list = list.concat(list3);
                }
            }
            this.hintSort(list, word);
            return {
                list,
                from: CodeMirror.Pos(cur.line, token.start),
                to: CodeMirror.Pos(cur.line, token.end)
            };
        },
        autocompleteTables(editor) {
            const cur = editor.getCursor();
            const token = this.getToken(editor, cur);
            const word = token.string.toLowerCase();

            const list = this.getSuggestListByRequsest(this.sqlSuggestTableNameLists, word);
            this.hintSort(list, word);
            return {
                list,
                from: CodeMirror.Pos(cur.line, token.start),
                to: CodeMirror.Pos(cur.line, token.end)
            };
        },
        // 联想表名|列名处理
        getSuggestListByRequsest(list, word) {
            const listArr = [];
            const map = {};
            for (let i = 0, len = list.length; i < len; i++) {
                const item = list[i].toLowerCase();
                if (item.indexOf(word.trim()) !== -1) {
                    if (!map[list[i]]) {
                        listArr.push(list[i]);
                        map[list[i]] = true;
                    }
                }
            }
            return listArr;
        },
        autocompleteColumns(editor) {
            let list = [];
            const cur = editor.getCursor();
            const token = this.getToken(editor, cur);
            const word = token.string.toLowerCase();
            // 联想列名
            const sqlSuggestColumns = this.getSuggestListByRequsest(
                this.sqlSuggestColumns,
                word
            );
            list = list.concat(sqlSuggestColumns);
            this.hintSort(list, word);
            list = [...new Set(list)];

            return {
                list: list,
                from: CodeMirror.Pos(cur.line, token.start),
                to: CodeMirror.Pos(cur.line, token.end)
            };
        },
        // 本地关键字联想
        autoComplete(editor) {
            const COMBINED_KEYWORDS = KEYWORDS.concat(FUNCTIONS);
            const cur = editor.getCursor();
            const token = this.getToken(editor, cur);
            const word = token.string.toLowerCase();
            let list = [];

            // 变量
            if (word.indexOf('.') < 0) {
                // 关键字,函数
                const a = this.getSuggestListByLocal(COMBINED_KEYWORDS, word);
                list = list.concat(a);
                if (this.systemVars && this.systemVars.length) {
                    const arr = this.getSuggestListByLocal(this.systemVars, word);
                    list = list.concat(arr);
                }
            }
            this.hintSort(list, word);
            list = [...new Set(list)];

            return {
                list: list,
                from: CodeMirror.Pos(cur.line, token.start),
                to: CodeMirror.Pos(cur.line, token.end)
            };
        },
        // 通过关键字 匹配获取suggest列表
        getSuggestListByLocal(list, word) {
            if (word.trim() === '') {
                return [];
            }

            const map = {};
            const listArr = [];
            for (let i = 0, localL = list.length; i < localL; i++) {
                const a = list[i].toLowerCase();
                if (a.indexOf(word.trim()) !== -1) {
                    if (!map[list[i]]) {
                        listArr.push(list[i]);
                        map[list[i]] = true;
                    }
                }
            }

            return listArr;
        },
        // 过滤输入字符
        hintSort(arr, key) {
            for (let i = 0; i < arr.Length; i++) {
                for (let j = i; j < arr.Length; j++) {
                    const a = arr[i].toLowerCase();
                    const b = arr[j].toLowerCase();
                    if (a.indexOf(key) > b.indexOf(key)) {
                        const temp = arr[i];
                        arr[i] = arr[j];
                        arr[j] = temp;
                    }
                }
            }
        },
        formatSqlFun() {
            if (this.sqlCode.split('\n').length > 1000 || this.sqlCode.split(';').length > 1000) {
                this.$hMessage.warning('超过1000行语句不支持美化功能!');
                return;
            }
            this.sqlCode = sqlFormatter.format(this.sqlCode, {});
        },
        // 键盘事件监听
        handleEditorKeyUp(editor, e) {
            this.timer && clearTimeout(this.timer);
            this.timer = setTimeout(() => {
                const value = editor.getValue().trim(); // 输入的所有sql
                const cur = editor.getCursor();
                const token = this.getToken(editor, cur);
                const originWord = token.string.trim();
                const word = originWord.toLowerCase(); // 关键字

                if (
                    e.key === 'Enter' ||
                    e.key === 'Tab' ||
                    e.key === 'Escape' ||
                    word === ''
                ) {
                    this.currentWord = '';
                    return true;
                }
                if (
                    this.currentWord === word &&
                    (e.key === 'ArrowUp' || e.key === 'ArrowDown')
                ) {
                    return true;
                }
                this.currentWord = word;
                // 复位
                this.sqlSuggestColumns = [];
                this.runCur = cur;
                this.getSuggestData(editor, value, word, originWord);
            }, 100);
        },
        // 获取光标的位置
        getToken(e, cur) {
            const t = e.getTokenAt(cur);
            if (t.string[0] === '`') {
                t.string = t.string.substring(1);
                t.start = t.start + 1;
            }
            if (
                t.string &&
                (t.string.indexOf('.') >= 0 || t.string.indexOf('$') >= 0)
            ) {
                if (cur.ch > 0) {
                    const before = e.getTokenAt({
                        line: cur.line,
                        ch: t.start
                    });
                    if (before.string[0] === '`') {
                        before.string = before.string.substring(1);
                        before.start = before.start + 1;
                    }
                    if (t.string.indexOf('.') === 0) {
                        t.string = t.string.split('.')[1];
                        t.start = t.start + 1;
                    } else {
                        t.string = (before.string + t.string).trim();
                        t.start = before.start;
                    }
                }
            }
            return t;
        },
        // eslint-disable-next-line max-params
        getSuggestData(editor, value, word, originWord) {
            const cur = editor.getCursor();
            // 记录当前查询关键词
            if (word.trim() === '') {
                return;
            }
            const tablesAlias = this.getTablesAndAlias(value);
            const leftValue = editor.getRange({ line: 0, ch: 0 }, cur);
            const lowValue = leftValue.toLowerCase();
            if (TABLE_SUGGESET_POS_REG.test(leftValue)) {
                // from join 后请求表名
                this.getHintTables();
            } else if (tablesAlias.length) {
                // 是否有表 表是否有别名
                this.getHintColumns(tablesAlias, word, originWord);
            } else if (lowValue.indexOf('select') > -1 || lowValue.indexOf('update') > -1) {
                this.getHintTables();
                this.showSuggestMenu('localAndTable');
            } else {
                this.showSuggestMenu('local'); // 本地联想
            }
        },
        getHintTables() {
            this.sqlSuggestTableNameLists = this.tableList;
            this.showSuggestMenu('table');
        },
        getHintColumns(tablesAlias, word, originWord) {
            let tables = [];
            const body = {};
            let leftWord;
            const idx = word.indexOf('.');
            if (idx !== -1) {
                leftWord = originWord.substr(0, idx);
                body.tables = [leftWord];
                this.getColumnList(body, true);
            } else {
                tablesAlias.forEach(item => {
                    if (item.name !== '') {
                        tables.push(item.name);
                    }
                });
                tables = [...new Set(tables)];
                body.tables = tables;
                if (Array.isArray(tables)) {
                    this.getColumnList(body);
                }
            }
        },
        async getColumnList(body, isTable) {
            this.sqlSuggestColumns = [];
            if (Array.isArray(body.tables)) {
                for (const item of body.tables) {
                    if (!this.tableStruct?.[item]) {
                        this.sqlSuggestColumns = this.tableList.indexOf(item) > -1
                            ? this.sqlSuggestColumns.concat(await this.getTableFields(item))
                            : this.sqlSuggestColumns;
                    } else {
                        this.sqlSuggestColumns = this.sqlSuggestColumns.concat(this.tableStruct[item]);
                    }
                }
            }
            if (isTable && this.sqlSuggestColumns.length) {
                this.sqlSuggestColumns = this.sqlSuggestColumns.map(item => {
                    return body.tables[0] + '.' + item;
                });
            }
            this.sqlSuggestColumns = [...new Set([...this.sqlSuggestColumns, ...this.tableList, ...KEYWORDS, ...FUNCTIONS])];
            this.showSuggestMenu('column');
        },
        // 别名是否合法
        isLegalAlias(alias) {
            const keywords = {
                where: 1,
                on: 1,
                using: 1,
                join: 1,
                group: 1,
                order: 1,
                limit: 1
            };
            return !keywords[alias.toLowerCase()];
        },
        // 拿到sql 输入的所有表和对应的别名
        getTablesAndAlias(sql) {
            const COMBINED_KEYWORDS = KEYWORDS.concat(FUNCTIONS);
            const names = [];
            for (let i = 0; i < TABLES_PATTENS.length; i++) {
                const reg = TABLES_PATTENS[i];
                for (; ;) {
                    const found = reg.exec(sql);
                    if (!found) {
                        break;
                    }

                    reg.lastIndex = found.index + 1;
                    const t = found[1];

                    let alias = '';
                    if (found[2]) {
                        alias = found[2].toLowerCase();
                        if (!this.isLegalAlias(alias)) {
                            alias = '';
                        }
                    }
                    if (COMBINED_KEYWORDS.indexOf(t.toUpperCase()) !== -1) {
                        continue;
                    }

                    names.push({
                        name: t,
                        alias: alias
                    });
                }
            }
            return names;
        },
        showSuggestMenu(type) {
            let autoFn;
            if (this.runCur) {
                const cur = this.codemirror.getCursor();
                if (!_.isEqual(this.runCur, cur)) {
                    return;
                }
            }

            if (type === 'local') {
                autoFn = this.autoComplete;
            } else if (type === 'table') {
                autoFn = this.autocompleteTables;
            } else if (type === 'column') {
                autoFn = this.autocompleteColumns;
            } else if (type === 'localAndTable') {
                autoFn = this.autocompleteLocalAndTable;
            }
            if (this.$refs.codeEditor && this.$refs.codeEditor.codemirror) {
                CodeMirror.showHint(this.$refs.codeEditor.codemirror, autoFn);
            }
        },
        // 获取sqlCode内容
        getSqlCode() {
            return this.sqlCode;
        },
        // 设置sqlCode内容
        setSqlCode(sql) {
            this.clearSqlCode();
            this.sqlCode = `${sql}`;
        },
        // 动态添加sql
        addSqlToEdit(sql) {
            this.sqlCode += `\n${sql}`;
        },
        // 清空sql
        clearSqlCode(){
            this.sqlCode = '';
        },
        // 每次编辑器内容更改时触发
        onChangehandle(){
            if (!this.isChange) return;
            this.$emit('code-change', this.sqlCode);
        }
    },
    render() {
        return <div class="sql-editor-wrapper">
            <codemirror
                v-model={this.sqlCode}
                options={this.editorOptions}
                ref="codeEditor"
                v-on:changes={_.debounce(this.onChangehandle, 2000)}
                style="height: 100%;"
            ></codemirror>
        </div>;
    }
};
