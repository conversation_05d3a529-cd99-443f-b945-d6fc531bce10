/*
 * @Description: rcm
 * @Author: <PERSON><PERSON>
 * @Date: 2023-06-19 15:19:50
 * @LastEditTime: 2023-06-25 21:56:07
 * @LastEditors: <PERSON><PERSON>
 */
import { getRcmProductTemplates } from '@/api/rcmApi';

export const state = () => ({
    transportTempList: [],      // 主题模板列表
    singleCtxList: [],          // 单例上下文列表
    clusterCtxList: []          // 集群上下文列表
});

export const mutations = {
    transport(data, list) {
        data.transportTempList = list;
    },
    singletonContext(data, list) {
        data.singleCtxList = list;
    },
    clusterContext(data, list) {
        data.clusterCtxList = list;
    }
};

export const actions = {
    getRcmProductTemplates(context, param) {
        return new Promise((resolve, reject) => {
            getRcmProductTemplates(param).then(res => {
                if (res.success) {
                    const { data } = res;
                    const keys = Object.keys(mutations);
                    if (data) {
                        keys.forEach(key => {
                            if (!(key in data)) {
                                data[key] = [];
                            }
                        });
                    }

                    Object.keys(data).forEach(ele => {
                        context.commit(ele, data[ele]);
                    });
                    resolve(true);
                } else {
                    resolve(false);
                }
            }).catch(err => {
                reject(err);
            });
        });
    }
};
