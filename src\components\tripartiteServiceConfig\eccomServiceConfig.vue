<template>
    <div ref="table-box" class="topo-config">
        <obs-title :title="title" class="obs-title-1">
            <template v-slot:extraTitleBox>
                <div class="des-box">
                    <h4>“证券期货低延时交易监控平台” 是 “上海华讯网络系统有限公司”  基于Arista7130低延时交换机，提供的一套光分低延时交换机应用穿透时延度量系统。支持通过Kafka中间件，将时延数据推送到三方监控运维系统。</h4>
                </div>
            </template>
        </obs-title>
      <obs-table
        ref="table2"
        :title="tableTitle"
        :tableData="tableData"
        showTitle
        :columns="columns"
        :hasPage="false"
        :loading="tableLoading"
        @button-click="handleButtonClick"
      />
      <collector-config-data-modal v-if="addOrEditDataInfo.status" :modalInfo="addOrEditDataInfo" :adaptedBizSysTypeDict="adaptedBizSysTypeDict" @add-or-edit="addOrEditEccomCollector" />
    </div>
  </template>

<script>
import { mapState, mapActions } from 'vuex';
import obsTitle from '@/components/common/title/obsTitle';
import obsTable from '@/components/common/obsTable/obsTable';
import collectorConfigDataModal from '@/components/tripartiteServiceConfig/modal/collectorConfigDataModal.vue';
import {
    addOrEditEccomCollector,
    getEccomCollector,
    deleteEccomCollector
} from '@/api/productApi';
export default {
    props: {},
    components: { obsTitle, obsTable, collectorConfigDataModal },
    data() {
        return {
            tableLoading: false,
            title: {
                label: '华讯数据服务'
            },
            supportFeature: false,
            tableTitle: {
                label: '配置服务接入代理',
                slots: [
                    {
                        type: 'button',
                        key: 'add-click',
                        buttonType: 'dark',
                        value: '创建'
                    }
                ]
            },
            tableData: [],
            columns: [
                {
                    title: '采集器实例',
                    key: 'instanceName',
                    ellipsis: true
                },
                {
                    title: '接入服务类型',
                    key: 'serverType',
                    ellipsis: true
                },
                {
                    title: '接入服务地址',
                    key: 'kafkaAddress',
                    render: (h, params) => {
                        return h('div', [
                            h('Poptip', {
                                class: 'apm-poptip monitor-poptip poptip-ellipsis',
                                props: {
                                    trigger: 'hover',
                                    placement: 'left-end',
                                    positionFixed: true,
                                    width: 150
                                }
                            }, [
                                h('div',
                                    {
                                        class: 'h-table-cell-ellipsis'
                                    },
                                    params.row?.kafkaAddress
                                ),
                                h('div', {
                                    slot: 'content',
                                    class: 'pop-content'
                                }, [
                                        params.row?.kafkaAddress?.split(',')?.map(item => {
                                            return h('p', {
                                                style: {
                                                    padding: '4px 0'
                                                }
                                            }, [
                                                item
                                            ]);
                                        })
                                ])
                            ])
                        ]);
                    }
                },
                {
                    title: 'Topic地址',
                    key: 'kafkaTopics',
                    render: (h, params) => {
                        return h('div', [
                            h('Poptip', {
                                class: 'apm-poptip monitor-poptip',
                                props: {
                                    placement: 'left-end',
                                    positionFixed: true,
                                    width: 200
                                }
                            }, [
                                h('Button',
                                    {
                                        props: {
                                            size: 'small',
                                            type: 'text'
                                        }
                                    },
                                    '查看'
                                ),
                                h('div', {
                                    slot: 'content',
                                    class: 'pop-content'
                                }, [
                                        params.row.kafkaTopics?.split(',')?.map(item => {
                                            return h('p', {
                                                style: {
                                                    padding: '4px 0'
                                                }
                                            }, [
                                                item
                                            ]);
                                        })
                                ])
                            ])
                        ]);
                    }
                },
                {
                    title: '已适配的业务系统',
                    key: 'adaptedBizSysType',
                    ellipsis: true,
                    render: (h, params) => {
                        const res = this.adaptedBizSysTypeDict?.find(o => o?.code === params?.row?.adaptedBizSysType)?.name || params.row?.adaptedBizSysType || '';
                        return h('span', {
                            attrs: {
                                title: res
                            }
                        }, res);
                    }
                },
                {
                    title: '绑定产品节点',
                    key: 'productList',
                    ellipsis: true,
                    algin: 'center',
                    render: (h, params) => {
                        return h('div',
                            {
                                class: 'h-table-cell-ellipsis apm-table-cell-text',
                                attrs: {
                                    title: params.row.productName
                                },
                                on: {
                                    click: (e) => {
                                        e.preventDefault();
                                        e.stopPropagation();
                                        this.goLink('/ldpLinkConfig', {
                                            productId: params?.row?.productId
                                        });
                                    }
                                }
                            },
                            [params.row.productName]
                        );
                    }
                },
                {
                    title: '操作',
                    key: 'action',
                    fixed: 'right',
                    width: 140,
                    render: (h, params) => {
                        return h('div', [
                            h(
                                'Button',
                                {
                                    props: {
                                        size: 'small',
                                        type: 'text'
                                    },
                                    on: {
                                        click: () => {
                                            this.handleButtonClick('edit-click', params.row);
                                        }
                                    }
                                },
                                '修改'
                            ),
                            h(
                                'Button',
                                {
                                    props: {
                                        size: 'small',
                                        type: 'text'
                                    },
                                    on: {
                                        click: () => {
                                            this.$hMsgBoxSafe.confirm({
                                                title: '删除',
                                                content: `您确定删除 '${params.row?.instanceName}' 实例？`,
                                                onOk: async () => {
                                                    await this.deleteEccomCollector(params.row?.id);
                                                }
                                            });
                                        }
                                    }
                                },
                                '删除'
                            )
                        ]);
                    }
                }
            ],
            addOrEditDataInfo: {
                status: false
            }
        };
    },
    mounted() {
    },
    beforeDestroy() {
    },
    computed: {
        ...mapState({
            adaptedBizSysTypeDict: state => {
                return state.product.adaptedBizSysTypeDict || [];
            }
        })
    },
    methods: {
        ...mapActions({ getAdaptedBizSysTypeDict: 'product/getAdaptedBizSysTypeDict' }),
        goLink(url, query) {
            this.$hCore.navigate(url, { history: true }, query);
        },
        // 初始化页面
        async initData() {
            this.clearData();

            try {
                this.tableLoading = true;
                await this.getAdaptedBizSysTypeDict();
                await this.getEccomCollector();
            } catch (err) {
                console.error(err);
            } finally {
                this.tableLoading = false;
            }
        },
        // 清理数据
        clearData() {
            this.tableData = [];
        },
        // 创建和修改弹窗
        handleButtonClick(key, params){
            if (key === 'add-click'){
                this.addOrEditDataInfo = {
                    status: true,
                    type: 'add',
                    title: '创建数据采集代理',
                    id: '',
                    adaptedBizSysType: '',
                    kafkaAddress: '',
                    kafkaTopics: '',
                    productId: ''
                };
            } else if (key === 'edit-click'){
                this.addOrEditDataInfo = {
                    status: true,
                    type: 'edit',
                    title: '修改数据采集代理',
                    id: params?.id,
                    adaptedBizSysType: params?.adaptedBizSysType,
                    kafkaAddress: params?.kafkaAddress,
                    kafkaTopics: params?.kafkaTopics,
                    productId: params?.productId
                };
            }
        },
        // 查询采集器列表（华讯）
        async getEccomCollector(){
            const res = await getEccomCollector();
            if (res?.code === '200'){
                this.tableData = res?.data || [];
            } else {
                this.tableData = [];
            }
        },
        // 删除
        async deleteEccomCollector(id){
            const param = {
                id
            };
            const res = await deleteEccomCollector(param);
            if (res?.code === '200'){
                this.$hMessage.success('删除成功');
            }
            this.getEccomCollector();
        },
        // 新增和修改数据
        async addOrEditEccomCollector(vals, type){
            const param = {
                ...vals
            };
            const res = await addOrEditEccomCollector(param);
            if (res?.code === '200'){
                this.$hMessage.success(type === 'add' ? '新增成功!' : '修改成功!');
                this.getEccomCollector();
            } else if (res?.code?.length === 8) {
                this.$hMessage.error(res?.message);
            }
        }
    }
};
</script>

  <style lang="less" scoped>
@import url("@/assets/css/poptip-1.less");

.topo-config {
    position: relative;
    width: 100%;
    height: auto;

    .obs-title-1 {
        background: var(--wrapper-color);
        margin-top: 10px;
        height: auto;
    }

    .des-box {
        h4 {
            overflow: hidden;
            text-overflow: ellipsis;
            // stylelint-disable property-no-vendor-prefix,value-no-vendor-prefix
            display: -webkit-box;
            -webkit-box-orient: vertical;
            -webkit-line-clamp: 3;
        }
    }

    /deep/.poptip-ellipsis {
        width: 100%;

        .h-poptip-rel {
            width: 100%;
        }
    }

    /deep/ .h-table-row-hover td .apm-table-cell-text {
        color: var(--table-button-hover-bgcolor) !important;
    }

    /deep/.apm-table-cell-text {
        cursor: pointer;
        color: var(--link-color);

        &:hover {
            color: var(--link-color);
            text-decoration: underline;
        }
    }
}
  </style>
