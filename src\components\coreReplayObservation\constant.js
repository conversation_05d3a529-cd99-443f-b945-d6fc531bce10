// 是否一致
export const VERIFE_RESULT_LIST = [
    {
        value: 'FAILURE',
        label: '不一致',
        icon: 'error'
    },
    {
        value: 'SUCCESS',
        label: '一致',
        icon: 'success'
    }
];

// 执行状态
export const VERIFE_STATUS_LIST = [
    {
        value: 'not_started',
        label: '未启动',
        icon: 'offline'
    },
    {
        value: 'running',
        label: '执行中',
        icon: 'loading'
    },
    {
        value: 'finished',
        label: '已完成',
        icon: 'success'
    },
    {
        value: 'paused',
        label: '已暂停',
        icon: 'paused'
    },
    {
        value: 'stopped',
        label: '已终止',
        icon: 'stop'
    },
    {
        value: 'failed',
        label: '失败',
        icon: 'error'
    }
];

// 通用按钮
const COMMON_BUTTONS = [
    { label: '详情', action: 'detail' },
    { label: '任务参数', action: 'view' },
    { label: '删除', action: 'delete' }
];

export const BUTTON_GROUP = {
    not_started: [
        // { label: '启动', action: 'start' },
        // { label: '修改配置', action: 'modify' },
        ...COMMON_BUTTONS
    ],
    running: [
        // { label: '暂停', action: 'paused' },
        // { label: '终止', action: 'terminated' },
        ...COMMON_BUTTONS.filter(btn => btn.action !== 'delete')
    ],
    paused: [
        // { label: '恢复', action: 'resume' },
        // { label: '终止', action: 'terminated' },
        // { label: '单步调试', action: 'step-debug' }
        ...COMMON_BUTTONS
    ],
    finished: COMMON_BUTTONS,
    stopped: COMMON_BUTTONS,
    failed: COMMON_BUTTONS
};
