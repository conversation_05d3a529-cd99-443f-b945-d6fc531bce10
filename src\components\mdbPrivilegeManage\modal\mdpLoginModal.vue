<template>
    <div>
        <h-msg-box-safe v-model="modalData.status" :escClose="false" :mask-closable="false" :closable="false" title="MDB用户登录" width="600">
            <a-tips tipText="若登录失败，请先确认产品节点是否选择正确。或联系管理员重置账户。"></a-tips>
            <h-form ref="loginForm" :model="loginForm" :rules="ruleInline" class="login-form" :label-width="80" :hidePopperTime="3000">
                <h-form-item label="产品节点" prop="productInstNo" required style="margin-bottom: 10px;">
                    <h-select v-model="loginForm.productInstNo" widthAdaption placeholder="请选择产品" :positionFixed="true"
                    :clearable="false" @on-change="handleChangeProduct">
                        <h-option v-for="item in productList" :key="item.id" :value="item.productInstNo">{{ item.productName
                        }}</h-option>
                    </h-select>
                </h-form-item>
                <h-form-item label="接入点" prop="endpointId" required style="margin-bottom: 0;">
                    <h-select v-model="loginForm.endpointId" widthAdaption placeholder="请选择接入点" :positionFixed="true"
                    :clearable="false">
                        <h-option v-for="item in endpointList" :key="item.id" :value="item.id">{{ item.name
                        }}</h-option>
                    </h-select>
                    <a-button class="link-text"  type="text" @click="goLink">配置接入点</a-button>
                </h-form-item>
                <div class="config">
                    <h-form-item prop="user" label="用户名" >
                        <h-input v-model.trim="loginForm.user" type="text" placeholder="用户名" :maxlength="30"></h-input>
                    </h-form-item>
                    <h-form-item label="密码" prop="password" required >
                        <h-input v-model.trim="loginForm.password" :type="showPassWord ? 'text':'password'" placeholder="密码" :maxlength="20"
                        :icon="showPassWord ? 'browse_fill' : 'eye-disabled'" autocomplete="new-password" @on-click="isShowPassWord"></h-input>
                    </h-form-item>
                </div>
            </h-form>
            <template v-slot:footer>
                <a-button type="primary" @click="handleSubmit('loginForm')">登录</a-button>
            </template>
        </h-msg-box-safe>
    </div>
</template>

<script>
import _ from 'lodash';
import { mapState, mapActions } from 'vuex';
import { validatePass } from '@/utils/validate';
import aButton from '@/components/common/button/aButton';
import aTips from '@/components/common/apmTips/aTips';
import { getMdbEndpoints } from '@/api/memoryApi';

export default {
    props: {
        modalInfo: {
            type: Object,
            default: null
        }
    },
    data() {
        return {
            modalData: this.modalInfo,
            loginForm: {
                productInstNo: '',
                endpointId: '',
                user: '',
                password: ''
            },
            ruleInline: {
                user: [{ required: true, message: '请填写用户名', trigger: 'blur' }],
                password: [
                    { required: true, message: '请填写密码', trigger: 'blur' },
                    {
                        type: 'string',
                        min: 8,
                        max: 20,
                        message: '请输入密码8~20位',
                        trigger: 'blur'
                    },
                    { validator: validatePass, trigger: 'blur' }
                ]
            },
            showPassWord: false,
            endpointList: []
        };
    },
    computed: {
        ...mapState({
            productList: state => {
                return state.product.productListLight;
            }
        })
    },
    mounted(){
        this.init();
    },
    methods: {
        ...mapActions({ getProductList: 'product/getProductListLight' }),
        init(){
            this.getProductList({ filter: 'excludeLdpApm' });
            this.$refs['loginForm'].resetFields();
            this.loginForm.productInstNo = this.modalInfo.productInstNo || '';
            this.loginForm.endpointId = this.modalInfo.endpointId || '';
            this.loginForm.user = this.modalInfo.user || '';
        },
        handleSubmit(name) {
            this.$refs[name].validate((valid) => {
                if (valid) {
                    this.$emit('login', this.loginForm);
                }
            });
        },
        isShowPassWord(){
            this.showPassWord = !this.showPassWord;
        },
        // 获取接入点信息
        async getMdbEndpoints(){
            try {
                // 获取接入点信息
                const res = await getMdbEndpoints({
                    productId: this.loginForm.productInstNo,
                    protocol: 'T2'
                });
                if (res.success) {
                    this.endpointList = res.data || [];
                } else {
                    this.endpointList = [];
                }
            } catch (error) {
                console.error(error);
                this.endpointList = [];
            }
        },
        // 切换产品节点
        async handleChangeProduct() {
            await this.getMdbEndpoints();
            this.loginForm.endpointId = _.find(this.endpointList, ['id', sessionStorage.getItem('apm.mdbsql.endpointId')])?.id || this.endpointList?.[0]?.id || '';
        },
        // 跳转MDB配置接入点
        goLink(){
            this.$hCore.navigate(`/productServiceList`, { history: true }, {
                menuId: 'mdbAccess'
            });
        }
    },
    components: { aButton, aTips }
};
</script>
<style lang="less" scoped>
/deep/ .h-modal-body {
    padding: 16px;
}

.tips-content {
    margin-bottom: 15px;
}

.link-text {
    color: var(--link-color);
    cursor: pointer;
    padding: 6px 0;
}

.config {
    background-color: #f3f3f3;
    margin-left: 80px;
    padding: 12px 12px 2px 0;

    .h-form-item {
        margin-bottom: 10px;
    }
}

</style>
