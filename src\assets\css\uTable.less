/*
 * 自定义Table组件样式--尽量使用a-table组件
*/
/deep/ .u-table-wrapper {
    width: 100%;
    height: auto;
    max-height: calc(100% - 50px);
    border: 0;
}

/deep/ .u-table {
    background: none;
    border-color: var(--border-color);
    margin: 10px 0;

    &::before {
        background-color: var(--border-color);
        z-index: 3;
        height: 0;
    }

    &::after {
        display: none;
    }

    .spin-loading {
        background-color: var(--input-bg-color);
    }
}

/deep/ .cur-th .gutter {
    background: none !important;
}

/deep/ .u-table th {
    height: 42px;
    background: var(--primary-color);
    border-color: #363c52;
    color: var(--font-color);
    font-size: var(--font-size);
    font-weight: var(--font-weight);
}

/deep/ .u-table td {
    border-bottom: 1px solid var(--border-color);
    border-right: 0;
    background: var(--input-bg-color);
    color: var(--font-color);
}

/deep/ .u-table-row-checked,
/deep/ .u-table-row-checked:hover,
/deep/ .u-table-row-highlight,
/deep/ .u-table-row-highlight:hover,
/deep/ .u-table-row-hover,
/deep/ .u-table-row-hover td {
    background: none !important;
}

/deep/ td .h-btn-text {
    color: var(--link-color);
}

/deep/ .u-table-row-hover .h-btn-text {
    color: var(--link-color);
}

/deep/ td .h-btn-text:hover {
    color: var(--link-color);
    text-decoration: underline;
}

/deep/ .u-table-tip tr td {
    color: #495060;
}

/deep/ .u-table-wrapper > .h-spin-fix {
    position: absolute;
    top: 0;
    left: 0;
    z-index: 8;
    width: 100%;
    height: 100%;
    border: var(--table-border);
    background-color: var(--input-bg-color);
}

/deep/ .u-table-fixed-body-shadow {
    border: none;
}


/deep/ .u-table-fixed-right-patch {
    background-color: var(--input-bg-color);
    border-bottom: var(--table-border);
}
