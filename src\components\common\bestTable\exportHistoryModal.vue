<template>
    <div>
        <!-- 数据导出 -->
        <h-msg-box-safe
            v-model="modalData.status"
            :escClose="true"
            :mask-closable="false"
            title="数据导出"
            width="700"
            height="450"
            @on-open="handleModalOpen"
        >
            <h-table
                height="400"
                border
                :columns="columns"
                :data="tableData"
                ></h-table>
            <div class="footer-line">
                <div style="width: 85px;">文件存储目录：</div>
                <div class="line-path">
                    <div class="line-path-text">{{ fileDirectory }}</div>
                    <span
                        class="line-path-button"
                        :data-clipboard-text="fileDirectory"
                        @click="onCopied">复制</span>
                </div>
                <div style="padding-left: 8px;">
                    占用空间：{{ fileSpace }}
                </div>
                <div>
                    <a-button type="text" style="padding: 0 4px; margin-top: -2px; font-weight: 600;" @click="deleteExportFile">清空数据</a-button>
                </div>
            </div>
            <template v-slot:footer>
                <a-button @click="modalData.status = false">取消</a-button>
            </template>
        </h-msg-box-safe>
    </div>
</template>

<script>
import Clipboard from 'clipboard';
import aButton from '@/components/common/button/aButton';
import { saveCase, getFileExportHistoryList, downloadExportFile, deleteExportFile } from '@/api/httpApi';
export default {
    props: {
        modalInfo: {
            type: Object,
            default: null
        }
    },
    data() {
        return {
            modalData: this.modalInfo,
            loading: false,
            formValidate: {
                name: ''
            },
            columns: [
                {
                    title: '导出时间',
                    key: 'exportTime'
                },
                {
                    title: '导出文件',
                    key: 'exportFileName'
                },
                {
                    title: '文件大小',
                    key: 'exportFileSize'
                },
                {
                    title: '操作',
                    key: 'action',
                    render: (h, params) => {
                        return h('div', [
                            h(
                                'Button',
                                {
                                    props: {
                                        type: 'text',
                                        size: 'small'
                                    },
                                    on: {
                                        click: () => {
                                            this.downloadExportFile(params.row.exportFilePath, params.row.exportFileName);
                                        }
                                    }
                                },
                                '下载'
                            ),
                            h(
                                'Button',
                                {
                                    props: {
                                        type: 'text',
                                        size: 'small'
                                    },
                                    on: {
                                        click: () => {
                                            this.deleteExportFile(params.row.exportFilePath);
                                        }
                                    }
                                },
                                '删除'
                            )
                        ]);
                    }
                }
            ],
            tableData: [],
            fileDirectory: '-',
            fileSpace: '-'
        };
    },
    methods: {
        handleModalOpen() {
            this.getFileExportHistoryList();
        },
        // 获取导出历史
        async getFileExportHistoryList() {
            const param = {
                productInstNo: localStorage.getItem('productInstNo'),
                exportScene: this.modalInfo.exportScene
            };
            try {
                const { data } = await getFileExportHistoryList(param) || {};
                this.fileDirectory = data?.fileDirectory || '-';
                this.fileSpace = data?.fileSpace || '-';
                this.tableData = data?.exportDetailList || [];
            } catch (error) {
                console.log(error);
            }
        },
        // 下载文件
        async downloadExportFile(path, name) {
            this.loading = true;
            try {
                const res = await downloadExportFile({
                    filePath: path
                });
                const blob = new Blob([res]);
                const objectUrl = URL.createObjectURL(blob);
                // 创建a标签链接并点击 start
                const link = document.createElement('a');
                link.style.display = 'none';
                link.href = objectUrl;
                link.download = name;
                document.body.appendChild(link);
                link.click();
                // 创建a标签链接并点击 end
                document.body.removeChild(link);
                window.URL.revokeObjectURL(blob);
            } catch (error) {
                console.error(error);
            }
            this.loading = false;
        },
        // 删除文件
        async deleteExportFile(path) {
            const param = {
                productInstNo: localStorage.getItem('productInstNo'),
                exportScene: this.modalInfo.exportScene
            };
            if (path) {
                param.filePath = path;
            } else {
                param.filePath = this.fileDirectory;
            }
            const res = await deleteExportFile(param);
            if (res.success) {
                this.getFileExportHistoryList();
                this.$hMessage.success('操作成功');
            } else {
                this.$hMessage.error(res.message || '操作失败');
            }
        },
        // 复制地址
        onCopied() {
            const clipBoard = new Clipboard('.line-path-button');
            clipBoard.on('success', (e) => {
                clipBoard.destroy();
            });
            clipBoard.on('error', (e) => {
                this.$hMessage.error('该浏览器不支持复制');
                clipBoard.destroy();
            });
        },
        submitConfig() {
            const that = this;
            this.$refs['formValidate'].validate(async (valid) => {
                if (valid) {
                    if (this.formValidate.name.length > 20) {
                        this.$hMessage.error('字符长度数不得超过20！');
                        return;
                    }
                    that.loading = true;
                    try {
                        const res = await saveCase({
                            caseName: this.formValidate.name,
                            sceneId: this.modalInfo.sceneId
                        });
                        if (res.success) {
                            this.$emit('update', res?.data?.sceneId);
                            that.$hMessage.success('用例创建成功!');
                            that.modalInfo.status = false;
                            this.$emit('query', this.modalInfo.sceneId);
                        } else {
                            that.loading = false;
                            this.$hMessage.error('用例创建失败!');
                        }
                    } catch (err) {
                        that.loading = false;
                    }
                }
            });
        }
    },
    components: { aButton }
};
</script>
<style lang="less" scoped>
    /deep/ .h-modal-body {
        padding: 16px 16px 0;
        user-select: text;
    }

    .export-title {
        font-weight: 600;
        border-bottom: 1px solid #ccc;
        padding-bottom: 6px;
    }

    .export-form-box {
        height: 200px;
        overflow: auto;
    }

    .footer-line {
        display: flex;
        margin-top: 4px;

        .line-path {
            display: flex;
            max-width: 370px;

            .line-path-text {
                display: inline-block;
                max-width: 330px;
                overflow: hidden;
                text-overflow: ellipsis;
                white-space: nowrap;
            }

            .line-path-button {
                margin-left: 4px;
                padding: 0 4px;
                cursor: pointer;
                font-weight: 600;

                &:hover {
                    color: var(--link-color);
                }

                &:active {
                    color: var(--link-opacity-color);
                }
            }
        }
    }
</style>
