<template>
    <div class="tab-box">
        <a-loading v-if="loading"></a-loading>
        <div v-else>
            <!--  GetNetworkServiceConfig 网络配置信息 -->
            <description-bar :data="description" style="margin: 0;"></description-bar>
            <!--  GetFuncInfo 业务功能信息 -->
            <obs-table :title="funcTable.title" showTitle :tableData="funcTable.tableData" :columns="funcTable.columns" notSetWidth autoHeadWidth :maxHeight="220" />
            <!-- GetBizDataInfo 业务数据组件信息 -->
            <obs-table :title="dataModuleTable.title" showTitle :tableData="dataModuleTable.tableData" :columns="dataModuleTable.columns" :maxHeight="220"/>
            <!--  GetBizModuleInfo 业务功能组件信息 -->
            <obs-table :title="funcModuleTable.title" showTitle :tableData="funcModuleTable.tableData" :columns="funcModuleTable.columns" :maxHeight="220"/>
        </div>
    </div>
</template>

<script>
import aLoading from '@/components/common/loading/aLoading';
import obsTable from '@/components/common/obsTable/obsTable';
import descriptionBar from '@/components/common/description/descriptionBar';
import { getManagerProxy } from '@/api/mcApi';
export default {
    name: 'NodeDeploy',
    components: { aLoading, obsTable, descriptionBar },
    props: {
        nodeData: {
            type: Object,
            default: () => { }
        },
        pollTime: {
            type: Number,
            default: 0
        }
    },
    data() {
        return {
            loading: true,
            description: {
                title: {
                    label: '网络配置信息'
                },
                details: [
                    {
                        dataDic: [
                            {
                                label: '服务器侦听地址',
                                key: 'Address'
                            },
                            {
                                label: '服务器侦听端口',
                                key: 'Port'
                            },
                            {
                                label: '是否端口复用',
                                key: 'SocketReuseAddress'
                            },
                            {
                                label: '心跳时间间隔',
                                key: 'HeartbeatIntervalSec'
                            },
                            {
                                label: '心跳超时次数',
                                key: 'HeartbeatTimes'
                            },
                            {
                                label: '最大连接数',
                                key: 'MaxConnectCount'
                            },
                            {
                                label: '最大发送缓存',
                                key: 'MaxSendBuffSizeBytes'
                            },
                            {
                                label: '最小接收缓存',
                                key: 'MinRecvBuffSizeBytes'
                            },
                            {
                                label: '是否合并收发线程',
                                key: 'MergeSendRecvThread'
                            },
                            {
                                label: '是否异步发送消息',
                                key: 'EnableAsyncSend'
                            },
                            {
                                label: '是否采用直接接收模式',
                                key: 'EnableDirectRecv'
                            },
                            {
                                label: '发送等待时间',
                                key: 'SendWaitTimeMilli'
                            },
                            {
                                label: '收发线程个数',
                                key: 'SendRecvThreadCount'
                            },
                            {
                                label: '发送线程个数',
                                key: 'SendThreadCount'
                            },
                            {
                                label: '接收线程个数',
                                key: 'RecvThreadCount'
                            },
                            {
                                label: '最大连接数量',
                                key: 'MaxConnectPerAcceptor'
                            },
                            {
                                label: '绑定CPU序号',
                                key: 'ThreadCoreBind'
                            },
                            {
                                label: '是否启用快速ACK',
                                key: 'EnableQuickAck'
                            },
                            {
                                label: '是否启用Nagle',
                                key: 'EnableNagle'
                            }
                        ],
                        data: {}
                    }
                ],
                data: {}
            },
            funcTable: {
                title: {
                    label: '业务功能信息'
                },
                tableData: [],
                columns: [
                    {
                        title: '功能号名',
                        key: 'FunctionName',
                        ellipsis: true,
                        minWidth: 200
                    },
                    {
                        title: '功能号',
                        key: 'FunctionNo',
                        ellipsis: true
                    },
                    {
                        title: '子系统号',
                        key: 'SystemNo',
                        ellipsis: true
                    },
                    {
                        title: '读写标志',
                        key: 'Flag',
                        ellipsis: true
                    },
                    {
                        title: '定位模式',
                        key: 'LocateMode',
                        ellipsis: true
                    },
                    {
                        title: '请求结构',
                        key: 'ReqStruct',
                        ellipsis: true
                    },
                    {
                        title: '请求可选字段',
                        key: 'ReqOptionalFields',
                        ellipsis: true
                    },
                    {
                        title: '应答结构',
                        key: 'RspStruct',
                        ellipsis: true
                    },
                    {
                        title: '应答可选字段',
                        key: 'RspOptionalFields',
                        ellipsis: true
                    }
                ]
            },
            dataModuleTable: {
                title: {
                    label: '业务数据组件信息'
                },
                tableData: [],
                columns: [
                    {
                        title: '路径',
                        key: 'Path',
                        minWidth: 400
                    },
                    {
                        title: '版本',
                        key: 'Version',
                        minWidth: 220
                    },
                    {
                        title: '子系统号',
                        key: 'SystemNo'
                    },
                    {
                        title: '是否初始化',
                        key: 'Inited'
                    }
                ]
            },
            funcModuleTable: {
                title: {
                    label: '业务功能组件信息'
                },
                tableData: [],
                columns: [
                    {
                        title: '路径',
                        key: 'Path',
                        minWidth: 400
                    },
                    {
                        title: '版本',
                        key: 'Version',
                        minWidth: 220
                    },
                    {
                        title: '子系统号',
                        key: 'SystemNo'
                    },
                    {
                        title: '是否初始化',
                        key: 'Inited'
                    }
                ]
            }
        };
    },
    mounted() {

    },
    methods: {
        async initData() {
            this.loading = true;
            await this.getFileData();
            this.loading = false;
        },
        // 构造页面数据
        async getFileData() {
            const { GetNetworkServiceConfig, GetFuncInfo, GetBizDataInfo, GetBizModuleInfo } = await this.getAPi();
            this.description.details[0].data = GetNetworkServiceConfig?.ActiveStandby || {};
            this.funcTable.tableData = GetFuncInfo?.GetFuncInfo;
            this.dataModuleTable.tableData = GetBizDataInfo?.GetBizDataInfo;
            const funcModuleTableData = [];
            Array.isArray(GetBizModuleInfo?.GetBizModuleInfo) && GetBizModuleInfo.GetBizModuleInfo.forEach(item => {
                item.BizModule.forEach(ele => {
                    funcModuleTableData.push({
                        SystemNo: item?.SystemNo,
                        Inited: ele?.Inited,
                        Path: ele?.Path,
                        Version: ele?.Version
                    });
                });
            });
            this.funcModuleTable.tableData = funcModuleTableData;
        },
        // 接口请求
        async getAPi() {
            const data = {
                GetNetworkServiceConfig: {},
                GetFuncInfo: {},
                GetBizDataInfo: {},
                GetBizModuleInfo: {}
            };
            const funNameList = ['GetNetworkServiceConfig', 'GetFuncInfo', 'GetBizDataInfo', 'GetBizModuleInfo'];
            const param = [];
            funNameList.forEach(item => {
                param.push({
                    manageProxyIp: this.nodeData.manageProxyIp,
                    manageProxyPort: this.nodeData.manageProxyPort,
                    pluginName: 'ldp_bizproc',
                    funcName: item
                });
            });
            try {
                const res = await getManagerProxy(JSON.stringify(param));
                if (res.code === '200') {
                    !res.data?.[0]?.ErrorNo && Object.keys(res.data?.[0]).length && (data.GetNetworkServiceConfig = res.data[0]);
                    !res.data?.[1]?.ErrorNo && Object.keys(res.data?.[1]).length && (data.GetFuncInfo = res.data[1]);
                    !res.data?.[2]?.ErrorNo && Object.keys(res.data?.[2]).length && (data.GetBizDataInfo = res.data[2]);
                    !res.data?.[3]?.ErrorNo && Object.keys(res.data?.[3]).length && (data.GetBizModuleInfo = res.data[3]);
                }
                return data;
            } catch (err) {
                this.$emit('clear');
            }
        }
    }
};
</script>
