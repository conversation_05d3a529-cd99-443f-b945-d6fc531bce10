<template>
    <div class="tab-box">
        <a-loading v-show="loading"></a-loading>
        <div v-show="!loading" class="json-box">
            <json-viewer
                v-if="!loading && !isShowTextFunc(jsonData)"
                :value="jsonData"
                :expand-depth="10"
                copyable
                :expanded="true"
            >
            </json-viewer>
            <monaco-code-editor
                v-if="!loading && isShowTextFunc(jsonData)"
                ref="monaco-code-editor"
                :options="options"
                :value="JSON.stringify(jsonData || {}, null, 4)"
                language="json"/>
        </div>
    </div>
</template>

<script>
import aLoading from '@/components/common/loading/aLoading';
import { getManagerProxy } from '@/api/mcApi';
import { getByteSize } from '@/utils/utils';
import jsonViewer from 'vue-json-viewer';
import MonacoCodeEditor from '@/components/locateConfig/MonacoCodeEditor.vue';
export default {
    name: 'ZkConfig',
    components: { aLoading, jsonViewer, MonacoCodeEditor },
    props: {
        nodeData: {
            type: Object,
            default: () => { }
        },
        pollTime: {
            type: Number,
            default: 0
        }
    },
    data() {
        return {
            loading: true,
            jsonData: '',
            options: {
                readOnly: true
            }
        };
    },
    mounted() {
    },
    methods: {
        async initData() {
            this.loading = true;
            this.jsonData = await this.getAPi();

            setTimeout(() => {
                this.loading = false;
            }, 500);
        },
        // 计算json大小，判断文本展示类型
        isShowTextFunc(data) {
            const jsonStr = JSON.stringify(data);
            if (getByteSize(jsonStr) > 600 * 1024) {
                return true;
            }
            return false;
        },
        // 管理功能Mproxy插件的GetAppJsonInfo数据
        async getAPi() {
            let data = {};
            const param = [
                {
                    manageProxyIp: this.nodeData.manageProxyIp,
                    manageProxyPort: this.nodeData.manageProxyPort,
                    pluginName: 'ldp_mproxy',
                    funcName: 'GetAppJsonInfo'
                }
            ];
            try {
                const res = await getManagerProxy(JSON.stringify(param));
                if (res.code === '200') {
                    !res.data?.[0]?.ErrorNo && Object.keys(res.data?.[0]).length && (data = res.data[0]);
                    return data;
                }
            } catch (err) {
                this.$emit('clear');
            }
        }
    }
};
</script>

<style lang="less" scoped>
@import url("@/assets/css/json-view.less");

.tab-box {
    position: relative;
    width: 100%;
    height: 100% !important;
    overflow-y: auto;

    .json-box {
        height: calc(100% - 14px);

        /deep/ .jv-container {
            height: 100%;
        }
    }
}
</style>
