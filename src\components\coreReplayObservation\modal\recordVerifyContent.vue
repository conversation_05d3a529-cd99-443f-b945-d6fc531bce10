<template>
    <div class="container">
      <!-- 交易日列表 -->
      <!-- <div class="trading-days">
        <h-tabs
          v-model="tabName"
          panelRight
          :labelWidth="100"
          alginDre="left"
          class="tab-container"
          @on-click="handleTabChange"
        >
          <h-tab-pane
            v-for="item in Object.keys(tradeDayList)"
            :key="item"
            :label="item"
            :name="item"
          ></h-tab-pane>
        </h-tabs>
      </div> -->

      <!-- 核心列表 -->
      <div class="table-select">
        <div class="table-select-title">
          <h-checkbox
            :indeterminate="indeterminate"
            :value="checkAll"
            :title="tabName"
            :disabled="!tabName"
            @click.prevent.native="handleCheckAll"
            >全选</h-checkbox
          >
          <h-input
            v-model="searchInput"
            filterable
            placeholder="输入核心名称查询"
            :positionFixed="true"
            icon="search"
            class="search-input"
            :disabled="!tabName"
            @on-change="handleTableNameSearch"
          ></h-input>
        </div>
        <div class="checkbox-container">
          <h-checkbox-group
            v-show="searchTableList.length"
            v-model="tables[tabName]"
            class="checkbox-group"
            @on-change="checkGroupChange"
          >
            <h-checkbox
              v-for="item in searchTableList"
              :key="item"
              :label="item"
              :title="item"
              class="checkbox-item"
            >
            </h-checkbox>
          </h-checkbox-group>
          <no-data v-show="!searchTableList.length"></no-data>
        </div>
      </div>
    </div>
  </template>

<script>
import _ from 'lodash';
import noData from '@/components/common/noData/noData';
export default {
    name: 'RecordVerifyContent',
    components: { noData },
    props: {
        tradeDayList: {
            type: Object,
            default: () => ({})
        }
    },
    data() {
        return {
            tabName: Object.keys(this.tradeDayList)?.[0] || '',
            searchTableList: [],
            tables: {},
            indeterminate: false,
            checkAll: false,
            searchInput: ''
        };
    },
    mounted() {
    },
    methods: {
        initData(params) {
            this.tables = params ? _.cloneDeep(params) : _.cloneDeep(this.tradeDayList);
            this.$emit('choose-tables', this.tables);
            this.tabName = Object.keys(this.tables)?.[0] || '';
            if (this.tabName) {
                this.handleTabChange(this.tabName);
            }
        },
        clearData(){
            this.tabName = '';
            this.searchTableList = [];
            this.tables = {};
            this.searchInput = '';
            this.indeterminate = false;
            this.checkAll = false;
        },
        handleTabChange(name) {
            this.tabName = name;
            this.searchInput = '';
            this.handleTableNameSearch();
        },
        handleTableNameSearch() {
            const tableList = this.tradeDayList[this.tabName] || [];
            const searchInputLower = this.searchInput.toLowerCase();
            this.searchTableList = searchInputLower
                ? tableList.filter((item) =>
                    item.toLowerCase().includes(searchInputLower)
                )
                : [...tableList];
            this.handleLinkage();
        },
        // 设置全选、全不选
        handleCheckAll() {
            if (this.indeterminate) {
                this.checkAll = false;
            } else {
                this.checkAll = !this.checkAll;
            }
            this.indeterminate = false;

            if (this.checkAll) {
                this.tables[this.tabName] = Array.from(
                    new Set([...this.tables[this.tabName], ...this.searchTableList])
                );
            } else {
                this.searchTableList.forEach((o) => {
                    const index = this.tables[this.tabName].findIndex((v) => o === v);
                    if (index > -1) {
                        this.tables[this.tabName].splice(index, 1);
                    }
                });
            }
            this.handleLinkage();
            this.$emit('choose-tables', this.tables);
        },
        // 切换checkbox-group内容
        checkGroupChange(data) {
            this.tables[this.tabName] = [...data];
            this.handleLinkage();
            this.$emit('choose-tables', this.tables);
        },
        // 根据checkbox数据联动展示全选、半选状态
        handleLinkage() {
            const tables = this.tables[this.tabName] || [];
            const resSome = this.searchTableList.some((o) => tables.includes(o));
            // 没有一个被选中或者筛选出的节点没有一个在已选中列表里
            if (!tables.length || !resSome) {
                this.indeterminate = false;
                this.checkAll = false;
                return;
            }

            // 筛选出来的节点都已经被选中
            const resEvery = this.searchTableList.every((o) => tables.includes(o));
            if (resEvery) {
                this.indeterminate = false;
                this.checkAll = true;
            } else {
                this.indeterminate = true;
                this.checkAll = false;
            }
        }
    }
};
</script>

  <style lang="less" scoped>
    .container {
        display: flex;
    }

    .trading-days,
    .table-select {
        // background: #262d43;
        border: 1px solid #485565;
    }

    .trading-days {
        width: 15%;

        .tab-container {
            height: 300px;
            background: #262d43;

            /deep/.h-tabs-nav-right .h-tabs-tab-active i,
            /deep/.h-tabs-nav-right .h-tabs-tab:hover i {
                display: none;
            }

            /deep/ .h-tabs-bar-right {
                height: 300px;
                overflow-y: auto;
            }

            /deep/.h-tabs-nav-right {
                font-family: PingFangSC-Regular;
                font-size: 12px;
                color: #fff;
                padding-right: 0;
            }

            /deep/.h-tabs-content-right {
                border-left: 1px solid #414d5d;
            }

            /deep/.h-tabs-nav-right .h-tabs-tab-active {
                background: #1f3759;

                &::after {
                    position: absolute;
                    top: 0;
                    left: 0;
                    content: "";
                    width: 4px;
                    height: 36px;
                    background: var(--link-color);
                }
            }

            /deep/.h-tabs-content-right .h-tabs-tabpane {
                padding: 0;
            }

            /deep/.h-tabs-nav-right .h-tabs-tab-alginleft {
                overflow: hidden;
                text-overflow: ellipsis;
                white-space: nowrap;
            }

            /deep/ .h-tabs-nav-right .h-tabs-tab:hover {
                background: #1f3759;
                cursor: pointer;

                &::after {
                    position: absolute;
                    top: 0;
                    left: 0;
                    content: "";
                    width: 4px;
                    height: 36px;
                    background: var(--link-color);
                }
            }
        }
    }

    .table-select {
        flex: 1;

        .table-select-title {
            height: 36px;
            background: #2c334a;
            border-radius: 4px 4px 0 0;
            border-bottom: 1px solid #444a60;
            padding: 1px 10px;
        }

        .search-input {
            float: right;
            width: 30%;
            max-width: 150px;

            /deep/ .h-input {
                border: var(--border) !important;
            }

            /deep/ .h-input-icon {
                color: #9ea7b4;
            }
        }

        .checkbox-group {
            display: flex;
            flex-wrap: wrap;
            align-items: flex-start;
            justify-content: flex-start;
            width: 100%;
        }

        .checkbox-group .checkbox-item {
            margin: 5px;
            box-sizing: border-box;
        }

        .checkbox-container {
            height: 262px;
            overflow-y: auto;
        }
    }
  </style>
