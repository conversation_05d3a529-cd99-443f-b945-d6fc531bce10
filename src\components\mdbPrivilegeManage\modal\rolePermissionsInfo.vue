<template>
  <div class="role-permission">
    <div class="role-left">
        <div v-if="treeLoading" class="demo-spin-container">
            <h-spin fix>
                <h-icon name="load-c" size="18" class="demo-spin-icon-load"></h-icon>
                <div>Loading</div>
            </h-spin>
        </div>
      <h-input
        v-model.trim="filterText"
        placeholder="请输入集群名"
        icon="search"
        class="search-bar"
      ></h-input>
      <h-tree  ref="tree" class="menu-tree" :data="clusterList" isAlwaysSelect :filter-node-method="filterNode" @on-select-change="handleSelectChange"></h-tree>
    </div>
    <div ref="table-box" class="role-right">
        <div v-if="loading" class="demo-spin-container">
            <h-spin fix>
                <h-icon name="load-c" size="18" class="demo-spin-icon-load"></h-icon>
                <div>Loading</div>
            </h-spin>
        </div>
      <div v-if="selectedclusterName">
        <div class="role-right-title-box">
          <div class="role-right-box-title">{{ selectedclusterName }}</div>
          <div class="role-right-box-config">
                <span v-show="!isReadonly && type === 'edit'">
                    <a-button type="ghost" :disabled="isReadonly" @click="copyConfig">复制配置</a-button>&nbsp;&nbsp;&nbsp;
                    <h-button-group class="select-icon" >
                        <a-button type="ghost" :disabled="isReadonly" @click="()=>checkAllSelectChange(true)">全选</a-button>
                        <a-button type="ghost" :disabled="isReadonly" @click="()=>checkAllSelectChange(false)">全不选</a-button>
                    </h-button-group>&nbsp;&nbsp;&nbsp;
                </span>
                <h-input
                    v-model.trim="searchTableName"
                    placeholder="请输入表名"
                    icon="search"
                    class="input"
                    style="width: 200px;"
                    @on-blur="setRolesPrivilege"
                    @on-click="setRolesPrivilege"
                />
          </div>
        </div>
        <h-simple-table
          :columns="type !== 'edit' ? viewColums : columns"
          :data="tableData"
          :height="type !== 'edit' ? '390' : '320'"
          showTitle
          :loading="tableLoading"
        ></h-simple-table>
      </div>
      <no-data v-else isWhite style="height: 390px;" text="请选择集群"></no-data>
    </div>
    <copy-config-modal v-if="copyConfigInfo.status" :modalInfo="copyConfigInfo" @copy-config="copyTableEditInfosByClusterIds"/>
  </div>
</template>

<script>
import _ from 'lodash';
import noData from '@/components/common/noData/noData';
import aButton from '@/components/common/button/aButton';
import copyConfigModal from '@/components/mdbPrivilegeManage/modal/copyConfigModal';
import {
    getClusterList,
    getRolesPrivilege,
    getTablesByClusterId
} from '@/api/mdbPrivilegeApi';
export default {
    name: 'RolePermissionsInfo',
    components: { noData, aButton, copyConfigModal },
    props: {
        productId: {
            type: String,
            default: ''
        },
        roleId: {
            type: String,
            default: ''
        },
        isReadonly: {
            type: Boolean,
            default: false
        },
        type: {
            type: String,
            default: 'edit'
        }
    },
    data() {
        const that = this;
        const setRender = (h, params, key) => {
            return h('h-checkbox', {
                props: {
                    value: params.row?.[key],
                    disabled: that.isReadonly
                },
                on: {
                    'on-change': (val) => {
                        this.enableChange(key, val, params.row?.tableCategory);
                    }
                }
            });
        };
        const setRenderHeader = (h, params) => {
            return h(
                'h-checkbox',
                {
                    props: {
                        value: that?.[params.column.title + 'All'],
                        disabled: !that.tableData?.length || that.isReadonly
                    },
                    on: {
                        'on-change': (val) => {
                            this.enableAllChange(params.column.title, val);
                        }
                    }
                },
                `${params.column.title}`
            );
        };
        return {
            loading: false,
            treeLoading: false,
            // tree
            filterText: '',
            selectedclusterId: '',
            selectedclusterName: '',
            // 选择表--表格
            tableLoading: false,
            searchTableName: '',
            selectAll: false,
            updateAll: false,
            insertAll: false,
            deleteAll: false,
            columns: [
                {
                    title: '表名',
                    key: 'tableName',
                    ellipsis: true
                },
                {
                    title: 'select',
                    key: 'select',
                    render: (h, params) => {
                        return setRender(h, params, 'select');
                    },
                    renderHeader: (h, params) => {
                        return setRenderHeader(h, params);
                    }
                },
                {
                    title: 'update',
                    key: 'update',
                    render: (h, params) => {
                        return setRender(h, params, 'update');
                    },
                    renderHeader: (h, params) => {
                        return setRenderHeader(h, params);
                    }
                },
                {
                    title: 'insert',
                    key: 'insert',
                    render: (h, params) => {
                        return setRender(h, params, 'insert');
                    },
                    renderHeader: (h, params) => {
                        return setRenderHeader(h, params);
                    }
                },
                {
                    title: 'delete',
                    key: 'delete',
                    render: (h, params) => {
                        return setRender(h, params, 'delete');
                    },
                    renderHeader: (h, params) => {
                        return setRenderHeader(h, params);
                    }
                }
            ],
            // 只读表头
            viewColums: [
                {
                    title: '表名',
                    key: 'tableName',
                    ellipsis: true
                },
                {
                    title: '权限',
                    key: 'permission',
                    ellipsis: true,
                    render: (h, params) => {
                        const actions = ['select', 'update', 'insert', 'delete'].filter(action => params?.row?.[action]) || [];
                        return h('span', {
                            attrs: {
                                title: actions.join(',') || '-'
                            }
                        }, actions.join(',') || '-');

                    }
                }
            ],
            allTableData: [],
            tableData: [],
            tableEditInfos: [],
            copyConfigInfo: {
                status: false,
                clusterList: []
            },
            clusterList: []
        };
    },
    mounted() {
    },
    beforeDestroy() {},
    computed: {},
    watch: {
        filterText(val) {
            this.$refs.tree.filter(val);
        }
    },
    methods: {
        // 初始化数据
        async initData() {
            this.treeLoading = true;
            this.clearAllData();
            await this.getClusterList();
            this.treeLoading = false;
            this.clusterList?.[0]?.children?.[0] && this.handleSelectChange([this.clusterList?.[0]?.children?.[0]]);
        },
        // 获取修改结果
        getTableEditInfos() {
            const res = [];
            if (this.tableEditInfos?.length){
                res.push({
                    hasAllTable: false,
                    privileges: [...this.tableEditInfos]
                });
            }
            return res;
        },
        // <----------------------------------------------------------- 接口--------------------------------------------------------------------------->
        // 获取集群列表
        async getClusterList() {
            try {
                const param = {
                    productId: this.productId
                };
                const res = await getClusterList(param);
                if (this.productId !== param.productId) return;
                if (res.code === '200' && Array.isArray(res?.data)) {
                    this.clusterList = this.transferTreeData(res.data);
                } else if (res.code.length === 8){
                    this.$hMessage.error(res.message);
                }
            } catch (err) {
                console.error(err);
            }
        },
        // 将集群信息转换成tree信息
        transferTreeData(data) {
            const treeList = [];
            data.forEach((ele, index) => {
                treeList.push({
                    expand: !index,
                    disabled: true,
                    id: ele.clusterType,
                    title: ele.clusterType,
                    children: this.transferChildTreeData(ele.clusters, index)
                });
            });
            return treeList;
        },
        transferChildTreeData(childData, index) {
            const childTreeList = [];
            childData.forEach((ele, idx) => {
                childTreeList.push({
                    id: ele.id,
                    title: ele.clusterName,
                    selected: !index && !idx
                });
            });
            return childTreeList;
        },
        // 获取mdb表初始权限、匹配表权限、回显缓存数据
        async getRolesPrivilege(){
            await this.getTablesByClusterId();
            try {
                const param = {
                    productId: this.productId,
                    roleId: this.roleId,
                    clusterId: this.selectedclusterId
                };
                const res = await getRolesPrivilege(param);
                if (this.productId !== param.productId || this.roleId !== param.roleId || this.selectedclusterId !== param.clusterId) return;
                if (res.code === '200') {
                    for (const [index, item] of Object.entries(this.allTableData || [])) {
                        const privileges = _.filter(res?.data?.privileges || [], o => { return o?.tableCategory === item?.tableCategory; })?.[0] || {};
                        const editInfo = _.filter(this.tableEditInfos || [], o => { return o?.clusterId === this.selectedclusterId && o?.tableCategory === item?.tableCategory; })?.[0] || {};
                        this.$set(this.allTableData, index, { ...item, ...privileges, ...editInfo, id: privileges?.id || '' });
                    }
                } else if (res.code.length === 8){
                    this.$hMessage.error(res.message);
                }
            } catch (err) {
                console.error(err);
            }
        },
        // 获取当前集群下所有的表
        async getTablesByClusterId(){
            const param = {
                productId: this.productId,
                clusterId: this.selectedclusterId
            };
            const res = await getTablesByClusterId(param);
            if (this.productId !== param.productId || this.selectedclusterId !== param.clusterId) return;
            if (res.code === '200'){
                this.allTableData = [];
                res.data.forEach(o => {
                    this.allTableData.push({
                        ...o,
                        clusterId: this.selectedclusterId,
                        select: false,
                        update: false,
                        insert: false,
                        delete: false
                    });
                });
            }
        },
        // <-----------------------------------------------------------  修改缓存相关  --------------------------------------------------------------------------->
        // 状态修改
        // eslint-disable-next-line max-params
        enableChange(key, value, tableCategory, isAllChange = false) {
            const index = this.allTableData.findIndex((o) => o?.tableCategory === tableCategory);
            this.$set(this.allTableData, index, { ...this.allTableData[index], [key]: value });
            if (!isAllChange) {
                this[key + 'All'] = this.allTableData?.length ? this.allTableData.every((o) => o[key] === true) : false;
                this.setRolesPrivilege();
            }
            this.setTableEditInfos(index, tableCategory, this.selectedclusterId);
        },
        // 缓存修改
        // eslint-disable-next-line max-params
        setTableEditInfos(index, tableCategory, clusterId, isCopy = false){
            const infoIndex = this.getEditInfoIndex(tableCategory, clusterId);
            if (infoIndex !== -1) {
                this.tableEditInfos[infoIndex] = isCopy ? { ...this.tableEditInfos?.[infoIndex], ...this.allTableData[index], clusterId: clusterId, id: '' } : { ...this.tableEditInfos?.[infoIndex], ...this.allTableData[index], clusterId: clusterId };
            } else {
                this.tableEditInfos.push(isCopy ? { ...this.allTableData[index], clusterId: clusterId, id: '' } : { ...this.allTableData[index], clusterId: clusterId });
            }
        },
        // 复制集群数据
        copyConfig(){
            if (!this.allTableData?.length) return;
            const clusterList = this.findSiblingsClusterData();
            this.copyConfigInfo = {
                status: true,
                clusterName: this.selectedclusterName,
                clusterList
            };
        },
        findSiblingsClusterData() {
            for (const node of Object.values(this.clusterList || [])) {
                const targetNode = node?.children?.findIndex(o => o.id === this.selectedclusterId);
                if (targetNode !== -1){
                    return node?.children?.filter(o => o.id !== this.selectedclusterId) || [];
                }
            }
            return [];
        },
        copyTableEditInfosByClusterIds(clusterIds){
            if (!clusterIds?.length) return;
            this.treeLoading = true;
            this.loading = true;
            const data = _.cloneDeep(this.allTableData);
            for (const index of Object.keys(data)) {
                for (const clusterId of Object.values(clusterIds)) {
                    this.setTableEditInfos(index, data[index]?.tableCategory, clusterId, true);
                }
            }
            this.$hMessage.success('复制成功');
            setTimeout(() => {
                this.treeLoading = false;
                this.loading = false;
            },  500);
        },
        // 全选、取消全选
        enableAllChange(key, value) {
            this[key + 'All'] = value;
            const data = _.cloneDeep(this.tableData);
            for (const index of Object.keys(data)) {
                this.enableChange(key, value, data[index]?.tableCategory, true);
            }
            this.setRolesPrivilege();
        },
        // 搜索节点
        filterNode(val, data, node) {
            if (!val) return true;
            return !node.disabled && node.title.indexOf(val) !== -1;
        },
        // 设置表格数据
        setRolesPrivilege(){
            const searchTable = this.searchTableName ? _.filter([...(this.allTableData || [])], o => { return o?.tableName?.includes(this.searchTableName); }) : [...(this.allTableData || [])];
            this.tableData = [...searchTable];
            this.checkAllChange();
        },
        // 切换集群
        async handleSelectChange(list) {
            this.loading = true;
            if (list?.[0]?.disabled) return;
            this.clearData();
            this.selectedclusterId = list?.[0]?.id;
            this.selectedclusterName = list?.[0]?.title;
            await this.getRolesPrivilege();
            this.setRolesPrivilege();
            this.loading = false;
        },
        // <-----------------------------------------------------------  表切换相关  --------------------------------------------------------------------------->
        // 找寻缓存位置
        getEditInfoIndex(tableCategory, clusterId){
            return this.tableEditInfos.findIndex((o) => o?.clusterId === clusterId && o?.tableCategory === tableCategory);
        },
        // 清理数据
        clearData() {
            this.searchTableName = '';
            this.selectAll = false;
            this.updateAll = false;
            this.insertAll = false;
            this.deleteAll = false;
            this.allTableData = [];
            this.tableData = [];
        },
        // 清理新修改的数据
        clearSaveData() {
            this.filterText = '';
            this.selectedclusterId = '';
            this.selectedclusterName = '';
            this.clusterList = [];
            this.tableEditInfos = [];
        },
        // 清理所有数据
        clearAllData() {
            this.clearData();
            this.clearSaveData();
        },
        // 选择表头全状态联动
        checkAllChange() {
            const hasTableData = this.tableData?.length > 0;
            const updateFlag = flag => hasTableData ? this.tableData.every(o => o?.[flag]) : false;
            this.selectAll = updateFlag('select');
            this.updateAll = updateFlag('update');
            this.insertAll = updateFlag('insert');
            this.deleteAll = updateFlag('delete');
        },
        // 全选/不选
        checkAllSelectChange(val){
            if (!this.tableData?.length) return;
            this.loading = true;
            const data = _.cloneDeep(this.tableData);
            for (const index of Object.keys(data)) {
                this.enableChange('select', val, data[index]?.tableCategory, true);
                this.enableChange('update', val, data[index]?.tableCategory, true);
                this.enableChange('insert', val, data[index]?.tableCategory, true);
                this.enableChange('delete', val, data[index]?.tableCategory, true);
            }
            this.setRolesPrivilege();
            this.loading = false;
        }
    }
};
</script>
<style lang="less" scoped>
@import url("@/assets/css/menu.less");

/deep/ .h-input-icon {
    cursor: pointer;

    &:hover {
        color: var(--link-color);
    }
}

.role-permission {
    width: 100%;
    height: 100%;
    display: flex;

    .role-left {
        width: 20%;
        height: auto;
        padding: 5px 15px 15px 10px;
        border: 1px solid #e3e3e3;
        position: relative;

        // 菜单搜索栏
        .search-bar {
            height: 44px;
            padding: 5px 5px 10px;
            margin-bottom: 5px;
            border-bottom: 1px solid #e3e3e3;
        }

        /deep/ .h-tree > li {
            min-height: 25px;
        }

        // 菜单树组件
        .menu-tree {
            height: calc(100% - 44px);
            overflow: auto;
            padding: 0 8px;

            /deep/ .h-tree-title {
                font-size: 12px;
                background: none;

                &:hover {
                    background: none;
                }
            }

            /deep/ .h-tree-arrow {
                color: var(--link-color);
                position: relative;

                & > .h-icon-ios-arrow-right::before {
                    content: "\25B8";
                    font-size: 24px;
                    position: relative;
                    bottom: -4px;
                    right: 0;
                }
            }

            /deep/ .h-tree-arrow i {
                transition: none;
            }

            /deep/ .h-tree-arrow-open {
                .h-icon-ios-arrow-right::before {
                    content: "\25B8";
                    font-size: 24px;
                    position: relative;
                    right: -4px;
                    bottom: 0;
                }
            }

            /deep/ .h-tree-children {
                & > li > .h-tree-item > .h-tree-title {
                    font-size: 12px;

                    &:hover {
                        background: none;
                    }
                }

                .h-tree-title-selected {
                    color: var(--link-color);
                    font-weight: 600;
                }
            }

            /deep/.h-tree-title-disabled,
            .h-tree-title-disabled:hover {
                color: #575e6c;
                cursor: not-allowed;
                background-color: transparent;
            }
        }
    }

    .role-right {
        width: calc(80% - 5px);
        height: 100%;
        padding: 5px 15px 15px 10px;
        margin-left: 5px;
        border: 1px solid #e3e3e3;
        position: relative;

        .role-right-title-box {
            position: relative;
            height: 44px;
            line-height: 44px;
            margin-bottom: 10px;

            & > .role-right-box-title {
                padding: 0 0 16px 20px;
                font-size: 14px;
                font-weight: 600;

                &::before {
                    content: "";
                    display: inline-block;
                    position: absolute;
                    left: 0;
                    top: 11px;
                    width: 4px;
                    height: 20px;
                    background: var(--link-color);
                }
            }

            .role-right-box-config {
                position: absolute;
                right: 0;
                top: 0;
            }
        }
    }

    /deep/ .h-checkbox-disabled.h-checkbox-checked .h-checkbox-inner {
        border-color: #298dff;
        background-color: #298dff;
    }

    /deep/ .h-checkbox-disabled.h-checkbox-checked .h-checkbox-inner::after {
        border-color: #fff;
    }

    .icon-button.h-btn.h-btn-ghost {
        padding: 0 6px;
    }

    /deep/ .h-btn-group .h-btn-icon-only .iconfont {
        font-size: 19px;
    }

    .select-icon {
        position: relative;
        top: 0;
    }
}
</style>
