<template>
    <div ref="wall" class="masonry-wall" :style="_style.wall" >
        <div v-for="(lane, index) in columns" :key="index" class="masonry-column" :style="_style.lane">
            <div v-for="i in lane.indexes" :key="i" :ref="`item_${i}`" class="masonry-item" :style="_style.item">
                <slot :item="items[i]" :index="i">{{ items[i] }}</slot>
            </div>
        </div>
    </div>
</template>

<script>
export default {
    props: {
        items: {
            type: Array,
            required: true
        },
        options: {
            type: Object,
            required: false
        },
        // 1.指定 容器中内容展示列数
        cols: {
            type: Number,
            default: 0
        },
        // 2.固定宽度
        colWidth: {
            type: Number,
            default: 0
        },
        // 3.适应宽度
        autoFixed: {
            type: Boolean,
            default: false
        },
        gap: {
            type: Number,
            default: 5
        }
    },
    data() {
        return {
            columns: [],
            cursor: 0
        };
    },
    mounted() {
        window.addEventListener('resize', this.resize);
        this.$nextTick(() => {
            this.init();
        });
    },
    beforeDestroy() {
        window.removeEventListener('resize', this.resize);
    },
    computed: {
        _style() {
            const padding = this.gap;
            return {
                wall: {
                    margin: `${padding}px`
                },
                lane: {
                    marginLeft: `${padding}px`,
                    marginRight: `${padding}px`
                },
                item: {
                    marginTop: `${padding}px`,
                    marginBottom: `${padding}px`
                }
            };
        }
    },
    methods: {
        resize() {
            // 由于网格布局拖拽放大缩小页面不能自适应，设置一个定时器使得加载为一个异步过程
            // setTimeout(() => {
            this.$nextTick(() => {
                this.init();
            });
            // }, 0);
        },
        init(){
            const count = this.setCounts();
            if (count > 0) {
                const columns = this._newColumns(count);

                for (let i = 0; i < this.items.length; i++) {
                    columns[i % count].indexes.push(i);
                }
                this.columns = columns;
                this.cursor = this.items.length;
            }
        },
        // 根据容器宽度调整 列
        setCounts(){
            let count = 2;
            // 指定列数
            if (this.cols){
                count = this.cols;
            }
            const wrapContentWidth = this.$refs['wall']?.offsetWidth;
            // 自适应宽度
            if (this.autoFixed){
                if (wrapContentWidth <= 200) {
                    this.cols = 1;
                    return;
                }
                if (wrapContentWidth > 200 && wrapContentWidth <= 500) {
                    this.cols = 2;
                    return;
                }

                if (wrapContentWidth > 500 && wrapContentWidth <= 1000) {
                    this.cols = 3;
                    return;
                }

                if (wrapContentWidth > 1000) {
                    this.cols = 4;
                    return;
                }
                return;
            }
            // 固定宽度
            if (this.colWidth){
                count = Math.floor(wrapContentWidth / this.colWidth);
                const contentWidth = this.colWidth * count;
                const whiteArea = count * this.gap * 4;
                if (contentWidth + whiteArea > wrapContentWidth){
                    count = count - 1;
                }
                if (this.items.length < count){
                    count = this.items.length;
                }
            }

            return count <= 0 ? 1 : count;
        },
        _newColumns (count){
            const columns = [];
            for (let i = 0; i < count; i++) {
                columns.push({ i: i, indexes: [] });
            }
            return columns;
        }
    }
};
</script>

<style  lang="less" scoped>
.masonry-wall {
    display: flex;
}

.masonry-column {
    flex-grow: 1;
    flex-basis: 0;
    display: flex;
    flex-direction: column;
    // flex-grow 属性无效，宽度被撑开问题解决方法
    width: 0;
}
</style>
