<template>
    <div class="main">
        <!-- 头部 -->
        <table-manage-top ref="manage-top" @connect-database="connectDatabase" @success-load="productListCompleted"></table-manage-top>

        <a-loading v-if="loading"></a-loading>
        <!-- 内存表管理 -->
        <menu-layout v-if="isShowTable" ref="menu" :menuList="tableList" titleAttribute="tableName" placeholder="查询内存表" showSearch @check-menu="checkTable">
            <template v-if="boxInfo.tableName" v-slot:right>
                <h-tabs v-model="tabName" class="product-box" @on-click="tabClickQuery(tabName, 'tabClick')">
                    <h-tab-pane label="数据查询" name="query">
                        <table-query ref="table-query" :boxInfo="boxInfo" :databaseName="databaseName" :endpointInfo="endpointInfo"></table-query>
                    </h-tab-pane>
                    <h-tab-pane label="数据修改" name="modify">
                        <table-modify ref="table-modify" :boxInfo="boxInfo" :databaseName="databaseName" :endpointInfo="endpointInfo"></table-modify>
                    </h-tab-pane>
                    <h-tab-pane label="内存表结构" name="structure">
                        <table-structure ref="table-structure"  :boxInfo="boxInfo" :databaseName="databaseName" :endpointInfo="endpointInfo" @updataMetadata="updataMetadata"></table-structure>
                    </h-tab-pane>
                </h-tabs>
            </template>
            <template v-else v-slot:right>
                <div style="height: 100%;">
                  <no-data text="请从左侧菜单选择内存表进行查看！" />
               </div>
            </template>
        </menu-layout>
        <template v-if="!isShowTable">
            <no-data text="请选择并连接应用节点！" />
        </template>
    </div>
</template>

<script>
import { loopAllData } from '@/utils/utils';
import tableManageTop from '@/components/ldpTable/tableManageTop';
import aLoading from '@/components/common/loading/aLoading';
import noData from '@/components/common/noData/noData';
import menuLayout from '@/components/common/menuLayout/menuLayout';
import tableQuery from '@/components/ldpTable/tableQuery.vue';
import tableStructure from '@/components/ldpTable/tableStructure';
import tableModify from '@/components/ldpTable/tableModify';
import { getDatabases, getTables, getTableMetadata } from '@/api/httpApi';
export default {
    data() {
        return {
            endpointInfo: {},
            loading: false,
            isShowTable: false,
            tableList: [],
            databaseName: '', // 数据库名
            boxInfo: {
                tableName: '',
                describe: '',
                expandAttributes: [],
                fields: []
            }, // 表基础信息
            tabName: ''
        };
    },
    async mounted() {
        this.loading = true;
        this.tabName = 'query';
    },
    methods: {
        // 成功获取产品列表
        productListCompleted() {
            const routeParam = localStorage.getItem('productInstNo');
            if (routeParam) {
                this.$refs['manage-top'].routeConnection(routeParam);
            }
            this.loading = false;
        },
        // 连接内存表
        async connectDatabase(endpointInfo) {
            if (endpointInfo?.endpoint) {
                this.endpointInfo = endpointInfo;
                // 调用接口获取库名和表列表
                await this.getTableLists(endpointInfo);
                this.isShowTable = this.tableList.length ? true : false;
                this.boxInfo.tableName = '';
            } else {
                this.isShowTable = false;
            }
        },
        // 获取内存表列表
        async getTableLists(endpointInfo) {
            const param1 = {
                ...endpointInfo,
                tableType: 'ustTable'
            };
            try {
                const res1 = await getDatabases(param1);
                if (!res1.errcode) {
                    this.databaseName = res1.databaseNames[0];
                    const param2 = {
                        databaseName: this.databaseName,
                        ...endpointInfo,
                        tableType: 'ustTable'
                    };
                    try {
                        const res2 = await getTables(param2);
                        if (!res2.errcode) {
                            this.tableList = res2?.tableInfos || [];
                            this.$refs['manage-top'].successLink();
                        }
                    } catch (err) {
                        console.error(err);
                        this.tableList = [];
                        this.$refs['manage-top'].breakLink();
                    }
                }
            } catch (err) {
                console.error(err);
                this.tableList = [];
                this.$refs['manage-top'].breakLink();
            }
        },
        // 切换菜单表名
        async checkTable(item) {
            if (item.tableName === this.boxInfo.tableName) return;
            // 获取表结构信息
            const param = {
                databaseName: this.databaseName,
                tableName: item.tableName,
                ...this.endpointInfo,
                tableType: 'ustTable'
            };
            await getTableMetadata(param).then(res => {
                if (res && res.tableName === item.tableName) {
                    this.boxInfo = { ...res };
                    this.boxInfo.fields = this.montageTableMetadata(res.fields);
                    // 切换表名 预览列表数据初始化
                    this.$nextTick(() => {
                        this.tabClickQuery(this.tabName);
                    });
                }
            }).catch((err) => {
                console.error(err);
                this.boxInfo.tableName = '';
            });
        },
        // 适配修改查看功能-不点击查看预览直接关闭提示
        tabClickQuery(tabName, operation){
            if (tabName === 'query' && operation === 'tabClick') {
                this.$refs[`table-${tabName}`].carryConditionsQuery();
            } else {
                this.$refs[`table-${tabName}`].initData();
            }
        },
        // 内存表字段结构拼接客户端保存的可见及可查询信息
        montageTableMetadata(fields) {
            let data = [];
            const tableFieldConfig = JSON.parse(localStorage.getItem('tableFieldConfig'));
            const configInfo = tableFieldConfig?.[`${this.databaseName}.${this.boxInfo.tableName}`];
            data = this.addAttribute(fields, configInfo);
            !configInfo && this.saveFiledAttrbute(data); // 保存默认配置
            // data = this.addDisabledAttribute(data); // 配置禁用属性
            return data;
        },
        addAttribute(json, source, context = {}) {
            const { nest = false, isTopLevel = false, allTopStruct = false } = context;

            if (Array.isArray(json)) {
                // 处理顶层数组，检查是否全为 struct
                if (context.isTopLevel === undefined) {
                    // 首次递归进入顶层数组时判断全 struct
                    const allStruct = json.every(item => item.type === 'struct');
                    json.forEach(item =>
                        this.addAttribute(item, source, {
                            nest: false,
                            isTopLevel: true,      // 标记为顶层元素
                            allTopStruct: allStruct // 传递全 struct 状态
                        }));
                } else {
                    // 非顶层数组直接递归处理
                    json.forEach(item =>
                        this.addAttribute(item, source, {
                            ...context,
                            nest: true,
                            isTopLevel: false
                        }));
                }
                return json;
            }

            if (json instanceof Object) {
                json.id = json.path;

                if (source) {
                    // 从 source 中取值
                    json.visible = source[json.path]?.visible;
                    json.hasCondition = source[json.path]?.hasCondition;
                } else {
                    // 处理可见性
                    if (isTopLevel) {
                        // 第一层逻辑
                        if (allTopStruct) {
                            // 全为 struct：隐藏自身，子级可见
                            json.visible = false;
                        } else {
                            // 非全 struct：struct 隐藏，其他可见
                            json.visible = (json.type === 'struct') ? false : true;
                        }
                    } else {
                        // 子层级逻辑
                        json.visible = (nest || json.type === 'struct') ? false : true;
                    }

                    // 处理 hasCondition
                    json.hasCondition = (json.type === 'struct' || json.type === 'array') ? false : !nest;
                }

                // 递归处理子级
                if (Array.isArray(json.children)) {
                    const isChildTopLevel = false; // 子级不可能是顶层
                    let childNest = true;

                    if (isTopLevel && allTopStruct) {
                        // 父级为顶层且全 struct，子级可见
                        childNest = false;
                    }

                    json.children.forEach(item => this.addAttribute(item, source, {
                        nest: childNest,
                        isTopLevel: isChildTopLevel,
                        allTopStruct: allTopStruct
                    }));
                }
            }

            return json;
        },
        // 遍历树保存可见和可筛选属性
        saveFiledAttrbute(fields) {
            let tableFieldConfig = JSON.parse(localStorage.getItem('tableFieldConfig'));
            const currentConfig = {};
            loopAllData(fields, (data) => {
                currentConfig[data.path] = {};
                currentConfig[data.path].visible = data?.visible;
                currentConfig[data.path].hasCondition = data?.hasCondition;
            });
            if (!tableFieldConfig) tableFieldConfig = {};
            tableFieldConfig[`${this.databaseName}.${this.boxInfo.tableName}`] = currentConfig;
            localStorage.setItem('tableFieldConfig', JSON.stringify(tableFieldConfig));
        },
        // 修改内存表结构后更新数据
        updataMetadata(data) {
            this.boxInfo = { ...data };
        },
        getPreviewTableData() {
            this.tabName = 'modify';
            this.$refs['table-modify'].initData();
        }
    },
    components: { aLoading, menuLayout, tableQuery, tableModify, tableStructure, tableManageTop, noData }
};
</script>

<style lang="less" scoped>
@import url("@/assets/css/input.less");
@import url("@/assets/css/tab.less");

.apm-box {
    height: calc(100% - 58px);
}
</style>
