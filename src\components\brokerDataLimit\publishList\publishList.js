import './publish.less';
import { getRemoteRuleList, getFuseRulesReleaseTime } from '@/api/brokerApi';
import aTitle from '@/components/common/title/aTitle';
import aTable from '@/components/common/table/aTable';
export default {
    name: 'brokerPublishList',
    props: {
        productId: {
            type: String,
            default: ''
        }
    },
    data() {
        return {
            loading: true,
            blackList: [],
            tpsList: [],
            whiteList: [],
            tableList: [],
            releaseTime: '',
            tableHeight: 0,
            column: [{
                key: 'type',
                title: '名单类型'
            },
            {
                key: 'accountIds',
                title: '资金账号',
                render: (h, params) => {
                    return h('span', {
                        attrs: {
                            title: params.row.ruleMap.accountIds
                        }
                    }, params.row.ruleMap.accountIds);
                }
            }, {
                key: 'functionNos',
                title: '功能号',
                render: (h, params) => {
                    return h('span', {
                        attrs: {
                            title: params.row.ruleMap.functionNos
                        }
                    }, params.row.ruleMap.functionNos);
                }
            }, {
                key: 'shardingNos',
                title: '分片号',
                render: (h, params) => {
                    return h('span', {
                        attrs: {
                            title: params.row.ruleMap.shardingNos
                        }
                    }, params.row.ruleMap.shardingNos);
                }
            }, {
                key: 'fc',
                title: '限流值（条/秒）',
                render: (h, params) => {
                    return h('span', {
                        attrs: {
                            title: params.row.ruleMap.fc
                        }
                    }, params.row.ruleMap.fc);
                }
            }]
        };
    },
    mounted() {
        this.$nextTick(() => {
            this.fetTableHeight();
        });
        window.addEventListener('resize', this.fetTableHeight);
    },
    beforeDestroy() {
        window.removeEventListener('resize', this.fetTableHeight);
    },
    methods: {
        async initData() {
            try {
                this.loading = true;
                // 获取最新发布时间
                const res1 = await getFuseRulesReleaseTime({
                    productId: this.productId
                });
                this.releaseTime = res1?.data?.releaseTime || '';
                // 获取远程配置名单
                const res2 = await getRemoteRuleList({
                    productId: this.productId
                });
                if (res2.code.length === 8) {
                    this.$hMessage.error(res2.message);
                }
                this.blackList = res2?.data?.BlackList || [];
                this.tpsList = res2?.data?.Tps || [];
                this.whiteList = res2?.data?.WhiteList || [];
                this.blackList.forEach(item => {
                    item.type = '黑名单';
                });
                this.whiteList.forEach(item => {
                    item.type = '白名单';
                });
                this.tpsList.forEach(item => {
                    item.type = '限流名单';
                });
                this.tableList = [...this.blackList, ...this.tpsList, ...this.whiteList];
            } catch (error) {
                console.error(error);
            }
            this.loading = false;
        },
        resetHeight() {
            return new Promise((resolve, reject) => {
                this.tableHeight = 0;
                resolve();
            });
        },
        // 设置table高度
        fetTableHeight() {
            this.resetHeight().then(res => {
                this.tableHeight = this.$refs['table-box'].getBoundingClientRect().height - 60;
            });
        }
    },
    render() {
        return <div ref='table-box' style="height: calc(100% - 40px);">
            <div style="text-align: right; color: #fff; padding-right: 10px; font-size: 14px;">最近一次发布时间：{this.releaseTime || '-'}</div>
            <div class='table-box'>
                <div class="table-title">
                    <div class="table-title-text">
                        名单列表
                    </div>
                </div>
                <a-table
                    ref="table1"
                    class="table-Storage"
                    height={this.tableHeight}
                    loading={this.loading}
                    tableData={this.tableList}
                    columns={this.column}
                    hasPage={false} />
            </div>
        </div>;
    },
    components: { aTitle, aTable }
};
