<template>
    <div>
        <!-- 批量编辑弹窗 -->
        <h-msg-box-safe id="sql-conditon-msgbox" v-model="modalData.status" :mask-closable="false" :title="modalData.title" width="600" maxHeight="400">
            <h-form
                ref="formValidate"
                :model="formValidate"
                :rules="ruleValidate"
                :label-width="120">
                <h-form-item label="表名: " prop="ruleName">
                    <span>{{ formValidate.tableName }}</span>
                </h-form-item>
                <h-form-item label="集群名称: " prop="clusterName">
                    <span>{{ formValidate.clusterName }}</span>
                </h-form-item>
                <h-form-item label="加载方式: " prop="loadMode">
                    <h-select
                        v-model="formValidate.loadMode"
                        style="width: 398px;"
                        placeholder="请选择加载方式"
                        :clearable="false"
                        transfer
                    >
                        <h-option v-for="item in loadingTypeOptions" :key="item.label" :value="item.value">{{ item.label }}</h-option>
                    </h-select>
                </h-form-item>
                <h-form-item label="过滤条件: " prop="loadSql">
                    <span>where_str</span>
                    <div style="display: flex; align-items: center;">
                        <h-input
                            v-model="formValidate.loadSql"
                            type="textarea"
                            :canResize="false"
                            style="width: 398px;"
                            :maxlength="1000"
                            placeholder="请输入SQL语句的where条件"
                        ></h-input>
                        <h-poptip placement="bottom-end" :transfer="true" positionFixed targetNode="#sql-conditon-msgbox">
                            <h-icon name="feedback_fill" size="24" color="#999" style="cursor: pointer; margin-left: 6px;"></h-icon>
                            <pre slot="content">{{`1.输入框内请填写sql语句的where条件\n2.条件为空时默认全表上场\n`}}</pre>
                        </h-poptip>
                    </div>
                </h-form-item>
            </h-form>
            <template v-slot:footer>
                <a-button @click="modalData.status = false">取消</a-button>
                <a-button type="primary" :loading="loading" @click="handleSubmit">确定</a-button>
            </template>
        </h-msg-box-safe>
    </div>
</template>

<script>
import { LOADING_TYPE_OPTIONS } from '@/components/secondAppearance/constant';
import aButton from '@/components/common/button/aButton';
import { configLoadDataRule } from '@/api/brokerApi';

export default {
    props: {
        productId: {
            type: String,
            default: ''
        },
        modalInfo: {
            type: Object,
            default: null
        },
        msgBoxData: {
            type: Object,
            default: () => {}
        }
    },
    components: { aButton },
    watch: {
        'modalData.status': {
            handler(){
                this.formValidate = this.msgBoxData;
                setTimeout(() => {
                    this.$refs['formValidate'].resetValidate();
                }, 0);
            }
        }
    },
    data() {
        return {
            loadingTypeOptions: LOADING_TYPE_OPTIONS,
            modalData: this.modalInfo,
            loading: false,
            formValidate: this.msgBoxData,
            ruleValidate: {
                loadMode: [{ required: true, message: '加载方式为必填字段' }]
                // loadSql: [{ required: true, message: '过滤条件为必填字段' }]
            }
        };
    },
    methods: {
        // 调用接口 保存用户的修改内容
        handleSubmit(){
            this.$refs['formValidate'].validate(async (valid) => {
                if (valid) {
                    const param = {
                        productId: this.productId,
                        loadMode: this.formValidate.loadMode,
                        loadSql: this.formValidate.loadSql,
                        tableName: this.formValidate.tableName,
                        clusterName: this.formValidate.clusterName,
                        whereCondition: 'all'
                    };
                    try {
                        this.loading = true;
                        const res = await configLoadDataRule(param);
                        if (res.success){
                            this.$hMessage.success('操作成功');
                            this.$emit('query');
                            this.modalData.status = false;
                        }
                    } catch (error) {
                        console.error(error);
                    }
                    this.loading = false;
                }
            });
        }
    }
};
</script>

<style lang="less" scoped>
    /deep/ .h-modal-body {
        padding: 16px;

        .h-input-wrapper {
            textarea {
                height: 96px;
                line-height: 1.5;
            }
        }
    }
</style>
<style lang="less">
#sql-conditon-msgbox {
    .h-poptip-popper[x-placement^="right"] .h-poptip-arrow {
        border-right-color: #fff !important;
    }

    .h-poptip-popper[x-placement^="right"] .h-poptip-arrow::after {
        border-right-color: #fff !important;
    }
}
</style>
