<template>
    <div class="select-content">
        <h-form ref="formDynamic" :model="formDynamic" :label-width="60" :showTipsOnlyFocus="true">
            <h-row class="form-item-request">
                <h-col span="24">
                    <h-form-item :label="formDynamic.type" prop="url">
                        <h-input v-model='url' placeholder="请输入" disabled :class="['h-input-wrapper', formDynamic.version ? 'with-version' : 'no-version']" titleShow></h-input>
                        <h-input v-if="formDynamic.version" v-model="formDynamic.version" placeholder="请输入" disabled style="width: 80px;" titleShow></h-input>
                        <a-button
                            type="primary"
                            style="display: inline-block;"
                            @click="debounceSend">Send</a-button>
                        <a-button
                            type="dark"
                            style="display: inline-block;"
                            @click="changeShowHidden">Params <h-icon :name="iconType ? 'packup' : 'unfold'" size='14' color="#cacfd4"></h-icon></a-button>&nbsp;
                        <a-button
                            type="dark"
                            class="icon-setting"
                            style="display: inline-block;"
                            title="管理功能使用说明"
                            @click="openAppConfigInfoModal">
                            <svg t="1752109961094" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="3579" width="20" height="20"><path d="M907.008 161.728l0.96 0.256 12.032 3.2c5.76 1.6 9.984 6.016 11.456 11.392l0.512 4.224v185.6c0 7.488-7.168 13.888-14.656 15.552l-3.712 0.448h-29.632a16.064 16.064 0 0 1-15.552-12.352l-0.448-3.648V220.032c-52.352-7.36-182.272-14.272-307.52 66.304l-14.464 9.664v489.984c54.72-28.48 153.152-66.752 298.624-74.24l23.424-0.96v-28.8c0-7.552 5.248-13.632 12.288-15.168l3.648-0.448h29.632c8.576 0 16.256 4.16 18.304 11.648l0.512 3.968v76.032a16.064 16.064 0 0 1-12.352 15.552l-3.648 0.448h-18.432c-201.6 0-316.416 62.784-351.616 85.568-6.4 4.16-9.92 7.04-11.328 8.064l-0.64 0.32-10.368 8.448a16.256 16.256 0 0 1-16.64 2.304l-3.84-2.304-9.92-8.448s-4.032-3.2-11.648-7.936c-33.92-22.016-141.888-81.216-330.304-85.376l-21.312-0.256H112a16.064 16.064 0 0 1-15.552-12.352L96 758.4v-76.416c0-8.576 5.632-13.312 13.44-14.464l4.032-0.32h30.528c7.36 0 13.568 4.16 15.424 10.624l0.576 4.16v29.248c150.464 4.16 253.696 41.152 312.96 70.592l9.024 4.608V296.32C357.184 208.64 224.64 212.032 166.4 219.136l-6.4 0.896v146.368a16.32 16.32 0 0 1-16 16c-8.768 0.384-20.8 0-30.528 0-8.32 0-15.232-3.2-17.024-10.432L96 368V181.184c0-6.08 3.072-11.136 8-13.952l4.032-1.664 11.968-3.2c8.384-2.368 205.632-51.2 393.984 78.08 183.68-126.4 375.744-83.2 393.024-78.72zM193.6 419.648c7.552 0 13.888 5.248 15.552 12.288l0.448 3.648V615.68a16.064 16.064 0 0 1-12.352 15.552l-3.648 0.448L121.6 631.232C79.232 627.2 46.016 581.632 46.016 525.632c0-52.48 29.184-95.872 67.776-104.704l7.808-1.28h72z m708.8 0c42.368 4.352 75.584 49.92 75.584 105.92 0 56.064-33.216 101.632-75.584 105.6l-72 0.448a16 16 0 0 1-16-16V435.584a16 16 0 0 1 16-16H902.4z" p-id="3580"></path></svg>
                        </a-button>
                    </h-form-item>
                </h-col>
            </h-row>
            <div v-if="iconType" class="wrapper-params">
                <h-form-item label="">
                    <div v-for="(item, index) in formDynamic.items" :key="'items' + index">
                        <h-row v-if="item.visible" class="row">
                            <!-- 字段名 -->
                            <h-col span="8">
                                <h-form-item :prop="'items.' + index + '.key'">
                                    <h-input v-model.trim="item.key" placeholder="字段名"
                                        :disabled="isdisabled()"></h-input>
                                </h-form-item>
                            </h-col>
                            <!-- 类型选择/展示 -->
                            <!-- <h-col span="4">
                                <h-form-item :prop="'items.' + index + '.key'"> -->
                                    <!-- 特殊枚举 -->
                                    <!-- <template v-if="isEnumType(item)">
                                        <h-input :value="changeType(item.valueType)" :disabled="isdisabled()" />
                                    </template> -->
                                    <!-- 数据类型 -->
                                    <!-- <template v-else>
                                        <h-select
                                            v-model="item.valueType"
                                            transfer
                                            :disabled="isdisabled()"
                                            :clearable="false"
                                        >
                                            <h-option v-for="opt in typeOptions" :key="opt.value" :label="opt.label" :value="opt.value" />
                                        </h-select>
                                    </template>
                                </h-form-item>
                            </h-col> -->
                            <!-- 字段值 -->
                            <h-col span="8">
                                <h-form-item
                                    :prop="'items.' + index + '.value'"
                                    :transfer="true"
                                    :valid-rules="matchValidate(changeType(item.valueType), item.validateValues)"
                                >
                                    <template v-if="isEnumType(item)">
                                        <h-select
                                            v-model.trim="item.value"
                                            placeholder="字段值"
                                            :transfer="true"
                                            autoPlacement
                                        >
                                            <h-option
                                                v-for="enumItem in transferEnumValues(item.enumValues || [])"
                                                :key="enumItem.key"
                                                :value="enumItem.key"
                                            >{{ enumItem.value }}</h-option>
                                        </h-select>
                                    </template>
                                    <template v-else>
                                        <h-input v-model.trim="item.value" placeholder="字段值" />
                                    </template>
                                </h-form-item>
                            </h-col>
                            <!-- 字段描述 -->
                            <h-col v-show="item.describe" span="1">
                                <h-poptip class="popo-icon" customTransferClassName="apm-poptip monitor-poptip" placement="right" :transfer="true">
                                    <h-icon name="help-circled" size="20" style="position: relative; top: 2px;"/>
                                    <template v-slot:content>
                                        <div style="max-width: 180px; white-space: normal;">
                                            {{ item.describe }}
                                        </div>
                                    </template>
                                </h-poptip>
                            </h-col>
                            <h-col span="1">&nbsp;</h-col>
                            <!-- 删除按钮 -->
                            <h-col span="1">
                                <h-form-item v-show="!isdisabled()">
                                    <a-button
                                        type="error"
                                        shape="circle"
                                        size="small"
                                        icon="android-remove"
                                        style="position: relative; top: 2px;"
                                        @click="handleRemove(index)"
                                    />
                                </h-form-item>
                            </h-col>
                        </h-row>
                    </div>
                    <!-- 新增按钮 -->
                    <h-row v-show="!isdisabled()">
                        <h-col span="12">
                            <h-form-item class="row">
                                <a-button
                                    type="primary"
                                    shape="circle"
                                    size="small"
                                    icon="plus-round"
                                    @click="handleAdd"
                                />
                            </h-form-item>
                        </h-col>
                    </h-row>
                </h-form-item>
            </div>
        </h-form>
        <!-- 管理功能说明 -->
        <app-config-info-drawer
            v-if="showAppConfigInfo.status"
            :modalInfo="showAppConfigInfo"
        />
    </div>
</template>

<script>
import _ from 'lodash';
import aButton from '@/components/common/button/aButton';
import { objectToQueryString, transferVal, stringToArray, stringToObject } from '@/utils/utils';
import { matchMangementValidate } from '@/utils/validate';
import appConfigInfoDrawer from './appConfigInfoDrawer.vue';
export default {
    components: { aButton, appConfigInfoDrawer },
    props: {
        requestUrl: {
            type: String,
            default: ''
        },
        managementData: {
            type: Object,
            default: () => { return {}; }
        },
        manageApiMeta: {
            type: Object,
            default: () => { return {}; }
        }
    },
    data() {
        return {
            formDynamic: {
                items: [],
                version: '',
                type: 'GET'
            },
            typeOptions: [
                { label: 'String', value: 'string' },
                { label: 'Number', value: 'number' },
                { label: 'Boolean', value: 'bool' },
                { label: 'Array', value: 'array' },
                { label: 'Object', value: 'object' }
            ],
            iconType: false, // params 显示
            matchValidate: matchMangementValidate,
            showAppConfigInfo: {
                status: false
            }
        };
    },
    mounted() {
    },
    computed: {
        url() {
            if (this.formDynamic.type === 'GET' && this.formDynamic.items.length) {
                return `${this.requestUrl}?${objectToQueryString(this.getObjectFromItems())}`;
            }
            return this.requestUrl;
        }
    },
    watch: {
        'manageApiMeta.version'() {
            this.formDynamic.version = this.manageApiMeta?.version || '';
        },
        managementData(newVal) {
            if (newVal && JSON.stringify(newVal) !== '{}') {
                this.formDynamic.items = [];
                this.formDynamic.type = newVal.request.method;
                const params = newVal.request.params;
                params.forEach(ele => {
                    this.$set(this.formDynamic.items, this.formDynamic.items.length, {
                        key: ele.key,
                        value: transferVal(ele.define?.defaultValue) || transferVal(ele?.value) || '',
                        visible: ele.define?.visible || ele?.visible || false,
                        describe: ele.define?.describe || ele?.describe || '',
                        default: ele?.define ? true : ele.default,
                        valueType: ele?.define ? ele.define?.valueType || 'string' : ele?.valueType || 'string',
                        enumValues: ele?.define ? ele.define?.enumValues || [] : ele?.enumValues || [],
                        validateValues: ele?.define ? {
                            // maxLen: ele.define?.maxLen || 0,
                            // minValue: ele.define?.minValue || 0,
                            // maxValue: ele.define?.maxValue || 0
                        } : ele?.validateValues || {}
                    });
                });
            }
        }
    },
    methods: {
        // 更改数据格式
        transferEnumValues(enumValues){
            const extractedData = {};
            const enumValuesList = [];

            // 例："enumValues": [{"ldp_front": "前置"}, {"ldp_bizproc": "核心"},{"ldp_todb": "回库"}]
            for (let i = 0; i < enumValues.length; i++) {
                const obj = enumValues[i];
                for (const key in obj) {
                    if (obj.hasOwnProperty(key)) {
                        extractedData[key] = obj[key];
                    }
                }
            }
            Object.keys(extractedData).forEach(key => {
                enumValuesList.push({
                    key: key,
                    value: extractedData[key]
                });
            });
            return enumValuesList;
        },
        // 区分type
        changeType(valueType){
            if (valueType.indexOf('array') !== -1) return 'array';
            if (valueType.indexOf('object') !== -1) return 'object';
            if (valueType.indexOf('bool') !== -1) return 'bool';
            if (valueType.indexOf('string') !== -1) return 'string';
            if (valueType.indexOf('number') !== -1) return 'number';
        },
        // 是否可编辑
        isdisabled() {
            const list = [...this.formDynamic.items].filter(ele => ele.default && ele.visible);
            return !!list.length;
        },
        isEnumType(item) {
            return item.valueType && item.valueType.indexOf('enum') !== -1;
        },
        // 删除条件
        handleRemove(index) {
            this.formDynamic.items.splice(index, 1);
            this.$emit('fet-Height');
        },
        // 增加where条件
        handleAdd() {
            this.formDynamic.items.push({
                key: '',
                value: '',
                visible: true,
                default: false, // 是接口返回值  还是  手动添加值
                valueType: 'string',
                enumValues: [], // 枚举类型数据
                validateValues: {}
            });
            this.$emit('fet-Height');
        },
        changeShowHidden() {
            this.iconType = !this.iconType;
            this.$emit('fet-Height');
        },
        // 将数组key value转成对象
        getObjectFromItems() {
            const param = {};
            [...this.formDynamic.items].forEach(ele => {
                if (ele.key && ele.value) {
                    if (ele.valueType.indexOf('number') !== -1){
                        param[ele.key] = Number(ele.value);
                    } else if (ele.valueType.indexOf('bool') !== -1){
                        param[ele.key] = ele.value === 'true' ? true : false;
                    } else if (ele.valueType.indexOf('array') !== -1){
                        param[ele.key] = stringToArray(ele.value);
                    } else if (ele.valueType.indexOf('object') !== -1){
                        param[ele.key] = stringToObject(ele.value);
                    } else {
                        param[ele.key] = ele.value;
                    }
                }
            });
            return param;
        },
        // 管理功能查询接口
        getManageInfo() {
            this.$emit('query', this.getObjectFromItems());
        },
        // 管理功能修改接口
        editManageInfo() {
            this.$emit('edit', this.getObjectFromItems());
        },
        // 查询
        handleSend() {
            this.$refs['formDynamic'].validate((valid) => {
                if (valid) {
                    if (this.formDynamic.type === 'GET') {
                        this.getManageInfo();
                    } else {
                        this.editManageInfo();
                    }
                } else {
                    return;
                }
            });
        },
        // 节流2s
        debounceSend: _.debounce(function () {
            this.handleSend();
        }, 500),
        // 打开说明文档弹窗
        openAppConfigInfoModal() {
            this.showAppConfigInfo.status = true;
            this.showAppConfigInfo.details = { ...this.manageApiMeta };
        }
    },
    beforeDestroy() {
    }
};
</script>

<style lang="less" scoped>

.select-content {
    width: auto;

    .popo-icon {
        margin: 0 0 0 5px;
        color: var(--font-color);
        cursor: pointer;
    }

    .form-item-request {
        padding: 5px 0 0;
        position: relative;

        .h-input-wrapper.with-version {
            width: calc(100% - 285px);
        }

        .h-input-wrapper.no-version {
            width: calc(100% - 205px);
        }

        .icon-setting {
            padding: 7px 7px 1px;
            transition: all 0.3s ease;

            &:hover {
                color: var(--link-color);
                border-color: var(--link-colorr);

                .icon {
                    fill: var(--link-color) !important;
                    transition: fill 0.3s ease;
                }
            }

            .icon {
                transition: fill 0.3s ease;
                fill: #fff !important;
            }
        }
    }

    .wrapper-params {
        width: 100%;
        max-height: 300px;
        overflow: auto;

        /deep/ .h-form-item-content {
            background: var(--primary-color);
            padding: 0 0 0 5px;
        }

        /deep/ .h-form-item > .h-form-item-content > div:first-child {
            padding-top: 3px;
        }

        /deep/ .h-btn-circle-outline.h-btn-icon-only.h-btn-small,
        /deep/ .h-btn-circle.h-btn-icon-only.h-btn-small {
            width: 22px;
            height: 22px;
            font-size: 12px;
        }
    }

    .h-checkbox-wrapper {
        color: var(--font-color);
    }

    .h-form-item {
        margin-bottom: 5px;
    }
}

.drawer {
    .h-row {
        margin-bottom: 10px;
    }

    .title {
        position: relative;
        padding: 0 0 16px 20px;

        &::before {
            content: "";
            display: inline-block;
            position: absolute;
            left: 0;
            width: 4px;
            height: 17px;
            background: var(--link-color);
        }
    }
}
</style>

