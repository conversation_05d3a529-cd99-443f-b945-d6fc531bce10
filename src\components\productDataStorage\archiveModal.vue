<template>
    <div>
        <!-- 下发配置列表 -->
        <h-msg-box-safe v-model="modalData.status" :escClose="true" :mask-closable="false" title="数据冷备" width="50" maxHeight="300" @on-open="getCollections" @on-close="cancelConfig">
            <p class='title'>{{ !nextStatus ? '选择归档条件':'归档条件确认'}}</p>
            <h-form  v-if="!nextStatus" ref="formValidate" :model="formItem" :label-width="100">
                <h-form-item label="归档服务器:" prop="ip" required>
                    <h-select v-model="formItem.ip" placeholder="请选择" :positionFixed="true" :setDefSelect="true">
                        <h-option v-for="item in ips" :key="item" :value="item" >{{item}}</h-option>
                    </h-select>
                </h-form-item>
                <h-form-item  label="归档目录:" prop="address" required>
                    <h-input v-model="formItem.address" placeholder="请输入" ></h-input>
                </h-form-item>
                <h-form-item label="产品元数据:" prop="range" required>
                    <h-select v-model="formItem.range" placeholder="请选择" :positionFixed="true" :setDefSelect="true">
                        <h-option v-for="item in ranges" :key="item.key" :value="item.key" >{{item.value}}</h-option>
                    </h-select>
                </h-form-item>
                <h-form-item label="产品遥测数据:" prop="indexTypes" required>
                    <h-checkbox-group v-model="formItem.indexTypes">
                        <h-checkbox v-for="item in indexTypeList" :key="item.key" :label="item.key">{{ item.title
                        }}</h-checkbox>
                    </h-checkbox-group>
                </h-form-item>
                <h-form-item label="数据日期范围:" prop="time" required>
                    <h-date-picker v-model="formItem.time" type="daterange" placeholder="选择日期"   placement= "top-start" :positionFixed="true" ></h-date-picker>
                </h-form-item>
            </h-form>
            <div v-if='nextStatus' class="confirm-data">
                    <p>归档服务器：<span>{{ confirmData.ip }}</span></p>
                    <p>归档目录：<span>{{ confirmData.address }}</span></p>
                    <p>产品元数据：<span>{{ confirmData.range }}</span></p>
                    <p>产品遥测数据：<span>{{ confirmData.indexTypes }}</span><h-icon name="createtask" style="margin-left: 10px; cursor: pointer;"  @on-click="openFileList"></h-icon></p>
                    <p>数据日期范围：<span>{{ confirmData.startTime }}</span> 至 <span>{{ confirmData.endTime }}</span></p>
            </div>
            <file-list-modal :modalInfo="fileInfo"/>
            <template v-slot:footer>
                <a-button v-if="!nextStatus" @click="ConfigConfirm">下一步</a-button>
                <a-button v-if="nextStatus" @click="cancelConfig">取消</a-button>
                <a-button v-if="nextStatus" type="primary" :loading="loading" @click="submitConfig">归档</a-button>
            </template>
        </h-msg-box-safe>
    </div>
</template>

<script>
import _ from 'lodash';
import { formatDate } from '@/utils/utils';
import { clearManagementData } from '@/api/httpApi';
import aButton from '@/components/common/button/aButton';
import fileListModal from '@/components/productDataStorage/fileListModal.vue';
export default ({
    props: {
        modalInfo: {
            type: Object,
            default: null
        }
    },
    data() {
        return {
            loading: false,
            modalData: this.modalInfo,
            ips: ['*************'],
            ranges: [{
                key: 'all',
                value: '全部'
            }],
            indexTypeList: [
                {
                    key: 'latency',
                    title: '时延跟踪数据'
                },
                {
                    key: 'monitor',
                    title: '监控指标数据'
                }
            ],
            formItem: {
                ip: '',
                address: '/dist',
                range: '',
                indexTypes: [],
                startTime: '',
                endTime: '',
                time: [formatDate(new Date()), formatDate(new Date())]
            },
            confirmData: {
                ip: '',
                address: '',
                range: '',
                indexTypes: '',
                startTime: '',
                endTime: ''
            },
            nextStatus: false,
            fileInfo: {
                status: false
            }
        };
    },
    methods: {
        getCollections() {
            this.formItem.time = [new Date(), new Date()];
            this.formItem.indexTypes = [];
        },
        ConfigConfirm() {
            this.$refs['formValidate'].validate(async (valid) => {
                if (valid) {
                    const types = [];
                    this.formItem.indexTypes.forEach(item => {
                        const type = _.find(this.indexTypeList, ['key', item])?.title;
                        types.push(type);
                    });
                    this.confirmData = {
                        ...this.formItem,
                        indexTypes: types.join(', '),
                        range: _.find(this.ranges, ['key', this.formItem.range])?.value,
                        startTime: formatDate(this.formItem.time[0]) + ' 00:00:00',
                        endTime: formatDate(this.formItem.time[1]) + ' 00:00:00'
                    };
                    this.nextStatus = true;
                }
            });
        },
        cancelConfig() {
            this.nextStatus = false;
            this.modalData.status = false;
        },
        async submitConfig() {
            const param = { ...this.formItem };
            param.startTime = formatDate(this.formItem.time[0]) + ' 00:00:00';
            param.endTime = formatDate(this.formItem.time[1]) + ' 00:00:00';
            const res = await clearManagementData(param);
            if (res.success) {
                this.$hMessage.success('归档指令发送成功！');
                this.modalData.status = false;
                this.nextStatus = false;
            } else {
                this.$hMessage.error('归档失败!');
                this.modalData.status = false;
                this.nextStatus = false;
            }
        },
        openFileList(){
            this.fileInfo.status = true;
        }
    },
    components: {  aButton, fileListModal }
});
</script>
<style lang="less" scoped>
.title {
    color: #666b79;
    font-size: 14px;
    font-weight: 600;
    position: absolute;
    top: 55px;
    left: 20px;
}

.confirm-data {
    font-size: var(--font-size);

    p {
        margin: 10px;
    }
}
</style>
