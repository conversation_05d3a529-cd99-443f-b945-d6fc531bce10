<!-- zk信息认证修改弹窗 -->
<template>
    <div class="check-link">
        <h-msg-box-safe v-model="modalData.status" title="更新服务集群身份信息" :mask-closable="false" width="460">
            <h-form
                ref="formItem"
                :model="formItem"
                :label-width="80"
            >
                <h-form-item
                    label="用户名："
                    prop="zkUserName"
                    required
                >
                    <h-input
                        v-model.trim="formItem.zkUserName"
                        placeholder="请输入用户名"
                    />
                </h-form-item>
                <h-form-item
                    label="密码："
                    prop="zkPassword"
                    required
                >
                    <h-input
                        v-model.trim="formItem.zkPassword"
                        placeholder="请输入密码"
                        :type="showPwd ? 'text':'password'"
                        :icon="showPwd ? 'browse_fill' : 'eye-disabled'"
                        @on-click="toggleShowPwd"
                    />
                </h-form-item>
            </h-form>
            <template v-slot:footer>
                <a-button @click="modalData.status = false">取消</a-button>
                <a-button type="primary" :loading="loading" @click="onSubmit">确认</a-button>
            </template>
        </h-msg-box-safe>
    </div>
</template>

<script>

import aButton from '@/components/common/button/aButton';
import { updateZkAuth } from '@/api/productApi';

export default {
    components: { aButton },
    props: {
        modalInfo: {
            type: Object,
            default: null
        }
    },
    data() {
        return {
            modalData: this.modalInfo,
            loading: false,
            showPwd: false,
            formItem: {
                zkUserName: this.modalInfo.zkUserName,
                zkPassword: this.modalInfo.zkPassword
            }
        };
    },
    methods: {
        onSubmit() {
            this.$refs['formItem'].validate((valid) => {
                if (valid) {
                    this.updatePassword();
                }
            });
        },
        /**
         * 提交更新密码
         */
        async updatePassword() {
            try {
                this.loading = true;
                const res = await updateZkAuth({
                    productId: this.modalData.productInfo?.id,
                    ...this.formItem
                });
                if (res?.success && res?.code === '200') {
                    this.$hMessage.success('更新成功');
                    this.$emit('close');
                } else {
                    this.$hMessage.error(res?.message ?? '更新失败');
                }
            } catch (error) {
                console.log('提交更新密码 失败', error);
            } finally {
                this.loading = false;
            }
        },
        toggleShowPwd() {
            this.showPwd = !this.showPwd;
        }
    }
};
</script>
