import clusterBusinessPoptip from './clusterBusinessPoptip';
import businessPoptip from './businessPoptip';
import dataAccordBusinessPoptip from './dataAccordBusinessPoptip';
import waterfallLayout from './waterfallLayoutPosition.vue';
import './businessBox.less';
export default {
    name: 'businessBox',
    props: {
        placement: {
            type: String,
            default: 'right'
        },
        panel: {
            type: Object,
            default: function() {
                return {};
            }
        },
        nodes: {
            type: Array,
            default: function() {
                return null;
            }
        },
        type: {
            type: String,
            default: ''
        },
        // 预防：用于构建node位置唯一标识。未及时清理上一次panel layout位置,tab切换时相同nodeId可能会采用上一次的panel layout位置信息导致poptip显示方向错误
        mode: {
            type: String,
            default: ''
        }
    },
    components: { businessPoptip, waterfallLayout, clusterBusinessPoptip, dataAccordBusinessPoptip },
    data() {
        return {
        };
    },
    watch: {
        panel(newVal) {
            this.$nextTick(() => {
                this.$refs['waterfall-layout'] && this.$refs['waterfall-layout'].resize();
            });
        }
    },
    mounted() {
    },
    methods: {
        handleDrawerOpen(val){
            this.$emit('drawser-open', val);
        },
        getStatusFlagClass(key){
            switch (key) {
                case 'runing':
                case 'normal':
                    return { status: 'bussiness-success', mainFlag: 'main-flag', standFlag: 'stand-flag' };

                case 'warning':
                    return { status: 'bussiness-warn', mainFlag: 'main-flag', standFlag: 'stand-flag' };

                case 'critical':
                    return { status: 'bussiness-error', mainFlag: 'main-flag', standFlag: 'stand-flag' };

                case 'stopped':
                    return { status: 'bussiness-stop', mainFlag: 'main-flag', standFlag: 'stand-flag' };

                default:
                    return { status: 'bussiness-none', mainFlag: 'main-flag', standFlag: 'stand-flag' };
            }
        },
        getBussinessPosition(id, type) {
            if (!this.$refs?.[id + this.mode]) return;
            let position = this.placement;
            const top = this.$refs[id + this.mode]?.getBoundingClientRect()?.top || 0;
            const left = this.$refs[id + this.mode]?.getBoundingClientRect()?.left || 0;
            if (type) {
                position = left < 500 ? 'top-start' : 'top-end';
            } else {
                if (this.placement === 'right' || this.placement === 'left') {
                    const posy = top <= 150 ? '-start' : top >= 450 ? '-end' : '';
                    const posx = left >= 420 ? 'left' : 'right';
                    position = posx + posy;
                } else if (this.placement === 'top'){
                    position = left < 500 ? 'top-start' : 'top-end';
                }
            }
            return position;
        },
        renderNodes(nodes, type) {
            return <ul>
                {
                    nodes.map(node => {
                        let flagClass = '';
                        const statusFlagClass = this.getStatusFlagClass(node?.target?.runningInfo?.healthStatus);
                        if (node?.target?.runningInfo?.healthStatus !== 'stopped') {
                            const clusterRole = node?.target?.runningInfo?.clusterRole;
                            if (clusterRole === 'ARB_ACTIVE') flagClass = statusFlagClass.mainFlag;
                            else if (clusterRole === 'ARB_INACTIVE') flagClass = statusFlagClass.standFlag;
                        }
                        return  <li
                            ref={node.id + this.mode}
                            class={[
                                'bussiness-btn',
                                statusFlagClass.status,
                                flagClass
                            ]}
                            key={node.id + this.mode}>
                            {this.type === 'instance' && <business-poptip placement={this.getBussinessPosition(node.id, type)} node={node} />}
                            {this.type === 'marketStart' && <data-accord-business-poptip placement={this.getBussinessPosition(node.id, type)} node={node} v-on:drawser-open={this.handleDrawerOpen}/>}
                        </li>;
                    })
                }
            </ul>;
        }
    },
    render() {
        return (
            Array.isArray(this.nodes) ? <div class="business-box" style={{ padding: '10px 20px' }}>
                {this.renderNodes(this.nodes, 'product')}
            </div>
                : <div class="business-box">
                    <p class="business-box-title" title={this.panel.name || '-'}>
                        <span>{this.panel.name}</span>
                        {this.panel.sign ? <div class='business-box-title-sign'>{this.panel.sign}</div> : ''}
                    </p>
                    {
                        this.panel?.content?.length ? (
                            <waterfall-layout ref="waterfall-layout" items={this.panel.content} colWidth={120} scopedSlots={{
                                default: ({ item }) => {
                                    return  <div class="business-content-list">
                                        <div class="business-box-content"  style="margin: 0px;" key={item?.target?.resourceName}>
                                            <p class="business-box-content-header">
                                                <span class="business-box-content-header-left">{item?.target?.resourceNameLeft || '-'}</span>
                                                {/* <span class="business-box-content-header-name" title={item?.target?.resourceName || '-'}>{item?.target?.resourceName || '-'}</span> */}
                                                <span class="business-box-content-header-right">{item?.target?.resourceNameRight}</span>
                                            </p>
                                            {Array.isArray(item?.nodes) && item?.nodes?.length && this.renderNodes(item?.nodes)}
                                        </div>
                                    </div>;
                                }
                            }}>
                            </waterfall-layout>
                        ) : null
                    }
                </div>
        );
    }
};
