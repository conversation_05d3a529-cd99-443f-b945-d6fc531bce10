<template>
    <div class="button-group">
      <h-button
        v-for="button in visibleButtons"
        :key="button.label"
        type="text"
        :loading="shouldShowLoading(button)"
        :disabled="shouldShowLoading(button)"
        @click="handleClick(button)"
      >{{ button.label }}</h-button>
      <h-dropdown v-show="hiddenButtons.length" @on-click="handleDropdownClick">
        <a href="javascript:void(0)" class="more">···</a>
        <h-dropdown-menu slot="list">
          <h-dropdown-item
            v-for="button in hiddenButtons"
            :key="button.action"
            :name="button.action"
          >{{ button.label }}</h-dropdown-item>
        </h-dropdown-menu>
      </h-dropdown>
    </div>
  </template>

<script>
import { BUTTON_GROUP } from '@/components/coreReplayObservation/constant';

export default {
    props: {
        currentState: {
            type: String,
            required: true
        },
        buttonOptions: {
            type: Object,
            default: () => BUTTON_GROUP
        }
    },
    data() {
        return {
            noLoadingActions: ['copy', 'view', 'delete', 'modify', 'detail'],
            loading: {} // 用于存储按钮的加载状态
        };
    },
    computed: {
        visibleButtons() {
            return (this.buttonOptions[this.currentState] || []).slice(0, 3);
        },
        hiddenButtons() {
            return (this.buttonOptions[this.currentState] || []).slice(3);
        }
    },
    methods: {
        handleClick(button) {
            const { action } = button;
            if (this.loading[action]) return;

            if (!this.noLoadingActions.includes(action)) {
                this.$set(this.loading, action, true);
            }

            // 创建一个 Promise，通过事件机制获取返回值，确保异步任务完成后再执行下一步
            const resultPromise = new Promise((resolve, reject) => {
                this.$emit('button-click', action, resolve);
            });

            resultPromise.then((result) => {
                if (!this.noLoadingActions.includes(action)) {
                    this.$set(this.loading, action, false);
                }
            }).catch((error) => {
                console.error(`请求失败: ${error}`);
                if (!this.noLoadingActions.includes(action)) {
                    this.$set(this.loading, action, false);
                }
            });
        },
        handleDropdownClick(name) {
            const button = this.hiddenButtons.find(btn => btn.action === name);
            this.handleClick(button);
        },
        shouldShowLoading(button) {
            return !this.noLoadingActions.includes(button.action) && this.loading[button.action];
        }
    }
};
</script>

<style scoped>
.button-group {
    display: inline-block;

    .h-btn.h-btn-text {
        padding: 6px;
    }

    .h-btn::before {
        background: transparent;
    }

    .h-btn-disable {
        color: #969797;
    }

    .more {
        font-weight: 600;
        margin: 0 3px;
    }
}
</style>
