/*
 * @Description: 财通行情模型组件
 * @Author: <PERSON><PERSON>
 * @Date: 2022-12-07 10:43:18
 * @LastEditTime: 2023-06-29 16:09:23
 * @LastEditors: <PERSON><PERSON>
 */
import Vis from 'vis';
import _ from 'lodash';
import { v4 as uuidv4 } from 'uuid';
export default {
    name: 'topo2',
    props: {
        template: { // 模型数据
            type: Object,
            default: {}
        },
        monitor: {  // 监控属性
            type: Boolean,
            default: false
        },
        selectedNode: {
            type: String,
            default: ''
        },
        clickable: {
            type: Boolean,
            default: false
        }
    },
    data() {
        return {
            nodes: [],
            edges: [],
            timeLines: [],
            options: {
                edges: {
                    chosen: false,
                    width: 1,
                    shadow: false,
                    smooth: {
                        enabled: false // 设置连线是直线还是湾线还是贝塞尔
                    },
                    arrowStrikethrough: false,
                    color: {
                        color: '#62CAFF',
                        highlight: '#62CAFF',
                        hover: '#62CAFF',
                        inherit: 'from',
                        opacity: 1.0
                    },
                    font: {
                        color: '#fff',
                        size: 20,
                        multi: false,
                        strokeWidth: 2,
                        strokeColor: 'rgb(112, 184, 240)'
                    }
                },
                nodes: {
                    chosen: false,
                    font: {
                        size: 20,
                        color: '#fff'
                    },
                    color: {
                        background: '#2D334C',
                        border: '#345882',
                        highlight: {
                            border: '#74E0ED',
                            background: '#2D334C'
                        },
                        hover: {
                            border: '#74E0ED',
                            background: '#2D334C'
                        }
                    },
                    borderWidth: 1
                },
                physics: {
                    enabled: false
                },
                interaction: {
                    dragNodes: false, // 是否能拖动节点
                    dragView: true, // 是否能拖动画布
                    hover: true, // 鼠标移过后加粗该节点和连接线
                    multiselect: false, // 按 ctrl 多选
                    selectable: false, // 是否可以点击选择
                    selectConnectedEdges: false, // 选择节点后是否显示连接线
                    hoverConnectedEdges: false, // 鼠标滑动节点后是否显示连接线
                    zoomView: true // 是否能缩放画布
                }
            },
            selectData: {
                id: '',
                type: '',
                timeDelayTrendField: ''
            },
            highLightData: {
                id: '',
                type: '',
                timeDelayTrendField: ''
            },
            hoverData: {
                id: '',
                type: '',
                timeDelayTrendField: ''
            }
        };
    },
    mounted() {
        this.init();
        window.addEventListener('resize', this.init);
    },
    beforeDestroy() {
        window.removeEventListener('resize', this.init);
    },
    methods: {
        init() {
            this.nodes = [];
            this.edges = [];
            this.getNodes();
            this.getEdges();
            this.generateNodeAndEdge();
            const networkData = {
                nodes: this.nodes,
                edges: this.edges
            };
            this.$options.network = new Vis.Network(
                this.$refs['model-box-2'],
                networkData,
                this.options
            );

            // 可点击模型
            if (this.clickable) {
                this.$options.network.on('hoverEdge', (ctx) => {
                    this.handleHoverEdge(ctx);
                });
                this.$options.network.on('hoverNode', ctx => {
                    this.handleHoverNode(ctx);
                });
                this.$options.network.on('blurEdge', ctx => {
                    this.clearHoverStatus();
                });
                this.$options.network.on('blurNode', ctx => {
                    this.clearHoverStatus();
                });
                this.$options.network.on('click', ctx => {
                    // 如果点击在节点上，则将其改为选中
                    if (this.hoverData.id && this.hoverData.timeDelayTrendField) {
                        this.handleSelect(this.hoverData.id, this.selectData.id, this.hoverData.type, this.selectData.type);
                        this.selectData = { ...this.hoverData };
                        this.$emit('selectedChange', this.selectData.timeDelayTrendField);
                    }
                });
                // 根据selectedNode来控制选中状态
                if (this.selectedNode) {
                    this.selectData = { ...this.getIdByName(this.selectedNode) };
                    this.selectData.type === 'node'
                        ? this.selectedNodeById(this.selectData.id, this.$options.network.body.data.nodes)
                        : typeof (this.selectData.id) === 'string'
                            ? this.handleEdgeById(this.selectData.id, this.$options.network.body.data.edges, 1)
                            : this.selectData.id.forEach(ele => {
                                this.handleEdgeById(ele, this.$options.network.body.data.edges, 1);
                            });
                }
            }
        },
        // 生成高亮悬浮效果
        setHighLightData() {
            if (this.hoverData.id && this.hoverData.timeDelayTrendField) {
                this.handleHover(this.hoverData.id, this.hoverData.type);
                this.highLightData = { ...this.hoverData };
            }
        },
        // 悬浮边操作
        handleHoverEdge(ctx) {
            if (!this.clickable) return;
            const edg = this.$options.network.body.data.edges;
            const edges = edg.get();
            const hoverEdge = edg.get(ctx.edge);
            this.hoverData.timeDelayTrendField = hoverEdge?.attributes?.timeDelayTrendField;
            this.hoverData.type = 'edge';
            for (const ele of edges) {
                if (ctx.edge === ele.id && ele.hasOwnProperty('height') && ele.height !== 0) {
                    const arr = this.getEdgeListByEdgeId(ele);
                    this.$options.network.selectEdges(arr);
                    this.hoverData.id = arr;
                    break;
                } else if (ctx.edge === ele.id) {
                    this.hoverData.id = ctx.edge;
                }
            }
            this.setHighLightData();
        },
        // 悬浮节点操作
        handleHoverNode(ctx) {
            if (!this.clickable) return;
            const nodes = this.$options.network.body.data.nodes;
            const hoverNode = nodes.get(ctx.node);
            this.hoverData.timeDelayTrendField = hoverNode?.attributes?.timeDelayTrendField;
            this.hoverData.id = ctx.node;
            this.hoverData.type = 'node';
            this.setHighLightData();
        },
        // 清理悬浮状态
        clearHoverStatus() {
            this.handelBlurNetwork();
            // 清理节点悬浮状态 (若悬浮节点被选中，则移开返回选中状态)
            const { id: oldId, type: oldType } = this.highLightData;
            this.handleSelect(this.selectData.id, oldId, this.selectData.type, oldType);
        },
        // 根据节点名获取节点Id
        getIdByName(name) {
            let id;
            const list = [this.edges, this.nodes];
            for (let i = 0; i < 2; i++) {
                for (const ele of list[i]) {
                    if (ele?.attributes?.timeDelayTrendField === name) {
                        if (i === 0 && ele.hasOwnProperty('height') && ele.height !== 0) {
                            id = this.getEdgeListByEdgeId(ele);
                        } else {
                            id = ele.id;
                        }
                        return { id, type: i ? 'node' : 'edge' };
                    }
                }
            }
        },
        // 找出edge相关的边id
        getEdgeListByEdgeId(edge) {
            const arr = [];
            const { from, to } = edge;
            this.$options.network.body.data.edges.forEach(item => {
                if (item.from === from || item.to === from || item.from === to || item.to === to) {
                    (!item.label || item.id === edge.id) && arr.push(item.id);
                }
            });
            arr.splice(_.findIndex(arr, edge.id), 1);
            arr.unshift(edge.id);
            return arr;
        },
        // 生成节点或者边
        generateNodeAndEdge() {
            Array.isArray(this.template.links) && this.template.links.forEach(ele => {
                const nodes = _.filter(this.template.nodes, o => {
                    return (o.id === ele.from || o.id === ele.to);
                }) || [];
                const targetNodes = [];
                nodes.forEach(item => {
                    const x = Number(item.x), y = Number(item.y) - Number(ele.height);
                    const nodeId = _.find(this.nodes, o => {
                        return (o.x === x && o.y === y);
                    })?.id;
                    nodeId || this.nodes.push({
                        id: uuidv4(),
                        label: '',
                        borderWidth: 0,
                        color: {
                            background: 'transparent'
                        },
                        chosen: false,
                        x,
                        y
                    });
                    // 连接假点竖线
                    nodeId !== item.id && this.edges.push({
                        from: nodeId || this.nodes.at(-1).id,
                        to: item.id,
                        dashes: true,
                        vertical: true
                    });
                    targetNodes.push(nodeId || this.nodes.at(-1).id);
                });
                this.edges.push({
                    from: targetNodes[0],
                    to: targetNodes[1],
                    height: ele.height,
                    dashes: true,
                    label: ele.label,
                    attributes: ele.attributes
                });
            });
        },
        getEdges() {
            Array.isArray(this.template.edges) && this.template.edges.forEach(ele => {
                const edge = { ...ele };
                // 判断是不是特殊节点
                if (this.timeLines.indexOf(edge.from) !== -1) {
                    edge.from = edge.from + 'right';
                }
                if (this.timeLines.indexOf(edge.to) !== -1) edge.to = edge.to + 'left';
                edge.arrows = ele.arrows ? {
                    to: {
                        enabled: true,
                        scaleFactor: 1,
                        type: 'arrow'
                    }
                } : undefined;
                edge.dashes = ele.dashes;
                this.edges.push(edge);
            });
        },
        // topo 鼠标移出
        handelBlurNetwork() {
            this.hoverData.id = '';
            this.hoverData.type = '';
            this.hoverData.timeDelayTrendField = '';
            this.$options.network.unselectAll();
        },
        // 需要应用展示配置信息交互
        getNodes() {
            this.template.nodes.forEach(ele => {
                let node = { ...ele };
                node.label = this.$store?.state?.apmDirDesc?.appTypeDictDesc[node.label] || node.label;
                switch (ele.skinType) {
                    case 'switch':
                        node.shape = 'image';
                        node.image = './static/switch.png';
                        node.size = 30;
                        break;

                    case 'box':
                        node.shape = 'box';
                        node.margin = {
                            top: 15,
                            right: 25,
                            left: 25,
                            bottom: 15
                        };
                        break;

                    case 'circle':
                        node.shape = 'circle';
                        break;

                    case 'timeLine':{
                        this.timeLines.push(node.id);
                        const nodes = [_.cloneDeep(node), _.cloneDeep(node), _.cloneDeep(node)];
                        nodes.forEach((ele, index) => {
                            if (index === 0) {
                                ele.shape = 'box';
                                ele.margin = {
                                    top: 15,
                                    right: 25,
                                    left: 25,
                                    bottom: 15
                                };
                            } else {
                                ele.shape = 'circle';
                                ele.label = '';
                                ele.size = 10;
                                if (index === 1) {
                                    ele.x = ele.x - 77;
                                    ele.id = ele.id + 'left';
                                } else {
                                    ele.x = ele.x + 77;
                                    ele.id = ele.id + 'right';
                                }
                            }
                        });
                        node = nodes;
                        break;
                    }
                }
                Array.isArray(node) ? this.nodes.push(...node) : this.nodes.push(node);
            });
        },
        // 手动更新edge上的数据
        handleUpdateEdgeLabel(list, alertStatus) {
            if (!this.$options.network) return;
            const network = this.$options.network;

            const opts = ['edges', 'nodes'];

            opts.forEach(item => {
                const optData = network.body.data[item];
                const optDataList = optData.get();
                for (const ele of optDataList) {
                    if (!ele.attributes) continue;
                    if (item === 'edges') {
                        const timeDelayMonitorField = ele?.attributes?.timeDelayMonitorField;
                        if (timeDelayMonitorField) {
                            const item = _.find(list, ['instanceName', timeDelayMonitorField]);
                            if (item) ele.label = item.data;
                        }
                    }
                    const alerts = alertStatus ? Object.keys(alertStatus) : [];
                    if (alerts.length) {
                        for (const i of alerts) {
                            if (ele.attributes.timeDelayTrendField.search(i) != -1) {
                                const hasError = ele.label.search('⚠️');
                                if (alertStatus && alertStatus[i]) {
                                    ele.label = hasError === -1 ? '⚠️ ' + ele.label : ele.label;
                                } else {
                                    ele.label = hasError !== -1 ? ele.label.substring(3) : ele.label;
                                }
                                break;
                            }
                        }
                    } else {
                        const hasError = ele.label.search('⚠️');
                        ele.label = hasError !== -1 ? ele.label.substring(3) : ele.label;
                    }
                }
                optData.update(optDataList);
            });
        },
        // 根据id选择节点状态
        selectedNodeById(id, nodes, type) {
            const selectNode = nodes.get(id);
            if (!selectNode) return;
            selectNode.color = type ? {
                border: '#CCFFCC',
                background: '#2D334C'
            } : {
                border: '#74E0ED',
                background: '#2D334C'
            };
            selectNode.borderWidth = 2; // type为'hover'显示悬浮效果
            nodes.update(selectNode, id);
        },
        // 根据id清理节点状态
        clearNodeById(id, nodes) {
            const selectNode = nodes.get(id);
            if (!selectNode) return;
            selectNode.color = {
                background: '#2D334C',
                border: '#345882'
            };
            selectNode.borderWidth = 2;
            nodes.update(selectNode, id);
        },
        // 根据id改变边状态
        handleEdgeById(id, edges, type = 0) { // type: 1 选中 0 取消 2 悬浮
            const selectEdge = edges.get(id);
            selectEdge.width = type === 1 || type === 2 ? 3 : 1;
            selectEdge.color = type === 2 ? {
                color: '#CCFFCC'
            } : {
                color: '#74E0ED'
            };
            edges.update(selectEdge, id);
        },
        // 根据id选择选中状态
        // eslint-disable-next-line max-params
        handleSelect(newId, oldId, newType = 'node', oldType = '') { // type: node || edge
            if (!newId) return;
            const nodes = this.$options.network.body.data.nodes;
            const edges = this.$options.network.body.data.edges;
            // 清理老的节点选中状态
            if (oldType === 'node') {
                this.clearNodeById(oldId, nodes);
            }
            // 将选中的节点状态赋值
            if (newType === 'node') {
                this.selectedNodeById(newId, nodes);
            }
            // 清理老的边选中状态
            if (oldType === 'edge') {
                if (typeof (oldId) === 'string') {
                    this.handleEdgeById(oldId, edges);
                } else if (Array.isArray(oldId)) {
                    oldId.forEach(ele => {
                        this.handleEdgeById(ele, edges);
                    });
                }
            }
            // 将选中的边状态赋值
            if (newType === 'edge') {
                if (typeof (newId) === 'string') {
                    this.handleEdgeById(newId, edges, 1);
                } else {
                    newId.forEach(ele => {
                        this.handleEdgeById(ele, edges, 1);
                    });
                }
            }
        },
        // 根据id选择悬浮状态
        handleHover(newId, newType = 'node') { // type: node || edge
            const nodes = this.$options.network.body.data.nodes;
            const edges = this.$options.network.body.data.edges;
            // 将选中的节点状态赋值
            if (newType === 'node') {
                this.selectedNodeById(newId, nodes, 'hover');
            }
            // 将选中的边状态赋值
            if (newType === 'edge') {
                if (typeof (newId) === 'string') {
                    this.handleEdgeById(newId, edges, 2);
                } else {
                    newId.forEach(ele => {
                        this.handleEdgeById(ele, edges, 2);
                    });
                }
            }
        }
    },
    render() {
        return <div ref="model-box-2" style="width: 100%; height: 100%; cursor: pointer;"></div>;
    },
    watch: {
        template: {
            handler(newVal, oldVal) {
                if (this.monitor) return; // 如果是监控的话必须通过手动调用
                this.init();
            },
            deep: true
        }
    }
};
