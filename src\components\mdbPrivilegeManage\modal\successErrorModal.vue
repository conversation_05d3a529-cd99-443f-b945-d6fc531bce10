<!-- 通用成功和失败模态框 -->
<template>
    <div>
        <h-msg-box-safe
            v-model="modalData.status"
            :mask-closable="false"
            :title="modalTitle"
            width="600"
            maxheight="350"
            :canDrag="false"
        >
        <div :class="iconClass" class="icon-tip">
            <h-icon :name="iconName" :size="28" :color="iconColor"></h-icon>
            <p>{{ message }}</p>
        </div>

        <p v-if="!isUserInfoVisible" class="err-msg">{{ modalData.errmsg }}</p>
        <h-form
            v-if="isUserInfoVisible"
            ref="modalData"
            :label-width="80"
            labelPosition="left"
        >
            <h-form-item label="用户：">
            <p>{{ modalData.userName }}</p>
            </h-form-item>
            <h-form-item label="密码：">
            <p>
                {{ displayedPassword }}
                <h-icon
                :name="passwordIcon"
                color="var(--link-color)"
                @on-click="isShowPassWord"
                ></h-icon>
            </p>
            </h-form-item>
            <h-form-item v-if="showRoles" label="关联角色：" prop="roles">
            <p>{{ modalData.roles }}</p>
            </h-form-item>
        </h-form>
        <template v-slot:footer>
            <a-button @click="modalData.status = false">关闭</a-button>
            <a-button
                v-if="isUserInfoVisible"
                type="primary"
                class="clip-button"
                :data-clipboard-text="clipboardText"
                :loading="loading"
                title="复制用户名、密码信息"
                @click="onCopied"
            >{{ copyButtonText }}</a-button
            >
        </template>
        </h-msg-box-safe>
    </div>
</template>

<script>
import aButton from '@/components/common/button/aButton';
import Clipboard from 'clipboard';
export default {
    name: 'SuccessErrorModal',
    props: {
        modalInfo: {
            type: Object,
            default: null
        },
        // 业务类型：'createUser' | 'resetPassword'
        businessType: {
            type: String,
            default: 'createUser'
        }
    },
    data() {
        return {
            modalData: this.modalInfo,
            loading: false,
            showPassWord: false,
            copied: false
        };
    },
    computed: {
        iconClass() {
            return {
                success: 'success-class',
                error: 'error-class'
            }[this.modalData.type];
        },
        iconName() {
            return {
                success: 't-b-correctinformati',
                error: 'closecircled'
            }[this.modalData.type];
        },
        iconColor() {
            return { success: '#52C41A',  error: '#F5222D' }[
                this.modalData.type
            ];
        },
        modalTitle() {
            const titleMap = {
                createUser: '添加用户',
                resetPassword: '重置密码'
            };
            return titleMap[this.businessType] || '操作结果';
        },
        message() {
            const messageMap = {
                createUser: {
                    success: '用户添加成功！',
                    error: '用户添加失败！'
                },
                resetPassword: {
                    success: '重置成功！',
                    error: '重置失败！'
                }
            };
            return messageMap[this.businessType]?.[this.modalData.type] || '操作完成';
        },
        isUserInfoVisible() {
            return ['success'].includes(this.modalData.type);
        },
        showRoles() {
            return this.businessType === 'createUser' && this.isUserInfoVisible;
        },
        displayedPassword() {
            return this.showPassWord
                ? this.modalData.password
                : this.generateStarString(this.modalData.password.length);
        },
        passwordIcon() {
            return this.showPassWord ? 'browse_fill' : 'eye-disabled';
        },
        clipboardText() {
            return `用户名：${this.modalData.userName}  密码：${this.modalData.password}`;
        },
        copyButtonText() {
            const textMap = {
                createUser: '复制用户信息',
                resetPassword: '复制信息',
                syncConfig: '复制信息'
            };
            return textMap[this.businessType] || '复制信息';
        }
    },
    methods: {
        onCopied() {
            if (this.copied) {
                return;
            }
            const clipBoard = new Clipboard('.clip-button');
            clipBoard.on('success', (e) => {
                this.copied = true;
                setTimeout(() => {
                    this.copied = false;
                }, 1000);
                clipBoard.destroy();
                this.$hMessage.success('复制成功');
            });
            clipBoard.on('error', (e) => {
                this.$hMessage.error('该浏览器不支持复制');
                clipBoard.destroy();
            });
        },
        isShowPassWord() {
            this.showPassWord = !this.showPassWord;
        },
        generateStarString(length) {
            return '*'.repeat(length);
        }
    },
    components: { aButton }
};
</script>

<style lang="less" scoped>
/deep/.h-modal-body {
    padding: 16px;
}

/deep/ .h-input-icon {
    cursor: pointer;
}

.icon-tip {
    text-align: center;
}

.h-form {
    width: 80%;
    margin: 10px auto;
    padding: 10px;
    background: #f3f3f3;
}

.err-msg {
    margin: 10px auto;
    width: 80%;
    text-align: center;
}

.h-form-item {
    margin-bottom: 0;
}
</style>
