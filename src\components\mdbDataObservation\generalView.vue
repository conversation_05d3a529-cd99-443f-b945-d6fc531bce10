<template>
    <div class="tab-box">
        <a-loading v-if="loading"></a-loading>
        <div v-else>
            <!-- 观测信息 -->
            <info-sum-bar :data="infoData" />
            <!-- 数据库处理性能总览 -->
            <info-grid :gridData="globalData"></info-grid>
            <!-- 数据库内存使用总览 -->
            <info-grid :gridData="memoryData"></info-grid>
            <!-- 数据进程列表信息 -->
            <obs-table
                ref="table1"
                maxHeight="220"
                :title="dirTitle"
                :tableData="dirTableData"
                :columns="dirColumns"
                :hasPage="false"
                :notSetWidth="true"
                :autoHeadWidth="true" />
            <!-- 数据库配置 -->
            <info-grid ref="memoryConfig" :gridData="memoryConfig"></info-grid>
        </div>
    </div>
</template>

<script>
import { getManagerProxy } from '@/api/mcApi';
import { formatChartNumber, calculateTimeDifference } from '@/utils/utils';
import aLoading from '@/components/common/loading/aLoading';
import obsTable from '@/components/common/obsTable/obsTable';
import infoGrid from '@/components/common/infoBar/infoGrid';
import infoSumBar from '@/components/common/infoBar/infoSumBar';
export default {
    props: {
        nodeData: {
            type: Object,
            default: () => { }
        }
    },
    components: { aLoading, infoGrid, infoSumBar, obsTable },
    data() {
        return {
            loading: true,
            infoData: {
                title: {
                    label: {
                        labelDic: [
                            {
                                key: 'name',
                                label: '数据库名称'
                            }
                        ],
                        labelInfo: {
                            name: '---'
                        }
                    },
                    slots: [
                        {
                            type: 'text', // 文本
                            label: '数据库角色',
                            value: '-'
                        }
                    ]
                },
                direction: 'row',
                details: [
                    {
                        type: 'text',
                        title: '数据库表个数',
                        info: {
                            key: 'TableCount',
                            value: '-'
                        }
                    },
                    {
                        type: 'text',
                        title: '主控进程号',
                        info: {
                            key: 'AdminPid',
                            value: '-'
                        }
                    },
                    {
                        type: 'text',
                        title: '加锁进程号',
                        info: {
                            key: 'LockPid',
                            value: '-'
                        }
                    },
                    {
                        type: 'text',
                        title: '已处理事务号',
                        info: {
                            key: 'CommitSqn',
                            value: '-'
                        }
                    }
                ]
            },
            // 数据处理性能
            globalData: {
                title: {
                    label: '数据库处理性能总览'
                },
                layout: [
                    { x: 0, y: 0, w: 4, h: 9, i: 'info1' },
                    { x: 4, y: 0, w: 8, h: 9, i: 'info2' }
                ],
                details: [
                    {
                        type: 'obj',
                        title: '事务处理性能',
                        infoDic: [
                            {
                                label: '事务处理总数',
                                key: 'CommitSqn'
                            },
                            {
                                label: '事务处理吞吐(笔/秒)',
                                key: 'RateCommitSqn'
                            }
                        ],
                        info: {}
                    },
                    {
                        type: 'chart',
                        info: {
                            height: '170',
                            basicOpiton: {
                                chartType: 'axis',
                                lineDeploy: [
                                    {
                                        name: '事务处理总数',
                                        type: 'line'
                                    },
                                    {
                                        name: '事务处理吞吐',
                                        type: 'line',
                                        yAxisIndex: 1
                                    }
                                ],
                                yAxiDeploy: [
                                    {
                                        name: '总数(笔)',
                                        alignTicks: true,
                                        axisLabel: {
                                            formatter: formatChartNumber
                                        }
                                    },
                                    {
                                        name: '吞吐(笔/秒)',
                                        show: true,
                                        alignTicks: true,
                                        axisLabel: {
                                            formatter: formatChartNumber
                                        }
                                    }
                                ]
                            },
                            additionalOpiton: {
                                backgroundColor: '#2d334c',
                                grid: {
                                    right: 50
                                }
                            },
                            chartData: {
                                xData: [],
                                data: {
                                    事务处理总数: [],
                                    事务处理吞吐: []
                                }
                            }
                        }
                    }
                ]
            },
            // 数据内存使用总览
            memoryData: {
                title: {
                    label: '数据库内存使用总览',
                    slots: [
                        {
                            type: 'text', // 文本
                            label: 'AddressSpaceRange',
                            value: '-'
                        }
                    ]
                },
                layout: [
                    { x: 0, y: 0, w: 4, h: 9, i: 'info1' },
                    { x: 4, y: 0, w: 8, h: 9, i: 'info2' }
                ],
                details: [
                    {
                        type: 'obj',
                        title: '内存使用分布',
                        infoDic: [
                            {
                                label: 'UsedMemoryMB',
                                key: 'UsedMemoryMB'
                            }
                        ],
                        info: {}
                    },
                    {
                        type: 'chart',
                        info: {
                            height: '170',
                            basicOpiton: {
                                chartType: 'axis',
                                lineDeploy: [
                                    {
                                        name: 'UsedMemoryMB',
                                        type: 'line'
                                    }
                                ],
                                yAxiDeploy: [
                                    {
                                        alignTicks: true,
                                        axisLabel: {
                                            formatter: formatChartNumber
                                        }
                                    }
                                ]
                            },
                            additionalOpiton: {
                                backgroundColor: '#2d334c'
                            },
                            chartData: {
                                xData: [],
                                data: {
                                    UsedMemoryMB: []
                                }
                            }
                        }
                    }
                ]
            },
            // 数据进程列表信息
            dirTitle: {
                label: '数据库进程列表信息',
                slots: [
                    {
                        type: 'text', // 文本
                        label: 'ProcessCount',
                        value: '-'
                    }
                ]
            },
            dirColumns: [
                {
                    title: 'ProcessNo',
                    key: 'ProcessNo'
                },
                {
                    title: 'SystemPid',
                    key: 'SystemPid'
                },
                {
                    title: 'Status',
                    key: 'Status'
                },
                {
                    title: 'LastHeartbeatMill',
                    key: 'LastHeartbeatMill'
                },
                {
                    title: 'TransCtrlCount',
                    key: 'TransCtrlCount'
                }
            ],
            dirTableData: [],
            // 数据库配置
            memoryConfig: {
                title: {
                    label: '数据库配置'
                },
                layout: [
                    { x: 0, y: 0, w: 12, h: 6, i: 'info1' },
                    { x: 0, y: 6, w: 3, h: 8, i: 'info2' },
                    { x: 3, y: 6, w: 3, h: 8, i: 'info3' },
                    { x: 6, y: 6, w: 3, h: 8, i: 'info4' },
                    { x: 9, y: 6, w: 3, h: 8, i: 'info5' }
                ],
                details: [
                    {
                        type: 'description',
                        title: '内存数据库版本信息',
                        infoDic: [{
                            key: 'LibName'
                        }, {
                            key: 'Version'
                        }, {
                            key: 'LibPath',
                            span: 12
                        }, {
                            key: 'MdbMode',
                            span: 24
                        }],
                        info: {}
                    }, {
                        type: 'obj',
                        title: '工作进程配置',
                        infoDic: [
                            {
                                key: 'MaxProcessCount'
                            },
                            {
                                key: 'HeartbeatTimeoutMill'
                            },
                            {
                                key: 'Trace'
                            }
                        ],
                        info: {}
                    }, {
                        type: 'obj',
                        title: 'AdminFile文件',
                        infoDic: [
                            {
                                key: 'AdminFile'
                            },
                            {
                                key: 'FileSizeBytes'
                            },
                            {
                                key: 'FileVersion'
                            }
                        ],
                        info: {}
                    }, {
                        type: 'obj',
                        title: '事务处理配置',
                        infoDic: [
                            {
                                key: 'WriteRedoPolicy'
                            },
                            {
                                key: 'RedoVersion'
                            },
                            {
                                key: 'UndoTruncateSizeMB'
                            }
                        ],
                        info: {}
                    }, {
                        type: 'obj',
                        title: '内存分配配置',
                        infoDic: [
                            {
                                key: 'PreAllocMemory'
                            }
                        ],
                        info: {}
                    }
                ]
            }
        };
    },
    mounted() {
    },
    methods: {
        clearData() {
            this.globalData.details[1].info.chartData.data['事务处理总数'] = [];
            this.globalData.details[1].info.chartData.xData = [];
            this.memoryData.details[1].info.chartData.data.UsedMemoryMB = [];
            this.memoryData.details[1].info.chartData.xData = [];
        },
        async initData() {
            this.loading = true;
            this.clearData();
            await this.getFileData();
            this.loading = false;
        },
        async getFileData() {
            try {
                const chartData = this.globalData.details[1].info.chartData;
                const chartData1 = this.memoryData.details[1].info.chartData;
                const newTime = this.handleChartBefore(chartData);
                chartData1.xData = chartData.xData;
                const { getMdbInfo, getMdbLibInfo } = await this.getAPi();
                // 数据库基本信息
                this.infoData.title.label.labelInfo.name = getMdbInfo.DataBase;
                this.infoData.title.slots[0].value = getMdbInfo.Role;
                this.infoData.details[0].info.value = getMdbInfo.TableCount;
                this.infoData.details[1].info.value = getMdbInfo.AdminPid;
                this.infoData.details[2].info.value = getMdbInfo.LockPid;
                this.infoData.details[3].info.value = getMdbInfo.CommitSqn ? getMdbInfo.CommitSqn - 1 : '-';
                const len = chartData.data['事务处理总数'].length;
                const index = chartData.xData.indexOf(newTime);
                if (len) {
                    getMdbInfo.RateCommitSqn = getMdbInfo.CommitSqn && chartData.xData[index - 1]
                        ? parseFloat((getMdbInfo.CommitSqn - chartData.data['事务处理总数'][index - 1] || 0) / calculateTimeDifference(newTime, chartData.xData[index - 1])).toFixed(2).replace(/\.?0*$/, '')
                        : undefined;
                } else {
                    getMdbInfo.RateCommitSqn = undefined;
                }

                // 数据库性能总览
                this.globalData.details[0].info = { ...getMdbInfo };

                this.$set(chartData.data['事务处理总数'], index, getMdbInfo.CommitSqn);
                this.$set(chartData.data['事务处理吞吐'], index, getMdbInfo.RateCommitSqn);

                // 数据库内存总览
                this.memoryData.title.slots[0].value = getMdbInfo.AddressSpaceRange || '-';
                this.memoryData.details[0].info = { ...getMdbInfo };
                this.$set(chartData1.data.UsedMemoryMB, index, getMdbInfo['UsedMemoryMB']);

                // 数据库进程列表信息
                this.dirTitle.slots[0].value = getMdbInfo.Processes?.length || 0;
                this.dirTableData = getMdbInfo.Processes || [];

                // 数据库配置
                if (this.$refs['memoryConfig'] && (typeof (getMdbInfo.Trace) === 'number')) {
                    this.$refs['memoryConfig'].setSelectVal('property', getMdbInfo.Trace);
                }
                this.memoryConfig.details.forEach((ele, index) => {
                    if (index) {
                        this.memoryConfig.details[index].info = { ...getMdbInfo };
                    } else {
                        this.memoryConfig.details[0].info = { ...getMdbLibInfo };
                    }
                });
            } catch (error) {
                this.$emit('clear');
            }
        },
        // 接口请求
        async getAPi() {
            const data = {
                getMdbInfo: {},
                getMdbLibInfo: {}
            };
            const param = [
                {
                    manageProxyIp: this.nodeData.ip,
                    manageProxyPort: this.nodeData.manageProxyPort,
                    pluginName: 'ldp_bizproc',
                    funcName: 'uftmdb_data_GetUftmdbCtrlInfo'
                },
                {
                    manageProxyIp: this.nodeData.ip,
                    manageProxyPort: this.nodeData.manageProxyPort,
                    pluginName: 'ldp_bizproc',
                    funcName: 'uftmdb_data_GetMDBLibInfo'
                }
            ];
            const res = await getManagerProxy(JSON.stringify(param));
            if (res.code === '200') {
                !res.data?.[0]?.ErrorNo && Object.keys(res.data?.[0]).length && (data.getMdbInfo = res.data[0]);
                !res.data?.[1]?.ErrorNo && (data.getMdbLibInfo = res.data[1]);
            }
            return data;
        },
        // 预处理请求前数据
        handleChartBefore(chartData) {
            if (chartData.xData.length > this.pointNum) {
                chartData.xData.shift();
            }
            const newTime = this.$getCurrentLocalTime();
            // 内存表总览走势图
            chartData.xData.push(new Date().toLocaleTimeString());
            return newTime;
        }
    }
};
</script>
