<template>
    <div>
        <h-msg-box-safe
            v-model="modalData.status"
            :title="`${modalData.type === 'edit' ? '修改监控服务' : '对接监控服务'}`"
            width="600"
            :mask-closable="false"
            @on-open="getCollections">
            <h-form
                ref="formValidate"
                :model="formValidate"
                :label-width="100">
                <h-form-item label="传输地址" prop="addr" :validRules="ip4Rule" required>
					<h-input v-model="formValidate.addr"></h-input>
				</h-form-item>
                <h-form-item label="传输端口" prop="port" :validRules="portRule" required>
					<h-input v-model="formValidate.port" type="int" :maxlength="5"></h-input>
				</h-form-item>
            </h-form>
            <template v-slot:footer>
                <h-button @click="modalData.status = false">取消</h-button>
                <h-button :loading="loading" type="primary" @click="submitConfig">确定</h-button>
            </template>
        </h-msg-box-safe>
    </div>
</template>
<script>
import { updataLdpAdminInfo } from '@/api/rcmApi';
import { validatePort } from '@/utils/validate';
export default {
    props: {
        modalInfo: {
            type: Object,
            default: null
        }
    },
    data() {
        return {
            modalData: this.modalInfo,
            loading: false, // 按钮加载中状态，默认为false，防止重复点击
            formValidate: {},
            ip4Rule: ['ip4'],
            portRule: [{ test: validatePort, trigger: 'blur' }]
        };
    },
    methods: {
        getCollections() {
            this.formValidate = {
                addr: this.modalData?.addr,
                port: this.modalData?.port
            };
        },
        submitConfig() {
            const that = this;
            this.$refs['formValidate'].validate(async (valid) => {
                if (valid) {
                    that.loading = true;
                    try {
                        const res = await updataLdpAdminInfo({
                            rcmId: this.modalInfo.rcmId,
                            id: this.modalInfo.id,
                            addr: this.formValidate.addr,
                            port: Number(this.formValidate.port)
                        });
                        if (res.code === '200') {
                            this.$emit('update');
                            that.$hMessage.success('更新成功!');
                            that.modalInfo.status = false;
                        } else if (res.code?.length === 8){
                            this.$hMessage.error('更新失败!');
                        }
                    } finally {
                        that.loading = false;
                    }
                }
            });
        }
    }
};
</script>

