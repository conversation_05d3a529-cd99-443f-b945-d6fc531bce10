<template>
    <div>
        <h-msg-box-safe v-model="modalData.status" :escClose="true" :mask-closable="false" title="事件列表" width="70" height="60">
            <h-timeline>
                <h-timeline-item v-for="(item, index) in modalData.events" :key="index" color="blue">
                    <p style="font-size: 14px; font-weight: 500; line-height: 18px;">{{ item.level }} {{ item.eventTime }}
                        {{ item.message }}
                        <h-poptip v-if="item.eventData" title="事件详情" content="提示内容" placement="right-start"
                            style="margin-left: 10px;">
                            <a @click="handleEventData(item.eventData)">详情</a>
                            <template v-slot:content>
                                <div>
                                    <div class="clip-tooltip">
                                        <span class="clip-button" :class="copied"
                                            :data-clipboard-text="jsonData.extensionId" @click="onCopied">
                                            {{ copied ? '复制成功' : '复制id' }}
                                        </span>
                                    </div>
                                    <div>
                                        <json-viewer :value="jsonData" :expand-depth="3" :expanded="true" ></json-viewer>
                                    </div>
                                </div>
                            </template>
                        </h-poptip>
                    </p>
                    <p style="line-height: 25px; color: #777;">{{ item.serviceType }} {{ item.serviceLocalIp }}</p>
                </h-timeline-item>
            </h-timeline>
            <template v-slot:footer>
                <a-button @click="modalData.status = false">取消</a-button>
            </template>
        </h-msg-box-safe>
    </div>
</template>

<script>
import jsonViewer from 'vue-json-viewer';
import Clipboard from 'clipboard';
import aButton from '@/components/common/button/aButton';
export default ({
    props: {
        modalInfo: {
            type: Object,
            default: null
        }
    },
    data() {
        return {
            copied: false,
            modalData: this.modalInfo,
            jsonData: ''
        };
    },
    methods: {
        handleEventData(data) {
            this.jsonData = data;
        },
        onCopied() {
            if (this.copied) {
                return;
            }
            const clipBoard = new Clipboard('.clip-button');
            clipBoard.on('success', (e) => {
                this.copied = true;
                setTimeout(() => {
                    this.copied = false;
                }, 2000);
                clipBoard.destroy();
            });
            clipBoard.on('error', (e) => {
                this.$hMessage.error('该浏览器不支持复制');
                clipBoard.destroy();
            });
        }
    },
    components: { jsonViewer, aButton }
});
</script>
<style lang="less" scoped>
/deep/ .jv-code {
    padding: 10px !important;
}

/deep/  .h-poptip-popper {
    min-width: 200px;
}

.clip-tooltip {
    position: absolute;
    right: 15px;
    top: 5px;
}

.clip-button {
    position: relative;
    cursor: pointer;
    display: inline-block;
    color: var(--link-color);
    padding: 5px;
    z-index: 5;

    &.copied {
        opacity: 0.4;
        cursor: default;
    }
}
</style>
