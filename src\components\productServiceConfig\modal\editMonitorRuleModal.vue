<template>
    <div>
        <h-msg-box-safe
            v-model="modalData.status"
            :escClose="true"
            :mask-closable="false"
            :title="modalData.title"
            width="700"
            @on-open="handleOpen"
        >
            <div>
                <h-alert
                    show-icon
                    style="width: 600px;
                    margin: 0 auto 10px;
                    padding: 6px 48px 6px 40px;">仅支持告警阈值修改</h-alert>
                <threshold-config
                    ref="configRef"
                    :showLabel="false" />
            </div>

            <template v-slot:footer>
                <h-button
                    @click="modalData.status = false">取消</h-button>
                <h-button
                    type="primary"
                    :loading="loading"
                    @click="handleSubmit">确定</h-button>
            </template>
        </h-msg-box-safe>
    </div>
</template>

<script>
import { saveRule } from '@/api/ruleApi';
import thresholdConfig from '@/components/productServiceConfig/rule/thresholdConfig.vue';
export default {
    name: 'EditMonitorRuleModal',
    props: {
        modalInfo: {
            type: Object,
            default: null
        }
    },
    data() {
        return {
            modalData: this.modalInfo,
            loading: false
        };
    },
    methods: {
        handleOpen() {
            this.$refs['configRef'].setValue(this.modalData.data?.monitorRuleParams?.condition);
        },
        // 修改监控配置
        async saveRule(param) {
            this.loading = true;
            try {
                const res = await saveRule(param);
                if (res.success) {
                    this.$hMessage.success('修改成功');
                    this.modalData.status = false;
                } else {
                    this.$hMessage.error(res.message || '修改失败');
                }
            } catch (error) {
                console.error(error);
            }
            this.loading = false;
        },
        handleSubmit() {
            this.$refs['configRef'].validate(valid => {
                if (valid) {
                    const val = this.$refs['configRef'].getValue();
                    const data = { ...this.modalData.data };
                    if (data.monitorRuleParams?.condition) {
                        data.monitorRuleParams.condition.operator = val.operator;
                        data.monitorRuleParams.condition.threshold = val.threshold;
                    }
                    this.saveRule(data);
                }
            });
        }
    },
    components: { thresholdConfig }
};
</script>

<style lang="less" scoped>
    /deep/ .h-modal-body {
        padding: 16px 32px;
    }
</style>
