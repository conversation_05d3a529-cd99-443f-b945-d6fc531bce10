// /**
//  * 将卡片数组转成多行，每行只有两个卡片
//  * @param arr {Array<IApmSingleRowCardData>}
//  * @return {Array<IApmSingleRowCardData>[]}
//  */
// export function convertArrayToMutil(arr) {
//     if (!arr.length || arr.length <= 2) return [arr];
//     const res = [];
//     let temp = [];
//     arr.forEach((item, index) => {
//         temp.push(item);
//         if ((index + 1) % 2 === 0) {
//             res.push(temp);
//             temp = [];
//         } else if (index === arr.length - 1) {
//             res.push(temp);
//         }
//     });
//     return res;
// }

/**
 *
 * @param {number} value
 */
export function formatCoreFuncValue(value) {
    if (value === undefined || value === null || isNaN(value)) return '-';
    if (String(value).length > 9) return '>999,999,999';
    return value.toLocaleString();
}
