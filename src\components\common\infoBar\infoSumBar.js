import './infoBar.less';
import obsTitle from '@/components/common/title/obsTitle';
import infoBar from '@/components/common/infoBar/infoBar';
export default {
    name: 'info-sum-bar',
    components: { obsTitle, infoBar },
    props: {
        data: {
            title: {
                type: Object,
                default: () => {}
            },
            direction: {
                type: String,
                default: 'row'  // column  grid
            },
            autoGrid: {
                type: Boolean,
                default: false
            },
            gridMinWidth: {
                type: String,
                default: '0'
            },
            autoScroll: {
                type: Boolean,
                default: false
            },
            scrollMaxHeight: {
                type: String,
                default: '0'
            },
            gridSpan: {
                type: Number,
                default: 0
            },
            details: {
                type: Array,
                default: () => []
            }
        },
        // infoId必须全局唯一性！！！！
        selectInfoId: {
            type: String,
            default: ''
        },
        selectedStyleType: {
            type: String,
            default: 'background' // border  background
        },
        // 表格加载
        loading: {
            type: Boolean,
            default: false
        },
        infoSize: {
            type: String,
            default: '' // 传空正常大小, min: 高度减小
        }
    },
    data() {
        return {
            selectedStyle: {
                border: { border: '2px solid var(--link-color)' },
                background: { backgroundColor: 'var(--poptip-bg-color)' }
            }
        };
    },
    methods: {
        handleEvent(name, ...args) {
            this.$emit(name, ...args);
        },
        setSelectVal(key, val) {
            this.$refs['obs-title'].setSelectVal(key, val);
        },
        getSelectVal(key) {
            return this.$refs['obs-title'].getSelectVal(key);
        },
        setCheckVals(key, val) {
            this.$refs['obs-title'].setCheckVals(key, val);
        },
        getCheckVals(key) {
            return this.$refs['obs-title'].getCheckVals(key);
        }
    },
    render() {
        return (
            <div class={['info-sum-bar', this.infoSize ? 'info-sum-bar-min' : '']}>
                { this.data?.title &&
                    <obs-title
                        ref="obs-title"
                        title={this.data?.title}
                        v-on:button-click={(...args) => this.handleEvent('button-click', ...args)}
                        v-on:select-change={(...args) => this.handleEvent('select-change', ...args)}
                        v-on:modal-click={(...args) => this.handleEvent('modal-click', ...args)}
                    >
                        <template slot="extraTitleBox">
                            {this.$slots.extraTitleBox}
                        </template>
                    </obs-title>
                }
                <div
                    class={[
                        'info-' + (this.data?.direction || 'row'),
                        this.data?.autoScroll ? 'scroll-bar' : ''
                    ]}
                    style={[
                        this.data?.autoGrid ? { gridTemplateColumns: `repeat(auto-fill, minmax(${this.data?.gridMinWidth || '110px'}, 1fr)` } : this.data?.gridSpan ? { gridTemplateColumns: `repeat(${this.data?.gridSpan}, 1fr)` } : '',
                        this.data?.autoScroll && this.data?.scrollMaxHeight ? { maxHeight: this.data?.scrollMaxHeight } : ''
                    ]}
                >
                    {
                        (this.data?.details || []).map(v => (
                            <info-bar
                                type={v.type}
                                title={v.title}
                                titleAlias={v.titleAlias}
                                hasBackgroundColor={v.hasBackgroundColor}
                                iconName={v.iconName}
                                iconColor={v.iconColor}
                                infoDic={v.infoDic}
                                infoId={v.infoId}
                                info={v.info}
                                canClick={v.canClick}
                                autoGrid={v.autoGrid}
                                gridMinWidth={v.gridMinWidth}
                                gridSpan={v.gridSpan}
                                poptipInfo={v.poptipInfo}
                                customClassName={v.customClassName}
                                infoSize={v.infoSize}
                                status={v.status}
                                loading={this.loading}
                                selectInfoId={this.selectInfoId}
                                v-on:on-current-change={(...args) => this.handleEvent('on-current-change', ...args)}
                                v-on:info-bar-click={(...args) => this.handleEvent('info-bar-click', ...args)}
                                style={[
                                    v.canClick && v.infoId === this.selectInfoId ? this.selectedStyle[this.selectedStyleType] :  ''
                                ]}
                            />
                        ))
                    }
                </div>
                {this.$slots.extra}
            </div>
        );
    }
};
