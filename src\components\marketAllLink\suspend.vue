<!--
 * @Description:
 * @Author: <PERSON><PERSON>
 * @Date: 2023-03-20 15:40:01
 * @LastEditTime: 2023-03-20 18:59:47
 * @LastEditors: <PERSON><PERSON>
-->

<template>
    <div class="suspend" :style="{ left: suspendData.left, top: suspendData.top }">
        <div v-if="suspendData.model === 1">
            <p class="s-top">{{ suspendData.name }}</p>
            <div v-for="item in suspendData.data" :key="item.instanceName">
                <p>
                    <span style="padding-right: 8px;">{{ item.instanceName }} </span>
                    <span class="s-title">应用状态：</span>
                    <span class="s-status">{{ item.status }}</span>
                </p>
            </div>
        </div>
        <div v-if="suspendData.model === 2">
            <p class="s-top">时延正常率</p>
            <p v-for="(item, index) in suspendData.data" :key="index">
                <span class="s-title">{{ titles[index] }}：</span>
                <span class="s-status">{{ item }}%</span>
            </p>
        </div>
    </div>
</template>

<script>
export default {
    props: {
        suspendInfo: {
            type: Object,
            default: null
        }
    },
    data() {
        return {
            suspendData: this.suspendInfo,
            titles: ['快照行情', '指数行情', '逐笔委托', '逐笔成交']
        };
    },
    methods: {
    }
};
</script>

<style lang="less" scoped>
.suspend {
    width: auto;
    height: auto;
    position: fixed;
    box-sizing: border-box;
    background: var(--base-color);
    border-radius: 4px;
    padding-bottom: 8px;
    z-index: 99;

    p {
        color: var(--font-color);
        font-size: var(--font-size-base);
        line-height: 25px;
        padding: 6px 30px 0 8px;
    }

    .s-top {
        font-size: var(--font-size);
        padding: 6px 8px;
        margin-bottom: 6px;
        border-bottom: var(--border);
    }

    .s-title {
        color: var(--font-color);
    }

    .s-right {
        margin-left: 10px;
    }
}
</style>
