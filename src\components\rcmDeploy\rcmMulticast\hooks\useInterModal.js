/**
 * <AUTHOR>
 * @email <EMAIL>
 * @create date 2024-09-11 09:07:39
 * @modify date 2024-09-11 09:07:39
 * @desc [中心间配置弹窗 hook]
 */
import { ref } from 'vue';
import { cloneDeep } from 'lodash';
import { OPEARTE_TYPE, SYNC_MODEL } from '../constant';

const modal = ref({
    status: false,
    data: { firstZoneNames: [], secondZoneNames: [] },
    type: OPEARTE_TYPE.ADD
});

/**
 * 中心间配置弹窗
 */
export function useInterZoneModal() {
    const onAdd = () => {
        modal.value = {
            status: true,
            data: {
                syncAckPoint: SYNC_MODEL.AFTER_RECV,
                firstZoneNames: [],
                secondZoneNames: []
            },
            type: OPEARTE_TYPE.ADD
        };
    };

    const onEdit = (data) => {
        modal.value = {
            status: true,
            data: cloneDeep(data),
            type: OPEARTE_TYPE.EDIT
        };
    };

    const onClose = () => {
        modal.value = { status: false };
    };

    return { modal, onAdd, onEdit, onClose };
}
