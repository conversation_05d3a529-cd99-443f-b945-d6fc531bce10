<!--
 * @Description: SQL语法说明
 * @Author: <PERSON><PERSON>
 * @Date: 2023-09-08 18:39:02
 * @LastEditTime: 2024-03-02 16:02:14
 * @LastEditors: yingzx38608 <EMAIL>
-->
<template>
    <div>
        <h-drawer
            ref="drawer"
            v-model="modalData.status"
            width="680"
            title="使用说明"
            class="drawer">
            <h-tabs v-model="activeTab" class="sql-rule-tab">
                <h-tab-pane label="SQL路由规则" name="tab1">
                    <Markdown
                        :value="value1"
                        isPreview
                        copyCode
                        :height="configTableHeight"
                        style="user-select: text;" />
                </h-tab-pane>
                <h-tab-pane label="SQL编写说明" name="tab2">
                    <Markdown
                        :value="value"
                        isPreview
                        copyCode
                        :height="configTableHeight"
                        style="user-select: text;" />
                </h-tab-pane>
            </h-tabs>
        </h-drawer>
    </div>
</template>

<script>
import Markdown from 'vue-meditor';
import sqlRouterRule from './sqlRouterRule.md';
export default ({
    props: {
        modalInfo: {
            type: Object,
            default: null
        }
    },
    data() {
        return {
            activeTab: 'tab1',
            modalData: this.modalInfo,
            configTableHeight: 0,
            value1: sqlRouterRule,
            value: `# HSSQL使用说明\n\n## 功能说明\n\n1. 支持选中执行，鼠标选中需要执行的sql语句，点击执行按钮。\n2. 支持外部SQL文件拖拽导入当前sql编辑器。\n3. sql编辑器支持**Ctrl + s**保存sql执行语句。\n4. 支持查询结果集下载导出。\n5. 支持通过F9快捷调整SQL语句格式。\n\n## 语法说明\n\nuft内存数据库集成了sqlite解析器，支持sqlite相关语法，通过"通用sql"支持sqlite相关语句，当前仅支持DML语句（SELECT、INSERT、UPDATE、DELETE）支持如下；\n\n### 通用关键字：\n\nINTO、SET、HAVING、LIMIT、DISTINCT、WHERE、FROM、JOIN、USING、TABLE、CASE、WHEN、THEN、ELSE、END、ON、UNION、CAST、AS、DATE、INT、OUTER、COALESCE、SUBSTR、ROUND、IFNULL、GROUP BY、ORDER BY、LOWER、UPPER、VALUES；\n\n### 逻辑运算符：\n\nAND、BETWEEN、EXISTS、IN、NOT、OR、LIKE、GLOB、NOT IN、IS NULL、IS、IS NOT、||；\n\n### 比较运算符：\n\n=、！=、< >、>、<、>=、<=；\n\n### 算术运算符：\n\n+、-、*、/、%；\n\n### 位运算符：\n\n &、|、~、<<、>>；\n\n### 函数：\n\nSUM、COUNT、MIN、MAX、AVG；\n\n### 模糊查询：\n\nlike、%、_、[ ]；\n\n### 联合查询：\n\nINNER JOIN、LEFT JOIN、RIGHT JOIN；\n\n支持UNION/UNION ALL操作（不支持分页联合查询）；\n\n支持使用字符串的连接符||和注释/**/；`
        };
    },
    mounted() {
        window.addEventListener('resize', this.setTableHeight);
        this.$nextTick(() => {
            this.setTableHeight();
        });
    },
    beforeDestroy() {
        window.removeEventListener('resize', this.setTableHeight);
    },
    methods: {
        /**
         * 设置文本高度
         */
        setTableHeight() {
            const element = document.querySelector('.h-drawer-content');
            this.configTableHeight = element.clientHeight - 140;
        }
    },
    components: { Markdown }
});
</script>

<style lang="less" scoped>
@import url("@/assets/css/drawer.less");
@import url("@/assets/css/tab.less");

/deep/ .h-drawer-body {
    padding: 10px;
}

/deep/ .h-tabs-bar {
    margin-bottom: 0;
}

/deep/ .markdown.border {
    border: none !important;
}

/deep/ .markdown-content,
/deep/ .markdown-preview,
/deep/ .markdown-preview > div {
    background-color: var(--main-color) !important;
}

/deep/ .markdown-preview h1,
/deep/ .markdown-preview h2,
/deep/ .markdown-preview h3,
/deep/ .markdown-preview h4,
/deep/ .markdown-preview h5 {
    color: #fff !important;
}

/deep/ .markdown-preview p,
/deep/ .markdown-preview ol li,
/deep/ .markdown-preview ul li {
    color: #cacfd4 !important;
    font-size: 12px !important;
}

/deep/ .markdown-content .markdown-preview table {
    table-layout: fixed;

    tr, tr:nth-of-type(even) {
        th, td {
            border: 1px solid #31364a;
            font-weight: var(--font-weight);
            font-size: var(--font-size-base);
        }

        th {
            background-color: var(--primary-color);
            color: var(--font-color);
        }

        td {
            background: var(--input-bg-color);
            color: var(--font-color);
            word-break: keep-all;
        }
    }

    tr:hover {
        th {
            background-color: var(--primary-color);
        }

        td {
            background: var(--input-bg-color);
        }
    }
}
</style>
