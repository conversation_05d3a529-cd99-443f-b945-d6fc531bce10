<template>
    <div ref="monitor" class="monitor">
        <a-loading v-if="loading"></a-loading>
        <obs-title
            class="monitor-title"
            :title="obsTitle">
            <template v-slot:extraTitleBox>
                <span class="monitor-title-slots">
                    <h-select
                        v-model="selectGroup.target"
                        :clearable="false"
                        :disabled="reqLoading"
                        style="width: 250px;"
                        @on-change="(val) => handleConditionChange(val, 'target')"
                        >
                        <h-option
                            v-for="item in targetOptions" :key="item.value"
                            :value="item.value"
                            >{{ item.label }}
                        </h-option>
                    </h-select>
                    <h-select
                        v-model="selectGroup.topN"
                        :clearable="false"
                        :disabled="reqLoading"
                        style="width: 150px;"
                        @on-change="(val) => handleConditionChange(val, 'topN')"
                        >
                        <h-option
                            v-for="item in topNOptions" :key="item.value"
                            :value="item.value"
                            >{{ item.label }}
                        </h-option>
                    </h-select>
                    <h-checkbox
                        v-model="selectGroup.checkbox"
                        :disabled="reqLoading"
                        @on-change="(val) => handleConditionChange(val, 'checkbox')">
                        积压数大于0
                    </h-checkbox>
                </span>
            </template>
        </obs-title>
        <div ref="table" class="monitor-table">
            <u-table
                :border="true"
                :canDrag="true"
                :showTitle="true"
                :height="tableHeight"
                :columns="columns"
                :data="tableData"
                :span-method="handleSpan"
            ></u-table>
        </div>
    </div>
  </template>
<script>
import aLoading from '@/components/common/loading/aLoading';
import obsTitle from '@/components/common/title/obsTitle';
import { getContextsBacklog } from '@/api/rcmApi';
import { COLUMNS, TARGET_OPTIONS, TOPN_OPTIONS } from './constant';
export default {
    name: 'RcmBacklog',
    components: { obsTitle, aLoading },
    props: {
        rcmId: {
            type: String,
            default: ''
        },
        rcmName: {
            type: String
        }
    },
    data() {
        return {
            obsTitle: {
                label: '上下文消息积压列表'
            },
            targetOptions: TARGET_OPTIONS,
            topNOptions: TOPN_OPTIONS,
            columns: [],
            tableData: [],
            mergeObjArr: {
                ctx: [],
                topic: []
            }, // 保存合并回调数据
            selectGroup: {
                target: 'sendCacheBacklogMsgSize',
                topN: 10,
                checkbox: true
            },
            loading: false,
            reqLoading: false,
            tableHeight: 0,
            timer: null
        };
    },
    mounted() {
        this.resizeObserver = new ResizeObserver(entries => {
            this.fetTableHeight();
            this.fetTableWidth();
        });
        this.resizeObserver.observe(this.$refs['table']);
    },
    beforeDestroy() {
        this.resizeObserver.disconnect(this.$refs['tab-box']);
        this.clearPolling();
    },
    methods: {
        async initData() {
            this.loading = true;
            const rsp = await this.getTableData();
            this.loading = false;
            this.clearPolling();
            // 确保第一次请求成功再进行轮询
            rsp && this.setPolling();
            this.fetTableHeight();
        },
        setPolling() {
            this.timer = setInterval(async() => {
                await this.getTableData();
            }, 5000);
        },
        clearPolling() {
            this.timer && clearInterval(this.timer);
        },
        // 获取表格全量数据
        async getTableData() {
            if (!this.rcmId) return;
            let tableData = [];
            const params = {
                rcmId: this.rcmId,
                // 排序、过滤条件
                hasBacklog: this.selectGroup.checkbox,
                sortBy: this.selectGroup.target,
                pageSize: this.selectGroup.topN
            };
            try {
                const res = await getContextsBacklog(params);
                if (res.code === '200' && params.rcmId === this.rcmId) {
                    tableData = res?.data || [];
                    return true;
                }
            } catch (err) {
                this.clearPolling();
            } finally {
                this.tableData = tableData;
                this.mergeArr = tableData?.length ? {
                    ctx: this.getMergeParam(tableData, ['sendContextName']),
                    topic: this.getMergeParam(tableData, ['sendContextName', 'topicName', 'partitionNo'])
                } : {
                    ctx: [],
                    topic: []
                };
            }
        },
        // 切换下拉选项
        async handleConditionChange(val, key) {
            this.reqLoading = true;
            this.selectGroup[key] = val;
            await this.getTableData();
            this.reqLoading = false;
        },
        // 设置table高度
        fetTableHeight() {
            this.tableHeight = this.$refs['monitor']?.offsetHeight - 64;
            this.tableWidth = this.$refs['table']?.offsetWidth;
        },
        // 设置table默认宽度
        fetTableWidth() {
            const tableWidth = this.$refs['table']?.offsetWidth;
            const width15 = tableWidth * 0.15;
            const width10 = tableWidth * 0.1;
            this.columns = [...COLUMNS].map(o => {
                if (['sendContextName', 'receiveContextName'].includes(o.key)) {
                    return {
                        ...o,
                        width: width15,
                        render: (h, params) => {
                            const ctx = params.row?.[params?.column?.key];
                            return h('h-button', {
                                attrs: {
                                    title: ctx
                                },
                                props: {
                                    type: 'text'
                                },
                                style: {
                                    width: '100%',
                                    padding: 0,
                                    'text-align': 'left',
                                    overflow: 'hidden',
                                    'white-space': 'nowrap',
                                    'text-overflow': 'ellipsis'
                                },
                                on: {
                                    click: () => {
                                        // 跳转上下文观测
                                        this.$hCore.navigate(`/rcmObservation`, { history: true }, {
                                            rcmId: this.rcmId,
                                            contextName: ctx,
                                            rcmName: this.rcmName
                                        });
                                    }
                                }
                            }, ctx);
                        }
                    };
                } else {
                    return {
                        ...o, width: width10
                    };
                }
            });
        },
        // 获取表格合并参数
        getMergeParam(tableData, keyArr) {
            // 拼接判断项
            let keyVal = keyArr.map(item => tableData?.[0]?.[item]).join();
            let count = 1;
            const result = [0];
            for (let i = 1; i < tableData.length; i++) {
                const iKeyVal = keyArr.map(item => tableData?.[i]?.[item]).join();
                if (iKeyVal === keyVal) {
                    count++;
                } else {
                    result.push(count);
                    result.push(i);
                    keyVal = iKeyVal;
                    count = 1;
                }
            }
            result.push(count);
            return result;
        },
        // 表格合并回调方法
        handleSpan({ row, column, rowIndex, columnIndex }) {
            if ([0].includes(columnIndex)) {
                const mergeArr = this.mergeArr?.ctx || [];
                for (let i = 0; i < mergeArr.length; i += 2) {
                    if (mergeArr[i + 1] > 1) {
                        if (rowIndex === mergeArr[i]) {
                            return [mergeArr[i + 1], 1];
                        } else if (rowIndex < mergeArr[i] + mergeArr[i + 1]) {
                            return [0, 0];
                        }
                    } else if (rowIndex === mergeArr[i]) {
                        return [1, 1];
                    }
                }
            }
            if ([1, 2, 3, 4].includes(columnIndex)) {
                const mergeArr = this.mergeArr?.topic || [];
                for (let i = 0; i < mergeArr.length; i += 2) {
                    if (mergeArr[i + 1] > 1) {
                        if (rowIndex === mergeArr[i]) {
                            return [mergeArr[i + 1], 1];
                        } else if (rowIndex < mergeArr[i] + mergeArr[i + 1]) {
                            return [0, 0];
                        }
                    } else if (rowIndex === mergeArr[i]) {
                        return [1, 1];
                    }
                }
            }
        }
    }
};
</script>

<style lang="less" scoped>
@import url("@/assets/css/uTable.less");

.monitor {
    height: 100%;
    background-color: var(--wrapper-color);
    overflow: auto;

    &-title {
        &-slots {
            float: right;
            margin-right: 10px;
        }
    }

    &-table {
        width: calc(100% - 20px);
        margin: 10px;
    }
}
</style>
