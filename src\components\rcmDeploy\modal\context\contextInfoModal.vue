<template>
    <div>
        <h-msg-box
            v-model="ctxConfigInfo.status"
            :escClose="true"
            :mask-closable="false"
            :title="`${ctxName} 上下文配置信息`"
            width="90"
            height="450"
            class="wrap-msgbox"
            :allowCopy="true"
            @on-open="getCollections">
            <div class="rcm-config-box">
                <div class="config-box-left">
                    <div class="config-title">上下文主题收发关系</div>
                    <div class="ctx-topo">
                        <rcm-topic-topo ref="rcm-topic-topo" />
                    </div>
                </div>
                <div class="config-box-right">
                    <h-tabs size="small">
                        <h-tab-pane label="基本信息">
                            <h-form ref="normalFormValidate" :model="formValidate" :label-width="160" cols="2">
                                <h-form-item label="上下文名称:">
                                    <p>{{ formValidate.name }}</p>
                                </h-form-item>
                                <h-form-item label="上下文模式:">
                                    <p>{{ formValidate.mode }}</p>
                                </h-form-item>
                                <h-form-item v-if="formValidate.mode === 'cluster'" label="集群名称:" >
                                    <p>{{ formValidate.tierName }}</p>
                                </h-form-item>
                            </h-form>
                            <div class="config-title">实例配置</div>
                            <normal-setting :saveValidateData="formValidate" readOnly :rcmId="rcmId" />
                            <div v-if="formValidate.mode === 'cluster'"  class="config-title">集群配置</div>
                            <cluster-setting v-if="formValidate.mode === 'cluster'" :saveValidateData="formValidate" readOnly />
                        </h-tab-pane>
                        <!-- <h-tab-pane label="运行时统计" style="position: relative;">
                            <h-spin v-if='loading' fix>加载中...</h-spin>
                            <json-viewer v-else :value="jsonData" :expand-depth="5" :expanded="true" ></json-viewer>
                        </h-tab-pane> -->
                    </h-tabs>
                    <span class="ctx-id">ID: {{ formValidate.id }}</span>
                </div>
            </div>
            <template v-slot:footer>
                <a-button @click="cancelMethod">取消</a-button>
            </template>
        </h-msg-box>
    </div>
</template>

<script>
import { getCtxRunningInfo } from '@/api/rcmApi';
import aButton from '@/components/common/button/aButton';
// import jsonViewer from 'vue-json-viewer';
import rcmTopicTopo from '@/components/common/topo/rcmTopicTopo';
import clusterSetting from '@/components/rcmDeploy/modal/clusterSetting';
import normalSetting from '@/components/rcmDeploy/modal/normalSetting';
export default {
    props: {
        modalInfo: {
            type: Object,
            default: null
        },
        rcmId: {
            type: String,
            default: ''
        }
    },
    // components: { aButton, normalSetting, clusterSetting, rcmTopicTopo, jsonViewer },
    components: { aButton, normalSetting, clusterSetting, rcmTopicTopo },
    data() {
        return {
            ctxConfigInfo: this.modalInfo,
            formValidate: {},
            ctxName: '',
            temp: {},
            jsonData: '',
            loading: true
        };
    },
    mounted() {
        this.ctxName = this.ctxConfigInfo?.data?.name;
        this.temp = this.ctxConfigInfo?.data?.viewMode;
        this.formValidate = { ...this.ctxConfigInfo?.data };
    },
    methods: {
        getCollections() {
            this.$refs['rcm-topic-topo'].init(this.temp, this.ctxName);
            // this.getCtxRunningInfo();
        },
        async getCtxRunningInfo() {
            this.loading = true;
            const res = await getCtxRunningInfo({
                rcmId: this.rcmId,
                name: this.ctxName
            });
            this.loading = false;
            this.jsonData = res.code === '200' ? res.data : '';
        },
        cancelMethod() {
            this.ctxConfigInfo.status = false;
        }
    }
};
</script>
<style lang="less" scoped>
/deep/ .h-modal-body {
    padding: 20px;
    overflow-y: hidden !important;
}

.rcm-config-box {
    display: flex;
    width: 95%;
    min-width: 700px;
    overflow-x: auto;
    height: 410px;
    margin: 0 auto;
    border: 1px solid #e7e7e7;

    & > .config-box-left {
        border-right: 1px solid #e7e7e7;
        width: 40%;

        & > .ctx-topo {
            width: 100%;
            height: 370px;
        }
    }

    & > .config-box-right {
        position: relative;
        width: 60%;

        /deep/ .h-tabs-tabpane {
            height: 355px;
            overflow: auto;
        }

        .ctx-id {
            position: absolute;
            right: 10px;
            top: 10px;
        }
    }

    .config-title {
        padding: 10px 0 0 30px;
        font-size: 12px;
        color: #333;
        font-weight: 500;

        &::before {
            display: inline-block;
            position: relative;
            left: -13px;
            top: 3px;
            content: "";
            width: 4px;
            height: 15px;
            background: var(--link-color);
        }
    }

    /deep/ .jv-code {
        padding: 0 20px 10px;
    }

    /deep/ .h-form-item {
        padding: 0;
    }
}
</style>
