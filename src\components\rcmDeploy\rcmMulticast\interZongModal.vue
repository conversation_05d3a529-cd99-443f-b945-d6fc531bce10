<!-- /**
 * <AUTHOR>
 * @email <EMAIL>
 * @create date 2024-09-10 13:44:12
 * @modify date 2024-09-11 13:44:12
 * @desc [中心间配置编辑、新增弹窗]
 */ -->
<template>
  <div>
    <h-msg-box-safe
      v-model="visible"
      :title="`${isAdd ? '新建' : '更新'}中心间配置`"
      :mask-closable="false"
      @on-close="onClose"
      @on-cancel="onClose"
    >
      <h-form
        ref="form"
        v-model="formData.data"
        onlyBlurRequire
        :label-width="100"
      >
        <h-form-item
          requiredTrigger="blur"
          required
          label="左中心"
          prop="data.firstZoneNames"
        >
          <h-select
            v-model="formData.data.firstZoneNames"
            filterable
            multiple
            style="width: 300px;"
            @on-blur="onBlur"
          >
            <h-option
              v-for="item in leftZones"
              :key="item.id"
              :value="item.name"
              >{{ item.name }}</h-option
            >
          </h-select>
        </h-form-item>

        <h-form-item
          requiredTrigger="blur"
          required
          label="右中心"
          prop="data.secondZoneNames"
        >
          <h-select
            v-model="formData.data.secondZoneNames"
            filterable
            multiple
            style="width: 300px;"
            @on-blur="onBlur"
          >
            <h-option
              v-for="item in rightZones"
              :key="item.id"
              :value="item.name"
              >{{ item.name }}</h-option
            >
          </h-select>
        </h-form-item>

        <h-form-item label="跨中心RPO" prop="data.rpo">
          <h-input
            v-model="formData.data.rpo"
            type="text"
            placeholder="请输入数字类型"
            style="width: 300px;"
          ></h-input>
        </h-form-item>

        <h-form-item label="同步确认模式" prop="data.syncAckPoint">
          <h-select v-model="formData.data.syncAckPoint" style="width: 300px;">
            <h-option
              v-for="item in syncAckPointList"
              :key="item.value"
              :value="item.value"
              >{{ item.name }}</h-option
            >
          </h-select>
        </h-form-item>
      </h-form>

      <template v-slot:footer>
        <a-button @click="onClose">取消</a-button>
        <a-button type="primary" :loading="loading" @click="onSubmit"
          >确认</a-button
        >
      </template>
    </h-msg-box-safe>
  </div>
</template>
<script>
import {
    defineComponent,
    ref,
    getCurrentInstance,
    nextTick,
    computed
} from 'vue';
import aButton from '@/components/common/button/aButton';
import { createInterZoneConfig, updateInterZoneConfig } from '@/api/rcmApi';
import { useInterZoneModal, useZonesList } from './hooks';
import { SYNC_MODEL_LIST, OPEARTE_TYPE } from './constant';
export default defineComponent({
    name: 'InnerZoneModal',
    props: {
        rcmId: String,
        reload: Function
    },
    setup(props, context) {
        const { modal, onClose } = useInterZoneModal();
        const { list } = useZonesList();
        const loading = ref(false);
        const { proxy } = getCurrentInstance();
        const isAdd = computed(() => modal.value.type === OPEARTE_TYPE.ADD);
        /**
     * 左中心可选择项，与右中心互斥
     */
        const leftZones = computed(() => {
            const secondZoneNames = modal.value.data.secondZoneNames || [];
            if (!secondZoneNames.length) {
                // 右中心未选
                return list.value;
            }
            return list.value.filter((item) => !secondZoneNames.includes(item.name));
        });

        /**
     * 右中心可选择项，与左中心互斥
     */
        const rightZones = computed(() => {
            const firstZoneNames = modal.value.data.firstZoneNames || [];
            if (!firstZoneNames.length) {
                // 左中心未选
                return list.value;
            }
            return list.value.filter((item) => !firstZoneNames.includes(item.name));
        });
        /**
     * 确认保存
     */
        const onSubmit = async () => {
            proxy.$refs['form'].validate();
            let canSubmit = true;
            if (!list.value.length) {
                modal.value.data.firstZoneNames = [];
                modal.value.data.secondZoneNames = [];
            } else {
                modal.value.data.firstZoneNames = modal.value.data.firstZoneNames.filter(item => leftZones.value.find(subItem => subItem.name === item));
                modal.value.data.secondZoneNames = modal.value.data.secondZoneNames.filter(item => rightZones.value.find(subItem => subItem.name === item));
            }
            if (!modal.value.data.firstZoneNames.length) {
                proxy.$refs['form'].validateField('data.firstZoneNames');
                canSubmit = false;
            } else {
                proxy.$refs['form'].resetValidateField('data.firstZoneNames');
            }
            if (!modal.value.data.secondZoneNames.length) {
                proxy.$refs['form'].validateField('data.secondZoneNames');
                canSubmit = false;
            } else {
                proxy.$refs['form'].resetValidateField('data.secondZoneNames');
            }
            if (canSubmit) {
                try {
                    const { rpo } = modal.value.data;
                    if (rpo) {
                        if (isNaN(rpo) || (rpo.search && rpo.search('e') !== -1)) {
                        // 请输入数字类型
                            proxy.$hMessageSafe.error({
                                content: `rpo填写错误：请输入数字类型`,
                                duration: 3
                            });
                            return;
                        }
                        if (Number(rpo) % 1 !== 0) {
                        // 请输入数字类型
                            proxy.$hMessageSafe.error({
                                content: `rpo填写错误：请输入数字类型`,
                                duration: 3
                            });
                            return;
                        }
                        if (rpo < -(2 ** 63) || rpo > 2 ** 63 - 1) {
                            proxy.$hMessageSafe.error({
                                content: `rpo填写错误：\n超出输入范围，输入范围在-9223372036854775808 ~ 9223372036854775807`,
                                duration: 3
                            });
                            return;
                        }
                    }
                    loading.value = true;
                    proxy.$refs['form'].resetValidate();
                    const api = isAdd.value
                        ? createInterZoneConfig
                        : updateInterZoneConfig;
                    const res = await api({ ...modal.value.data, rcmId: props.rcmId });
                    if (res.code === '200') {
                        // 成功，重新刷新页面
                        proxy.$hMessageSafe.success({
                            content: `中心间配置 已${isAdd.value ? '添加' : '更新'}`,
                            duration: 3
                        });
                        onClose();
                        context.emit('reload');
                    }
                } finally {
                    loading.value = false;
                }
            }
        };

        const onBlur = () => {
            nextTick(() => {
                proxy.$refs['form'].resetValidate();
            });
        };

        return {
            visible: modal.value.status,
            onClose,
            isAdd,
            formData: modal,
            loading,
            onBlur,
            onSubmit,
            syncAckPointList: SYNC_MODEL_LIST,
            leftZones,
            rightZones
        };
    },
    components: { aButton }
});
</script>
