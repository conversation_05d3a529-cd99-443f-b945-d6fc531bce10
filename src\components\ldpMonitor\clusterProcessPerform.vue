<template>
    <div class="perform-box">
        <a-loading v-if="loading" style="width: 100%; height: 100%;"></a-loading>
        <div v-else class="cluster-box">
            <div v-for="item in performData" :key="item.cluster">
                <info-sum-bar :data="setPerformItem(item)" :selectInfoId="selectInfoId" selectedStyleType="border"
                    @info-bar-click="handleBarClick"></info-sum-bar>
            </div>
        </div>
        <no-data v-if="!loading && !performData.length" />
        <h-drawer ref="drawer-box" v-model="modelInfo.status" title="功能号处理" width="40" @on-close="handleClose">
            <obs-table :hasDarkClass="false" :height="tableHeight" showTitle :tableData="modelInfo.tableData" :columns="columns"></obs-table>
        </h-drawer>
    </div>
</template>
<script>
import _ from 'lodash';
import { formatNumber, autoConvertTime, transferVal } from '@/utils/utils';
import { getCoreCluster, getCoreClusterDetail } from '@/api/productApi';
import infoSumBar from '@/components/common/infoBar/infoSumBar';
import obsTable from '@/components/common/obsTable/obsTable';
import noData from '@/components/common/noData/noData';
import aLoading from '@/components/common/loading/aLoading';

export default {
    props: {
        productInfo: {
            type: Object,
            default: () => {
                return {};
            }
        },
        productInstNo: {
            type: String,
            default: ''
        },
        curTabDashBorad: {
            type: Object,
            default: () => { return {}; }
        }
    },
    components: {
        aLoading,
        noData,
        infoSumBar,
        obsTable
    },
    data() {
        return {
            timer: null,
            loading: false,
            tableHeight: 0,
            // 弹窗信息
            modelInfo: {
                status: false,
                tableData: []
            },
            // 表格
            columns: [
                {
                    title: '应用节点',
                    key: 'instanceName',
                    ellipsis: true
                },
                {
                    title: '功能号',
                    key: 'functionId',
                    ellipsis: true
                },
                {
                    title: '吞吐(tps)',
                    key: 'throughput',
                    ellipsis: true,
                    render: (h, params) => {
                        const throughput = formatNumber(params?.row?.throughput) || '-';
                        return h('span', [throughput]);
                    }
                },
                {
                    title: '平均时延(ns)',
                    key: 'latency',
                    ellipsis: true
                },
                {
                    title: '错误率(%)',
                    key: 'errorRate',
                    ellipsis: true,
                    render: (h, params) => {
                        const errorRate = formatNumber(params?.row?.errorRate) || '-';
                        return h('span', [errorRate]);
                    }
                }
            ],
            // 集群组件信息
            performOverview: {
                title: {
                    label: {
                        labelDic: [
                            {
                                key: 'clusterName',
                                label: '-'
                            }
                        ],
                        labelInfo: {
                            clusterName: '-'
                        }
                    },
                    slots: [
                        {
                            type: 'text',
                            label: '分片号',
                            value: '-'
                        }
                    ]
                },
                direction: 'row',
                details: [
                    {
                        type: 'obj',
                        title: '委托交易',
                        canClick: true,
                        infoDic: [
                            {
                                label: '吞吐率',
                                key: 'throughput'
                            },
                            {
                                label: '平均时延',
                                key: 'latency'
                            },
                            {
                                label: '错误率',
                                key: 'errorRate'
                            }
                        ],
                        infoId: '',
                        info: {
                            throughput: '-',
                            latency: '-',
                            errorRate: '-'
                        }
                    },
                    {
                        type: 'obj',
                        title: '委托查询',
                        infoId: '',
                        canClick: true,
                        infoDic: [
                            {
                                label: '吞吐率',
                                key: 'throughput'
                            },
                            {
                                label: '平均时延',
                                key: 'latency'
                            },
                            {
                                label: '错误率',
                                key: 'errorRate'
                            }
                        ],
                        info: {
                            throughput: '-',
                            latency: '-',
                            errorRate: '-'
                        }
                    }, {
                        type: 'obj',
                        title: '其他业务',
                        canClick: true,
                        infoId: '',
                        infoDic: [
                            {
                                label: '吞吐率',
                                key: 'throughput'
                            },
                            {
                                label: '平均时延',
                                key: 'latency'
                            },
                            {
                                label: '错误率',
                                key: 'errorRate'
                            }
                        ],
                        info: {
                            throughput: '-',
                            latency: '-',
                            errorRate: '-'
                        }
                    }
                ]
            },
            // 选中id
            selectInfoId: '',
            // 页面数据结构
            performData: []
        };
    },
    mounted() {
        window.addEventListener('resize', this.fetTableHeight);
    },
    methods: {
        // 初始化
        async init() {
            this.clearPolling();
            this.cleanData();
            this.loading = true;
            this.curTabDashBorad?.timerSwitch && this.setPolling(this.curTabDashBorad?.timerInterval || 10);
            await this.getPerformanceData();
            this.loading = false;
        },
        // 设置table高度
        fetTableHeight() {
            this.tableHeight = this.$refs['drawer-box']?.$el?.firstChild?.clientHeight - 85;
        },
        // 手动清理页面数据
        cleanData() {
            this.tableHeight = 0;
            this.handleClose();
            this.performData = [];
        },
        // 构建组件数据
        setPerformItem(performData) {
            const data = _.cloneDeep(this.performOverview);
            const { trade, query, other } = performData?.handlingPerformances;
            data.title.label.labelDic[0].label = performData?.clusterInstanceType;
            data.title.label.labelInfo.clusterName = performData.clusterName;
            data.title.slots[0].value = performData.shardingNo;
            data.details[0].infoId = trade?.id || '';
            data.details[0].info = this.formatDetailData(trade);
            data.details[1].infoId = query?.id  || '';
            data.details[1].info = this.formatDetailData(query);
            data.details[2].infoId = other?.id  || '';
            data.details[2].info = this.formatDetailData(other);
            return data;
        },
        // 数据单位换算
        formatDetailData(obj) {
            const { value, unit } = autoConvertTime(obj?.latency);
            return {
                id: obj?.id,
                throughput: (formatNumber(obj?.throughput) || '-') + ' tps',
                latency: transferVal(obj?.latency) ? value + ' ' + unit : '- ns',
                errorRate: (formatNumber(obj?.errorRate)  || '-') + ' %'
            };
        },
        // 点击事件
        async handleBarClick(id) {
            this.selectInfoId = id || '';
            if (!id) return;
            this.modelInfo.status = true;
            await this.getCoreClusterDetail(id);
            this.fetTableHeight();
        },
        // 关闭弹窗
        handleClose() {
            this.selectInfoId = '';
            this.modelInfo = {
                status: false,
                tableData: []
            };
        },
        // 构建页面数据
        async getPerformanceData() {
            await this.getCoreCluster();
            // 点击弹窗表格轮询最新数据
            if (Array.isArray(this.performData) && this.performData?.length) {
                const res = this.performData.filter(v => v?.handlingPerformances?.trade?.id === this.selectInfoId || v?.handlingPerformances?.query?.id === this.selectInfoId || v?.handlingPerformances?.other?.id === this.selectInfoId);
                const id = res?.length ? this.selectInfoId : '';
                this.handleBarClick(id);
            }
        },
        // 获取核心集群处理性能
        async getCoreCluster() {
            const params = {
                productId: this.productInstNo
            };
            try {
                const res = await getCoreCluster(params);
                if (this.productInstNo !== params.productId) return;
                if (res.code === '200') {
                    this.performData = this.setPerformData([...res.data] || []);
                } else {
                    this.clearPolling();
                }
            } catch (err) {
                this.clearPolling();
                console.log(err);
            }
        },
        // 设置接口返回数据
        setPerformData(performData){
            const res = [];
            for (const performItem of Object.values(performData)){
                res.push(
                    {
                        clusterName: performItem.clusterName,
                        clusterInstanceType: this.$store.state.apmDirDesc?.appTypeDictDesc?.[performItem?.clusterInstanceType] || performItem?.clusterInstanceType,
                        shardingNo: performItem.shardingNo,
                        handlingPerformances: this.setHandlingPerformances(performItem.handlingPerformances, performItem.clusterId)
                    }
                );
            }
            return res;
        },
        // 设置id
        setHandlingPerformances(handlingPerformances, clusterId){
            const data =  {};
            for (const item of Object.values(handlingPerformances)){
                data[item.type] = {
                    id: clusterId + '@' + item.type,
                    ...item
                };
            }
            return data;
        },
        //  获取核心集群处理功能号列表
        async getCoreClusterDetail(id){
            const params = {
                clusterId: id?.split('@')?.[0],
                type: id?.split('@')?.[1],
                productId: this.productInstNo
            };
            try {
                const res = await getCoreClusterDetail(params);
                if (this.selectInfoId !== [params.clusterId, params.type].join('@')) return;
                if (res.code === '200') {
                    this.modelInfo.tableData = [...res?.data] || [];
                } else {
                    this.handleClose();
                }
            } catch (err) {
                this.handleClose();
                console.log(err);
            }
        },
        // 定时器
        setPolling(timerInterval) {
            this.clearPolling();
            this.timer = setInterval(() => {
                this.getPerformanceData();
            }, timerInterval * 1000);
        },
        // 清楚定时器
        clearPolling() {
            this.timer && clearInterval(this.timer);
        }
    },
    beforeDestroy() {
        this.clearPolling();
        window.removeEventListener('resize', this.fetTableHeight);
    }
};
</script>
<style lang="less" scoped>
.perform-box {
    width: 100%;
    height: 100%;
    padding: 0 10px;
    overflow: auto;

    .cluster-box {
        display: grid;
        grid-template-columns: repeat(2, 1fr);
        grid-auto-rows: row dense;
        grid-gap: 10px;
    }

    .info-sum-bar {
        margin-top: 0;

        /deep/ .info-bar {
            border: 2px solid var(--primary-color);

            &:hover {
                border: 2px solid var(--link-opacity-color);
            }
        }
    }
}

/deep/ .h-modal-body {
    padding: 16px;
}

/deep/ .h-drawer-body {
    overflow: hidden;
}
</style>

