<template>
    <div class="main">
        <header>
            <a-title title="应用状态墙">
                <slot>
                    <div class="title-box">
                        <div class="content-top-operation-type">
                            <div
                                class="content-top-operation-type-item"
                                :data-current="groupMode==='service'"
                                @click="() => changetype('service')">
                                服务视图
                            </div>
                            <div
                                class="content-top-operation-type-item"
                                :data-current="groupMode==='deploy'"
                                @click="() => changetype('deploy')">
                                部署视图
                            </div>
                        </div>

                        <div v-show="productList.length > 1" class="input-line"></div>

                        <h-icon
                            class="title-icon"
                            name="setup"
                            size="18"
                            color="var(--font-color)"
                            @on-click="handleMonitorDrawer"></h-icon>

                        <h-select
                            v-show="productList.length > 1"
                            v-model="productInstNo"
                            placeholder="请选择"
                            :positionFixed="true"
                            :clearable="false"
                            @on-change="checkProduct">
                            <h-option
                                v-for="item in productList"
                                :key="item.id"
                                :value="item.productInstNo"
                                >{{ item.productName }}</h-option>
                        </h-select>
                    </div>
                </slot>
            </a-title>
        </header>
        <div class="tab-box">
            <cluster-and-group-monitor
                ref="appStatus"
                :productInstNo="productInstNo"
                :groupMode="groupMode"
                :hiddenArbInstance="hiddenArbInstance"
                type="instance" />
        </div>
        <!-- 监控告警配置 -->
        <h-drawer
            v-model="visible"
            width="480"
            title="配置"
            class="drawer">
            <p class="drawer-title">基础配置</p>
            <div style="margin: 10px;">
                显示仲裁节点
                <h-switch
                    v-model="hiddenArbInstanceBackUp"></h-switch>
            </div>
            <p class="drawer-title">告警规则配置</p>

            <div class="drawer-tip">可配置核心节点在各时间段内的标准状态。当节点在某一时间内的状态与预设标准不符，将会告警。注意：同一时间只存在一种状态，修改后记得及时保存。</div>
            <h-row class="drawer-th">
                <h-col span="2" style="padding-left: 10px;">序号</h-col>
                <h-col span="7" style="padding-left: 10px;">开始时间</h-col>
                <h-col span="7" style="padding-left: 10px;">结束时间</h-col>
                <h-col span="7" style="padding-left: 10px;">节点状态</h-col>
            </h-row>
            <div class="drawer-monitor-list">
                <div
                    v-for="(item, index) in monitorList"
                    :key="index"
                    class="drawer-monitor">
                    <span style="padding: 0 20px 0 6px; color: #fff;">{{ index + 1 }}</span>
                    <h-time-picker
                        v-model="item.startTime"
                        type="time"
                        :clearable="false"
                        placeholder="开始时间"
                        autoPlacement
                        transfer
                        style="width: 150px;"
                    ></h-time-picker>
                    <span style="padding: 0 5px;">~</span>
                    <h-time-picker
                        v-model="item.endTime"
                        type="time"
                        :clearable="false"
                        autoPlacement
                        transfer
                        placeholder="结束时间"
                        style="width: 150px;"
                    ></h-time-picker>
                    <h-select
                        v-model="item.bizStatus"
                        autoPlacement
                        :clearable="false"
                        positionFixed
                        style="width: 150px;
                        margin-left: 22px;">
                        <h-option
                            v-for="item in bizStatuList"
                            :key="item.key"
                            :value="item.key">
                            {{ item.value }}
                        </h-option>
                    </h-select>
                    <h-icon
                        class="drawer-trash"
                        name="trash"
                        size="20"
                        color="#9296A1"
                        @on-click="handleRuleDel(index)">
                    </h-icon>
                </div>
            </div>

            <a-button
                v-show="monitorList.length < 11"
                class="drawer-push"
                type="dark"
                icon="plus-round"
                @click="handleRulePush">
            新增规则</a-button>
            <div slot="footer">
                <a-button
                    class="drawer-save"
                    type="primary"
                    @click="saveMonitorRules">保存配置</a-button>
                <a-button
                    type="dark"
                    @click="visible=false">关闭</a-button>
            </div>
        </h-drawer>

        <alarm-drawer ref="alarmDrawer" :productId="productInstNo" />
    </div>
</template>
<script>
import _ from 'lodash';
import { mapState, mapActions } from 'vuex';
import { getMonitorRules, saveMonitorRules } from '@/api/httpApi';
import aTitle from '@/components/common/title/aTitle';
import aButton from '@/components/common/button/aButton';
import alarmDrawer from '@/components/ldpProduct/alarmDrawer/alarmDrawer';
import clusterAndGroupMonitor from '@/components/ldpMonitor/clusterAndGroupMonitor';
export default {
    components: { clusterAndGroupMonitor, aTitle, aButton, alarmDrawer },
    data() {
        return {
            productInstNo: '',
            groupMode: 'service',
            hiddenArbInstance: true,
            hiddenArbInstanceBackUp: true,
            visible: false,
            monitorList: [],
            monitorHistoryInfo: {
                status: false
            },
            showModel: 'card' // card or table
        };
    },
    async mounted() {
        this.loading = true;
        await this.getProductList();
        this.loading = false;
        const productInstNo = localStorage.getItem('productInstNo');
        this.productInstNo =  _.find(this.productList, ['productInstNo', productInstNo])?.productInstNo ||
            this.productList?.[0]?.productInstNo;
        this.checkProduct(this.productInstNo);
    },
    computed: {
        ...mapState({
            productList: state => {
                return _.filter(state.product.productListLight, function (o) {
                    return o.productType !== 'NSQ1.0';
                }) || [];
            }
        }),
        bizStatuList() {
            const bizStatusDict = this.$store?.state?.apmDirDesc?.bizStatusDict;
            const list = [];
            Object.keys(bizStatusDict).forEach(ele => {
                list.push({
                    key: Number(ele),
                    value: bizStatusDict[ele]
                });
            });
            return list;
        }
    },
    methods: {
        ...mapActions({ getProductList: 'product/getProductListLight' }),
        async onCheckboxChange(val, key){
            this.clearPolling();
            key === 'hiddenArbInstance' && (this.hiddenArbInstance = val);
            await this.$refs['appStatus'] && this.$refs['appStatus'].init();
        },
        // 切换产品
        checkProduct(e) {
            if (this.productList.length) {
                this.productInfo = e ? _.find(this.productList, ['productInstNo', e]) : this.productList[0];
                this.productInstNo = this.productInfo.productInstNo;
                localStorage.setItem('productInstNo', this.productInfo.productInstNo);
            }
        },
        // 打开监控页面
        handleMonitorDrawer() {
            this.hiddenArbInstanceBackUp = this.hiddenArbInstance;
            this.visible = true;
            this.getMonitorRules();
        },
        // 获取监控列表
        async getMonitorRules() {
            const res = await getMonitorRules({
                productId: this.productInfo.productInstNo
            });
            if (res.success) {
                this.$nextTick(() => {
                    this.monitorList = _.find(res?.data || [], ['monitorRuleCode', 'appBizStatusMonitor'])?.monitorRuleParams || [];
                });
            }
        },
        // 添加规则
        handleRulePush() {
            this.monitorList.push({
                startTime: '09:15:00',
                endTime: '15:00:00',
                bizStatus: 1
            });
        },
        // 删除规则
        handleRuleDel(index) {
            this.monitorList.splice(index, 1);
        },
        // 保存规则
        saveMonitorRules() {
            this.$hMsgBoxSafe.confirm({
                title: '保存',
                content: `您确定保存当前所有配置吗？`,
                onOk: async () => {
                    const param = {
                        productId: this.productInfo.productInstNo,
                        monitorRuleCode: 'appBizStatusMonitor',
                        monitorRuleParams: this.monitorList
                    };
                    try {
                        const res = await saveMonitorRules(param);
                        if (res.success) {
                            this.$hNotice.success({
                                title: '保存成功',
                                duration: 2 });
                        } else if (res.code.length === 8) {
                            this.$hNotice.warning({
                                title: '保存失败',
                                desc: res.message
                            });
                        }
                    } catch (error) {
                        console.error(error);
                    } finally {
                        this.hiddenArbInstance = this.hiddenArbInstanceBackUp;
                        this.onCheckboxChange(this.hiddenArbInstance, 'hiddenArbInstance');
                    }
                }
            });
        },
        getMonitorEvents(id) {
            this.$refs['alarmDrawer'].init('instanceId', id);
        },
        clearPolling() {
            const appStatus = this.$refs['appStatus'];
            appStatus && appStatus.clearPolling();
        },
        async changetype(type) {
            this.groupMode = type;
            await this.$refs['appStatus'] && this.$refs['appStatus'].init();
        }
    },
    watch: {
        productInstNo() {
            this.clearPolling();
            this.$nextTick(async () => {
                await this.$refs['appStatus'] && this.$refs['appStatus'].init();
            });
        }
    },
    provide() {
        return {
            getMonitorEvents: this.getMonitorEvents // 提供方法给后代组件
        };
    },
    beforeDestroy(){
        this.clearPolling();
    }
};
</script>
<style lang="less" scoped>
@import url("@/assets/css/drawer.less");

.title-box {
    display: flex;
    align-items: center;
    position: absolute;
    top: 0;
    right: 0;
    height: 44px;

    & > .h-select {
        width: auto;
        min-width: 200px;
        display: inline-block;
        margin-right: 8px;
    }

    .input-line {
        display: inline-block;
        width: 1px;
        height: 24px;
        margin-right: 8px;
        background: var(--base-color);
    }

    .title-icon {
        cursor: pointer;
        border: var(--border);
        border-radius: 4px;
        padding: 0 6px;
        margin-right: 10px;
        height: 32px;
        line-height: 31px;
    }

    .title-group-select {
        width: 120px !important;
        min-width: 150px;
    }

    & > .h-checkbox-wrapper {
        margin-right: 8px;
    }
}

.tab-box {
    width: 100%;
    margin-top: 6px;
    height: calc(100% - 50px);

    /deep/.h-tabs {
        height: 100%;
        background: var(--main-color);
    }

    /deep/ .h-tabs-nav-container {
        height: 42px;
    }

    /deep/ .h-tabs-bar {
        border-bottom: var(--border);
    }

    /deep/ .h-tabs-tab {
        padding: 10px 6px;
    }

    /deep/.h-tabs .h-tabs-content-wrap {
        height: calc(100% - 50px);
        overflow: hidden;
    }

    /deep/.h-tabs .h-tabs-tabpane {
        height: calc(100% - 10px);
        cursor: pointer;
        overflow: hidden;
    }

    /deep/.h-tabs .h-tabs-content {
        height: 100%;
    }
}

.drawer-title {
    color: #fff;
    line-height: 22px;
    padding-left: 8px;

    &::before {
        display: inline-block;
        position: relative;
        left: -5px;
        top: 2px;
        content: "";
        width: 3px;
        height: 12px;
        background: var(--link-color);
    }
}

.drawer-tip {
    position: relative;
    margin-top: 8px;
    padding: 6px 10px 6px 30px;
    line-height: 20px;
    background-color: #1f3759;
    border-radius: 4px;
    color: #fff;

    &::before {
        position: absolute;
        left: 10px;
        top: 5px;
        color: #2d8de5;
        content: "ⓘ";
    }
}

.drawer-th {
    height: 42px;
    line-height: 42px;
    margin-top: 10px;
    background-color: #262d43;
    color: #fff;
}

.drawer-monitor-list {
    width: 100%;
    max-height: calc(100% - 196px);
    overflow-y: auto;

    & > .drawer-monitor {
        display: flex;
        align-items: center;
        height: 42px;
        padding: 0 8px;
        margin-top: 10px;
        background-color: #262d43;
    }
}

.drawer-push {
    margin-top: 10px;
    border: 1px dashed #485565;
}

.drawer-trash {
    margin-left: 5px;
    cursor: pointer;

    &:hover {
        color: rgb(115, 65, 65) !important;
    }
}

/deep/ .h-drawer-footer {
    border-top: 1px solid #444a60;

    .drawer-save {
        margin: 8px 5px 8px 10px;
    }
}

.content {
    display: flex;
    flex-direction: column;
    height: calc(100% - 44px);
    overflow-x: auto;

    &-top {
        display: flex;
        height: 44px;
        width: calc(100vw - 16px);

        &-tab {
            flex: 1;

            .h-tabs-bar {
                border-bottom: none;
            }

            .h-tabs-nav-container {
                border-bottom: var(--border);
                padding-left: 20px;

                .h-tabs-tab {
                    height: 44px;
                    color: #fff;
                    padding: 10px 0;
                    margin-right: 20px;

                    &:last-child {
                        margin-right: 0;
                    }
                }

                .h-tabs-return,
                .h-tabs-enter {
                    height: 43px;
                    width: 20px;
                    padding: 0;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                }

                .h-tabs-ink-bar {
                    left: 8px;
                    width: unset !important;
                }

                .h-tabs-nav .h-tabs-tab-active {
                    color: #298dff;
                }

                .h-tabs-tab-disabled {
                    cursor: not-allowed;
                }
            }

            &[data-loading="true"] {
                .h-tabs-nav-container {
                    cursor: not-allowed;
                }
            }

            &[data-showarrow="true"] {
                .h-tabs-nav-container {
                    padding-left: 0;
                }
            }
        }

        &-operation {
            display: flex;
            align-items: center;
            border-bottom: var(--border);
            flex-wrap: nowrap;

            .h-select-selection {
                color: var(--font-color);
                background-color: var(--input-bg-color);
                border: var(--border);
                margin-right: 8px;
                flex: 1;
            }

            &-line {
                background: #474e6f;
                height: 26px;
                width: 1px;
                margin-right: 8px;
            }

            &-setting {
                background: #262d43;
                border: 1px solid #485565;
                border-radius: 4px;
                cursor: pointer;

                .h-icon {
                    color: #fff;
                    width: 32px;
                    height: 32px;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                }
            }

            &-type {
                width: 128px;
                background: #262d43;
                border: 1px solid #485565;
                border-radius: 4px;
                display: flex;
                flex-wrap: nowrap;
                align-items: center;
                height: 32px;
                justify-content: space-between;
                padding: 4px;
                margin-right: 8px;

                &-item {
                    cursor: pointer;
                    width: 120px;
                    text-align: center;
                    height: 100%;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    color: #9296a1;
                    font-size: 12px;

                    &[data-current="true"] {
                        background: #383f59;
                        border-radius: 2px;
                        color: #fff;
                    }
                }
            }
        }
    }

    &-wrap {
        height: calc(100% - 44px);
        flex: 1;
        overflow-y: auto;

        &-table {
            td,
            .h-table-cell {
                text-align: right !important;
            }

            .core-func-left {
                .h-table-cell {
                    text-align: left !important;
                }
            }
        }
    }

    &-list {
        display: flex;
        flex-wrap: wrap;
        height: inherit;
        align-content: baseline;

        &-row {
            display: flex;

            &-item {
                margin-top: 15px;
                height: 109px;
                transition: all 0.3s;
            }
        }

        .empty-funcs {
            margin-top: 200px;
        }
    }
}

@media only screen and (max-width: 1014px) {
    .content-list {
        min-width: 983px;

        &[data-big-than-two="true"] {
            overflow-x: auto;
        }

        &[data-big-than-two="false"] {
            overflow-x: unset;
            min-width: unset;

            .content-list-row-item {
                min-width: 480px;
            }
        }
    }

    .content-list-row-item {
        width: 50%;
        box-sizing: border-box;

        &:nth-child(2n-1) {
            padding-right: 10px;
        }
    }
}

@media only screen and (min-width: 1014px) {
    .content-list-row-item {
        width: 50%;
        box-sizing: border-box;

        &:nth-child(2n-1) {
            padding-right: 10px;
        }
    }
}

@media only screen and (min-width: 1681px) {
    .content-list-row-item {
        width: 33.3%;
        margin-top: 15px;
        margin-right: 0 !important;
        margin-left: 0 !important;
        box-sizing: border-box;
        padding-right: 0 !important;

        &[data-is-third-count="true"] {
            padding-left: 10px;
            padding-right: 10px !important;
        }
    }
}

.main-loading-wraper {
    position: fixed;
    width: 300px;
    height: 300px;
    top: 50%;
    left: 50%;
    transform: translateX(-50%) translateY(-50%);

    .spin-container {
        background: var(--main-color);
    }
}

</style>
