<!-- eslint-disable vue/no-deprecated-slot-attribute -->
<template>
    <div class="cluster-form">
        <div class="title" style="position: relative;">
            <span>集群参数配置</span>&nbsp;&nbsp;<h-icon :name="buttonStatus1 ? 'packup' : 'unfold'"
                            size="16" style="cursor: pointer;" @on-click='changeButtonStatus1'></h-icon>
            <span style="position: absolute; right: 140px;">引用模板</span>
            <h-select v-model="ref" placeholder="请选择" :positionFixed="true"
                style="width: 120px; position: absolute; right: 0; top: -6px;" :clearable="true"
                @on-change="handleSelectChange">
                <h-option v-for="item in clusterTemplateNames" :key="item.value" :value="item.value">{{ item.label }}</h-option>
            </h-select>
        </div>
        <div class="border"></div>
        <cluster-setting v-show="buttonStatus1" ref="cluster-setting" :rcmId="rcmId" :saveValidateData="formValidate"/>
        <div style="margin-top: 24px;">
            <div class="title" style="position: relative;">
                <span>集群上下文配置</span>&nbsp;&nbsp;<h-icon :name="buttonStatus2 ? 'packup' : 'unfold'"
                            size="16" style="cursor: pointer;" @on-click='changeButtonStatus2'></h-icon>
            </div>
            <div class="border"></div>
            <h-tabs v-show="buttonStatus2" v-model="tabValue" type="card" showArrow :closable="type === 'add' && contexts.length !== 2" :isRemoveTab="false" @on-before-tab-remove="handleTabRemove">
                <h-tab-pane v-for="(item, idx) in contexts" :key="idx.toString()" :label="item.name || `${contextName+'#'+idx}`" :name="idx.toString()">
                    <normal-setting :ref="`normal-setting-${idx}`" :rcmId="rcmId" :saveValidateData="item"/>
                </h-tab-pane>
                <a-button v-if="type === 'add'" slot="extra" style="margin-bottom: 5px;" type="ghost" shape="circle" size="small" icon="plus-round"  @click="handleTabsAdd"></a-button>
            </h-tabs>
        </div>
    </div>
</template>

<script>
import { normalTempDefault, clusterTempDefault } from '@/config/rcmDefaultConfig';
import aButton from '@/components/common/button/aButton';
import clusterSetting from '@/components/rcmDeploy/modal/clusterSetting';
import normalSetting from '@/components/rcmDeploy/modal/normalSetting';
import { getTemplateDetail } from '@/api/rcmApi';
export default {
    props: {
        clusterTemplateNames: {
            type: Array,
            default: []
        },
        clusterSetting: {
            type: Object,
            default: null
        },
        type: {
            type: String,
            default: ''
        },
        contextName: {
            type: String,
            default: ''
        },
        rcmId: {
            type: String,
            default: 'f5vUs4gBMZNZCXioG9WA'
        }
    },
    name: 'ClusterContextSetting',
    components: { clusterSetting, normalSetting, aButton },
    data() {
        return {
            formValidate: {
                ...clusterTempDefault
            },
            tabValue: '',
            ref: '',
            contexts: [],
            isChange: true,
            buttonStatus1: true,
            buttonStatus2: true
        };
    },
    mounted() {
    },
    methods: {
        // 初始化
        init() {
            this.isChange = false;
            this.formValidate = { ...this.formValidate, ...this.clusterSetting.clusterParam };

            if (this.type === 'add'){
                this.contexts = [];
                this.handleTabsAdd(2);
            } else {
                this.contexts = [...this.clusterSetting.contexts];
            }
            this.ref = this.isExistRef(this.clusterSetting?.ref) ? this.clusterSetting?.ref : '';
            // 模板切换值
            this.$nextTick(() => {
                this.$refs['cluster-setting'].init();
                for (const idx of Object.keys(this.contexts)){
                    this.$refs[`normal-setting-${idx}`][0].init();
                }
                this.tabValue = this.type !== 'add' ? this.findIndexById(this.clusterSetting.id) : '0';
            });

            setTimeout(() => {
                this.isChange = true;
            }, 1000);
        },
        // 找寻index值
        findIndexById(id){
            const index = this.contexts.findIndex(e => e.id === id);
            return String(index) || '0';
        },
        // 判断模板是否存在
        isExistRef(ref){
            const refs = this.clusterTemplateNames.filter(v => v.value === ref);
            return refs.length;
        },
        // 返回数据
        getFileData() {
            const clusterRes = this.$refs['cluster-setting'].getFileData();
            let normalRes = true;
            for (const [idx, item] of Object.entries(this.contexts)){
                const res = this.$refs[`normal-setting-${idx}`][0].getFileData();
                if (!res){
                    normalRes = false;
                    break;
                } else {
                    this.contexts[idx] = {
                        name: this.type !== 'add' ? item.name : '',
                        ...res
                    };
                }
            }
            if (clusterRes && normalRes) {
                return {
                    ...clusterRes,
                    contexts: this.type !== 'add' ? this.contexts : this.changeContext(),
                    ref: this.ref
                };
            } else {
                return false;
            }
        },
        // 模板切换
        async handleSelectChange(val) {
            if (this.isChange){
                if (!val) return;
                const param = {
                    id: this.rcmId,
                    name: val,
                    templateType: 'clusterContext'
                };
                const res = await getTemplateDetail(param);
                if (res.code === '200') {
                    const clusterContext = res.data?.template?.clusterContext;
                    this.formValidate = {
                        syncIp: clusterContext.syncIp,
                        syncAddr: clusterContext.syncAddr,
                        syncPort: clusterContext.syncPort,
                        syncHeartbeatIntervalMilli: clusterContext.syncHeartbeatIntervalMilli,
                        syncHeartbeatTimeoutMilli: clusterContext.syncHeartbeatTimeoutMilli,
                        syncAckIntervalMilli: clusterContext.syncAckIntervalMilli,
                        syncAckTimeoutMilli: clusterContext.syncAckTimeoutMilli,
                        syncSendWindowSize: clusterContext.syncSendWindowSize,
                        syncMtu: clusterContext.syncMtu,
                        syncMode: clusterContext.syncMode,
                        zone: clusterContext.zone
                    };
                    for (const [index, item] of Object.entries(this.contexts)){
                        this.contexts[index] = {
                            id: this.type !== 'add' ? item.id : '',
                            name: this.type !== 'add' ? item.name : '',
                            ip: clusterContext.ip,
                            recordDir: clusterContext.recordDir,
                            repairPortStart: clusterContext.repairPortStart,
                            repairPortEnd: clusterContext.repairPortEnd,
                            ttl: clusterContext.ttl,
                            mtu: clusterContext.mtu,
                            sendWindowSize: clusterContext.sendWindowSize,
                            maxMemoryAllowedMBytes: clusterContext.maxMemoryAllowedMBytes,
                            socketBufferSizeKBytes: clusterContext.socketBufferSizeKBytes,
                            mcLoop: clusterContext.mcLoop,
                            hasTotalOrder: clusterContext.hasTotalOrder,
                            hasRecord: clusterContext.hasRecord
                        };
                    }
                }

                // 模板切换值
                this.$nextTick(() => {
                    this.$refs['cluster-setting'].init();
                    for (const idx of Object.keys(this.contexts)){
                        this.$refs[`normal-setting-${idx}`][0].init();
                    }
                    this.tabValue = '0';
                });
            }
        },
        // 显示、隐藏
        changeButtonStatus1() {
            this.buttonStatus1 = !this.buttonStatus1;
        },
        changeButtonStatus2() {
            this.buttonStatus2 = !this.buttonStatus2;
        },
        // 增加tab页
        async handleTabsAdd(index = 1){
            if (!this.ref) {
                for (let i = 0; i < index; i++) {
                    this.contexts.push({
                        id: null,
                        ...normalTempDefault
                    });
                }
            } else {
                const param = {
                    id: this.rcmId,
                    name: this.ref,
                    templateType: 'clusterContext'
                };
                const res = await getTemplateDetail(param);
                if (res.code === '200') {
                    const clusterContext = res.data?.template?.clusterContext;
                    for (let i = 0; i < index; i++) {
                        this.contexts.push({
                            id: null,
                            appName: '',
                            ip: clusterContext.ip,
                            recordDir: clusterContext.recordDir,
                            repairPortStart: clusterContext.repairPortStart,
                            repairPortEnd: clusterContext.repairPortEnd,
                            ttl: clusterContext.ttl,
                            mtu: clusterContext.mtu,
                            sendWindowSize: clusterContext.sendWindowSize,
                            maxMemoryAllowedMBytes: clusterContext.maxMemoryAllowedMBytes,
                            socketBufferSizeKBytes: clusterContext.socketBufferSizeKBytes,
                            mcLoop: clusterContext.mcLoop,
                            hasTotalOrder: clusterContext.hasTotalOrder,
                            hasRecord: clusterContext.hasRecord
                        });
                    }
                }
            }
            this.tabValue = String(this.contexts.length - 1) || '';
        },
        // 删除tab页
        handleTabRemove(index, name) {
            // 获取最新的contexts值
            for (const idx of Object.keys(this.contexts)){
                const res = this.$refs[`normal-setting-${idx}`][0].getFileData();
                res && (this.contexts[idx] = { ...res });
            }
            this.contexts.splice(index, 1);
            // 页面更新
            for (const [idx, item] of Object.entries(this.contexts)){
                this.$refs[`normal-setting-${idx}`][0].init(item);
            }
            this.tabValue = '0' || '';
        },
        changeContext(){
            for (const [index, item] of Object.entries(this.contexts)){
                this.contexts[index] = {
                    ...item,
                    name: item.name || this.contextName + '#' + index
                };
            }
            return this.contexts;
        }
    }
};
</script>
<style lang="less" scoped>
.cluster-form {
    .title {
        padding-left: 12px;

        span {
            font-size: 14px;
            font-weight: 600;
        }
    }

    .border {
        border-top: 1px solid #b0b4ba;
        margin-bottom: 15px;
        margin-top: 10px;
    }

    /deep/ .h-tabs-tab {
        padding: 6px;
    }
}
</style>
