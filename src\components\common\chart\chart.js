import * as echarts from 'echarts';
import './chart.less';
import _ from 'lodash';
import obsTitle from '@/components/common/title/obsTitle';
import { echartOptionAssign } from '@/utils/echartOption';
import { xyOption, pieOption, barOption, whiteXyOption, noDataOption } from '@/components/common/chart/chartConfig';
export default {
    name: 'obsChart',
    components: { obsTitle },
    props: {
        title: {
            type: Object,
            default: () => {}
        },
        chartRef: {
            type: String,
            default: 'chart'
        },
        slotHeight: {
            type: Number,
            default: 0
        },
        isWhiteBg: {
            type: Boolean,
            default: false
        },
        basicOpiton: {
            type: Object,
            default: null
        },
        additionalOpiton: {
            type: Object,
            default: null
        },
        chartData: {
            type: Object,
            default: null
        }
    },
    data() {
        return {
            myChart: null,
            supportClick: false, // 饼图是否支持选中
            selectKey: '', // 选中点索引
            option: {}
        };
    },
    mounted() {
        if (this.$refs[this.chartRef]) {
            this.myChart = echarts.init(this.$refs[this.chartRef], '#262B40');
            this.initOption();
            this.resizeObserver = new ResizeObserver(() => {
                this.resize();
            });
            this.resizeObserver.observe(this.$refs[this.chartRef]);
        }
    },
    beforeDestroy() {
        this.resizeObserver && this.resizeObserver.disconnect(this.$refs[this.chartRef]);
    },
    methods: {
        // 标题插槽事件
        handleButtonClick(key) {
            this.$emit('button-click', key);
        },
        handleCheckChange(val, key) {
            this.$emit('check-change', val, key);
        },
        handleSelectChange(val, key) {
            val && this.$emit('select-change', val, key);
        },
        setSelectVal(key, val) {
            this.$refs['obs-title'].setSelectVal(key, val);
        },
        getSelectVal(key) {
            return this.$refs['obs-title'].getSelectVal(key);
        },
        setCheckVals(key, val) {
            this.$refs['obs-title'].setCheckVals(key, val);
        },
        getCheckVals(key) {
            return this.$refs['obs-title'].getCheckVals(key);
        },
        // 生成渐变色
        generateGradientColor(color) {
            const [r, g, b] = color;
            return [`rgba(${r}, ${g}, ${b}, 1)`, `rgba(${r}, ${g}, ${b}, .5)`, `rgba(${r}, ${g}, ${b}, .1)`];
        },
        // 获取表格配置生成option
        initOption() {
            const { chartType, lineDeploy, yAxiDeploy, supportClick } = this.basicOpiton;
            if ((chartType === 'axis' || chartType === 'horizontalBar') && lineDeploy) {
                const option = {
                    yAxis: yAxiDeploy,
                    series: []
                };
                Array.isArray(lineDeploy) && lineDeploy.forEach(item => {
                    const areaColor = item?.areaColor;
                    const itemColor = item?.itemColor;
                    const unitTooltip = {
                        valueFormatter: function (value) {
                            const res = (value?.toString() && value !== '-') ? value + ' ' + (item?.unit || '') : '-';
                            return res;
                        }
                    };
                    option.series.push({
                        name: item?.name,
                        type: item?.type,
                        tooltip: item?.tooltip || unitTooltip,
                        yAxisIndex: item?.yAxisIndex || 0,
                        stack: item?.stack || undefined, // 堆叠
                        barMaxWidth: item?.barWidth || 12,
                        realtimeSort: item?.realtimeSort, // 动态排序
                        label: item?.label, // 展示数值
                        showSymbol: item?.showSymbol || false,
                        smooth: true,
                        sampling: item?.sampling || undefined, // 降采样策略, 可选: 'lttb', 'average', 'min', 'max'等
                        lineStyle: areaColor ? {
                            color: this.generateGradientColor(areaColor)?.[0]
                        } : undefined,
                        itemStyle: itemColor ? {
                            color: itemColor
                        } : undefined,
                        areaStyle: areaColor ? {
                            opacity: 0.5,
                            color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                                {
                                    offset: 0.1,
                                    color: this.generateGradientColor(areaColor)[0]
                                },
                                {
                                    offset: 0.5,
                                    color: this.generateGradientColor(areaColor)[1]
                                },
                                {
                                    offset: 1,
                                    color: this.generateGradientColor(areaColor)[2]
                                }
                            ])
                        } : item.areaStyle ? item.areaStyle : undefined
                    });
                });
                this.option = echartOptionAssign(_.cloneDeep(this.isWhiteBg ? whiteXyOption : xyOption), _.cloneDeep(option));
                if (chartType === 'horizontalBar') {
                    this.option.xAxis = barOption.xAxis;
                    this.option.yAxis = { ...barOption.yAxis, ...yAxiDeploy };
                }
            } else if (chartType === 'pie') {
                this.option = _.cloneDeep(pieOption);
                this.option.series[0].selectedMode = supportClick ? 'single' : false;
                // 点击事件监听
                this.supportClick = supportClick;
                supportClick && this.clickEvent();
            }
            if (this.additionalOpiton && Object.keys(this.additionalOpiton)?.length) {
                this.option = echartOptionAssign(_.cloneDeep(this.additionalOpiton), _.cloneDeep(this.option));
            }
            this.refreshData(this.chartData);
        },
        clickEvent() {
            const that = this;
            let selectKey = '';
            this.myChart.on('click', function(params) {
                selectKey = params.data.key;
                // 如果点击的是饼图扇区
                if (params.componentType === 'series' && params.seriesType === 'pie') {
                    // 获取所有的饼图数据
                    const data = that.option.series[0].data;
                    // 遍历所有数据，设置其 selected 状态为 false（取消选择）
                    data.forEach(function (item) {
                        if (item.key === selectKey) {
                            item.selected = true;
                        } else {
                            item.selected = false;
                        }
                    });
                    // 使用 setOption 重新渲染饼图
                    that.myChart.setOption(that.option);
                    that.selectKey = selectKey;
                    that.$emit('pie-click', selectKey);
                }
            });
        },
        // 转换y轴的数值，undefined || null异常兜底
        converYaxisData(yaxisData) {
            if (!yaxisData || !Array.isArray(yaxisData)) return yaxisData;
            return yaxisData.map(item => item ?? '-');
        },
        // 刷新数据
        refreshData(chartData) {
            if (this.basicOpiton.chartType === 'axis') {
                if (!this.option.series || !this.option.xAxis) return;
                this.option.xAxis.data = chartData?.xData || [];
                this.option.series.forEach(item => {
                    item.data = this.converYaxisData(chartData?.data?.[item.name]);
                });
                this.myChart && this.myChart.setOption(this.option);
            } else if (this.basicOpiton.chartType === 'pie') {
                if (chartData?.data?.length) {
                    let isExist = false;
                    // 默认选中-key存在则选中，不存在选中第一个
                    this.supportClick && chartData.data.forEach(item => {
                        if (item.key === this.selectKey) {
                            item.selected = true;
                            isExist = true;
                        } else {
                            item.selected = false;
                        }
                    });
                    chartData.data[0].selected = isExist ? chartData.data[0].selected : true;
                    this.option.series[0].data = chartData.data;
                    this.myChart && this.myChart.setOption(this.option, true);
                } else {
                    this.myChart && this.myChart.setOption(noDataOption, true);
                }
            } else if (this.basicOpiton.chartType === 'horizontalBar') {
                this.option.series.forEach(item => {
                    item.data = chartData?.data?.[item.name];
                });
                this.myChart && this.myChart.setOption(this.option);
            }
        },
        // 表格重置尺寸
        resize() {
            this.myChart && this.myChart.resize();
        }
    },
    watch: {
        basicOpiton: {
            handler(newVal, oldVal) {
                if (newVal) {
                    this.initOption();
                } else {
                    this.myChart.dispose();
                }
            },
            deep: true
        },

        chartData: {
            handler(newVal) {
                if (newVal) {
                    this.refreshData(newVal);
                } else {
                    this.myChart.dispose();
                }
            },
            deep: true
        }
    },
    computed: {
        chartStyle() {
            const style = this.title ? {
                padding: '15px 5px 0',
                width: '100%',
                height: `calc(100% - ${52 + this.slotHeight}px)`
            } : {
                width: '100%',
                height: '100%'
            };
            return style;
        }
    },
    render() {
        return (
            <div class={ this.isWhiteBg ? '' : 'apm-chart'}>
                {this.title && <obs-title ref='obs-title' title={this.title}
                    v-on:button-click={this.handleButtonClick}
                    v-on:check-change={this.handleCheckChange}
                    v-on:select-change={this.handleSelectChange}>
                </obs-title>
                }
                {this.$slots.default}
                <div ref={this.chartRef} style={this.chartStyle}></div>
            </div>
        );
    }
};
