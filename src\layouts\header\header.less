@headerHeight: 48px;
@fontSize: 12px;

.apm-header {
    position: relative;
    width: 100%;
    /* stylelint-disable */
    z-index: 1002;
    display: flex;
    height: @headerHeight;
    // top: 0;
    // left: 0;
    padding: 0 20px;
    // background-color: var(--primary-color);
    background-image: linear-gradient(270deg, #2C3147 0%, #262B40 97%);
    flex-wrap: nowrap;
    font-size: @fontSize;

    ul.h-menu {
        flex: unset;
        width: auto;
        height: @headerHeight;
        // background-color: var(--primary-color);
        background-color: unset;
    }

    .main-title[data-active="true"] {
        // color: #298dff;
        color: #fff;
    }
    .single-title {
      font-size: 12px;
      display: flex;
      align-items: center;
      justify-content: center;
      height: 48px;
    }

    .h-menu-submenu {
        padding: 0;
        height: 48px;
        font-size: @fontSize;
        .h-select-dropdown {
          background: #262D43;
          box-shadow: 0 2px 6px 0 rgba(0,0,0,0.10);
          border-radius: 4px;
          padding: 8px 0;
          animation: none !important;
          border: 1px solid #485565;
          margin: 4px 0;
        }
        .h-menu-item {
          padding: 0 !important;
          background-color: unset !important;
          span {
            display: block;
            padding: 0 12px;
            height: 32px;
            line-height: 32px;
            &[data-active="true"] {
              background: #1F3759;
              color: #2D8DE5;
            }
          }
          &:hover {
            background-color: unset !important;
            span {
              color: #fff;
              background: #1F3759 !important;
            }
          }
        }
    }
    .h-menu-submenu-title {
      height: 48px;
      display: flex;
      align-items: center;
      justify-content: space-between;
      position: relative;
      font-size: @fontSize;
      span {
        line-height: 48px;
        display: flex;
        align-items: center;
        justify-content: space-between;
      }
      .h-icon {
       display: none;
      }
      .icon {
        width: 14px;
        height: 14px;
        margin-left: 2px;
        margin-right: 0;
        transform-origin: center;
        transition: transform .3s;
      }
      &:hover {
        .icon {
          transform: rotate(180deg);
          path {
            fill: white;
          }
        }
      }

    }
    .h-menu-horizontal .h-menu-submenu .h-select-dropdown .h-menu-item {
      font-size: @fontSize !important;
    }
    .h-menu-item {
        padding: 0 10px;
        height: 48px;
        span {
          color: #CACFD4;
        }
        &[data-active="true"] {
          background: #202637;
          &::after {
            display: block;
          }
          .icon path {
            // fill: #2D8DE5;
            fill: #fff;
          }
        }
        &::after {
          content: '';
          position: absolute;
          width: 100%;
          height: 2px;
          background: #2D8DE5;
          left: 0px;
          bottom: 0;
          display: none;
      }
    }

    &-logo {
        height: @headerHeight;
        font-weight: bold;
        display: flex;
        align-items: center;
        cursor: pointer;
        width: 152px;

        img {
            display: block;
            width: 92px;
            margin: 0 20px;
        }
    }

    .menu-stack {
        width: 48px;
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
        position: relative;
        font-size: @fontSize;
        &-icon {
          height: 14px;
          svg {
            width: 14px;
            height: 14px;
            path {
              fill: #CACFD4;
            }
          }
        }
        &:hover {
          background: #202637;
            svg {
              path {
                fill: rgba(255, 255, 255, 1);
              }
            }
          .list-wraper {
            display: block;
            padding-bottom: 60px;
            padding-right: 60px;
          }
        }
        &[data-active="true"] {
          background: #202637;
          &::after {
            content: '';
            position: absolute;
            width: 100%;
            height: 2px;
            background: #2D8DE5;
            left: 0px;
            bottom: 0;
          }
        }
        .list-wraper {
          display: none;
          position: absolute;
          width: auto;
          height: auto;
          top: 48px;
          left: 0;
          transition: left .4s;
          transition-delay: .1s;
        }
        &-list {
          background: #262D43;
          box-shadow: 0 2px 6px 0 rgba(0,0,0,0.10);
          border-radius: 4px;
          padding: 8px 0;
          border: 1px solid #485565;
          margin: 5px 0;
          &-item {
            position: relative;

            &-name {
              display: flex;
              justify-content: space-between;
              align-items: center;
              height: 32px;
              color: #CACFD4;
              white-space: nowrap;
              padding: 0 12px;
              line-height: 32px;
              .icon {
                width: 14px;
                height: 14px;
                transform-origin: center;
                transform: rotate(-90deg);
                margin-left: 14px;
              }
              &[data-active="true"] {
                background-color: #1F3759;
                color: #2D8DE5;
              }
              &:hover {
                background: #1F3759;
                color: #fff;
                .wraper {
                  display: block;
                }
              }
            }
            .wraper {
              position: absolute;
              top: 0;
              left: 100%;
              display: none;
            }
            &-child {
              background: #262D43;
              box-shadow: 0 2px 6px 0 rgba(0,0,0,0.10);
              border-radius: 4px;
              padding: 8px 0;
              border: 1px solid #485565;
              width: auto;
              height: auto;
              margin-left: 10px;
              &-item {
                position: relative;
                &-name {
                  height: 32px;
                  color: #CACFD4;
                  white-space: nowrap;
                  padding: 0 12px;
                  line-height: 32px;
                  &[data-active="true"] {
                    background: #1F3759;
                    color: #2D8DE5;
                  }
                  &:hover {
                    background: #1F3759;
                    color: #fff;
                  }
                }
              }
            }
          }
        }
    }
}

.menu-history {
  height: 32px;
  background: #1B2130;
  border: 1px solid #31364A;
  border-left-width: 0;
  border-right-width: 0;
  position: relative;
  z-index: 1001;
  .h-tabs-bar {
    margin: 0;
    border: none;
  }
  .h-tabs.h-tabs-card-top>.h-tabs-bar-top .h-tabs-tab, .h-tabs.h-tabs-card>.h-tabs-bar .h-tabs-tab {
    background: #202637;;
    color: #CACFD4;
    font-size: 12px;
    border-radius: 0;
    padding: 0 8px;
    height: 30px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    margin: 0;
    border: none;
    text-align: center;
    box-shadow: inset -1px 0 0 0 #485565;
    line-height: 32px;
    position: relative;
    transition: none;

    div {
      padding: 0 8px;
      height: 30px;
    }
    i {
      opacity: 0;
      width: 12px;
      right: 6px;
      top: 0;
      font-size: 12px;
      position: absolute;
      transition: none;

    }
    &:hover {
      padding-right: 18px;
      i {
        opacity: 1;
      }
    }
  }
  .h-tabs-return, .h-tabs-enter {
    width: 30px;
    padding: 0;
    display: flex;
    box-shadow: inset -1px 0 0 0 #485565;
    align-items: center;
    background: #202637;;
    justify-content: center;
    height: 30px;
    i {
      color: #bbb;
      font-size: 12px;
    }
  }
  .h-tabs-enter {
    box-shadow: inset 1px 0 0 0 #485565;
  }
  .h-tabs-nav-wrap-width {
    width: calc(100% - 60px);
  }
  .h-tabs-tab-active {
    color:  #2D8DE5 !important;
  }
  &[data-matchHistory="true"] {
    .h-tabs-tab-active {
      color:  #CACFD4 !important;
    }
  }
}
.historyMenuText {
  padding: 0 8px;
  font-size: 12px;
  position: absolute;
  z-index: -1;
  background: #202637;
  color: #CACFD4;
  font-size: 12px;
  border-radius: 0;
  padding: 0 8px;
  height: 30px;
  margin: 0;
  border: none;
  -webkit-box-shadow: inset -1px 0 0 0 #485565;
  box-shadow: inset -1px 0 0 0 #485565;
  line-height: 32px;
  letter-spacing: 0;
  border-image-width: 1;
  opacity: 0;
  box-sizing: border-box;
  display: inline-block;
}

// 语言切换器样式
.language-switcher {
    margin-left: 0;
    display: flex;
    align-items: center;
    width: 65px;

    .language-btn {
        display: flex;
        align-items: center;
        padding: 8px 12px;
        cursor: pointer;
        border-radius: 4px;
        transition: background-color 0.3s;

        &:hover {
            background-color: rgba(255, 255, 255, 0.1);
        }

        .current-lang {
            margin: 0 6px;
            color: #fff;
            font-size: 12px;
        }
    }
}

// 下拉菜单样式覆盖
.h-dropdown-menu {
    background: #262D43 !important;
    border: 1px solid #485565 !important;
    border-radius: 4px !important;
    box-shadow: 0 2px 6px 0 rgba(0,0,0,0.10) !important;

    .h-dropdown-item {
        color: #fff !important;
        padding: 8px 16px !important;
        font-size: 12px !important;

        &:hover {
            background-color: rgba(255, 255, 255, 0.1) !important;
        }

        &.active {
            background-color: #298dff !important;
            color: #fff !important;
        }
    }
}