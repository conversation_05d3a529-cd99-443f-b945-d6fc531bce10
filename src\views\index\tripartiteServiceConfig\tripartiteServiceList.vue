<template>
    <div class="main">
        <div class="title">
            <a-title title="三方服务集成管理"></a-title>
        </div>
        <a-loading v-if="loading"></a-loading>
        <menu-layout ref="menu" customMenu @menu-fold="menuFold">
            <template v-slot:menu>
                <div class="menu">
                    <div class="header-menu" :style="{ padding: menuFoldStatus ? '0' : '0px 10px' }">三方服务列表</div>
                    <h-menu v-if="menuList.length" theme="dark" :active-name="activeMenu" @on-select="selectMenuChange">
                        <h-menu-item v-for="item in menuList" :key="item.id" :name="item.id">
                            <span>{{ item.name }}</span>
                        </h-menu-item>
                    </h-menu>
                </div>
            </template>
            <template v-slot:right>
                <h-tabs v-if="activeMenu && tabs.length" v-model="tabName" @on-click="tabClick(tabName)">
                    <h-tab-pane v-for="item in tabs" :key="item.id" :label="item.name" :name="item.id">
                        <component :is="item.component" :ref="item.id"/>
                    </h-tab-pane>
                </h-tabs>
                <no-data v-else></no-data>
            </template>
        </menu-layout>
    </div>
</template>
<script>
import noData from '@/components/common/noData/noData';
import aTitle from '@/components/common/title/aTitle';
import aLoading from '@/components/common/loading/aLoading';
import menuLayout from '@/components/common/menuLayout/menuLayout';
import eccomServiceConfig from '@/components/tripartiteServiceConfig/eccomServiceConfig.vue';
import { MENUS } from './constant';

export default {
    data() {
        return {
            loading: false,
            tabName: '',
            menuList: MENUS,
            menuFoldStatus: false,
            activeMenu: ''
        };
    },
    created() {
    },
    destroyed(){
    },
    mounted() {
        this.initActiveComponent();
        if (this.menuList.length <= 1) {
            this.$refs['menu'].setMenuStatus(true);
        } else {
            this.$refs['menu'].setMenuStatus(false);
        }
    },
    computed: {
        tabs() {
            if (!this.activeMenu) return [];
            for (const menu of this.menuList) {
                if (menu.id === this.activeMenu) {
                    return menu.tabs;
                }
            }
            return [];
        }
    },
    watch: {
        '$route'(to, from) {
            const { menuId: toMenudId, ...param } = to.query;
            const { menuId: fromMenudId } = from.query;
            if (!!toMenudId && toMenudId !== this.activeMenu && toMenudId !== fromMenudId) {
                this.initActiveComponent(toMenudId, true, param);
            }
        }
    },
    methods: {
        menuFold(status) {
            this.menuFoldStatus = status;
        },
        /**
         * 初始化激活当前菜单组件
         * @param {Boolean} ignoreNavigate 是否忽视路由跳转
         * @param {Object} param 路由跳转时，组件传递参数
         */
        initActiveComponent(childId = null, ignoreNavigate = false, param = {}) {
            const { menuId } = this.$route.query || {};
            const targerMenuId = childId || menuId;
            // 支持指定默认展开的菜单，根据menuId展开菜单
            if (targerMenuId) {
                for (const menu of this.menuList) {
                    if (menu.id === targerMenuId) {
                        this.activeMenu = menu.id;
                        this.tabName = menu.tabs[0].id;
                        this.tabClick(menu.tabs[0].id, ignoreNavigate, param);
                        break;
                    }
                }
            } else {
                const activeTab = this.menuList[0].tabs[0].id;
                this.tabName = activeTab;
                this.activeMenu = this.menuList[0].id;
                this.tabClick(activeTab);
            }
        },
        selectMenuChange(menuId) {
            this.initActiveComponent(menuId);
        },
        /**
         * @param {Boolean} ignoreNavigate 是否忽视路由跳转
         * @param {Object} param 路由跳转时，组件传递参数
         */
        tabClick(name, ignoreNavigate = false, param = {}) {
            this.tabName = name;
            if (!ignoreNavigate) {
                this.$hCore.navigate(`/tripartiteServiceList`, { history: false }, {
                    menuId: this.activeMenu
                });
            }
            this.$nextTick(() => {
                this.$refs?.[name]?.[0] && this.$refs[name][0].initData(param);
            });
        }
    },
    components: { menuLayout, aTitle, aLoading,  noData, eccomServiceConfig }
};
</script>
<style lang="less" scoped>
@import url("@/assets/css/tab.less");
@import url("@/assets/css/input.less");
@import url("@/assets/css/menu.less");

.main {
    & > .apm-box {
        height: calc(100% - 58px);
        background: none;
        min-width: 1000px;

        /deep/ .menu {
            border: 0;
        }

        /deep/ .left-box {
            border-radius: 4px;
        }

        /deep/ .right-box {
            background: none;
            padding: 0 10px;
        }

        /deep/ .h-tabs-bar {
            margin-bottom: 5px;
        }

        /deep/ .h-tabs-content-wrap {
            height: calc(100% - 50px);
        }
    }
}
</style>
