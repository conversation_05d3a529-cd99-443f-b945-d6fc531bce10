<!--
 * <AUTHOR>
 * @email <EMAIL>
 * @create date 2024-08-09 09:31:26
 * @modify date 2024-08-09 16:33:03
 * @desc [产品服务列表页面组件]
-->
<template>
    <div class="main">
        <div class="title">
            <a-title title="产品服务配置">
                <slot>
                    <h-select v-show="productList.length > 1" v-model="productInstNo" placeholder="请选择"
                        class="securities" placement="bottom"
                        :positionFixed="true" :clearable="false" @on-change="checkProduct">
                        <h-option v-for="item in productList" :key="item.id" :value="item.productInstNo || ''">{{
                        item.productName || '' }}</h-option>
                    </h-select>
                </slot>
            </a-title>
        </div>
        <a-loading v-if="loading"></a-loading>
        <menu-layout customMenu @menu-fold="menuFold">
            <template v-slot:menu>
                <div class="menu">
                    <div class="header-menu" :style="{ padding: menuFoldStatus ? '0' : '0px 10px' }">产品服务列表</div>
                    <h-menu v-if="menuList.length" theme="dark" :active-name="activeMenu" :open-names="openMenus" @on-select="selectMenuChange">
                        <submenu v-for="item in menuList" :key="item.id" :name="item.id">
                            <template v-slot:title>{{ item.name }}
                            </template>
                            <h-menu-item v-for="child in item.children" :key="child.id" :name="child.id">
                                <span>{{ child.name }}</span>
                            </h-menu-item>
                        </submenu>
                    </h-menu>
                </div>
            </template>
            <template v-slot:right>
                <h-tabs v-if="activeMenu && tabs.length" v-model="tabName" @on-click="tabClick(tabName)">
                    <h-tab-pane v-for="item in tabs" :key="item.id" :label="item.name" :name="item.id">
                        <component :is="item.component" :ref="item.id" :productInfo="productInfo"
                            :endpointType="item.endpointType" @tab-jump="handleTabJump"/>
                    </h-tab-pane>
                </h-tabs>
                <component :is="activeMenu" v-else-if="activeMenu" :ref="activeMenu" :productInfo="productInfo"/>
                <no-data v-else></no-data>
            </template>
        </menu-layout>
    </div>
</template>
<script>
import _ from 'lodash';
import { mapState, mapActions } from 'vuex';
import noData from '@/components/common/noData/noData';
import aTitle from '@/components/common/title/aTitle';
import aLoading from '@/components/common/loading/aLoading';
import menuLayout from '@/components/common/menuLayout/menuLayout';
import linkTopoConfig from '@/components/productServiceConfig/linkTopoConfig.vue';
import linkTopoReview from '@/components/productServiceConfig/linkTopoReview.vue';
import logSourceConfig from '@/components/productServiceConfig/logSourceConfig.vue';
import manageFunctionMeta from '@/components/productServiceConfig/manageFunctionMeta.vue';
import endpointConfig from '@/components/endpointConfig/endpointConfig.vue';
import appMonitorRule from '@/components/productServiceConfig/monitor/appMonitorRule.vue';
import { MENUS } from './constant';

export default {
    components: { menuLayout, aTitle, aLoading,  noData,
        endpointConfig, linkTopoConfig, linkTopoReview, appMonitorRule, logSourceConfig, manageFunctionMeta
    },
    data() {
        return {
            productInstNo: '', // 选中的产品
            loading: false,
            productInfo: {},
            tabName: '',
            menuList: MENUS,
            menuFoldStatus: false,
            openMenus: [],
            activeMenu: ''
        };
    },
    mounted() {
        this.init();
    },
    computed: {
        ...mapState({
            productList: state => {
                return state.product.productListLight || [];
            }
        }),
        tabs() {
            if (!this.activeMenu) return [];
            for (const menu of this.menuList) {
                for (const subMenu of menu.children) {
                    if (subMenu.id === this.activeMenu) {
                        return subMenu.tabs;
                    }
                }
            }
            return [];
        }
    },
    watch: {
        '$route'(to, from) {
            const { menuId: toMenudId, ...param } = to.query;
            const { menuId: fromMenudId } = from.query;
            if (!!toMenudId && toMenudId !== this.activeMenu && toMenudId !== fromMenudId) {
                this.initActiveComponent(toMenudId, true, param);
            }
        }
    },
    methods: {
        ...mapActions({ getProductList: 'product/getProductListLight' }),
        menuFold(status) {
            this.menuFoldStatus = status;
        },
        async init() {
            try {
                this.loading = true;
                await this.getProductList({ filter: 'excludeLdpApm' });
                const productInstNo = localStorage.getItem('productInstNo');
                this.productInstNo = _.find(this.productList, ['productInstNo', productInstNo])?.productInstNo || this.productList?.[0]?.productInstNo || '';
                const productInfo = _.find(this.productList, ['productInstNo', this.productInstNo]);
                this.productInfo = productInfo || {};
            } catch (e) {
                console.error(e);
                this.productInstNo = '';
                this.productInfo = {};
            } finally {
                this.loading = false;
            }
        },
        /**
         * 初始化激活当前菜单组件
         * @param {Boolean} ignoreNavigate 是否忽视路由跳转
         * @param {Object} param 路由跳转时，组件传递参数
         */
        initActiveComponent(childId = null, ignoreNavigate = false, param = {}) {
            const { menuId } = this.$route.query || {};
            const targerMenuId = childId || menuId;
            // 支持指定默认展开的菜单，根据menuId展开菜单
            if (targerMenuId) {
                for (const menu of this.menuList) {
                    const matchSubMenu = menu.children.find(subMenu => subMenu.id === targerMenuId);

                    if (matchSubMenu && matchSubMenu?.tabs?.length) {
                        this.activeMenu = matchSubMenu.id;
                        this.openMenus = [...new Set([...(this.openMenus || []), menu.id])];
                        this.tabName = matchSubMenu.tabs[0].id;
                        this.tabClick(matchSubMenu.tabs[0].id, ignoreNavigate, param);
                        break;
                    } else if (matchSubMenu) {
                        this.activeMenu = matchSubMenu.id;
                        this.openMenus = [...new Set([...(this.openMenus || []), menu.id])];
                        this.$hCore.navigate(`/productServiceList`, { history: false }, {
                            menuId: this.activeMenu
                        });
                        this.$nextTick(() => {
                            this.$refs?.[this.activeMenu] && this.$refs[this.activeMenu].initData(param);
                        });
                        break;
                    }
                }
            } else {
                this.openMenus = [this.menuList[0].id];
                const activeTab = this.menuList[0].children[0].tabs[0].id;
                this.tabName = activeTab;
                this.activeMenu = this.menuList[0].children[0].id;
                this.tabClick(activeTab);
            }
        },
        selectMenuChange(menuId) {
            this.initActiveComponent(menuId);
        },
        /**
         * @param {Boolean} ignoreNavigate 是否忽视路由跳转
         * @param {Object} param 路由跳转时，组件传递参数
         */
        tabClick(name, ignoreNavigate = false, param = {}) {
            this.tabName = name;
            if (!ignoreNavigate) {
                this.$hCore.navigate(`/productServiceList`, { history: false }, {
                    menuId: this.activeMenu
                });
            }
            this.$nextTick(() => {
                this.$refs?.[name]?.[0] && this.$refs[name][0].initData(param);
            });
        },
        /**
         * 切换导航产品
         * @param {String} productInstNo
         */
        checkProduct(productInstNo) {
            if (!productInstNo) return;
            localStorage.setItem('productInstNo', productInstNo);
            this.productInstNo = productInstNo;
            const productInfo = _.find(this.productList, ['productInstNo', this.productInstNo]);
            this.productInfo = productInfo || {};
            this.initActiveComponent(this.activeMenu);
        },
        // 跳转查看链路模型
        handleTabJump(modelId) {
            this.tabClick('linkTopoReview', false, modelId);
            setTimeout(() => {
                this.$nextTick(() => {
                    document.querySelector('#product-service-placeholder').focus();
                });
            }, 1000);
        }
    }
};
</script>
<style lang="less" scoped>
@import url("@/assets/css/tab.less");
@import url("@/assets/css/input.less");
@import url("@/assets/css/menu.less");

.main {
    .title {
        min-width: 1000px;
    }

    & > .apm-box {
        height: calc(100% - 58px);
        background: none;
        min-width: 1000px;

        /deep/ .menu {
            border: 0;
        }

        /deep/ .left-box {
            border-radius: 4px;

            .h-menu-item {
                padding-left: 40px;
            }
        }

        /deep/ .right-box {
            background: none;
            padding: 0 10px;
        }

        /deep/ .h-tabs-bar {
            margin-bottom: 5px;
        }

        /deep/ .h-tabs-content-wrap {
            height: calc(100% - 50px);
        }
    }
}
</style>
