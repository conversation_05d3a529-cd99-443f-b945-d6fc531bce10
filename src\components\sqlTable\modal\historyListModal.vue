<template>
    <div>
        <h-msg-box-safe v-model="modalData.status" :escClose="true" :mask-closable="false" title="历史执行SQL" width="60"
            height="70">
            <h-input v-if="historyList.length" v-model="searchValue" icon="search" placeholder="搜索历史SQL"
                style="width: 100%;" @on-change="handleSearch" @on-click="handleSearch"></h-input>
            <h-timeline v-if="newHistoryList.length" class="box-records">
                <h-timeline-item v-for="(item, index) in newHistoryList" :key="index" color="blue"
                    style="position: relative;">
                    <p style="font-size: 14px; color: #777; width: 480px;">
                        {{  getLabel(item.databaseName )}} : {{ formatDates(new Date(item.time)) }}
                    </p>
                    <p style="margin-top: 6px; color: #555; font-weight: 500; user-select: text;">{{ item.sql }}</p>
                    <h-button type="primary" size="small" style="position: absolute; right: 10px; top: 4px;"
                        @click="() => addSqlToEdit(item.sql, item.databaseName)">导入</h-button>
                </h-timeline-item>
            </h-timeline>
            <no-data v-else isWhite />
            <template v-slot:footer>
                <h-button @click="modalData.status = false">关闭</h-button>
            </template>
        </h-msg-box-safe>
    </div>
</template>

<script>
import _ from 'lodash';
import { formatDates } from '@/utils/utils';
import noData from '@/components/common/noData/noData';
export default ({
    props: {
        modalInfo: {
            type: Object,
            default: null
        },
        historyList: {
            type: Array,
            default: () => []
        }
    },
    data() {
        return {
            modalData: this.modalInfo,
            searchValue: '',
            newHistoryList: []
        };
    },
    mounted() {
        this.newHistoryList = _.cloneDeep(this.historyList);
    },
    methods: {
        getLabel(databaseName) {
            if (Array.isArray(databaseName)) {
                return databaseName.map(o => o?.label).join(',');
            }
            return '';
        },
        handleEventData(idx) {
            this.$emit('restContrastData', idx);
            this.modalData.status = false;
        },
        formatDates(time) {
            return formatDates(time);
        },
        addSqlToEdit(sql, databaseName) {
            this.$emit('addSqlToEdit', sql, databaseName);
            this.$hMessage.success('导入成功，可继续导入');
        },
        handleSearch() {
            this.newHistoryList = [];
            const reg = this.searchValue ? new RegExp(`${this.searchValue.replace(/[-/\\^$*+?.()|[\]{}]/g, '\\$&')}`, 'i') : '';
            this.historyList.forEach(element => {
                if (element.sql.search(reg) > -1) {
                    this.newHistoryList.push(element);
                }
            });
        }
    },
    components: { noData }
});
</script>

<style lang="less" scoped>
/deep/ .h-modal-body {
    padding: 15px;

    & > .box-records {
        margin-top: 10px;
        height: calc(100% - 42px);
        overflow-y: auto;
    }
}
</style>
