<template>
    <div>
        <h-msg-box-safe v-model="modalData.status" title="创建数据重试任务" :closable="true" :mask-closable="false" width="550" maxHeight="350" allowCopy>
            <div v-show="step === '1'">
                <div>
                重试模式：<h-radio-group v-model="mode" @on-change="changeMode">
                        <h-radio label="all">
                            <span>全部</span>
                        </h-radio>
                        <h-radio label="cluster">
                            <span>指定集群</span>
                        </h-radio>
                        <h-radio label="fund_account" >
                            <span>指定账户</span>
                        </h-radio>
                    </h-radio-group>
                </div>
                <br/>
                <h-transfer
                    v-show= "mode !== 'all'"
                    :data="data"
                    :target-keys="targetKeys"
                    :render-format="render"
                    @on-change="handleChange"
                ></h-transfer>
            </div>

            <div v-show="step === '2'" class="content-body">
                <div class="header-info">
                    <h-icon :name="blukConfigInfo.headerIcon" color="var(--warning-color)" size=24></h-icon>
                    <span class="title-info">{{blukConfigInfo.headerTitle}}</span>
                </div>
                <div class="obj-body">
                    <p v-for="info in blukConfigInfo.contentDic" :key="info.key">
                        <span :title="info.title">{{info.title}}</span>
                        <span :title="blukConfigInfo.contentObj[info.key]">
                            {{blukConfigInfo.contentObj[info.key]}}
                        </span>
                    </p>
                </div>
            </div>

            <template v-slot:footer>
                <div>
                    <a-button @click="modalData.status = false">取消</a-button>
                    <a-button v-show="step === '2'" @click="step = '1'">上一步</a-button>
                    <a-button v-show="step === '1'" type="primary" @click="step = '2'">下一步</a-button>
                    <a-button v-show="step === '2'" type="primary" @click="submitConfig">创建</a-button>
                </div>
            </template>
        </h-msg-box-safe>
        <revision-config-modal v-if="blukConfigInfo.status" :key="blukConfigInfo.key" :modalInfo="blukConfigInfo"/>
    </div>
</template>

<script>
import aButton from '@/components/common/button/aButton';
import revisionConfigModal  from '@/components/ldpLogCenter/modal/revisionConfigModal';

export default {
    name: 'SuspensionTaskModal',
    components: { aButton, revisionConfigModal },
    props: {
        modalInfo: {
            type: Object,
            default: null
        }
    },
    data() {
        return {
            step: '1',
            loading: false,
            modalData: this.modalInfo,
            mode: 'all',
            data: this.getMockData(),
            targetKeys: [],
            blukConfigInfo: {
                status: false,
                title: '创建数据重试任务',
                headerIcon: 'android-alert',
                headerTitle: '您确认要对以下错误记录进行重试处理吗？',
                key: 'bluk-config',
                contentDic: [
                    {
                        key: 'ClusterTotal',
                        title: '集群总数：'
                    },
                    {
                        key: 'fundAccountTotal',
                        title: '资金账户数：'
                    },
                    {
                        key: 'errorRecordsTotal',
                        title: '错误记录总数：'
                    }
                ],
                contentObj: {
                    ClusterTotal: '',
                    fundAccountTotal: '',
                    errorRecordsTotal: ''
                }
            }
        };
    },
    mounted(){
        this.loading = true;
        setTimeout(() => {
            this.loading = false;
        }, 300);
    },
    methods: {
        // 修正接口请求
        submitConfig() {
            this.$emit('bulk-revision');
            this.modalData.status = false;
        },
        // 切换模式
        changeMode(key){

        },
        getMockData() {
            const mockData = [];
            for (let i = 1; i <= 20; i++) {
                mockData.push({
                    key: i.toString(),
                    label: '内容' + i,
                    description: '内容' + i + '的描述信息',
                    disabled: Math.random() * 3 < 1
                });
            }
            return mockData;
        },
        getTargetKeys() {
            return this.getMockData()
                .filter(() => Math.random() * 2 > 1)
                .map((item) => item.key);
        },
        render(item) {
            return item.label;
        },
        handleChange(newTargetKeys, direction, moveKeys) {
            this.targetKeys = newTargetKeys;
        }
    }
};
</script>

<style lang="less" scoped >
/deep/ .h-transfer-list {
    width: 210px;
}

/deep/ .h-modal-body {
    padding: 5px 32px;
}

.header-info {
    font-weight: 500;
    display: flex;

    & > .h-icon {
        position: relative;
        top: 2px;
    }

    .title-info {
        line-height: 36px;
        font-size: 14px;
        font-weight: 600;
        vertical-align: top;
        padding: 0 5px;
    }
}

.content-body {
    .text-body {
        font-size: 12px;
        color: #24262b;
        text-align: left;
        line-height: 20px;
        padding-left: 18px;
        position: relative;
    }

    .obj-body {
        color: var(--font-color);
        line-height: 30px;
        width: 100%;

        p {
            display: flex;

            span {
                text-align: left;
                overflow: hidden;
                white-space: nowrap;
                text-overflow: ellipsis;
            }
        }

        .obj-body-text {
            width: 100px;
            text-align: left;
        }
    }
}

</style>
