/**
 * <AUTHOR>
 * @email <EMAIL>
 * @create date 2024-12-10 13:58:23
 * @modify date 2024-12-10 13:58:23
 * @desc 单行卡片，需求源于核心功能号处理
 */

import { defineComponent } from 'vue';
import './apmSingleRowCard.less';
export default defineComponent({
    name: 'apmSingleRowCard',
    props: {

        /**
         * 标题
         */
        title: {
            type: String
        },
        /**
         * 副标题
         */
        subTitle: {
            type: String
        },
        /**
         * 副标题2，一般用于两个副标题直接有一个竖杠
         */
        subTitle2: {
            type: String
        },
        /**
         * 卡片显示数据集合
         * @param {IApmSingleRowCardData}
         */
        list: {
            type: Array,
            default: () => []
        }
    },
    render() {
        return (
            <div class="apm-single-row-card" style={{ minWidth: '0' }}>
                <div class="apm-single-row-card-title">
                    <div class="apm-single-row-card-title-main">{this.title}</div>
                    <div class="apm-single-row-card-title-subtile">
                        {this.subTitle}
                        {!!this.subTitle2 && <div class="apm-single-row-card-title-subtile-line" />}
                        {!!this.subTitle2 && <div class="apm-single-row-card-title-subtile-subtitle2">{this.subTitle2}</div>}
                    </div>
                </div>
                <div class="apm-single-row-card-content">
                    {
                        this.list.map(item => (
                            <div class="apm-single-row-card-content-item" key={item.title}>
                                <div class="apm-single-row-card-content-item-title">
                                    {item.title}
                                </div>
                                <div class="apm-single-row-card-content-item-value">
                                    {item.value}
                                    {
                                        item.valueSurfix && <span class="apm-single-row-card-content-item-value-surfix">{item.valueSurfix}</span>
                                    }
                                </div>
                            </div>
                        ))
                    }
                </div>
            </div>
        );
    }
});
