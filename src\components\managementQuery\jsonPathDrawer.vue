<template>
      <div>
  <h-drawer
    v-model="modalData.status"
    :title="`配置（`+ modalData.title + ')'"
    class="drawer"
    width="50"
    :mask-closable="false"
    @on-close="handleDrawerClose"
  >
    <a-tips
      tipText="通过配置JsonPath，将管理功能表格展示的一个结果集拆分为多个表格呈现（一个JsonPath表示拆分一个表格）。"  theme="dark"
      style="margin: 0 0 10px;"
      ></a-tips>
      <a-table
         ref="jsonPathRef"
        :columns="columns"
        :tableData="configList"
        :hasPage="false"
        :border="false"
        :hasDarkClass="true"
      />

    <a-button
      class="drawer-push"
      type="dark"
      icon="plus-round"
      @click="handleRulePush"
    >
      添加</a-button
    >
    <template v-slot:footer>
      <a-button type="primary" @click="saveRules"
        >保存配置</a-button
      >
      <a-button type="dark" @click="handleDrawerClose">关闭</a-button>
    </template>
  </h-drawer>
</div>
</template>

<script>
import aInput from '@/components/common/input/aInput';
import aTips from '@/components/common/apmTips/aTips';
import aButton from '@/components/common/button/aButton';
import aTable from '@/components/common/table/aTable';
import { getDBInstance } from '@/utils/indexedDBInstance';
export default {
    name: 'JsonPathDrawer',
    props: {
        modalInfo: {
            type: Object,
            default: () => {}
        }
    },
    components: { aButton, aTips, aTable },
    data() {
        return {
            modalData: this.modalInfo,
            configList: [],
            columns: [
                {
                    title: 'JsonPath',
                    key: 'jsonPath',
                    renderHeader(h) {
                        return h('span', [
                            'JsonPath',
                            h('h-poptip', {
                                props: {
                                    autoPlacement: true,
                                    trigger: 'hover',
                                    transfer: true,
                                    customTransferClassName: 'apm-poptip monitor-poptip'
                                }
                            }, [
                                h('h-icon', {
                                    props: {
                                        name: 'prompt',
                                        size: 16,
                                        color: '#999'
                                    },
                                    style: {
                                        cursor: 'pointer',
                                        position: 'relative',
                                        top: '1px'
                                    }
                                }),
                                h('div', {
                                    slot: 'content',
                                    style: { whiteSpace: 'pre-line' }
                                }, `表格结果路径。核心规则：
1. 根节点：所有JSONPath都以 $ 开头，表示JSON根对象。
2. 对象子节点：用 . 连接，如 $.a 表示根下的 a 属性。
3. 数组元素：用 [] 指定索引（如 $.list[0] ）或通配符 *（如 $.list[*] 表示所有元素）。
4. 通配符： * 匹配所有同级节点（如 $.a.* 匹配 a 的所有子节点）。
5. 递归下降： .. 用于匹配任意层级的节点（如 $..name 匹配所有层级的 name 属性）。
`)
                            ])
                        ]);
                    },
                    render: (h, { row, index }) => {
                        return h(aInput, {
                            props: {
                                key: `row_${index}_jsonPath`,
                                prop: 'input',
                                noTrim: false,
                                value: row?.jsonPath,
                                placeholder: '请输入',
                                positionFixed: true,
                                placement: 'top',
                                validRules: [
                                    {
                                        validator: (rule, value, callback) => {
                                            if (!value) {
                                                callback(new Error('请输入jsonPath'));
                                                return;
                                            }
                                            callback();
                                        },
                                        trigger: 'change'
                                    }
                                ]
                            },
                            on: {
                                'on-change': val => {
                                    this.handleChange(val, 'jsonPath', index);
                                    this.$nextTick(() => {
                                        this.validateAllRows();
                                    });
                                }
                            }
                        });
                    }
                },
                {
                    title: '别名',
                    key: 'aliasName',
                    renderHeader(h) {
                        return h('span', [
                            '别名',
                            h('h-poptip', {
                                props: {
                                    autoPlacement: true,
                                    trigger: 'hover',
                                    transfer: true,
                                    customTransferClassName: 'apm-poptip monitor-poptip'
                                }
                            }, [
                                h('h-icon', {
                                    props: {
                                        name: 'prompt',
                                        size: 16,
                                        color: '#999'
                                    },
                                    style: {
                                        cursor: 'pointer',
                                        position: 'relative',
                                        top: '1px'
                                    }
                                }),
                                h('div', {
                                    slot: 'content',
                                    style: { whiteSpace: 'normal' }
                                }, '表格展示名称')
                            ])
                        ]);
                    },
                    render: (h, { row, index }) => {
                        return h('h-input', {
                            props: { value: row.aliasName, clearable: false, placeholder: '请输入' },
                            on: {
                                'on-change': event => { this.handleChange(event.target.value, 'aliasName', index); }
                            }
                        });
                    }
                },
                {
                    title: '是否支持排序',
                    key: 'sortable',
                    align: 'center',
                    width: 120,
                    renderHeader(h) {
                        return h('span', [
                            '是否支持排序',
                            h('h-poptip', {
                                props: {
                                    autoPlacement: true,
                                    trigger: 'hover',
                                    transfer: true,
                                    customTransferClassName: 'apm-poptip monitor-poptip'
                                }
                            }, [
                                h('h-icon', {
                                    props: {
                                        name: 'prompt',
                                        size: 16,
                                        color: '#999'
                                    },
                                    style: {
                                        cursor: 'pointer',
                                        position: 'relative',
                                        top: '1px'
                                    }
                                }),
                                h('div', {
                                    slot: 'content',
                                    style: { whiteSpace: 'normal' }
                                }, '表格字段支持行排序(仅当表格为一维数组时生效)')
                            ])
                        ]);
                    },
                    render: (h, { row, index }) => {
                        return h('h-switch', {
                            props: { value: row.sortable },
                            style: { cursor: 'pointer' },
                            on: {
                                'on-change': val => { this.handleChange(val, 'sortable', index); }
                            },
                            scopedSlots: {
                                open: () => <h-icon name="right"></h-icon>,
                                close: () => <h-icon name="close"></h-icon>
                            }
                        });
                    }
                },
                {
                    title: ' ',
                    key: 'action',
                    align: 'center',
                    width: 80,
                    render: (h, { index }) => {
                        return h('h-icon', {
                            class: 'delete-icon',
                            props: { name: 'trash', size: 20, color: '#9296A1' },
                            on: {
                                'on-click': () => this.handleRuleDel(index)
                            }
                        });
                    }
                }
            ],
            indexDB: null
        };
    },
    async mounted(){
        const db = await getDBInstance();
        const key = [this.modalData?.plugin, this.modalData?.title].join('/');
        const data = await db.get(key, 'management-jsonpath');
        if (Array.isArray(data)) {
            this.configList = data;
        } else {
            this.configList = [];
        }
    },
    methods: {
        handleDrawerClose() {
            this.modalData.status = false;
        },
        // 修改
        handleChange(val, key, index) {
            this.configList[index][key] = val;
        },
        // 添加
        handleRulePush() {
            this.configList.push({
                jsonPath: '$.',
                aliasName: '',
                sortable: false
            });
        },
        // 删除
        handleRuleDel(index) {
            this.configList.splice(index, 1);
        },
        // 保存
        async saveRules() {
            if (!this.validateAllRows()) return;
            const db = await getDBInstance();
            const key = [this.modalData?.plugin, this.modalData?.title].join('/');
            await db.set(this.configList, key, 'management-jsonpath');
            this.$emit('update');
            this.$nextTick(() => {
                this.handleDrawerClose();
            });
        },
        // 校验
        validateAllRows() {
            // 强制刷新，确保最新数据
            this.$forceUpdate();

            // 等待DOM更新后，手动触发所有a-input的校验
            this.$nextTick(() => {
                // 通过 ref 获取表格组件，然后查找其中的 a-input 组件
                const tableRef = this.$refs.jsonPathRef;
                if (tableRef) {
                    // 查找表格中的所有 a-input 组件
                    const aSelectComponents = tableRef.$el.querySelectorAll('.a-input');
                    aSelectComponents.forEach(element => {
                        // 获取对应的 Vue 组件实例
                        const component = element.__vue__;
                        if (component && component.validate) {
                            component.validate('change', () => {});
                        }
                    });
                }
            });

            let valid = true;
            for (const row of this.configList) {
                if (!row?.jsonPath) {
                    valid = false;
                    break;
                }
            }
            return valid;
        }
    }
};
</script>

<style lang="less" scoped>
@import url("@/assets/css/input.less");

/deep/ .h-drawer-footer {
    padding: 5px 5px 5px 15px;
    border-top: var(--border);
}

/deep/ .a-input {
    width: 100%;

    .h-tooltip {
        width: 100%;

        .h-tooltip-rel {
            width: 100%;
        }
    }
}

/deep/ .h-icon.delete-icon {
    cursor: pointer;
}
</style>
