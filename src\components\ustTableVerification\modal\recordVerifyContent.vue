<!-- 新增校验任务 -校验内容 -->
<template>
    <div>
        <!-- 表记录总数校验 -->
        <div>
            <!-- <h-radio-group v-model="recordChoosemode">
                <h-radio label="selectTable">
                    <span>直接选择表</span>
                </h-radio>
                <h-radio label="sqlTemplate">
                    <span>使用预定义SQL模板</span>
                </h-radio>
            </h-radio-group> -->
            <!-- 直接选择表 -->
            <div v-if="recordChoosemode === 'selectTable'" class="table-select">
                <div class="table-select-title">
                    <h-checkbox
                        :indeterminate="indeterminate"
                        :value="checkAll"
                        @click.prevent.native="handleCheckAll"
                        >表名
                    </h-checkbox>
                    <h-input
                        v-model=searchInput
                        filterable
                        placeholder="输入表名查询"
                        :positionFixed="true"
                        icon="search"
                        class="search-input"
                        @on-change="handleTableNameSearch">
                    </h-input>
                </div>
                <div class="checkbox-container">
                    <h-checkbox-group
                        v-model="tables"
                        class="checkbox-group"
                        @on-change="checkGroupChange">
                        <h-checkbox v-for="item in tableList" :key="item"
                            :label="item" :title="item" class="checkbox-item">
                        </h-checkbox>
                    </h-checkbox-group>
                </div>
            </div>
            <!-- 使用预定义SQL模版 -->
            <!-- <div v-if="recordChoosemode === 'sqlTemplate'" class="sql-template">
                <div class="sql-template-title">
                    SQL模板01
                    <span>比对两个选定数据库，其中库里面的表结构一致，并且使用指定主键</span>
                </div>
                <div class="sql-template-content">
                    <p>
                        Compare &nbsp;
                        源数据库 &nbsp;
                        With &nbsp;
                        目标数据库 &nbsp;
                    </p>
                    <p>
                        Using $Table &nbsp;
                        <h-select v-model="sqlTableName" placeholder="选择表">
                            <h-option v-for="item in allTableList" :key="item" :value="item">
                                {{item}}
                            </h-option>
                        </h-select> &nbsp;
                        $FileterField &nbsp;
                        <h-select v-model="sqlField" placeholder="选择字段">
                            <h-option v-for="item in fieldList" :key="item" :value="item">
                                {{item}}
                            </h-option>
                        </h-select>
                    </p>
                    <p>
                        $Result=SQL(SELECT MAX({{sqlField || '-'}}) FROM ({{sqlTableName || '-'}}))
                    </p>
                    <p>
                        SQL(SELECT COUNT(1) FROM ({{sqlTableName || '-'}}) WHERE({{sqlField || '-'}})＜$Result)
                    </p>
                </div>
            </div> -->
        </div>
    </div>
</template>

<script>
export default {
    name: 'RecordVerifyContent',
    components: {},
    props: {
        allTableList: {
            type: Array,
            default: () => []
        },
        selectedTables: {
            type: Array,
            default: () => []
        }
    },
    data() {
        return {
            tableList: [],
            recordChoosemode: 'selectTable',
            tables: [],
            indeterminate: false,
            checkAll: false,
            searchInput: '',

            fieldList: [],
            sqlTableName: '',
            sqlField: ''
        };
    },
    mounted() {
        this.initData();
    },
    methods: {
        initData() {
            this.tableList = [...this.allTableList];
            this.tables = [...this.selectedTables];
            this.handleLinkage();
        },
        // 表名过滤
        handleTableNameSearch() {
            // Step 1: 将搜索输入转为小写
            const searchInputLower = this.searchInput.toLowerCase();

            // Step 2: 对列表进行过滤，忽略大小写进行模糊匹配
            this.tableList = this.allTableList.filter(item =>
                item.toLowerCase().includes(searchInputLower)
            );

            // 重新设置全选、半选状态
            this.checkGroupChange();
        },
        // 设置全选、全不选
        handleCheckAll() {
            if (this.indeterminate) {
                this.checkAll = false;
            } else {
                this.checkAll = !this.checkAll;
            }
            this.indeterminate = false;

            if (this.checkAll) {
                this.tables = [...new Set([...this.tables, ...this.tableList])];
            } else {
                this.tables = this.tables.filter(table => !this.tableList.includes(table));
            }

            this.$emit('choose-tables', this.tables);
        },
        // 切换checkbox-group内容
        checkGroupChange() {
            this.handleLinkage();

            this.$emit('choose-tables', this.tables);
        },
        // 根据checkbox数据联动展示全选、半选状态
        handleLinkage() {
            if (this.tableList.length && this.tableList.every(ele => this.tables.includes(ele))) {
                this.indeterminate = false;
                this.checkAll = true;
            } else if (this.tableList.length && this.tableList.some(ele => this.tables.includes(ele))) {
                this.indeterminate = true;
                this.checkAll = false;
            } else {
                this.indeterminate = false;
                this.checkAll = false;
            }
        }
    }
};
</script>
<style lang="less" scoped>
/deep/ .h-radio-inner {
    background-color: var(--wrapper-color);
}

.table-select {
    background: #262d43;
    border: 1px solid #485565;
    border-radius: 4px;

    .table-select-title {
        height: 40px;
        background: #2c334a;
        border-radius: 4px 4px 0 0;
        border-bottom: 1px solid #444a60;
        padding: 5px 10px;
    }

    .search-input {
        float: right;
        width: 200px;

        /deep/ .h-input {
            border: var(--border) !important;
        }

        /deep/ .h-input-icon {
            color: #9ea7b4;
        }
    }

    .checkbox-container {
        min-height: 150px;
        max-height: 300px; /* 设置最大高度为300px */
        overflow-y: auto; /* 启用垂直滚动 */
        padding: 5px;
    }

    .checkbox-group {
        display: flex; /* 启用 Flexbox 布局 */
        flex-wrap: wrap; /* 允许多行布局 */
        justify-content: flex-start; /* 确保子元素靠左对齐 */
        width: 100%; /* 设置宽度为100%，使其与父容器宽度一致 */
        height: 100%;
    }

    .checkbox-group .checkbox-item {
        flex: 0 0 200px; /* 每个复选框占据父容器的25%宽度，也就是一行4个 */
        box-sizing: border-box; /* 确保内边距和边框不会增加实际宽度 */
        padding: 5px; /* 添加一些内边距 */
        overflow: hidden; //超出的文本隐藏
        text-overflow: ellipsis; //溢出用省略号显示
        white-space: nowrap; //溢出不换行
    }
}

.sql-template {
    background: #2d334c;
    border-radius: 4px;
    padding: 10px 15px;

    .sql-template-title {
        color: var(--font-color);
        margin-bottom: 5px;

        span {
            padding-left: 10px;
            color: #9296a1;
        }
    }

    .sql-template-content {
        background: #2c334a;
        border: 1px solid #485565;
        border-radius: 4px;
        padding: 0 10px;

        .h-select {
            width: 150px;
        }
    }
}

</style>
