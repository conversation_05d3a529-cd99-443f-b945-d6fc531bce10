<template>
    <div ref="tab-box" class="tab-box">
        <a-loading v-if="loading"></a-loading>
        <div v-else class="tab-child-box">
            <obs-table ref="table" :maxHeight="tableHeight" :title="title" :tableData='tableData'
                :columns="columns" showTitle :hasPage="false" />
        </div>
    </div>
</template>

<script>
import aLoading from '@/components/common/loading/aLoading';
import obsTable from '@/components/common/obsTable/obsTable';
import { getManagerProxy } from '@/api/mcApi';
export default {
    props: {
        nodeData: {
            type: Object,
            default: () => { }
        },
        pollTime: {
            type: Number,
            default: 0
        }
    },
    components: { aLoading, obsTable },
    data() {
        return {
            loading: true,
            title: {
                label: '连接信息'
            },
            tableData: [],
            columns: [
                {
                    title: '网关地址',
                    key: 'IP',
                    ellipsis: true
                },
                {
                    title: '网关端口号',
                    key: 'Port',
                    ellipsis: true
                },
                {
                    title: '连接状态',
                    key: 'Status',
                    ellipsis: true
                },
                {
                    title: '发送次数',
                    key: 'SendCount',
                    ellipsis: true
                }
            ],
            tableHeight: 0
        };
    },
    mounted() {
        window.addEventListener('resize', this.fetTableHeight);
    },
    beforeDestroy() {
        window.removeEventListener('resize', this.fetTableHeight);
    },
    methods: {
        async initData() {
            this.loading = true;
            await this.getFileData();
            this.loading = false;
        },
        // 设置table高度
        fetTableHeight() {
            this.tableHeight = this.$refs['tab-box']?.offsetHeight - 80;
        },
        // 构建页面数据
        async getFileData() {
            const { getConnectionStatus } = await this.getAPi();
            this.tableData = [...getConnectionStatus];
            this.fetTableHeight();
        },
        // 接口请求
        async getAPi() {
            const data = {
                getConnectionStatus: []
            };
            const param = [
                {
                    manageProxyIp: this.nodeData.ip,
                    manageProxyPort: this.nodeData.manageProxyPort,
                    pluginName: 'ldp_todb',
                    funcName: 'GetConnectionStatus'
                }
            ];
            try {
                const res = await getManagerProxy(JSON.stringify(param));
                if (res.code === '200') {
                    res.data?.[0]?.GWConnections?.length && (data.getConnectionStatus = res.data[0].GWConnections);
                }
                return data;
            } catch (err) {
                this.$emit('clear');
            }
        }
    }
};
</script>
<style lang="less" scoped>
.tab-box {
    height: 100%;
    overflow: hidden;

    .tab-child-box {
        height: 100%;

        .obs-table {
            height: auto;
        }
    }
}
</style>
