## 拓扑图
### appTopo
#### Props
| **属性** | **说明** | **类型** | **默认值** |
| --- | --- | --- | --- |
| template | 模型数据 | Object | {} |
| monitor | 监控属性 | Boolean | false |
| analyse | 分析属性 | Boolean | false |
| clickable | 是否可点击 | Boolean | false |
| selectedGroup | 所选择的类别 | String | ‘’ |

#### Methods
| **方法名** | **说明** | **用法/参数** |
| --- | --- | --- |
| init | 初始化图数据 | this.$refs.xxx.init() |
| drawCanvas | 绘制 | / |
| generateEdges | 生成edges | / |
| isDrowGaphical | 画线框 | / |
| roundRect | canvas圆角 | / |
| roundRectInnerShadow | canvas圆角内部阴影 | / |
| handleUpdateEdgeLabel | 手动更新edge上的数据 | this.$refs.xxx.handleUpdateEdgeLabel(info) |
| selectEdge | 边选中事件监听 | / |
| destroy | 销毁 | / |

### allLinkTopo
#### Props
| **属性** | **说明** | **类型** | **默认值** |
| --- | --- | --- | --- |
| template | 模型数据 | Object | {} |
| monitor | 监控属性 | Boolean | false |
| clickable | 是否可点击 | Boolean | false |
| selectedGroup | 所选择的类别 | String | ‘’ |

#### Methods
| **方法名** | **说明** | **用法/参数** |
| --- | --- | --- |
| init | 初始化图数据 | this.$refs.xxx.init() |
| getIdByName | 根据节点名获取节点Id | / |
| getEdgeListByEdgeId | 找出edge相关的边id | / |
| getNameById | 根据ID获取节点名 | / |
| generateNodeAndEdge | 生成节点或者边 | / |
| getEdges | edges | / |
| handelBlurNetwork | topo 鼠标移出 | / |
| getNodes | 需要应用展示配置信息交互 | / |
| selectedNodeById | 根据id选择节点状态 | / |
| clearNodeById | 根据id清理节点状态 | / |
| handleEdgeById | 根据id改变边状态 | / |
| handleSelect | 根据id选择选中状态 | / |

