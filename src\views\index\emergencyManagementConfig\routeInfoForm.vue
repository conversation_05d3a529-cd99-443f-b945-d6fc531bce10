<template>
    <no-data v-if="!formItems.visible && type !== 'add'" class="no-data" text="请选择要修改的路由" />
    <h-form v-else ref="formValidate" :model="formItems" :label-width="90" :rules="rules">
        <h-form-item label="功能号" prop="Functions">
            <h-input v-model="formItems.Functions" :disabled="disabled" type="textarea" :canResize="false" placeholder="请输入"></h-input>
            <div class="desc">
                支持*、?、数字(不为负值)。多个功能号使用英文分号分隔
            </div>
        </h-form-item>
        <h-form-item label="目标系统号" prop="SystemNo">
            <h-input v-model="formItems.SystemNo" :disabled="disabled" :canResize="false" type="textarea" placeholder="请输入"></h-input>
            <div class="desc">
                支持*、数字(不为负值、最大255)。多个功能号使用英文分号分隔
            </div>
        </h-form-item>
        <h-form-item label="节点号" prop="NodeNo">
            <h-input v-model="formItems.NodeNo" :disabled="disabled" placeholder="请输入"></h-input>
            <div class="desc">
                支持*、数字(不为负值、最大65535)
            </div>
        </h-form-item>
        <h-form-item label="目标端ID" prop="BackendID">
            <h-select v-model="formItems.BackendID" :disabled="disabled" placement="top" placeholder="请选择">
                <h-option v-for="item in backendIDs" :key="item.BackendId" :value="item.BackendId">{{ item.BackendId }}</h-option>
            </h-select>
        </h-form-item>
    </h-form>
</template>
<script>
import { getManagerProxy } from '@/api/mcApi';
import noData from '@/components/common/noData/noData';
import { functionValidate, systemNoValidate, nodeNoValidate, backendIdValidate } from './util';

export default {
    components: { noData },
    data() {
        return {
            backendIDs: [],
            uuid: null,
            formItems: {
                Functions: null,
                SystemNo: null,
                NodeNo: null,
                BackendID: null
            },
            rules: {
                Functions: [{
                    validator: functionValidate,
                    trigger: 'blur,change',
                    required: true
                }],
                SystemNo: [{
                    validator: systemNoValidate,
                    trigger: 'blur,change',
                    required: true
                }],
                NodeNo: [{
                    validator: nodeNoValidate,
                    required: false,
                    trigger: 'blur,change'
                }],
                BackendID: [
                    {
                        validator: backendIdValidate,
                        required: true,
                        trigger: 'blur,change'
                    }
                ]
            }
        };
    },
    props: {
        node: {
            type: Object,
            default: () => {}
        },
        type: {
            type: String
        },
        disabled: {
            type: Boolean
        }
    },
    mounted() {
        this.init();
    },
    created() {
        if (this.type === 'add') return;
        Object.keys(this.formItems).forEach(key => {
            this.$watch(`formItems.${key}`, (newVal, oldVal) => {
                this.callUpdateTable();
            });
        });
    },
    methods: {
        /**
         * 校验并返回数据
         */
        validateAndGetAll() {
            return new Promise((resolve, reject) => {
                this.$refs['formValidate'].validate(async valid => {
                    if (valid) {
                        resolve({ data: this.formItems, uuid: this.uuid });
                    } else {
                        reject(new Error(''));
                    }
                });
            });
        },
        callUpdateTable() {
            this.$refs['formValidate'] && this.$refs['formValidate'].validate(async valid => {
                if (valid) {
                    this.$emit('callUpdateTable', { data: this.formItems, uuid: this.uuid });
                }
            });
        },
        /**
         * 设置数据
         */
        setData({ uuid, ...others }) {
            this.uuid = uuid;
            this.formItems = { ...others, visible: others.visible ?? true };
        },
        /**
         * 获取单一数据
         */
        getData() {
            return {
                ...this.formItems,
                uuid: this.uuid
            };
        },
        async init() {
            const param = [
                {
                    manageProxyIp: this.node.manageProxyIp,
                    manageProxyPort: this.node.manageProxyPort,
                    pluginName: 'ldp_front',
                    funcName: 'BackendStat'
                }
            ];
            try {
                const res = await getManagerProxy(JSON.stringify(param));
                if (res.code === '200') {
                    if (res.data?.[0]?.ErrorNo) {
                        this.$hMsgBox.error({
                            title: '获取目标端id失败',
                            content: res.data?.[0]?.ErrorMsg
                        });
                    } else {
                        const dataObj = res.data?.[0] || {};
                        this.backendIDs = dataObj.Backends || [];
                    }
                }
            } catch (err) {
                console.log('获取目标端id失败', err);
            }
        }
    }
};

</script>
<style lang="less" scoped>
.desc {
    font-size: 12px;
    color: #9296a1;
    line-height: 12px;
    margin-top: 10px;
}

/deep/ .no-data {
    height: 200px;
}
</style>
