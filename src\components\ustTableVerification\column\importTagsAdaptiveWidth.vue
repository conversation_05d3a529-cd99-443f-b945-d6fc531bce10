<template>
    <div ref="tags-container" class="tags-container">
        <span v-for="(item, index) in tagList" :key="index"
            ref="tags" class="tags">{{item}}
            <h-icon
                v-if="showCopyIcon"
                name="max"
                size="12"
                :data-clipboard-text="item"
                class="copy-icon"
                @on-click="onCopied"></h-icon>
        </span>
        <h-poptip
            class="apm-poptip monitor-poptip"
            :title="poptipTitle"
            :trigger="poptipTrigger"
            placement="top-end"
            positionFixed
            :style="{display: showMore ? 'inline-block' : 'none'}"
        >
            <div ref="tags-more" class="tags-more">···</div>
            <div slot="content" class="poptip-content">
                <div v-for="(item, index) in tagList" :key="index" class="tags">
                    {{item}}
                    <h-icon
                        v-if="showCopyIcon"
                        name="max"
                        size="12"
                        :data-clipboard-text="item"
                        class="copy-icon"
                        @on-click="onCopied"></h-icon>
                </div>
            </div>
        </h-poptip>
    </div>
</template>

<script>
import Clipboard from 'clipboard';
export default {
    name: 'ImportTagsAdaptiveWidth',
    components: {
    },
    props: {
        tagList: {
            type: Array,
            default: () => []
        },
        showCopyIcon: {
            type: Boolean,
            default: false
        },
        poptipTitle: {
            type: String,
            default: ''
        },
        poptipTrigger: {
            type: String,
            default: 'hover'
        }
    },
    data() {
        return {
            showMore: false
        };
    },
    mounted() {
        this.resizeObserver = new ResizeObserver(entries => {
            this.fetTagsWidth();
        });
        this.resizeObserver.observe(this.$refs['tags-container']);
    },
    beforeDestroy() {
        this.resizeObserver.disconnect(this.$refs['tags-container']);
    },
    methods: {
        // 标签根据容器宽度自适应展示
        fetTagsWidth() {
            this.$nextTick(() => {
                const container = this.$refs['tags-container'];
                const tags = this.$refs['tags'];
                const moreTag = this.$refs['tags-more'];

                this.showMore = false;
                const marginWidth = tags?.length * 5;
                let totalWidth = container?.offsetWidth - moreTag?.offsetWidth - marginWidth;
                // 计算并隐藏超出宽度的标签
                for (let i = 0; i <= tags?.length - 1; i++) {
                    if (!tags[i]) break;
                    tags[i].style.display = 'inline-block';
                    const tagWidth = tags[i]?.offsetWidth;
                    if (totalWidth - tagWidth < 0) {
                        this.showMore = true; // 存在标签隐藏，显示···标签
                        tags[i].style.display = 'none';
                        totalWidth -= tagWidth;
                    } else {
                        totalWidth -= tagWidth;
                    }
                }

            });
        },
        // 标签复制
        onCopied() {
            const clipBoard = new Clipboard('.copy-icon');
            clipBoard.on('success', (e) => {
                this.$hMessage.success('复制成功');
                clipBoard.destroy();
            });
            clipBoard.on('error', (e) => {
                this.$hMessage.error('该浏览器不支持复制');
                clipBoard.destroy();
            });
        }
    }
};
</script>

<style lang="less" scope>
@import url("@/assets/css/poptip-1.less");

.tags-container {
    display: flex;
    overflow: hidden;
    white-space: nowrap;

    .tags {
        padding: 3px 5px;
        margin-right: 5px;
        background: #33394e;
        border-radius: 3px;

        .copy-icon {
            margin-left: 3px;
            color: var(--icon-color);
        }

        .copy-icon:hover {
            color: var(--icon-hover);
        }

        .copy-icon:active {
            color: var(--icon-press-down);
        }
    }

    .tags-more {
        font-size: 14px;
        margin-left: auto; /* 将"..."标签推到最右边 */
        padding: 3px 5px;
        margin-right: 5px;
        background: #33394e;
        border-radius: 3px;
        cursor: pointer;
        user-select: none; /* 防止文本被选中 */
    }

    .poptip-content {
        max-width: 300px;
        min-height: 20px;
        max-height: 150px;
        margin: 4px;

        & > .tags {
            margin: 0 0 4px;
        }
    }
}
</style>
