<template>
    <div ref="tab-box" class="tab-box">
        <a-loading v-if="loading"></a-loading>
        <div v-else class="tab-child-box">
            <obs-table ref="table" :height="tableHeight" :title="offerTitle" showTitle :tableData="offerTableData" notSetWidth autoHeadWidth :columns="offerColumns" highlightRow rowSelectOnly :hasPage="false" @on-current-change="tableRowcheckedChange" />
            <obs-table ref="table" :height="tableHeight" :title="seatTitle" showTitle :tableData="seatTableData" notSetWidth autoHeadWidth :columns="seatColumns" :hasPage="false" />
        </div>
    </div>
</template>

<script>
import aLoading from '@/components/common/loading/aLoading';
import obsTable from '@/components/common/obsTable/obsTable';
import { getManagerProxy } from '@/api/mcApi';
export default {
    props: {
        nodeData: {
            type: Object,
            default: () => { }
        },
        pollTime: {
            type: Number,
            default: 0
        }
    },
    components: { aLoading, obsTable },
    data() {
        return {
            loading: true,
            offerTitle: {
                label: '报盘实例统计信息'
            },
            offerTableData: [],
            offerColumns: [
                {
                    title: '报盘序号',
                    key: 'OfferIndex',
                    ellipsis: true
                },
                {
                    title: '报盘名',
                    key: 'OfferName',
                    ellipsis: true
                },
                {
                    title: '报盘类型',
                    key: 'OfferType',
                    ellipsis: true
                },
                {
                    title: '报盘状态',
                    key: 'OfferStatus',
                    ellipsis: true
                },
                {
                    title: '交易所状态',
                    key: 'ExchangeStatus',
                    ellipsis: true
                },
                {
                    title: '申报个数',
                    key: 'ReportNum',
                    ellipsis: true
                },
                {
                    title: '申报成功个数',
                    key: 'ReportSucceedNum',
                    ellipsis: true
                },
                {
                    title: '申报失败个数',
                    key: 'ReportFailedNum',
                    ellipsis: true
                },
                {
                    title: '重复申报个数',
                    key: 'RepeatNum',
                    ellipsis: true
                },
                {
                    title: '确认个数',
                    key: 'ConfirmNum',
                    ellipsis: true
                },
                {
                    title: '确认回报成功个数',
                    key: 'ConfirmSucceedNum',
                    width: 140,
                    ellipsis: true
                },
                {
                    title: '确认回报失败个数',
                    key: 'ConfirmFailedNum',
                    width: 140,
                    ellipsis: true
                },
                {
                    title: '成交个数',
                    key: 'DealNum',
                    ellipsis: true
                },
                {
                    title: '成交回报成功个数',
                    key: 'DealSucceedNum',
                    width: 140,
                    ellipsis: true
                },
                {
                    title: '成交回报失败个数',
                    key: 'DealFailedNum',
                    width: 140,
                    ellipsis: true
                }
            ],
            seatTitle: {
                label: '席位号报盘处理统计信息'
            },
            seatTableData: [],
            seatColumns: [
                {
                    title: '席位号',
                    key: 'SeatId',
                    ellipsis: true
                },
                {
                    title: '平台类型',
                    key: 'PlatformType',
                    ellipsis: true
                },
                {
                    title: '申报个数',
                    key: 'ReportNum',
                    ellipsis: true
                },
                {
                    title: '申报成功个数',
                    key: 'ReportSucceedNum',
                    ellipsis: true
                },
                {
                    title: '申报失败个数',
                    key: 'ReportFailedNum',
                    ellipsis: true
                },
                {
                    title: '重复申报个数',
                    key: 'RepeatNum',
                    ellipsis: true
                },
                {
                    title: '确认个数',
                    key: 'ConfirmNum',
                    ellipsis: true
                },
                {
                    title: '确认回报成功个数',
                    key: 'ConfirmSucceedNum',
                    width: 140,
                    ellipsis: true
                },
                {
                    title: '确认回报失败个数',
                    key: 'ConfirmFailedNum',
                    width: 140,
                    ellipsis: true
                },
                {
                    title: '成交个数',
                    key: 'DealNum',
                    ellipsis: true
                },
                {
                    title: '成交回报成功个数',
                    key: 'DealSucceedNum',
                    width: 140,
                    ellipsis: true
                },
                {
                    title: '成交回报失败个数',
                    key: 'DealFailedNum',
                    width: 140,
                    ellipsis: true
                }
            ],
            currentRow: {},
            tableHeight: 0
        };
    },
    mounted() {
        window.addEventListener('resize', this.fetTableHeight);
    },
    beforeDestroy() {
        window.removeEventListener('resize', this.fetTableHeight);
    },
    methods: {
        async initData() {
            this.loading = true;
            this.currentRow = {};
            await this.getFileData();
            this.loading = false;
        },
        // 设置table高度
        fetTableHeight() {
            this.tableHeight = (this.$refs['tab-box']?.offsetHeight - 150) / 2;
        },
        // 表格切换选中行
        tableRowcheckedChange(currentRow) {
            if (currentRow?.OfferIndex !== this.currentRow?.OfferIndex){
                this.seatTableData = [];
            }
            this.currentRow = currentRow;
            this.offerTableData.forEach(item => {
                item._highlight = item?.OfferIndex === this.currentRow?.OfferIndex; // 表格刷新后默认选中
            });
            this.seatTableData = currentRow?.SeatsStatisticsInfo || [];
        },
        async getFileData() {
            const { offerStatisticsInfo } = await this.getAPi();
            this.offerTableData = [...offerStatisticsInfo];
            if (this.offerTableData.length){
                const row = this.offerTableData.find(v => v?.OfferIndex === this.currentRow?.OfferIndex);
                this.tableRowcheckedChange(Object.keys(this.currentRow).length && row ? row : this.offerTableData?.[0]);
            } else {
                this.seatTableData = [];
            }
            this.fetTableHeight();
        },
        // 接口请求
        async getAPi() {
            const data = {
                offerStatisticsInfo: []
            };
            const param = [
                {
                    manageProxyIp: this.nodeData.ip,
                    manageProxyPort: this.nodeData.manageProxyPort,
                    pluginName: 'ldp_offerproc',
                    funcName: 'GetOfferStatisticsInfo'
                }
            ];
            try {
                const res = await getManagerProxy(JSON.stringify(param));
                if (res.code === '200') {
                    res.data?.[0]?.OfferStatisticsInfo?.length && (data.offerStatisticsInfo = res.data[0].OfferStatisticsInfo);
                }
                return data;
            } catch (err) {
                this.$emit('clear');
            }
        }
    }
};
</script>
<style lang="less" scoped>
.tab-box {
    height: 100%;
    overflow: hidden;

    .tab-child-box {
        height: 100%;

        .obs-table {
            height: auto;
        }
    }
}
</style>
