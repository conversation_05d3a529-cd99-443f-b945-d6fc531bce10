.box-monitor {
    // display: flex;
    // justify-content: space-between;
    // flex-wrap: wrap;
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(48%, 1fr));
    // grid-template-rows: repeat(auto-fill, minmax(48%, 1fr));
    gap: 5px 0;
    width: 100%;
    height: 100%;
    overflow-x: hidden;
    overflow-y: auto;

    & > .chart {
        width: 100%;
        height: 100%;
        min-height: 200px;
    }

    .chart:nth-child(2n+1):last-child {
        grid-column: span 2;
        /* 让最后一行的奇数项（最后一个元素）跨足整个行 */
    }

    & > .no-data {
        width: calc(100% - 15px);
        height: 100%;
        display: flex;
        justify-content: center;
        align-items: center;
        color: var(--font-color);
        font-size: var(--title-font-size);
    }
}
