<template>
    <div ref="mc-box" class="mc-box">
        <a-loading v-if="loading"></a-loading>
        <obs-table
            ref="tableRef"
            :height="tableHeight"
            :title="title"
            :isSimpleTable="true"
            showTitle
            :columns="columns"
            :tableData="tableData"
            @button-click="handleBtnClick"
        />

        <!-- 死信消息侧拉框 -->
        <mc-msg-list-drawer
            v-if="drawerInfo.status"
            :modalInfo="drawerInfo">
        </mc-msg-list-drawer>
        <!-- 清空队列弹窗 -->
        <clear-queue-modal
            v-if="modalInfo.status"
            :modalInfo="modalInfo"
            @update="initData">
        </clear-queue-modal>
    </div>
</template>

<script>
import { getManagerProxy } from '@/api/mcApi';
import aLoading from '@/components/common/loading/aLoading';
import obsTable from '@/components/common/obsTable/obsTable';
import McMsgListDrawer from './modal/mcMsgListDrawer.vue';
import clearQueueModal from './modal/clearQueueModal.vue';
export default {
    name: 'DeadLetterQueue',
    components: { obsTable, aLoading, McMsgListDrawer, clearQueueModal },
    props: {
        nodeData: {
            type: Object,
            default: () => {}
        },
        productId: {
            type: String,
            default: ''
        }
    },
    data() {
        return {
            loading: false,
            title: {
                label: '死信队列',
                slots: [
                    {
                        type: 'button',
                        buttonType: 'dark',
                        value: '刷新'
                    }
                ]
            },
            columns: [
                {
                    title: '死信主题',
                    key: 'DLQName',
                    ellipsis: true
                },
                {
                    title: '消费者集群',
                    key: 'GroupName',
                    minWidth: 100,
                    ellipsis: true
                },
                {
                    title: '消息主题',
                    key: 'TopicName',
                    ellipsis: true
                },
                {
                    title: '消息分区',
                    key: 'PartitionNo',
                    ellipsis: true
                },
                {
                    title: '上次接收时间',
                    key: 'LastRecvTime',
                    minWidth: 110,
                    render: (h, params) => {
                        const formatTime = params.row?.LastRecvTime ? this.formatTime(params.row.LastRecvTime) : '';
                        return h('div', {
                            attrs: {
                                title: formatTime
                            },
                            style: {
                                overflow: 'hidden', // 超出的文本隐藏
                                'text-overflow': 'ellipsis', // 溢出用省略号显示
                                'white-space': 'nowrap' // 溢出不换行
                            }
                        }, formatTime);
                    }
                },
                {
                    title: '上次进入原因',
                    key: 'LastEntryReason',
                    minWidth: 110,
                    ellipsis: true
                },
                {
                    title: '死信消息数',
                    key: 'MsgCount',
                    width: 110,
                    render: (h, params) => {
                        return h('div', [
                            h(
                                'Button',
                                {
                                    props: {
                                        type: 'text',
                                        size: 'small'
                                    },
                                    on: {
                                        click: () => {
                                            this.viewDeadLetterMsg(params.row);
                                        }
                                    }
                                },
                                params.row.MsgCount
                            )
                        ]);
                    }
                },
                {
                    title: '消息内容',
                    key: 'Msg',
                    width: 100,
                    render: (h, params) => {
                        return h('div', [
                            h(
                                'Button',
                                {
                                    props: {
                                        type: 'text',
                                        size: 'small'
                                    },
                                    on: {
                                        click: () => {
                                            this.handleClearQueue(params.row);
                                        }
                                    }
                                },
                                '清空队列'
                            )
                        ]);
                    }
                }
            ],
            tableData: [],
            drawerInfo: {
                status: false
            },
            modalInfo: {
                status: false
            },
            tableHeight: 0
        };
    },
    mounted() {
        window.addEventListener('resize', this.fetTableHeight);
    },
    beforeDestroy() {
        window.removeEventListener('resize', this.fetTableHeight);
    },
    methods: {
        async initData() {
            this.loading = true;
            this.tableData = await this.getMcAPi();
            this.fetTableHeight();
            this.loading = false;
        },
        // 刷新列表
        handleBtnClick() {
            this.initData();
        },
        formatTime(timeString) {
            if (!timeString) return '';
            // 提取日期和时间部分
            const datePart = timeString.substring(0, 8);
            const timePart = timeString.substring(9, 22); // 包含时分秒和微秒部分

            // 构建年、月、日、时、分、秒、微秒部分
            const year = datePart.substring(0, 4);
            const month = datePart.substring(4, 6);
            const day = datePart.substring(6, 8);

            const hour = timePart.substring(0, 2);
            const minute = timePart.substring(2, 4);
            const second = timePart.substring(4, 6);
            const microsecond = timePart.substring(7, 14);

            // 格式化输出
            return `${year}-${month}-${day} ${hour}:${minute}:${second}.${microsecond}`;
        },
        // 设置table高度
        fetTableHeight() {
            this.tableHeight = this.$refs['mc-box']?.offsetHeight - 64;
        },
        // 接口请求
        async getMcAPi() {
            let data = [];
            try {
                const insInfo = this.nodeData?.productInstances?.[0];
                const param = [
                    {
                        manageProxyIp: insInfo?.manageProxyIp,
                        manageProxyPort: insInfo?.manageProxyPort,
                        instanceName: insInfo?.instanceName,
                        pluginName: 'ldp_mc',
                        funcName: 'GetDeadLetterQueueList'
                    }
                ];
                const res = await getManagerProxy(JSON.stringify(param));
                if (res.code === '200' && res.data?.[0]?.Result?.length) {
                    data = res.data[0].Result || [];
                }
            } catch (err) {
                this.$emit('clear');
            }
            return data;
        },
        // 死信消息侧拉框
        viewDeadLetterMsg(row) {
            this.drawerInfo.params = {
                productId: this.productId,
                appClusterId: this.nodeData?.id,
                topicName: row?.DLQName,
                partitionNo: 0,
                queryType: 'range',
                beginId: row?.BeginMsgId,
                endId: row?.EndMsgId,
                filter1: row?.GroupName,
                filter2: row?.TopicName,
                filter3: row?.PartitionNo,
                page: 1,
                pageSize: 1000
            };
            this.drawerInfo.status = true;
        },
        // 清空队列
        handleClearQueue(row) {
            const insInfo = this.nodeData?.productInstances?.[0];
            this.modalInfo.reqParams = [
                {
                    manageProxyIp: insInfo?.manageProxyIp,
                    manageProxyPort: insInfo?.manageProxyPort,
                    instanceName: insInfo?.instanceName,
                    pluginName: 'ldp_mc',
                    funcName: 'ClearDeadLetterQueueData',
                    params: {
                        DLQName: row?.DLQName,
                        GroupName: row?.GroupName,
                        TopicName: row?.TopicName,
                        PartitionNo: row?.PartitionNo
                    }
                }
            ];
            this.modalInfo.status = true;
        }
    }
};
</script>

<style lang="less" scoped>
.mc-box {
    height: 100%;
    overflow: hidden;

    .obs-table {
        margin: 0;
    }
}
</style>
