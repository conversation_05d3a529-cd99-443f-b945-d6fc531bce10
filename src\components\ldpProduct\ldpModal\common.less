/deep/ .h-modal-body {
    padding: 20px 32px;
}

.wrapper {
    display: flex;
    width: 100%;
    height: 400px;
    border: 1px solid #e7e7e7;

    & > .wrapper-left {
        width: 220px;
        height: 100%;
        overflow-y: auto;
        border-right: 1px solid #e7e7e7;

        & > p {
            padding: 5px 0 3px 30px;
        }
    }

    & > .wrapper-right {
        flex: 1;
        overflow-y: auto;
    }

    /deep/ .h-tabs-tab {
        font-size: var(--font-size-base);
    }

    /deep/ textarea {
        background: #f8f8f8;
        border: none;
    }
}

.title {
    position: relative;
    font-size: var(--font-size);
    padding: 15px 10px 10px 30px;

    &::before {
        display: inline-block;
        content: "";
        position: absolute;
        width: 4px;
        height: 17px;
        background: var(--link-color);
        left: 15px;
        top: 17px;
    }
}

span {
    color: #777;
}
