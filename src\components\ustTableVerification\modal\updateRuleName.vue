<!--
 * @Description: 修改校验任务名称
-->
<template>
    <div>
        <h-msg-box-safe
            v-model="modalData.status"
            :escClose="true"
            :mask-closable="false"
            title="修改任务名称"
            width="600"
            @on-open="getCollections"
            @submit.native.prevent>
            <h-form ref="formValidate" :model="formValidate" :label-width="100">
                <h-form-item
                    label="任务名称"
                    prop="ruleName"
                    required
                    :validRules="stringRule">
                    <h-input
                        v-model.trim="formValidate.ruleName"
                        placeholder="请输入任务名称">
                    </h-input>
                </h-form-item>
            </h-form>

            <template v-slot:footer>
                <a-button @click="modalData.status = false">取消</a-button>
                <a-button type="primary" @click="submitConfig">确定</a-button>
            </template>
        </h-msg-box-safe>
    </div>
</template>

<script>
import _ from 'lodash';
import { stringLengthRule } from '@/utils/validate';
import aButton from '@/components/common/button/aButton';
export default {
    props: {
        modalInfo: {
            type: Object,
            default: null
        }
    },
    data() {
        return {
            modalData: this.modalInfo,
            formValidate: {
                ruleName: ''
            },
            stringRule: [{ test: stringLengthRule(15), trigger: 'change, blur' }]
        };
    },
    methods: {
        async getCollections() {
            this.formValidate.ruleName = _.cloneDeep(this.modalData.ruleName);
        },
        submitConfig() {
            this.$refs['formValidate'].validate(async (valid) => {
                if (valid) {
                    this.$emit('update-rule-name', this.formValidate.ruleName, this.modalData._index);
                    this.modalData.status = false;
                }
            });
        }
    },
    components: { aButton }
};
</script>
<style lang="less" scoped>
    p {
        color: var(--font-opacity-color);
    }

</style>
