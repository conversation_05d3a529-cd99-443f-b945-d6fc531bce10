/* stylelint-disable color-named */
.best-table {
    position: relative;
    width: 100%;
    height: 100%;
    overflow: hidden;
    padding: 0;

    .table-title {
        width: 100%;
        height: 40px;
        border-bottom: var(--border);
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-top: 10px;

        & > span {
            font-size: var(--font-size);
            color: var(--font-color);
        }

        & > div {
            button {
                margin-right: 5px;
            }
        }
    }

    .form-box {
        overflow-y: hidden;
        transition: height 1s;
    }

    .h-page {
        height: 42px;
    }

    // 含有tab页的表格页面

    .h-tabs {
        height: 100%;
    }

    .h-tabs .h-tabs-content-wrap {
        height: 100%;
        overflow: hidden;
        border-top: 1px solid var(--border-color);
    }

    .h-tabs .h-tabs-tabpane {
        height: 100%;
        overflow: hidden;
    }

    .h-tabs .h-tabs-content {
        height: 100%;
    }

    .h-tabs-nav .h-tabs-tab {
        color: var(--font-color);
    }

    .h-tabs-nav .h-tabs-tab-active {
        color: var(--link-color);
    }

    .h-tabs-bar {
        margin-bottom: 0;
        border: none;
    }

    .business-tab > .h-tabs-bar {
        width: calc(100% - 466px);
    }

    .btn-list {
        position: absolute;
        right: 0;
        top: 2px;
        z-index: 1;

        & > button {
            margin-right: 5px;
        }
    }

    .table-box {
        width: 100%;
        height: 100%;
    }
}
