import './infoBar.less';
import '@/assets/css/poptip-1.less';
import aButton from '@/components/common/button/aButton';
import processBar from '@/components/common/processBar/processBar';
import obsTable from '@/components/common/obsTable/obsTable';
import obsTitle from '@/components/common/title/obsTitle';
import description from '@/components/common/description/description';
import chart from '@/components/common/chart/chart';
import { transferVal } from '@/utils/utils';
export default {
    name: 'info-bar',
    components: { processBar, obsTable, obsTitle, description, chart, aButton },
    props: {
        // info 类型
        type: {
            type: String,
            default: 'text'  // text process-bar obj table chart description combine monitor
        },
        // 是否支持点击
        canClick: {
            type: Boolean,
            default: false
        },
        customClassName: {
            type: String,
            default: ''  // 与info-bar类同级；用于特定对象修改样式
        },
        // infoId必须全局唯一性！！！！
        selectInfoId: {
            type: String,
            default: ''
        },
        // monitor类型布局--特有属性
        autoGrid: {
            type: Boolean,
            default: false
        },
        // monitor类型布局--特有属性
        gridMinWidth: {
            type: String,
            default: '110px'
        },
        // monitor类型布局--特有属性
        gridSpan: {
            type: Number,
            default: 0
        },
        // 气泡提示框
        poptipInfo: {
            type: Object,
            default: undefined
        },
        // 点击事件 infoId 用来区分组件
        infoId: {
            type: String,
            default: ''
        },
        // 是否存在背景色
        hasBackgroundColor: {
            type: Boolean,
            default: true
        },
        // 标题
        title: {
            type: String | Object,
            default: undefined
        },
        // 标题别名
        titleAlias: {
            type: String,
            default: ''
        },
        // 标题icon
        iconName: {
            type: String,
            default: ''
        },
        // 标题颜色
        iconColor: {
            type: String,
            default: ''
        },
        // 宽度
        infoWidth: {
            type: String,
            default: ''
        },
        // 高度
        infoHeight: {
            type: String,
            default: ''
        },
        // key-value字典    // 对象  描述列表等
        infoDic: {
            type: Array,
            default: () => []
        },
        // 数据     // 对象  描述列表  纯文本  进度条  折线图、饼图 table combine monitor
        info: {
            type: Array | Object
        },
        // 表格加载
        loading: {
            type: Boolean,
            default: false
        },
        status: {
            type: String,
            default: '' // success, warn, error
        },
        infoSize: {
            type: String,
            default: 'min' // 传空正常大小, min: 高度减小
        }
    },
    data() {
        return {
        };
    },
    methods: {
        handleButtonClick(key) {
            this.$emit('button-click', key);
        },
        handleSelectChange(val, key) {
            this.$emit('select-change', val, key);
        },
        handleTableRowChange(val) {
            this.$emit('on-current-change', val);
        },
        handleInputChange(val, searchKey) {
            this.$emit('input-change', val, searchKey);
        },
        handlePieClick(key) {
            this.$emit('pie-click', key);
        },
        setSelectVal(key, val) {
            this.$refs['obs-title'].setSelectVal(key, val);
        },
        getSelectVal(key) {
            return this.$refs['obs-title'].getSelectVal(key);
        },
        setCheckVals(key, val) {
            this.$refs['obs-title'].setCheckVals(key, val);
        },
        getCheckVals(key) {
            return this.$refs['obs-title'].getCheckVals(key);
        },
        handleBarClicK(e, id, canClick) {
            if (!canClick) return;
            e.preventDefault();
            e.stopPropagation();
            this.$emit('info-bar-click', id);
        }
    },
    // eslint-disable-next-line complexity
    render() {
        // poptip
        const generatePoptip = (title, poptipInfo) => {
            return <div class="info-bar-title">
                <h-poptip trigger="hover" width={poptipInfo?.width || 220} customTransferClassName="apm-poptip monitor-poptip" transfer={true} title={poptipInfo.title} placement={poptipInfo.placement || 'top'}>
                    {title}
                    <div slot="content" class="pop-content">
                        {
                            Array.isArray(poptipInfo.contentDic)
                                ? poptipInfo.contentDic.map(item => {
                                    const unit = item?.unit ?? '';
                                    const key = item.key;
                                    const label = transferVal(item?.label) || key;
                                    const value = transferVal(poptipInfo.content?.[key]) ? transferVal(poptipInfo.content?.[key]) : '-';
                                    const formatMethod = item.formatMethod || poptipInfo.formatMethod;
                                    const formattedValue = transferVal(poptipInfo.content?.[key]) && formatMethod ? formatMethod(value, unit) : { value, unit };
                                    return <h-row>
                                        <h-col span="10" title={label || '-'}>{label || '-'}</h-col>
                                        <h-col span="4">&nbsp;</h-col>
                                        <h-col span="10" title={`${formattedValue.value} ${formattedValue.unit ?? unit}`}>{formattedValue.value} {formattedValue.unit ?? unit}</h-col>
                                    </h-row>;
                                })
                                : Object.keys(poptipInfo.content).map(key => {
                                    const label = transferVal(poptipInfo?.contentDic?.[key]) || key || '-';
                                    const value = transferVal(poptipInfo.content[key]) ? transferVal(poptipInfo.content[key]) : '-';
                                    const usedFormatMethod = poptipInfo?.formatMethod;
                                    const formattedValue = transferVal(poptipInfo.content[key]) && usedFormatMethod ? usedFormatMethod(value) : { value };
                                    return <h-row>
                                        <h-col span="10" title={label}>{label}</h-col>
                                        <h-col span="4">&nbsp;</h-col>
                                        <h-col span="10" title={formattedValue.value}>{formattedValue.value}</h-col>
                                    </h-row>;
                                })
                        }
                    </div>
                </h-poptip>
            </div>;
        };
        // 纯文本
        const generateText = (info) => {
            const unit = info?.unit ?? '';
            const value = transferVal(info?.value) ? transferVal(info?.value) : '-';
            const formattedValue = transferVal(info?.value) && info?.formatMethod ? info.formatMethod(value, unit) : { value, unit };
            return <div class="info-bar-text" title={`${formattedValue.value} ${formattedValue.unit ?? unit}`}>
                <span>{formattedValue.value} {formattedValue.unit ?? unit}</span>
            </div>;
        };
        // 进度条
        const  generateProcessBar = (info) => {
            return <div class="info-process-bar">{ info && info.map(v => { return <process-bar label={v.label} width={v.scale || '0'} name={transferVal(v.text) || '-'}></process-bar>; })}</div>;
        };
        // 对象--slots
        const generateSlots = (slots = []) => {
            return slots.map((o) => {
                if (o.type === 'button') {
                    return (
                        <h-button
                            type={'text'}
                            onClick={() => this.handleButtonClick(o?.key)}
                            class="slot-button"
                        >
                            {o?.iconName && <h-icon name={o?.iconName} size={o?.iconSize || 18} style={{ color: o?.iconColor || 'var(--font-color)' }}></h-icon> }
                            {o?.value || ''}
                        </h-button>
                    );
                }
                return '';
            });
        };

        // 性能指标对象
        const generatePerformObj = (infoDic, info) => {

            // eslint-disable-next-line complexity
            const renderInfoBar = (item) => {
                const label = transferVal(item?.label) ? transferVal(item?.label) : '';
                const value = transferVal(info[item.key]) ? transferVal(info[item.key]) : '-';
                const unit = item?.unit ?? '';
                const formattedValue =  transferVal(info[item.key]) && item?.formatMethod ? item.formatMethod(value, unit) : { value, unit };

                return (
                    <div class='info-bar-perform-detail'>
                        {
                            item.tagLabel && <div class="info-bar-perform-tag" title={item.tagLabel}>
                                <span>{item.tagLabel}</span>
                            </div>
                        }
                        {
                            !item.tagLabel && <div class="info-bar-title" title={item.labelAlias || item.label}>
                                {item.label} {item.iconName && <h-icon name={item.iconName} color={item.iconColor}></h-icon>}
                            </div>
                        }
                        {
                            !item?.type && <div class="info-bar-perform-text-detail">
                                { label && <span class='info-bar-perform-label' title={label}>{label}</span> }
                                <span class='info-bar-perform-text' style={item?.fontStyles || ''} title={formattedValue.value}>{formattedValue.value}</span>
                                {(formattedValue.unit ?? unit) && <span class='info-bar-perform-label' title={formattedValue.unit ?? unit}>{formattedValue.unit ?? unit}</span>}
                            </div>
                        }
                        {
                            item?.type === 'process-bar' &&
                            <div class="info-process-bar" style="height: 62px">
                                <process-bar label={label} width={value || '0'} name={transferVal(item.text)}></process-bar>
                            </div>
                        }
                        {
                            item?.type === 'text' &&
                            <div class="info-bar-text" title={`${formattedValue.value} ${formattedValue.unit ?? unit}`}>
                                <span>{formattedValue.value} {formattedValue.unit ?? unit}</span>
                            </div>
                        }
                    </div>
                );
            };

            const renderInfoObj = (items) => {
                return (
                    <div class="info-bar-perform-obj"> {
                        items.map(v => {
                            const label = transferVal(v.label || v.key) ? transferVal(v.label || v.key) : '';
                            const value = transferVal(info[v.key]) ? transferVal(info[v.key]) : '-';

                            const unit = v?.unit ?? '';
                            const formattedValue =  transferVal(info[v.key]) && v?.formatMethod ? v.formatMethod(value, unit) : { value, unit };

                            return (
                                <div class="info-bar-perform-obj-detail">
                                    <span title={label}>{label}</span>
                                    <span style="text-align: right;"  title={`${formattedValue.value} ${formattedValue.unit ?? unit}`}>
                                        <b style="color: var(--font-color);" >{formattedValue.value}</b> {formattedValue.unit ?? unit}
                                    </span>
                                </div>
                            );
                        })
                    }</div>
                );
            };

            const activeInfo = infoDic.filter(o => o?.posLevel);
            const extraInfo = infoDic.filter(o => !o?.posLevel);
            return <div>
                {activeInfo.length > 0 && activeInfo.map(renderInfoBar)}
                {extraInfo.length > 0 && renderInfoObj(extraInfo)}
            </div>;
        };

        // 对象
        const generateObj = (infoDic, info) => {
            return <div class="info-bar-detail"> {
                infoDic.map(v => {
                    const label = transferVal(v.label || v.key) ? transferVal(v.label || v.key) : '-';
                    const value = transferVal(info[v.key]) ? transferVal(info[v.key]) : '-';

                    const unit = v?.unit ?? '';
                    const formattedValue =  transferVal(info[v.key]) && v?.formatMethod ? v.formatMethod(value, unit) : { value, unit };
                    return (
                        <p>
                            <span style="color: var(--font-opacity-color);" title={label}>{label}</span>
                            <span style="text-align: right;"  title={`${formattedValue.value} ${formattedValue.unit ?? unit}`}>
                                {formattedValue.value} {formattedValue.unit ?? unit}
                            </span>
                            {generateSlots(v?.slots || [])}
                        </p>
                    );
                })
            }</div>;
        };
        // 描述列表
        const  generateDes = (infoDic, info) => {
            return <description title='' type='description' dataDic={infoDic} data={info}></description>;
        };
        // 表格
        const generateTable = (info, loading = false) => {
            return <div class="info-bar-table"> {
                <obs-table loading={loading} height={info.height || '220'} showTitle tableData={info.tableData || []} columns={info.columns || []} highlightRow={info.highlightRow} rowSelectOnly={info.rowSelectOnly} v-on:on-current-change={this.handleTableRowChange}/>
            }
            </div>;
        };
        // 折线图、饼图
        const generateChart = (info) => {
            return <chart isWhiteBg={info.isWhiteBg} basicOpiton={info.basicOpiton} additionalOpiton={info.additionalOpiton} chartData={info.chartData}
                v-on:pie-click={this.handlePieClick} style={{ width: '100%', height: info.height ? info.height + 'px' : '100%' }} />;
        };
        // 组合模式：进度条、对象、描述列表
        const  generateCombine = (info) => {
            return <div class="info-bar-combine">
                {
                    info.map(v => {
                        if (v.type === 'process-bar') return generateProcessBar(v.info);
                        if (v.type === 'obj') return generateObj(v.infoDic, v.info);
                        if (v.type === 'description') return generateDes(v.infoDic, v.info);
                        return '';
                    })
                }
            </div>;
        };

        // 监控指标类
        const generatePopTipMonitor = (info) => {
            const gridStyles = this.autoGrid ? { gridTemplateColumns: `repeat(auto-fill, minmax(${this.gridMinWidth}, 1fr))` } : this.gridSpan ? { gridTemplateColumns: `repeat(${this.gridSpan}, 1fr)` } : {};

            return (
                <div class={this.autoGrid || this.gridSpan ? 'info-bar-monitor-grid' : 'info-bar-monitor'} style={gridStyles}>
                    {info.map(v => {
                        const itemStyles = { border: v?.canClick && v?.infoId === this.selectInfoId ? '1px solid var(--icon-press-down)' : '' };
                        const label = transferVal(v?.label || v?.key) ? (v?.label || v?.key) : '';
                        return (
                            <div
                                key={v?.infoId}
                                class={[
                                    v?.canClick ? 'info-bar-monitor-item-can-click' : 'info-bar-monitor-item',
                                    v?.status ? `info-bar-status-${v?.status}` : ''
                                ]}
                                style={itemStyles}
                                onclick={(e) => this.handleBarClicK(e, v?.infoId, v?.canClick)} // 点击的infoId,必须全局唯一性！！！！
                            >
                                {label && !v?.poptipInfo && <div class="info-bar-title" title={v?.labelAlias || label || '-'}><span>{label || '-'}</span></div>}
                                {label && v?.poptipInfo && generatePoptip(label, v?.poptipInfo)}
                                {v.type === 'text' && generateText(v)}
                                {v.type === 'obj' && generateObj(v.infoDic, v.info, v?.infoId)}
                                {v.type === 'performObj' && generatePerformObj(v.infoDic, v.info)}
                            </div>
                        );
                    })}
                </div>
            );
        };

        // 标题渲染
        const renderTitle = () => {
            if (this.title && typeof (this.title) === 'string' && this.poptipInfo) {
                return generatePoptip(this.title, this.poptipInfo);
            }
            if (this.title && typeof (this.title) === 'string' && !this.poptipInfo) {
                return <div class="info-bar-title" title={this.titleAlias || this.title}>
                    {this.title}
                    {this.iconName && <h-icon name={this.iconName} color={this.iconColor} />}
                </div>;
            }
            if (this.title && typeof (this.title) === 'object') {
                return <obs-title
                    ref="obs-title"
                    title={this.title}
                    v-on:button-click={this.handleButtonClick}
                    v-on:select-change={this.handleSelectChange}
                    v-on:input-change={this.handleInputChange}
                />;
            }
            return '';
        };

        return  <div
            class={[
                'info-bar',
                this.hasBackgroundColor ? '' : 'info-bar-no-color',
                this.status ? `info-bar-status-${this.status}` : '',
                this.canClick ? 'info-bar-clickable' : '',
                this.infoSize ? 'info-bar-min' : '',
                this.customClassName
            ]}
            style={[
                this.infoWidth ? { width: this.infoWidth } : '',
                this.infoHeight ? { height: this.infoHeight } : ''
            ]}
            title={this.titleTip}
            onclick={e => this.handleBarClicK(e, this.infoId, this.canClick)}
        >
            {/* 标题 */}
            { renderTitle() }

            {/* 内容 */}
            { this.type === 'text'  && generateText(this.info)}
            { this.type === 'process-bar'  && generateProcessBar(this.info)}
            { this.type === 'obj'  && generateObj(this.infoDic, this.info) }
            { this.type === 'performObj'  && generatePerformObj(this.infoDic, this.info) }
            { this.type === 'description' &&  generateDes(this.infoDic, this.info)}
            { this.type === 'table' &&  generateTable(this.info, this.loading)}
            { this.type === 'chart'  && generateChart(this.info)}
            { this.type === 'combine'  && generateCombine(this.info) }
            { this.type === 'monitor'  && generatePopTipMonitor(this.info) }
        </div>;
    }
};
