import fetch from './fetch';
import { objectToQueryString } from '@/utils/utils';

const v1 = window['LOCAL_CONFIG']['API_HOME'] || '/';
/**
 * 文件上传通用接口
 */
export function fileUpload(param) {
    return fetch({ contentType: 'multipart/form-data' }).post(`${v1}/file/upload`, param);
}
// --------------------------------------------------  产品总览 -----------------------------------------------------------------------

/**
 * 获取产品列表（拆）
 */
export function getProductListLight(param) {
    if (!param){
        return fetch().get(`${v1}/products`);
    }
    return fetch().get(`${v1}/products?${objectToQueryString(param)}`);
}
/**
 * 获取产品总览信息（拆）
 */
export function getProductOverview(param) {
    return fetch().get(`${v1}/product/overview-info?${objectToQueryString(param)}`);
}
/**
 * 获取产品应用节点列表（拆）
 */
export function getProductInstances(param) {
    return fetch().get(`${v1}/product/instances?${objectToQueryString(param)}`);
}
/**
 * 获取产品集群列表（拆）
 */
export function getProductClusters(param) {
    return fetch().get(`${v1}/product/appClusters?${objectToQueryString(param)}`);
}
/**
 * 更新zk用户名密码
 */
export function updateZkAuth(param) {
    return fetch().post(`${v1}/product/zookeeper/config`, param);
}
/**
 * 获取产品分片列表（拆）
 */
export function getProductShardings(param) {
    return fetch().get(`${v1}/product/shardings?${objectToQueryString(param)}`);
}

/**
 * 获取zookeeper LDP产品节点路径
 */
export function getZkProductPaths(param) {
    return fetch().get(`${v1}/zookeeper/ldp/product-paths?${objectToQueryString(param)}`);
}

/**
 * 应用节点识别
 */
export function getZkProductInstances(param) {
    return fetch().get(`${v1}/product/instance/integrated?${objectToQueryString(param)}`);
}

/**
 * 批量更新应用节点信息
 */
export function updateBatchAppInstance(param) {
    return fetch().post(`${v1}/product/instances/update/batch`, param);
}

/**
 * 添加、修改应用分片信息
 */
export function updateShardingInfo(param) {
    return fetch().post(`${v1}/product/sharding`, param);
}

/**
 * 删除应用分片信息
 */
export function deleteShardingInfo(param) {
    return fetch().post(`${v1}/product/sharding/delete`, param);
}

/**
 * 获取机房信息
 */
export function getMachineRoomInfo(param) {
    return fetch().get(`${v1}/product/rooms?${objectToQueryString(param)}`);
}

/**
 * 修改机房信息
 */
export function updateMachineRoomInfo(param) {
    return fetch().post(`${v1}/product/room`, param);
}

/**
 * 获取所有接入点信息
 */
export function getEndpoint(param) {
    return fetch().get(`${v1}/endpoints?${objectToQueryString(param)}`);
}

/**
 * 添加、修改接入点
 */
export function updateEndpoint(param) {
    return fetch().post(`${v1}/endpoint/create`, param);
}

/**
 * 接入点连接测试
 */
export function getEndpointTest(param) {
    return fetch().post(`${v1}/endpoint/test`, param);
}

/**
 * 删除接入点
 */
export function deleteEndpoint(param) {
    return fetch().get(`${v1}/endpoint/delete?${objectToQueryString(param)}`);
}

/**
 * 创建或更新内存表配置
 */
export function updateMemoryConfig(param) {
    return fetch().post(`${v1}/product/memory-table/config/createOrUpdate`, param);
}

/**
 * 获取产品rcm配置列表
 */
export function getRcmConfigList(param) {
    return fetch().get(`${v1}/product/ldpConfigs?${objectToQueryString(param)}`);
}

/**
 * 获取产品上下文列表（支持组播域）
 */
export function getContextRelationList(param) {
    return fetch().get(`${v1}/rcm/products/context/relation?${objectToQueryString(param)}`);
}

/**
 * 性能-一定时间段内委托的运行性能指标
 */
export function getPerformanceIndicators(param) {
    return fetch().get(`${v1}/latency/performance/indicators?${objectToQueryString(param)}`);
}

/**
 * 性能-一定时间段内委托的运行性能指标
 */
export function getPerformanceTrend(param) {
    return fetch().get(`${v1}/latency/performance/trend?${objectToQueryString(param)}`);
}

/**
 * 获取产品链路模型配置
 */
export function getTraceModelConfig(param) {
    return fetch().get(`${v1}/latency/trace-model/config?${objectToQueryString(param)}`);
}
/**
 * 获取核心集群处理性能
 */
export function getCoreCluster(param) {
    return fetch().get(`${v1}/observables/performance/core-cluster?${objectToQueryString(param)}`);
}

/**
* 获取核心集群处理功能号列表
*/
export function getCoreClusterDetail(param) {
    return fetch().get(`${v1}/observables/performance/core-cluster/detail?${objectToQueryString(param)}`);
}

/**
 * 获取产品特性开关列表
 */
export function getProductFeatures(param) {
    return fetch().get(`${v1}/product/supported-features?${objectToQueryString(param)}`);
}

/**
 * 查询时延链路模型名称列表
 */
export function getTraceModelNames(param) {
    return fetch().get(`${v1}/latency/trace-models/names?${objectToQueryString(param)}`);
}

/**
 * 设置时延链路模型
 */
export function setLatencyModel(param) {
    return fetch().post(`${v1}/latency/trace-model`, param);
}

/**
 * 查询时延链路模型列表
 */
export function getLatencyTraceModels(param) {
    return fetch().get(`${v1}/latency/trace-models?${objectToQueryString(param)}`);
}

/**
 * 查询时延链路模型列表
 */
export function getTraceModels(param) {
    return fetch().get(`${v1}/product/latency/trace-models?${objectToQueryString(param)}`);
}

/**
 * 查询产品时延链路模型配置
 */
export function getProductLatencyTraceModels(param) {
    return fetch().get(`${v1}/product/latency/trace-models-info?${objectToQueryString(param)}`);
}

/**
 * 设置产品时延链路模型
 */
export function setProductLatencyTraceModels(param) {
    return fetch().post(`${v1}/product/latency/trace-model`, param);
}

/** ---------------------------------------------- 三方服务集成-华讯 -------------------------------------------* */
/**
 * 新增/修改采集器（华讯）
 */
export function addOrEditEccomCollector(param) {
    return fetch().post(`${v1}/product/integration/eccom/collector`, param);
}

/**
 * 查询采集器列表（华讯）
 */
export function getEccomCollector() {
    return fetch().get(`${v1}/product/integration/eccom/collectors`);
}

/**
 * 删除采集器（华讯）
 */
export function deleteEccomCollector(param) {
    return fetch().post(`${v1}/product/integration/eccom/collector/delete`, param);
}

/**
 * 查询跨度汇总
 */
export function getLatencyAnalysisReport(param) {
    return fetch().post(`${v1}/entrust/latency/analysis/report`, param);
}

/**
 * 查询委托分布
 */
export function getLatencyAnalysisDistribution(param) {
    return fetch().post(`${v1}/entrust/latency/analysis/entrust/distribution`, param);
}

/**
 * 生成T3Config配置
 */
export function getT3ConfigData(param) {
    return fetch().get(`${v1}/entrust/latency/monitor/span/indicators/t3-api/config?${objectToQueryString(param)}`);
}
/**
 * 委托时延分布直方图查询
 */
export function getLatencyDistribution(param) {
    return fetch().post(`${v1}/entrust/span/latency/distribution`, param);
}

/**
 * 校验配置路径下有多少产品实例
 */
export function checkProductInstance(param) {
    return fetch().post(`${v1}/product/manage/check`, param);
}

/**
 * 第三方产品对接-已适配业务系统
 */
export function getAdaptedBizSysTypeDict() {
    return fetch().get(`${v1}/product/integration/eccom/bizSysTypes`);
}

// -------------------------------------------------------  产品服务器 ------------------------------------------------------

/**
 * 获取产品服务器列表
 */
export function getProductHosts(param) {
    return fetch().get(`${v1}/product/hosts?${objectToQueryString(param)}`);
}

/**
 * 产品服务器列表修改保存
 */
export function setProductHosts(param) {
    return fetch().post(`${v1}/product/hosts`, param);
}

/**
 * 测试产品服务器连接
 */
export function testHostsConnection(param) {
    return fetch().post(`${v1}/product/host/test-connections`, param);
}

// -----------------------------------------------------  产品服务 ------------------------------------------------------

/**
 * 获取产品服务列表
 */
export function getBusinessServices(param) {
    return fetch().get(`${v1}/product/services?${objectToQueryString(param)}`);
}
/**
 * 获取数据分片规则列表
 */
export function getServiceDataSharding(param) {
    return fetch().get(`${v1}/product/data/sharing-rules?${objectToQueryString(param)}`);
}

/**
 * 获取平台管理功能列表
 */
export function getLdpManageApis(param) {
    return fetch().get(`${v1}/ldp/manage-apis/meta?${objectToQueryString(param)}`);
}

/**
 * 获取LDP管理功能元信息
 */
export function getManageMeta(param) {
    return fetch().get(`${v1}/ldp/manage-api/meta?${objectToQueryString(param)}`);
}

/**
 * 管理功能adoc文件上传
 */
export function manageAdocFileUpload(param) {
    return fetch({ contentType: 'multipart/form-data' }).post(`${v1}/ldp/manage-api/adoc-file/upload`, param);
}

/**
 * 获取业务管理号列表
 */
export function getBusFunctionNumber(param) {
    return fetch().get(`${v1}/product/function-numbers?${objectToQueryString(param)}`);
}
/**
 * 获取数据管理列表
 */
export function getServiceDataManagers(param) {
    return fetch().get(`${v1}/product/data/managers?${objectToQueryString(param)}`);
}

/**
 * 同步
 */
export function syncDataConfig(param) {
    return fetch().post(`${v1}/product/data/sync`, param);
}

/**
 * 获取元信息列表
 */
export function getTablesMetaInfo(param) {
    return fetch().get(`${v1}/product/tables/meta-info?${objectToQueryString(param)}`);
}

/**
 * 保存功能配置
 */
export function saveFuncNoConfig(param) {
    return fetch().post(`${v1}/product/function/config`, param);
}
/**
 * 保存服务规则配置
 */
export function saveServiceRules(param) {
    return fetch().post(`${v1}/product/services`, param);
}

//  ------------------------   链路日志配置   --------------------------------------

/**
 * 获取链路日志配置列表
 */
export function queryLogSource(param) {
    return fetch().get(`${v1}/product/latency/trace-log/config?${objectToQueryString(param)}`);
}

/**
 * 同步链路日志配置
 */
export function syncLogSource(param) {
    return fetch().post(`${v1}/product/latency/trace-log/config/sync`, param);
}

/**
 * 添加链路日志配置
 */
export function addLogSource(param) {
    return fetch().post(`${v1}/product/latency/trace-log/config`, param);
}

/**
 * 删除链路日志配置
 */
export function delLogSource(param) {
    return fetch().post(`${v1}/product/latency/trace-log/config/delete`, param);
}

/**
 * 获取链路日志配置的应用类型
 */
export function querytraceModelInsType(param) {
    return fetch().get(`${v1}/product/latency/trace-model/instance-type?${objectToQueryString(param)}`);

}

