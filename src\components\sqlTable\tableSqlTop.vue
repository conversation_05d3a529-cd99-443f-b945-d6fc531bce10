<template>
    <div class="title">
        <div class="drop-menu">
            <div v-show="type" class="apm-drop-menu-select-type">{{typeDictionary[type]}}</div>
            <apm-drop-menu-select
                v-if="showSelectNode"
                ref="apm-drop-menu-select"
                :disabled="disabled || !isLogin"
                :placeholder="!isCores ? '请选择节点或集群或服务' : '请选择节点'"
                innerPlaceholder="输入节点名查询"
                :menuList="dropMenuList"
                menuMode="text"
                :clearable="true"
                :groupCanClick="!isCores ? true : false"
                :subCanClick="!isCores ? true : false"
                :showCheckBox="isCores ? true : false"
                :showGroupRadioBox="isCores ? false : true"
                :showGroupCheckBox="isCores ? true : false"
                :isMultiple="isCores ? true : false"
                :collapseTags="isCores ? true : false"
                :collapseTagsNumber="isCores ? 2 : 1"
                @confirm="handleDatabaseChange"
            />
        </div>
        <a-title :title="title || (isCores ? 'MDB-SQL-多核心分发':'MDB-SQL')">
            <slot>
                <div class="title-select">
                    <h-icon v-if="showConfig" class="icon-setting" name="setup" size="20" color="var(--font-color)"
                      @on-click="settingBtnClick"></h-icon>
                    <!--分隔符-->
                    <div v-if="showConfig" class="input-line"></div>
                    <h-select
                        v-show="productList.length && productList.length > 1"
                        v-model="productInstNo"
                        widthAdaption
                        placeholder="请选择"
                        class="select1"
                        :positionFixed="true"
                        :clearable="false"
                        :disabled="disabled"
                        @on-change="handleChangeProduct">
                        <h-option
                            v-for="item in productList"
                            :key="item.id"
                            :value="item.productInstNo">
                            {{ item.productName }}
                        </h-option>
                    </h-select>
                    <h-button
                        v-if="!isLogin"
                        type="text"
                        size="small"
                        style="color: #fff;"
                        @click="(e)=>setLoginInfo()">登录</h-button>
                    <h-dropdown
                        v-if="isLogin"
                        trigger="click"
                        :transfer="true"
                        placement="bottom-end"
                        @on-click="handleDropdownSelect">
                        <h-button
                            type="text"
                            size="small"
                            style="margin-top: -10px;">
                            <h-icon
                                name="mine"
                                size="24"
                                class="mine-icon"></h-icon>
                        </h-button>
                        <h-dropdown-menu slot="list" style="max-height: 200px; overflow: auto;" >
                            <h-dropdown-item key="user" name="user" disabled style="border-bottom: 1px solid #444a60;">
                                <h-icon
                                        name="mine"
                                        size="22"
                                    >
                                </h-icon>
                                <div class="dropdown-item-text" :title="mdbUser">
                                    {{ mdbUser }}
                                </div>
                            </h-dropdown-item>
                            <h-dropdown-item key="viewIdPassWord" name="viewIdPassWord">
                                <div class="dropdown-item-text">查看密钥</div>
                            </h-dropdown-item>
                            <h-dropdown-item key="setPassWord" name="setPassWord" style="border-bottom: 1px solid #444a60;">
                                <div class="dropdown-item-text">修改密码</div>
                            </h-dropdown-item>
                            <h-dropdown-item
                                key="logout"
                                name="logout">
                                <div class="dropdown-item-text">退出登录</div>
                            </h-dropdown-item>
                        </h-dropdown-menu>
                    </h-dropdown>
                </div>
            </slot>
        </a-title>
        <setting-modal
            v-if="settingBtnInfo.status"
            :modalInfo="settingBtnInfo"
            :isCores="isCores"
            @endpointId-change="handleEndpointIdChange"
        ></setting-modal>
        <!-- 修改密码 -->
        <edit-password-modal
            v-if="editPasswordInfo.status"
            :modalInfo="editPasswordInfo"
            @update-passwd="updatePassword"></edit-password-modal>
        <!-- 登录页面 -->
        <mdp-login-modal
            v-if="loginInfo.status"
            :modalInfo="loginInfo"
            @login="handleLogin"></mdp-login-modal>
        <!-- 查看ID及密码 -->
         <view-id-password-modal
            v-if="viewIdPasswordInfo.status"
            :modalInfo="viewIdPasswordInfo"></view-id-password-modal>
    </div>
</template>

<script>
const { SM3 } = require('gm-crypto');
import _ from 'lodash';
import { mapState, mapActions } from 'vuex';
import aTitle from '@/components/common/title/aTitle';
import { getMonitorHeartbeats, getClusterRoles } from '@/api/httpApi';
import { getMdbEndpoints, getMemoryDatabaseList } from '@/api/memoryApi';
import { getMdbUsers, getMdbPerAccess } from '@/api/mdbPrivilegeApi';
import { MDB_NO_LOGIN } from '@/config/errorCode';
import mdpLoginModal from '@/components/mdbPrivilegeManage/modal/mdpLoginModal';
import editPasswordModal from '@/components/mdbPrivilegeManage/modal/editPasswordModal';
import viewIdPasswordModal from '@/components/mdbPrivilegeManage/modal/viewIdPasswordModal';
import settingModal from '@/components/sqlTable/modal/settingModal';
// 左上侧菜单
import apmDropMenuSelect from '@/components/common/apmDropMenuSelect/apmMdbSqlDropMenu';
import { getMachineRoomInfo } from '@/api/productApi';
import {  setMenuList } from '@/utils/roomSeviceClusterNode';
import {  getObjByArray } from '@/utils/utils';

export default {
    props: {
        isCores: {
            type: Boolean,
            default: false
        },
        disabled: {
            type: Boolean,
            default: false
        },
        /**
         * 自定义标题
         */
        title: {
            type: String,
            default: null
        },
        /**
         * 是否显示 选择节点、集群、服务
         */
        showSelectNode: {
            type: Boolean,
            default: true
        },
        /**
         * 是否显示自定义配置
         */
        showConfig: {
            type: Boolean,
            default: true
        }
    },
    components: { apmDropMenuSelect, aTitle, mdpLoginModal, editPasswordModal, settingModal, viewIdPasswordModal },
    data() {
        return {
            settingBtnInfo: {
                status: false
            },
            endpointId: '',
            productInfo: {},
            productInstNo: '',
            endpointList: [],
            isChange: false,
            // 登录
            mdbUser: '',
            loginInfo: {
                status: false
            },
            // 修改密码
            editPasswordInfo: {
                status: false
            },
            // 查看ID及密码
            viewIdPasswordInfo: {
                status: false
            },
            isLogin: false,
            // 核心选择
            clusterRoles: {}, // 集群角色
            dropMenuList: [], // 分类数据
            instanceList: [],
            // 类型选择
            type: '',
            typeDictionary: {
                service: '服务',
                cluster: '集群',
                instance: '节点'
            }
        };
    },
    computed: {
        ...mapState({
            productList: state => {
                return state.product.productListLight;
            }
        })
    },
    methods: {
        ...mapActions({
            getProductList: 'product/getProductListLight',
            mdbLogin: 'token/mdbLogin',
            mdbLoginout: 'token/mdbLoginout',
            mdbUpdatePassword: 'token/mdbUpdatePassword',
            updateMdbToken: 'token/updateMdbToken'
        }),
        // <---------------------------------------------------------------------- 登录 登出  修改密码------------------------------------------------------------------------------------->
        // 设置用户登录
        setLoginInfo(mdbUser){
            this.isLogin = false;
            this.clearData();
            this.loginInfo.status = true;
            this.loginInfo.productInstNo = this.productInstNo || localStorage.getItem('productInstNo') || this.productList?.[0]?.productInstNo || '';
            this.loginInfo.endpointId = sessionStorage.getItem('apm.mdbsql.endpointId')?.id || this.endpointList?.[0]?.id || '';
            this.loginInfo.user = mdbUser || '';
        },
        // 自动续约Token
        updateToken(token){
            token && this.updateMdbToken(token);
        },
        // 获取登录用户名
        async getMdUserName(){
            this.isLogin = true;
            const id = localStorage.getItem('mdbUserId');
            this.mdbUser = await this.getMdbUsers(id);
            if (!this.mdbUser){
                this.setLoginInfo();
                return;
            }
        },
        // 登录
        async handleLogin(val){
            const param = {
                productId: val.productInstNo,
                userName: val.user,
                password: SM3.digest(val.password, 'utf8', 'hex')
            };
            try {
                const res = await this.mdbLogin(param);
                if (res){
                    this.isLogin = true;
                    this.loginInfo.status = false;
                    this.$hMessage.success('登录成功!');
                    this.mdbUser = val.user || '未知用户';
                    sessionStorage.setItem('apm.mdbsql.endpointId', val?.endpointId);
                    await this.init(val.productInstNo);
                }
            } catch (error) {
                console.error(error);
            }
        },
        // 修改密码  退出登录
        async handleDropdownSelect(name){
            if (this.disabled) return;
            if (name === 'setPassWord'){
                this.editPasswordInfo = {
                    status: true
                };
                return;
            }

            if (name === 'viewIdPassWord'){
                const id = localStorage.getItem('mdbUserId');
                const res = await getMdbPerAccess({ userId: id });
                if (res?.code === '200'){
                    this.viewIdPasswordInfo.status = true;
                    this.viewIdPasswordInfo.id = res?.data?.userId;
                    this.viewIdPasswordInfo.password = res?.data?.pwd;
                } else {
                    this.$hMessage.error(res?.message);
                }
                return;
            }

            if (name === 'logout'){
                const res = await this.mdbLoginout();
                if (res){
                    this.mdbUser && this.$hMessage.success('请重新登录!');
                    this.mdbUser = '';
                    this.setLoginInfo();
                }
            }
        },
        // 修改密码
        async updatePassword(val){
            const param = {
                oldPassword: SM3.digest(val.curPasswd, 'utf8', 'hex'),
                newPassword: SM3.digest(val.passwd, 'utf8', 'hex'),
                id: localStorage.getItem('mdbUserId') || ''
            };
            const res = await this.mdbUpdatePassword(param);
            if (res){
                this.$hMessage.success('修改密码成功!请重新登录!');
                this.setLoginInfo(this.mdbUser);
                this.mdbUser = '';
            }
        },
        // 获取所有的用户信息
        async getMdbUsers(id){
            try {
                let mdbUser = '';
                const param = {
                    productId: this.productInstNo,
                    useName: ''
                };
                const res = await getMdbUsers(param);
                if (res?.code === '200' && Array.isArray(res?.data)) {
                    mdbUser = _.find(res?.data || [], o => { return o?.id === id; })?.userName || '';
                }
                return mdbUser;
            } catch (error) {
                console.error('getMdbUsers error:', error);
                return '';
            }
        },
        // <--------------------------------------------------------------------------------------- sql界面 -------------------------------------------------------------------------------->
        async init(id) {
            try {
                this.isLogin = false;
                // 查询产品节点
                await this.getProductList({ filter: 'excludeLdpApm' });
                this.productInstNo = id || localStorage.getItem('productInstNo');
                this.isChange = true;
                this.handleChangeProduct(this.productInstNo);
                await this.getMdbEndpoints();
                this.isChange = false;
            } catch (error) {
                console.error(error);
            }
        },
        // 清理数据
        clearData() {
            this.type = '';
            this.endpointList = [];
            this.dropMenuList = [];
            this.clusterRoles = {};
            this.instanceList = [];
            this.$refs['apm-drop-menu-select'] && this.$refs['apm-drop-menu-select'].clearSelectedMenu();
            this.$emit('clear-data');
        },
        // 切换产品节点----用户登录
        async handleChangeProduct(val) {
            this.isLogin = false;
            this.clearData();
            if (!this.productList?.length) return;
            this.productInfo = _.find(this.productList, ['productInstNo', val]) || this.productList[0];
            this.productInstNo = this.productInfo.productInstNo;
            localStorage.setItem('productInstNo', this.productInstNo);
            !this.isChange && await this.handleDropdownSelect('logout');
        },
        // 获取接入点信息
        async getMdbEndpoints(){
            try {
                // 获取接入点信息
                const res = await getMdbEndpoints({
                    productId: this.productInstNo,
                    protocol: 'T2'
                });
                if (res.success) {
                    this.endpointList = res.data || [];
                }
            } catch (error) {
                console.error(error);
            }
            this.endpointId = _.find(this.endpointList, ['id', sessionStorage.getItem('apm.mdbsql.endpointId')])?.id || this.endpointList?.[0]?.id || '';
            sessionStorage.setItem('apm.mdbsql.endpointId',  this.endpointId);

            // 当接入点值不变化是不会触发change事件-需手动调用
            await this.handleChangeBizType(this.endpointId);
        },
        // 切换接入点
        async handleEndpointIdChange(val){
            if (this.isChange || !this.isLogin) return;
            this.endpointId = val;
            sessionStorage.setItem('apm.mdbsql.endpointId', val);
            await this.handleChangeBizType(val);
        },
        // 获取接入点下应用信息
        async handleChangeBizType(_val) {
            try {
                const roomTableData = await this.getMachineRoomInfo();
                this.clusterRoles =  await this.getClusterRoles();
                const heartList = await this.getMonitorHeartbeats();
                this.instanceList = await this.getMemoryDatabaseList(heartList);
                if (!this.isLogin) return;
                // 多核心集群给定唯一key否则存在交互问题  单核心选择时集群支持独立选择key支持重复
                this.dropMenuList = setMenuList(this.instanceList, roomTableData, this.clusterRoles, this.$store?.state?.apmDirDesc?.appTypeDictDesc, !this.isCores, !this.isCores);
                const node =  _.find(this.instanceList, ['instanceId', this.$route.query.instanceId]) || this.dropMenuList?.[0]?.groupList?.[0]?.subGroupList?.[0]?.nodes?.[0];
                if (!node) {
                    // 配置面板中，上一个接入点存在数据，切换接入点突然无数据需要手动清空下
                    this.type = '';
                    this.$refs['apm-drop-menu-select'].clearSelectedMenu();
                    this.$emit('clear-data');
                    return;
                };
                this.$nextTick(() => {
                    this.$emit('loginSuccess');
                    this.$refs?.['apm-drop-menu-select'] && this.$refs['apm-drop-menu-select'].setSelectMenu([{
                        label: node?.instanceNo || node?.label,
                        value: node.instanceId || node?.value
                    }]);
                });
            } catch (error){
                console.error(error);
            }
        },
        determineType(type) {
            switch (type) {
                case 'group':
                    return 'service'; // 服务
                case 'subGroup':
                    return 'cluster'; // 集群
                default:
                    return 'instance'; // 节点
            }
        },
        // 获取服务下所有集群下的第一个备节点、主节点
        getServiceNode(node) {
            const nodeArr = [];
            // eslint-disable-next-line no-labels
            outerLoop:
            for (const menu of this.dropMenuList) {
                const group = menu?.groupList?.find(group => node?.[0]?.value === group?.key);
                if (group) {
                    for (const subGroup of (group?.subGroupList || [])) {
                        const stand = subGroup?.nodes?.filter(o => !o?.badge)?.[0]; // 备节点
                        const main = subGroup?.nodes?.filter(o => o?.badge)?.[0]; // 主节点

                        // 保留层级关系信息
                        if (stand) {
                            nodeArr.push({
                                ...stand,
                                roomId: menu.menuId,
                                roomName: menu.menuName,
                                serviceKey: group.key,
                                serviceName: group.label,
                                clusterKey: subGroup.key,
                                clusterName: subGroup.label
                            });
                        }
                        if (main) {
                            nodeArr.push({
                                ...main,
                                roomId: menu.menuId,
                                roomName: menu.menuName,
                                serviceKey: group.key,
                                serviceName: group.label,
                                clusterKey: subGroup.key,
                                clusterName: subGroup.label
                            });
                        }
                    }
                    // eslint-disable-next-line no-labels
                    break outerLoop;
                }
            }
            return nodeArr;
        },
        // 获取集群下的第一个备节点、主节点
        getClusterNode(node) {
            const nodeArr = [];
            // eslint-disable-next-line no-labels
            outerLoop:
            for (const menu of this.dropMenuList) {
                for (const group of (menu?.groupList || [])) {
                    const subGroup = group?.subGroupList?.find(subGroup => node?.[0]?.value === subGroup?.key);
                    if (subGroup) {
                        const stand = subGroup?.nodes?.filter(o => !o?.badge)?.[0]; // 备节点
                        const main = subGroup?.nodes?.filter(o => o?.badge)?.[0]; // 主节点

                        // 保留层级关系信息
                        if (stand) {
                            nodeArr.push({
                                ...stand,
                                serviceKey: group.key,
                                serviceName: group.label,
                                clusterKey: subGroup.key,
                                clusterName: subGroup.label
                            });
                        }
                        if (main) {
                            nodeArr.push({
                                ...main,
                                serviceKey: group.key,
                                serviceName: group.label,
                                clusterKey: subGroup.key,
                                clusterName: subGroup.label
                            });
                        }
                        // eslint-disable-next-line no-labels
                        break outerLoop;
                    }
                }
            }
            return nodeArr;
        },
        // 获取节点的层级关系信息
        getInstanceNode(node) {
            const nodeArr = [];
            // eslint-disable-next-line no-labels
            outerLoop:
            for (const menu of this.dropMenuList) {
                for (const group of (menu?.groupList || [])) {
                    for (const subGroup of (group?.subGroupList || [])) {
                        const instance = subGroup?.nodes?.find(instance => node?.[0]?.value === instance?.value);
                        if (instance) {
                            // 保留层级关系信息
                            nodeArr.push({
                                ...instance,
                                serviceKey: group.key,
                                serviceName: group.label,
                                clusterKey: subGroup.key,
                                clusterName: subGroup.label
                            });
                            // eslint-disable-next-line no-labels
                            break outerLoop;
                        }
                    }
                }
            }
            return nodeArr;
        },
        // 切换应用节点
        async handleDatabaseChange(node) {
            let param = {};
            this.type = !node.length ? '' : this.determineType(node[0]?.type);
            // 多核心
            if (this.isCores){
                param = {
                    productInstNo: this.productInstNo,
                    endpointId: this.endpointId,
                    instances: node
                };
            } else {
                let selectNode = [];
                if (this.type === 'service') {
                    // 服务：选择当前服务下所有集群中的第一个备节点，第一个主节点
                    const serviceNode = this.getServiceNode(node);
                    if (serviceNode) {
                        selectNode.push(...serviceNode);
                    }
                } else if (this.type === 'cluster'){
                    // 集群：默认选择一个备节点，第一个主节点
                    const clusterNode = this.getClusterNode(node);
                    if (clusterNode) {
                        selectNode.push(...clusterNode);
                    }
                } else {
                    // 节点：获取节点的层级关系信息
                    const instanceNode = this.getInstanceNode(node);
                    if (instanceNode) {
                        selectNode.push(...instanceNode);
                    } else {
                        selectNode = node;
                    }

                }
                param = {
                    type: this.type, // 所选分类
                    instance: selectNode,
                    productInstNo: this.productInstNo,
                    endpointId: this.endpointId
                };
            }
            this.$emit('connect-database', param);
        },
        // 获取节点列表
        $_getDropMenuList() {
            return this.dropMenuList;
        },
        // 获取当前类型
        $_getType() {
            return this.type;
        },
        // 获取产品行信息
        $_getProductInfo(){
            const productName = _.find(this.productList, ['productInstNo', this.productInstNo])?.productName;
            return {
                productName: productName,
                productInstNo: this.productInstNo
            };
        },
        // 获取产品下内存表节点信息
        async getMemoryDatabaseList(heartList) {
            const res = await getMemoryDatabaseList({
                productInstNo: this.productInstNo,
                endpointId: this.endpointId
            });
            let list = [];
            if (res.code === MDB_NO_LOGIN){
                this.setLoginInfo();
                return [];
            }
            if (res?.databaseNames) {
                list =  (Object.values(res?.databaseNames || {})?.flat() || []).filter(item => { return _.find(heartList, ['id', item.instanceId])?.status !== 'stop'; });
            }
            await this.getMdUserName();
            this.updateToken(res?.headers?.authorization);
            return list;
        },
        // 获取心跳数据
        async getMonitorHeartbeats() {
            let heartList = [];
            const param = {
                productId: this.productInstNo,
                type: 'instance'
            };
            const res = await getMonitorHeartbeats(param);
            if (res?.code === '200') {
                heartList = res?.data || [];
            }
            return heartList;
        },
        // 获取机房信息
        async getMachineRoomInfo() {
            let roomTableData = [];
            const res = await getMachineRoomInfo({ productId: this.productInstNo });
            if (res?.code === '200') {
                roomTableData = res?.data || [];
            }
            return roomTableData;
        },
        // 获取集群角色
        async getClusterRoles() {
            let clusterRoles = {};
            const param = {
                productId: this.productInstNo
            };
            const res = await getClusterRoles(param);
            if (res?.code === '200'  && Array.isArray(res.data)) {
                clusterRoles = getObjByArray(res.data, 'instanceId');
            }
            return clusterRoles;
        },
        // ------------------------  设置按钮 ----------------------------------------
        settingBtnClick(){
            if (this.disabled) return;
            this.settingBtnInfo.status = true;
            this.settingBtnInfo.endpointList = [...this.endpointList];
            this.settingBtnInfo.endpointId = this.endpointId;
        },
        /**
         * 获取mbUser
         */
        $_getMdbUser() {
            return this.mdbUser;
        }
    }
};
</script>

<style lang="less" scoped>

.title {
    min-width: 900px;
    position: relative;

    .drop-menu {
        position: absolute;
        top: -4px;
        right: 0;
        bottom: 0;
        left: 0;
        margin: auto;
        display: flex;
        align-items: center;
        justify-content: center;

        .apm-drop-menu-select-type {
            width: 42px;
            color: #fff;
            padding: 5px 8px;
            z-index: 1;
            background: #1f3759;
            border-radius: 4px;
        }

        /deep/ .apm-drop-menu-select .apm-drop-menu-select-wrap {
            position: absolute;
            z-index: 100;
            bottom: 0;
            left: 50%;
            transform: translateX(-50%);
        }
    }
}

.title-select {
    display: flex;
    float: right;
    margin-top: 5px;

    .icon-setting {
        position: relative;
        border: var(--border);
        border-radius: 4px;
        padding: 6px;
        color: var(--font-color);
        font-size: 20px;
        height: 32px;
        line-height: 18px;
        cursor: pointer;
    }

    .h-select {
        width: auto;
        min-width: 200px;
    }

    & > .input-line {
        display: inline-block;
        width: 1px;
        height: 24px;
        margin: 4px 10px 0;
        text-align: center;
        background: var(--base-color);
    }
}

.mine-icon {
    color: var(--font-color);

    &:hover {
        color: var(--link-color);
    }
}

.h-dropdown-item {
    color: var(--font-color);
}

.h-dropdown-item-disabled {
    cursor: not-allowed;
}

.h-dropdown-item-disabled:hover {
    background: #262d43;
}

.h-dropdown-menu {
    padding: 0;
    background: #262d43;
    border: 1px solid #485565;
    box-shadow: 0 2px 6px 0 rgba(0, 0, 0, 0.1);
    border-radius: 4px;
}

.h-dropdown-item:hover {
    background: #1f3759;
}

.dropdown-item-text {
    width: 120px;
    display: inline-block;
    margin-right: 10px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}
</style>
