<!--
 * @Description:
 * @Author: <PERSON><PERSON>
 * @Date: 2022-11-16 19:54:28
 * @LastEditTime: 2023-03-20 19:03:04
 * @LastEditors: <PERSON><PERSON>
-->
<template>
    <div>
        <h-msg-box-safe v-model="modalData.status" :escClose="false" :mask-closable="false" width="600" title="创建报表">
            <div v-if="instanceId">
                <h3>报表"{{instanceName}}"创建成功!</h3>
            </div>
            <div v-else>
                <p>产品节点：<span>{{ reportInfo.productName }}</span></p>
                <p>交易所：<span>{{getName(modalInfo.formItem.exchangeId)}}</span></p>
                <p v-if="modalInfo.formItem.seat">席位号：<span>{{modalInfo.formItem.seat}}</span></p>
                <p v-if="modalInfo.formItem.accountId">资金账号：<span>{{modalInfo.formItem.accountId}}</span></p>
                <p>开始时间：<span>{{ startTime }}</span></p>
                <p>结束时间：<span>{{ endTime }}</span></p>
            </div>
            <template v-slot:footer>
                <a-button type="ghost" @click="cancel">取消</a-button>
                <a-button :loading="loading" type="primary" style="margin-left: 8px;" @click="submitForm">{{instanceId ? '查看' :
                    '创建'}}</a-button>
            </template>
        </h-msg-box-safe>
    </div>
</template>

<script>
import { createAnalyseReport } from '@/api/httpApi';
import { formatDate, getExchangeName } from '@/utils/utils';
import aButton from '@/components/common/button/aButton';
export default {
    props: {
        reportInfo: {
            type: Object,
            default: null
        },
        modalInfo: {
            type: Object,
            default: null
        }
    },
    data() {
        return {
            formatDate: formatDate,
            getName: getExchangeName,
            modalData: this.reportInfo,
            loading: false,
            instanceName: '',
            instanceId: ''
        };
    },
    computed: {
        startTime: function() {
            return this.reportInfo.sTime;
        },
        endTime: function() {
            return this.reportInfo.eTime;
        }
    },
    methods: {
        submitForm() {
            if (this.instanceId) {
                this.goLink(this.instanceId);
            } else {
                this.createAnalyseReport();
            }
        },
        cancel() {
            this.reportInfo.status = false;
        },
        // 跳转测试分析页面
        goLink(id) {
            this.$hCore.navigate(`/analyseData`, {
                id
            });
        },
        // 创建分析报表
        async createAnalyseReport() {
            this.loading = true;
            const param = {
                accountId: this.modalInfo.formItem.accountId,
                bizSysType: this.reportInfo.bizSysType,
                startTime: this.startTime,
                endTime: this.endTime,
                exchangeId: this.modalInfo.formItem.exchangeId,
                span: this.modalInfo.formItem.span,
                seat: this.modalInfo.formItem.seat,
                productInstNo: this.modalInfo.formItem.productInstNo,
                interval: 1000,
                loopType: 'RtnCfmXchg',
                testCaseName: '自由分析',
                sceneName: '应用时延分析',
                indicators: ['avg', 'max', 'min', 'stdDeviation'],
                percentiles: [50, 90, 95, 99]
            };
            const res = await createAnalyseReport(param);
            if (res.success) {
                this.instanceId = res.data?.id;
                this.instanceName = res.data?.instanceName;
            } else this.$hMessage.error('报表创建失败!');
            this.loading = false;
        }
    },
    components: {  aButton }
};
</script>

<style lang="less" scoped>
/deep/ .h-modal-body {
    padding: 16px;

    div > h3 {
        padding-left: 20px;
        color: #666;
    }

    div > p {
        color: #333;
        padding: 0 0 8px 20px;

        & > span {
            color: #777;
            padding-left: 10px;
        }
    }
}

/deep/ .h-modal-confirm-head-icon {
    display: none;
}
</style>
