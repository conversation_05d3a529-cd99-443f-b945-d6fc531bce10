<template>
    <div class="perform-box">
        <!-- 指标总览 -->
        <info-sum-bar
            :data="performOverview"
            :selectInfoId="selectInfoId"
            selectedStyleType="border"
            @info-bar-click="handleBarClick">
        </info-sum-bar>

        <!-- 侧边弹窗 -->
        <data-accord-drawer
            v-if="drawerInfo.status"
            ref="accord-drawer"
            :modalInfo="drawerInfo"
            @drawer-close="handleDrawerClose"
        ></data-accord-drawer>
    </div>
</template>

<script>
import infoSumBar from '@/components/common/infoBar/infoSumBar';
import _ from 'lodash';
import dataAccordDrawer from './dataAccordDrawer.vue';
export default {
    props: {
        productInstNo: {
            type: String,
            default: ''
        },
        curTab: {
            type: String,
            default: ''
        },
        indicators: {
            type: Array,
            default: () => []
        }
    },
    components: {
        infoSumBar,
        dataAccordDrawer
    },
    data() {
        return {
            performOverview: {
                direction: 'grid',
                autoGrid: true,
                gridMinWidth: '450px',
                details: []
            },
            selectInfoId: '',
            // 指标值
            indicatorsData: [
                {
                    label: '主核心事务数',
                    key: 'masterCommitSqn'
                },
                {
                    label: '备核心事务最大差量',
                    key: 'standbyMaxCommitSqnDiff'
                },
                {
                    label: '回库事务最大差量',
                    key: 'todbMaxCommitSqnDiff'
                }
            ],

            // ------------------------- 弹窗数据 -------------------------
            drawerInfo: {
                status: false
            }
        };
    },
    mounted() {
        window.addEventListener('resize', this.resize);
    },
    beforeDestroy() {
        window.removeEventListener('resize', this.resize);
    },
    methods: {
        // 初始化
        init() {
            this.cleanData();
            this.getPerformanceData();
        },
        // 手动清理页面数据
        cleanData(){
            this.selectInfoId = '';
            this.performOverview.details = [];
        },
        // 点击事件
        async handleBarClick(id) {
            this.selectInfoId = id;
            const [selectInsId, dataType] = id?.split('#');
            // 弹窗数据
            const details = _.find(this.performOverview.details, ['infoId', selectInsId]) || {};
            this.handleDrawerOpen(details?.instances, dataType, details?.shardingNo);
        },
        // 构建info组件数据
        setIndicatorsInfoData(indicators){
            const infoList = [];
            for (const indicator of Object.values(indicators)){
                infoList.push({
                    type: 'monitor',
                    title: {
                        label: `分片 ${indicator.shardingNo || '-'}`,
                        noTagColor: true,
                        slots: [
                            {
                                type: 'text', // 文本
                                value: indicator.clusterName || '-'
                            }
                        ]
                    },
                    canClick: false,
                    infoId: indicator.clusterId,
                    info: this.setIndicators(indicator),
                    shardingNo: indicator?.shardingNo,
                    instances: indicator?.instances || [] // 弹窗数据
                });
            }
            return infoList;
        },
        // 指定指标
        setIndicators(indicator){
            const info = [];
            Object.values(this.indicatorsData).forEach(data => {
                info.push(
                    {
                        type: 'text',
                        label: data.label,
                        canClick: true,
                        infoId: `${indicator.clusterId}#${data.key}`,
                        key: data.key,
                        value: indicator[data?.key]
                    }
                );
            });
            return info;
        },
        // 展示指标数据以及指标图表
        async getPerformanceData() {
            // 指标数据按照分片过滤
            let indicators = [];
            indicators = this.indicators?.filter(o =>
                o.serviceCode === this.curTab
            );
            this.performOverview.details = this.setIndicatorsInfoData(indicators);
            if (this.performOverview.details.length) {
                // 获取选中模块Id
                const selectInsId = this.selectInfoId?.split('#')?.[0];
                const selectIns = _.find(this.performOverview.details, ['infoId', selectInsId]) || {};
                const id = _.find(selectIns?.info, ['infoId', this.selectInfoId])?.infoId;
                id && this.updateDrawerData(id);
            }
        },

        // ----------------------------------------DrawerBox-------------------------------------------------------------
        // 查看侧弹窗信息
        handleDrawerOpen(data, dataType, shardingNo) {
            this.drawerInfo.status = true;
            this.drawerInfo.insData = data;
            this.drawerInfo.dataType = dataType;
            this.drawerInfo.shardingNo = shardingNo;
        },
        // 轮询设置弹窗数据
        updateDrawerData(id) {
            if (this.selectInfoId !== id) return;
            const [selectInsId, dataType] = id?.split('#');
            const instances = _.find(this.performOverview.details, ['infoId', selectInsId])?.instances || [];
            this.drawerInfo.insData = instances;
            this.drawerInfo.dataType = dataType;
            if (this.$refs['accord-drawer']) {
                this.$refs['accord-drawer'].handleDataUpdate();
            }
        },
        // 关闭弹窗
        handleDrawerClose() {
            this.selectInfoId = '';
        }
    }
};
</script>
  <style lang="less" scoped>
.perform-box {
    width: 100%;
    height: 100%;
    overflow: auto;

    .info-sum-bar {
        margin-top: 0;
        background: none;

        /deep/ .info-bar {
            &:hover {
                box-shadow: 0 2px 6px rgba(0, 0, 0, 0.5);
                cursor: default;
            }
        }
    }

    /deep/ .obs-title {
        background: var(--primary-color);
    }
}

  </style>

