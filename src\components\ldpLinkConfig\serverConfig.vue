<template>
    <div class="tab-box">
        <div ref="table-box" class="table-box">
        <obs-table
            :title="tableTitle"
            :tableData="tableData"
            :columns="columns"
            :maxHeight="tableHeight"
            @button-click="handleButtonClick"
            @selection="handleSelectionChange" />
        </div>
        <a-loading v-if="loading"></a-loading>

        <!-- SSH配置 -->
        <server-ssh-config-modal
            v-if="sshInfo.status"
            :productId="productInfo.id"
            :modalInfo="sshInfo"
            @update="initData"/>

        <!-- 连通性测试 -->
        <server-test-connect-modal
            v-if="connectInfo.status"
            :productId="productInfo.id"
            :modalInfo="connectInfo">
        </server-test-connect-modal>

        <!-- 关联机房、修改主机名 -->
        <server-config-modal
            v-if="configInfo.status"
            :productId="productInfo.id"
            :modalInfo="configInfo"
            :configMode="configMode"
            @update="initData">
        </server-config-modal>
    </div>
</template>

<script>
import { getProductHosts } from '@/api/productApi';
import obsTable from '@/components/common/obsTable/obsTable';
import aLoading from '@/components/common/loading/aLoading';
import serverSshConfigModal from '@/components/ldpLinkConfig/modal/serverSshConfigModal.vue';
import serverTestConnectModal from '@/components/ldpLinkConfig/modal/serverTestConnectModal.vue';
import serverConfigModal from '@/components/ldpLinkConfig/modal/serverConfigModal.vue';

export default {
    name: 'ServerList', // 组件名称
    components: { obsTable, aLoading, serverSshConfigModal, serverTestConnectModal, serverConfigModal },
    props: {
        productInfo: {
            type: Object,
            default: () => {}
        }
    },
    data() {
        return {
            tableHeight: 0, // 表格高度
            tableTitle: {
                label: `服务器：${(this.tableData || []).length}`, // 表格标题
                slots: [
                    {
                        type: 'button',
                        buttonType: 'dark',
                        key: 'testConnection',
                        value: '测试连通性' // 测试连通性按钮
                    },
                    {
                        type: 'button',
                        buttonType: 'dark',
                        key: 'sshConfig',
                        value: 'SSH配置' // SSH配置按钮
                    }
                ]
            },
            columns: [
                {
                    type: 'selection',
                    width: 60,
                    align: 'center'
                },
                {
                    title: '主机名',
                    key: 'hostName'
                },
                {
                    title: '主机别名',
                    key: 'hostNameAlias',
                    minWidth: 150,
                    render: (h, { row }) => {
                        return h('div', {
                            style: {
                                display: 'flex',
                                'align-items': 'center'
                            }
                        }, [
                            h('span', row.hostNameAlias || '-'),
                            h('h-icon', {
                                props: {
                                    name: 't-b-modify'
                                },
                                style: {
                                    color: '#2D8DE5',
                                    cursor: 'pointer',
                                    marginLeft: '8px'
                                },
                                on: {
                                    'on-click': () => this.handleServerConfigure('host', row)
                                }
                            })
                        ]);
                    }
                },
                {
                    title: 'IP地址/域名',
                    key: 'ips',
                    minWidth: 200,
                    render: (h, { row }) => {
                        const ips = row.ips ? row.ips?.join(',') : '-'; // 渲染IP地址
                        return h('div', ips);
                    }
                },
                {
                    title: 'SSH用户名',
                    key: 'loginUser',
                    render: (h, { row }) => {
                        const loginUser = row.loginUser || '-'; // 渲染SSH用户名
                        return h('div', loginUser);
                    }
                },
                {
                    title: '关联机房',
                    key: 'roomName', // 关联机房列
                    formatMethod: (row) => row.roomName || '-'
                },
                {
                    title: '操作',
                    key: 'action',
                    width: 150,
                    render: (h, params) => {
                        return h('div', [
                            h(
                                'Button',
                                {
                                    props: { type: 'text', size: 'small' },
                                    style: {
                                        padding: 0
                                    },
                                    on: {
                                        click: () => this.handleSshConfig('single', params.row) // SSH配置按钮的点击事件
                                    }
                                },
                                'SSH配置'
                            ),
                            h(
                                'Button',
                                {
                                    props: { type: 'text', size: 'small' },
                                    on: {
                                        click: () => this.handleServerConfigure('room', params.row) // 关联机房按钮的点击事件
                                    }
                                },
                                '关联机房'
                            )
                        ]);
                    }
                }
            ],
            tableData: [], // 表格数据
            selectedRows: [], // 选中行
            sshInfo: {
                status: false // SSH信息状态
            },
            connectInfo: {
                status: false // 连通性信息状态
            },
            configInfo: {
                status: false
            },
            configMode: '',
            loading: false // 加载状态
        };
    },
    mounted() {
        this.setTableHeight(); // 设置表格高度
        window.addEventListener('resize', this.setTableHeight); // 监听窗口大小变化事件
    },
    beforeDestroy() {
        window.removeEventListener('resize', this.setTableHeight); // 移除窗口大小变化事件监听
    },
    methods: {
        /**
         * 设置表格高度
         */
        setTableHeight() {
            this.tableHeight = this.$refs['table-box']?.offsetHeight - 52; // 动态设置表格高度
        },
        /**
         * 初始化数据
         */
        async initData() {
            this.clearData(); // 清除数据
            this.loading = true; // 设置加载状态为true
            try {
                this.tableData = await this.getProductHosts(); // 获取服务器信息
            } finally {
                this.tableTitle.label = `服务器：${(this.tableData || []).length}`; // 更新表格标题
                this.loading = false; // 设置加载状态为false
            }
        },

        /**
         * 清除表格数据
         */
        clearData() {
            this.selectedRows = []; // 清除选中行
            this.tableData = []; // 清除表格数据
        },

        /**
         * 处理表格行选择变化
         * @param {Array} selection - 选中的行数组
         */
        handleSelectionChange(selection) {
            this.selectedRows = selection; // 更新选中行数据
        },

        /**
         * 获取服务器信息
         * @returns {Array} tableData - 表格数据
         */
        async getProductHosts() {
            let tableData = [];
            try {
                const param = {
                    productId: this.productInfo.id
                };
                const res = await getProductHosts(param);
                if (res?.code === '200') {
                    tableData = res?.data || [];
                } else if (res.code?.length === 8) {
                    this.$hMessage.error(res.message);
                }
            } catch (err) {
                console.error(err);
            }

            return tableData;
        },

        /**
         * 处理按钮点击事件
         * @param {String} key - 按钮的key值
         */
        handleButtonClick(key) {
            if (!this.selectedRows?.length) {
                this.$hMessage.warning({
                    content: '请选择服务器', // 如果没有选中行则显示警告消息
                    duration: 3
                });
                return;
            }

            if (key === 'sshConfig') {
                // SSH批量配置
                this.handleSshConfig('batch');
            } else {
                // 测试连通性
                this.handleTestConnect();
            }
        },

        /**
         * SSH配置 (单条或批量)
         * @param {String} type - 配置类型（单条或批量）
         * @param {Object} row - 当前行的数据对象（单条配置时使用）
         */
        handleSshConfig(type, row) {
            if (type === 'single') {
                this.sshInfo = {
                    ...row,
                    type,
                    ids: [row.id],
                    status: true
                };
            } else {
                this.sshInfo = {
                    status: true,
                    type,
                    ids: this.selectedRows.map(item => item.id)
                };
            }
        },

        /**
         * 批量测试连通性
         */
        handleTestConnect() {
            this.connectInfo = {
                ids: this.selectedRows.map(item => item.id),
                status: true
            };
        },

        /**
         * 设置配置模式和信息
         * @param {string} mode - 配置模式 ('room' 或 'host')
         * @param {Object} row - 当前行的数据对象
         */
        handleServerConfigure(mode, row) {
            // 检查 mode 是否有效
            if (mode !== 'room' && mode !== 'host') {
                console.error('Invalid mode provided:', mode);
                return;
            }

            this.configMode = mode;
            this.configInfo = {
                ...row,
                status: true
            };
        }
    }
};
</script>

<style lang="less" scoped>
.tab-box {
    position: relative;
    width: 100%;
    height: calc(100% - 18px); // tab-box样式

    .table-box {
        height: 100%; // table-box样式
    }
}

</style>
