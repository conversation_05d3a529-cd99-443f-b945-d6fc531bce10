import { initFetch } from 'hui-core';
import MsgBox from 'h_ui/dist/lib/MsgBox';

// 自定义统一设置API前缀
function ldpFetchFunc (fetchConfig) {
    const { timeout, contentType, includeResponseHeaders, responseType, showErrorToast = true } = fetchConfig || {};
    return initFetch({
        defaultConfig: {
            headers: {
                'Content-Type': contentType || 'application/json',
                Authorization: localStorage.getItem('mdbToken') || ''
            },
            timeout: timeout || 30000,
            responseType: responseType || 'json',
            includeResponseHeaders: includeResponseHeaders || false
        },
        beforeRequestSendMiddlewares: [
            (config, next) => {
                return next();
            }
        ],
        afterRequestSendMiddlewares: [
            (err, res, next) => {
                if (err && showErrorToast) {
                    if (err.error_code === '-1' || err.error_code === '-2') {
                        MsgBox.error({
                            title: '网络请求超时',
                            content: ''
                        });
                    } else {
                        MsgBox.error({
                            title: err.error || err.error_code ||  '网络异常',
                            content: err.message || err.error_message
                        });
                    }
                    return next(); // try catch
                } else {
                    // 当状态存在且不等于200时调用
                    if (res?.code?.length === 8) return next();
                    if ((res?.errcode && res?.errcode !== 200) || (res?.code && res?.code !== '200')){
                        if (showErrorToast) {
                            MsgBox.error({
                                title: '服务异常',
                                content: res.message || res.errmsg
                            });
                        }
                    }
                    return next();
                }
            }
        ]
    });
};
export default ldpFetchFunc;
