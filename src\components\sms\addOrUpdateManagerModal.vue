<!--
 * @Description:
 * @Author: <PERSON><PERSON>
 * @Date: 2022-10-24 17:31:04
 * @LastEditTime: 2022-10-31 14:57:14
 * @LastEditors: <PERSON><PERSON>
-->
<template>
    <div>
        <!-- 下发配置列表 -->
        <h-msg-box v-model="modalData.status" :title="modalData.type ? '修改干系人信息' : '添加干系人信息'" :mask-closable="false" width="50" maxHeight="240" @on-open="getCollections">
            <h-form ref="formValidate" :model="formItem" :rules="ruleValidate" :label-width="60">
                <h-form-item label="干系人" prop="addresseeName" required>
                    <h-input v-model="formItem.addresseeName" placeholder="请输入...">
                    </h-input>
                </h-form-item>
                <h-form-item label="手机号" prop="mobile" :validRules="mobileRule" required>
                    <h-input v-model="formItem.mobile" placeholder="请输入...">
                    </h-input>
                </h-form-item>
                <h-form-item label="邮箱" prop="email">
                    <h-input v-model="formItem.email" placeholder="请输入...">
                    </h-input>
                </h-form-item>
            </h-form>
            <template v-slot:footer>
                <a-button @click="modalData.status = false">取消</a-button>
                <a-button type="primary" :loading="loading" @click="submitConfig">确认</a-button>
            </template>
        </h-msg-box>
    </div>
</template>

<script>
import { addAddressee, updateAddressee } from '@/api/httpApi';
import aButton from '@/components/common/button/aButton';
export default {
    props: {
        modalInfo: {
            type: Object,
            default: null
        }
    },
    data() {
        return {
            modalData: this.modalInfo,
            formItem: {
                id: '',
                addresseeName: '',
                mobile: '',
                email: ''
            },
            loading: false,
            mobileRule: [
                { test: /^1[3456789]\d{9}$/, message: '手机号格式不正确', trigger: 'blur' }
            ],
            ruleValidate: {
                email: [
                    { required: true, message: '邮箱不能为空', trigger: 'blur' },
                    { type: 'email', message: '邮箱格式不正确', trigger: 'blur' }
                ]
            }
        };
    },
    methods: {
        getCollections() {
            this.formItem.addresseeName = this.modalData.data.addresseeName;
            this.formItem.mobile = this.modalData.data.mobile;
            this.formItem.email = this.modalData.data.email;
            if (!this.modalData.type) {
                this.$nextTick(() => {
                    this.$refs['formValidate'].resetFields();
                });
            } else {
                this.formItem.id = this.modalData.data.id;
            }
        },
        // 添加干系人
        addAddressee() {
            this.loading = true;
            delete this.formItem.id;
            addAddressee({ ...this.formItem }).then(res => {
                if (res.success) {
                    this.$hMessage.success('添加成功');
                    this.modalData.status = false;
                    this.$emit('update');
                } else {
                    this.$hMessage.error(res.message || '添加失败');
                }
                this.loading = false;
            }).catch(err => {
                this.$hMessage.error('添加失败');
                this.loading = false;
                console.log(err);
            });
        },
        // 修改干系人信息
        updateAddressee() {
            this.loading = true;
            updateAddressee({ ...this.formItem }).then(res => {
                if (res.success) {
                    this.$hMessage.success('修改成功');
                    this.modalData.status = false;
                    this.$emit('update');
                } else {
                    this.$hMessage.error(res.message || '修改失败');
                }
                this.loading = false;
            }).catch(err => {
                this.$hMessage.error('修改失败');
                this.loading = false;
                console.log(err);
            });
        },
        submitConfig() {
            this.$refs['formValidate'].validate(valid => {
                if (valid) {
                    !this.modalData.type ? this.addAddressee() : this.updateAddressee();
                }
            });
        }
    },
    components: {  aButton }
};
</script>
