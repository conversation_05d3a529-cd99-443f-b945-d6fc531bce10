.h-btn-dark {
    background: var(--button-bg-color);
    border: var(--border);
    color: var(--font-color);
}

.h-btn-dark:hover,
.h-btn-dark:active {
    background: var(--button-hover-bgcolor);
    border: var(--border);
    color: var(--font-color);
}

.h-btn-danger {
    background-color: transparent;
    border-color: var(--error-color);
}

.h-btn-danger:hover,
.h-btn-danger:active {
    background: var(--button-hover-bgcolor);
    border: 1px solid var(--error-color);
}

.h-btn-disable {
    background: var(--button-bg-color);
    border: var(--border);
    color: var(--font-color);
}


.h-btn-primary.disabled,
.h-btn-primary.disabled.active,
.h-btn-primary.disabled:active,
.h-btn-primary.disabled:focus,
.h-btn-primary.disabled:hover,
.h-btn-primary[disabled],
.h-btn-primary[disabled].active,
.h-btn-primary[disabled]:active,
.h-btn-primary[disabled]:focus,
.h-btn-primary[disabled]:hover {
    color: var(--font-color);
    background-color: #298dff;
    border-color: #298dff;
}

.h-btn-dark.h-btn.disabled,
.h-btn-dark.h-btn.disabled.active,
.h-btn-dark.h-btn.disabled:active,
.h-btn-dark.h-btn.disabled:focus,
.h-btn-dark.h-btn.disabled:hover,
.h-btn-dark.h-btn[disabled],
.h-btn-dark.h-btn[disabled].active,
.h-btn-dark.h-btn[disabled]:active,
.h-btn-dark.h-btn[disabled]:focus,
.h-btn-dark.h-btn[disabled]:hover {
    background: var(--button-bg-color);
    border: var(--border);
    color: var(--font-color);
}

