/* stylelint-disable selector-class-pattern */
.best-form {
    display: flex;
    flex-wrap: wrap;
    background-color: #262b40;

    .form-title {
        display: flex;
        justify-content: space-between;
        align-items: center;
        width: 100%;
        height: 48px;
        border-bottom: 1px solid #31364a;

        & > .form-title-text {
            padding-left: 33px;
            font-size: 14px;
            color: #fff;

            &::before {
                display: inline-block;
                position: relative;
                left: -15px;
                top: 3px;
                content: "";
                width: 5px;
                height: 17px;
                background: var(--link-color);
            }
        }
    }

    .h-form {
        width: 100%;
    }

    // 自定义输入框，下拉框样式
    .h-form-item-label {
        color: var(--font-color) !important;
    }

    .h-input {
        background-color: var(--input-bg-color);
        border: var(--border);
        border-radius: var(--border-radius);
        color: var(--font-color);
    }

    .h-input input {
        color: var(--font-color);
    }

    .h-form-item-error .h-input {
        border-color: var(--error-color) !important;
    }

    .h-selectTable .h-selectTable-selection {
        color: var(--font-color);
        background-color: var(--input-bg-color);
        border-color: var(--border-color);
    }

    .h-selectTable .h-selectTable-selection .h-selectTable-input {
        color: var(--font-color);
    }

    .h-form-item-error .h-selectTable .h-selectTable-selection {
        border-color: var(--error-color);
    }

    .h-select > .h-select-left {
        color: var(--font-color);
        background-color: var(--input-bg-color);
        border: var(--border);
    }

    .h-select-content-input,
    .h-select-input {
        color: var(--font-color);
    }

    .h-form-item-error .h-select-selection {
        border: 1px solid var(--error-color);
    }

    // drawer
    .h-drawer-content {
        background: var(--main-color);
    }

    .h-drawer-header {
        border-bottom: var(--border);
    }

    .h-drawer-header-inner {
        color: var(--font-color);
    }

    // checkbox 公共样式
    .h-checkbox.h-checkbox-disabled > .h-checkbox-inner {
        background-color: var(--border-color) !important;
        border-color: var(--border-color) !important;
    }

    .h-checkbox.h-checkbox-disabled + span {
        color: var(--border-color);
    }

    .h-checkbox + span {
        color: var(--font-color);
    }

    .h-checkbox-inner {
        border: 1px solid var(--font-color);
        border-radius: 2px;
        background: var(--input-bg-color);
    }

    .h-checkbox-inner::after {
        border: none;
    }

    .h-checkbox-checked > .h-checkbox-inner::after {
        border: 2px solid var(--font-color);
        border-top: 0;
        border-left: 0;
    }

    .h-checkbox-checked > .h-checkbox-inner {
        border-color: var(--link-color) !important;
        background: var(--link-color) !important;
    }

    // radio
    .h-radio-inner {
        background-color: var(--wrapper-color);
    }

    // poptip
    .h-input-group-append,
    .h-input-group-prepend {
        background: none;
        border: none;
    }

    .td-time {
        cursor: pointer;

        &:hover {
            color: var(--link-color);
        }
    }
}
