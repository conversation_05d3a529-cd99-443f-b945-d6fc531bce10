<!-- /**
 * <AUTHOR>
 * @email <EMAIL>
 * @create date 2024-09-10 13:44:12
 * @modify date 2024-09-10 13:44:12
 * @desc [中心内配置编辑、新增弹窗]
 */ -->
<template>
  <div>
    <h-msg-box-safe
      v-model="visible"
      :title="`${isAdd ? '新建' : '更新'}中心内配置`"
      :mask-closable="false"
      @on-close="onClose"
      @on-cancel="onClose"
    >
      <h-form ref="form" v-model="formData.data" :label-width="100">
        <h-form-item label="中心名" prop="data.name" required>
          <h-input
            v-model="formData.data.name"
            type="text"
            style="width: 300px;"
            :maxlength="100"
            placeholder="最大输入长度100"
            @on-blur="onBlur"
            @on-change="onChangeName"
          ></h-input>
        </h-form-item>

        <h-form-item label="同中心RPO" prop="data.rpo">
          <h-input
            v-model="formData.data.rpo"
            type="text"
            placeholder="请输入数字类型"
            style="width: 300px;"
          ></h-input>
        </h-form-item>

        <h-form-item label="同步确认模式" prop="data.syncAckPoint">
          <h-select
            v-model="formData.data.syncAckPoint"
            type="text"
            style="width: 300px;"
          >
            <h-option
              v-for="item in syncAckPointList"
              :key="item.value"
              :value="item.value"
              >{{ item.name }}</h-option
            >
          </h-select>
        </h-form-item>
      </h-form>

      <template v-slot:footer>
        <a-button @click="onClose">取消</a-button>
        <a-button type="primary" :loading="loading" @click="onSubmit"
          >确认</a-button
        >
      </template>
    </h-msg-box-safe>
  </div>
</template>
<script>
import {
    defineComponent,
    ref,
    getCurrentInstance,
    nextTick,
    computed
} from 'vue';
import aButton from '@/components/common/button/aButton';
import { createZoneConfig, updateZoneConfig } from '@/api/rcmApi';
import { useInnerZoneModal, useZonesList } from './hooks';
import { SYNC_MODEL_LIST, OPEARTE_TYPE } from './constant';
// import { longIntValidate } from '@/utils/validate';
export default defineComponent({
    name: 'InnerZoneModal',
    props: {
        rcmId: String
    },
    setup(props) {
        const { modal, onClose } = useInnerZoneModal();
        const { fetchData } = useZonesList();
        const loading = ref(false);
        const isAdd = computed(() => modal.value.type === OPEARTE_TYPE.ADD);
        const { proxy } = getCurrentInstance();
        /**
     * 确认保存
     */
        const onSubmit = async () => {
            const { data } = modal.value;
            if (!data.name) {
                proxy.$refs['form'].validate();
                return;
            }
            // 暂时不需要前端校验名称唯一性，后续可能需要，先保留
            // const existData = list.value.find(item => item.name === data.name);
            // let hasExist = isAdd.value && !!existData;
            // if (!isAdd.value) {
            //     // 编辑时，检查中心名是否重复
            //     hasExist = !!list.value.find(item => item.name === data.name && item.id !== data.id);
            // }
            // if (hasExist) {
            //     proxy.$hMessageSafe.error({
            //         content: `中心内配置 "${data.name}" 已存在"`,
            //         duration: 3
            //     });
            //     return;
            // }
            try {
                const { rpo } = data;
                if (rpo) {
                    if (isNaN(rpo) || (rpo.search && rpo.search('e') !== -1)) {
                        // 请输入数字类型
                        proxy.$hMessageSafe.error({
                            content: `rpo填写错误：请输入数字类型`,
                            duration: 3
                        });
                        return;
                    }
                    if (Number(rpo) % 1 !== 0) {
                        // 请输入数字类型
                        proxy.$hMessageSafe.error({
                            content: `rpo填写错误：请输入数字类型`,
                            duration: 3
                        });
                        return;
                    }
                    if (rpo < -(2 ** 63) || rpo > 2 ** 63 - 1) {
                        proxy.$hMessageSafe.error({
                            content: `rpo填写错误：\n超出输入范围，输入范围在-9223372036854775808 ~ 9223372036854775807`,
                            duration: 3
                        });
                        return;
                    }
                }
                loading.value = true;
                proxy.$refs['form'].resetValidateField('data.name');
                const api = isAdd.value ? createZoneConfig : updateZoneConfig;
                const res = await api({ ...data, rcmId: props.rcmId });
                if (res.code === '200') {
                    // 成功，重新刷新页面
                    const zoneName = data.name.length > 10 ? data.name.slice(0, 10) + '...' : data.name;
                    proxy.$hMessageSafe.success({
                        content: `中心内配置 "${zoneName}" 已${isAdd.value ? '添加' : '更新'}`,
                        duration: 3
                    });
                    fetchData(props.rcmId);
                    onClose();
                }
            } finally {
                loading.value = false;
            }
        };
        const onBlur = () => {
            nextTick(() => {
                nextTick(() => {
                    proxy.$refs['form'].resetValidateField('data.name');
                });
            });
        };
        const onChangeName = (ev) => {
            if (ev.target.value) {
                nextTick(() => {
                    proxy.$refs['form'].resetValidateField('data.name');
                });
            } else {
                proxy.$refs['form'].resetValidateField('data.name');
            }
        };
        return {
            visible: modal.value.status,
            onClose,
            isAdd,
            formData: modal,
            loading,
            onSubmit,
            onBlur,
            onChangeName,
            // test: [{ trigger: 'blur,change', test: (rule, value, callback) => {
            //     return longIntValidate(rule, modal.value.data.rpo, callback);
            // } }],
            syncAckPointList: SYNC_MODEL_LIST
        };
    },
    components: { aButton }
});
</script>
