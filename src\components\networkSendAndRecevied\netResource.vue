<template>
  <div ref="result-box" class="net-resource">
    <menu-layout ref="menu" customMenu @menu-fold="menuFold">
      <template v-slot:menu>
        <div class="menu">
          <div
            class="header-menu"
            :style="{ padding: menuFoldStatus ? '0' : '0px 10px' }"
          >
            已托管应用节点
          </div>
          <h-menu
            v-show="instanceList.length"
            theme="dark"
            :active-name="selectInstanceId"
            @on-select="selectInstanceChange"
          >
            <h-menu-item
              v-for="item in instanceList"
              :key="item.id"
              :name="item.id"
            >
              <span class="instance-name" :title="item.instanceName">{{
                item.instanceName
              }}</span>
              <h-icon
                v-if="getStatusById(item.id)"
                class="instance-status"
                name="order"
                size="14"
                title="抓包中"
              ></h-icon>
            </h-menu-item>
          </h-menu>
          <p v-show="!menuFoldStatus && !instanceList.length" style="padding: 10px; text-align: center;">无托管应用节点</p>
        </div>
      </template>
      <template v-slot:right>
        <h-form
          ref="formItems"
          :model="formItems"
          :label-width="70"
          :cols="3"
          style="background: var(--wrapper-color);"
          @submit.native.prevent
        >
          <h-form-item label="插件" prop="plugin" required>
            <h-select
              v-model="formItems.plugin"
              :clearable="false"
              multiple
              isCheckall
              collapseTags
              :disabled="selectInstanceStatus"
            >
              <h-option v-for="item in pluginList" :key="item.pluginName" :value="item.pluginName"
                >{{ item.pluginName }}
              </h-option>
            </h-select>
          </h-form-item>
          <h-form-item label="功能号：" prop="funcNos">
            <label slot="label">
              <h-poptip
                trigger="hover"
                transfer
                autoPlacement
                customTransferClassName="apm-poptip monitor-poptip"
              >
                <h-icon name="ios-information-outl" size="14"></h-icon>
                <pre slot="content">{{`1.功能号可以包含数字和英文字符“?”\n2.英文字符“?”表示单个占位符，使用该占位符则表示使用模糊匹配\n3.多个功能号使用英文字符“;”分隔`}}</pre>
            </h-poptip
              >功能号
            </label>
            <h-input
              v-model.trim="formItems.funcNos"
              placeholder="请输入"
              :disabled="selectInstanceStatus"
              :maxlength="50"
            ></h-input>
          </h-form-item>
          <h-form-item class="form-no-label">
            <a-button
              v-show="!selectInstanceStatus"
              type="primary"
              @click="() => handleOpenOrCloseResource(true)"
            >
              <h-poptip
                trigger="hover"
                transfer
                autoPlacement
                customTransferClassName="apm-poptip monitor-poptip"
              >
                <h-icon name="ios-information-outl"></h-icon>
                <div slot="content" class="pop-content">
                  <span>一个应用节点仅能开启一个抓包任务</span>
                </div>
              </h-poptip>
              开始抓包</a-button
            >
            <a-button
              v-show="selectInstanceStatus"
              type="danger"
              @click="() => handleOpenOrCloseResource(false)"
            >
              <h-icon name="ios-pause"></h-icon>
              停止抓包</a-button
            >
          </h-form-item>
        </h-form>
        <div class="result-box">
          <h-split v-model="splitVal" min="400px" max="400px">
            <div slot="left" class="demo-split-pane">
              <div class="table-box">
                <obs-table
                  ref="msgTableData"
                  highlightRow
                  :height="tableHeight"
                  :loading="tableLoading"
                  border
                  :title="msgTitle"
                  :columns="columns"
                  :tableData="msgTableData"
                  :hasPage="false"
                  showTitle
                  rowSelectOnly
                  :loadingText="selectInstanceStatus && tableLoading ? '抓包中，请稍后' : '加载中'"
                  @on-current-change="tableRowcheckedChange"
                  @button-click="handleButtonClick"
                  @query="getTableData"
                >
                    <template v-slot:extraTitleBox>
                        <div v-if="selectInstanceStatus" class="select-status">抓包中</div>
                    </template>
                </obs-table>
                <br />
                <div class="page-box">
                  <span>共 {{ msgTotal }} 条</span>&nbsp;&nbsp;
                  <a-button type="dark" :disabled="!msgTableData.length" :loading="buttonLoading" @click="handlePrev"
                    >上一页</a-button
                  >&nbsp;&nbsp;
                  <a-button type="dark" :disabled="!msgTableData.length" :loading="buttonLoading" @click="handleNext"
                    >下一页</a-button
                  >&nbsp;&nbsp;,
                  <span>
                    跳转序号
                    <h-input
                      ref="jumpInput"
                      v-model="jumpIndex"
                      type="int"
                      specialFilter
                      :specialLength="10"
                      :specialDecimal="0"
                      :maxlength="20"
                      style="width: 70px;"
                      @on-blur="handlePageChange"
                      @on-enter="handlePageChange"
                    ></h-input>
                  </span>
                </div>
              </div>
            </div>
            <div slot="right" class="demo-split-pane">
              <div class="json-box">
                <obs-title :title="jsonTitle" />
                <json-viewer
                  v-if="!isShowTextFunc(jsonData)"
                  :value="jsonData"
                  :expand-depth="10"
                  copyable
                  :expanded="true"
                  @copied="onCopy"
                >
                </json-viewer>
                <monaco-code-editor
                    v-if="isShowTextFunc(jsonData)"
                    ref="monaco-code-editor"
                    :options="options"
                    :value="JSON.stringify(jsonData || {}, null, 4)"
                    language="json"
                />
              </div>
            </div>
          </h-split>
        </div>
      </template>
    </menu-layout>

    <func-nos-rule-modal
        v-if="funcNosRulesInfo.status"
        :modalData="funcNosRulesInfo"
    />
    <draggable-table-configModal
        v-if="draggableTableConfigInfo.status"
        :modalData="draggableTableConfigInfo"
        @set-config="setNewConfigList"
    />
  </div>
</template>

<script>
import _ from 'lodash';
import obsTitle from '@/components/common/title/obsTitle';
import obsTable from '@/components/common/obsTable/obsTable';
import aButton from '@/components/common/button/aButton';
import menuLayout from '@/components/common/menuLayout/menuLayout';
import jsonViewer from 'vue-json-viewer';
import MonacoCodeEditor from '@/components/locateConfig/MonacoCodeEditor.vue';
import funcNosRuleModal from '@/components/networkSendAndRecevied/modal/funcNosRuleModal';
import draggableTableConfigModal from '@/components/networkSendAndRecevied/modal/draggableTableConfigModal';
import { getProductInstances } from '@/api/productApi';
import {
    getPacketCaptureInstancesStatus,
    getPacketCaptureInstanceStatus,
    getPacketCaptureInstanceFilter,
    postPacketCaptureEnable,
    getPacketCaptureData,
    clearPacketCaptureData,
    setPacketTransferData
} from '@/api/networkApi';
import { transferVal, getByteSize } from '@/utils/utils';
export default {
    props: {
        productInstNo: {
            type: String,
            default: ''
        }
    },
    components: {
        menuLayout,
        aButton,
        obsTable,
        obsTitle,
        jsonViewer,
        MonacoCodeEditor,
        funcNosRuleModal,
        draggableTableConfigModal
    },
    data() {
        return {
            splitVal: 0.6,
            menuFoldStatus: false,
            selectInstanceId: '',
            selectInstanceStatus: false,
            instanceList: [],
            instanceStatusList: [],
            pluginList: [],
            formItems: {
                plugin: [],
                funcNos: ''
            },
            // regRule: [
            //     { test: /^(?:\d+\?*;)*\d+\?*$/,
            //         message: `1.功能号可以包含数字和英文字符“?”号\n2.英文字符“?”号表示0~9任意一个数字，只能出现在最后\n（比如“22??”表示2200~2299这一段功能号）\n3.多个功能号使用英文字符“;”号分隔`,
            //         trigger: 'blur'
            //     }
            // ],
            jsonTitle: {
                label: '消息体'
            },
            jsonData: '',
            tableLoading: false,
            buttonLoading: false,
            msgTitle: {
                label: '消息列表',
                slots: [
                    {
                        type: 'button',
                        key: 'refresh',
                        iconName: 'refresh',
                        iconSize: '12px',
                        value: ' 刷新'
                    },
                    {
                        type: 'button',
                        key: 'clear',
                        buttonType: 'danger',
                        iconName: 'trash',
                        iconSize: '12px',
                        iconColor: 'var(--error-color)',
                        value: ' 清空'
                    },
                    {
                        type: 'button',
                        key: 'config',
                        iconSize: '12px',
                        iconName: 't-b-setting'
                    }
                ]
            },
            AllColumns: [
                {
                    title: '功能号',
                    key: 'FunctionID',
                    ellipsis: true,
                    isChecked: true
                },
                {
                    title: '系统号',
                    key: 'SystemNo',
                    ellipsis: true,
                    isChecked: false
                },
                {
                    title: 'Token',
                    key: 'Token',
                    ellipsis: true,
                    isChecked: false
                },
                {
                    title: '用户自定义',
                    key: 'UserDefined',
                    ellipsis: true,
                    isChecked: false
                },
                {
                    title: '抓包时间',
                    key: 'Time',
                    minWidth: 200,
                    ellipsis: true,
                    isChecked: true,
                    sortable: true
                },
                {
                    title: '包类型',
                    key: 'MsgType',
                    ellipsis: true,
                    isChecked: true
                },
                {
                    title: '附加信息',
                    key: 'Remark',
                    minWidth: 200,
                    ellipsis: true,
                    isChecked: true
                }
            ],
            columns: [
                {
                    title: '序号',
                    key: 'Index'
                }
            ],
            msgTotal: 0,
            maxCount: 1000000,
            count: 10,
            jumpIndex: undefined,
            startIdx: 1,
            endIndex: 1,
            AllTableData: [],
            msgTableData: [],
            sortType: 'desc',
            tableHeight: 0,
            funcNosRulesInfo: {
                status: false
            },
            draggableTableConfigInfo: {
                status: false
            },
            options: {
                readOnly: true
            },
            timer: null,
            innerTimer: null
        };
    },
    mounted() {
        this.fetTableHeight();
        this.clearPolling();
        window.addEventListener('resize', this.fetTableHeight);
    },
    beforeDestroy() {
        this.clearPolling();
        window.removeEventListener('resize', this.fetTableHeight);
    },
    methods: {
        onCopy() {
            this.$hMessage.success('Copied！');
        },
        menuFold(status) {
            this.menuFoldStatus = status;
        },
        // 设置table高度
        fetTableHeight() {
            this.tableHeight = this.$refs['result-box'] ? this.$refs['result-box'].offsetHeight - 190 : 0;
        },
        // 清空数据
        clearData() {
            this.pluginList = [];
            this.formItems = {
                plugin: [],
                funcNos: ''
            };
            this.msgTableData = [];
            this.jsonData = '';
            this.msgTotal = 0;
            this.jumpIndex = undefined;
            this.startIdx = 1;
            this.endIndex = 1;
            this.selectInstanceStatus = false;
            this.$refs['formItems'] && this.$refs['formItems'].resetFields();
            this.sortType = 'desc';
        },
        clearInstanceData(){
            this.instanceList = [];
            this.instanceStatusList = [];
            this.selectInstanceId = '';
        },
        // 初始化
        async initData() {
            this.clearInstanceData();
            this.clearData();
            this.clearPolling();

            // 合并默认配置和本地存储的配置
            this.AllColumns = this.AllColumns.map((defaultCol) => {
                const savedCol = (JSON.parse(localStorage.getItem('apm-msg-list-config')) || []).find(col => col.key === defaultCol.key) || {};
                return { ...defaultCol, ...savedCol, sortable: defaultCol.sortable };
            });

            // 筛选选中的列
            this.columns = [
                // 表格显示
                {
                    title: '序号',
                    key: 'Index'
                },
                ...this.AllColumns.filter(v => v.isChecked)
            ];

            await this.getProductInstances();
            this.fetTableHeight();
            if (!this.instanceList?.length) return;
            // 初始化时执行一次
            this.getPacketCaptureInstancesStatus();
            // 开启全局定时器
            this.createInstancesPolling();
            this.createInnerPolling();
        },
        // ******************************* 定时器 *********************************************
        clearPolling(){
            this.clearInstancesPolling();
            this.clearInnerPolling();
        },
        createInstancesPolling() {
            this.clearInstancesPolling();
            this.timer = setInterval(() => {
                this.getPacketCaptureInstancesStatus();
            }, 5000);
        },
        clearInstancesPolling() {
            this.timer && clearInterval(this.timer);
        },
        createInnerPolling(){
            this.clearInnerPolling();
            this.innerTimer = setInterval(() => {
                this.setSelectInstanceStatus();
            }, 5000);
        },
        clearInnerPolling(){
            this.innerTimer && clearInterval(this.innerTimer);
        },
        // ******************************* 菜单功能 *********************************************
        // 切换列表
        async selectInstanceChange(id) {
            this.clearData();
            this.selectInstanceId = id;
            sessionStorage.setItem('apm.netSendInstanceId', id);
            if (this.selectInstanceId) {
                await this.getPluginList();
            }
        },
        // 获取应用节点列表
        async getProductInstances() {
            const res = await getProductInstances({ productId: this.productInstNo });
            if (res.code === '200') {
                this.instanceList = (res?.data?.instances || []).filter(o => {
                    return o?.supportPacketCapture !== undefined
                        ? o.supportPacketCapture === true
                        : o?.instanceType !== 'zk' && o?.instanceType !== 'ldpadmin';
                });
                this.$nextTick(async () => {
                    if (this.instanceList?.length) {
                        const netSendInstanceId = sessionStorage.getItem('apm.netSendInstanceId') || '';
                        const id = _.find(this.instanceList, ['id', netSendInstanceId])?.id || this.instanceList?.[0]?.id || '';
                        await this.selectInstanceChange(id);
                    }
                });
            }
        },
        // 获取所有节点抓包开关状态
        async getPacketCaptureInstancesStatus() {
            const instanceIds = this.instanceList.map(o => o?.id) || [];
            const param = {
                instanceIds: instanceIds
            };
            const res = await getPacketCaptureInstancesStatus(param);
            if (res.code === '200') {
                this.instanceStatusList = res?.data || [];
            }
        },
        // 轮询页面展示节点抓包状态
        async setSelectInstanceStatus(noSavePlugin = true) {
            if (!this.selectInstanceId) return;
            const param = {
                instanceId: this.selectInstanceId
            };
            const res = await getPacketCaptureInstanceStatus(param);
            if (this.selectInstanceId !== param?.instanceId) return;
            if (res?.code === '200') {
                !noSavePlugin && (this.pluginList = res?.data?.packetCaptureStatus?.pluginPacketCaptureStatuses?.map(o => {
                    return {
                        pluginName: o?.pluginName,
                        pluginNameNo: o.instanceNo
                    };
                }) || []);
                this.selectInstanceStatus = res?.data?.enable;
                if (this.selectInstanceStatus) {
                    this.formItems.plugin = res?.data?.packetCaptureStatus?.pluginPacketCaptureStatuses?.filter(o => o?.enable)?.map(o => o?.pluginName) || [];
                    this.getPacketCaptureInstanceFilter();
                }
            }
        },
        getStatusById(id) {
            return _.find(this.instanceStatusList, ['instanceId', id])?.enable;
        },
        // ******************************* 抓包表单 *********************************************
        // 获取插件列表
        async getPluginList() {
            await this.setSelectInstanceStatus(false);
            this.$nextTick(() => {
                this.getTableData();
            });
        },
        // 当处于抓包状态中时获取功能号
        async getPacketCaptureInstanceFilter(){
            const param = {
                instanceId: this.selectInstanceId
            };
            const res = await getPacketCaptureInstanceFilter(param);
            if (this.selectInstanceId !== param?.instanceId) return;
            if (res?.code === '200') {
                this.formItems.funcNos = res?.data?.map(o => o?.filterValue)?.join(',');
            } else {
                this.formItems.funcNos = '';
            }
        },
        // 开关抓包
        async handleOpenOrCloseResource(enable) {
            if (!this.selectInstanceId) {
                this.$hMessage.info('请先选择应用节点');
                return;
            }
            this.$refs['formItems'].validate(async (valid) => {
                if (valid) {
                    const plugins = [];
                    (this.formItems?.plugin || []).forEach(o => {
                        plugins.push({
                            pluginName: o,
                            pluginInstanceNo: _.filter(this.pluginList, ['pluginName', o])?.[0]?.pluginNameNo || 0
                        });
                    });
                    const param = {
                        instanceId: this.selectInstanceId,
                        plugins: plugins,
                        filter: {
                            filterType: 'functionNo',
                            filterValue: this.formItems.funcNos
                        },
                        enable
                    };
                    if (this.selectInstanceId !== param?.instanceId) return;
                    const res = await postPacketCaptureEnable(param);
                    if (res?.code === '200') {
                        enable && (this.selectInstanceStatus = true);
                        !enable && (this.selectInstanceStatus = false);
                        this.$nextTick(() => {
                            this.getTableData();
                        });
                    } else if (res?.code?.length === 8) {
                        this.$hMessage.error(res?.message);
                    } else {
                        enable && this.$hMessage.error('开启抓包失败');
                        !enable && this.$hMessage.error('停止抓包失败');
                    }
                }
            });
        },
        // 获取表格数据
        async getTableData() {
            this.tableLoading = true;
            this.jumpIndex = undefined;
            this.startIdx = 1;
            this.endIndex = 1;
            const param = {
                instanceId: this.selectInstanceId,
                start: -1,
                count: this.maxCount
            };
            this.sortType = this.$refs['msgTableData']?.getPageData()?.sortType?.toLowerCase() || 'desc';
            try {
                const res = await getPacketCaptureData(param);
                if (this.selectInstanceId !== param?.instanceId) return;
                if (res?.code === '200') {
                    this.AllTableData = this.sortType !== 'asc' ? (res?.data?.list || []).reverse() : res?.data?.list || [];
                    this.msgTableData = this.AllTableData?.slice(0, this.count) || [];
                    this.msgTotal = res?.data?.totalCount || 0;
                    this.startIdx = this.sortType !== 'asc' ? this.AllTableData?.[this.msgTotal - 1]?.Index || 1 : this.msgTableData?.[0]?.Index || 1;
                    this.endIndex = this.sortType !== 'asc' ? this.msgTableData?.[0]?.Index || 1 : this.AllTableData?.[this.msgTotal - 1]?.Index || 1;
                    this.jsonData = '';
                } else {
                    this.msgTableData = [];
                    this.msgTotal = 0;
                    this.jsonData = '';
                }
            } catch (err) {
                this.msgTableData = [];
                this.msgTotal = 0;
                this.jsonData = '';
            } finally {
                this.tableLoading = false;
            }
        },
        // ******************************* 表格功能 *********************************************
        // 消息列表-行选中
        tableRowcheckedChange(row) {
            this.setPacketTransferData(row?.Data || '');
        },
        // 抓包数据解析
        async setPacketTransferData(msg){
            const param = {
                instanceId: this.selectInstanceId,
                ldpMsgBase64: msg
            };
            const res = await setPacketTransferData(param);
            if (this.selectInstanceId !== param?.instanceId || msg !== param?.ldpMsgBase64) return;
            if (res?.code === '200') {
                this.jsonData =  this.formatJsonData(res?.data);
            } else {
                this.jsonData = msg;
            }
        },
        // 消息列表
        handleButtonClick(key) {
            if (key === 'config') {
                this.draggableTableConfigInfo = {
                    status: true,
                    configList: _.cloneDeep([...this.AllColumns]),
                    defaultCheckedList: _.cloneDeep(
            [...this.AllColumns]
                ?.filter((v) => v?.isChecked)
                ?.map((o) => o?.key)
                    )
                };
                return;
            }

            if (key === 'refresh') {
                this.refreshPacketCaptureData();
            }

            if (key === 'clear') {
                this.clearPacketCaptureData();
            }
        },
        // 刷新表格
        refreshPacketCaptureData() {
            this.getTableData();
        },
        // 清空抓包缓存数据
        async clearPacketCaptureData() {
            // 确认
            this.$hMsgBoxSafe.confirm({
                title: '清空',
                content: `确定清空抓包数据吗?`,
                onOk: async () => {
                    const param = {
                        productId: this.productInstNo,
                        instanceId: this.selectInstanceId
                    };
                    const res = await clearPacketCaptureData(param);
                    if (res?.code === '200') {
                        this.getTableData();
                    }
                }
            });
        },
        // 配置列表显示字段-排序、筛选表头
        setNewConfigList(configList) {
            const columnsList = configList.filter((v) => v.isChecked);
            this.AllColumns = [...configList]; // 拖拽结束后的列表顺序
            localStorage.setItem('apm-msg-list-config', JSON.stringify(configList));

            this.columns = [
                // 表格显示
                {
                    title: '序号',
                    key: 'Index'
                },
                ...columnsList
            ];

            // 更改表头排序会默认normal排序
            // hui无法手动设置排序规则，设置初始值排序仅对当前表格10条数据有效，排序规则错误
            // obsTable组件内保留上一次的排序方式，结果存在差异
            // 因此抓包页面配置表头重新请求表格数据
            this.$nextTick(() => {
                this.$refs['msgTableData'] && this.$refs['msgTableData'].resetSortData();
                this.getTableData();
                this.$hMessage.success('消息列表已重置！');
            });
        },
        // 上一个
        handlePrev() {
            this.buttonLoading = true;
            const firstMsgIndex = this.msgTableData?.[0]?.Index;
            if (firstMsgIndex && (this.sortType !== 'asc' ? firstMsgIndex < this.endIndex : firstMsgIndex > this.startIdx)) {
                const currIndex = this.AllTableData?.findIndex(item => item?.Index === firstMsgIndex);
                if (currIndex > 0){
                    const startIndex = Math.max(0, currIndex - this.count);
                    this.msgTableData = this.AllTableData?.slice(startIndex, currIndex) || [];
                } else {
                    this.msgTableData = [];
                }
            } else {
                this.$hMessage.warning('当前页为首页无法支持翻页，请刷新列表或用序号跳转至您想查询的位置');
            }
            this.jsonData = '';
            this.buttonLoading = false;
        },
        // 下一个
        handleNext() {
            this.buttonLoading = true;
            const lastMsgIndex = this.msgTableData?.slice(-1)?.[0]?.Index;
            if (lastMsgIndex && (this.sortType !== 'asc' ? lastMsgIndex - 1 >= this.startIdx : lastMsgIndex + 1 <= this.endIndex)) {
                const targetIndexValue = this.sortType !== 'asc' ? lastMsgIndex - 1 : lastMsgIndex + 1;
                const targetIndex = this.AllTableData?.findIndex(item => item?.Index === targetIndexValue);
                if (targetIndex > 0) {
                    const remainingItems = this.AllTableData?.length - targetIndex;
                    const sliceEndIndex = targetIndex + Math.min(this.count, remainingItems);
                    this.msgTableData = this.AllTableData?.slice(targetIndex, sliceEndIndex) || [];
                } else {
                    this.msgTableData = [];
                }
            } else {
                this.$hMessage.warning('当前页为最后一页无法支持翻页，请刷新列表或用序号跳转至您想查询的位置');
            }
            this.jsonData = '';
            this.buttonLoading = false;
        },
        // 跳转页
        handlePageChange() {
            if (!transferVal(this.jumpIndex)){
                this.msgTableData = this.AllTableData?.slice(0, this.count) || [];
            } else {
                const targetIndex = this.AllTableData?.findIndex(item => item?.Index === Number(this.jumpIndex));
                if (targetIndex === -1) {
                    this.msgTableData = [];
                } else {
                    this.msgTableData = this.AllTableData?.slice(targetIndex, targetIndex + this.count) || [];
                }
                this.jsonData = '';
            }
            this.$refs.jumpInput.blur();
        },
        // 计算json大小，判断文本展示类型
        isShowTextFunc(data) {
            const jsonStr = JSON.stringify(data);
            if (getByteSize(jsonStr) > 600 * 1024) {
                return true;
            }
            return false;
        },
        // json转换防止报错
        formatJsonData(str) {
            if (str) {
                try {
                    const data = JSON.parse(str);
                    return data;
                } catch (error) {
                    return str;
                }
            }
            return '';
        }
    }
};
</script>

<style lang="less" scoped>
@import url("@/assets/css/input.less");
@import url("@/assets/css/json-view.less");
@import url("@/assets/css/poptip-1.less");
@import url("@/assets/css/menu.less");

/deep/.h-menu-dark.h-menu-vertical .h-menu-item:hover,
.h-menu-dark.h-menu-vertical .h-menu-submenu-title:hover {
    background: var(--link-opacity-color) !important;

    /deep/ &::after {
        position: absolute;
        top: 0;
        left: 0;
        content: "";
        width: 4px;
        height: 33px;
        background: var(--link-color);
    }
}

.net-resource {
    height: 100%;

    .h-menu {
        height: calc(100% - 60px);

        .instance-name {
            display: inline-block;
            margin-right: 10px;
            width: 80%;
            overflow: hidden;
            white-space: nowrap;
            text-overflow: ellipsis;
        }

        .instance-status {
            position: relative;
            top: -10px;
        }
    }

    /deep/ .h-form-row .h-form-item {
        padding: 6px 10px;
    }

    /deep/ .h-input-group-append,
    /deep/ .h-input-group-prepend {
        color: #999;
        background-color: var(--wrapper-color);
        border: var(--border);
    }

    .form-no-label {
        /deep/.h-form-item-content {
            margin-left: 0 !important;
        }
    }

    .icon-setting {
        cursor: pointer;

        &:hover {
            color: var(--link-color);
        }
    }

    .result-box {
        height: calc(100% - 65px);
        margin-top: 15px;

        /deep/ .obs-title .title-box .h-icon:hover {
            text-decoration: none;
        }

        .demo-split-pane {
            height: 100%;
        }

        .table-box {
            .select-status {
                position: absolute;
                font-size: 12px;
                height: 24px;
                line-height: 24px;
                width: 53px;
                color: #fff;
                padding: 0 8px;
                z-index: 1;
                background: #1f3759;
                border-radius: 4px;
                top: 10px;
                left: 105px;
            }

            .obs-table {
                height: auto;
                margin-top: 0;

                /deep/.h-btn {
                    padding: 6px 10px;
                }
            }

            .page-box {
                margin-right: 10px;
                color: var(--font-color);
                float: right;
            }
        }

        .json-box {
            height: 100%;
            background-color: var(--wrapper-color);
            border-radius: var(--border-radius);

            /deep/ .jv-code {
                padding: 20px 10px;
            }
        }

        .monaco-code-editor {
            margin: 5px;
            height: calc(100% - 48px);
            width: calc(100% - 7px);
        }
    }

    /deep/ .h-split-trigger {
        border: none;
    }

    /deep/ .h-split-trigger-vertical {
        width: 4px;
        background: #1b2130;
    }

    /deep/.h-split-trigger-vertical .h-split-trigger-bar {
        background: #485565;
    }

    /deep/ .h-checkbox-inner {
        background-color: var(--font-color);
        border: 1px solid #d7dde4;
    }
}
</style>
