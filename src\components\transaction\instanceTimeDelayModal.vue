<!--
 * @Description:
 * @Author: <PERSON><PERSON>
 * @Date: 2022-10-16 15:21:23
 * @LastEditTime: 2022-10-16 21:19:00
 * @LastEditors: <PERSON><PERSON>
-->
<template>
    <div>
        <h-msg-box v-model="modalData.status" :escClose="true" :max-height="240" :title="`${modalData.key}时延数据列表`">
            <h-timeline>
                <h-timeline-item
                    v-for="(item, idx) in modalData.data"
                    :key="idx"
                    :color="item.selected ? 'green': 'blue'">
                    <p :class="`text-time ${item.selected ? 'text-selected': ''}`"
                        @click="selectInstance(idx)">（{{item.ip}}）{{cutZero(((item.duration || 0) / 1000).toFixed(3))}}μs</p>
                </h-timeline-item>
            </h-timeline>
            <template v-slot:footer>
                <a-button type="primary" @click="cancel">确认</a-button>
            </template>
        </h-msg-box>
    </div>
</template>

<script>
import { cutZero } from '@/utils/utils';
import aButton from '@/components/common/button/aButton';
export default {
    props: ['modalInfo'],
    data() {
        return {
            modalData: this.modalInfo,
            cutZero
        };
    },
    methods: {
        selectInstance(index) {
            this.modalData.data.forEach((ele, idx) => {
                ele.selected = idx === index;
                this.$set(this.modalData.data, idx, ele);
                this.$emit('update', this.modalData);
            });
        },
        cancel() {
            this.modalData.status = false;
        }
    },
    components: {  aButton }
};
</script>
<style lang="less" scoped>
/deep/ .h-timeline-item-content {
    cursor: pointer;

    .text-selected {
        color: var(--success-color);
    }

    .text-time:hover {
        color: var(--link-color);
        text-decoration: underline;
    }
}

</style>
