<template>
    <div>
        <!-- 下发配置列表 -->
        <h-msg-box-safe v-model="modalData.status" :escClose="true" :mask-closable="false" title="配置报表" width="70"  maxHeight="240" @on-open="getCollections">
            <h-form ref="formValidate" :model="formItem" :label-width="80">
                <h-form-item label="委托回路" prop="loopType" required style="width: 240px;">
                    <h-select v-model="formItem.loopType" :clearable="false" :positionFixed="true" @on-change="handleLoop">
                        <h-option v-for="item in loopList" :key="item.bizMsgType" :value="item.bizMsgType">{{
                            getLoopName(item.bizMsgType)
                        }}</h-option>
                    </h-select>
                </h-form-item>

                <h-form-item label="可视跨度" prop="spans" required>
                    <h-checkbox-group v-model="formItem.spans">
                        <h-checkbox v-for="item in spanList" :key="item.label"
                            :label="item.label">{{ spanLatencyDictDesc[item.label]}}</h-checkbox>
                    </h-checkbox-group>
                </h-form-item>

                <h-form-item label="可视指标" prop="indicators" required>
                    <h-checkbox-group v-model="formItem.indicators">
                        <h-checkbox v-for="item in indicatorList" :key="item.key" :label="item.key">{{ item.title
                        }}</h-checkbox>
                    </h-checkbox-group>
                </h-form-item>

                <h-form-item label="度量单位">
                    <h-radio-group v-model="formItem.unit">
                        <h-radio label="0">纳秒（ns）</h-radio>
                        <h-radio label="1">微秒（μs）</h-radio>
                        <h-radio label="2">毫秒（ms）</h-radio>
                    </h-radio-group>
                </h-form-item>
            </h-form>

            <template v-slot:footer>
                <a-button @click="modalData.status = false">取消</a-button>
                <a-button type="primary" @click="submitConfig">确定</a-button>
            </template>
        </h-msg-box-safe>
    </div>
</template>

<script>
import _ from 'lodash';
import { loopList } from '@/config/exchangeConfig';
import aButton from '@/components/common/button/aButton';
import { mapState } from 'vuex';
export default ({
    props: {
        modalInfo: {
            type: Object,
            default: null
        },
        loopList: {
            type: Array,
            default: []
        },
        loopType: {
            type: String,
            defalut: ''
        }
    },
    data() {
        return {
            modalData: this.modalInfo,
            spanList: [],
            indicatorList: [
                {
                    title: '最小（min）',
                    key: 'min'
                },
                {
                    title: '中位数（p50）',
                    key: 'p50'
                },
                {
                    title: '平均（avg）',
                    key: 'avg'
                },
                {
                    title: '95分位（p95）',
                    key: 'p95'
                },
                {
                    title: '99分位（p99）',
                    key: 'p99'
                },
                {
                    title: '最大（max）',
                    key: 'max'
                },
                {
                    title: '标准差（stdDeviation）',
                    key: 'stdDeviation'
                }
            ],
            formItem: {
                loopType: '',
                spans: [],
                indicators: [],
                unit: '2'
            }
        };
    },
    computed: {
        ...mapState({
            // TODO：参数已被删除，从/ldplt/api/v1/product/latency/trace-models接口，spans参数块，使用spanName转义spanAlias即可
            spanLatencyDictDesc: state => {
                return state?.apmDirDesc?.spanLatencyDictDesc || {};
            }
        })
    },
    methods: {
        getCollections() {
            this.formItem.loopType = this.loopType;

            this.modalData.columns.forEach((ele, index) => {
                if (index) {
                    this.formItem.indicators.push(ele.key);
                }
            });

            this.formItem.unit = this.modalData.unit;
            // 首次进入，spans根据table变化
            this.$nextTick(() => {
                this.formItem.spans = [];
                this.modalData.tableData.forEach(ele => {
                    this.formItem.spans.push(ele.spanName);
                });
            });
        },
        submitConfig() {
            this.$refs['formValidate'].validate(valid => {
                if (valid) {
                    const columns = _.filter(this.indicatorList, o => { return this.formItem.indicators.includes(o.key); });
                    const data = _.find(this.loopList, ['bizMsgType', this.formItem.loopType])
                        .ldpDelaySpanIndicatorsList;
                    const tableData = _.filter(data, o => { return this.formItem.spans.includes(o.spanName); });
                    this.$emit('update', {
                        columns,
                        tableData,
                        loopType: this.formItem.loopType,
                        unit: this.formItem.unit
                    });
                }
            });
        },
        handleLoop() {
            this.spanList = [];
            this.formItem.spans = [];
            _.find(this.loopList, ['bizMsgType', this.formItem.loopType])
                .ldpDelaySpanIndicatorsList.forEach(ele => {
                    this.spanList.push({
                        label: ele.spanName
                    });
                    this.formItem.spans.push(ele.spanName);
                });
        },
        handleSelection(item) {
            this.selectedInfo = item;
        },
        // 根据回路类型获取回路名
        getLoopName(type) {
            return _.find(loopList, ['value', type]).label;
        }
    },
    components: {  aButton }
});
</script>

<style lang="less" scoped>
/deep/ .h-modal-body {
    padding: 16px 32px;
}

.title {
    margin-bottom: 16px;
}

/deep/ .h-checkbox-wrapper {
    margin-left: 10px;
}

/deep/ .h-checkbox {
    margin-right: 5px;
}
</style>
