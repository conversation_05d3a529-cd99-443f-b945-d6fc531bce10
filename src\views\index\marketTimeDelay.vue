<template>
    <div class="main">
        <!-- 行情系统时延走势分析 -->
        <header>
            <a-title title="行情系统时延走势分析"></a-title>
            <div class="slot-box">
                <!-- 日期选择器 -->
                <h-date-picker v-model="modalInfo.data.date" class="line-box" type="date" :clearable="false" format="yyyy-MM-dd"
                    placement="bottom-end" placeholder="选择日期" @on-change="getData"></h-date-picker>
                <!-- 时间选择器 -->
                <h-time-picker v-model="modalInfo.data.time" class="line-box" format="HH:mm:ss" :clearable="false" type="timerange"
                    placement="bottom-end" placeholder="选择时间" @on-change="getData"></h-time-picker>
                <!-- 选择交易所 -->
                <h-select v-model="modalInfo.data.exchangeId" class="line-box" placeholder="选择交易所" :clearable="false"
                    @on-change="getData">
                    <h-option v-for="item in modalInfo.data.exchangeList" :key="item.label" :value="item.label">{{
                    item.value }}</h-option>
                </h-select>
                <!-- 选择链路 -->
                <h-select v-model="modalInfo.data.spanFlag" class="line-box" placeholder="链路状态" :clearable="false" @on-change="getData">
                    <h-option v-for="item in marketLoopList" :key="item.value" :value="item.value">{{ item.label }}
                    </h-option>
                </h-select>
                <!-- 选择数据类型 -->
                <h-select v-model="modalInfo.data.bizType" class="line-box" placeholder="选择数据类型" :clearable="false"
                    @on-change="getData">
                    <h-option v-for="item in checkList" :key="item.label" :value="item.label">{{ item.name }}</h-option>
                </h-select>
                <h-icon class="icon-setting" name="android-settings" size="26" color="#fff" @on-click="showSettingModel">
                </h-icon>
            </div>
        </header>

        <h-spin v-show="loading" fix>
            <div class="demo-spin-icon-load">
                <h-icon name="load-c" size="18"></h-icon>
            </div>
            <div>Loading</div>
        </h-spin>
        <!-- echarts -->
        <div v-if="!noDataStatus" id="container" :key="1" style="width: 97.5%; height: 45%;" class="echarts"
            :class="`${timeStamp.length}` === 0 ? 'hidden' : ''"></div>
        <div v-else style="width: 97.5%; height: 45%;">
            <no-data />
        </div>

        <!-- 行情组件时延透视 -->
        <a-title :title="`${brushTime[0] || ''} - ${brushTime[1] || ''} 行情组件时延透视`"></a-title>

        <!-- 透视列表 -->
        <div v-if="reFresh" class="box-charts">
            <scope-chart
                v-for="(item, index) in perspectiveList"
                :ref="`scopeChart${index}`"
                :key="`scopeChart${index}`"
                :modalInfo="modalInfo"
                :compareSpan="item.compareSpan"
                :compareSpanFlag="item.compareSpanFlag[modalInfo.data.exchangeId]" />
        </div>

        <scopeNotify :scopeNotifyInfo="scopeNotifyInfo">
            <template v-slot:footer>
                <a-button>确认</a-button>
            </template>
        </scopeNotify>

        <!-- 配置弹出框 -->
        <allLinkAnalyseConfig v-if="modalInfo.status" :modalInfo="modalInfo" @setInfoData="setInfoData" />
    </div>
</template>

<script>
import _ from 'lodash';
import * as echarts from 'echarts';
import { marketLoopList, checkList } from '@/config/exchangeConfig';
import { getMarketAllLinkDelay } from '@/api/httpApi';
import { formatDate } from '@/utils/utils';
import allLinkAnalyseConfig from '@/components/marketAllLink/allLinkAnalyseConfig';
import scopeNotify from '@/components/marketAllLink/scopeNotify.js';
import scopeChart from '@/components/marketAllLink/scopeChart.js';
import aTitle from '@/components/common/title/aTitle';
import aButton from '@/components/common/button/aButton';
import noData from '@/components/common/noData/noData';

export default {
    data() {
        const that = this;
        return {
            modalInfo: {
                status: false,
                data: {
                    date: new Date(),
                    spanFlag: 'NSQ',
                    spanFlags: [],
                    bizType: 'SM',
                    exchangeId: 'SSE',
                    time: ['09:15:00', '15:00:00'],
                    exchangeTime: 'ALL_DAY',
                    indicators: 'avg',
                    exchangeList: [] // 市场列表
                }
            },
            // 统计方式集合
            lineNormList: [
                {
                    label: 'avg',
                    value: '平均'
                },
                {
                    label: 'p95',
                    value: '95%'
                },
                {
                    label: 'p5',
                    value: '5%'
                },
                {
                    label: 'max',
                    value: '每秒最大'
                },
                {
                    label: 'min',
                    value: '每秒最小'
                }
            ],
            perspectiveList: [{
                compareSpan: 'MDP',
                compareSpanFlag: {
                    SSE: 'NSQ',
                    SZSE: 'NSQ'
                }
            }, {
                compareSpan: 'MDP',
                compareSpanFlag: {
                    SSE: 'FPGA',
                    SZSE: 'FPGA'
                }
            }, {
                compareSpan: 'MDP',
                compareSpanFlag: {
                    SSE: 'VDE',
                    SZSE: 'MDGW'
                }
            }],
            reFresh: true,
            marketLoopList, // 链路列表
            loading: false,
            loadingLink: false,
            noDataStatus: true,
            timeStamp: [],
            scopeNotifyInfo: {
                visible: false,
                range: [],
                data: {
                    startTime: '',
                    endTime: '',
                    original: {
                        name: '',
                        data: []
                    },
                    contrast: {
                        name: '',
                        data: []
                    }
                }
            },
            myChart: null,
            option: {
                grid: {
                    left: 50,
                    right: 50,
                    bottom: 45,
                    top: 39
                },
                tooltip: {
                    trigger: 'axis',
                    backgroundColor: 'rgba(88,94,106,0.50)',
                    borderColor: 'rgba(88,94,106,0.40)',
                    textStyle: {
                        color: '#fff',
                        fontSize: 12
                    },
                    formatter: function (params) {
                        let relVal = params[0].name;
                        for (let i = 0, l = params.length; i < l; i++) {
                            if (params[i].seriesName === '本地全系统时延走势分析(μs)') {
                                relVal += '<br/>' + params[i].marker + params[i].seriesName  + `<br/><span style="padding-left: 15px">${ _.find(that.lineNormList, o => { return o.label === that.modalInfo.data.indicators; }).value }：</span>` + params[i].value + ' μs';
                            } else {
                                relVal += '<br/>' + params[i].marker + params[i].seriesName  + `<br/><span style="padding-left: 15px">${_.find(that.lineNormList, o => { return o.label === that.modalInfo.data.indicators; }).value }：</span>` + params[i].value + ' ms';
                            }
                        }
                        return relVal;
                    }
                },
                dataZoom: [
                    {
                        type: 'inside',
                        start: 0,
                        end: 100
                    },
                    {
                        bottom: 9, // 下滑块距离x轴底部的距离
                        height: 18, // 下滑块手柄的高度调节
                        type: 'slider', // 类型,滑动块插件
                        show: true, // 是否显示下滑块
                        xAxisIndex: [0], // 选择的x轴
                        start: 90, // 初始数据显示多少
                        end: 100 // 初始数据最多显示多少
                    }
                ],
                legend: {
                    textStyle: {
                        color: '#fff',
                        fontSize: 11,
                        padding: [0, 0, 0, 10]
                    },
                    itemHeight: 1,
                    itemWidth: 16,
                    left: 60
                },
                xAxis: {
                    type: 'category',
                    boundaryGap: false,
                    axisLine: {
                        show: true,
                        lineStyle: {
                            color: '#31364a'
                        }
                    },
                    axisTick: {
                        show: false
                    },
                    axisLabel: {
                        color: '#fff'
                    },
                    data: []
                },
                yAxis: [{
                    name: 'μs',
                    nameTextStyle: {
                        color: '#fff'
                    },
                    type: 'value',
                    axisLine: {
                        show: true,
                        lineStyle: {
                            color: '#31364a'
                        }
                    },
                    axisLabel: {
                        color: '#fff'
                    },
                    splitLine: {
                        lineStyle: {
                            color: '#31364a'
                        }
                    }
                }, {
                    name: 'ms',
                    nameTextStyle: {
                        color: '#fff'
                    },
                    type: 'value',
                    axisLine: {
                        show: true,
                        lineStyle: {
                            color: '#31364a'
                        }
                    },
                    axisLabel: {
                        color: '#fff'
                    },
                    splitLine: {
                        lineStyle: {
                            color: '#31364a'
                        }
                    }
                }],
                toolbox: {
                    right: 10
                },
                brush: {
                // toolbox: ['lineX'],
                    xAxisIndex: 0,
                    brushMode: 'single',
                    brushType: 'lineX',
                    // transformable: false,
                    removeOnClick: true,
                    brushStyle: {
                        borderWidth: 1,
                        color: 'rgba(88,94,106,0.40)',
                        borderColor: 'rgba(88,94,106,0.40)'
                    },
                    throttleType: 'debounce',
                    throttleDelay: 600,
                    outOfBrush: {
                        colorAlpha: 0.1
                    }
                },
                series: [
                    {
                        name: '本地全系统时延走势分析(μs)',
                        type: 'line',
                        showSymbol: false,
                        lineStyle: {
                            color: '#2D8DE5'
                        },
                        itemStyle: {
                            color: '#2D8DE5'
                        },
                        data: []
                    },
                    {
                        name: '交易所到消费端总时延(ms)',
                        type: 'line',
                        showSymbol: false,
                        yAxisIndex: 1,
                        lineStyle: {
                            color: '#5AD8A6'
                        },
                        itemStyle: {
                            color: '#5AD8A6'
                        },
                        data: []
                    }
                ]
            },
            brushTime: [],
            operationName: {
                SSE: {
                    NSQ: {
                        Local: 'vde.x.1-nsq.x.2',
                        App: 'exchange.x.1-nsq.x.2'
                    },
                    FPGA: {
                        Local: 'vde.x.1-fpga.x.2',
                        App: 'exchange.x.1-fpga.x.2'
                    }
                },
                SZSE: {
                    NSQ: {
                        Local: 'mdgw.x.1-nsq.x.2',
                        App: 'exchange.x.1-nsq.x.2'
                    },
                    FPGA: {
                        Local: 'mdgw.x.1-fpga.x.2',
                        App: 'exchange.x.1-fpga.x.2'
                    }
                }
            },
            checkList // 行情列表
        };
    },
    methods: {
    // 获取延时数据
        async getLinkData() {
            if (!this.modalInfo.data.date) {
                this.$hMessage.warning('查询日期不得为空!');
                return;
            }
            return new Promise((resolve, reject) => {
                this.loadingLink = true;
                const date = formatDate(this.modalInfo.data.date);
                const flags = this.operationName[this.modalInfo.data.exchangeId][this.modalInfo.data.spanFlag];
                this.spanFlags = [flags.App, flags.Local];
                const param = {
                    exchangeId: this.modalInfo.data.exchangeId,
                    spans: [ // 链路指标
                        'ETC',
                        'LFL'
                    ],
                    indicators: [this.modalInfo.data.indicators],
                    spanFlags: this.spanFlags,
                    interval: 1000,
                    bizType: this.modalInfo.data.bizType,
                    startTime: `${date} ${this.modalInfo.data.time[0]}`,
                    endTime: `${date} ${this.modalInfo.data.time[1]}`
                };
                getMarketAllLinkDelay(param).then(res => {
                    let xAxis = [];
                    if (res.data.length) {
                        for (const i of res.data) {
                            if (i.trendChart.xaxis.length) {
                                xAxis = i.trendChart.xaxis;
                                break;
                            }
                        }
                    }
                    if (res.success && xAxis.length) {
                        this.option.xAxis.data = xAxis;
                        res.data.forEach(ele => {
                            if (ele.dimensionName === 'LFL') {
                                this.option.series[0].data = [];
                                ele.trendChart.yaxis.forEach(ele => {
                                    this.option.series[0].data.push(ele / 1000);
                                });
                            } else if (ele.dimensionName === 'ETC') {
                                this.option.series[1].data = [];
                                ele.trendChart.yaxis.forEach(ele => {
                                    this.option.series[1].data.push(ele / 1000000);
                                });
                            }
                        });
                        resolve(true);
                    } else {
                        resolve(false);
                        this.$hMessage.warning('查询的数据不存在');
                    }
                    this.loadingLink = false;
                }).catch(err => {
                    reject(err);
                });
            });
        },
        // 调用子组件透视行情数据
        handleScopeChartInit(times) {
            this.perspectiveList.forEach((ele, idx) => {
                this.$refs[`scopeChart${idx}`][0].init(times);
            });
        },
        // 获取图表数据
        async getData() {
        // 如果已经有数据在查询了，则跳过下一笔数据请求，直到第一笔查询结束
            if (this.loading) return;

            // 组件销毁重置
            this.reFresh = false;
            this.$nextTick(() => {
                this.reFresh = true;
            });
            this.loading = true;
            const result = await this.getLinkData();
            if (result) {
                this.noDataStatus = false;
                this.handleScopeChartInit();
                this.$nextTick(() => {
                    this.drawInit();
                });
            } else {
                this.myChart = null;
                this.noDataStatus = true;
            }
            this.loading = false;
        },
        drawInit() {
            this.myChart = echarts.init(document.getElementById('container'), '#262B40');
            this.draw();
            window.addEventListener('resize', () => {
                this.myChart.resize();
            });
        },
        // draw
        draw() {
            this.myChart && this.myChart.clear();
            this.myChart.setOption(this.option);
            this.myChart.dispatchAction({
            // 刷选模式的开关。使用此 action 可将当前鼠标变为可刷选状态。 事实上，点击 toolbox 中的 brush 按钮时，就是通过这个 action，将当前普通鼠标变为刷选器的。
                type: 'takeGlobalCursor',
                // 如果想变为“可刷选状态”，必须设置。不设置则会关闭“可刷选状态”。
                key: 'brush',
                brushOption: {
                // 参见 brush 组件的 brushType。如果设置为 false 则关闭“可刷选状态”。
                    brushType: 'lineX'
                }
            });
            /* 添加 */
            this.myChart.off('brushselected'); // 解绑事件处理函数（可根据情况而定是否需要，这里我这边会重绘几次表，所以需要解绑事件处理函数）。
            this.myChart.on('brushselected', (param) => {
                if (param.batch) {
                    const list = param.batch[0].areas;
                    if (list.length) {
                        const x1 = list[0].coordRange[0];
                        const x2 = list[0].coordRange[1];
                        this.brushTime = [this.option.xAxis.data[x1], this.option.xAxis.data[x2]];
                    } else {
                        this.brushTime = [];
                    }
                }
            });
        },
        // 显示设置弹窗
        showSettingModel() {
            this.modalInfo.status = true;
        },
        // 子组件修改modalInfoData的数据
        setInfoData(obj) {
            Object.keys(obj).forEach(key => {
                this.modalInfo.data[key] = obj[key];
            });
            this.getData();
        },
        // 选择链路
        changeLoopType(data){
            this.getData();
        },
        // 获取交易所列表
        async getConfigList() {
            // const that = this;
            return new Promise((resolve, reject) => {
            //     // TODO：getConfigInfo废弃
            //     getConfigInfo().then(res => {
            //         if (res.success) {
            //             const _list = ['SSE', 'SZSE'];
            //             that.modalInfo.data.exchangeId = _list[0] || '';
            //             that.modalInfo.data.exchangeList.length = 0;

                //             _list.forEach((item) => {
                //                 const exchange = exchangeList.find((ex) => ex.label === item);
                //                 if (exchange) {
                //                     that.modalInfo.data.exchangeList.push({
                //                         ...exchange,
                //                         exchangeTimeMap: res.data.exchangeTimeMap[item]
                //                     });
                //                 }
                //             });

            //             const timeObj = _.find(that.modalInfo.data.exchangeList, o => { return o.label === that.modalInfo.data.exchangeId; })?.exchangeTimeMap[that.modalInfo.data.exchangeTime];
            //             that.modalInfo.data.time = [timeObj.startTime, timeObj.endTime];
            //             resolve(true);
            //         }
            //         resolve(false);
            //     }).catch(err => {
            //         reject(err);
            //     });
            });
        }
    },
    async mounted() {
        await this.getConfigList();
        await this.getData();
    },
    watch: {
        brushTime(newVal) {
            this.handleScopeChartInit(newVal);
        }
    },
    components: { allLinkAnalyseConfig, scopeNotify, scopeChart, aTitle, aButton, noData }
};
</script>

<style lang="less" scoped>
@import "@/assets/css/transaction";
@import "@/assets/css/input";

.main {
    .box-charts {
        display: flex;
        width: 97.5%;
        height: calc(43% - 16px);
        margin: 15px auto;
    }
}
</style>
