/*
 * 自定义Table组件样式
*/
.a-table {
    #multi .cur-th {
        display: none;
    }

    .h-table {
        background: none;
        border-color: var(--table-border-color);
        margin: 10px 0;

        &::before {
            display: none;
        }

        &::after {
            display: none;
        }
    }

    .h-table-wrapper {
        width: 100%;
        height: auto;
        max-height: calc(100% - 50px);
        border: none;
    }

    .h-table th {
        background: var(--primary-color);
        border-bottom-color: #31364a;
        border-right-color: #383c51;
        color: var(--font-color);
        font-size: var(--font-size);
        font-weight: var(--font-weight);
    }

    .h-table td {
        border-bottom: 1px solid #31364a;
        border-right: 0;
        background: var(--input-bg-color);
        color: var(--font-color);
    }

    .h-table-row-checked,
    .h-table-row-checked:hover,
    .h-table-row-highlight,
    .h-table-row-highlight:hover,
    .h-table-row-hover,
    .h-table-row-hover td {
        background: #284871 !important;
    }

    .h-table-row-hover td button {
        color: var(--table-button-hover-bgcolor) !important;
    }

    .h-table-row .h-table-cell .h-btn-disable:hover {
        text-decoration: none;
        color: #969797;
    }

    .a-table-footer {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 0 5px;
    }

    .h-page {
        margin-left: auto;

        .h-page-total {
            color: #fff;
        }

        & > li {
            background-color: var(--primary-color);
            border-color: var(--border-color);

            & > a {
                color: var(--font-color);
            }
        }
    }

    .h-page-options {
        .h-select .h-select-selection,
        .h-select-selection {
            background-color: var(--primary-color);
            border-color: var(--border-color);
            color: var(--font-color);
        }
    }

    .h-page-item-active {
        background-color: var(--link-color) !important;
        border-color: var(--link-color) !important;
    }

    .h-page-options-elevator {
        color: var(--font-color);

        & > input {
            background-color: var(--input-bg-color);
            border-color: var(--border-color);
            color: var(--font-color);
        }
    }

    .h-page.mini .h-page-disabled {
        background-color: var(--input-bg-color);
    }

    td .h-btn-text {
        color: var(--link-color);
    }

    .h-btn.h-btn-text.h-btn-disable {
        color: #969797;
    }

    .h-table-row-hover .h-btn-text {
        color: var(--font-color);
    }

    td .h-btn-text:hover {
        color: var(--link-color);
        text-decoration: underline;
    }

    .h-table-tiptext {
        width: 100% !important;
    }

    .h-table-wrapper > .h-spin-fix {
        position: absolute;
        top: 0;
        left: 0;
        z-index: 8;
        width: 100%;
        height: 100%;
        border: var(--table-border);
        background-color: var(--input-bg-color);
    }

    .h-table-fixed-body-shadow {
        border: none;
    }

    .h-page-item-jump-next::after,
    .h-page-tem-jump-prev::after {
        content: "\2022\2022\2022";
    }

    .h-table-fixed-right-patch {
        background-color: var(--input-bg-color);
        border-bottom: var(--table-border);
    }

    // checkbox 公共样式
    .h-checkbox.h-checkbox-disabled > .h-checkbox-inner {
        background-color: var(--border-color) !important;
        border-color: var(--border-color) !important;
    }

    .h-checkbox.h-checkbox-disabled + span {
        color: var(--border-color);
    }

    .h-checkbox + span {
        color: var(--font-color);
    }

    .h-checkbox-inner {
        border: 1px solid var(--font-color);
        border-radius: 2px;
        background: var(--input-bg-color);
    }

    .h-checkbox-inner::after {
        border: none;
    }

    .h-checkbox-checked > .h-checkbox-inner::after {
        border: 2px solid var(--font-color);
        border-top: 0;
        border-left: 0;
    }

    .h-checkbox-checked > .h-checkbox-inner {
        border-color: var(--link-color) !important;
        background: var(--link-color) !important;
    }
}

.self-table {
    .search-box {
        display: flex;
        justify-content: flex-end;
    }

    .page-box {
        display: flex;
        justify-content: flex-end;
    }
}
