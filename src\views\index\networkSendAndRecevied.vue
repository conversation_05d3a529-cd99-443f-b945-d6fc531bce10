<template>
    <div class="main">
        <div class="title">
            <a-title :title="$getProductType(productInfo.productType)">
                <slot>
                    <h-select
                        v-show="productList.length > 1"
                        v-model="productInstNo"
                        placeholder="请选择"
                        class="title-select"
                        :positionFixed="true"
                        :clearable="false"
                        @on-change="checkProduct">
                        <h-option
                            v-for="item in productList"
                            :key="item.id"
                            :value="item.productInstNo">
                            {{ item.productName }}
                        </h-option>
                    </h-select>
                </slot>
            </a-title>
        </div>
        <a-loading v-if="loading"></a-loading>
        <h-tabs v-model="tabName" class="product-box" @on-click="tabClick(tabName)">
            <h-tab-pane v-for="item in editableTabs" :key="item.name" :label="item.describe" :name="item.name">
                <component :is="item.name" :ref="item.name" :productInstNo="productInstNo"></component>
            </h-tab-pane>
        </h-tabs>
    </div>
</template>

<script>
import _ from 'lodash';
import { mapState, mapActions } from 'vuex';
import aLoading from '@/components/common/loading/aLoading';
import aTitle from '@/components/common/title/aTitle';
import netResource from '@/components/networkSendAndRecevied/netResource.vue';
import netSend from '@/components/networkSendAndRecevied/netSend.vue';
import netThrouth from '@/components/networkSendAndRecevied/netThrouth.vue';
import netRecord from '@/components/networkSendAndRecevied/netRecord.vue';
export default {
    name: 'ProductDataStorage',
    components: { aTitle, aLoading, netResource, netThrouth, netSend, netRecord },
    data() {
        return {
            loading: false,
            productInfo: {},
            productInstNo: '',
            tabName: 'netResource',
            editableTabs: [
                {
                    name: 'netResource',
                    describe: '应用抓包',
                    timerSwitch: false,
                    timerInterval: 10
                },
                {
                    name: 'netThrouth',
                    describe: '抓包回放',
                    timerSwitch: false,
                    timerInterval: 10
                },
                {
                    name: 'netSend',
                    describe: '应用发包',
                    timerSwitch: false,
                    timerInterval: 10
                },
                {
                    name: 'netRecord',
                    describe: '发包用例',
                    timerSwitch: false,
                    timerInterval: 10
                }
            ]
        };
    },
    async mounted() {
        await this.init();
    },
    methods: {
        ...mapActions({ getProductList: 'product/getProductListLight' }),
        async init() {
            try {
                this.loading = true;
                await this.getProductList({ filter: 'excludeLdpApm' });
                const id = localStorage.getItem('productInstNo');
                this.productInstNo = _.find(this.productList, ['productInstNo', id])?.productInstNo || this.productList[0].productInstNo;
                this.productInfo = _.find(this.productList, ['productInstNo', this.productInstNo]);
            } catch (e) {
                console.error(e);
            } finally {
                this.loading = false;
            }
        },
        checkProduct(val) {
            this.loading = true;
            this.productInfo = _.find(this.productList, ['productInstNo', val]) || {};
            localStorage.setItem('productInstNo', this.productInfo.productInstNo);
            setTimeout(() => {
                this.loading = false;
                this.tabClick(this.tabName);
            }, 500);
        },
        tabClick(name) {
            // 切换tab时清楚抓包定时器
            if (name !== 'netResource'){
                this.$refs?.['netResource']?.[0] && this.$refs['netResource'][0].clearPolling();
            }
            this.$refs?.[name]?.[0] && this.$refs[name][0].initData();
        }
    },
    computed: {
        ...mapState({
            productList: (state) => {
                return state.product.productListLight || [];
            }
        })
    },
    beforeDestroy() {
    }
};
</script>

<style scoped lang="less">
@import url("@/assets/css/tab.less");

.main {
    .title {
        min-width: 900px;

        p {
            display: inline-block;
            color: var(--font-color);
            font-size: var(--title-font-size);
            position: absolute;
            left: 50%;
            transform: translateX(-50%);
        }

        .title-select {
            float: right;
            margin-right: 4px;
            top: 5px;
            min-width: 200px;
            width: auto;
        }
    }

    .product-box {
        min-width: 900px;

        /deep/ .apm-box {
            margin-top: 0;
            height: 100%;
            background: none;
        }

        /deep/ .right-box {
            background: none;
            padding-left: 10px;
        }
    }

    .h-tabs-tabpane {
        overflow: hidden;
    }
}
</style>
