<template>
      <h-poptip class="func-poptip-box" placement="top-end" autoPlacement width="450" title="记录字段信息" transfer customTransferClassName="apm-poptip">
        <span class="mouse-span" :title="data">{{ data }}</span>
        <div slot="content">
            <h-table
                class="pop-table"
                :columns="columns"
                :data="tableData"
                maxHeight="140"
                showTitle
                disabledHover
            />
        </div>
      </h-poptip>
  </template>
<script>
export default {
    name: 'RecordMsgPoptip',
    props: {
        data: {
            type: String | Object,
            default: ''
        },
        tableData: {
            type: Array,
            default: () => []
        }
    },
    components: {
    },
    data() {
        return {
            columns: [
                {
                    title: '字段名',
                    key: 'key',
                    ellipsis: true
                },
                {
                    title: '字段值',
                    key: 'value',
                    ellipsis: true
                }
            ]
        };
    },
    mounted(){
    },
    methods: {
    },
    beforeDestroy() {
    }
};

</script>
<style lang="less" scoped>
@import url("@/assets/css/poptip-1.less");

/deep/ .h-table {
    background-color: transparent;
    width: 100% !important;

    .h-table-header,
    table {
        width: 100% !important;
    }
}

/deep/ .h-table-wrapper {
    border: none;
}

/deep/ .h-table::before,
/deep/ .h-table::after {
    display: none;
}

/deep/ .h-table th {
    height: 28px;
    background-color: #636974d9;
    color: var(--font-color);
    border: none;
}

/deep/ .h-table td {
    height: 28px;
    font-size: 12px;
    background-color: #ccc0;
    color: var(--font-color);
    border-bottom: 1px solid var(--poptip-line-color);
}

/deep/ .h-table-cell {
    font-weight: normal;
    padding: 0 8px;
}

/deep/ .h-table-body::-webkit-scrollbar {
    height: 4px;
}

/deep/ .h-table-body::-webkit-scrollbar-thumb {
    border-radius: 6px;
    background-color: #d1d1d1;
}

/deep/ .h-table-tiptext {
    color: #d1d1d1;
}

.func-poptip-box {
    width: 100%;
}

/deep/ .h-poptip-rel {
    width: 100%;
    word-break: keep-all;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

/deep/ .mouse-span {
    margin-left: 2px;
    cursor: pointer;

    &:hover {
        border-bottom: 1px solid var(--font-color);
    }
}
</style>
