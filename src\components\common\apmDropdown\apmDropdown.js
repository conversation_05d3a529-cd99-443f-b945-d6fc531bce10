/*
 * @Description: APM自定义下拉菜单
 * @Author: <PERSON><PERSON>
 * @Date: 2023-08-29 16:22:00
 * @LastEditTime: 2023-08-29 16:38:43
 * @LastEditors: <PERSON><PERSON>
 */
export default {
    name: 'ApmDropdown',
    props: {
        dropList: {
            type: Array,
            default: () => []
        }
    },
    data() {
        return {
            visible: false
        };
    },
    methods: {
        handleOpen() {
            this.visible = true;
        },
        handleClose() {
            this.visible = false;
        }
    },
    render() {
        return this.dropList.map(item => {
            return <h-dropdown
                trigger="custom"
                visible={this.visible}>
                <a href="javascript:void(0)" v-on:click={this.handleOpen}>
                    {item.label}
                    <h-icon name="unfold"></h-icon>
                </a>
                <h-dropdown-menu slot="list">
                    <p>常用于各种自定义下拉内容的场景。</p>
                    <div style="text-align: right; margin: 10px;">
                        <h-button type="primary" v-on:click={this.handleClose}>关闭</h-button>
                    </div>
                </h-dropdown-menu>
            </h-dropdown>;
        });
    }
};
