import './menuLayout.less';
import aButton from '@/components/common/button/aButton';
export default {
    name: 'apm-box',
    props: {
        menuTitle: {
            type: String,
            default: undefined
        },
        // 底部button文本
        footer: {
            type: String,
            default: undefined
        },
        // 收起时左侧盒子最小宽度
        menuMinWidth: {
            type: Number,
            default: 0
        },
        showSearch: {
            type: Boolean,
            default: false
        },
        placeholder: {
            type: String,
            default: '查询'
        },
        // 菜单列表
        menuList: {
            type: Array,
            default: () => {
                return [];
            }
        },
        // 菜单项title所绑定的menuList的属性名
        titleAttribute: {
            type: String,
            default: ''
        },
        // 菜单项绑定id属性名
        menuItemId: {
            type: String,
            default: ''
        },
        // 菜单项图标绑定属性名
        liClassAttribute: {
            type: String,
            default: ''
        },
        productAlerts: {
            type: Array,
            default: () => {
                return [];
            }
        }
    },
    data() {
        return {
            lastLeftX: 224,
            leftBarWidth: 224,
            iconType: false, // 0:收起, 1:展开
            startX: 0,
            searchText: '',
            menuListCopy: this.menuList,
            selectedMenu: null
        };
    },
    methods: {
        // 动态绑定菜单节点样式
        bindClass(environ, selected) {
            const classInfo = { 'menu-selected': selected,  'pro-environ': environ === 'produce', 'test-environ': environ === 'test' };
            return classInfo;
        },

        // 左侧box显示与隐藏
        changeShowHidden() {
            this.iconType = !this.iconType;
            this.$emit('menu-fold', this.iconType);
            this.lastLeftX = this.iconType ? this.menuMinWidth : this.leftBarWidth;
        },

        // 左侧宽度拖动
        mouseDown(e) {
            if (this.iconType) return;
            this.startX = e.clientX;
            this.mouseMove(e);
            this.mouseUp();
            document.addEventListener('mousemove', this.mouseMove);
            document.addEventListener('mouseup', this.mouseUp);
        },
        mouseUp() {
            this.leftBarWidth = this.lastLeftX;
            document.removeEventListener('mousemove', this.mouseMove);
            document.removeEventListener('mouseup', this.mouseUp);
        },
        mouseMove(e) {
            e.preventDefault();
            e.stopPropagation();
            if (e.clientX < 200) return;
            const offset = e.clientX - this.startX;
            if (offset) {
                this.lastLeftX = offset + this.leftBarWidth;
            }
        },

        // 切换菜单
        checkMenu(item) {
            this.selectedMenu = item;
            this.$emit('check-menu', item);
        },
        // 底部btn点击事件
        footBtnClick(item) {
            this.$emit('footer-click', item);
        },
        // 菜单模糊搜索
        fuzzySearch(val) {
            this.menuListCopy = val ? this.menuList.filter(item => item[this.titleAttribute].search(val) !== -1) : this.menuList;
        },
        // 无筛选初始化菜单
        initMenu(item) {
            this.checkMenu(item);
            this.fuzzySearch('');
        }
    },
    computed: {
        leftStyle() {
            return { width: this.lastLeftX + 'px' };
        },
        liStyle() {
            const style = this.iconType ? { display: 'none' } : null;
            return style;
        }
    },
    watch: {
        searchText(val) {
            const regVal = val ? new RegExp(`${val}`, 'i') : '';
            this.fuzzySearch(regVal);
        }
    },
    render() {
        const { leftStyle, liStyle, menuTitle, menuListCopy, liClassAttribute, titleAttribute, iconType, showSearch, placeholder, selectedMenu } = this;
        return <div class="apm-box">
            <div class="left-box" style={leftStyle}>
                {
                    this.$slots.menu ? this.$slots.menu
                        : <div class="menu">
                            {menuTitle ? <div class="header-menu" title={menuTitle}>&nbsp;&nbsp;&nbsp;{menuTitle}</div> : ''}
                            {showSearch ? <h-input v-model={this.searchText} placeholder={placeholder} icon="android-search" style={liStyle}></h-input> : ''}
                            <ul class="menu-ul" style={this.footer ? 'height: calc(100% - 95px)' : 'height: calc(100% - 54px)'}>
                                {menuListCopy.map((item) => {
                                    return <li key={item.id} class={this.bindClass(item[liClassAttribute], this.menuItemId ? item?.[this.menuItemId] === selectedMenu?.[this.menuItemId]
                                        : item?.[this.titleAttribute] === selectedMenu?.[this.titleAttribute])} onclick={() => this.checkMenu(item)}>
                                        {this.productAlerts.indexOf(item.productId) > -1 ? <span>⚠️</span> : ''}
                                        <span style={liStyle}>{item[titleAttribute]}</span>
                                    </li>;
                                })}
                            </ul>
                            {this.footer ? <aButton type="danger" class="menu-footer" style={liStyle} onclick={() => this.footBtnClick(selectedMenu)}>{this.footer}</aButton> : ''}
                        </div>
                }
                <div class="x-resizer" onMousedown={this.mouseDown}></div>
                <span class="cate-switch" title={iconType ? '展开' : '收起'} onClick={this.changeShowHidden}>
                    <h-icon name={iconType ? 'ios-arrow-forward' : 'ios-arrow-back'} size='14' color="#cacfd4"></h-icon>
                </span>
            </div>

            <div class="right-box">
                {this.$slots.right}
            </div>
        </div>;
    }
};
