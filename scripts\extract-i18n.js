#!/usr/bin/env node

const fs = require('fs');
const path = require('path');

/**
 * 中文文案提取脚本
 * 遍历项目中的所有Vue、JS文件，提取中文文案并按路由维度组织生成国际化键值对文件
 */

class I18nExtractor {
    constructor() {
        this.srcDir = path.join(__dirname, '../src');
        this.outputDir = path.join(__dirname, '../src/locales/extracted');
        this.routeConfig = this.loadRouteConfig();
        this.extractedTexts = new Map(); // 存储提取的文案，key为路由路径，value为文案数组
        this.allTexts = new Set(); // 存储所有提取的文案，用于去重
        
        // 中文字符正则表达式
        this.chineseRegex = /[\u4e00-\u9fff]+/g;
        
        // 各种文案提取的正则表达式
        this.patterns = {
            // Vue template中的中文文本
            templateText: /(?:>|\s)([^<>]*[\u4e00-\u9fff][^<>]*?)(?=<|$)/g,
            // 属性值中的中文
            attributeValue: /(?:title|placeholder|label|content|text)=["']([^"']*[\u4e00-\u9fff][^"']*?)["']/g,
            // JavaScript字符串中的中文
            jsString: /(?:['"`])([^'"`]*[\u4e00-\u9fff][^'"`]*?)(?:['"`])/g,
            // 对象属性值中的中文
            objectValue: /(?::\s*['"`])([^'"`]*[\u4e00-\u9fff][^'"`]*?)(?:['"`])/g,
            // 函数调用参数中的中文
            functionParam: /\$(?:hMessage|message|confirm|alert|prompt)\.(?:info|success|warning|error|confirm)\s*\(\s*['"`]([^'"`]*[\u4e00-\u9fff][^'"`]*?)['"`]/g
        };
    }

    /**
     * 加载路由配置
     */
    loadRouteConfig() {
        try {
            const routerPath = path.join(this.srcDir, 'router/router.js');
            const routerContent = fs.readFileSync(routerPath, 'utf-8');
            return this.parseRouteConfig(routerContent);
        } catch (error) {
            console.warn('无法加载路由配置:', error.message);
            return {};
        }
    }

    /**
     * 解析路由配置
     */
    parseRouteConfig(content) {
        const routes = {};
        
        // 提取路由名称和路径的映射关系
        const routePattern = /name:\s*['"`]([^'"`]+)['"`][\s\S]*?path:\s*['"`]([^'"`]+)['"`]/g;
        let match;
        
        while ((match = routePattern.exec(content)) !== null) {
            const [, name, path] = match;
            routes[name] = path;
        }
        
        return routes;
    }

    /**
     * 根据文件路径推断路由信息
     */
    inferRouteFromPath(filePath) {
        const relativePath = path.relative(this.srcDir, filePath);
        
        // 处理views目录下的文件
        if (relativePath.startsWith('views/index/')) {
            const fileName = path.basename(filePath, path.extname(filePath));
            return `index-${fileName}`;
        }
        
        // 处理components目录下的文件
        if (relativePath.startsWith('components/')) {
            const parts = relativePath.split('/');
            if (parts.length >= 2) {
                return parts[1]; // 使用组件目录名作为路由标识
            }
        }
        
        // 其他文件使用目录结构
        const parts = relativePath.split('/');
        return parts.slice(0, -1).join('-') || 'common';
    }

    /**
     * 提取文件中的中文文案
     */
    extractFromFile(filePath) {
        try {
            const content = fs.readFileSync(filePath, 'utf-8');
            const texts = new Set();
            
            // 使用各种正则表达式提取中文文案
            Object.entries(this.patterns).forEach(([patternName, regex]) => {
                let match;
                const globalRegex = new RegExp(regex.source, regex.flags);
                
                while ((match = globalRegex.exec(content)) !== null) {
                    const text = match[1];
                    if (text && this.isValidChineseText(text)) {
                        texts.add(text.trim());
                    }
                }
            });
            
            return Array.from(texts);
        } catch (error) {
            console.warn(`提取文件 ${filePath} 失败:`, error.message);
            return [];
        }
    }

    /**
     * 验证是否为有效的中文文案
     */
    isValidChineseText(text) {
        // 去除首尾空白
        text = text.trim();
        
        // 过滤条件
        if (!text || text.length < 1) return false;
        if (text.length > 100) return false; // 过长的文本可能不是文案
        if (/^[\d\s\-_\.\/\\]+$/.test(text)) return false; // 纯数字、符号
        if (/^[a-zA-Z\s\-_\.]+$/.test(text)) return false; // 纯英文
        if (!/[\u4e00-\u9fff]/.test(text)) return false; // 必须包含中文
        
        // 排除一些常见的非文案内容
        const excludePatterns = [
            /^console\./,
            /^import\s/,
            /^export\s/,
            /^function\s/,
            /^var\s/,
            /^let\s/,
            /^const\s/,
            /^\$\w+/,
            /^@\w+/,
            /^#\w+/,
            /^\.\w+/,
            /^\/\//,
            /^\/\*/,
            /^\*\//
        ];
        
        return !excludePatterns.some(pattern => pattern.test(text));
    }

    /**
     * 生成国际化key
     */
    generateI18nKey(text, routeKey) {
        // 移除特殊字符，保留中文、英文、数字
        let key = text.replace(/[^\u4e00-\u9fff\w\s]/g, '');
        
        // 将中文转换为拼音或使用英文描述（这里简化处理）
        key = this.chineseToKey(key);
        
        // 转换为小驼峰格式
        key = this.toCamelCase(key);
        
        // 添加路由前缀
        return `${routeKey}.${key}`;
    }

    /**
     * 中文转换为英文key（简化版本）
     */
    chineseToKey(text) {
        const commonMappings = {
            '查询': 'query',
            '搜索': 'search',
            '添加': 'add',
            '新增': 'add',
            '编辑': 'edit',
            '修改': 'edit',
            '删除': 'delete',
            '保存': 'save',
            '取消': 'cancel',
            '确认': 'confirm',
            '提交': 'submit',
            '重置': 'reset',
            '刷新': 'refresh',
            '导出': 'export',
            '导入': 'import',
            '上传': 'upload',
            '下载': 'download',
            '配置': 'config',
            '设置': 'setting',
            '管理': 'manage',
            '监控': 'monitor',
            '观测': 'observation',
            '数据': 'data',
            '表格': 'table',
            '列表': 'list',
            '详情': 'detail',
            '信息': 'info',
            '状态': 'status',
            '类型': 'type',
            '名称': 'name',
            '时间': 'time',
            '日期': 'date',
            '开始': 'start',
            '结束': 'end',
            '成功': 'success',
            '失败': 'failed',
            '错误': 'error',
            '警告': 'warning',
            '提示': 'tip',
            '请选择': 'pleaseSelect',
            '请输入': 'pleaseInput',
            '暂无数据': 'noData',
            '加载中': 'loading',
            '操作': 'operation',
            '功能': 'function',
            '模块': 'module',
            '页面': 'page',
            '组件': 'component'
        };
        
        // 先尝试直接映射
        if (commonMappings[text]) {
            return commonMappings[text];
        }
        
        // 部分匹配
        for (const [chinese, english] of Object.entries(commonMappings)) {
            if (text.includes(chinese)) {
                text = text.replace(chinese, english);
            }
        }
        
        // 如果还有中文，使用拼音首字母或描述性英文
        if (/[\u4e00-\u9fff]/.test(text)) {
            // 这里可以集成拼音库，暂时使用简化处理
            text = text.replace(/[\u4e00-\u9fff]/g, 'text');
        }
        
        return text;
    }

    /**
     * 转换为小驼峰格式
     */
    toCamelCase(str) {
        return str
            .replace(/[^\w\s]/g, '') // 移除特殊字符
            .split(/\s+/) // 按空格分割
            .filter(word => word.length > 0)
            .map((word, index) => {
                if (index === 0) {
                    return word.toLowerCase();
                }
                return word.charAt(0).toUpperCase() + word.slice(1).toLowerCase();
            })
            .join('');
    }

    /**
     * 遍历目录提取文案
     */
    extractFromDirectory(dir) {
        try {
            console.log(`📁 扫描目录: ${path.relative(this.srcDir, dir) || '.'}`);
            const files = fs.readdirSync(dir);

            files.forEach(file => {
                const filePath = path.join(dir, file);
                const stat = fs.statSync(filePath);

                if (stat.isDirectory()) {
                    // 跳过node_modules等目录
                    if (!['node_modules', 'dist', '.git', '.vscode'].includes(file)) {
                        this.extractFromDirectory(filePath);
                    }
                } else if (stat.isFile()) {
                    // 只处理Vue和JS文件
                    if (/\.(vue|js)$/.test(file)) {
                        console.log(`📄 处理文件: ${path.relative(this.srcDir, filePath)}`);
                        const texts = this.extractFromFile(filePath);
                        if (texts.length > 0) {
                            console.log(`   ✅ 提取到 ${texts.length} 条文案`);
                            const routeKey = this.inferRouteFromPath(filePath);

                            if (!this.extractedTexts.has(routeKey)) {
                                this.extractedTexts.set(routeKey, []);
                            }

                            texts.forEach(text => {
                                if (!this.allTexts.has(text)) {
                                    this.allTexts.add(text);
                                    this.extractedTexts.get(routeKey).push({
                                        text,
                                        file: path.relative(this.srcDir, filePath),
                                        key: this.generateI18nKey(text, routeKey)
                                    });
                                }
                            });
                        }
                    }
                }
            });
        } catch (error) {
            console.error(`❌ 扫描目录 ${dir} 失败:`, error.message);
        }
    }

    /**
     * 生成国际化文件
     */
    generateI18nFiles() {
        // 确保输出目录存在
        if (!fs.existsSync(this.outputDir)) {
            fs.mkdirSync(this.outputDir, { recursive: true });
        }
        
        const zhCN = {};
        const enUS = {};
        const mapping = {}; // 文案映射关系
        
        // 按路由组织数据
        this.extractedTexts.forEach((texts, routeKey) => {
            zhCN[routeKey] = {};
            enUS[routeKey] = {};
            mapping[routeKey] = [];
            
            texts.forEach(({ text, file, key }) => {
                const simpleKey = key.split('.').pop(); // 去掉路由前缀
                zhCN[routeKey][simpleKey] = text;
                enUS[routeKey][simpleKey] = text; // 英文版本暂时使用中文，需要后续翻译
                
                mapping[routeKey].push({
                    key: simpleKey,
                    chinese: text,
                    english: text,
                    file,
                    fullKey: key
                });
            });
        });
        
        // 写入文件
        this.writeJsonFile('zh-CN-extracted.js', zhCN);
        this.writeJsonFile('en-US-extracted.js', enUS);
        this.writeJsonFile('mapping.json', mapping);
        
        // 生成统计报告
        this.generateReport();
    }

    /**
     * 写入JSON文件
     */
    writeJsonFile(filename, data) {
        const filePath = path.join(this.outputDir, filename);
        
        if (filename.endsWith('.js')) {
            const content = `export default ${JSON.stringify(data, null, 2)};`;
            fs.writeFileSync(filePath, content, 'utf-8');
        } else {
            fs.writeFileSync(filePath, JSON.stringify(data, null, 2), 'utf-8');
        }
        
        console.log(`✅ 生成文件: ${filePath}`);
    }

    /**
     * 生成提取报告
     */
    generateReport() {
        const report = {
            summary: {
                totalTexts: this.allTexts.size,
                totalRoutes: this.extractedTexts.size,
                extractTime: new Date().toISOString()
            },
            routes: {}
        };
        
        this.extractedTexts.forEach((texts, routeKey) => {
            report.routes[routeKey] = {
                count: texts.length,
                texts: texts.map(({ text, file }) => ({ text, file }))
            };
        });
        
        const reportPath = path.join(this.outputDir, 'extraction-report.json');
        fs.writeFileSync(reportPath, JSON.stringify(report, null, 2), 'utf-8');
        
        console.log('\n📊 提取统计:');
        console.log(`总计提取中文文案: ${this.allTexts.size} 条`);
        console.log(`涉及路由/模块: ${this.extractedTexts.size} 个`);
        console.log(`详细报告: ${reportPath}`);
    }

    /**
     * 执行提取
     */
    run() {
        console.log('🚀 开始提取中文文案...');
        console.log(`源目录: ${this.srcDir}`);
        console.log(`输出目录: ${this.outputDir}`);
        
        this.extractFromDirectory(this.srcDir);
        this.generateI18nFiles();
        
        console.log('✨ 提取完成!');
    }
}

// 执行脚本
if (require.main === module) {
    const extractor = new I18nExtractor();
    extractor.run();
}

module.exports = I18nExtractor;
