<!-- 服务器配置弹窗 -->
<template>
    <div>
        <h-msg-box-safe
            v-model="modalData.status"
            :mask-closable="false"
            :title="configTitle"
            width="600"
            @submit.native.prevent
            @on-open="handleOpen">
            <h-form
                ref="formValidate"
                :model="formValidate"
                :label-width="80">
                <template v-if="configMode === 'room'">
                    <h-form-item
                        label="机房"
                        prop="roomId"
                        required>
                        <h-select
                            v-model="formValidate.roomId"
                            placeholder="请选择要关联的机房">
                            <h-option
                                v-for="item in roomsList"
                                :key="item.roomId"
                                :value="item.roomId">
                                {{ item.roomName }}
                            </h-option>
                        </h-select>
                    </h-form-item>
                </template>
                <template v-if="configMode === 'host'">
                    <h-form-item
                        label="主机别名"
                        prop="hostNameAlias"
                        required>
                        <h-input
                            v-model="formValidate.hostNameAlias"
                            :maxlength="30"
                            placeholder="请输入主机别名">
                        </h-input>
                    </h-form-item>
                </template>
            </h-form>

            <template v-slot:footer>
                <h-button @click="modalData.status = false">取消</h-button>
                <h-button
                    type="primary"
                    :loading="loading"
                    @click="submitConfig">确定
                </h-button>
            </template>
        </h-msg-box-safe>
    </div>
</template>

<script>
import { setProductHosts, getMachineRoomInfo } from '@/api/productApi';

export default {
    name: 'ServerConfigModal',
    components: { },
    props: {
        productId: {
            type: String,
            default: ''
        },
        modalInfo: {
            type: Object,
            default: null
        },
        configMode: {
            type: String,
            default: 'room' // 默认值可以是 'room' 或 'host'
        }
    },
    data() {
        return {
            modalData: this.modalInfo,
            formValidate: {
                roomId: '',
                hostNameAlias: ''
            },
            roomsList: [],
            loading: false
        };
    },
    computed: {
        configTitle() {
            return this.configMode === 'room' ? '关联机房' : '修改主机别名';
        }
    },
    methods: {
        async handleOpen() {
            if (this.configMode === 'room') {
                await this.getMachineRoomInfo();
                this.formValidate.roomId = this.modalData?.roomId;
            } else if (this.configMode === 'host') {
                this.formValidate.hostNameAlias = this.modalData?.hostNameAlias;
            }
        },
        async getMachineRoomInfo() {
            const res = await getMachineRoomInfo({
                productId: this.productId
            });
            if (res.code === '200') {
                this.roomsList = res?.data || [];
            }
        },
        submitConfig() {
            this.$refs['formValidate'].validate((valid) => {
                if (valid) {
                    this.setProductHosts();
                }
            });
        },
        async setProductHosts() {
            this.loading = true;
            try {
                const params = {
                    productId: this.productId,
                    ids: [this.modalData.id],
                    loginUser: this.modalData.loginUser,
                    loginPwd: this.modalData.loginPwd,
                    sshPort: this.modalData.sshPort,
                    roomId: this.configMode === 'room' ? this.formValidate.roomId : this.modalData.roomId,
                    hostNameAlias: this.configMode === 'host' ? this.formValidate.hostNameAlias : this.modalData.hostNameAlias
                };
                const res = await setProductHosts(params);
                if (res.code === '200') {
                    this.$emit('update');
                    this.$hMessage.success('操作成功');
                    this.modalData.status = false;
                } else if (res.code?.length === 8) {
                    this.$hMessage.error(res.message);
                }
            } finally {
                this.loading = false;
            }
        }
    }
};
</script>

<style lang="less" scoped>
.auto-height-select {
    height: auto;

    /deep/ .h-select-selection {
        height: auto;
    }
}
</style>
