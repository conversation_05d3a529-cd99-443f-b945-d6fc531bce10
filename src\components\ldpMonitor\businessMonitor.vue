<template>
    <div class="main">
        <div v-if="traceModelConfigs.length" class="wrapper">
            <template>
                <a-title title="展示链路拓扑">
                    <slot>
                        <span style="position: absolute; left: 130px;">
                            <h-switch v-model="switch1"></h-switch>
                        </span>
                        <!--度量数据类型选择-->
                        <h-select v-model="secondType" class="title-select second-select" :clearable="false" @on-change="handleExchange">
                            <h-option v-for="item in secondTypeList" :key="item.key" :value="item.key">{{ item.value
                            }}</h-option>
                        </h-select>
                        <!--统计周期选择-->
                        <h-select v-model="interval" class="title-select unit-select" :clearable="false" @on-change="handleExchange">
                            <h-option v-for="item in intervalList" :key="item.value" :value="item.value">{{ item.label
                            }}</h-option>
                        </h-select>
                        <!--度量数据类型选择-->
                        <h-select v-model="timeValue" class="title-select time-interval" :clearable="false" @on-change="handleExchange">
                            <h-option v-for="item in timeIntervalList" :key="item.value" :value="item.value">{{ item.label
                            }}</h-option>
                        </h-select>
                        <!--TraceType选择-->
                        <h-select v-model="bizTraceType" class="title-select" :clearable="false" style="width: 180px;" @on-change="handleTopoChange">
                            <h-option v-for="item in bizTraceTypes" :key="item.traceType" :value="item.traceType" setDefSelect>{{ item.traceTypeAlias
                            }}</h-option>
                        </h-select>
                        <!--业务选择-->
                        <h-select v-model="subBizType" class="title-select" :clearable="false" style="width: 180px;" @on-change="handleBusinessChange">
                            <h-option v-for="item in traceModelConfigs" :key="item.subBizType" :value="item.subBizType">{{ item.subBizTypeAlias
                            }}</h-option>
                        </h-select>
                    </slot>
                </a-title>
                <div v-if="switch1" class="topo-box">
                    <all-link-topo v-if="penetrationViewMode" ref="topo" :template="penetrationViewMode"
                        :clickable="true" :selectedNode="selectedNode" style="position: absolute; left: 0; top: 0;"
                        @selectedChange="handleSelectedChange" />
                    <app-topo v-else-if="applicationViewMode && !penetrationViewMode" ref="topo" :template="applicationViewMode"
                        :clickable="true" :selectedGroup="selectedGroup" style="position: absolute; left: 0; top: 0;"
                        :hoveredGroup="hoveredGroup" @on-hover="handleHoverGroup" @on-click="handleCheckGroup" />
                    <no-data v-else text="该模型文件暂无拓扑结构"/>
                </div>
                <h-tabs v-model="tabName" class="echart-box" :style="updateChartBoxHeight()">
                    <h-tab-pane label="时延趋势" name="tab1">
                        <div v-if="monitorList.length && (applicationViewMode || penetrationViewMode)" style="height: 100%;">
                            <monitor
                                ref="monitor"
                                :monitorList="monitorList" />
                        </div>
                        <div v-else style="width: 100%; height: 100%;">
                            <no-data text="当前实例无采集时延走势信息" />
                        </div>
                    </h-tab-pane>
                </h-tabs>
            </template>
        </div>
        <no-data v-else text="暂无数据" />
        <a-loading v-if="loading"></a-loading>
    </div>
</template>

<script>
import _ from 'lodash';
import { isJSON, cutZero, formatDate } from '@/utils/utils';
import { mapActions } from 'vuex';
import { getTraceModelConfig } from '@/api/productApi';
import { getMonitorDelayTrend16, getMonitorSpanIndicators16 } from '@/api/httpApi';
import monitor from '@/components/ldpProduct/monitor/monitor';
import aTitle from '@/components/common/title/aTitle';
import noData from '@/components/common/noData/noData';
import aLoading from '@/components/common/loading/aLoading';
import appTopo from '@/components/common/topo/appTopo';
import allLinkTopo from '@/components/common/topo/allLinkTopo';
const defaultTraceType = 'RtnCfmXchg'; // 默认链路回路：交易所确认
export default {
    props: {
        productId: {
            type: String,
            default: ''
        },
        productInfo: {
            type: Object,
            default: () => { return {}; }
        }
    },
    data() {
        return {
            date: formatDate(new Date()),
            loading: false,
            switch1: true,
            subBizType: '',
            bizTraceType: '',
            traceModelConfigs: [],
            selectedGroup: '', // 默认选中应用
            interval: 5000,
            intervalList: [
                {
                    label: '5秒',
                    value: 5000
                }, {
                    label: '10秒',
                    value: 10000
                }, {
                    label: '30秒',
                    value: 30000
                }
            ],
            timer: null,
            indicators: [],
            allIndicators: [],
            secondType: 'p50',
            secondTypeList: [{
                key: 'p50',
                value: '中位数'
            },
            {
                key: 'avg',
                value: '平均值'
            }, {
                key: 'max',
                value: '最大值'
            }, {
                key: 'min',
                value: '最小值'
            }],
            // 监控时间区间选择
            timeIntervalList: [{
                label: '最近五分钟',
                value: 300
            }, {
                label: '最近十五分钟',
                value: 900
            }, {
                label: '最近三十分钟',
                value: 1800
            }],
            timeValue: 1800,
            monitorList: [], // 图表数据
            selectedNode: '',
            hoveredGroup: '',
            tabName: 'tab1'
        };
    },
    computed: {
        spanList: function () {
            if (this.penetrationViewMode) {
                return this.selectedNode.split(',');
            } else if (this.applicationViewMode) {
                const list = [];
                Array.isArray(this.applicationViewMode?.groups) && this.applicationViewMode.groups.forEach(ele => {
                    if (ele.name === this.selectedGroup) {
                        Array.isArray(ele.attributes?.spanGroup) && ele.attributes.spanGroup.forEach(item => {
                            list.push(item.spanName);
                        });
                    }
                });
                return list;
            }
            return [];
        },
        bizTraceTypes: function() {
            const data = _.find(this.traceModelConfigs, ['subBizType', this.subBizType]);
            return data?.bizTraceTypes || [];
        },
        // 应用链路模型
        applicationViewMode: function () {
            const data = _.find(this.traceModelConfigs, ['subBizType', this.subBizType]);
            const jsonData = data?.latencyTopology?.applicationLatencyTopology || '';
            let info = '';
            const list = (jsonData && isJSON(jsonData)) ? JSON.parse(jsonData) : '';
            if (Array.isArray(list)) {
                for (const ele of list) {
                    if (ele?.meta?.bizWorkloadTraceType === (this.bizTraceType || data?.bizTraceTypes?.[0]?.traceType)) {
                        info = ele;
                        break;
                    }
                }
            }
            return info;
        },
        // 穿透链路模型
        penetrationViewMode: function () {
            const data = _.find(this.traceModelConfigs, ['subBizType', this.subBizType]);
            const info = data?.latencyTopology?.penetrationLatencyTopology || '';
            return (info && isJSON(info)) ? JSON.parse(info) : '';
        }
    },
    watch: {
        switch1(newVal, oldVal) {
            this.$nextTick(() => {
                if (newVal) {
                    this.$refs['topo'] && this.$refs['topo'].init();
                }
                this.$refs['monitor'] && this.$refs['monitor'].resize();
            });
        },
        productId(newVal) {
            this.loading = true;
            setTimeout(() => {
                this.loading = false;
            }, 1000);
        }
    },
    methods: {
        init() {
            this.checkProduct();
        },
        ...mapActions({ getAccountList: 'product/getAccountList' }),
        // 当前产品切换重置图表数据
        resetData() {
            this.clearPolling();
            this.tabName = 'tab1';
            this.monitorList = [];
            this.indicators = [];
            this.checkAppViewMode();
        },
        // 切换产品
        async checkProduct() {
            this.loading = true;
            this.resetData();
            try {
                await this.getTraceModelConfig();
            } catch (error) {
                console.error(error);
            }
            this.bizTraceType = _.find(this.bizTraceTypes, ['traceType', defaultTraceType])?.traceType || this.bizTraceTypes?.[0]?.traceType;
            this.subBizType = this.traceModelConfigs?.[0]?.subBizType;
            this.$nextTick(() => {
                this.checkAppViewMode();
                this.loading = false;
            });
        },
        async getTraceModelConfig() {
            const param = { productId: this.productId };
            const res = await getTraceModelConfig(param);
            if (param.productId !== this.productId) return;
            this.traceModelConfigs = res?.data?.traceModelConfigs || [];
        },
        // 选择模型链路
        checkAppViewMode() {
            if (!this.applicationViewMode && !this.penetrationViewMode) return;
            this.$nextTick(() => {
                if (this.penetrationViewMode) {
                    this.selectedNode = '';
                    // 设置初始应用
                    const links = this?.penetrationViewMode?.links;
                    const edges = this?.penetrationViewMode?.edges;
                    const nodes = this?.penetrationViewMode?.nodes;
                    // eslint-disable-next-line no-labels
                    outer:
                    for (const item of [links, edges, nodes]) {
                        if (Array.isArray(item)) {
                            // eslint-disable-next-line no-labels
                            inter:
                            for (const ele of item) {
                                if (ele?.attributes?.defaultSelect) {
                                    this.selectedNode = ele.attributes.timeDelayTrendField;
                                    // 手动更新下topo视图, 因为数据每次都会重置所以topo组件在全局只有一个
                                    this.$nextTick(() => {
                                        this.$refs['topo'] && this.$refs['topo'].init();
                                    });
                                    // eslint-disable-next-line no-labels
                                    break outer;
                                }
                            }
                        }
                    }
                } else if (this.applicationViewMode) {
                    this.selectedGroup = '';
                    for (const item of this.applicationViewMode.groups) {
                        if (item?.attributes?.defaultSelect) {
                            // 设置初始应用
                            this.selectedGroup = item?.name;
                            // 手动更新下topo视图, 因为数据每次都会重置所以topo组件在全局只有一个
                            this.$refs['topo'] && this.$refs['topo'].init();
                        }
                    }
                }
                this.handleUpdateTimer();
            });
        },
        // 统一处理轮循返回值，监听页面状态变化适时丢弃数据
        listenPageChange(param) {
            return (param.productInstNo !== this.productInfo?.productInstNo) ? false : true;
        },
        // 获取监控时延数据
        getMonitorDelayTrend(param) {
            const params = _.cloneDeep(param);
            getMonitorDelayTrend16(Object.assign(params, {
                linkSpanList: this.spanList,
                indicators: [this.secondType],
                timeWindow: this.timeValue,
                subBizType: this.subBizType
            })).then(res => {
                if (!this.listenPageChange({ ...param })) return;
                this.monitorList = [];
                if (res.success) {
                    const list = res.data;
                    const data = _.find(this.traceModelConfigs, ['subBizType', this.subBizType]);
                    if (Array.isArray(list) && list.length) {
                        list.forEach(ele => {
                            this.monitorList.push({
                                name: _.find(data.spans || [], ['spanName', ele.span])?.spanAlias,
                                xaxis: ele?.trendChart?.xaxis,
                                yaxis: ele?.trendChart?.yaxis.map(e => { return cutZero((Number(e) / 1000 || 0).toFixed(1)); })
                            });
                        });
                    }
                    return;
                }
                this.$hMessage.error('获取图表监控时延数据失败！');
                this.clearPolling();
            }).catch(err => {
                this.clearPolling();
            });
        },
        // 获取聚合数据
        getMonitorSpanIndicators(param) {
            return new Promise((resolve, reject) => {
                const params = _.cloneDeep(param);
                const data = _.find(this.bizTraceTypes, ['traceType', this.bizTraceType]);
                const spans = data?.spans || [];
                getMonitorSpanIndicators16(Object.assign(params, {
                    linkSpanList: spans,
                    indicators: ['avg', 'p50', 'max', 'min', 'last'],
                    timeWindow: this.timeValue,
                    subBizType: this.subBizType
                })).then(res => {
                    if (!this.listenPageChange({ ...param })) {
                        resolve(false);
                        return;
                    }
                    if (res.success) {
                        this.allIndicators = res.data || [];
                        this.indicators = [];
                        this.spanList.forEach(ele => {
                            const data = _.find(this.allIndicators, ['span', ele]);
                            data && this.indicators.push(data);
                        });
                        let loopSpanInfo;
                        if (this.penetrationViewMode) {
                            loopSpanInfo = [];
                            this.allIndicators.forEach(ele => {
                                loopSpanInfo.push({
                                    instanceName: ele.span,
                                    data: (ele[this.secondType] / 1000 || 0).toFixed(1) + ' μs'
                                });
                            });
                        } else {
                            loopSpanInfo = {};
                            this.allIndicators.forEach(ele => {
                                loopSpanInfo[ele.span] = (ele[this.secondType] / 1000 || 0).toFixed(1) + ' μs';
                            });
                        }
                        resolve(loopSpanInfo);
                    } else {
                        this.clearPolling();
                        this.$hMessage.error('获取聚合数据失败！');
                        resolve(false);
                    }
                }).catch(err => {
                    reject(err);
                    this.clearPolling();
                });
            });

        },
        // 发送数据接口归集
        async sendData() {
            try {
                const param = {
                    productInstNo: this.productInfo?.productInstNo, // 产品ID
                    date: this.date, // 只查询当天数据
                    loopType: defaultTraceType, // 监控回路
                    interval: this.interval
                };
                this.getMonitorDelayTrend(param);
                const loopSpanInfo = await this.getMonitorSpanIndicators(param);
                loopSpanInfo && this.switch1 && this.$refs['topo'] && this.$refs['topo'].handleUpdateEdgeLabel(loopSpanInfo, {});
            } catch (error) {
                console.error(error);
                this.clearPolling();
            }
        },
        // 更新定时器
        handleUpdateTimer() {
            this.clearPolling();
            this.sendData();
            this.timer = setInterval(() => {
                this.sendData();
            }, 5000);
        },
        // 切换选项重置图表
        handleExchange() {
            this.sendData();
        },
        // 切换业务类型
        handleBusinessChange() {
            this.bizTraceType = '';
            this.$nextTick(() => {
                // 模型回路默认选择交易所确认，如没有选择第一个
                this.bizTraceType = _.find(this.bizTraceTypes, ['traceType', defaultTraceType])?.traceType || this.bizTraceTypes?.[0]?.traceType;
            });
        },
        // 切换链路模型
        handleTopoChange() {
            if (!this.bizTraceType) return;
            this.loading = true;
            setTimeout(() => {
                this.checkAppViewMode();
                this.$refs['topo'] && this.$refs['topo'].init();
                this.loading = false;
            }, 800);
        },
        // 动态计算echart-box 高度
        updateChartBoxHeight() {
            return { height: this.switch1 ? `calc(65% - 46px)` : `calc(100% - 50px)` };
        },
        // hover拓扑应用回调
        handleHoverGroup(name) {
            this.hoveredGroup = '';
            const spans = _.find(this.applicationViewMode.groups, ['name', name])?.attributes?.spanGroup;
            if (Array.isArray(spans) && spans.length) {
                this.hoveredGroup = name;
            }
        },
        // 点击选中拓扑应用回调
        handleCheckGroup(name) {
            const spans = _.find(this.applicationViewMode.groups, ['name', name])?.attributes?.spanGroup;
            if (Array.isArray(spans) && spans.length) {
                this.selectedGroup = name;
                this.sendData();
            }
        },
        // 获取到拓扑点击变化
        handleSelectedChange(name) {
            this.selectedNode = name;
            this.sendData();
        },
        clearPolling() {
            this.timer && clearInterval(this.timer);
        }
    },
    beforeDestroy() {
        this.clearPolling();
    },
    components: { aLoading, monitor, appTopo, allLinkTopo, aTitle, noData }
};
</script>

<style lang="less" scoped>
@import url("@/assets/css/monitor.less");
@import url("@/assets/css/tab.less");

.main {
    width: 100%;
    height: 100%;
    padding: 0;

    & > .wrapper {
        width: 100%;
        height: 100%;
        position: relative;
        padding: 0;
        cursor: pointer;
    }

    .echart-box {
        background-color: var(--wrapper-color);
    }
}
</style>
