/*
 * @Description: 表格查询组件
 */
import aForm from './aForm';
import aTable from '@/components/common/table/aTable';
import aButton from '@/components/common/button/aButton';
import aTips from '@/components/common/apmTips/aTips';
import './queryTable.less';
import { formatDate, transferVal } from '@/utils/utils';
import draggableTableConfigModal from '@/components/networkSendAndRecevied/modal/draggableTableConfigModal'; // 抓包页面使用
export default {
    name: 'queryTable',
    props: {
        title: {
            type: String,
            default: ''
        },
        btnTitle: {
            type: String,
            default: '筛选条件'
        },
        tipText: {
            type: String,
            default: ''
        },
        loading: {
            type: Boolean,
            default: false
        },
        formItems: {
            type: Array,
            default: () => []
        },
        formCols: {
            type: Number,
            default: 4
        },
        tableData: {
            type: Array,
            default: () => []
        },
        columns: {
            type: Array,
            default: () => []
        },
        multiLevel: {
            type: Array,
            default: () => []
        },
        hasFilter: {
            type: Boolean,
            default: true
        },
        hasPage: {
            type: Boolean,
            default: true
        },
        total: {
            type: Number,
            default: 0
        },
        hasSetTableColumns: {
            type: Boolean,
            default: false
        },
        setTableColumnsType: {
            type: String,
            default: 'noSorted'   // hasSorted
        },
        showTitle: {
            type: Boolean,
            default: false
        }
    },
    data() {
        return {
            formStatus: false,
            styles: {
                height: 'calc(100% - 55px)',
                paddingBottom: '53px'
            },
            currentCheckedKeys: [],
            checkList: [], // 列 信息
            columnData: [], // 需要展示出来的列
            tableHeight: 0,
            queryTags: [],
            historyQuery: {},
            visible: false,
            draggableTableConfigInfo: {
                status: false
            }
        };
    },
    mounted() {
        this.$_init();
        window.addEventListener('resize', this.fetTableHeight);
        this.$nextTick(() => {
            this.fetTableHeight();
        });

    },
    beforeDestroy() {
        window.removeEventListener('resize', this.fetTableHeight);
    },
    computed: {
        formHeight() {
            const itemsHeight = Math.ceil(this.formItems.length / (this.formCols)) * 45;
            return this.formStatus ? itemsHeight + 30 : 0;
        },
        showFormTags() {
            return !this.formStatus && this.queryTags.length;
        }
    },
    methods: {
        resetHeight() {
            return new Promise((resolve, reject) => {
                this.tableHeight = 0;
                resolve();
            });
        },
        // 设置table高度
        fetTableHeight() {
            this.resetHeight().then(res => {
                const formHeight = this.formHeight;
                const subtractHeight = this.hasPage ? 110 : 50;
                const formTagsHeight = this.showFormTags ? 45 : 0;
                const tipHeight = this.tipText ? 34 : 0;
                this.tableHeight = this.$refs['query-table'].getBoundingClientRect().height - subtractHeight - formHeight - formTagsHeight - tipHeight;
            });
        },
        setFormOpen() {
            if (!this.formStatus) {
                this.changeFormStatus();
            }
        },
        changeFormStatus(){
            this.formStatus = !this.formStatus;
            this.fetTableHeight();
            this.queryTags = this.$_getFormParam();
        },
        $_init(){
            if (this.setTableColumnsType === 'hasSorted'){
                this.checkList = [...this.columns].filter(o => o?.key !== 'action').map(o => {
                    return {
                        ...o,
                        isChecked: transferVal(o?.isChecked) ?  o?.isChecked : true
                    };
                });
                const action = [...this.columns].filter(o => o?.key === 'action');
                const columns = [...this.checkList].filter(o => o?.isChecked && o?.key !== 'action');
                this.columnData = [...columns, ...action];
            } else {
                this.checkList = [];
                this.columnData = [];
                if (this.currentCheckedKeys?.length){
                    this.checkList = [...this.currentCheckedKeys];
                    this.columns.forEach(element => {
                        if (this.currentCheckedKeys.includes(element.key)){
                            this.columnData.push(element);
                        }
                    });
                } else {
                    this.columns.forEach(element => {
                        this.checkList.push(element.key);
                        this.columnData.push(element);
                    });
                }
            }
        },
        // 配置表格列
        setTableColumns() {
            if (this.setTableColumnsType === 'hasSorted'){
                this.draggableTableConfigInfo.status = true;
                this.draggableTableConfigInfo.configList = [...this.checkList];
                this.draggableTableConfigInfo.defaultCheckedList = [...this.columnData]?.map((o) => o?.key);
            } else {
                this.visible = true;
            }
        },
        setNewConfigList(configList) {
            const columnsList = configList.filter((v) => v.isChecked);
            this.checkList = [...configList]; // 拖拽结束后的列表顺序
            // 操作
            const action = [...this.columns].filter(o => o?.key === 'action');
            this.columnData = [...columnsList, ...action];
        },
        $_handleCheckbox(value) {
            this.currentCheckedKeys = [...value];
            this.columnData = [];
            this.columns.forEach(ele => {
                if (value.includes(ele.key)){
                    this.columnData.push(ele);
                }
            });
            this.columnData.forEach(ele => {
                ele.hiddenCol = !value.includes(ele.key);
            });
        },
        // 判断并获取筛选条件值
        $_getFormParam() {
            const param = this.historyQuery;
            const queryTags = [];

            Object.keys(param).forEach(key => {
                const formItem = this.formItems.find(o => o.key === key);

                if (!formItem || !param[key] || !param[key]?.length) return;

                const { type, options, label } = formItem;
                let value = '';

                switch (type) {
                    case 'timerange':
                        value = Array.isArray(param[key]) ? param[key].join('-') : '';
                        break;
                    case 'select':
                    case 'selectSearch':
                        if (Array.isArray(param[key])) {
                            value = param[key].map(item => {
                                const option = options?.find(e => e.value === item);
                                return option ? option.label : item;
                            }).join('，');
                        } else {
                            const option = options?.find(e => e.value === param[key]);
                            value = option ? option.label : param[key];
                        }
                        break;
                    case 'daterange':
                        value = Array.isArray(param[key]) ? param[key].map(o => { return formatDate(o); }).join('~') : '';
                        break;
                    default:
                        // 字符串类型
                        value = param[key];
                        break;
                }

                queryTags.push({
                    key: key,
                    label: label,
                    value: value
                });
            });

            return queryTags;
        },
        // 删除筛选条件标签
        $_handletagClose(key) {
            if (key === 'clearAll') {
                this.$refs['forms'].init();
                this.queryTags = [];
            } else {
                // 重置对应key
                this.$refs['forms'].resetItem(key);
                const delInx = this.queryTags.findIndex(o => o.key === key);
                if (delInx !== -1) {
                    this.queryTags.splice(delInx, 1);
                }
            }
            this.fetTableHeight();
            // 触发重新查询
            const param = this.$refs['forms'].query();
            this.historyQuery = param;
            this.$_handleQuery();
        },
        onCurrentChange(row){
            this.$emit('on-current-change', row);
        },
        rowClick(row) {
            this.$emit('on-row-click', row);
        },
        $_tableSelection(selection){
            this.$emit('selection', selection);
        },
        $_handleQuery(shouldShowLoading = true) {
            const pageParam = this.hasPage ? this.$refs['table'].getPageData() : {};
            this.$emit('query', { ...this.historyQuery, ...pageParam }, shouldShowLoading);
            this.$nextTick(() => {
                this.fetTableHeight();
            });
        },
        $_handleReset() {
            this.handleClickQuery();
        },
        handleClickQuery() {
            const param = this.$refs['forms'].query();
            this.historyQuery = param;
            this.$refs['table'].resetPage();
            this.$_handleQuery();
        },
        $_handleResetPageDataAndQuery() {
            this.$refs['table'].resetPage();
            this.$refs['table'].resetPageSize();
            this.$_handleQuery();
            this.fetTableHeight();
        },
        $_handleResetAndDefaultQuery(defaultObj) {
            this.$refs['forms'].setDefaultVal(defaultObj);
            this.$nextTick(() => {
                this.handleClickQuery();
            });
        }
    },
    components: { aForm, aTable, aButton, aTips, draggableTableConfigModal },
    render() {
        return <div ref="query-table" class="query-table">
            <div class="query-table-title">
                <div class="table-title">{this.title}</div>
                <div class="title-left">
                    {/* 标题区插槽 */}
                    {this.$slots.tableTitle}
                </div>
                <div class="title-right">
                    {/* 自定义操作区插槽 */}
                    {this.$slots.operateBlock}
                    {
                        this.hasFilter ? <a-button type="dark" onClick={this.changeFormStatus}>
                            { this.btnTitle }
                            <h-icon name={this.formStatus ? 'packup' : 'unfold'}></h-icon>
                        </a-button> : ''
                    }
                    {
                        this.hasSetTableColumns ? <div class="separator" style='margin: 0 4px'></div> : ''
                    }
                    {this.hasSetTableColumns && <a-button type="dark" onClick={this.setTableColumns}>配置表格</a-button>}
                </div>
            </div>

            {/* tips */}
            {
                this.tipText
                    ? <div style="margin: 10px 15px 0; width: calc(100% - 30px);">
                        <a-tips
                            theme="dark"
                            tipText={this.tipText}
                        ></a-tips>
                    </div> : ''
            }

            {/* 筛选框展开 */}
            <div class="form-box"
                style={{ height: this.formHeight + 'px' }}>
                <a-form ref="forms"
                    formCols={this.formCols}
                    formItems={this.formItems}
                    v-on:click-query={this.handleClickQuery}
                    v-on:reset={this.$_handleReset}
                />
            </div>

            {/* 有筛选值，收起筛选框，展示已选条件 */}
            <div class="form-tags"
                style={{ display: this.showFormTags ? 'flex' : 'none' }}>
                <span class="tag-title">{ this.btnTitle }：</span>
                <div class="scroll-context">
                    {
                        (this.queryTags || []).map(item => {
                            return <h-tag
                                size="small" closable
                                showTitle={item.value.length > 20}
                                title={`${item.label}：${item.value}`}
                                v-on:on-close={() => this.$_handletagClose(item.key)}>
                                {`${item.label}：${item.value.length > 20 ? `${item.value.slice(0, 20)}...` : item.value}`}
                            </h-tag>;
                        })
                    }
                </div>
                <a-button type="text" class="clear-text"
                    onClick={() => this.$_handletagClose('clearAll')}>清空
                </a-button>
            </div>

            <div class='table-box'>
                {
                    this.isSimpleTable
                        ? <a-simple-table
                            ref="table"
                            tableData={this.tableData}
                            height={this.tableHeight}
                            multiLevel={this.multiLevel}
                            columns={this.columnData}
                            hasPage={this.hasPage}
                            total={this.total}
                            canDrag={true}
                            border={true}
                            loading = {this.loading}
                            showTitle={this.showTitle}
                            v-on:query={this.$_handleQuery}
                            v-on:on-current-change={this.onCurrentChange}
                            v-on:selection={this.$_tableSelection} />
                        : <a-table
                            ref="table"
                            tableData={this.tableData}
                            height={this.tableHeight}
                            multiLevel={this.multiLevel}
                            columns={this.columnData}
                            hasPage={this.hasPage}
                            total={this.total}
                            canDrag={true}
                            border={true}
                            loading = {this.loading}
                            showTitle={this.showTitle}
                            v-on:query={this.$_handleQuery}
                            v-on:onCurrentChange={this.onCurrentChange}
                            v-on:rowClick = {this.rowClick}
                            v-on:selection={this.$_tableSelection} />
                }
            </div>
            <h-drawer
                v-model={this.visible}
                width="280"
                title="表格配置"
                styles={this.styles}>
                <h-checkbox-group
                    v-model={this.checkList}
                    onInput={this.$_handleCheckbox}
                    vertical>
                    {
                        this.columns.map(item => {
                            return <h-checkbox key={item.key}
                                label={item.key}
                                disabled={item.disabled || (this.checkList.length <= 1 && this.checkList.includes(item.key))} style="display: flex; align-items: center;">
                                <span style="white-space:nowrap;">{item.title}</span>
                            </h-checkbox>;
                        })
                    }
                </h-checkbox-group>
            </h-drawer>
            { this.draggableTableConfigInfo.status ? <draggable-table-configModal
                modalData={this.draggableTableConfigInfo}
                v-on:set-config={this.setNewConfigList}
            /> : ''
            }
        </div>;
    }
};
