<template>
    <div class="main">
        <a-loading v-if="loading" style="z-index: 9;"></a-loading>
        <!-- 重演任务列表 -->
        <core-replay-task-list
            v-show="tabName === 'task-list'"
            ref="task-list"
            :productList="productList"
            @check-product="checkProduct"
            @to-create-task="handleTabChange"
            @to-detail-task="handleTabChange"
        >
        </core-replay-task-list>
        <!-- 创建 -->
        <create-core-replay-Task
            v-if="tabName === 'create-task'"
            ref="create-task"
            :productInstNo="productInstNo"
            :productList="productList"
            @to-task-list="handleTabChange"
        >
        </create-core-replay-Task>
        <!-- 重演细节 -->
        <core-replay-detail
            v-if="tabName === 'task-detail'"
            ref="task-detail"
            :productInstNo="productInstNo"
            :productList="productList"
            @to-task-list="handleTabChange"
        >
        </core-replay-detail>
    </div>
</template>

<script>
import _ from 'lodash';
import { mapState, mapActions } from 'vuex';
import aLoading from '@/components/common/loading/aLoading';
import coreReplayTaskList from '@/components/coreReplayObservation/coreReplayTaskList.vue';
import createCoreReplayTask from '@/components/coreReplayObservation/createCoreReplayTask.vue';
import coreReplayDetail from '@/components/coreReplayObservation/coreReplayDetail.vue';

export default {
    name: 'CoreReplayObservation',
    components: { aLoading, coreReplayTaskList, createCoreReplayTask, coreReplayDetail },
    data() {
        return {
            productInstNo: '',
            loading: false,
            tabName: 'task-list' // task-list列表  create-task创建  task-detail具体内容
        };
    },
    mounted() {
        this.init();
    },
    computed: {
        ...mapState({
            productList: state => {
                return state.product.productListLight || [];
            }
        })
    },
    methods: {
        ...mapActions({ getProductList: 'product/getProductListLight' }),
        // 初始化页面
        async init() {
            try {
                this.loading = true;
                await this.getProductList();
                // 初始展示表格
                const productInstNo = localStorage.getItem('productInstNo');
                this.productInstNo = _.find(this.productList, ['productInstNo', productInstNo])?.productInstNo || this.productList?.[0]?.productInstNo;
                this.handleTabChange(this.tabName);
            } catch (e) {
                console.error(e);
            } finally {
                this.loading = false;
            }
        },
        // 切换导航产品
        async checkProduct(item) {
            this.loading = true;
            this.productInstNo = item;
            localStorage.setItem('productInstNo', item);
            this.handleTabChange('task-list');
            setTimeout(() => {
                this.loading = false;
            }, 500);
        },
        // 切换
        handleTabChange(tab, params) {
            this.tabName = tab;
            this.$nextTick(() => {
                // 去除定时器
                this.$refs['task-list'] && this.$refs['task-list'].clearPolling();
                this.$refs['task-detail'] && this.$refs['task-detail'].clearPolling();
                // 页面请求
                this.$refs[this.tabName] && this.$refs[this.tabName].initData(params);
            });
        }
    }
};
</script>
