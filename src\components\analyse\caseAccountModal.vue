<template>
    <div>
        <!-- 下发配置列表 -->
        <h-msg-box-safe
            v-model="modalData.status"
            :escClose="true"
            :mask-closable="false"
            title="账号配置"
            width="820"
            height="60"
            @on-open="getConfigInfo"
        >
            <div class="box-count">
                <div class="line-count">
                    <div class="line-count-left">
                        <span>资金账号</span>
                        <a-button type="ghost" @click="handleAccounts('amountInfo', false)">导入</a-button>
                    </div>
                    <div class="line-count-right">
                        <span>起始账号</span>
                        <h-input
                            v-model="amountInfo.start"
                            placeholder="请输入起始账号"
                            style="width: 120px;"
                        ></h-input>
                        <span>生成个数</span>
                        <h-input-number
                            v-model="amountInfo.times"
                            style="width: 120px;"
                            setzero
                        ></h-input-number>
                        <a-button type="ghost" @click="amountInfo.accounts = generateAccount(amountInfo.start, amountInfo.times)">自动生成</a-button>
                    </div>
                </div>
                <h-input
                    v-model="amountInfo.accounts"
                    type="textarea"
                    :rows="3"
                    placeholder="账号..."
                ></h-input>
            </div>

            <div class="box-count">
                <div class="line-count">
                    <div class="line-count-left">
                        <span>股东账号</span>
                        <a-button type="ghost" @click="handleAccounts('holderInfo', holderInfo.markId)">导入</a-button>
                    </div>
                    <div class="line-count-right">
                        <h-radio-group v-model="holderInfo.markId">
                            <h-radio label="0"><span>上交所</span></h-radio>
                            <h-radio label="1"><span>深交所</span></h-radio>
                        </h-radio-group>
                        <span>起始账号</span>
                        <h-input
                            v-model="holderInfo.start"
                            placeholder="请输入起始账号"
                            style="width: 120px;"
                        ></h-input>
                        <span>生成个数</span>
                        <h-input-number
                            v-model="holderInfo.times"
                            style="width: 120px;"
                            setzero
                        ></h-input-number>
                        <a-button type="ghost" @click="holderAccount">自动生成</a-button>
                    </div>
                </div>
                <h-input
                    v-if="holderInfo.markId==='0'"
                    v-model="holderInfo.shaccounts"
                    type="textarea"
                    :rows="3"
                    placeholder="账号..."
                ></h-input>
                <h-input
                    v-else
                    v-model="holderInfo.szaccounts"
                    type="textarea"
                    :rows="3"
                    placeholder="账号..."
                ></h-input>
            </div>

            <div class="box-count">
                <div class="line-count">
                    <div class="line-count-left">
                        <span>证券代码</span>
                        <a-button type="ghost" @click="handleAccounts('securityInfo', securityInfo.markId)">导入</a-button>
                    </div>
                    <div class="line-count-right">
                        <h-radio-group v-model="securityInfo.markId">
                            <h-radio label="0"><span>上交所</span></h-radio>
                            <h-radio label="1"><span>深交所</span></h-radio>
                        </h-radio-group>
                        <span>证券代码</span>
                        <h-input
                            v-model="securityInfo.start"
                            placeholder="请输入起始账号"
                            style="width: 120px;"
                        ></h-input>
                        <span>生成个数</span>
                        <h-input-number
                            v-model="securityInfo.times"
                            style="width: 120px;"
                            setzero
                        ></h-input-number>
                        <a-button type="ghost" @click="securityAccount">自动生成</a-button>
                    </div>
                </div>
                <h-input
                    v-if="securityInfo.markId==='0'"
                    v-model="securityInfo.shaccounts"
                    type="textarea"
                    :rows="3"
                    placeholder="账号..."
                ></h-input>
                <h-input
                    v-else
                    v-model="securityInfo.szaccounts"
                    type="textarea"
                    :rows="3"
                    placeholder="账号..."
                ></h-input>
            </div>

            <template v-slot:footer>
                <a-button @click="modalData.status = false">取消</a-button>
                <a-button type="primary" :loading="loading" @click="submitConfig">确定</a-button>
            </template>
        </h-msg-box-safe>
        <import-account-modal v-if="dataInfo.status" :modalInfo="dataInfo" @update="updateAccounts" />
    </div>
</template>

<script>
import importAccountModal from '@/components/analyse/importAccountModal';
import aButton from '@/components/common/button/aButton';
import { configCaseAccount } from '@/api/httpApi';
import { prefixZero } from '@/utils/utils';
export default {
    props: {
        modalInfo: {
            type: Object,
            default: null
        }
    },
    data() {
        return {
            modalData: this.modalInfo,
            loading: false,
            amountInfo: {
                start: ********,
                times: 500,
                accounts: ''
            },
            holderInfo: {
                start: ********,
                times: 500,
                markId: '0',
                shaccounts: '',
                szaccounts: ''
            },
            securityInfo: {
                start: 0,
                times: 500,
                markId: '0',
                shaccounts: '',
                szaccounts: ''
            },
            dataInfo: {
                status: false,
                info: ''
            }
        };
    },
    methods: {
        // 弹窗初始化
        getConfigInfo() {
            const data = this.modalData.customConfig;
            if (data) {
                this.amountInfo.accounts = data?.account;
                this.holderInfo.szaccounts = data?.szStockAccount;
                this.holderInfo.shaccounts = data?.shStockAccount;
                this.securityInfo.szaccounts = data?.szStockcode;
                this.securityInfo.shaccounts = data?.shStockcode;
            }
        },
        // 生成证券代码
        securityAccount() {
            if (this.securityInfo.markId === '0') {
                this.securityInfo.shaccounts = this.generateAccount(this.securityInfo.start, this.securityInfo.times, 'security', this.securityInfo.markId);
            } else {
                this.securityInfo.szaccounts = this.generateAccount(this.securityInfo.start, this.securityInfo.times, 'security', this.securityInfo.markId);
            }
        },
        // 生成股东账号
        holderAccount() {
            if (this.holderInfo.markId === '0') {
                this.holderInfo.shaccounts = this.generateAccount(this.holderInfo.start, this.holderInfo.times, 'holder', this.holderInfo.markId);
            } else {
                this.holderInfo.szaccounts = this.generateAccount(this.holderInfo.start, this.holderInfo.times, 'holder', this.holderInfo.markId);
            }
        },
        // 自动生成
        /* eslint-disable */
        generateAccount(count, time, type, markId) {
            if (typeof (Number(count)) === 'number' && typeof (Number(time)) === 'number') {
                const arr = [];
                for (let i = 0; i < time; i++) {
                    const num = Number(count) + i;
                    if (arguments.length === 4) {
                        if (type === 'holder') {
                            arr.push(markId === '0' ? 'A0' + num : '00' + num);
                        } else {
                            arr.push(markId === '0' ? '60' + prefixZero(num, 4) : '00' + prefixZero(num, 4));
                        }
                    } else {
                        arr.push(num);
                    }
                }
                return arr.join(',');
            } else {
                this.$hMessage.error('请确认输入内容为数字！');
            }
        },
        // 提交
        async submitConfig() {
            this.loading = true;
            try {
                const param = {
                    sceneId: this.modalInfo.sceneId,
                    testCaseId: this.modalInfo.testCaseId,
                    account: this.amountInfo.accounts,
                    szStockAccount: this.holderInfo.szaccounts,
                    shStockAccount: this.holderInfo.shaccounts,
                    szStockcode: this.securityInfo.szaccounts,
                    shStockcode: this.securityInfo.shaccounts
                };
                const res = await configCaseAccount(param);
                if (res.success) {
                    this.$emit('update', param.testCaseId);
                    this.$hMessage.success('用例账号更新成功!');
                    this.modalData.status = false;
                } else {
                    this.loading = false;
                    this.$hMessage.error('用例账号更新!');
                }
            } catch (err) {
                this.loading = false;
            }

        },
        // 打开弹窗
        handleAccounts(info, markId) {
            this.dataInfo.status = true;
            this.dataInfo.info = info;
            this.dataInfo.markId = markId;
        },
        // 更新账号数据
        updateAccounts(info, markId, data) {
            if (markId) {
                if (markId === '0') {
                    this[info].shaccounts = data;
                } else {
                    this[info].szaccounts = data;
                }
            } else {
                this[info].accounts = data;
            }
        }
    },
    components: {
        importAccountModal,
        aButton
    }
};
</script>

<style lang="less" scoped>
.box-count {
    margin-bottom: 25px;

    .line-count {
        display: flex;
        justify-content: space-between;
        width: 100%;
        height: 45px;

        .line-count-left {
            position: relative;
            font-size: var(--font-size);

            &::before {
                content: "";
                width: 4px;
                height: 17px;
                position: absolute;
                left: 0;
                top: 6px;
                background: var(--link-color);
            }

            & > span {
                padding: 0 5px 0 20px;
            }
        }

        .line-count-right {
            & > span {
                padding: 0 6px 0 2px;
            }

            & > button {
                margin-left: 4px;
            }
        }
    }

    /deep/ textarea.h-input {
        background: #f8f8f8;
    }

    /deep/ .h-radio-group {
        margin-top: -3px;
    }

    /deep/ .h-radio-wrapper {
        margin-right: 4px;
    }

    /deep/ .h-radio {
        margin-right: 4px;
    }
}
</style>
