<template>
    <div ref="edit-container" class="monaco-code-editor"></div>
  </template>

<script>
import * as monaco from 'monaco-editor';

export default {
    props: {
        value: {
            type: String,
            default: ''
        },
        language: {
            type: String,
            default: 'json'
        },
        options: {
            type: Object,
            default: () => {}
        }
    },
    data() {
        return {
            editor: null,
            model: null
        };
    },
    watch: {
        value(newVal) {
            this.model.setValue(newVal);
        },
        language(newVal) {
            this.updateModelLanguage(newVal);
        }
    },
    mounted() {
        this.initializeMonaco();
    },
    beforeDestroy() {
        this.destroyMonaco();
    },
    methods: {
        initializeMonaco() {
            if (this.model) {
                this.model.dispose();
            }
            this.model = monaco.editor.createModel(this.value, this.language);
            this.editor = monaco.editor.create(this.$refs['edit-container'], {
                model: this.model,
                language: this.language,
                automaticLayout: true,
                theme: 'vs-dark',
                folding: true,
                colorDecorators: true, // 颜色装饰器
                scrollBeyondLastLine: false, // 禁用超出最后一行的滚动
                renderOverviewRuler: false, // 关闭右侧预览
                minimap: {
                    enabled: false // 关闭右下角的小地图
                },
                wordWrap: 'on',  // 设置自动换行
                contextmenu: false, // 关闭右键菜单
                ...this.options
            });
        },
        // 获取编辑器数据
        getContent() {
            const content = this.editor.getValue();
            return content;
        },
        // 更新语言
        updateModelLanguage(language) {
            // 更改模型语言
            try {
                monaco.editor.setModelLanguage(this.model, language);
            } catch (error) {
                console.error('Error setting model language:', error);
            }
        },
        destroyMonaco() {
            if (this.editor) {
                this.editor.dispose();
            }
            if (this.model) {
                this.model.dispose();
            }
        }
    }
};
</script>

<style scoped>
.monaco-code-editor {
    width: 100%;
    height: 100%;
}
  </style>
