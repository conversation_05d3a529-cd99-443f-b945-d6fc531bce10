/*
 * @Description: APM 通用业务Store管理
 * @Author: <PERSON><PERSON>
 * @Date: 2023-03-03 15:46:07
 * @LastEditTime: 2023-04-17 13:58:48
 * @LastEditors: <PERSON><PERSON>
 */
import { getApmDirDesc } from '@/api/httpApi';
export const state = () => ({
    apmDirDesc: {}, // APM字典描述信息
    apmConfigDesc: {}, // APM配置信息
    apmLicense: 'DD0E4968407B75DF27308603F8EB07B5'
});

export const mutations = {
    setApmDirDesc(state, data) {
        state.apmDirDesc = data;
    }
};

export const actions = {
    // 通用字典
    getApmDirDesc(context) {
        return new Promise((resolve, reject) => {
            getApmDirDesc().then(res => {
                if (res.success) {
                    context.commit('setApmDirDesc', res.data || {});
                    resolve(true);
                } else {
                    resolve(false);
                }
            }).catch(err => {
                reject(err);
                console.error(err);
            });
        });
    }
};
