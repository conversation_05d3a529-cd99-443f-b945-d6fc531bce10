.setting-func-content {
    display: flex;
    flex-direction: column;
    height: 100%;

    &-top {
        margin-bottom: 15px;

        .h-alert-message {
            display: flex;
            align-items: center;

            i {
                color: #2d8de5;
                margin-right: 9px;
                position: relative;
                top: 1px;
            }
        }

        &-category {
            border-left: 4px solid #2d8de5;
            padding-left: 12px;
            font-size: 14px;
            margin-top: 15px;
            margin-bottom: 15px;
        }

        &-info {
            display: flex;
            height: 32px;
            align-items: center;
            justify-content: space-between;

            &-search {
                display: flex;

                .h-input-suffix {
                    cursor: pointer;
                }
            }
        }
    }

    &-table {
        flex: 1;
        display: flex;
        overflow-y: auto;
        flex-direction: column;

        &-view {
            flex: 1;
            overflow-y: auto;
        }

        &-page {
            margin-top: 10px;
        }
    }
}
