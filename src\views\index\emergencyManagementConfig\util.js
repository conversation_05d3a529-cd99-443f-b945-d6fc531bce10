/**
 * @param values {string}
 * 功能号校验规则
 */
export function functionValidate(rule, values, callback) {
    if (!values || String(values).trim() === '') {
        return callback(new Error('输入不能为空'));
    }
    const nums = values.split(';');
    for (const num of nums) {
        const pureNum = Number(num.replace(/\*|\?/g, ''));
        const hasStar = num.includes('*');
        if (hasStar && num.length > 1) {
            return callback(new Error('*只能单独存在，不能与其他组合'));
        }
        const hasQuestion = num.includes('?');
        if (hasQuestion) {
            if (num.startsWith('?')) {
                return callback(new Error('不能以问号开头'));
            }
            const chars = num.split('');
            // 有问号
            for (let i = 0; i < chars.length - 1; i++) {
                if (chars[i] === '?' && chars[i + 1] !== undefined && chars[i + 1] !== '?') {
                    return callback(new Error('英文问号必须连续且在最后'));
                }
            }
        }
        if (isNaN(pureNum)) {
            return callback(new Error('数字不合法'));
        }
        if (pureNum < 0) {
            return callback(new Error('数字不能为负值'));
        }
    }
    return callback();
}

/**
 * @param values {string}
 * 系统号号校验规则
 */
export function systemNoValidate(rule, values, callback) {
    if (!values || String(values).trim() === '') {
        return callback(new Error('输入不能为空'));
    }
    const nums = values.split(';');
    for (const num of nums) {
        const hasStar = num.includes('*');
        if (hasStar && num.length > 1) {
            return callback(new Error('*只能单独存在，不能与其他组合'));
        }
        const pureNum = Number(num);
        if (isNaN(pureNum) && !hasStar) {
            return callback(new Error('数字不合法'));
        }
        if (pureNum > 255) {
            return callback(new Error('数字不能大于255'));
        }
    }
    return callback();
}

/**
 * @param values {string}
 * 节点校验规则
 */
export function nodeNoValidate(rule, num, callback) {
    if (!num) {
        // 允许不填
        return callback();
    }
    const hasStar = num.includes('*');
    if (hasStar && num.length > 1) {
        return callback(new Error('*只能单独存在，不能与其他组合'));
    }
    const pureNum = Number(num);
    if (isNaN(pureNum) && !hasStar) {
        return callback(new Error('数字不合法'));
    }
    if (pureNum < 0) {
        return callback(new Error('数字不能为负值'));
    }
    if (pureNum > 65535) {
        return callback(new Error('数字不能大于65535'));
    }
    return callback();
}

/**
 *
 */
export function backendIdValidate(rule, val, callback) {
    if (!val) return callback(new Error('请选择目标端ID'));
    return callback();
}
