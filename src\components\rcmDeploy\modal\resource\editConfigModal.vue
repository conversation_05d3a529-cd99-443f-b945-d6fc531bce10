<template>
  <div>
    <h-msg-box-safe
      v-model="modalData.status"
      class="modal"
      :escClose="true"
      :mask-closable="false"
      title="编辑配置文件"
      width="90%"
      :height="viewHeight"
      :top="top"
    >
      <codemirror
        v-model="modalData.data"
        :options="options"
        class="edit-config"
      ></codemirror>
      <template v-slot:footer>
        <div class="footer">
          <h-button :loading="loading" @click="onSave">保存</h-button>
        </div>
      </template>
    </h-msg-box-safe>
  </div>
</template>

<script>
import { getByteSize } from '@/utils/utils';
import { codemirror } from 'vue-codemirror';
import { rcmConfigSave } from '@/api/rcmApi';
import 'codemirror/lib/codemirror.css';
// JSON代码高亮需要由JavaScript插件支持
import 'codemirror/mode/javascript/javascript.js';
import 'codemirror/theme/idea.css';
import 'codemirror/keymap/sublime.js';
import 'codemirror/addon/dialog/dialog.js';
import 'codemirror/addon/dialog/dialog.css';
import 'codemirror/addon/search/search';
import 'codemirror/addon/search/searchcursor.js';
import 'codemirror/addon/fold/foldgutter.css';
import 'codemirror/addon/fold/foldcode.js';
import 'codemirror/addon/fold/foldgutter.js';
import 'codemirror/addon/fold/brace-fold.js';
import 'codemirror/addon/fold/comment-fold.js';
import 'codemirror/addon/display/fullscreen.css';
import 'codemirror/addon/display/fullscreen.js';
import 'codemirror/addon/edit/matchbrackets.js';
import 'codemirror/addon/edit/closebrackets.js';
import 'codemirror/addon/hint/show-hint.css';
import 'codemirror/addon/hint/show-hint.js';
import 'codemirror/addon/hint/anyword-hint.js';
import 'codemirror/addon/comment/comment.js';
// JSON错误检查
import 'codemirror/addon/lint/lint.css';
import 'codemirror/addon/lint/lint.js';
import 'codemirror/addon/lint/json-lint.js';
import 'codemirror/addon/display/autorefresh';
// 引入jsonlint
import jsonlint from 'jsonlint-mod';
window.jsonlint = jsonlint;

export default {
    props: {
        modalInfo: {
            type: Object,
            default: null
        },
        rcmId: {
            type: String,
            default: null
        }
    },
    data() {
        return {
            modalData: this.modalInfo,
            options: {
                mode: 'application/json', // 接受的类型，json xml....
                smartIndent: true, // 是否智能缩进
                styleActiveLine: true, // 当前行高亮
                lineNumbers: true, // 是否显示行数
                indentUnit: 2, // 缩进单位，默认2
                gutters: [
                    'CodeMirror-linenumbers',
                    'CodeMirror-foldgutter',
                    'CodeMirror-lint-markers' // CodeMirror-lint-markers是实现语法报错功能
                ],
                lint: true,
                matchBrackets: true, // 括号匹配显示
                autoCloseBrackets: true, // 输入和退格时成对
                readOnly: this.readonly, // 只读
                foldGutter: true,
                autoRefresh: true
            },
            loading: false,
            viewHeight: window.innerHeight * 0.9 - 200,
            top: window.LOCAL_CONFIG.HEADER_VISIBLE === true ? 10 : 100
        };
    },
    methods: {
        async onSave() {
            try {
                if (getByteSize(this.modalData.data) > 16 * 1024 * 1024){
                    this.$hMessage.error('文件大小超出16MB');
                    return;
                }
                this.loading = true;
                const result = await rcmConfigSave({
                    rcmId: this.rcmId,
                    config: this.modalData.data
                });
                if (result.code === '200') {
                    this.modalData.status = false;
                    this.$emit('reload');
                }
            } finally {
                this.loading = false;
            }
        }
    },
    components: { codemirror }
};
</script>
<style>
/* 确保 lint 提示框在其他元素之上显示 */
/* stylelint-disable-next-line selector-class-pattern */
.CodeMirror-lint-tooltip {
    /* stylelint-disable-next-line plugin/z-index-value-constraint */
    z-index: 1001;
}

/* 确保 cm-s-default 主题不会覆盖样式 */
/* stylelint-disable-next-line selector-class-pattern */
.cm-s-default .CodeMirror-lint-tooltip {
    /* stylelint-disable-next-line plugin/z-index-value-constraint */
    z-index: 1001;
}
</style>
<style scoped>
.edit-config {
    height: 100%;

    .CodeMirror {
        height: 100%;
    }
}

.footer {
    text-align: center;
}

/deep/ .h-modal-body {
    padding: 0 10px 0 0;
}

/deep/ .cm-s-default {
    height: 100% !important;
}

</style>
