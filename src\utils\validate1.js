/**
 * @description: 内存表各类型字段值校验类
 */
import { byteLength } from '@/utils/utils';

const matchRules = {
    int: {
        minValue: -2147483648,
        maxValue: 2147483647
    },
    'unsigned int': {
        minValue: 0,
        maxValue: 4294967295
    },
    short: {
        minValue: -32768,
        maxValue: 32767
    },
    'short int': {
        minValue: -32768,
        maxValue: 32767
    },
    'unsigned short': {
        minValue: 0,
        maxValue: 65535
    },
    'short unsigned int': {
        minValue: 0,
        maxValue: 65535
    },
    long: {
        minValue: -(2 ** 63),
        maxValue: 2 ** 63 - 1
    },
    'long int': {
        minValue: -(2 ** 63),
        maxValue: 2 ** 63 - 1
    },
    'long long': {
        minValue: -(2 ** 63),
        maxValue: 2 ** 63 - 1
    },
    'long long int': {
        minValue: -(2 ** 63),
        maxValue: 2 ** 63 - 1
    },
    'unsigned long': {
        minValue: 0,
        maxValue: 2 ** 64 - 1
    },
    'long unsigned int': {
        minValue: 0,
        maxValue: 2 ** 64 - 1
    },
    'unsigned long long': {
        minValue: 0,
        maxValue: 2 ** 64 - 1
    },
    'long long unsigned int': {
        minValue: 0,
        maxValue: 2 ** 64 - 1
    },
    float: {
        unInteger: true,
        minValue: -3.40282 * 10 ** 38,
        maxValue: 3.40282 * 10 ** 38
    },
    double: {
        unInteger: true,
        minValue: -1.79769 * 10 ** 308,
        maxValue: 1.79769 * 10 ** 308
    },
    'long double': {
        unInteger: true,
        minValue: -1.19 * Math.pow(10, 4932),
        maxValue: 1.19 * Math.pow(10, 4932)
    }
};
export class ValidateClass {
    constructor(type, size) {
        this.type = type;
        this.size = size;
    }

    /**
     * 内存表数据校验规则
     * @param {String} type 字符类型
     * @param {Number} size 字符大小
     * @returns 校验方法
     */
    static matchValidate(type, size) {
        const validate = new ValidateClass(type, size);
        if (type === 'char' || type === 'signed char' || type === 'unsigned char') {
            return [{ test: this.charValidate, trigger: 'blur, change' }];
        } else if (type === 'bool') {
            return [{ test: this.boolValidate, trigger: 'blur, change' }];
        } else if (type.indexOf('char[') !== -1) {
            return [{ test: validate.stringValidate.bind(validate), trigger: 'blur, change' }];
        } else {
            return [{ test: validate.validateFunction.bind(validate), trigger: 'blur, change' }];
        }
    }

    static charValidate(rule, val, callback) {
        if (val.charCodeAt() > 256 || val.charCodeAt() < 0 || val.length > 1) return callback(new Error('所占字节数超出输入范围'));
        return callback();
    }

    static boolValidate(rule, val, callback) {
        if (isNaN(val)) return callback(new Error('请输入0或1'));
        if (val !== '0' && val !== '1') return callback(new Error('请输入0或1'));
        return callback();
    }

    validateFunction(rule, val, callback) {
        const { minValue, maxValue, unInteger } = matchRules?.[this.type];
        if (val.match(/^[ ]*$/)) return callback(new Error('输入不能为空'));
        if (isNaN(val) || val.search('e') !== -1) return callback(new Error('请输入数字类型'));
        if (!unInteger && Number(val) % 1 !== 0) return callback(new Error('请输入整型数字'));
        if (val < minValue || val > maxValue) {
            return callback(new Error(`超出输入范围，输入范围在${minValue} ~ ${maxValue}`));
        }
        return callback();
    }

    stringValidate(rule, val, callback) {
        if (byteLength(val) > this.size - 1) {
            return callback(new Error('所占字节数超出输入范围'));
        } else {
            return callback();
        }
    }
}
