<template>
    <div>
        <h-msg-box
            v-model="modalData.status"
            :mask-closable="false"
            title="SSH配置"
            width="700"
            maxHeight="400"
            @on-open="getCollections">
            <h-form ref="formValidate" :model="formValidate" :label-width="80">
                <h-form-item label="账号" prop="loginUser" required>
                    <h-input
                        v-model.trim="formValidate.loginUser"
                        :maxlength="100"
                        autocomplete="off"
                        placeholder="请输入账号">
                    </h-input>
                </h-form-item>
                <h-form-item label="密码" prop="loginPwd" required>
                    <h-input
                        v-model.trim="formValidate.loginPwd"
                        :type="showPwd ? 'text' : 'password'"
                        :icon="showPwd ? 'browse_fill' : 'eye-disabled'"
                        autocomplete="new-password"
                        placeholder="请输入密码"
                        :maxlength="100"
                        @on-click="toggleShowPwd">
                    </h-input>
                </h-form-item>
                <h-form-item label="端口号" prop="sshPort" required :validRules="portRule">
                    <h-input
                        v-model.trim="formValidate.sshPort"
                        type="int"
                        specialFilter
                        :specialDecimal="0"
                        placeholder="请输入端口号">
                    </h-input>
                </h-form-item>
                <h-form-item label="连通测试">
                    <h-button
                        :loading="testLoading"
                        @click="testHostsConnection">连接
                    </h-button>
                </h-form-item>
                <!-- 连通测试结果 -->
                 <h-form-item>
                    <h-table
                        v-if="tableData.length"
                        :loading="testLoading"
                        :columns="columns"
                        :data="tableData"
                    />
                 </h-form-item>
            </h-form>

            <template v-slot:footer>
                <h-button @click="handleCancel">取消</h-button>
                <h-button
                    type="primary"
                    :loading="loading"
                    @click="handleOk">确定
                </h-button>
            </template>
        </h-msg-box>
    </div>
</template>

<script>
import { setProductHosts, testHostsConnection } from '@/api/productApi';
import { validatePort } from '@/utils/validate';
// 引入 pkcs8Rsa 模块
const { encryptData } = require('@/utils/pkcs8Rsa');

export default {
    name: 'ServerSshConfigModal',
    props: {
        productId: {
            type: String,
            default: ''
        },
        modalInfo: {
            type: Object,
            default: () => ({})
        }
    },
    data() {
        return {
            modalData: this.modalInfo,
            showPwd: false,
            loading: false,
            testLoading: false,
            formValidate: {
                loginUser: '',
                loginPwd: '',
                sshPort: 22
            },
            portRule: [{ test: validatePort, trigger: 'change, blur' }],
            columns: [
                {
                    title: '主机名',
                    key: 'hostName',
                    minWidth: 130
                },
                {
                    title: 'IP地址/域名',
                    key: 'ips',
                    minWidth: 180,
                    render: (h, { row }) => {
                        const ips = row.ips ? row.ips?.join(',') : '-';
                        return h('div', ips);
                    }
                },
                {
                    title: 'SSH用户名',
                    key: 'loginUser',
                    minWidth: 120,
                    render: (h, { row }) => {
                        const loginUser = row.loginUser || '-';
                        return h('div', loginUser);
                    }
                },
                {
                    title: '连通性测试结果',
                    key: 'connectionStatus',
                    minWidth: 130,
                    render: (h, { row }) => {
                        return h('span', {
                            style: {
                                color: row?.connectionStatus === 'SUCCESS' ? 'green' : 'red'
                            }
                        },
                        row?.connectionStatus === 'SUCCESS' ? '成功' : `失败`
                        );
                    }
                }
            ],
            tableData: []
        };
    },
    methods: {
        /**
         *  打开弹窗
         */
        getCollections() {
            this.formValidate = {
                ...this.modalData,
                loginUser: this.modalData.loginUser || '',
                loginPwd: '',
                sshPort: this.modalData.sshPort ?? 22
            };
        },
        /**
         * 切换密码显示状态
         */
        toggleShowPwd() {
            this.showPwd = !this.showPwd;
        },
        /**
         * 弹窗确认操作
         */
        handleOk() {
            this.$refs.formValidate.validate(valid => {
                if (valid) {
                    this.setProductHosts();
                }
            });
        },
        handleCancel() {
            this.modalData.status = false;
        },
        /**
        * 产品服务器保存
        */
        async setProductHosts() {
            this.loading = true;
            try {
                const encryptedPwd = encryptData(this.formValidate.loginPwd);
                const params = {
                    productId: this.productId,
                    ids: this.modalData.ids,
                    loginUser: this.formValidate.loginUser,
                    loginPwd: encryptedPwd,
                    sshPort: Number(this.formValidate.sshPort),
                    roomId: this.modalData.roomId
                };
                const res = await setProductHosts(params);
                if (res.code === '200') {
                    this.$emit('update');
                    this.$hMessage.success('配置成功');
                    this.handleCancel();
                } else if (res.code?.length === 8) {
                    this.$hMessage.error(res.message);
                }
            } catch (err) {
                console.error(err);
            } finally {
                this.loading = false;
            }
        },
        /**
        * 测试产品服务器连接
        */
        async testHostsConnection() {
            this.$refs.formValidate.validate(async valid => {
                if (valid) {
                    this.testLoading = true;
                    try {
                        const encryptedPwd = encryptData(this.formValidate.loginPwd);
                        const params = {
                            productId: this.productId,
                            ids: this.modalData.ids,
                            loginUser: this.formValidate.loginUser,
                            loginPwd: encryptedPwd,
                            sshPort: Number(this.formValidate.sshPort)
                        };
                        const res = await testHostsConnection(params);
                        if (res.code === '200') {
                            this.tableData = res.data || [];
                        } else if (res.code?.length === 8) {
                            this.$hMessage.error(res.message);
                        }
                    } catch (err) {
                        console.error(err);
                    }  finally {
                        this.testLoading = false;
                    }
                }
            });
        }
    }
};
</script>

<style scoped lang="less">
/deep/ .h-modal-body {
    padding: 20px 20px 16px;
}

.h-form-item {
    margin-bottom: 15px;
}
</style>
