<template>
  <div class="receiver">
    <!-- 会话信息 -->
    <info-grid ref="session" :gridData="sessionData" @select-change="handleTopicSelectChange"></info-grid>
    <!-- 应用消息处理 -->
    <info-grid :gridData="RecevingData"></info-grid>
    <!-- 通讯分片处理 -->
    <info-grid :gridData="NakingData"></info-grid>
  </div>
</template>
<script>

import infoGrid from '@/components/common/infoBar/infoGrid';
export default {
    props: {
        rcmInfo: {
            type: Object,
            default: () => {}
        }
    },
    data() {
        return {
            // 会话信息
            topicPartion: '',
            sessionData: {
                title: {
                    label: {
                        labelDic: [{ key: 'ConnectIdSessionPhase', label: '会话信息' }],
                        labelInfo: {
                            ConnectIdSessionPhase: '-'
                        }
                    }
                },
                layout: [
                    { x: 0, y: 0, w: 4, h: 6, i: 'Receiver' },
                    { x: 4, y: 0, w: 4, h: 6, i: 'Transport' },
                    { x: 8, y: 0, w: 4, h: 6, i: 'Transmitter' }
                ],
                details: [
                    {
                        type: 'description',
                        title: {
                            label: {
                                labelDic: [{ key: 'ContextId', label: 'Receiver' }],
                                labelInfo: {
                                    ContextId: '-'
                                }
                            }
                        },
                        infoDic: [
                            {
                                label: 'ContextName',
                                key: 'ContextName',
                                span: 12
                            },
                            {
                                label: 'TierName',
                                key: 'TierName',
                                span: 12
                            },
                            {
                                label: 'Role',
                                key: 'Role',
                                span: 12
                            },
                            {
                                label: 'StopRecv',
                                key: 'StopRecv',
                                span: 12
                            }
                        ],
                        info: {
                            Role: '-',
                            ContextName: '-',
                            StopRecv: '-',
                            TierName: '-'
                        }
                    },
                    {
                        type: 'description',
                        title: {
                            label: {
                                labelDic: [{ key: 'TransportId', label: 'Transport' }],
                                labelInfo: {
                                    TransportId: '-'
                                }
                            },
                            slots: [
                                {
                                    type: 'select',
                                    key: 'topicPartion',
                                    options: [],
                                    minWidth: '80px',
                                    maxWidth: '180px',
                                    placeholder: '主题分区'
                                }
                            ]
                        },
                        infoDic: [
                            {
                                label: 'TopicName',
                                key: 'TopicName',
                                span: 12
                            },
                            {
                                label: 'PartitionNo',
                                key: 'PartitionNo',
                                span: 12
                            },
                            {
                                label: 'Addr',
                                key: 'Addr',
                                span: 12
                            },
                            {
                                label: 'Port',
                                key: 'Port',
                                span: 12
                            }
                        ],
                        info: {
                            TopicName: '-',
                            PartitionNo: '-',
                            Addr: '-',
                            Port: '-'
                        }
                    },
                    {
                        type: 'description',
                        title: {
                            label: {
                                labelDic: [{ key: 'ContextId', label: 'Transmitter' }],
                                labelInfo: {
                                    ContextId: '-'
                                }
                            }
                        },
                        infoDic: [
                            {
                                label: 'ContextName',
                                key: 'ContextName',
                                span: 12
                            },
                            {
                                label: 'TierName',
                                key: 'TierName',
                                span: 12
                            },
                            {
                                label: 'Ip',
                                key: 'Ip',
                                span: 12
                            }
                        ],
                        info: {
                            TierName: '-',
                            ContextName: '-',
                            Ip: '-'
                        }
                    }
                ]
            },
            // 应用消息处理
            RecevingData: {
                title: {
                    label: '应用消息处理'
                },
                layout: [
                    { x: 0, y: 0, w: 4, h: 6.5, i: '消息接收' },
                    { x: 4, y: 0, w: 4, h: 6.5, i: '消息应答' },
                    { x: 8, y: 0, w: 4, h: 6.5, i: '消息投递' }
                ],
                details: [
                    {
                        type: 'monitor',
                        title: {
                            label: '消息接收',
                            noTagColor: true,
                            slots: [
                                {
                                    type: 'text',
                                    label: 'LastRecvMilli',
                                    value: '-'
                                }
                            ]
                        },
                        gridSpan: 2,
                        info: [
                            {
                                type: 'text',
                                label: 'NextMsgNo',
                                key: 'NextMsgNo',
                                value: '-',
                                labelAlias: '下一个待接收消息序号'
                            },
                            {
                                type: 'text',
                                label: 'MaxMsgNo',
                                key: 'MaxMsgNo',
                                value: '-',
                                labelAlias: '收到的最大消息序号'
                            }
                        ]
                    },
                    {
                        type: 'monitor',
                        title: {
                            label: '消息投递',
                            noTagColor: true
                        },
                        gridSpan: 2,
                        info: [
                            {
                                type: 'text',
                                label: 'NextMsgNoDeliver',
                                key: 'NextMsgNoDeliver',
                                value: '-',
                                labelAlias: '下一个待处理消息序号'
                            }
                        ]
                    },
                    {
                        type: 'monitor',
                        title: {
                            label: '消息应答',
                            noTagColor: true

                        },
                        gridSpan: 2,
                        info: [
                            {
                                type: 'text',
                                label: 'MsgNoAcked',
                                key: 'MsgNoAcked',
                                value: '-',
                                labelAlias: '已经确认的消息序号'
                            }
                        ]
                    }
                ]
            },
            // 通讯分片处理
            NakingData: {
                title: {
                    label: '通讯分片处理'
                },
                layout: [
                    { x: 0, y: 0, w: 6, h: 6.5, i: '分片接收' },
                    { x: 6, y: 0, w: 6, h: 6.5, i: '分片应答' },
                    { x: 0, y: 1, w: 6, h: 8.5, i: '分片补缺' },
                    { x: 6, y: 1, w: 6, h: 8.5, i: '乱序和丢包检测' }
                ],
                details: [
                    {
                        type: 'monitor',
                        title: {
                            label: '分片接收',
                            noTagColor: true
                        },
                        gridSpan: 3,
                        info: [
                            {
                                type: 'text',
                                label: 'NextSqn',
                                key: 'NextSqn',
                                value: '-',
                                labelAlias: '下一个待接收分片序号'
                            },
                            {
                                type: 'text',
                                label: 'NextSqnPop',
                                key: 'NextSqnPop',
                                value: '-',
                                labelAlias: '下一个待取消息分片序号'
                            },
                            {
                                type: 'text',
                                label: 'MaxSqn',
                                key: 'MaxSqn',
                                value: '-',
                                labelAlias: '收到的最大分片序号'
                            }
                        ]
                    },
                    {
                        type: 'monitor',
                        title: {
                            label: '分片应答',
                            noTagColor: true,
                            slots: [
                                {
                                    type: 'text',
                                    label: 'LastAckMilli',
                                    value: '-'
                                }
                            ]
                        },
                        gridSpan: 3,
                        info: [
                            {
                                type: 'text',
                                label: 'SqnAcked',
                                key: 'SqnAcked',
                                value: '-',
                                labelAlias: '已经确认的分片号'
                            },
                            {
                                type: 'text',
                                label: 'LastAckReason',
                                key: 'LastAckReason',
                                value: '-',
                                labelAlias: '最后发送ACK的原因'
                            }
                        ]
                    },
                    {
                        type: 'monitor',
                        title: {
                            label: '分片补缺',
                            noTagColor: true,
                            slots: [
                                {
                                    type: 'text',
                                    label: 'LastNakMilli',
                                    value: '-'
                                }
                            ]
                        },
                        gridSpan: 2,
                        info: [
                            {
                                type: 'obj',
                                label: '补缺执行',
                                infoDic: [
                                    {
                                        label: 'MaxSqnLastNak',
                                        key: 'MaxSqnLastNak'
                                    },
                                    {
                                        label: 'MaxSqnToNak',
                                        key: 'MaxSqnToNak'
                                    }
                                ],
                                info: {
                                    MaxSqnLastNak: '-',
                                    MaxSqnToNak: '-'
                                }
                            },
                            {
                                type: 'obj',
                                label: '补缺统计',
                                infoDic: [
                                    {
                                        label: 'NakCount',
                                        key: 'NakCount'
                                    },
                                    {
                                        label: 'QuickNakCount',
                                        key: 'QuickNakCount'
                                    }
                                ],
                                info: {
                                    NakCount: '-',
                                    QuickNakCount: '-'
                                }
                            }
                        ]
                    },
                    {
                        type: 'monitor',
                        title: {
                            label: '丢包检测',
                            noTagColor: true
                        },
                        gridSpan: 2,
                        info: [
                            {
                                type: 'obj',
                                label: '丢包统计',
                                infoDic: [
                                    {
                                        label: 'PacketLossRate',
                                        key: 'PacketLossRate'
                                    },
                                    {
                                        label: 'RecentlyFiveSecondsLossRate',
                                        key: 'RecentlyFiveSecondsLossRate'
                                    }
                                ],
                                info: {
                                    PacketLossRate: '-',
                                    RecentlyFiveSecondsLossRate: '-'
                                }
                            }
                        ]
                    }
                ]
            }
        };
    },
    components: { infoGrid },
    methods: {
        initData() {
            this.getFileData();
        },
        // 会话信息
        setSessionDetail(data){
            this.sessionData.title.label.labelInfo.ConnectIdSessionPhase = (data?.ConnectId || '') + ' - ' + (data?.SessionPhase || '');
            this.sessionData.details.forEach((item) => {
                Object.keys(item.title.label.labelInfo).forEach(key => {
                    item.title.label.labelInfo[key] = data?.[key];
                });
                Object.keys(item.info).forEach(key => {
                    item.info[key] = data?.[key];
                });
            });

            this.sessionData.details[0].title.label.labelInfo.ContextId = this.rcmInfo?.ContextId;
            this.sessionData.details[0].info.Role = this.rcmInfo?.Role;
            this.sessionData.details[0].info.ContextName = this.rcmInfo?.ContextName;
            this.sessionData.details[0].info.TierName = this.rcmInfo?.TierName;
        },
        // 应用消息处理
        setRecevingDetail(data){
            this.RecevingData.details.forEach((item) => {
                item.title.slots && (item.title.slots[0].value = data?.[item.title.slots[0].label]);
                item.info.forEach(o => {
                    o.value = data?.[o.key];
                });
            });
        },
        // 网络分片处理
        setNakingDetail(data){
            this.NakingData.details.forEach((item) => {
                item.title.slots && (item.title.slots[0].value = data[item.title.slots[0].label]);
                item.info.forEach(o => {
                    o.type === 'text' && (o.value = data?.[o.key]);
                    o.type === 'obj' && (
                        Object.keys(o.info).forEach(key => {
                            o.info[key] = data?.[key];
                        })
                    );
                });
            });
        },
        // 切换主题分区
        handleTopicSelectChange(val) {
            this.topicPartion = val;
            const data = (this.rcmInfo?.Receiver?.Rms || []).filter(o => [o?.ContextId, o?.TransportId].join('@') === val)?.[0] || {};
            this.setSessionDetail(data);
            this.setRecevingDetail(data);
            this.setNakingDetail(data);
        },
        // 构造页面数据
        getFileData() {
            const options = (this.rcmInfo?.Receiver?.Rms || []).map(o => {
                return {
                    value: [o?.ContextId, o?.TransportId].join('@'),
                    label: `${o?.TopicName}[${o?.PartitionNo}]`
                };
            });
            this.sessionData.details[1].title.slots[0].options = options;
            this.$nextTick(() => {
                const value = options.filter(o => o?.value === this.topicPartion)?.[0]?.value || options?.[0]?.value || '';
                this.$refs['session'].$refs['info-bar'][1].setSelectVal('topicPartion',  value);
            });
        }
    }
};
</script>
