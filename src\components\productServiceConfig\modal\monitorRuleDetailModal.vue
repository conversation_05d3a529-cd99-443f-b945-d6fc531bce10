<template>
    <div>
        <!-- 下发配置列表 -->
        <h-msg-box-safe
            v-model="modalData.status"
            :escClose="true"
            :mask-closable="false"
            :title="modalData.title"
            width="600"
            maxHeight="350"
        >
            <div v-for="(item, index) in modalData.contentList" :key="index" class="rule-item">
                <span class="rule-item-title">{{ item.label }}</span>
                <span class="rule-item-text">{{ item.text || '-' }}</span>
            </div>

            <template v-slot:footer>
                <h-button @click="modalData.status = false">关闭</h-button>
            </template>
        </h-msg-box-safe>
    </div>
</template>

<script>
export default {
    name: 'MonitorRuleDetailModal',
    props: {
        modalInfo: {
            type: Object,
            default: null
        }
    },
    data() {
        return {
            modalData: this.modalInfo
        };
    }
};
</script>

<style lang="less" scoped>
    /deep/ .h-modal-body {
        padding: 16px 32px;
    }

    .rule-item {
        display: flex;
        margin-bottom: 8px;

        & > .rule-item-title {
            display: block;
            width: 80px;
            flex: none;
        }

        & > .rule-item-text {
            display: block;
            flex-grow: 1;
            user-select: text;
        }
    }
</style>
