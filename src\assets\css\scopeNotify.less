.scope {
    width: auto;
    height: auto;
    box-sizing: border-box;
    padding: 10px 14px;
    position: fixed;
    top: 125px;
    right: 14px;
    background: var(--base-color);
    border-radius: 6px;
    cursor: crosshair;

    & > p {
        line-height: 20px;
        font-size: var(--font-size-base);
        color: var(--font-color);

        & > i {
            color: var(--font-color);
            font-style: normal;
        }
    }

    span {
        display: inline-block;
        background: var(--link-color);
        padding: 4px 6px;
        color: var(--font-color);
        font-size: var(--font-size-base);
        border-radius: var(--border-radius);
        margin-top: 5px;
        cursor: pointer;

        &:hover {
            background: var(--link-color);
        }
    }
}
