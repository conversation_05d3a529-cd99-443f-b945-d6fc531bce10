.process {
    width: 100%;
    display: flex;

    .process-label {
        line-height: 36px;
        width: 15%;
        text-align: center;
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
        color: var(--font-opacity-color);
    }

    .process-bar-box {
        width: 100%;
        border-radius: var(--border-radius);
        padding: 10px;
        margin: 5px;
        background-color: var(--border-color);

        & > div {
            position: relative;
            background: #2d8de536;
            height: 12px;
            border-radius: 2px;

            & > span {
                position: absolute;
                top: -3px;
                left: 50%;
                width: 70%;
                font-weight: 600;
                text-align: center;
                transform: translateX(-50%);
                color: var(--font-opacity-color);
                overflow: hidden;
                white-space: nowrap;
                text-overflow: ellipsis;
            }
        }

        .progress-box {
            background: var(--box-color);

            & > span {
                color: var(--font-color);
            }
        }

        .progress-bar {
            height: 100%;
            border-radius: 2px;
            transition-property: all;
            transition-duration: 1s;
            background: var(--link-color);
        }
    }
}
