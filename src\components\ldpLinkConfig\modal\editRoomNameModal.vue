<!-- 机房配置弹窗 -->
<template>
    <div>
        <h-msg-box-safe
            v-model="modalData.status"
            :mask-closable="false"
            title="修改机房别名"
            width="600"
            @submit.native.prevent
            @on-open="handleOpen">
            <h-form
                ref="formValidate"
                :model="formValidate"
                :label-width="80">
                    <h-form-item
                        label="机房别名"
                        prop="roomNameAlias"
                        required>
                        <h-input
                            v-model="formValidate.roomNameAlias"
                            :maxlength="30"
                            placeholder="请输入机房别名">
                        </h-input>
                    </h-form-item>
            </h-form>

            <template v-slot:footer>
                <h-button @click="modalData.status = false">取消</h-button>
                <h-button
                    type="primary"
                    :loading="loading"
                    @click="submitConfig">确定
                </h-button>
            </template>
        </h-msg-box-safe>
    </div>
</template>

<script>
import { updateMachineRoomInfo } from '@/api/productApi';

export default {
    name: 'EditRoomNameModal',
    components: { },
    props: {
        productId: {
            type: String,
            default: ''
        },
        modalInfo: {
            type: Object,
            default: null
        }
    },
    data() {
        return {
            modalData: this.modalInfo,
            formValidate: {
                roomNameAlias: ''
            },
            roomsList: [],
            loading: false
        };
    },
    methods: {
        async handleOpen() {
            this.formValidate.roomNameAlias = this.modalData?.roomNameAlias || '';
        },
        submitConfig() {
            this.$refs['formValidate'].validate((valid) => {
                if (valid) {
                    this.setRoomNameAlias();
                }
            });
        },
        async setRoomNameAlias() {
            this.loading = true;
            try {
                const params = {
                    productId: this.productId,
                    roomId: this.modalData.roomId,
                    roomNameAlias: this.formValidate.roomNameAlias
                };
                const res = await updateMachineRoomInfo(params);
                if (res.code === '200') {
                    this.$emit('update');
                    this.$hMessage.success('修改成功');
                    this.modalData.status = false;
                } else if (res.code?.length === 8) {
                    this.$hMessage.error(res.message);
                }
            } finally {
                this.loading = false;
            }
        }
    }
};
</script>

