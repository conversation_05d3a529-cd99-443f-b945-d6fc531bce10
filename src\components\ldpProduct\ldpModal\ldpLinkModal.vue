<!--
 * @Description: 对接LDP业务系统弹窗
 * @Author: <PERSON><PERSON>
 * @Date: 2022-08-16 11:34:55
 * @LastEditTime: 2022-08-16 15:43:16
 * @LastEditors: <PERSON><PERSON>
-->
<template>
    <div>
        <!-- 下发配置列表 -->
        <h-msg-box-safe v-model="modalData.status" :escClose="true" :mask-closable="false" title="连接LDP业务系统" width="50">
            <h-form ref="formValidate" :model="formItem" :label-width="130">
                <h-row>
                    <h-col span="10">
                        <h-form-item label="注册中心地址：" prop="name" required>
                            <h-input v-model="formItem.name" placeholder="请输入"
                                onkeydown="if(event.keyCode==13){event.keyCode=0;event.returnValue=false;}"></h-input>
                        </h-form-item>
                    </h-col>
                    <h-col span="10">
                        <h-form-item label="注册中心端口：" prop="name" required>
                            <h-input v-model="formItem.name" placeholder="请输入"
                                onkeydown="if(event.keyCode==13){event.keyCode=0;event.returnValue=false;}"></h-input>
                        </h-form-item>
                    </h-col>
                    <h-col span="2">
                        <a-button type="primary" style="margin-left: 30px;">连接</a-button>
                    </h-col>
                </h-row>
                <hr style="height: 1px; border: none; border-top: 1px solid #e7e7e7;" />
                <br />
                <h-form-item label="产品节点列表：" prop="name" required>
                    <h-select v-model="formItem.select" filterable style="width: 200px;" :positionFixed="true">
                        <h-option value="beijing">北京市</h-option>
                        <h-option value="shanghai">上海市</h-option>
                        <h-option value="shenzhen">深圳市</h-option>
                    </h-select>
                </h-form-item>
                <h-form-item label="产品部署结构：" prop="name" required>
                    <h-select v-model="formItem.select" filterable style="width: 200px;" :positionFixed="true" >
                        <h-option value="beijing">北京市</h-option>
                        <h-option value="shanghai">上海市</h-option>
                        <h-option value="shenzhen">深圳市</h-option>
                    </h-select>
                </h-form-item>
            </h-form>
            <template v-slot:footer>
                <a-button type="ghost" @click="cancel">取消</a-button>
                <a-button type="primary" style="margin-left: 8px;" @click="submitForm">创建</a-button>
            </template>
        </h-msg-box-safe>
    </div>
</template>

<script>
import aButton from '@/components/common/button/aButton';
export default {
    data() {
        return {
            modalData: {
                status: true
            },
            formItem: {}
        };
    },
    components: {  aButton }
};
</script>
