import fetch from './fetch';
import { objectToQueryString } from '@/utils/utils';

const prefix = window['LOCAL_CONFIG']['API_HOME'] || '/';
/**
 * 创建RCM产品实例
 */
export function addRcmProductInstance(param) {
    return fetch().post(`${prefix}/rcm/product`, param);
}

/**
 * 删除RCM产品实例
 */
export function delRcmProductNode(param) {
    return fetch().post(`${prefix}/rcm/products/delete`, param);
}

/**
 * 获取rcm产品节点列表
 */
export function getRcmProductList() {
    return fetch().get(`${prefix}/rcm/products`);
}

/**
 * 获取rcm实例节点列表
 */
export function getRcmInstanceList(param) {
    return fetch().get(`${prefix}/product/ldpConfigs?${objectToQueryString(param)}`);
}

/**
 * 获取rcm产品实例总览基础信息
 */
export function getRcmOverview(param) {
    return fetch().get(`${prefix}/rcm/products/config?${objectToQueryString(param)}`);
}

/**
 * 更新ldpadmin信息
 */
export function updataLdpAdminInfo(param) {
    return fetch().post(`${prefix}/rcm/products/manage_config`, param);
}

/**
 * 删除ldpadmin信息
 */
export function deleteldpAdminConfig(param) {
    return fetch().post(`${prefix}/rcm/products/manage_config/delete`, param);
}

/**
 * 获取rcm上下文分组分页信息
 */
export function getRcmContextGroup(param) {
    return fetch().get(`${prefix}/rcm/products/contexts/group?${objectToQueryString(param)}`);
}

/**
 * 删除rcm上下文分组
 */
export function delContextGroup(param) {
    return fetch().post(`${prefix}/rcm/products/contexts/group/delete`, param);
}

/**
 * 更新rcm物料配置信息
 */
export function updateRcmMaterialInfo(param) {
    return fetch().post(`${prefix}/rcm/products/material-range`, param);
}

/**
 * 上下文详情-查询
 */
export function getContextDetial(param) {
    return fetch().get(`${prefix}/rcm/products/context?${objectToQueryString(param)}`);
}

/**
 * 查询RCM模板列表
 */
export function getRcmProductTemplates(param) {
    return fetch().get(`${prefix}/rcm/products/templates?${objectToQueryString(param)}`);
}

/**
 * 主题-创建或更新主题
 */
export function createTopics(param) {
    return fetch().post(`${prefix}/rcm/products/transport`, param);
}

/**
 * 主题-分页查询
 */
export function queryTopics(param) {
    return fetch().get(`${prefix}/rcm/products/transports?${objectToQueryString(param)}`);
}

/**
 * 主题-批量删除
 */
export function deleteTopic(param) {
    return fetch().post(`${prefix}/rcm/products/transports/delete`, param);
}

/**
 * 主题-批量添加主题标签
 */
export function addTopicTags(param) {
    return fetch().post(`${prefix}/rcm/products/transports/tags`, param);
}
/**
 * 主题-批量删除主题标签
 */
export function delTopicTags(param) {
    return fetch().post(`${prefix}/rcm/products/transports/tags/delete`, param);
}

/**
 * 主题-查询所有主题标签
 */
export function queryTopicsTags(param) {
    return fetch().get(`${prefix}/rcm/products/transports/tags?${objectToQueryString(param)}`);
}

/**
 * 配置模板查询
 */
export function queryTemplates(param) {
    return fetch().get(`${prefix}/rcm/products/templates?${objectToQueryString(param)}`);
}

/**
 * 查询模板详情信息
 */
export function getTemplateDetail(param) {
    return fetch().get(`${prefix}/rcm/products/template?${objectToQueryString(param)}`);
}

/**
 * 上下文-创建
 */
export function createContext(param) {
    return fetch().post(`${prefix}/rcm/products/context`, param);
}

/**
 * 上下文-批量删除
 */
export function deleteContext(param) {
    return fetch().post(`${prefix}/rcm/products/contexts/delete`, param);
}

/**
 * 上下文-根据查询条件批量删除
 */
export function betchDeleteContext(param) {
    return fetch().get(`${prefix}/rcm/products/contexts/delete/batch?${objectToQueryString(param)}`);
}

/**
 * 上下文-批量添加上下文标签
 */
export function addContextTags(param) {
    return fetch().post(`${prefix}/rcm/products/contexts/tags`, param);
}
/**
 * 上下文-批量删除上下文标签
 */
export function delContextTags(param) {
    return fetch().post(`${prefix}/rcm/products/contexts/tags/delete`, param);
}

/**
 * 主题-查询所有上下文标签
 */
export function queryContextTags(param) {
    return fetch().get(`${prefix}/rcm/products/contexts/tags?${objectToQueryString(param)}`);
}

/**
 * 上下文--查询
 */
export function queryContext(param) {
    return fetch().get(`${prefix}/rcm/products/contexts?${objectToQueryString(param)}`);
}
/*
 * 查询RCM模板详情
 */
export function getRcmTempDetail(param) {
    return fetch().get(`${prefix}/rcm/products/template?${objectToQueryString(param)}`);
}

/**
 * 创建主题模板
 */
export function createTopicTemp(param) {
    return fetch().post(`${prefix}/rcm/products/transport/template`, param);
}

/**
 * 创建上下文模板
 */
export function createContextTemp(param) {
    return fetch().post(`${prefix}/rcm/products/context/template`, param);
}

/**
 * 获取本地与远程rcm配置信息
 */
export function getLocalRemoteConfig(param) {
    return fetch().get(`${prefix}/rcm/products/local-remote?${objectToQueryString(param)}`);
}

/**
 * 查询发布历史版本
 */
export function getRcmProductsHistory(param) {
    return fetch().get(`${prefix}/rcm/products/history?${objectToQueryString(param)}`, param);
}

/**
 * 配置模板删除
 */
export function delRcmTemp(param) {
    return fetch().post(`${prefix}/rcm/products/template/delete`, param);
}

/**
 * LDP产品查询RCM配置
 */
export function getRcmDeployInfo(param) {
    return fetch().get(`${prefix}/product/rcm/config?${objectToQueryString(param)}`);
}

/**
 * LDP产品关联RCM配置
 */
export function connectRcmDeploy(param) {
    return fetch().post(`${prefix}/product/rcm/config`, param);
}

/**
 * 获取zookeeper LDP产品配置路径
 */
export function getZkRcmPath(param) {
    return fetch().get(`${prefix}/zookeeper/ldp/config-path?${objectToQueryString(param)}`);
}

/**
 * 获取上下文运行时信息
 */
export function getCtxRunningInfo(param) {
    return fetch().get(`${prefix}/rcm/products/context/running-info?${objectToQueryString(param)}`);
}

/**
 * rcm配置发布
 */
export function rcmConfigRelase(param) {
    return fetch().post(`${prefix}/rcm/products/publish`, param);
}

/**
 * 恢复历史版本rcm配置
 */
export function rcmHistoryResume(param) {
    return fetch().post(`${prefix}/rcm/products/history/resume`, param);
}

/**
 * 下载RCM文档
 */
export function downloadRcmFile(param) {
    return fetch().get(`${prefix}/rcm/products/export?${objectToQueryString(param)}`);
}

/**
 * rcm告警状态查询
 */
export function getRcmAlertStatus() {
    return fetch().get(`${prefix}/rcm/alerts/status`);
}

/**
 * 根据id获取rcm文档信息
 */
export function getRcmFileContent(param) {
    return fetch().get(`${prefix}/rcm/products/rcmConfig?${objectToQueryString(param)}`);
}

/**
 * 查询可观测主题
 */
export function getObservableTopic(param) {
    return fetch().get(`${prefix}/observables/dashboards/topics?${objectToQueryString(param)}`);
}

/**
 * 查询可观测集群信息
 */
export function getObservableClusters(param) {
    return fetch().get(`${prefix}/product/appClusters/?${objectToQueryString(param)}`);
}

/**
 * 获取应用节点连接信息
 */
export function getAppInfo(param) {
    return fetch().get(`${prefix}/observables/dashboards/instance/info?${objectToQueryString(param)}`);
}

/**
 * 获取应用节点连接信息
 */
export function getAppClusterInfo(param) {
    return fetch().get(`${prefix}/observables/dashboards/appCluster/info?${objectToQueryString(param)}`);
}

/**
 * 获取应用连接关系
 */
export function getObservableInstanceTopology(param) {
    return fetch().get(`${prefix}/observables/dashboards/instanceTopology?${objectToQueryString(param)}`);
}

/**
 * 获取集群连接关系
 */
export function getObservableClusterTopology(param) {
    return fetch().get(`${prefix}/observables/dashboards/clusterTopology?${objectToQueryString(param)}`);
}

/**
 * 获取应用拓扑关系
 */
export function getObservableAppInstanceTopology(param) {
    return fetch().get(`${prefix}/observables/dashboards/instanceTypeTopology?${objectToQueryString(param)}`);
}

/**
 * 获取新RCM拓扑结构
 */
export function getRcmTopology(param) {
    return fetch().post(`${prefix}/rcm/products/context/topology`, param);
}

// ---------------------------------------------------------------- rcm积压监控 --------------------------------------------------------------------------

/**
 * 监控-上下⽂积压查询
 */
export function getContextsBacklog(param) {
    return fetch({ timeout: 10000 }).post(`${prefix}/rcm/products/contexts/backlog`, param);
}

// ---------------------------------------------------------------- 主播域 --------------------------------------------------------------------------

/**
 * 新增中心内配置
 */
export function createZoneConfig(param) {
    return fetch().post(`${prefix}/rcm/products/zones/create`, param);
}

/**
 * 修改中心内配置
 */
export function updateZoneConfig(param) {
    return fetch().post(`${prefix}/rcm/products/zones/update`, param);
}

/**
 * 删除中心内配置
 */
export function deleteZoneConfig(param) {
    return fetch().post(`${prefix}/rcm/products/zones/delete`, param);
}

/**
 * 组播域-获取中⼼内配置列表
 */
export function getZones(param) {
    return fetch().get(`${prefix}/rcm/products/zones?${objectToQueryString(param)}`);
}

/**
 * 组播域-查询中⼼间配置列表
 */
export function getInterZones(param) {
    return fetch().get(`${prefix}/rcm/products/inter-zones?${objectToQueryString(param)}`);
}

/**
 * 删除中心间配置
 */
export function deleteInterZoneConfig(param) {
    return fetch().post(`${prefix}/rcm/products/inter-zones/delete`, param);
}

/**
 * 修改中心间配置
 */
export function updateInterZoneConfig(param) {
    return fetch().post(`${prefix}/rcm/products/inter-zones/update`, param);
}

/**
 * 新增中心间配置
 */
export function createInterZoneConfig(param) {
    return fetch().post(`${prefix}/rcm/products/inter-zones/create`, param);
}

/**
 * rcm配置保存接口
 */
export function rcmConfigSave(param) {
    return fetch().post(`${prefix}/rcm/products/config`, param);
}
