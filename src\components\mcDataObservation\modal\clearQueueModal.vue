<template>
    <div>
        <h-msg-box
            v-model="modalData.status"
            :mask-closable="false"
            :closable="false"
            title="清空队列"
            width="600"
            max-height="300"
            @on-open="getCollections"
        >
            <h-spin v-if="loading" fix>
                <h-icon name="load-c" size="18" class="demo-spin-icon-load"></h-icon>
                <div>清空中</div>
            </h-spin>
            <div v-else class="text-container">
                <p v-show="clearStatus === 0">是否清空队列所有死信消息？</p>
                <p v-show="clearStatus === 1">清空成功！</p>
                <p v-show="clearStatus === 2">清空失败：{{errorText}}</p>
            </div>

            <template v-slot:footer>
                <h-button @click="modalData.status = false">取消</h-button>
                <h-button type="primary" :loading="loading" @click="submitConfig">确认</h-button>
            </template>
        </h-msg-box>
    </div>
</template>

<script>
import { getManagerProxy } from '@/api/mcApi';
export default {
    name: 'McMsgContentModal',
    props: {
        modalInfo: {
            type: Object,
            default: null
        }
    },
    data() {
        return {
            modalData: this.modalInfo,
            loading: false,
            clearStatus: 0,
            errorText: ''
        };
    },
    methods: {
        getCollections() {
            this.clearStatus = 0;
            this.errorText = '';
        },
        async submitConfig() {
            // 清理完成确认
            if (this.clearStatus !== 0) {
                this.modalData.status = false;
                return;
            }

            // 确认清理
            this.loading = true;
            const clearRes = await this.getMcAPi();
            if (clearRes?.ErrorNo === '0') {
                this.clearStatus = 1;
            } else {
                this.clearStatus = 2;
                this.errorText = clearRes?.ErrorInfo;
            }
            this.loading = false;
        },
        // 接口请求
        async getMcAPi() {
            let data = {};
            try {
                const param = this.modalInfo?.reqParams;
                const res = await getManagerProxy(JSON.stringify(param));
                if (res.code === '200' && res.data?.[0]?.Result?.length) {
                    data = res.data[0].Result?.[0];
                    this.$emit('update');
                } else if (res.data?.[0]?.ErrorMsg) {
                    this.$hMessage.error(res.data?.[0]?.ErrorMsg);
                }
            } catch (err) {
                console.error(err);
            }
            return data;
        }
    }
};
</script>

<style lang="less" scoped>
.text-container {
    text-align: center;

    p {
        font-size: 13px;
        font-weight: 600;
    }
}

.demo-spin-icon-load {
    animation: ani-demo-spin 1s linear infinite;
    display: inline-block;
}

@keyframes ani-demo-spin {
    from {
        transform: rotate(0deg);
    }

    50% {
        transform: rotate(180deg);
    }

    to {
        transform: rotate(360deg);
    }
}
</style>
