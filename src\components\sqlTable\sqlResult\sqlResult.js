/*
 * @Description: sql执行结果集
 * @Author: <PERSON><PERSON>
 * @Date: 2023-08-24 15:43:07
 * @LastEditTime: 2023-11-29 17:58:10
 * @LastEditors: yingzx38608 <EMAIL>
 */
import _ from 'lodash';
import { exportCsv } from '@/utils/utils';
import './sqlResult.less';
import '@/assets/css/poptip-1.less';
import aTable from '@/components/common/table/aTable';
import aTips from '@/components/common/apmTips/aTips';
import sqlExportModal from './sqlExportModal.vue';
import { getMemoryDataBySql } from '@/api/memoryApi';
import { MDB_NO_LOGIN } from '@/config/errorCode';
import { Parser } from 'node-sql-parser';
export default {
    name: 'sqlResult',
    components: { aTable, aTips, sqlExportModal },
    emits: ['onChangePage', 'callSetLoginInfo'],
    props: {
        resultList: {
            type: Array,
            default: () => []
        },
        height: {
            type: Number,
            default: 100
        },
        clusterRoles: {
            type: Object,
            default: () => {}
        },
        getTableMetaData: {
            type: Function
        }
    },
    data() {
        return {
            tabName: '',
            sqlExportModalVisible: false
        };
    },
    computed: {
        tableData: function () {
            const tData = _.find(this.resultList, ['label', this.tabName])?.data || [];
            return tData;
        }
    },
    mounted() {
    },
    methods: {
        enableExportSql(data) {
            return data.isExport &&
                !!this.tableData.length &&
                data.type !== 'service' &&
                data.type !== 'cluster' &&
                !this.isSubquery(data.sql);
        },
        isSubquery(sql) {
            try {
                const ast = new Parser().astify(sql);
                if (Array.isArray(ast)) {
                    return ast.some(stmt => this.checkSubquery(stmt, true));
                }
                return this.checkSubquery(ast, true);
            } catch (e) {
                return false;
            }
        },
        checkSubquery(ast, isRoot = false) {
            if (!ast || typeof ast !== 'object') {
                return false;
            }

            if (this.isInnerSelect(ast, isRoot)) {
                return true;
            }

            if (this.hasSubqueryInFrom(ast)) {
                return true;
            }

            if (this.hasSubqueryInWith(ast)) {
                return true;
            }

            if (this.hasSubqueryInColumns(ast)){
                return true;
            }

            if (this.hasSubqueryInObject(ast)) {
                return true;
            }

            if (this.hasSubqueryInNext(ast)){
                return true;
            }

            return false;
        },
        // 只要不是最外层，遇到 select 就认为是子查询
        isInnerSelect(ast, isRoot) {
            return !isRoot && ast.type === 'select';
        },
        // FROM 子句
        hasSubqueryInFrom(ast) {
            if (!Array.isArray(ast.from)) {
                return false;
            }
            return ast.from.some(item =>
                item?.expr?.ast?.type === 'select' || this.checkSubquery(item, false)
            );
        },
        // WITH 子句（兼容 node-sql-parser 的数组结构）
        hasSubqueryInWith(ast) {
            if (!Array.isArray(ast.with)) {
                return false;
            }
            return ast.with.some(cte =>
                cte?.stmt?.ast && this.checkSubquery(cte.stmt.ast, false)
            );
        },
        // 字段表达式
        hasSubqueryInColumns(ast) {
            if (!Array.isArray(ast.columns)){
                return false;
            }
            return ast.columns.some(col =>
                col?.expr && this.checkSubquery(col.expr, false)
            );
        },
        // 递归所有对象属性（通用处理，覆盖 CASE、EXISTS、函数参数等所有场景）
        hasSubqueryInObject(ast) {
            for (const key in ast) {
                if (!Object.prototype.hasOwnProperty.call(ast, key)) {
                    continue;
                }

                const value = ast[key];
                if (Array.isArray(value)) {
                    if (value.some(v => this.checkSubquery(v, false))) {
                        return true;
                    }
                } else if (typeof value === 'object' && value !== null) {
                    if (this.checkSubquery(value, false)) {
                        return true;
                    }
                }
            }
            return false;
        },
        // 递归 UNION 的 _next 属性
        hasSubqueryInNext(ast) {
            return !!ast._next && this.checkSubquery(ast._next, false);
        },
        handlePageChange(index) {
            const curTable = this.resultList.find(item => item.label === this.tabName);
            const data = this.$refs[`table_${this.tabName}`].getPageData();
            this.$emit('handlePageChange', index, data.page, data.pageSize, (newPageNo, newPageSize) => {
                this.$nextTick(() => {
                    this.$refs[`table_${curTable.label}`].setPageSizeInfo(newPageNo, newPageSize);
                });
            });
        },
        resetPage() {
            this.$nextTick(() => {
                const curTable = this.resultList.find(item => item.label === this.tabName);
                this.$refs[`table_${curTable.label}`] && this.$refs[`table_${curTable.label}`].setPageSizeInfo(curTable.page, curTable.pageSize);
            });
        },
        // 导出表格数据
        async handleDataOperate(val, index) {
            if (val === 'exportCsv') {
                await this.exportCsv(index);
                return;
            }

            if (val === 'exportSql') {
                await this.openExportSqlModal();
                return;
            }
        },
        // 导出 csv
        async exportCsv(index) {
            try {
                const records = await this.fetchMemoryDataBySql(index);
                if (!records) {
                    return;
                }

                const data = this.resultList[index];
                exportCsv(data.label, data.columns, records);
            } catch (e) {
                this.$hMessage.error(e.message);
            }
        },
        // 打开导出 sql 模态框操作
        async openExportSqlModal() {
            this.sqlExportModalVisible = true;
        },
        // 关闭导出 sql 弹窗
        closeExportSqlModal() {
            this.sqlExportModalVisible = false;
        },
        // 请求 sql 数据
        async fetchMemoryDataBySql(index, sql) {
            const curTable = this.resultList[index];
            const tableRes = await getMemoryDataBySql(
                {
                    ...curTable.param,
                    page: 1,
                    sql: sql || curTable.sql,
                    pageSize: 10000
                },
                curTable.param.performSqlTimeout + 2000
            );

            if (tableRes.code === MDB_NO_LOGIN){
                // 重新登录
                this.$emit('callSetLoginInfo');
                return;
            }

            if (tableRes.code !== '200') {
                // 根据 code 是否是 200 判断，是否失败，需要提示吗？？
                throw new Error(tableRes.message);
            }

            return tableRes.data.tableRecords || [];
        },
        // 清空选中tab
        clearData() {
            this.tabName = '';
        },
        // 查看sql错误
        handleErrorInfo(arr) {
            this.$emit('errorInfo', arr);
        }
    },
    watch: {
        resultList(newVal) {
            if (newVal.length) {
                this.tabName = this.tabName ? this.tabName : newVal[0].label;
            }
        }
    },
    render() {
        const data = _.find(this.resultList, ['label', this.tabName]);
        const index = _.findIndex(this.resultList, ['label', this.tabName]);
        const isArbActive = (data !== undefined && data.id !== undefined)
            ? this.clusterRoles?.[data.id]?.clusterRole === 'ARB_ACTIVE'
            : false;
        return data ? (
            <div
                ref="sqlResult"
                class="sql-result">
                {/* tips */}
                <div class="title-result">
                    <h-select
                        v-model={this.tabName}
                        clearable={false}
                        style="width: 150px;"
                        v-on:on-change={this.resetPage}>
                        {
                            this.resultList.map((item, index) => {
                                return (
                                    <h-option
                                        key={item.key}
                                        value={item.label}>
                                        {item.label.split('-')?.[1]}
                                    </h-option>
                                );
                            })
                        }
                    </h-select>
                    <h-row style="color: #CACFD4; padding: 4px 8px 0; width: calc(100% - 100px); top: 4px;">
                        <h-col span="14">
                            <h-poptip
                                placement="top-start"
                                transfer
                                customTransferClassName="apm-poptip monitor-poptip">
                                <div class='option-text'>
                                    {
                                        data.type === 'service' ? <span style="padding: 8px; background: #1F3759; color: #fff; border-radius: 8px; margin-right: 6px;">服务</span> : ''
                                    }
                                    <span>{data.instanceName}</span>
                                    { isArbActive ? <span class="main-flag"></span> : ''}
                                    <div style="display: inline-block; color: #647495;">｜</div>
                                    {data.sql}
                                </div>
                                <div class="pop-content" slot="content" style='white-space: normal;'>{data.sql}</div>
                            </h-poptip>
                        </h-col>
                        <h-col span="10" style="text-align: right;">
                            <div class="export-tip-wrap">
                                <div>
                                    <h-dropdown v-on:on-click={(val) => { this.handleDataOperate(val, index); }}>
                                        <a href="javascript:void(0)">
                                            数据操作
                                            <h-icon name="unfold"></h-icon>
                                        </a>
                                        <h-dropdown-menu slot="list">
                                            <h-dropdown-item
                                                name="exportCsv"
                                                disabled={!data.isExport || !this.tableData.length}
                                            >
                                                <h-tooltip
                                                    placement="left"
                                                    content="最多导出1万条"
                                                >
                                                    <span>导出CSV</span>
                                                    <h-icon
                                                        class="sql-data-operate-tooltip-icon"
                                                        name="prompt"
                                                        size="20"
                                                        color="#9296A1"
                                                    />
                                                </h-tooltip>
                                            </h-dropdown-item>
                                            <h-dropdown-item
                                                name="exportSql"
                                                disabled={!this.enableExportSql(data)}
                                            >
                                                <h-tooltip placement="left">
                                                    <span>导出SQL</span>
                                                    <h-icon
                                                        class="sql-data-operate-tooltip-icon"
                                                        name="prompt"
                                                        size="20"
                                                        color="#9296A1"
                                                    />
                                                    <div slot="content">
                                                        1、服务、集群模式不支持导出SQL；<br />
                                                        2、子查询不支持导出SQL；<br />
                                                        3、最多导出1万条；
                                                    </div>
                                                </h-tooltip>
                                            </h-dropdown-item>
                                        </h-dropdown-menu>
                                    </h-dropdown>
                                    {
                                        this.sqlExportModalVisible && (
                                            <sql-export-modal
                                                visible={this.sqlExportModalVisible}
                                                onCloseModal={this.closeExportSqlModal}
                                                instanceName={data.instanceName}
                                                isArbActive={isArbActive}
                                                sqlQuery={data.sql}
                                                sqlFields={data.columns.map(({ title }) => title).filter(({ key }) => key !== 'apm_node')}
                                                getTableMetaData={this.getTableMetaData}
                                                fetchMemoryDataBySql={(sql) => this.fetchMemoryDataBySql(index, sql)}
                                            />
                                        )
                                    }
                                </div>
                            </div>
                            {/* <h-icon class="icon-down" name='t-b-download' v-on:on-click={() => { this.downTableData(index); }} /> */}
                        </h-col>
                    </h-row>
                </div>
                {
                   data?.instanceErrorMsg?.length
                       ? <div style="margin: 5px 5px 0;">
                           <a-tips
                               theme="dark"
                               type={data.isAllError ? 'error' : 'warning'}
                           >
                               <div slot='tipContent'>
                                   <span>{data.isAllError ? '全部节点执行失败' : '部分节点执行失败'}</span>
                                   <h-button
                                       type="text"
                                       size="small"
                                       style="color: #197ce8;"
                                       v-on:on-click={() => this.handleErrorInfo(data.instanceErrorMsg)}>查看详情</h-button>
                               </div>
                           </a-tips>
                       </div> : ''
                }
                <a-table
                    ref={`table_${data.label}`}
                    columns={data.columns}
                    border
                    total={data.totalCount ?? data.data.length}
                    tableData={this.tableData}
                    height={data?.instanceErrorMsg?.length ? this.height - 40 : this.height}
                    loading={data.loading}
                    canDrag
                    showTotal
                    showTitle
                    v-on:query={() => this.handlePageChange(index)}
                    hPageSize='small'
                    noDataText="当前查询无数据返回"
                >
                    <template slot='footerInfo'>
                        {
                            <div class='excute-time'>
                                {
                                    (data.time === '-' || !data.time)
                                        ? <div>执行时间：-ms&nbsp;</div>
                                        :                                                                                                                        <h-poptip trigger="hover" class="apm-poptip-time" transfer customTransferClassName="apm-poptip apm-poptip-time monitor-poptip">
                                            执行时间：{data.totalTime}ms&nbsp;
                                            <div class="pop-content" slot="content" style='white-space: normal;'>
                                                <div>
                                                    <span class="time-item-label">整体耗时：</span>
                                                    {data.totalTime}ms
                                                </div>
                                                <div>
                                                    <span class="time-item-label">APM耗时：</span>
                                                    {data.apmTime}ms
                                                </div>
                                                <div>
                                                    <span class="time-item-label">MDB耗时：</span>
                                                    {data.sqlRunTime}ms
                                                </div>
                                            </div>
                                        </h-poptip>
                                }
                            </div>
                        }
                    </template>
                </a-table>
            </div>
        ) : '';
    }
};
