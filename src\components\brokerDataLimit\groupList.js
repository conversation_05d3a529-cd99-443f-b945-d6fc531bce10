import './public.less';
import { mapState } from 'vuex';
import { getRuleGroupList, deleteRuleGroup } from '@/api/brokerApi';
import aTitle from '@/components/common/title/aTitle';
import aButton from '@/components/common/button/aButton';
import normalTable from '@/components/common/bestTable/normalTable/normalTable';
import addOrEditGroupModal from '@/components/brokerDataLimit/modal/addOrEditGroupModal.vue';
export default {
    name: 'brokerConfigList',
    props: {
        productId: {
            type: String,
            default: ''
        }
    },
    data() {
        return {
            loading: false,
            modalInfo: {
                status: false,
                title: ''
            },
            formItems: [
                {
                    type: 'input',
                    key: 'groupName',
                    label: '组名称',
                    value: '',
                    placeholder: '请输入组名称'
                },
                {
                    type: 'input',
                    key: 'functionNo',
                    label: '功能号',
                    value: '',
                    placeholder: '请输入功能号'
                },
                {
                    type: 'input',
                    key: 'accountId',
                    label: '资金账号',
                    value: '',
                    placeholder: '请输入资金账号'
                }
            ],
            columns: [
                {
                    title: '组名称',
                    key: 'groupName',
                    width: 150,
                    ellipsis: true
                },
                {
                    title: '组描述',
                    key: 'groupDescribe',
                    ellipsis: true
                },
                {
                    title: '资金账号',
                    ellipsis: true,
                    key: 'accountIds',
                    render: (h, params) => {
                        return h('span', {
                            attrs: {
                                title: params.row?.ruleMap?.accountIds
                            }
                        }, params.row?.ruleMap?.accountIds);
                    }
                },
                {
                    title: '功能号',
                    key: 'functionNos',
                    ellipsis: true,
                    render: (h, params) => {
                        return h('span', {
                            attrs: {
                                title: params.row?.ruleMap?.functionNos
                            }
                        }, params.row?.ruleMap?.functionNos);
                    }
                },
                {
                    title: '最近一次编辑时间',
                    key: 'gmtModified',
                    width: 160,
                    ellipsis: true
                },
                {
                    title: '操作',
                    key: 'action',
                    fixed: 'right',
                    width: 110,
                    render: (h, params) => {
                        return h('div', [
                            h('Button',
                                {
                                    props: {
                                        size: 'small',
                                        type: 'text'
                                    },
                                    on: {
                                        click: () => { this.handleOperationGroup('edit', params.row); }
                                    }
                                },
                                '编辑'
                            ),
                            h('Button',
                                {
                                    props: {
                                        size: 'small',
                                        type: 'text'
                                    },
                                    on: {
                                        click: () => {
                                            this.$hMsgBoxSafe.confirm({
                                                title: `删除`,
                                                content: `您确认要删除名为"${params.row.groupName}"的组吗？删除改组后，会移除关联规则中的所有名单配置！`,
                                                onOk: async () => {
                                                    this.handleDeleteGroup(params.row.id);
                                                }
                                            });
                                        }
                                    }
                                },
                                '删除'
                            )
                        ]);
                    }
                }
            ],
            tableData: [],
            total: 0,
            selection: [],
            templates: []
        };
    },
    mounted() {
    },
    computed: {
        ...mapState({
            transportTempList: state => {
                return state.rcm.transportTempList || [];
            }
        })
    },
    methods: {
        // 初始化数据
        async initData(){
            this.handleQuery();
        },
        // 查询
        async handleQuery(val){
            const param = {
                productId: this.productId,
                groupName: val?.groupName || '',
                accountId: val?.accountId || '',
                functionNo: val?.functionNo || ''
            };
            try {
                this.tableData = [];
                this.loading = true;
                const res = await getRuleGroupList(param);
                this.tableData = res?.data || [];
            } catch (err) {
                console.error(err);
            }
            this.loading = false;
        },
        // 删除行
        async handleDeleteGroup(id){
            try {
                const res = await deleteRuleGroup({ id });
                if (res.success) {
                    this.$hMessage.success('删除成功');
                    this.$refs['table'].$_handleQuery();
                } else if (res.code.length === 8) {
                    this.$hMessage.error('删除失败');
                }
            } catch (error) {
                console.error(error);
            }
        },
        handleOperationGroup(type, data) {
            this.modalInfo.title = type === 'create' ? '创建组' : '修改组';
            this.modalInfo.status = true;
            this.modalInfo.type = type;
            if (type === 'edit') this.modalInfo.data = data;
        }
    },
    render() {
        return <div class="topic-box">
            <normal-table
                ref="table"
                formTitle="筛选条件"
                tableTitle="组列表"
                formItems={this.formItems}
                columns={this.columns}
                loading={this.loading}
                tableData={this.tableData}
                hasPage={false}
                hasSetTableColumns={false}
                showTitle={true}
                v-on:query={this.handleQuery}>
                <div class='table-slot-box' slot='btns'>
                    <a-button type="primary" onClick={() => { this.handleOperationGroup('create'); }}>创建</a-button>
                </div>
            </normal-table>
            <style jsx>
                {
                    `    
                        .table-slot-box > .h-btn {
                            margin-right: 10px;
                        }
                    `
                }
            </style>
            {
                this.modalInfo.status ? <add-or-edit-group-modal modalInfo={this.modalInfo} v-on:query={() => { this.$refs['table'].$_handleQuery(); }} productId={this.productId} /> : ''
            }
        </div>;
    },
    components: { addOrEditGroupModal, normalTable, aTitle, aButton }
};

