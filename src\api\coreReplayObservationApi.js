import fetch from './fetch';
import { objectToQueryString } from '@/utils/utils';

const prefix = window['LOCAL_CONFIG']['API_HOME'] || '/';

// 创建
export function setReplayCreate(param) {
    return fetch().post(`${prefix}/replay/create`, param);
}

// 创建并启动
export function setReplayCreateAndStart(param) {
    return fetch().post(`${prefix}/replay/createAndStart`, param);
}

// 同步
export function setReplaySync(param) {
    return fetch().post(`${prefix}/replay/sync`, param);
}

// 启动
export function setReplayStart(param) {
    return fetch().post(`${prefix}/replay/start`, param);
}

// 终止
export function setReplayStop(param) {
    return fetch().post(`${prefix}/replay/stop`, param);
}

// 暂停
export function setReplayPause(param) {
    return fetch().post(`${prefix}/replay/pause`, param);
}

// 删除
export function setReplayDelete(param) {
    return fetch().post(`${prefix}/replay/delete`, param);
}

// 恢复
export function setReplayResume(param) {
    return fetch().post(`${prefix}/replay/resume`, param);
}

// 重演任务配置查询
export function getReplayConfig(param) {
    return fetch().get(`${prefix}/replay/config?${objectToQueryString(param)}`);
}

// 查询列表
export function getReplayList(param) {
    return fetch().get(`${prefix}/replay?${objectToQueryString(param)}`);
}

// 重演任务状态明细查询
export function getReplayStatusList(param) {
    return fetch().get(`${prefix}/replay/status?${objectToQueryString(param)}`);
}

// 获取核心可重演交易日列表
export function getReplayTradeDay(param) {
    return fetch().get(`${prefix}/replay/tradeDay?${objectToQueryString(param)}`);
}

// 查询重演任务详情
export function getReplayDetail(param) {
    return fetch().get(`${prefix}/replay/detail?${objectToQueryString(param)}`);
}

// 不一致详情下载
export function downloadInconsistencyfile(param){
    return fetch({ responseType: 'blob', includeResponseHeaders: true, timeout: 60000 }).get(`${prefix}/replay/response-inconsistency/${param.replayId}/content?clusterName=${param.clusterName}`);
}
