<template>
    <div>
        <!-- 下发配置列表 -->
        <h-msg-box-safe
            v-model="modalData.status"
            :escClose="true"
            :mask-closable="false"
            title="导入账号信息"
            width="600"
        >
        <div style=" display: flex; justify-content: center; align-items: center;">
            <h-upload ref="upload" multiple type="drag" :action="url" :on-success="submitConfig" :on-error="handleError">
                <div style="padding: 20px 10px;">
                    <h-icon name="upload" size="52" style="color: #39f;"></h-icon>
                    <p>点击或将文件拖拽到这里上传</p>
                </div>
            </h-upload>
            </div>
            <template v-slot:footer>
                <a-button @click="modalData.status = false">取消</a-button>
            </template>
        </h-msg-box-safe>
    </div>
</template>

<script>
import aButton from '@/components/common/button/aButton';
export default {
    props: {
        modalInfo: {
            type: Object,
            default: null
        }
    },
    data() {
        return {
            modalData: this.modalInfo,
            url: `${window['LOCAL_CONFIG']['API_HOME'] || '/'}/scene/test/case/config/account/csv`
        };
    },
    methods: {
        submitConfig(info) {
            if (info.success) {
                this.modalData.status = false;
                this.$emit('update', this.modalData.info, this.modalData.markId, info.data);
            } else {
                this.$hMessage.error(info.message);
                this.$refs.upload.clearFiles();
            }
        },
        // 上传失败
        handleError(error, response, file) {
            this.$hMessage.error('文件内容不合法');
            this.$refs.upload.clearFiles();
        }
    },
    components: {
        aButton
    }
};
</script>
