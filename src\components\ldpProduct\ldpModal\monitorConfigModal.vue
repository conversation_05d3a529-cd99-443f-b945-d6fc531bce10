<!--
 * @Description: 监控阀值测试
 * @Author: <PERSON><PERSON>
 * @Date: 2023-01-17 16:39:29
 * @LastEditTime: 2023-03-20 16:40:34
 * @LastEditors: <PERSON><PERSON>
-->
<template>
    <div>
        <h-msg-box-safe v-model="modalData.status" :escClose="true" :mask-closable="false" title="修改告警规则" width="50" @on-open="getCollections">
            <monitor-html
                ref="monitorHtml"
                :monitorDict="modalData.monitorDict"
                :monitorData="modalData.monitorData"
                :isEdit="true"
                :placeholder="modalData.data.placeholder"
                @ch-change="handleValueChange"
                @ch-blur="handleValueChange"></monitor-html>
            <template v-slot:footer>
                <a-button @click="modalData.status = false">取消</a-button>
                <a-button type="primary" :loading="loading" @click="submitConfig">确认</a-button>
            </template>
        </h-msg-box-safe>
    </div>
</template>
<script>
import monitorHtml from '@/components/ldpProduct/monitor/monitorHtml';
import aButton from '@/components/common/button/aButton';
import { updateMonitorConfig } from '@/api/httpApi';
export default ({
    name: 'MonitorConfigModal',
    props: {
        modalInfo: {
            type: Object,
            default: {}
        }
    },
    data() {
        return {
            modalData: this.modalInfo,
            loading: false
        };
    },
    methods: {
        getCollections() {
            this.$refs['monitorHtml'].init();
        },
        // 自定义下拉，输入事件回调
        handleValueChange(val, name) {
            this.modalData.data.placeholder[name] = val;
        },
        async submitConfig() {
            // 判断用户是否未输入
            for (const item of Object.keys(this.modalData.data.placeholder)) {
                if (!this.modalData.data.placeholder[item]) {
                    return;
                }
            }
            const param = {
                ...this.modalData.data
            };
            param.desc = param.metricsDesc;
            this.loading = true;
            const res = await updateMonitorConfig(param);
            if (res.success) {
                this.$emit('handleEditChange');
                this.$hMessage.success('规则修改成功！');
            }
            this.laoding = false;
            this.modalData.status = false;
        }
    },
    components: { monitorHtml, aButton }
});
</script>

<style lang="less" scoped>
.row-header {
    font-weight: bold;
}

hr {
    border: 1px solid #efefef;
    margin: 10px 0;
}

.row-list {
    margin-bottom: 10px;

    /deep/ & > .h-col {
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        height: 32px;
        line-height: 30px;
    }
}
</style>
