import './infoBar.less';
import obsTitle from '@/components/common/title/obsTitle';
import infoBar from '@/components/common/infoBar/infoBar';
import apmScroll from '@/components/common/bestScroll/apmScroll';
export default {
    name: 'info-scroll-bar',
    components: { obsTitle, infoBar, apmScroll },
    props: {
        data: {
            title: {
                type: Object,
                default: () => {}
            },
            direction: {
                type: String,
                default: 'row'  // column
            },
            configData: {
                type: Object,
                default: () => {}
            },
            details: {
                type: Array,
                default: () => []
            }
        },
        selectInfoId: {
            type: String,
            default: ''
        },
        selectedStyleType: {
            type: String,
            default: 'background' // border  background
        },
        // 表格加载
        loading: {
            type: Boolean,
            default: false
        },
        infoSize: {
            type: String,
            default: '' // 传空正常大小42px, min: 高度减小36px
        }
    },
    data() {
        return {
            selectedStyle: {
                border: { border: '2px solid var(--link-color)' },
                background: { backgroundColor: 'var(--poptip-bg-color)' }
            }
        };
    },
    methods: {
        handleEvent(name, ...args) {
            this.$emit(name, ...args);
        },
        setSelectVal(key, val) {
            this.$refs['obs-title'].setSelectVal(key, val);
        },
        getSelectVal(key) {
            return this.$refs['obs-title'].getSelectVal(key);
        },
        setCheckVals(key, val) {
            this.$refs['obs-title'].setCheckVals(key, val);
        },
        getCheckVals(key) {
            return this.$refs['obs-title'].getCheckVals(key);
        }
    },
    render() {
        return (
            <div class={[
                this.data?.direction === 'row' ? 'info-scroll-bar-row' : 'info-scroll-bar',
                this.infoSize ? 'info-scroll-bar-min' : ''
            ]}>
                {this.data?.title && (
                    <obs-title
                        ref="obs-title"
                        title={this.data?.title}
                        v-on:button-click={(...args) => this.handleEvent('button-click', ...args)}
                        v-on:select-change={(...args) => this.handleEvent('select-change', ...args)}
                    >
                        <template slot="extraTitleBox">
                            {this.$slots.extraTitleBox}
                        </template>
                    </obs-title>
                )}
                <apm-scroll ref="apmScroll" configData={this.data?.configData}>
                    <div class="scroll-content">
                        {(this.data?.details || []).map(v => (
                            <info-bar
                                type={v.type}
                                title={v.title}
                                titleAlias={v.titleAlias}
                                hasBackgroundColor={v.hasBackgroundColor}
                                iconName={v.iconName}
                                iconColor={v.iconColor}
                                infoDic={v.infoDic}
                                infoId={v.infoId}
                                info={v.info}
                                loading={this.loading}
                                canClick={v.canClick}
                                autoGrid={v.autoGrid}
                                gridMinWidth={v.gridMinWidth}
                                gridSpan={v.gridSpan}
                                poptipInfo={v.poptipInfo}
                                customClassName={v.customClassName}
                                infoSize={v.infoSize}
                                status={v.status}
                                selectInfoId={this.selectInfoId}
                                v-on:on-current-change={(...args) => this.handleEvent('on-current-change', ...args)}
                                v-on:info-bar-click={(...args) => this.handleEvent('info-bar-click', ...args)}
                                style={[
                                    { width: v.infoWidth || 240 + 'px' },
                                    v.canClick && v.infoId === this.selectInfoId ? this.selectedStyle[this.selectedStyleType] :  ''
                                ]}
                            >
                            </info-bar>
                        ))}
                    </div>
                </apm-scroll>
            </div>
        );
    }
};
