<!-- 创建产品节点弹窗 -->
<template>
    <div class="check-link">
        <h-msg-box-safe v-model="modalData.status" class-name="add-modal" top="10" title="对接产品配置中心" :mask-closable="false" width="950"
            :height="height"
        >
            <h-alert type="warning" show-icon>检测到下列配置路径已被产品节点关联，请确认是否继续对接产品配置中心</h-alert>
            <h-collapse>
            <h-panel v-for="item in productInfoList" :key="item.path" class="check-link-list">
                <div slot class="check-link-list-item">
                    <div><span>配置路径：</span>{{ item.path }}</div>
                    <div class="check-link-list-item-zk">
                        <span>zookeeper：</span>
                        <div class="check-link-list-item-zk-addr">
                        <h-poptip placement="left-start" transfer>
                                 <div  class="check-link-list-item-zk-addr-content" >
                                    {{item.zkAddr}}
                                 </div>
                            <div slot="content">{{ item.zkAddr }}</div>
                        </h-poptip>
                    </div>
                    </div>
                </div>
                <div v-for="product in item.products" slot="content" :key="product.id" class="check-link-list-products">
                    <div class="check-link-list-products-name">
                        {{ product.productName }}
                    </div>
                    <div class="check-link-list-products-id">
                        {{ product.id }}
                    </div>
                    <div class="check-link-list-products-view">
                        <a @click="() => turnToDetail(product.id)">查看</a>
                    </div>
                </div>
            </h-panel>
        </h-collapse>

            <template v-slot:footer>
                <a-button @click="modalData.status = false">取消</a-button>
                <a-button type="primary" :loading="loading" @click="onCreate">创建</a-button>
            </template>
        </h-msg-box-safe>
    </div>
</template>

<script>
import aButton from '@/components/common/button/aButton';
export default {
    props: {
        modalInfo: {
            type: Object,
            default: null
        },
        height: {
            type: Number
        }
    },
    data() {
        return {
            modalData: this.modalInfo,
            loading: false
        };
    },
    computed: {
        productInfoList() {
            const { configInfo, data } = this.modalData;
            const { paths, zkAddr } = configInfo || {};
            if (!paths?.length) return [];
            const verifyList = [];
            paths.forEach(item => {
                const matchPathProducts = data.filter(itemProduct => itemProduct.configInfo?.paths?.includes(item));
                if (matchPathProducts?.length) {
                    verifyList.push({
                        path: item,
                        zkAddr,
                        products: matchPathProducts
                    });
                }
            });
            return verifyList;
        }
    },
    methods: {
        turnToDetail(productInstNo) {
            this.$emit('selectMenuChange', productInstNo);
            this.$emit('close');
        },
        async onCreate() {
            this.loading = true;
            await this.modalData.createCallback().finally(() => {
                this.loading = false;
            });
        }
    },
    components: {  aButton }
};
</script>

<style lang="less" scoped>
/deep/ .h-collapse-header {
    height: 42px;
}

/deep/.h-collapse-content-box {
    padding: 0;
}

.check-link {
    &-list {
        line-height: 42px;

        &-line {
            margin-top: 6px;
            margin-bottom: 10px;
            height: 1px;
            background-color: #d5d5d5;
        }

        &-item {
            display: flex;

            div {
                span {
                    font-weight: bold;
                }
            }

            &-zk {
                max-width: 320px;
                display: flex;
                margin-left: 20px;

                &-addr {
                    overflow: hidden;
                    white-space: nowrap;
                    text-overflow: ellipsis;
                    flex: 1;

                    &-content {
                        white-space: nowrap;
                        text-overflow: ellipsis;
                        overflow: hidden;
                    }
                }
            }
        }

        &-products {
            display: flex;

            div {
                width: 300px;
                text-align: center;
                white-space: nowrap;
                overflow: hidden;
                text-overflow: ellipsis;
            }

            &-id {
                width: 200px;
                text-align: left !important;
            }

            &-name {
                max-width: 300px;
                text-align: left !important;
            }

            &-view {
                cursor: pointer;
            }
        }
    }
}

/deep/ .add-modal {
    .h-modal-body {
        padding-top: 16px;
    }
}
</style>
