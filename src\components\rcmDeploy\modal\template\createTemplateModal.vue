<template>
    <div class="form">
        <h-msg-box v-model="modalData.status" :escClose="true" :mask-closable="false"
            :title="title" width="60" max-height="500"
            class="wrap-msgbox" @on-open="getCollections" @on-close="cancelMethod">
            <h-form ref="formValidate" :model="formValidate" :label-width="160" cols="2">
                <h-form-item label="模板名称:" prop="name" required :validRules="nameRule">
                    <h-input v-model="formValidate.name" ></h-input>
                </h-form-item>
                <h-form-item label="继承模板:" prop="ref">
                    <h-select v-model="formValidate.ref" placeholder="请选择" :positionFixed="true" @on-change="handleChange">
                        <h-option v-for="item in modalData.list" :key="item.name" :value="item.name">{{ item.name }}</h-option>
                    </h-select>
                </h-form-item>
            </h-form>
            <theme-setting v-if="modalInfo.type === 'topic'" ref="topic" :rcmId="rcmId"  :saveValidateData="topicData"/>
            <cluster-setting v-if="modalInfo.type === 'cluster'" ref="cluster" :rcmId="rcmId" :saveValidateData="clusterData" />
            <normal-setting v-if="modalInfo.type === 'normal'" ref="normal" :rcmId="rcmId"  :saveValidateData="normalData" :hasAppName="false" />
            <template v-slot:footer>
                <h-button @click="cancelMethod">取消</h-button>
                <h-button type="primary" :loading="loading" @click="saveSubmit">确定</h-button>
            </template>
        </h-msg-box>
    </div>
</template>

<script>
import { createTopicTemp, createContextTemp, getRcmTempDetail } from '@/api/rcmApi';
import { normalTempDefault, topicTempDefault, clusterTempDefault } from '@/config/rcmDefaultConfig';
import clusterSetting from '@/components/rcmDeploy/modal/clusterSetting';
import normalSetting from '@/components/rcmDeploy/modal/normalSetting';
import themeSetting from '@/components/rcmDeploy/modal/themeSetting';
import { stringLengthRule } from '@/utils/validate';
export default {
    props: {
        modalInfo: {
            type: Object,
            default: null
        },
        rcmId: {
            type: String,
            default: ''
        }
    },
    name: 'CreateTemplateModal',
    components: { clusterSetting, normalSetting, themeSetting },
    data() {
        return {
            loading: false,
            modalData: this.modalInfo,
            formValidate: {
                name: '',
                ref: ''
            },
            nameRule: [{ test: stringLengthRule(50), trigger: 'change, blur' }],
            title: '', // 弹窗名
            topicData: {
                ...topicTempDefault
            },
            normalData: {
                ...normalTempDefault
            },
            clusterData: {
                ...clusterTempDefault
            }
        };
    },
    methods: {
        // 初始化
        getCollections() {
            this.title = this.modalInfo.type === 'topic' ? '主题配置模板' : this.modalInfo.type === 'cluster'  ? '集群配置模板' : '通用配置模板';
        },
        // 取消
        cancelMethod(){
            this.modalData.status = false;
        },
        // 切换继承模板下拉
        async handleChange(e) {
            if (!e) return;
            const desc = {
                topic: {
                    name: 'transport',
                    data: topicTempDefault
                },
                normal: {
                    name: 'singletonContext',
                    data: normalTempDefault
                },
                cluster: {
                    name: 'clusterContext',
                    data: clusterTempDefault
                }
            };
            const keyData = desc[this.modalInfo.type];
            const { data } = await getRcmTempDetail({
                id: this.rcmId,
                templateType: keyData.name,
                name: e
            });
            const result = data.template[keyData.name] || keyData.data;
            Object.keys(this[this.modalInfo.type + 'Data']).forEach(item => {
                this[this.modalInfo.type + 'Data'][item] = result[item];
            });
            this.$refs[this.modalInfo.type].init();
        },
        // 创建主题模板
        async createTopicTemp(param) {
            this.loading = true;
            const res = await createTopicTemp(param);
            this.loading = false;
            if (res.code === '200') {
                this.modalData.status = false;
                this.$emit('refresh');
                this.$hMessage.success('主题模板创建成功');
            } else {
                this.$hMessage.error('主题模板创建失败');
            }
        },
        // 创建上下文模板
        async createContextTemp(param) {
            this.loading = true;
            const res = await createContextTemp(param);
            this.loading = false;
            if (res.code === '200') {
                this.modalData.status = false;
                this.$emit('refresh');
                this.$hMessage.success('上下文模板创建成功');
            }
        },
        // 提交
        saveSubmit() {
            this.$refs['formValidate'].validate((valid) => {
                const result = this.$refs[this.modalInfo.type]?.getFileData();
                if (valid && result) {
                    if (this.modalInfo.type === 'topic') {
                        this.createTopicTemp({ ...this.formValidate, ...result, rcmId: this.rcmId });
                    } else {
                        this.createContextTemp({ ...this.formValidate, ...result, rcmId: this.rcmId, mode: this.modalInfo.type === 'cluster' ? 'cluster' : 'singleton' });
                    }
                }
            });
        }
    }
};
</script>
