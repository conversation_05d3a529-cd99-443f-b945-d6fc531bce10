/deep/ .h-editgird {
    background: none;
    border-color: var(--table-border-color);
    margin: 10px 0;

    &::before {
        display: none;
    }

    &::after {
        display: none;
    }
}

/deep/ .h-editgird table {
    width: 100% !important;
}

/deep/ .h-editgird-wrapper {
    width: 100%;
    height: auto;
    max-height: calc(100% - 50px);
    border: none;
}

/deep/ .h-editgird th {
    background: var(--primary-color);
    border-bottom: 1px solid #31364a;
    border-right: 1px solid transparent;
    color: var(--font-color);
    font-size: var(--font-size);
    font-weight: var(--font-weight);
}

/deep/ .h-editgird tr {
    cursor: pointer;
}

/deep/ .h-editgird td {
    border-bottom: 1px solid #31364a;
    border-right: 1px solid transparent;
    background: var(--input-bg-color);
    color: var(--font-color);
}

/deep/ .h-editgird-row-checked,
/deep/ .h-editgird-row-checked:hover,
/deep/ .h-editgird-row-highlight,
/deep/ .h-editgird-row-highlight:hover,
/deep/ .h-editgird-row-hover,
/deep/ .h-editgird-row-hover td {
    background: var(--primary-color) !important;
}

/deep/ .h-editgird-row-hover td button {
    color: var(--table-button-hover-bgcolor) !important;
}

/deep/ .h-editgird-row-hover .h-btn-text {
    color: var(--font-color);
}

/deep/ td .h-btn-text:hover {
    color: var(--link-color);
    text-decoration: underline;
}

/deep/ .h-editgird-tiptext {
    width: 100% !important;
}

/deep/ .h-editgird-wrapper > .h-spin-fix {
    position: absolute;
    top: 0;
    left: 0;
    z-index: 8;
    width: 100%;
    height: 100%;
    border: var(--table-border);
    background-color: var(--input-bg-color);
}

/deep/ .h-editgird-fixed-body-shadow {
    border: none;
}

/deep/ .h-editgird-fixed-right-patch {
    background-color: var(--input-bg-color);
    border-bottom: var(--table-border);
}
