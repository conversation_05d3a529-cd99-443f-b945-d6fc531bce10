<template>
    <div class="main">
      <a-title title="应用检查配置">
        <slot>
          <h-select
              v-show="productList.length > 1"
              v-model="productInstNo"
              class="title-single-select"
              placeholder="请选择"
              :positionFixed="true"
              :clearable="false"
              @on-change="checkProduct"
          >
            <h-option
              v-for="item in productList"
              :key="item.id"
              :value="item.productInstNo"
              >{{ item.productName }}</h-option
            >
          </h-select>
        </slot>
      </a-title>
      <a-loading v-if="loading" style="z-index: 9;"></a-loading>
      <h-tabs v-if="editableTabs.length" v-model="tabName" @on-click="tabClick(tabName)">
          <h-tab-pane v-for="item in editableTabs" :key="item.name" :label="item.describe" :name="item.name">
              <component :is="item.name" :ref="item.name" :productInstNo="productInstNo"></component>
          </h-tab-pane>
      </h-tabs>
      <div v-else  style="height: calc(100% - 50px);">
          <no-data />
      </div>
    </div>
  </template>
<script>
import _ from 'lodash';
import { mapState, mapActions } from 'vuex';
import noData from '@/components/common/noData/noData';
import aTitle from '@/components/common/title/aTitle';
import aLoading from '@/components/common/loading/aLoading';
import normalTable from '@/components/common/bestTable/normalTable/normalTable';
import { getDashboardTag, getDashboardConfigV2 } from '@/api/httpApi';
import AppBindingCore from '@/components/appBindingCore/AppBindingCore';
export default {
    components: { noData, aTitle, normalTable, aLoading, AppBindingCore },
    data() {
        return {
            productInstNo: '',
            instanceList: [],
            loading: false,
            editableTabs: [],
            tabName: ''
        };
    },
    mounted() {
        this.init();
    },
    beforeDestroy() {},
    computed: {
        ...mapState({
            productList: (state) => {
                return state.product.productListLight;
            }
        })
    },
    methods: {
        ...mapActions({ getProductList: 'product/getProductListLight' }),
        // 初始化
        async init() {
            try {
                this.loading = true;
                await this.getProductList({ filter: 'supportAppConfigCheck' });
                const productInstNo = localStorage.getItem('productInstNo');
                this.productInstNo =
            _.find(this.productList, ['productInstNo', productInstNo])
                ?.productInstNo || this.productList?.[0]?.productInstNo;
            } catch (e) {
                console.error(e);
            } finally {
                this.loading = false;
            }
        },
        // 切换产品
        async checkProduct(productId) {
            if (!this.productList.length) return;
            this.loading = true;
            this.productInfo = productId
                ? _.find(this.productList, ['productInstNo', productId])
                : this.productList[0];
            this.productInstNo = this.productInfo.productInstNo;
            localStorage.setItem('productInstNo', this.productInfo.productInstNo);
            await this.queryCategory(this.productInstNo);
            if (this.editableTabs?.length){
                await this.tabClick(this.editableTabs?.[0]?.name);
            }
            setTimeout(() => {
                this.loading = false;
            }, 500);
        },
        /**
           * 查询分类信息
           */
        async queryCategory(productId) {
            try {
                let tags = [];
                const tagsRes = await getDashboardTag({
                    scene: 'appConfigCheck',
                    objectType: 'product',
                    objectId: productId
                });
                if (tagsRes.code === '200') {
                    tags = tagsRes?.data || [];
                }
                const res = await getDashboardConfigV2({
                    scene: 'appConfigCheck',
                    tags: tags,
                    objectType: 'product',
                    objectId: productId
                });
                if (res.code === '200') {
                    this.editableTabs = res?.data?.filter(v => v?.visible === true) || [];
                }
            } catch (error) {
                this.editableTabs = [];
                this.loading = false;
            }
        },
        // 切换tab
        tabClick(name) {
            this.tabName = name;
            this.$nextTick(async () => {
                  this.$refs?.[name]?.[0] && await this.$refs[name][0].initData();
            });
        }
    }
};
</script>
  <style lang="less" scoped>
    @import url("@/assets/css/tab.less");
  </style>
