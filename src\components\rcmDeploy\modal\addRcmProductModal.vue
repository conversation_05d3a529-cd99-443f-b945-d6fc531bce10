<template>
    <div>
        <!-- 下发配置列表 -->
        <h-msg-box-safe v-model="modalData.status" :mask-closable="false" title="创建产品节点" width="700" maxHeight="350">
            <h-form ref="formValidate" :model="formValidate" :label-width="100">
                <h-form-item label="产品类型：" prop="type">
                    <span>可靠一致性主播</span>
                </h-form-item>
                <h-form-item label="产品名称：" prop="name" required :validRules="stringRule">
                    <h-input v-model.trim="formValidate.name" placeholder="请输入产品名称"></h-input>
                </h-form-item>
                <h-form-item label="产品版本：" prop="version" :validRules="stringRule">
                    <h-input v-model.trim="formValidate.version" placeholder="请输入产品版本"></h-input>
                </h-form-item>
                <h-form-item label="配置来源：" prop="configSourceType" required>
                    <h-select v-model="formValidate.configSourceType" setDefSelect :clearable="false" @on-change="changeSourceType">
                        <h-option value="zookeeper">zookeeper</h-option>
                        <h-option value="fileImport">配置文件导入</h-option>
                    </h-select>
                </h-form-item>
                <h-form-item v-if="formValidate.configSourceType === 'fileImport'" label="上传配置：" prop="configFile" required>
                    <h-upload type="drag" action="" :before-upload="handleUpload" style="width: 100%;">
                        <div style="padding: 10px 20px 0; width: 530px;">
                            <h-icon name="upload" size="40" style="color: var(--link-color);"></h-icon>
                            <p>点击或将文件拖拽到这里上传</p>
                        </div>
                        <div v-if="file !== null">
                            上传文件：{{ file.name }}
                            <h-button v-if="loadingStatus === 0" type="text" :loading="!loadingStatus">上传中</h-button>
                            <span v-if="loadingStatus === 2" style="color: var(--success-color); margin: 0 10px;">上传成功</span>
                            <span v-if="loadingStatus === 3" style="color: var(--error-color); margin: 0 10px;">上传失败</span>
                        </div>
                    </h-upload>
                </h-form-item>
                <h-form-item v-if="formValidate.configSourceType === 'fileImport'" label="发布目标：" prop="publishMethod" required>
                    <h-select v-model="formValidate.publishMethod" setDefSelect :clearable="false">
                        <h-option value="zookeeper">zookeeper</h-option>
                    </h-select>
                </h-form-item>
                <h-form-item label="ZK集群地址: " prop="publishConfigInfo.zkAddr" required :validRules="zkAddressRule">
                    <h-input v-model="formValidate.publishConfigInfo.zkAddr" placeholder="请输入ZK集群地址" @on-blur="blurZkInput"></h-input>
                </h-form-item>
                <p class="form-tip">使用IP:PORT形式，多个地址使用英文逗号分隔</p>
                <h-form-item v-if="formValidate.configSourceType === 'zookeeper'" label="配置路径: " prop="publishConfigInfo.path" required>
                    <h-select v-model="formValidate.publishConfigInfo.path" multiple :clearable="false" :loading="loading2" placement="top">
                        <h-option
                            v-for="item in pathList"
                            :key="item.value"
                            :value="item.value"
                            >{{ item.label }}</h-option
                        >
                    </h-select>
                </h-form-item>
            </h-form>

            <template v-slot:footer>
                <a-button @click="modalData.status = false">取消</a-button>
                <a-button type="primary" :loading="loading" @click="submitConfig">创建</a-button>
            </template>
        </h-msg-box-safe>
    </div>
</template>

<script>
import { isJSON } from '@/utils/utils';
import { stringLengthRule, multipleAddressRule } from '@/utils/validate';
import { addRcmProductInstance, getZkRcmPath } from '@/api/rcmApi';
import aButton from '@/components/common/button/aButton';
export default {
    props: {
        modalInfo: {
            type: Object,
            default: null
        }
    },
    data() {
        return {
            modalData: this.modalInfo,
            loading: false,
            stringRule: [{ test: stringLengthRule(50), trigger: 'change, blur' }],
            zkAddressRule: [{ test: multipleAddressRule, trigger: 'change, blur' }],
            loadingStatus: 1,
            file: null,
            formValidate: {
                type: '',
                name: '',
                version: '',
                configSourceType: '',
                configFile: '',
                publishMethod: '',
                publishConfigInfo: {
                    zkAddr: '',
                    path: []
                }
            },
            pathList: [],
            loading2: false
        };
    },
    methods: {
        async submitConfig() {
            const that = this;
            this.$refs['formValidate'].validate(async (valid) => {
                if (valid) {
                    that.loading = true;
                    try {
                        const param = {
                            name: this.formValidate.name,
                            version: this.formValidate.version,
                            configSourceType: this.formValidate.configSourceType,
                            publishConfigInfo: this.formValidate.publishConfigInfo
                        };
                        if (this.formValidate.configSourceType === 'zookeeper') {
                            param.publishConfigInfo.path = this.formValidate.publishConfigInfo.path.join(',');
                        } else {
                            param.configFile = this.formValidate.configFile;
                            param.publishMethod = this.formValidate.publishMethod;
                            param.publishConfigInfo.path = '';
                        }
                        const res = await addRcmProductInstance(param);
                        if (res.code === '200') {
                            that.$emit('reload'); // 初始化产品节点
                            that.$hMessage.success('添加成功!');
                            that.modalInfo.status = false;
                        } else {
                            that.loading = false;
                        }
                    } catch (err) {
                        that.loading = false;
                    }
                }
            });
        },
        // 切换配置来源
        changeSourceType(type) {
            this.$refs.formValidate.resetValidate();
        },
        // 文件上传处理
        handleUpload(file) {
            this.file = file;
            this.loadingStatus = 0;
            this.getActionData(file);
            return false;
        },
        // 解析获取文件内容
        getActionData (file) {
            const that = this;
            const reader = new FileReader();// 新建一个FileReader
            reader.readAsText(file, 'UTF-8');// 读取文件
            reader.onload = function (evt) { // 读取完文件之后会回来这里
                const fileString = evt.target.result; // 读取文件内容
                if (isJSON(fileString)) {
                    that.formValidate.configFile = fileString;
                    that.loadingStatus = 2;
                } else {
                    that.loadingStatus = 3;
                    that.$hMessage.error('文件格式校验不通过,请校验文件格式并重新拖动或点击文件上传');
                }
            };
        },
        // 修改zk地址后刷新路径列表
        blurZkInput() {
            if (this.formValidate.configSourceType === 'fileImport') return;
            this.$refs['formValidate'].validateField('publishConfigInfo.zkAddr', (err) => {
                !err && this.getZkRcmPath();
            });
        },
        // 获取zookeeper LDP产品配置路径
        getZkRcmPath() {
            this.loading2 = true;
            this.pathList = [];
            getZkRcmPath({
                zkAddr: this.formValidate.publishConfigInfo.zkAddr
            }).then(res => {
                if (res.success) {
                    Array.isArray(res.data) && res.data.forEach(item => {
                        this.pathList.push({
                            value: item,
                            label: item
                        });
                    });
                    this.loading2 = false;
                } else {
                    this.loading2 = false;
                }
            }).catch(err => {
                this.loading2 = false;
            });
        }
    },
    components: {  aButton }
};
</script>

<style lang="less" scoped>
    .form-tip {
        color: var(--font-opacity-color);
        margin: -20px 0 20px 100px;
    }

</style>
