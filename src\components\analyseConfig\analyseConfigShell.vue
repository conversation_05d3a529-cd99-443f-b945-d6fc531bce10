<template>
    <div class="config-shell">
        <h-form v-show="sceneId" ref="formValidate" :model="formItems" label-position="left" :label-width="90">
        <div class="wrapper-config">
            <div class="box-config">
                <div class="box-config-left">
                    <div>
                        <p class="number">1</p>
                    </div>
                    <p class="box-header">选择发单工具</p>
                </div>
                <div class="box-config-right">
                    <h-form-item label="发单工具" prop="deployInfo.name">
                        <h-input value="ApiTest" placeholder="请输入发单工具" disabled></h-input>
                    </h-form-item>
                    <h-form-item label="部署到" prop="deployInfo.targetIp" required :validRules="ipRule">
                        <h-input v-model="formItems.deployInfo.targetIp" placeholder="请输入部署地址" disabled></h-input>
                    </h-form-item>
                    <h-form-item label="部署实例数" prop="deployInfo.deployInstanceNum" required :validRules="rangeRule">
                        <h-input v-model="formItems.deployInfo.deployInstanceNum" placeholder="请输入部署示例数"
                            @on-blur="handleUpdateInfo('deployInfo', 'deployInstanceNum')"></h-input>
                    </h-form-item>
                </div>
            </div>
            <div class="middle-arrow"></div>
            <div class="box-config">
                <div class="box-config-left">
                    <div>
                        <p class="number">2</p>
                    </div>
                    <p class="box-header">连接目标LDP业务系统</p>
                </div>
                <div class="box-config-right">
                    <h-form-item class="form-pro" label="产品节点" prop="bizSystemInfo.productInstNo" :label-width="85" required>
                        <h-select v-model="formItems.bizSystemInfo.productInstNo" placeholder="请选择" :positionFixed="true" :clearable="false"
                            @on-change="handleChangeProduct">
                            <h-option v-for="item in productList" :key="item.id" :value="item.productInstNo" :disabled="!item.hasSupportOrderTest">{{item.productName}}</h-option>
                        </h-select>
                    </h-form-item>
                    <h-form-item class="form-pro" label="应用节点" prop="bizSystemInfo.bizType" :label-width="85" required>
                        <h-select v-model="formItems.bizSystemInfo.bizType" placeholder="请选择" :positionFixed="true" :clearable="false"
                            @on-change="handleChangeBizType">
                            <h-option v-for="item in bizModelList" :key="item.instanceName" :value="item.instanceType">{{ `${item.instanceName}（${getInstanceTypeDesc(item.instanceType)}）` }}</h-option>
                        </h-select>
                    </h-form-item>
                    <h-form-item class="form-pro" label="接入IP" prop="bizSystemInfo.ip" :label-width="85">
                        {{formItems.bizSystemInfo.ip}}
                    </h-form-item>
                    <h-form-item class="form-pro" label="接入端口" prop="bizSystemInfo.port" :label-width="85">
                        {{formItems.bizSystemInfo.port}}
                    </h-form-item>
                </div>
            </div>
            <div class="middle-arrow"></div>
            <div class="box-config">
                <div class="box-config-left">
                    <div>
                        <p class="number">3</p>
                    </div>
                    <p class="box-header">选择测试用例</p>
                </div>
                <div class="box-config-right">
                    <h-form-item label="测试用例" prop="testCaseId" required>
                        <h-select v-model="formItems.testCaseId" @on-change="handleChangeTestCaseId">
                            <h-option
                                v-for="item in caseList"
                                :key="item.id"
                                :value="item.id"
                                >{{ item.name }}</h-option>
                        </h-select>
                    </h-form-item>
                    <div class="form-item flex-center">
                        <a-button type="info" class="btn-def" @click="addCase">新建用例</a-button>
                        <a-button v-if="formItems.testCaseId" type='dark' class="btn-del"
                            @click="handleDelCase(formItems.testCaseId)">删除用例</a-button>
                    </div>
                    <div v-if="formItems.testCaseId" class="form-item flex-center">
                        <a-button type='dark' class="btn-def" @click="handleConfigCase">配置用例</a-button>
                        <a-button type='dark' class="btn-def" @click="handleAccounts">配置多账号</a-button>
                    </div>
                </div>
            </div>
        </div>
            <!-- 执行测试用例 -->
            <div class="wrapper-shell">
            <div class="wrapper-shell-left">④&nbsp;&nbsp;&nbsp;执行测试</div>
            <div class="form-item">
                <h-form-item label="命令行参数" prop="commandLine" required>
                    <h-input
                        v-model="formItems.commandLine"
                        placeholder="请输入执行命令"
                    ></h-input>
                </h-form-item>
                <h-checkbox v-model="formItems.hasPrintDelayLog">打印时延日志</h-checkbox>
                <h-button type="info" :loading="loading" @click="doPerformCase">执行测试</h-button>
            </div>
        </div>
    </h-form>
      <!-- 新建测试用例 -->
      <add-case-modal v-if="addCaseInfo.status" :modalInfo="addCaseInfo" @query="querySceneInfo" />
        <!-- 配置用例 -->
        <case-config-modal v-if="caseConfigInfo.status" :modalInfo="caseConfigInfo" @update="updateConfigText" />
        <!-- 配置账号 -->
        <case-account-modal v-if="caseAccountInfo.status" :modalInfo="caseAccountInfo" @update="updateConfigText" />
    </div>

</template>

<script>
import _ from 'lodash';
import aButton from '@/components/common/button/aButton';
import addCaseModal from '@/components/analyse/addCaseModal.vue';
import caseConfigModal from '@/components/analyse/caseConfigModal.vue';
import caseAccountModal from '@/components/analyse/caseAccountModal.vue';
import { delCase, updateSceneInfo, performCase, querySceneInfo } from '@/api/httpApi';
function rangeFunc(rule, val, callback) {
    if (Number(val) <= 10) {
        callback();
    } else {
        callback(new Error('请输入1-10正整数'));
    }
}
function rangePortFunc(rule, val, callback) {
    const port = Number(val);
    if (isNaN(port) || port < 1 || port > 65535) {
        callback(new Error('请输入1到65535之间的端口号'));
    } else {
        callback();
    }
}
export default {
    name: 'AnalyseConfigShell',
    props: {
        sceneId: {
            type: String,
            default: ''
        },
        productList: {
            type: Array,
            default: []
        },
        caseList: {
            type: Array,
            default: []
        }
    },
    data() {
        return {
            formItems: {
                testCaseId: '',             // 当前选中用例
                deployInfo: {
                    targetIp: '127.0.0.1',  // 目标ip
                    deployInstanceNum: 1    // 部署实例个数
                },
                bizSystemInfo: {
                    productInstNo: '',
                    bizType: '',            // 业务类型
                    ip: '',                 // 业务ip
                    port: 33337             // 业务端口
                },
                hasPrintDelayLog: true,     // 是否打印日志
                commandLine: ''             // 命令行
            },
            loading: false,
            ipRule: ['ip4'],
            intRule: ['intege1'],
            portRule: [
                'intege1',
                { test: rangePortFunc, trigger: 'blur' }
            ],
            rangeRule: [
                'intege1',
                { test: rangeFunc, trigger: 'blur' }
            ],
            addCaseInfo: {
                status: false
            },
            caseConfigInfo: {
                status: false,
                config: {},
                testCaseId: '',
                sceneId: ''
            },
            caseAccountInfo: {
                status: false,
                testCaseId: '',
                sceneId: '',
                customConfig: {}
            },
            productInfo: {}
        };
    },
    mounted() {
        this.$refs['formValidate'].resetValidate();
    },
    computed: {
        bizModelList: {
            get: function() {
                return this.productInfo.instances || [];
            }
        }
    },
    methods: {
        // 查询场景信息
        async querySceneInfo(id) {
            try {
                return new Promise((resolve, reject) => {
                    querySceneInfo(id).then(res => {
                        if (res.success) {
                            const { data } = res;
                            this.formItems.deployInfo = { ...data.deployInfo };
                            this.formItems.bizSystemInfo = { ...data.bizSystemInfo };
                            this.formItems.bizSystemInfo.productInstNo = data?.bizSystemInfo?.productInstNo || _.find(this.productList, { hasSupportOrderTest: 1 })?.productInstNo;
                            this.formItems.bizSystemInfo.bizType = data?.bizSystemInfo?.bizType || this.bizModelList?.[0]?.serviceType;
                            this.$emit('set-scene-info', data);
                            if (data.caseList.length) this.formItems.testCaseId = data.caseList[0].id;
                            resolve(true);
                        } else {
                            this.$hMessage.error('未查询到场景信息!');
                            resolve(false);
                        }
                    }).catch(err => {
                        reject(err);
                        this.$hMessage.error('查询场景信息异常!');
                    });
                });
            } catch (err) {
                this.$hMessage.error('查询场景信息失败!');
            }
        },
        // 监听输入框失去焦点变化
        handleUpdateInfo(obj, key) {
            this.$refs.formValidate.validateField(`${obj}.${key}`, valid => {
                if (valid === '') {
                    const param = {};
                    param[key] = this.formItems[obj][key];
                    this.updateSceneInfo(param);
                }
            });
        },
        // 切换产品节点
        handleChangeProduct(currentValue) {
            this.productInfo = _.find(this.productList, ['productInstNo', currentValue]) || {};
            this.formItems.bizSystemInfo.productInstNo = this.productInfo.productInstNo || '';
            const bizType = this.formItems.bizSystemInfo.bizType;
            this.formItems.bizSystemInfo.bizType = '';
            this.$nextTick(() => {
                this.formItems.bizSystemInfo.bizType = _.find(this.bizModelList, ['instanceType', bizType]) ? bizType : this.bizModelList?.[0]?.instanceType;
            });
        },
        // 切换应用节点
        handleChangeBizType(val) {
            const { endpointIp, endpointPort } = (this.bizModelList.length && val) ? _.find(this.bizModelList, ['instanceType', val]) : { endpointIp: '', endpointPort: '' };
            this.formItems.bizSystemInfo.ip = endpointIp;
            this.formItems.bizSystemInfo.port = endpointPort;
            if (val) {
                this.$refs.formValidate.validateField('bizSystemInfo.productInstNo', valid => {
                    if (valid === '') {
                        this.$refs.formValidate.validateField('bizSystemInfo.bizType', valid => {
                            if (valid === '') this.updateSceneInfo({ ...this.formItems.bizSystemInfo });
                        });
                    }
                });
            }
        },
        // 切换测试用例
        handleChangeTestCaseId(val){
            this.formItems.commandLine = _.find(this.caseList, o => { return o.id === val; })?.commandLine;
        },
        // 更新场景信息
        updateSceneInfo(param, callback) {
            param.sceneId = this.sceneId;
            return new Promise((resolve, reject) => {
                try {
                    updateSceneInfo(param).then(res => {
                        if (res.success) {
                            this.formItems.deployInfo = { ...res?.data?.deployInfo };
                            this.formItems.bizSystemInfo = { ...res?.data?.bizSystemInfo };
                        }
                        resolve(res.success);
                        callback && callback(res.success);
                    });
                } catch (err) {
                    this.$hMessage.error('更新场景信息失败!');
                    reject(err);
                    callback && callback(false);
                }
            });
        },
        // 根据应用类型获取别名
        getInstanceTypeDesc(type) {
            return this.$store?.state?.apmDirDesc?.appTypeDictDesc?.[type] || '';
        },
        // 新建测试用例
        addCase() {
            if (this.sceneId) {
                this.addCaseInfo.sceneId = this.sceneId;
                this.addCaseInfo.status = true;
            } else {
                this.$hMessage.error('请先新建测试场景!');
            }
        },
        // 删除用例
        async handleDelCase(id) {
            this.$hMsgBoxSafe.confirm({
                title: '删除',
                content: `您确定删除当前测试用例${_.find(this.caseList, o => { return o.id === id; })?.name}？`,
                onOk: async () => {
                    const res = await delCase({
                        sceneId: this.sceneId,
                        testCaseId: this.formItems.testCaseId
                    });
                    if (res.success) {
                        this.querySceneInfo(this.sceneId);
                        this.$hMessage.success('测试用例删除成功!');
                    } else {
                        this.querySceneInfo(this.sceneId);
                        this.$hMessage.error({
                            content: res.message,
                            closable: true,
                            duration: 0
                        });
                    }
                }
            });
        },
        // 配置用例
        handleConfigCase() {
            this.caseConfigInfo.config = _.find(this.caseList, o => { return o.id === this.formItems.testCaseId; })?.apiDemoConfig?.config;
            this.caseConfigInfo.status = true;
            this.caseConfigInfo.testCaseId = this.formItems.testCaseId;
            this.caseConfigInfo.sceneId = this.sceneId;
        },
        // 配置多账号
        handleAccounts() {
            this.caseAccountInfo.status = true;
            this.caseAccountInfo.testCaseId = this.formItems.testCaseId;
            this.caseAccountInfo.sceneId = this.sceneId;
            this.caseAccountInfo.customConfig = _.find(this.caseList, o => { return o.id === this.formItems.testCaseId; })?.apiDemoConfig?.customConfig;
        },
        // 更新配置用例内容
        async updateConfigText(id) {
            const result = await this.querySceneInfo(this.sceneId);
            if (result) {
                this.formItems.testCaseId = id;
            }
        },
        // 获取实例列表
        queryInstance(id, items, isChange) {
            this.$emit('query-instance', id, items, isChange);
        },
        // 执行测试用
        doPerformCase() {
            this.$refs['formValidate'].validate((valid) => {
                if (valid) {
                    this.loading = true;
                    const param = {
                        sceneId: this.sceneId,
                        ...this.formItems
                    };
                    const productName = _.find(this.productList, o => { return o.productInstNo === this.formItems.bizSystemInfo.productInstNo; })?.productName;
                    const testCaseName = _.find(this.caseList, o => { return o.id === this.formItems.testCaseId; })?.name;
                    this.$hMsgBoxSafe.confirm({
                        title: `确定`,
                        content: `确定对${productName}-${testCaseName}执行测试？`,
                        okText: '确认',
                        onOk: async () => {
                            try {
                                const res = await performCase(param);
                                if (res.success) {
                                    // 如果是有实例在进行中
                                    if (res.data?.testCaseId && res.data?.testCaseInstanceId) {
                                        this.$hMsgBoxSafe.info({
                                            title: `${res.message}`,
                                            okText: '查看实例',
                                            onOk: () => {
                                                this.queryInstance(res.data.testCaseInstanceId, {
                                                    productInstNo: res.data.productInstNo,
                                                    testCaseId: res.data.testCaseId
                                                }, false);
                                            }
                                        });
                                    } else {
                                        this.$hMessage.success('测试执行成功!');
                                        await this.querySceneInfo();
                                        this.formItems.testCaseId = param.testCaseId;
                                        this.$nextTick(() => {
                                            this.queryInstance(false, {
                                                productInstNo: this.formItems.bizSystemInfo.productInstNo,
                                                testCaseId: param.testCaseId
                                            }, false);
                                        });
                                    }
                                }
                            } catch (err) {
                                console.log(err);
                            }
                            this.loading = false;
                        },
                        onCancel: () => {
                            this.loading = false;
                        }
                    });

                }
            });
        }
    },
    components: { aButton, addCaseModal, caseConfigModal, caseAccountModal }
};
</script>

<style  lang="less" scoped>
.config-shell {
    .wrapper-config {
        display: flex;
        width: 100%;
        height: 180px;
        padding: 16px 0;

        & > div {
            display: inline-block;
        }

        & > .box-config {
            width: 32%;
            height: 144px;
            background: var(--primary-color);
            border-radius: var(--border-radius);
            color: var(--font-color);
            box-sizing: border-box;
            display: flex;
            align-items: center;
            padding: 0 10px;

            .form-pro {
                margin: 2px 0;
            }

            .box-config-left {
                width: 64px;
                height: 120px;
                box-sizing: border-box;
                background: var(--box-color);
                padding: 5px 4px;
                border-radius: var(--border-radius);

                & > div {
                    text-align: center;
                    margin: 10px 0;
                }

                .number {
                    display: inline-block;
                    width: 14px;
                    height: 14px;
                    border-radius: 7px;
                    line-height: 14px;
                    border: 1px solid var(--font-color);
                    text-align: center;
                }

                & > .box-header {
                    font-size: var(--font-size-base);
                    line-height: 18px;
                    font-weight: 500;
                    text-align: center;
                }
            }

            .box-config-right {
                width: calc(100% - 64px);
                position: relative;
            }

            .flex-center {
                display: flex;
                justify-content: center;
                margin-bottom: 10px;

                & > button {
                    margin: 0 4px;
                }

                .btn-del {
                    border-color: var(--error-color);
                    color: var(--error-color);
                }

                .btn-def {
                    width: 80px;
                    display: flex;
                    justify-content: center;
                }
            }
        }

        & > .middle-arrow {
            position: relative;
            width: 1%;
            margin: 0 1%;
            border-bottom: 2px solid var(--border-color);
            height: 71px;
        }

        & > .middle-arrow::before {
            content: "";
            position: absolute;
            display: inline-block;
            right: -4px;
            bottom: -5px;
            width: 0;
            height: 0;
            border-top: 4px solid transparent;
            border-left: 10px solid var(--border-color);
            border-bottom: 4px solid transparent;
        }
    }

    .wrapper-shell {
        display: flex;
        align-items: center;
        width: 100%;
        height: 56px;
        background: var(--primary-color);
        border-radius: var(--border-radius);
        color: var(--font-color);
        padding: 0 25px 0 15px;

        & > .wrapper-shell-left {
            width: 100px;
            height: 32px;
            border-radius: var(--border-radius);
            background: var(--box-color);
            text-align: center;
            line-height: 32px;
            font-weight: 500;
        }
    }

    // 当前页面表单
    /deep/ .h-form-item {
        margin: 8px 0;
    }

    /deep/ .h-form-item-label {
        padding: 0 10px;
    }

    .form-item {
        display: flex;
        align-items: center;
        width: 100%;
        font-size: var(--font-size-base);

        & > .h-form-item {
            display: flex;
            width: calc(100% - 180px);
        }

        /deep/ .h-form-item-content {
            width: calc(100% - 90px);
            flex-shrink: 1;
            margin-left: 0 !important;
        }

        /deep/ .h-checkbox-wrapper {
            display: flex;
            justify-content: center;
            align-items: center;
            width: 120px;
            flex-shrink: 0;
        }

        /deep/ .h-checkbox {
            margin-right: 4px;
        }
    }
}

</style>
