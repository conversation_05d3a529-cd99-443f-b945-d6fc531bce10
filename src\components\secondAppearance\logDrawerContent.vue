<template>
  <div class="drawer-content-container">
    <div v-for="item in records" :key="item.clusterName" class="drawer-content-item" >
        <aTitle :title="item.clusterName"></aTitle>
        <h-form ref="formCols" :label-width="120" cols="3">
            <h-form-item label="上场记录总数：">
                <span>{{ item.totalCount || 0}}</span>
            </h-form-item>
            <h-form-item label="成功记录数：" cols="4">
                <span>{{ item.successCount || 0}}</span>
            </h-form-item>
            <h-form-item label="失败记录数：" cols="4">
                <span>{{item.failCount || 0}}</span>
            </h-form-item>
            <h-form-item v-if="item.errorMessage" label="错误信息：" cols="3">
                <h-input v-model="item.errorMessage" type="textarea" width="506px" :canResize="false" disabled></h-input>
            </h-form-item>
        </h-form>
    </div>
  </div>
</template>

<script>
import aTitle from '@/components/common/title/aTitle';
import { getLoadDataRuleResult } from '@/api/brokerApi';

export default {
    props: {
        productId: {
            required: true,
            type: String
        },
        drawerData: {
            required: false,
            type: Object,
            default: () => {}
        }
    },
    watch: {
        drawerData: {
            handler(){
                this.getLogData();
            },
            deep: true
        }
    },
    components: {
        aTitle
    },
    data(){
        return {
            records: []
        };
    },
    methods: {
        // 调用接口，获取日志信息
        async getLogData(){
            const params = {
                execIds: this.drawerData.execIds
            };
            this.records = [];
            try {
                const res = await getLoadDataRuleResult(params);
                if (res.success){
                    this.records = res?.data || [];
                }
            } catch (err) {
                this.$hMessage.error(err.message);
            }
        }
    }
};
</script>

<style lang="less" scoped>
.drawer-content-container {
    .apm-title {
        background: #fff;
        color: #333;
    }

    /deep/ .h-input-wrapper {
        textarea {
            height: 164px;
        }
    }
}
</style>
