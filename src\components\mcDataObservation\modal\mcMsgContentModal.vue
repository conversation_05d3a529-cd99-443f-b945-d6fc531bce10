<template>
    <div>
        <h-msg-box
            v-model="modalData.status"
            :mask-closable="false"
            footerHide
            title="消息内容"
            width="700"
            max-height="400"
            @on-open="getCollections">
            <div class="clip-box">
                <span
                    class="clip-button"
                    :class="copied ? 'copied' : ''"
                    :data-clipboard-text="msgCount"
                    @click="onCopied">
                    {{ copied ? '复制成功' : '复制' }}
                </span>
            </div>
            <p>{{msgCount}}</p>
        </h-msg-box>
    </div>
</template>

<script>
import Clipboard from 'clipboard';
export default {
    name: 'McMsgContentModal',
    props: {
        modalInfo: {
            type: Object,
            default: null
        }
    },
    data() {
        return {
            copied: false,
            msgCount: '',
            modalData: this.modalInfo
        };
    },
    methods: {
        getCollections() {
            this.msgCount = this.modalInfo?.params?.msg;
        },
        // 复制功能
        onCopied() {
            if (this.copied) {
                return;
            }
            const clipBoard = new Clipboard('.clip-button');
            clipBoard.on('success', (e) => {
                this.copied = true;
                setTimeout(() => {
                    this.copied = false;
                }, 2000);
                clipBoard.destroy();
            });
            clipBoard.on('error', (e) => {
                this.$hMessage.error('该浏览器不支持复制');
                clipBoard.destroy();
            });
        }
    }
};
</script>

<style lang="less" scoped>
/deep/ .h-modal-body {
    padding: 10px 16px;
    min-height: 150px;
}

.clip-box {
    margin: 5px 8px;
    text-align: right;
    color: var(--link-color);

    &.copied {
        opacity: 0.4;
        cursor: default;
    }

    .clip-button {
        cursor: pointer;
    }
}
</style>
