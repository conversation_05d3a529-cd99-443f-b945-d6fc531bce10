<template>
    <div v-if="modalData.status">
        <!-- 批量编辑弹窗 -->
        <h-msg-box-safe v-model="modalData.status" :mask-closable="false" :title="modalData.title" width="600" height="230px" @on-open="getInitData">
            <h-form
                ref="formValidate"
                :model="formValidate"
                :rules="ruleValidate"
                :label-width="120">
                <h-form-item label="加载方式: " prop="loadMode">
                    <h-select
                        v-model="formValidate.loadMode"
                        style="width: 398px;"
                        placeholder="请选择加载方式"
                        :clearable="false"
                        transfer
                    >
                        <h-option v-for="item in loadingTypeOptions" :key="item.label" :value="item.value">{{ item.label }}</h-option>
                    </h-select>
                </h-form-item>
                <h-form-item label="资金账户:" prop="loadSql">
                    <h-input v-model.trim="formValidate.loadSql" type="textarea" style="width: 398px;" :maxlength="1000" :canResize="false"></h-input>
                </h-form-item>
            </h-form>
            <template v-slot:footer>
                <a-button @click="modalData.status = false">取消</a-button>
                <a-button type="primary" :loading="loading" @click="handleSubmit">确定</a-button>
            </template>
        </h-msg-box-safe>
    </div>
</template>

<script>
import { LOADING_TYPE_OPTIONS } from '@/components/secondAppearance/constant';
import aButton from '@/components/common/button/aButton';
import { batchLoadDataRule } from '@/api/brokerApi';

export default {
    props: {
        modalInfo: {
            type: Object,
            default: null
        },
        productId: {
            type: String,
            default: ''
        }
    },
    data() {
        return {
            loading: false,
            loadingTypeOptions: LOADING_TYPE_OPTIONS,
            modalData: this.modalInfo,
            formValidate: {
                loadMode: '',
                loadSql: ''
            },
            ruleValidate: {
                loadMode: [{ required: true, message: '加载方式为必填项' }]
            }
        };
    },
    computed: { },
    methods: {
        // 初始化
        getInitData() {
            const { loadSql, loadMode } = this.modalInfo.data;
            this.formValidate.loadSql = loadSql;
            this.formValidate.loadMode = loadMode;
        },
        // 调用接口 编辑条件
        handleSubmit(){
            this.$refs['formValidate'].validate(async (valid) => {
                if (valid) {
                    const param = {
                        productId: this.productId,
                        whereCondition: 'fundAccount',
                        loadMode: Number(this.formValidate.loadMode),
                        loadSql: this.formValidate.loadSql
                    };
                    try {
                        this.loading = true;
                        const res = await batchLoadDataRule(param);
                        if (res.success) {
                            this.$hMessage.success('操作成功!');
                            this.$emit('query');
                            this.modalData.status = false;
                        }
                    } catch (error) {
                        console.error(error);
                    }
                    this.loading = false;
                }
            });
        }
    },
    components: { aButton }
};
</script>

<style lang="less" scoped>
    /deep/ .h-modal-body {
        padding: 16px;

        .h-input-wrapper {
            textarea {
                height: 96px;
                line-height: 1.5;
            }
        }
    }
</style>
