<template>
    <div class="main">
        <div class="title">
            <a-title title="RCM配置管理">
                <slot>
                    <h-select
                        v-show="productList.length > 1"
                        v-model="productInstNo"
                        placeholder="请选择"
                        class="title-single-select"
                        placement="bottom"
                        :positionFixed="true"
                        :clearable="false"
                        @on-change="checkProduct">
                        <h-option
                            v-for="item in productList"
                            :key="item.id"
                            :value="item.productInstNo || ''">
                            {{ item.productName }}
                        </h-option>
                    </h-select>
                </slot>
            </a-title>
        </div>
        <a-loading v-if="loading"></a-loading>

        <menu-layout
            ref="menu"
            :menuList="menuList"
            menuItemId="id"
            titleAttribute="name"
            menuTitle="RCM配置列表"
            footer="删除配置"
            :productAlerts="productAlerts"
            @check-menu="selectMenuChange"
            @footer-click="delRcmProductNode">
            <template v-slot:right>
                <h-tabs
                    v-if="rcmId && editableTabs.length"
                    v-model="tabName"
                    class="product-box"
                    @on-click="tabClick(tabName)">
                    <h-tab-pane
                        v-for="item in editableTabs"
                        :key="item.name"
                        :label="tabLabel(item)"
                        :name="item.name">
                        <component
                            :is='item.name'
                            :ref="item.name"
                            :rcmId="rcmId"
                            :instanceId="instanceData.instanceId"
                            :localStatus="localStatus"
                            :remoteStatus="remoteStatus"
                            @turnToContext="turnToContext">
                        </component>
                    </h-tab-pane>
                </h-tabs>
                <no-data v-if="!rcmId" class="box"></no-data>
            </template>
        </menu-layout>
    </div>
</template>

<script>
import _ from 'lodash';
import { mapState, mapActions } from 'vuex';
import { getRcmInstanceList, delRcmProductNode, getRcmAlertStatus } from '@/api/rcmApi';
import { getDashboardTag, getDashboardConfigV2 } from '@/api/httpApi';
import noData from '@/components/common/noData/noData';
import aTitle from '@/components/common/title/aTitle';
import aLoading from '@/components/common/loading/aLoading';
import menuLayout from '@/components/common/menuLayout/menuLayout';
import rcmOverview from '@/components/rcmDeploy/rcmOverview';
import rcmConfigModel from '@/components/rcmDeploy/rcmConfigModel';
import rcmSourceFile from '@/components/rcmDeploy/rcmSourceFile';
import rcmDeployTopic from '@/components/rcmDeploy/rcmDeployTopic';
import rcmDeployContext from '@/components/rcmDeploy/rcmDeployContext';
import rcmMulticast from '@/components/rcmDeploy/rcmMulticast/index.vue';
export default {
    name: 'RcmDeploy',
    data() {
        return {
            editableTabs: [],
            rcmId: '', // rcm文档id
            productInstNo: '', // 选中的产品
            tabName: 'rcmOverview', // tab默认选择
            menuList: [],
            localStatus: false,     // 是否需要告警
            remoteStatus: false,
            productAlerts: [],      // 告警产品id集合
            loading: false,
            timer: null,
            instanceData: {},
            isFirstRender: true,
            tabLabel: (item) => (h) => {
                return h('div', [
                    h('span', item.describe),
                    ((this.localStatus || this.remoteStatus) && item.describe === '发布') ? h('Poptip', {
                        props: {
                            customTransferClassName: 'apm-poptip monitor-poptip',
                            content: '请查看待发布配置和已发布配置告警提示',
                            positionFixed: true,
                            transfer: true,
                            trigger: 'hover'
                        }
                    }, [
                        h('h-icon', {
                            props: {
                                name: 'prompt_fill',
                                color: '#ff9901',
                                size: 16
                            },
                            style: {
                                'margin-left': '5px'
                            }
                        })
                    ]) : null
                ]);
            }
        };
    },
    mounted() {
        this.init();
    },
    computed: {
        ...mapState({
            productList: state => {
                return state.product.productListLight || [];
            }
        })
    },
    methods: {
        ...mapActions({ getProductList: 'product/getProductListLight' }),
        ...mapActions({ getRcmProductTemplates: 'rcm/getRcmProductTemplates' }),
        // 初始化页面
        async init() {
            try {
                this.loading = true;
                await this.getProductList({
                    filter: 'supportRcm'
                });
                this.timer = setInterval(() => {
                    this.getRcmAlertStatus();
                }, 3000);
                const productInstNo = localStorage.getItem('productInstNo');
                this.productInstNo = _.find(this.productList, ['productInstNo', productInstNo])?.productInstNo || this.productList?.[0]?.productInstNo;
            } catch (e) {
                console.error(e);
            } finally {
                this.loading = false;
            }
        },

        async getDashboardConfig() {
            try {
                let tags = [];
                // 获取dashboard查询tag
                const tagsRes = await getDashboardTag({
                    scene: 'RcmObservation',
                    objectType: 'rcm',
                    objectId: this.rcmId
                });
                if (tagsRes.code === '200') {
                    tags = tagsRes?.data || [];
                }
                // tags作为入参，查询dashboard配置
                const res = await getDashboardConfigV2({
                    scene: 'RcmObservation',
                    tags: tags,
                    objectType: 'rcm',
                    objectId: this.rcmId
                });
                if (res.code === '200') {
                    this.editableTabs = res?.data?.filter(v => v?.visible === true) || [];
                }
            } catch (err) {
                this.editableTabs = [];
            }
        },
        // 切换导航产品
        async checkProduct(item) {
            if (!item) return;

            this.loading = true;
            localStorage.setItem('productInstNo', item);
            this.productInstNo = item;
            await this.getRcmInstanceList(item);

            // 判断左侧菜单少于一个选项时，隐藏菜单
            this.$nextTick(() => {
                if (this.$refs['menu']) {
                    this.$refs['menu'].setMenuStatus(this.menuList?.length <= 1);
                }
            });
            // rcm配置跳转
            if (this.$route.query.rcmId) {
                this.instanceData = this.isFirstRender ? _.find(this.menuList, ['id', this.$route.query.rcmId]) : this.menuList?.[0];
            } else {
                this.instanceData = this.menuList?.[0];
            }
            this.tabName = this.isFirstRender ? this.$route.query?.activeTabName || this.tabName : this.tabName;
            await this.$refs['menu'].initMenu(this.instanceData);
            setTimeout(() => {
                this.loading = false;
                this.isFirstRender = false;
            }, 800);
        },
        // 导航栏切换 - name对应rcm产品实例下的rcm文档id
        async selectMenuChange(item) {
            this.localStatus = false;
            this.remoteStatus = false;
            this.getRcmAlertStatus();
            this.instanceData = item;
            this.rcmId = item?.id;
            await this.getRcmProductTemplates({
                id: this.rcmId,
                templateTypes: 'transport,singletonContext,clusterContext'
            });

            // 获取tab页配置
            await this.getDashboardConfig();

            const tab = _.find(this.editableTabs, ['name', this.tabName]);
            this.tabName = tab ? this.tabName : this.editableTabs?.[0]?.name || 'rcmOverview';

            this.$nextTick(async () => {
                await this.tabClick(this.tabName);
            });
        },
        // 切换tab
        async tabClick(name) {
            this.tabName = name;
            this.$nextTick(async () => {
                const ref = this.$refs[name]?.[0];
                ref && ref.initData(this.isFirstRender ? this.$route.query : '');
            });
        },
        // 获取rcm实例节点列表
        async getRcmInstanceList(productInstNo) {
            try {
                const res = await getRcmInstanceList({ productId: productInstNo });
                if (res.code === '200') {
                    this.menuList = res?.data || [];
                } else {
                    this.menuList = [];
                }
            } catch (err) {
                this.menuList = [];
            }
        },
        // 删除RCM文档节点
        async delRcmProductNode() {
            if (!this.rcmId) return;
            this.instanceData.instanceId && this.$hMsgBoxSafe.confirm({
                title: `删除`,
                content: `您确定删除名为"${this.instanceData.name}"的节点实例？`,
                onOk: async () => {
                    const res = await delRcmProductNode({
                        id: this.instanceData.instanceId
                    });
                    if (res.success) {
                        this.$hMessage.success('删除成功');
                        this.checkProduct(this.productInstNo);
                    }
                }
            });
        },
        // 监控文件状态变更
        async getRcmAlertStatus() {
            try {
                const res = await getRcmAlertStatus();
                if (res.code === '200') {
                    const data = _.find(res.data, ['id', this.rcmId]);
                    this.productAlerts = _.map(res.data, 'id');
                    this.localStatus = data?.localStatus || false;
                    this.remoteStatus = data?.remoteStatus ||  false;
                } else {
                    this.localStatus = false;
                    this.remoteStatus = false;
                    this.timer && clearInterval(this.timer);
                }
            } catch (error) {
                console.error(error);
                this.localStatus = false;
                this.remoteStatus = false;
                this.timer && clearInterval(this.timer);
            }
        },
        /**
         * 跳转至上下文
         */
        turnToContext(zone) {
            this.tabName = 'rcmDeployContext';
            const ref = this.$refs[this.tabName]?.[0];
            if (ref) {
                ref.initData({ zone });
                setTimeout(() => {
                    this.$nextTick(() => {
                        document.querySelector('#rcm-inpt-placeholder').focus();
                    });
                }, 1000);
            }
        }
    },
    beforeDestroy() {
        this.timer && clearInterval(this.timer);
    },
    components: { rcmMulticast, aTitle, noData, aLoading, menuLayout, rcmOverview, rcmConfigModel, rcmDeployTopic, rcmSourceFile, rcmDeployContext  }
};
</script>

<style lang="less" scoped>
@import url("@/assets/css/tab.less");
@import url("@/assets/css/input.less");
@import url("@/assets/css/menu.less");

.main {
    .title {
        min-width: 900px;
    }

    .product-box {
        /deep/ .h-tabs-nav-wrap {
            float: none !important;
        }

        /deep/ .h-tabs-nav-right {
            position: absolute;
            right: 0;
            top: 5px;
        }
    }

    /deep/ .apm-box {
        height: calc(100% - 58px);
    }
}
</style>
