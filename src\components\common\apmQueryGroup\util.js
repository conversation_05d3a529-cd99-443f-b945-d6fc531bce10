/**
 * 初始化data数据
 * @param {IQueryItem[]} items
 */
export function initValueObject(items) {
    const result = {};
    items.forEach((item) => {
        result[item.key] = item.value;
    });
    return result;
}

/**
 * 转换value成String
 * @param {IQueryItem[]} items
 * @param {Record<string, any>} data
 */
export function convertValueFromObject(items, data) {
    let result = '';
    items.forEach((item) => {
        result += data[item.key];
    });
    return result;
}
