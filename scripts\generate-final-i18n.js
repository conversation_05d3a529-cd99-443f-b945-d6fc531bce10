#!/usr/bin/env node

const fs = require('fs');
const path = require('path');

/**
 * 最终国际化文件生成脚本
 * 将提取和组织的文案与现有的国际化文件合并，生成最终的国际化文件
 */

class FinalI18nGenerator {
    constructor() {
        this.srcDir = path.join(__dirname, '../src');
        this.organizedDir = path.join(__dirname, '../src/locales/organized');
        this.outputDir = path.join(__dirname, '../src/locales/final');
        
        // 加载现有的国际化文件
        this.existingZhCN = this.loadExistingI18n('zh-CN.js');
        this.existingEnUS = this.loadExistingI18n('en-US.js');
        
        // 加载组织后的文件
        this.organizedZhCN = this.loadOrganizedI18n('zh-CN.js');
        this.organizedEnUS = this.loadOrganizedI18n('en-US.js');
    }

    /**
     * 加载现有的国际化文件
     */
    loadExistingI18n(filename) {
        try {
            const filePath = path.join(this.srcDir, 'locales', filename);
            if (!fs.existsSync(filePath)) {
                console.warn(`现有国际化文件不存在: ${filePath}`);
                return {};
            }
            
            const content = fs.readFileSync(filePath, 'utf-8');
            // 简单的解析，移除export default并解析JSON
            const jsonStr = content.replace(/export\s+default\s+/, '').replace(/;?\s*$/, '');
            return JSON.parse(jsonStr);
        } catch (error) {
            console.warn(`加载现有国际化文件失败 ${filename}:`, error.message);
            return {};
        }
    }

    /**
     * 加载组织后的国际化文件
     */
    loadOrganizedI18n(filename) {
        try {
            const filePath = path.join(this.organizedDir, filename);
            const content = fs.readFileSync(filePath, 'utf-8');
            const jsonStr = content.replace(/export\s+default\s+/, '').replace(/;?\s*$/, '');
            return JSON.parse(jsonStr);
        } catch (error) {
            console.error(`加载组织后的国际化文件失败 ${filename}:`, error.message);
            return {};
        }
    }

    /**
     * 深度合并对象
     */
    deepMerge(target, source) {
        const result = { ...target };
        
        for (const key in source) {
            if (source.hasOwnProperty(key)) {
                if (typeof source[key] === 'object' && source[key] !== null && !Array.isArray(source[key])) {
                    result[key] = this.deepMerge(result[key] || {}, source[key]);
                } else {
                    result[key] = source[key];
                }
            }
        }
        
        return result;
    }

    /**
     * 合并国际化文件
     */
    mergeI18nFiles() {
        // 合并中文文件
        const finalZhCN = this.deepMerge(this.existingZhCN, this.organizedZhCN);
        
        // 合并英文文件
        const finalEnUS = this.deepMerge(this.existingEnUS, this.organizedEnUS);
        
        return { finalZhCN, finalEnUS };
    }

    /**
     * 生成使用示例文件
     */
    generateUsageExample() {
        const exampleContent = `// 国际化使用示例

// 1. 在Vue组件中使用
export default {
    template: \`
        <div>
            <!-- 基本使用 -->
            <h-button>{{ $t('common.query') }}</h-button>
            
            <!-- 页面特定文案 -->
            <div>{{ $t('pages.managementQuery.pleaseSelect') }}</div>
            
            <!-- 组件特定文案 -->
            <span>{{ $t('components.ldpTable.noData') }}</span>
        </div>
    \`,
    methods: {
        showMessage() {
            // 在JavaScript中使用
            this.$hMessage.success(this.$t('common.operationSuccess'));
        }
    }
};

// 2. 在JavaScript文件中使用
import { i18n } from '@/locales';

function showAlert() {
    alert(i18n.t('common.error'));
}

// 3. 常用文案路径参考
const commonPaths = {
    // 通用操作
    'common.query': '查询',
    'common.add': '添加',
    'common.edit': '编辑',
    'common.delete': '删除',
    'common.save': '保存',
    'common.cancel': '取消',
    
    // 状态提示
    'common.success': '成功',
    'common.failed': '失败',
    'common.loading': '加载中',
    'common.noData': '暂无数据',
    
    // 页面文案
    'pages.managementQuery.pleaseSelect': '请选择',
    'pages.sqlCores.query': '查询',
    'pages.analyseData.config': '配置报表',
    
    // 组件文案
    'components.ldpTable.export': '导出',
    'components.common.confirm': '确认'
};

// 4. 国际化key命名规范
/*
结构: {category}.{module}.{key}

category: 
- common: 通用文案
- pages: 页面特定文案  
- components: 组件特定文案

module: 具体的页面名或组件名

key: 具体的文案标识，使用小驼峰命名
*/`;

        return exampleContent;
    }

    /**
     * 生成更新指南
     */
    generateUpdateGuide() {
        const guideContent = `# 国际化文案更新指南

## 文件结构
\`\`\`
src/locales/
├── final/           # 最终生成的国际化文件
│   ├── zh-CN.js    # 中文文案
│   ├── en-US.js    # 英文文案
│   └── index.js    # 导出文件
├── organized/       # 组织后的文件
├── extracted/       # 原始提取的文件
├── zh-CN.js        # 原有的中文文案
├── en-US.js        # 原有的英文文案
└── index.js        # 原有的导出文件
\`\`\`

## 使用新的国际化文件

### 1. 更新导入路径
将现有的国际化导入从：
\`\`\`javascript
import customLocales from '@/locales';
\`\`\`

更新为：
\`\`\`javascript
import customLocales from '@/locales/final';
\`\`\`

### 2. 更新组件中的文案引用
将硬编码的中文文案替换为国际化key：

**之前：**
\`\`\`vue
<template>
    <h-button>查询</h-button>
    <div>请选择节点</div>
</template>
\`\`\`

**之后：**
\`\`\`vue
<template>
    <h-button>{{ $t('common.query') }}</h-button>
    <div>{{ $t('pages.managementQuery.pleaseSelectNode') }}</div>
</template>
\`\`\`

### 3. 更新JavaScript中的文案
**之前：**
\`\`\`javascript
this.$hMessage.success('操作成功');
\`\`\`

**之后：**
\`\`\`javascript
this.$hMessage.success(this.$t('common.operationSuccess'));
\`\`\`

## 添加新文案

### 1. 确定分类
- \`common\`: 通用文案（按钮、状态、提示等）
- \`pages.{pageName}\`: 页面特定文案
- \`components.{componentName}\`: 组件特定文案

### 2. 添加到对应文件
在 \`src/locales/final/zh-CN.js\` 和 \`src/locales/final/en-US.js\` 中添加新的键值对。

### 3. 使用小驼峰命名
- 好的命名: \`pleaseSelect\`, \`operationSuccess\`, \`startTime\`
- 避免: \`please_select\`, \`operation-success\`, \`StartTime\`

## 注意事项

1. **保持一致性**: 相同含义的文案使用相同的key
2. **避免重复**: 优先使用common中的通用文案
3. **语义化命名**: key名称应该能够表达文案的含义
4. **及时更新**: 添加新文案时同时更新中英文版本

## 常用文案参考

### 操作类
- \`common.query\`: 查询
- \`common.add\`: 添加
- \`common.edit\`: 编辑
- \`common.delete\`: 删除
- \`common.save\`: 保存
- \`common.cancel\`: 取消

### 状态类
- \`common.success\`: 成功
- \`common.failed\`: 失败
- \`common.loading\`: 加载中
- \`common.noData\`: 暂无数据

### 提示类
- \`common.pleaseSelect\`: 请选择
- \`common.pleaseInput\`: 请输入
- \`common.operationSuccess\`: 操作成功
- \`common.confirmDelete\`: 确认删除吗？
`;

        return guideContent;
    }

    /**
     * 生成最终文件
     */
    generateFinalFiles() {
        // 确保输出目录存在
        if (!fs.existsSync(this.outputDir)) {
            fs.mkdirSync(this.outputDir, { recursive: true });
        }

        const { finalZhCN, finalEnUS } = this.mergeI18nFiles();

        // 写入最终的国际化文件
        this.writeFile('zh-CN.js', `export default ${JSON.stringify(finalZhCN, null, 2)};`);
        this.writeFile('en-US.js', `export default ${JSON.stringify(finalEnUS, null, 2)};`);

        // 生成索引文件
        const indexContent = `import zhCN from './zh-CN';
import enUS from './en-US';

export default {
    'zh-CN': zhCN,
    'en-US': enUS
};`;
        this.writeFile('index.js', indexContent);

        // 生成使用示例
        this.writeFile('usage-example.js', this.generateUsageExample());

        // 生成更新指南
        this.writeFile('UPDATE_GUIDE.md', this.generateUpdateGuide());

        // 生成统计报告
        this.generateFinalReport(finalZhCN, finalEnUS);
    }

    /**
     * 写入文件
     */
    writeFile(filename, content) {
        const filePath = path.join(this.outputDir, filename);
        fs.writeFileSync(filePath, content, 'utf-8');
        console.log(`✅ 生成文件: ${filePath}`);
    }

    /**
     * 生成最终报告
     */
    generateFinalReport(zhCN, enUS) {
        const countTexts = (obj) => {
            let count = 0;
            for (const key in obj) {
                if (typeof obj[key] === 'object' && obj[key] !== null) {
                    count += countTexts(obj[key]);
                } else {
                    count++;
                }
            }
            return count;
        };

        const report = {
            summary: {
                totalChineseTexts: countTexts(zhCN),
                totalEnglishTexts: countTexts(enUS),
                generateTime: new Date().toISOString(),
                categories: Object.keys(zhCN)
            },
            details: {
                chinese: this.analyzeStructure(zhCN),
                english: this.analyzeStructure(enUS)
            }
        };

        this.writeFile('final-report.json', JSON.stringify(report, null, 2));

        console.log('\n📊 最终统计:');
        console.log(`中文文案总数: ${report.summary.totalChineseTexts}`);
        console.log(`英文文案总数: ${report.summary.totalEnglishTexts}`);
        console.log(`主要分类: ${report.summary.categories.join(', ')}`);
    }

    /**
     * 分析结构
     */
    analyzeStructure(obj, prefix = '') {
        const result = {};
        
        for (const key in obj) {
            const fullKey = prefix ? `${prefix}.${key}` : key;
            
            if (typeof obj[key] === 'object' && obj[key] !== null) {
                const subResult = this.analyzeStructure(obj[key], fullKey);
                Object.assign(result, subResult);
            } else {
                const category = fullKey.split('.')[0];
                if (!result[category]) {
                    result[category] = 0;
                }
                result[category]++;
            }
        }
        
        return result;
    }

    /**
     * 执行生成
     */
    run() {
        console.log('🚀 开始生成最终国际化文件...');
        console.log(`组织文件目录: ${this.organizedDir}`);
        console.log(`输出目录: ${this.outputDir}`);

        this.generateFinalFiles();

        console.log('✨ 最终国际化文件生成完成!');
        console.log('\n📝 后续步骤:');
        console.log('1. 查看生成的文件: src/locales/final/');
        console.log('2. 阅读更新指南: src/locales/final/UPDATE_GUIDE.md');
        console.log('3. 参考使用示例: src/locales/final/usage-example.js');
        console.log('4. 更新项目中的国际化导入路径');
        console.log('5. 逐步替换硬编码的中文文案');
    }
}

// 执行脚本
if (require.main === module) {
    const generator = new FinalI18nGenerator();
    generator.run();
}

module.exports = FinalI18nGenerator;
