.apm-title {
    position: relative;
    width: 100%;
    height: 42px;
    padding: 0 0 0 20px;
    background: var(--primary-color);
    border-radius: var(--border-radius);
    color: var(--font-color);
    font-size: var(--title-font-size);
    line-height: 42px;
}

.apm-title::before {
    display: inline-block;
    position: relative;
    left: -5px;
    top: 7px;
    content: "";
    width: 5px;
    height: 25px;
    background: var(--link-color);
}

.apm-title > .text {
    margin-left: 10px;
}

.apm-title .title-single-select {
    position: absolute;
    top: 5px;
    right: 10px;
    width: auto;
    min-width: 200px;
}

.apm-title .h-form-item-label {
    color: var(--font-color) !important;
}

.apm-title .h-input {
    background-color: var(--input-bg-color);
    border: var(--border);
    border-radius: var(--border-radius);
    color: var(--font-color);
}

.apm-title .h-select > .h-select-left {
    color: var(--font-color);
    background-color: var(--input-bg-color);
    border: var(--border);
}

.apm-title .h-select-content-input,
.apm-title .h-select-input {
    color: var(--font-color);
}

.obs-title {
    position: relative;
    width: 100%;
    height: 42px;
    padding: 0 0 0 20px;
    border-radius: var(--border-radius);
    color: var(--font-color);
    font-size: var(--title-font-size);
    line-height: 42px;
}

.obs-title::before {
    display: inline-block;
    position: relative;
    left: -5px;
    top: 7px;
    content: "";
    width: 5px;
    height: 25px;
    background: var(--link-color);
}

.obs-title > .title-text {
    margin-left: 10px;
}

.obs-title > .title-label {
    position: absolute;
    top: 0;
    left: 35px;
}

.obs-title > .title-label p {
    display: inline-block;
    margin-right: 10px;
}

.obs-title .title-box {
    float: right;
}

.obs-title .title-box > .h-select {
    width: auto;
    min-width: 200px;
    display: inline-block;
    margin-right: 10px;
}

.obs-title .title-box > p {
    display: inline-block;
    margin-right: 10px;
}

.obs-title .title-box > button {
    display: inline-block;
    margin-right: 10px;
}
