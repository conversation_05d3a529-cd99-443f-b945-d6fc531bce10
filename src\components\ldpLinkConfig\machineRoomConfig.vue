<!-- 部署机房详情Tab -->
<template>
    <div class="tab-box">
        <div ref="table-box" class="table-box">
            <obs-table
                :title="tableTitle"
                :tableData="tableData"
                :columns="columns"
                :maxHeight="tableHeight"
                :loading="loading" />
        </div>

        <!-- 修改机房名 -->
        <edit-room-name-modal
            v-if="configInfo.status"
            :productId="productInfo.id"
            :modalInfo="configInfo"
            @update="initData">
        </edit-room-name-modal>
    </div>
</template>

<script>
import { getMachineRoomInfo } from '@/api/productApi';
import obsTable from '@/components/common/obsTable/obsTable';
import editRoomNameModal from '@/components/ldpLinkConfig/modal/editRoomNameModal.vue';

export default {
    name: 'MachineRoomConfig',
    components: { obsTable, editRoomNameModal },
    props: {
        productInfo: {
            type: Object,
            default: {}
        }
    },
    data() {
        return {
            tableHeight: 0,
            tableTitle: {
                label: `机房信息：${(this.tableData || []).length}`
            },
            columns: [
                {
                    title: '机房名',
                    key: 'roomName',
                    minWidth: 180
                },
                {

                    title: '机房别名',
                    key: 'roomNameAlias',
                    minWidth: 150,
                    render: (h, { row }) => {
                        return h('div', {
                            style: {
                                display: 'flex',
                                'align-items': 'center'
                            }
                        }, [
                            h('span', row.roomNameAlias || '-'),
                            h('h-icon', {
                                props: {
                                    name: 't-b-modify'
                                },
                                style: {
                                    color: '#2D8DE5',
                                    cursor: 'pointer',
                                    marginLeft: '8px'
                                },
                                on: {
                                    'on-click': () => this.editRoomName(row)
                                }
                            })
                        ]);
                    }
                },
                {
                    title: '关联应用个数',
                    key: 'instanceNumber'
                }
            ],
            tableData: [],
            loading: false,

            configInfo: {
                status: false
            }
        };
    },
    mounted() {
        this.fetTableHeight();
        window.addEventListener('resize', this.fetTableHeight);
    },
    beforeDestroy() {
        window.removeEventListener('resize', this.fetTableHeight);
    },
    methods: {
        // 设置table高度
        fetTableHeight() {
            this.tableHeight = this.$refs['table-box']?.offsetHeight - 52;
        },
        async initData() {
            this.clearPageData();
            this.loading = true;
            try {
                await this.getMachineRoomInfo();
            } finally {
                this.tableTitle.label = `机房信息：${(this.tableData || []).length}`;
                this.loading = false;
            }
        },
        /**
         * 修改机房名
         */
        editRoomName(row) {
            this.configInfo = {
                ...row,
                status: true
            };
        },
        // 清空页面数据
        clearPageData() {
            this.instances = [];
            this.tableData = [];
        },
        // 获取机房信息
        async getMachineRoomInfo() {
            const res = await getMachineRoomInfo({ productId: this.productInfo.id });
            if (res.code === '200') {
                this.tableData = res?.data || [];
            }
        }
    }
};
</script>

<style lang="less" scoped>
.tab-box {
    position: relative;
    width: 100%;
    height: calc(100% - 18px);

    .table-box {
        height: 100%;
    }
}
</style>
