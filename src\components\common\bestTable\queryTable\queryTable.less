@import "@/assets/css/tag.less";

.query-table {
    position: relative;
    width: 100%;
    height: 100%;
    overflow: hidden;

    .query-table-title {
        display: flex;
        justify-content: space-between;
        align-items: center;
        height: 40px;
        padding: 5px 15px;
        border-bottom: 1px solid #31364a;

        .table-title {
            color: var(--font-color);
            font-size: 14px;
        }

        .title-left {
            display: flex;
            align-items: center;
            padding-right: 10px;
            margin-right: auto;
            white-space: nowrap;         /* 防止换行 */
            overflow: hidden;            /* 隐藏溢出内容 */
            text-overflow: ellipsis;     /* 使用省略号表示溢出内容 */
        }

        .title-right {
            display: flex;
            align-items: center;
            white-space: nowrap;

            .separator {
                border-left: 1px solid #474e6f;
                height: 26px;
                margin-right: 8px;
            }
        }
    }

    .form-box {
        overflow-y: hidden;
        transition: height 0.5s;
    }

    .form-tags {
        width: 100%;
        height: 45px;
        padding: 15px 15px 5px;
        color: var(--font-color);
        align-items: center;

        .tag-title {
            width: 70px;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
        }

        .scroll-context {
            height: 30px;
            max-width: calc(100% - 120px);
            overflow-y: auto;
        }

        .h-tag {
            display: flex;
            margin: 4px 0 4px 4px;
            padding: 0 8px;
            background: #33394e;
            border-color: #33394e;
            color: var(--font-color);
            border-radius: 3px;
            opacity: 1;
            overflow: hidden;
            cursor: pointer;

            .h-icon {
                color: #9296a1;
            }
        }

        .clear-text {
            color: var(--link-color);

            &:hover {
                color: var(--font-color);
            }
        }
    }

    .h-page {
        height: 42px;
    }

    .table-box {
        padding: 0 15px;

        .a-table .h-table th {
            border-right-color: #2d334c;
        }

        // 自定义添加边框样式
        .a-table .h-table .muti-border-bottom {
            border-bottom-color: #444a60;
        }

        .a-table .h-table .muti-border-right {
            border-right: 1px solid #444a60;
        }
    }

    .h-tooltip-popper {
        .h-tooltip-inner {
            white-space: normal;
            overflow-wrap: break-word;
            max-height: 200px;
            overflow-y: auto;
        }
    }
}
