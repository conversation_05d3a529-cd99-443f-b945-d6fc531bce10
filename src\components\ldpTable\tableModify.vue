<template>
    <div class="modify-box">
        <!-- 筛选部分 -->
        <div class="tag-box">
            <a-title title="历史修改单">
                <slot>
                    <tag-list
                        ref='tag-list'
                        :queryList="modificationOrders"
                        :selectedId="modificationId"
                        :closable='false'
                        style="width: calc(100% - 110px); left: 110px;"
                        @on-click="handleTagClicK">
                    </tag-list>
                </slot>
            </a-title>
        </div>
        <div ref="tab-box" class="tab-box">
            <h-tabs v-model="tabName">
                <h-tab-pane label="修改预览" name="preview">
                    <a-title :title="boxInfo.tableName+ ' 表修改操作详情'">
                        <slot>
                            <span style="float: right; margin: -1px 10px;">
                                <a-button type="dark" @click="handleClear">清空操作</a-button>
                                <a-button type="dark" @click="handleSubmit">提交修改</a-button>
                            </span>
                        </slot>
                    </a-title>
                    <a-table
                        ref="table"
                        :tableData="previewTableData"
                        :columns="columns"
                        showTitle
                        :height="tableHeight"
                        :hasPage="true"
                        :total="total"
                        :loading="loading"
                        @query="handleQuery"  />
                </h-tab-pane>
                <h-tab-pane :label="tabLabel" :name="modificationId">
                    <history-modify-list ref='history-list'
                        :endpointInfo="endpointInfo"
                        :modificationId="modificationId"
                        @order-execute="viewOrderExecute">
                    </history-modify-list>
                </h-tab-pane>
            </h-tabs>
        </div>
        <!-- 查询结果 -->
        <update-box-modal v-if="updateModifyInfo.status" :modalInfo="updateModifyInfo" :boxInfo="boxInfo" @createPreview="createPreview"/>
        <submit-modify-modal v-if="submitModifyInfo.status" :modalInfo="submitModifyInfo" :detailInfo="detailInfo" @execute="executeModify" />
    </div>
</template>

<script>
import aTable from '@/components/common/table/aTable';
import aTitle from '@/components/common/title/aTitle';
import aButton from '@/components/common/button/aButton';
import tagList from '@/components/common/tag/tagList';
import _ from 'lodash';
import updateBoxModal from '@/components/ldpTable/modal/updateBoxModal.vue';
import submitModifyModal from '@/components/ldpTable/modal/submitModifyModal.vue';
import historyModifyList from '@/components/ldpTable/historyModifyList.vue';
import { clearPreview, getPreview, submitModificationOrder, executeModificationOrder, getModificationOrders, clearPreviewItem } from '@/api/httpApi';
export default {
    props: {
        databaseName: {
            type: String,
            default: ''
        },
        endpointInfo: {
            type: Object,
            default: () => {}
        },
        boxInfo: {
            tableName: {
                type: String,
                default: ''
            },
            expandAttributes: {
                type: Array,
                default: []
            },
            fields: {
                type: Array,
                default: []
            },
            describe: {
                type: String,
                default: ''
            }
        }
    },
    data() {
        return {
            tabName: 'preview',
            modificationId: '',
            tabLabel: '',
            tableHeight: 100,
            columns: [
                {
                    title: '数据记录号',
                    key: 'recordId',
                    width: 120,
                    render: (h, params) => {
                        return h(
                            'Poptip', {
                                props: {
                                    trigger: 'hover',
                                    content: `表序号：${params.row.tableId} 线程号：${params.row.threadId} 记录号：${params.row.recordId}`,
                                    placement: 'bottom-start'
                                },
                                style: {
                                    width: '100%'
                                }
                            }, [
                                h('div', [params.row.recordId])
                            ]
                        );
                    }
                }, {
                    title: '字段名',
                    key: 'fieldPath',
                    ellipsis: true
                }, {
                    title: '字段类型',
                    key: 'fieldType',
                    ellipsis: true
                }, {
                    title: '修改值',
                    key: 'updateValue',
                    ellipsis: true,
                    className: 'table-info-column'
                }, {
                    title: '原记录值',
                    key: 'currentValue',
                    ellipsis: true
                },  {
                    title: '操作',
                    key: 'action',
                    render: (h, params) => {
                        return h('Button',
                            {
                                props: {
                                    size: 'small',
                                    type: 'text'
                                },
                                on: {
                                    click: () => {
                                        this.$hMsgBoxSafe.confirm({
                                            title: `删除`,
                                            content: `您确定删除"${params.row.fieldName}"的修改操作吗？`,
                                            onOk: () => {
                                                this.handleDelete(params.row.id);
                                            }
                                        });
                                    }
                                }
                            },
                            '删除'
                        );
                    }
                }
            ],
            updateModifyInfo: {
                status: false
            },
            submitModifyInfo: {
                status: false
            },
            loading: false,
            detailInfo: {}, // 进度条页面数据
            modificationOrders: [], // 历史修改单
            previewTableData: [], // 修改预览表格数据
            total: 0,
            previewOrderId: '', // 预览表id
            affectedItemNum: 0, // 待修改数据条数
            affectedFieldNum: 0, // 待修改字段个数
            timer: null
        };
    },
    mounted() {
        window.addEventListener('resize', this.fetTableHeight);
    },
    beforeDestroy() {
        window.removeEventListener('resize', this.fetTableHeight);
    },
    methods: {
        async initData() {
            await this.getModificationOrders();
            await this.handleQuery();
            this.tabName = 'preview';
            this.modificationId = '';
            this.tabLabel = '';
            this.fetTableHeight();
        },
        resetHeight() {
            return new Promise((resolve, reject) => {
                this.tableHeight = 100;
                resolve();
            });
        },
        // 设置table高度
        fetTableHeight() {
            this.resetHeight().then(res => {
                const tableHeight = this.$refs['tab-box'].getBoundingClientRect().height - 160;
                this.tableHeight = tableHeight;
            });
        },
        async getModificationOrders() {
            this.modificationOrders = [];
            const params = {
                databaseName: this.databaseName,
                ...this.endpointInfo,
                tableName: this.boxInfo.tableName
            };
            const res = await getModificationOrders(params);
            if (!res.errcode){
                this.modificationOrders = [...res?.orders];
                this.$nextTick(() => {
                    this.modificationOrders?.length && this.$refs['tag-list'].scrollReset();
                });
            } else {
                this.$hMessage.error('修改单列表查询失败!');
            }
        },
        // 切换tag重置查询数据
        handleTagClicK(id) {
            this.modificationId = id;
            this.tabLabel = this.getQueryCondition(id)?.name || '';
            this.tabName = id;
            this.$refs['history-list'].handleQuery(id);
        },
        // 新建修改单
        addmodification() {
            this.updateModifyInfo.status = true;
        },
        // 清空操作
        async handleClear(){
            this.$hMsgBoxSafe.confirm({
                title: `您确定执行清空操作吗？`,
                onOk: async () => {
                    const param = {
                        instanceId: this.endpointInfo?.instanceId,
                        previewOrderId: this.previewOrderId
                    };
                    const res = await clearPreview(param);
                    if (!res.errcode){
                        this.previewTableData = [];
                        this.previewOrderId = '';
                        this.total = 0;
                        this.affectedItemNum = 0;
                        this.affectedFieldNum = 0;
                    } else {
                        this.$hMessage.error('清空操作失败!');
                    }
                }
            });
        },
        // 修改预览数据
        async handleQuery(){
            this.loading = true;
            const pageInfo = this.$refs['table']?.getPageData();
            const param = {
                databaseName: this.databaseName,
                ...this.endpointInfo,
                tableName: this.boxInfo.tableName,
                page: pageInfo.page,
                pageSize: pageInfo.pageSize
            };

            const res = await getPreview(param);
            this.loading = false;
            if (!res.errcode) {
                this.total = res?.totalCount || 0;
                this.previewTableData = res?.list || [];
                this.previewOrderId = res?.id || '';
                this.affectedItemNum = res?.affectedItemNum || 0;
                this.affectedFieldNum = res?.affectedFieldNum || 0;
            } else {
                this.total = 0;
                this.previewTableData = [];
                this.previewOrderId = '';
                this.$hMessage.error('修改预览查询失败!');
            }
        },
        // 预览表格--删除
        async handleDelete(id) {
            const param = {
                instanceId: this.endpointInfo?.instanceId,
                previewOrderItemId: id
            };
            const res = await clearPreviewItem(param);
            if (!res.errcode){
                this.$hMessage.success('预览修改单条目删除成功!');
                await this.handleQuery();
            } else {
                this.$hMessage.error('预览修改单条目删除失败!');
            }
        },
        // 提交修改
        handleSubmit() {
            if (this.affectedFieldNum === 0){
                this.$hMessage.info('预览修改单条目不存在!');
                return;
            }
            this.submitModifyInfo = {
                status: true,
                affectedItemNum: this.affectedItemNum,
                affectedFieldNum: this.affectedFieldNum,
                orderNums: this.modificationOrders.length + 1
            };
        },
        // 执行修改
        async executeModify(name, callback){
            const param = {
                instanceId: this.endpointInfo?.instanceId,
                previewOrderId: this.previewOrderId,
                modificationOrderName: name
            };
            try {
                const res = await submitModificationOrder(param);
                if (!res.errCode){
                    this.detailInfo = {};
                    await this.pollModify(res.id);
                } else {
                    this.$hMessage.error('执行修改失败!');
                    this.detailInfo = {};
                    callback(false);
                }
                await this.initData();
                callback(true);
            } catch (err){
                this.detailInfo = {};
                callback(false);
                this.submitModifyInfo.status = false;
                await this.initData();
            }
        },
        // 轮询接口
        async pollModify(id){
            this.timer && clearInterval(this.timer);
            try {
                this.timer = setInterval(async () => {
                    const param = {
                        instanceId: this.endpointInfo?.instanceId,
                        modificationOrderId: id
                    };
                    const res = await executeModificationOrder(param);
                    if (!res.errcode){
                        this.detailInfo = {
                            ...res
                        };
                        if (res.status === 'finished') {
                            this.timer && clearInterval(this.timer);
                            this.timer = null;
                            // 重加载历史修改单--用于改变滚动宽度
                            await this.getModificationOrders();
                            this.handleTagClicK(id);
                        }
                    } else {
                        this.submitModifyInfo.status = false;
                        this.$hMessage.error('执行修改失败!');
                        this.detailInfo = {};
                        this.timer && clearInterval(this.timer);
                        this.timer = null;
                    }
                }, 1000);

                this.$on('hook:beforeDestroy', () => {
                    this.timer && clearInterval(this.timer);
                    this.timer = null;
                    this.detailInfo = {};
                });
            } catch (err){
                this.submitModifyInfo.status = false;
                this.timer && clearInterval(this.timer);
                this.timer = null;
                this.detailInfo = {};
            }
        },
        // 创建修改预览
        createPreview(params) {
            this.$emit('createPreview', params);
        },
        // 根据id获取对应的查询条件
        getQueryCondition(id) {
            const data = _.find(this.modificationOrders, o => { return o?.id === id; });
            return data;
        },
        // 查看执行中的修改单
        viewOrderExecute(id) {
            this.submitModifyInfo = {
                status: true,
                modelType: 2,
                tabLabel: this.getQueryCondition(id)?.name || ''
            };
            this.detailInfo = {};
            this.pollModify(id);
        }
    },
    components: { tagList, aButton, aTitle, aTable, updateBoxModal, submitModifyModal, historyModifyList }
};
</script>

<style lang="less" scoped>
@import url("@/assets/css/tab.less");

.modify-box {
    width: 100%;
    height: 100%;
    overflow: hidden;

    /deep/ .h-table td.table-info-column {
        background-color: var(--primary-color);
        font-weight: 600;
    }

    .tag-box {
        position: relative;

        // /deep/ .monitor-list {
        //     width: calc(100% - 200px);
        // }

        .button {
            position: absolute;
            right: 10px;
            top: 6px;
        }
    }

    .tab-box {
        height: calc(100% - 50px);

        /deep/ .h-tabs-tabpane {
            overflow: auto;
        }
    }
}
</style>
