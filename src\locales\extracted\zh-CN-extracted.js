export default {
  "views\\index\\accordMonitor.vue": {
    "pleaseSelect": "请选择",
    "config": "配置",
    "texttexttexttexttextTotalcount": "总上场次数：{{ totalCount }}",
    "texttexttexttextdatatexttexttexttexttexttext": "主备核心数据同步差量告警：",
    "text": "秒",
    "datatexttextrefreshtexttext": "数据自动刷新频率："
  },
  "views\\index\\accordObservation.vue": {
    "texttextdatatexttext": "核心数据同步",
    "texttext": "未知"
  },
  "views\\index\\analyseConfig.vue": {
    "texttexttexttextAtitleTitle": ">\r\n        <!-- 头部标题 -->\r\n        <a-title :title=",
    "texttextinfotexttexttexttexttexttextimporttexttexttexttext": "场景信息为空,请新建或导入场景信息",
    "texttextidScenename": ",            // 场景ID\r\n            sceneName:",
    "texttextnameApidemoinfo": ",          // 场景Name\r\n            apiDemoInfo:",
    "exporttexttextdatasuccess": "导出场景数据成功!",
    "exporttexttextfailed": "导出场景失败!",
    "texttextscenename": "场景：${sceneName}",
    "exporttexttext": "导出场景",
    "importtexttext": "导入场景",
    "texttexttexttext": "新建场景"
  },
  "views\\index\\analyseData.vue": {
    "texttexttexttextHiconVif": ">测试实例\r\n                    <h-icon v-if=",
    "texttexttexttextdivDivClass": ">测试参数</div>\r\n                <div class=",
    "starttimedivDivClass": ">开始时间</div>\r\n                <div class=",
    "endtimedivDivClass": ">结束时间</div>\r\n                <div class=",
    "texttextstatusdivDivClass": ">报表状态</div>\r\n                <div class=",
    "texttextHiconVif": ">备注\r\n                    <h-icon v-if=",
    "texttexttexttextDivVif": "/>\r\n        <!-- 报表视图 -->\r\n        <div v-if=",
    "texttexttexttext": "测试参数",
    "texttextidLooptype": ",  // 场景id\r\n            loopType:",
    "texttextmin": "最小（min）",
    "texttexttextp50": "中位数（p50）",
    "texttextavg": "平均（avg）",
    "95texttextp95": "95分位（p95）",
    "99texttextp99": "99分位（p99）",
    "texttextmax": "最大（max）",
    "texttexttextstddeviation": "标准差（stdDeviation）",
    "texttextdetail": "指标详情",
    "texttexttexttexttexttexttext": "交易所申报编号",
    "texttexttexttexttext": "柜台委托号",
    "texttextdata": "时延数据",
    "text": "无",
    "texttext": "备注",
    "texttextdetailtexttextunitname": "报表详情（单位：${unitName}）",
    "texttextthisunitname": "单位（${this.unitName}）",
    "thisspanlatencydictdescspanTexttextdetail": "${this.spanLatencyDictDesc[span] || ''} 指标详情",
    "starttime": "开始时间",
    "endtime": "结束时间",
    "texttextstatus": "报表状态",
    "configtexttext": "配置报表"
  },
  "views\\index\\apmMonitorConfig.vue": {
    "texttexttexttexttexttexttextobservationtexttexttexttexttexttexttext": "当前节点不支持观测！请重新选择节点",
    "texttexttexttexttexttexttext": "已托管应用节点"
  },
  "views\\index\\appRunningState.vue": {
    "texttextstatustext": "应用状态墙",
    "texttextconfigpDivStyle": ">基础配置</p>\r\n            <div style=",
    "texttexttexttextconfigpDivClass": ">告警规则配置</p>\r\n\r\n            <div class=",
    "texttexthcolHcolSpan": ">序号</h-col>\r\n                <h-col span=",
    "starttimehcolHcolSpan": ">开始时间</h-col>\r\n                <h-col span=",
    "endtimehcolHcolSpan": ">结束时间</h-col>\r\n                <h-col span=",
    "savesuccess": "保存成功",
    "savefailed": "保存失败",
    "texttexttextsavetexttexttexttextconfigtext": "您确定保存当前所有配置吗？",
    "texttexttexttext": "部署视图",
    "texttextconfig": "基础配置",
    "texttexttexttexttexttext": "显示仲裁节点",
    "texttexttexttextconfig": "告警规则配置",
    "texttext": "关闭",
    "texttextstatus": "节点状态",
    "addtexttext": "新增规则",
    "saveconfig": "保存配置"
  },
  "views\\index\\brokerDataLimit.vue": {
    "texttexttexttextconfig": "限流名单配置",
    "texttexttexttextlist": "发布名单列表",
    "texttexttextconfig": "白名单配置",
    "textconfig": "组配置",
    "texttexttext": "发布中",
    "texttext": "发布",
    "texttexttexttexttextTabname": ", // 选中的产品\r\n            tabName:",
    "texttexttexttexttextlisttexttexttexttexttexttexttexttexttexttexttexttexttexttexttexttext": "确定要发布列表中已启用的黑名单、白名单、限流名单吗？",
    "cancel": "取消",
    "publishloadingTexttexttextTexttext": "{{publishLoading ? '发布中' : '发布'}}"
  },
  "views\\index\\coreFuncHandleObservation\\displaySettingDrawer.vue": {
    "texttexttextfunctiontextspanVif": ">\r\n                        已展示功能号：<span v-if=",
    "texttextfunctiontextquery": "输入功能号查询",
    "functiontextname": "功能号名称",
    "functiontext": "功能号",
    "texttexttexttext": "是否展示",
    "texttexttexttexttexttexttexttexttexttexttexttexttexttext30text": "为保证查看体验，建议选择不超过30个。",
    "queryfunctiontexttexttext": "查询功能号异常",
    "settingsuccess": "设置成功",
    "settingfailed": "设置失败",
    "settingtexttexttexttextfailed": "设置是否展示失败",
    "thisenabletotalcounttext": "${this.enableTotalCount}个",
    "texttexttextfunctiontext": "已展示功能号："
  },
  "views\\index\\coreFuncHandleObservation\\index.vue": {
    "texttextfunctiontexttexttext": "核心功能号处理",
    "pleaseselecttexttext": "请选择集群",
    "texttext": "集群",
    "texttexttexttexttps": "执行吞吐(tps)",
    "texttexttexttexttexttextns": "平均执行耗时(ns)",
    "errortexttexttext": "错误次数(次)",
    "errortext": "错误率(%)",
    "texttexttexttext": "队列积压",
    "querytexttextfailed": "查询分类失败",
    "querytexttextinfotexttext": "查询分片信息异常",
    "querytexttextinfo": "查询集群信息",
    "texttexttexttextloading": "分片、集群加载中",
    "functiontexttexttextsetting": "功能号显示设置"
  },
  "views\\index\\createRule\\createRule.vue": {
    "texttextmonitortexttext": "创建监控规则",
    "configtexttexttexttext": "配置规则内容",
    "texttexttexttexttext": "可引用变量",
    "sqltexttexttexttextmd": "SQL语法说明.md",
    "texttext": "测试",
    "pleaseselecttexttext": "请选择核心",
    "pleaseinputtexttexttext": "请输入指标名",
    "texttextsql": "执行SQL",
    "texttexttexttext": "获取变量",
    "texttextname": "规则名称",
    "pleaseinputtexttexttexttexttexttext20texttext": "请输入规则名（不超过20字符）",
    "pleaseinputtexttexttexttexttexttexttext200texttext": "请输入规则说明（不超过200字符）",
    "texttexttextabuttonSpanVif": ">上一步</a-button>\r\n            <span v-if=",
    "texttextmonitortexttextabuttonAbuttonType": ">创建监控规则</a-button>\r\n            <a-button type=",
    "texttext1": "指标1",
    "texttext2": "指标2",
    "texttexttexttexttexttext": "正式执行内容",
    "texttexttexttextsuccess": "获取变量成功",
    "texttexttexttextfailedtexttexttext": "变量获取失败，请重试！",
    "texttexttexttextfailed": "规则创建失败",
    "texttextfailedtexttexttext": "测试失败，请重试",
    "texttextsqlfailed": "测试sql失败",
    "texttextsuccess": "创建成功",
    "querytexttextfailed": "查询核心失败",
    "texttexttexttexttexttext500texttexttext": "最大输入长度500个字符",
    "texttexttexttexttexttextselecttexttext": "只允许写一条select语句",
    "texttexttexttexttextoperationtexttexttextsavedatatexttexttexttexttexttexttexttext": "离开后当前操作将不会保存，数据会丢失，请谨慎操作！",
    "texttextmonitortexttexttexttexttextsql": "创建监控规则-自定义SQL",
    "texttexttextsql": "预执行SQL",
    "texttexttextsqltexttexttexttexttexttexttexttexttexttexttexttexttexttexttexttexttexttextsqltexttexttexttexttext": "“预执行SQL”执行后可得到对应变量，变量可在下方“执行SQL”被引用。点击",
    "texttextsqltexttexttexttext": "下载SQL语法指南",
    "texttexttexttexttexttexttexttexttexttexttexttexttexttexttexttexttexttexttexttext": "点击“获取变量”，生成对应可引用变量，在下方显示",
    "sqltexttexttexttext": "SQL语法指南",
    "texttexttexttexttexttexttexttexttexttexttexttexttexttexttexttexttexttexttexttexttexttexttext": "统一策略：取结果集的第一行第一列，结果集必须是数字",
    "sqltexttext": "SQL结果",
    "texttexttexttexttexttexttexttexttexttextdatatexttexttexttexttexttexttexttextsettingtexttexttexttexttexttexttext": "针对上一步获取的比对数据所形成的告警指标，设置对应的告警阈值。",
    "texttexttext": "上一步",
    "texttexttextconfigtexttexttexttext": "下一步：配置规则内容"
  },
  "views\\index\\dataSecondAppearance.vue": {
    "pagenameDetailDatatexttexttexttext": "pageName === 'detail' ? '数据二次上场' : ''",
    "datatexttexttexttext": "数据二次上场",
    "texttexttexttexttextPagename": ", // 选中的产品\r\n            pageName:",
    "tabtexttexttexttextTitletext": ", // tab默认选择\r\n            titleText:",
    "texttexttexttexttexttexttexttext": "创建二次上场任务"
  },
  "views\\index\\emergencyManagementConfig\\addModal.vue": {
    "addtexttext": "添加路由",
    "addtexttextfailed": "添加路由失败",
    "texttext": "确定"
  },
  "views\\index\\emergencyManagementConfig\\helpModal.vue": {
    "configtexttext": "配置说明"
  },
  "views\\index\\emergencyManagementConfig\\index.vue": {
    "configtexttextedit": "配置应急修改",
    "texttexttexttextname": "输入节点名称",
    "texttexttextconfigtexttextfailed": "初始化配置应急失败"
  },
  "views\\index\\emergencyManagementConfig\\routeConfig.vue": {
    "texttextconfig": "更新配置",
    "texttexttexttextVersion": "当前版本：{{ version || \"-\" }}",
    "texttextfunctiontext": "输入功能号",
    "editconfig": "修改配置",
    "configjsontexttext": "配置json预览",
    "texttexttexttexttext": "目标系统号",
    "texttext": "节点",
    "texttexttextid": "目标端ID",
    "operation": "操作",
    "delete": "删除",
    "texttextfailed": "更新失败",
    "texttextsuccess": "更新成功",
    "texttexttexttextconfigfailed": "获取路由配置失败",
    "texttexttexttexttextconfigtext": "确定要更新配置吗？",
    "texttextconfigtexttexttexttexttexttexttexttexttexttexttexttext": "参数配置更新后实时生效，重启后失效。",
    "texttexttextdeletetexttexttext": "确定要删除该路由？",
    "texttextconfigtextversioninfotexttextadddeleteedittexttext": "路由配置无version信息，不可添加、删除、修改配置。"
  },
  "views\\index\\emergencyManagementConfig\\routeInfoForm.vue": {
    "pleaseselecttextedittexttexttext": "请选择要修改的路由",
    "pleaseInput": "请输入",
    "texttexttexttexttextidfailed": "获取目标端id失败",
    "texttexttexttexttexttexttexttexttexttextfunctiontexttexttexttexttexttexttexttexttext": "支持*、?、数字(不为负值)。多个功能号使用英文分号分隔",
    "texttexttexttexttexttexttexttexttexttext255texttextfunctiontexttexttexttexttexttexttexttexttext": "支持*、数字(不为负值、最大255)。多个功能号使用英文分号分隔",
    "texttexttexttexttexttexttexttexttexttext65535": "支持*、数字(不为负值、最大65535)"
  },
  "views\\index\\latencyTrendAnalysis.vue": {
    "texttexttexttexttexttexttexttext": "应用逐笔时延统计",
    "texttexttexttext": "委托笔数:",
    "texttextstatus": "链路状态",
    "noData": "暂无数据",
    "90texttextp90": "90分位（p90）",
    "texttexttexttexttexttext": "查看时延分布",
    "pleaseselectquerytexttext": "请选择查询分页",
    "texttexttext": "最小值",
    "90texttext": "90分位",
    "95texttext": "95分位",
    "99texttext": "99分位",
    "xtexttextnameHtmlstr": "';// x轴的名称\r\n\r\n                htmlStr += '",
    "texttext": "查看",
    "timetexttextstimeEtimeTexttexttexttexttextcount0text": "时间范围：${sTime} - ${eTime} 总记录笔数：${count || 0}笔",
    "texttexttexttexttexttextconfigtexttexttexttexttexttexttexttexttexttexttexttexttexttexttexttexttext": "当前产品尚未配置模型，请前往“产品服务配置”进行模型配置",
    "texttextconfig": "前往配置"
  },
  "views\\index\\ldpDataObservation.vue": {
    "texttexttexttext": "盘后定价",
    "texttexttexttexttexttext": "午盘竞价交易",
    "texttext": "盘休",
    "texttexttexttexttexttexttexttexttextobservationtexttexttexttexttexttexttext": "当前应用节点不支持观测！请重新选择节点",
    "texttexttextCurrenttime": "今天是: {{ currentTime }}"
  },
  "views\\index\\ldpLinkConfig.vue": {
    "texttexttexttextconfigmanage": "产品节点配置管理",
    "deletesuccess": "删除成功",
    "deletefailed": "删除失败",
    "texttexttext": ") {\r\n                // 如果是",
    "texttexttextdeletetexttextthisproductinfoproductnametexttexttexttexttext": "您确定删除名为\"${this.productInfo.productName}\"产品节点吗？",
    "texttexttexttexttexttext": "对接产品节点",
    "texttexttexttexttexttexttext": "已注册产品节点",
    "deletetexttexttexttext": "删除产品节点",
    "texttexttexttextinfo": "同步节点信息"
  },
  "views\\index\\ldpLogCenter.vue": {
    "texttexterrortexttexttexttext": "回库错误重试运维",
    "texttexterrortexttext": "回库错误日志",
    "texttextCurrentdate": "日期：{{ currentDate }}",
    "texttextquerytexttexttime": "日志查询超时时间："
  },
  "views\\index\\ldpMonitor\\clusterMonitor.vue": {
    "texttextstatus": "应用状态"
  },
  "views\\index\\ldpMonitor\\index.vue": {
    "texttext": "盘后",
    "text": "和",
    "texttexttexttexttexttexttextTexttexttexttexttexttexttexttext": "之间跳转的时候，\r\n    // 由于会渲染同样的"
  },
  "views\\index\\ldpMonitor\\ldpAppMonitor.vue": {
    "texttexttexttext": "核心性能"
  },
  "views\\index\\ldpTable.vue": {
    "querytexttexttext": "查询内存表",
    "dataquery": "数据查询",
    "dataedit": "数据修改",
    "texttexttexttexttext": "内存表结构",
    "texttexttexttexttexttexttexttexttexttexttexttexttexttexttext": "请从左侧菜单选择内存表进行查看！",
    "pleaseselecttexttexttexttexttexttexttext": "请选择并连接应用节点！"
  },
  "views\\index\\locateConfig.vue": {
    "locateconfigtexttexttexttexttext": "Locate配置一致性校验",
    "texttextconfigmanage": "节点配置管理",
    "texttextconfigtexttext": "节点配置校验"
  },
  "views\\index\\managementQuery.vue": {
    "instancelistlengthPleaseselecttexttextTexttexttexttexttexttexttexttext": "instanceList.length ? '请选择节点': '当前暂无活跃节点'",
    "texttexttexttexttextquery": "输入节点名查询",
    "searchmanagefunction": "搜索管理功能",
    "texttext": "下载",
    "pleaseselectmanagefunctiontexttexttexttexttexttext": "请选择管理功能手动发起请求",
    "pleaseselectmanagefunction": "请选择管理功能",
    "pleaseselecttexttext": "请选择节点",
    "texttexttexttexttexttexttexttext": "当前暂无活跃节点",
    "texttextdata": "其他数据",
    "jsontexttexttexttexttexttexttextjavascripttexttexttexttextImport": ";\r\n// JSON代码高亮需要由JavaScript插件支持\r\nimport",
    "managefunctiontexttexttexttextApmtime": ", // 管理功能执行耗时\r\n            apmTime:",
    "texttexttexttexttextjsonpathconfigfailed": "初始化默认jsonPath配置失败:",
    "texttextsavetexttexttexttextfunctiontexttexttexttexttexttexttexttexttexttexttexttextquerytexttext": "如需保存当前选中功能号输入参数，请手动触发一次查询请求！",
    "texttextjsonpathconfigfailed": "获取jsonPath配置失败:",
    "functiontexttexttexttextdata": "功能号无返回数据！",
    "export": "导出",
    "texttextexport": "快捷导出",
    "texttexttexttextexporttexttexttexttexttexttexttexttextmanagefunctiondata": "支持批量导出当前产品下的所有管理功能数据",
    "texttexttexttextexporttexttexttexttextmanagetexttexttexttexttextgetfuncdetailinfotexttextfunction": "点击一键导出所选产品管理所有核心的「GetFuncDetailInfo」管理功能",
    "childchinesenameTexttext": "{{  child.chineseName || '暂无'}}",
    "textquerytextmanagefunction": "未查询到管理功能",
    "texttexttexttexttexttext": "应用节点耗时：",
    "texttexttexttext": "关闭全部",
    "apmtexttext": "APM耗时：",
    "configtable": "配置表格"
  },
  "views\\index\\marketAllLink.vue": {
    "texttexttexttexttexttexttexttexttexttexttext": "行情全链路应用节点关系",
    "texttexttexttexttexttexttexttexttextmonitor": "终端客户全链路质量监控",
    "texttextdatatexttexttexttextmonitor": "行情数据时延趋势监控",
    "nsqtexttext": "NSQ链路",
    "fpgatexttext": "FPGA链路",
    "fpgatexttexttext": "FPGA全链路",
    "texttexttexttextstatus": "应用节点状态",
    "texttexttexttexttext": "时延正常率",
    "nsqtexttexttext": "NSQ全链路"
  },
  "views\\index\\marketMonitor.vue": {
    "texttexttexttexttexttexttexttexttexttexttextinfo": "当前实例无采集时延走势信息",
    "texttexttexttexttexttexttexttexttexttexttext": "该模型文件暂无拓扑结构",
    "texttexttexttexttext": "最近五分钟",
    "texttexttexttexttexttext": "最近三十分钟",
    "texttexttexttexts": "逐笔成交(μs)",
    "getproducttypeproductinfoproducttypeTexttextmonitor": "{{ $getProductType(productInfo.productType) }}时延监控",
    "texttexttexttext": "显示拓扑"
  },
  "views\\index\\marketNodeDelayList.vue": {
    "texttexttexttexttexttexttexttexttexttexttexttext": "行情产品节点穿透时延分析",
    "texttextquery": "汇总查询",
    "texttexttext": "交易日",
    "texttextmin": "最小(min)",
    "1texttexttextp1": "1分位数(p1)",
    "texttexttextp50": "中位数(p50)",
    "texttexttextavg": "平均数(avg)",
    "95texttexttextp95": "95分位数(p95)",
    "99texttexttextp99": "99分位数(p99)",
    "texttextmax": "最大(max)",
    "texttexttexttext": "交易日期",
    "texttexttime": "交易时间",
    "queryfailed": "查询失败!"
  },
  "views\\index\\marketPenetrateList.vue": {
    "texttexttext": "席位号",
    "texttexttexttexttext": "上海交易所",
    "texttexttexttexttexttext": "行情-交易时延",
    "tgwtexttexttexttext": "TGW下行时延",
    "tgwtexttext": "TGW时延",
    "texttexttexttexttexttexttexttext": "交易-收到行情时延",
    "texttexttexttexttexttexttext": "防火墙下行时延",
    "texttexttexttext": "业务字段",
    "text": "天",
    "detailquery": "详情查询",
    "texttext": "日期"
  },
  "views\\index\\marketTimeDelay.vue": {
    "texttexttime": "选择时间",
    "texttexttexttexttext": "选择交易所",
    "texttextdatatexttext": "选择数据类型",
    "texttext": "平均",
    "texttexttexttext": "每秒最小",
    "texttexttexttexttexttexttexttexttexttexttexts": "本地全系统时延走势分析(μs)",
    "texttexttexttexttexttexttexttexttexttextms": "交易所到消费端总时延(ms)",
    "querytextdatatexttexttext": "查询的数据不存在",
    "confirm": "确认"
  },
  "views\\index\\mcDataObservation.vue": {
    "texttexttextmc30texttext": "已托管MC3.0集群"
  },
  "views\\index\\mcDeploy.vue": {
    "mc30configmanage": "MC3.0配置管理",
    "texttext": "主题"
  },
  "views\\index\\mdbDataExport\\detailDrawer.vue": {
    "detail": "详情",
    "texttexttext": "服务器",
    "texttext": "表名",
    "texttexttexttext": "远程路径",
    "exportstatustexttexttext": "导出状态和结果",
    "exporttext": "导出中",
    "exportsuccess": "导出成功",
    "exportfailed": "导出失败",
    "textexport": "待导出",
    "texttexttexttextdetailfailed": "查看历史详情失败",
    "errorinfo": "错误信息"
  },
  "views\\index\\mdbDataExport\\exportHistory.vue": {
    "texttextid": "任务ID",
    "exporttime": "导出时间",
    "exportstatus": "导出状态",
    "exporttexttexttexttexttext": "导出内存表数量",
    "deletetexttexttexttexttextdata": "删除远程服务器数据",
    "deleteapmtexttexttextdata": "删除APM服务器数据",
    "textdelete": "已删除",
    "deletetexttexttexttextfailed": "删除历史任务失败,",
    "texttextsuccess": "下载成功!",
    "texttextfailed": "下载失败",
    "texttexttexttexttextfailed": "下载单张表失败,",
    "deleteexporttexttexttexttexttexttexttexttexttexttexttexttexttexttexttexttexttexttexttexttexttexttexttextdatatexttexttexttexttexttexttexttexttexttext": "删除导出任务的同时，可同时删除该任务导出在服务器的内存表数据。请勾选需要删除的内容。",
    "deleteexporttexttexttexttexttexttexttexttexttexttexttexttexttexttexttexttexttexttexttexttextdatatexttexttexttexttexttexttexttexttexttext": "删除导出任务，可同时删除该任务导出在服务器的内存表数据。请勾选需要删除的内容。",
    "texttexttexttext": "查看原因"
  },
  "views\\index\\mdbDataExport\\exportTable.vue": {
    "texttexttexttextexportspanSpanClass": ">最近一次导出</span>\r\n              <span class=",
    "texttexttexttextexport": "最近一次导出",
    "nbspnbspfailedtexttext": "&nbsp;&nbsp;失败原因",
    "texttexttexttext": "创建任务"
  },
  "views\\index\\mdbDataExport\\index.vue": {
    "mdbdataexport": "MDB数据导出",
    "exporttexttext": "导出历史",
    "exportmdbdata": "导出MDB数据"
  },
  "views\\index\\mdbDataObservation.vue": {},
  "views\\index\\mdbPrivilegeManage.vue": {
    "mdbtexttextmanage": "MDB权限管理",
    "texttexttexttexttexttexttexttexttexttextmdb": "将本地用户权限下发至MDB",
    "texttexttexteditNbspnbspnbspnbsphswitchVmodel": ">权限可编辑  &nbsp;&nbsp;&nbsp;&nbsp;<h-switch v-model=",
    "text": "与",
    "texttexttexttexttexttexttexttext": "）。若当前权限内容为",
    "texttexttexttexttexttextedittexttexttexttexttexttexttexttext": "权限内容。如需编辑，请至当前页面顶部",
    "texttexttexttext": "同步权限",
    "texttexttexttexthispermissionsinfoheadertime": "}}(最新下发：{{hisPermissionsInfo.headerTime ||",
    "texttextfailed": "下发失败",
    "texttextsuccess": "下发成功",
    "texttextmanage": "角色管理",
    "mdbtexttexttexttexttexttexttexttexttext": "MDB权限与本地权限一致。",
    "mdbtexttexttexttexttexttexttexttexttexttext": "MDB权限与本地权限不一致。",
    "texttexttexttexttexttextmdbtexttexttexttexttexttexttext": "暂时无法获取MDB权限，请稍后查看。",
    "texttexttexttextsuccess": "权限下发成功！",
    "texttexttexttexttexttext": "权限下发异常！",
    "texttexttexttextfailed": "权限下发失败！",
    "exportsuccess": "导出成功!",
    "importtexttextsuccess": "导入权限成功",
    "importtexttextfailed": "导入权限失败",
    "texttexttexttexttexttexttexttextfailedtexttexttexttexttexttexttexttexttexttexttexttexttexttexttext": "用户权限部分下发失败，请稍后重新尝试。本次下发详细如下：",
    "texttexttexttexttexttextfailedtexttexttexttexttexttexttexttexttexttexttexttexttexttexttext": "用户权限下发失败，请稍后重新尝试。本次下发详细如下：",
    "texttexttexteditstatuseditsuccess": "权限可编辑状态修改成功!",
    "texttextoperation": "权限操作",
    "texttextmdbtexttext": "查看MDB权限",
    "importtexttext": "导入权限",
    "exporttexttext": "导出权限",
    "texttexttexteditNbspnbspnbspnbsp": "权限可编辑  &nbsp;&nbsp;&nbsp;&nbsp;"
  },
  "views\\index\\monitor\\businessMonitor.vue": {
    "texttexttexttexttexttext": "展示链路拓扑",
    "texttexttexttext": "时延趋势",
    "10text": "10秒",
    "30text": "30秒",
    "texttexttexttextmonitortexttextdatafailed": "获取图表监控时延数据失败！",
    "texttexttexttextdatafailed": "获取聚合数据失败！",
    "t3texttexttext": "T3监视器"
  },
  "views\\index\\monitor\\index.vue": {},
  "views\\index\\networkSendAndRecevied.vue": {
    "texttexttexttext": "发包用例"
  },
  "views\\index\\noticeManagerList.vue": {
    "texttexttexttextmanage": "告警人员管理",
    "texttexttexttext": "告警人员",
    "texttexttextname": "干系人名称",
    "texttexttext": "手机号",
    "texttext": "邮箱",
    "texttexttexttexttext": "通知干系人",
    "edit": "修改",
    "texttexttextdeletetexttextparamaddresseenametexttexttexttextinfo": "您确定删除名为\"${param.addresseeName}\"的干系人信息？",
    "add": "添加"
  },
  "views\\index\\productDataStorage.vue": {
    "monitordatamanage": "监控数据管理",
    "texttexttext": "索引名",
    "texttexttexttext": "定时清理",
    "texttexttexttexttexttext": "委托交易时延",
    "texttextquerytexttextdata": "委托查询时延数据",
    "monitortexttext": "监控指标",
    "texttexttexttexttexttexttextparamsrowindextexttexttextdatatext": "您确定清空名为\"${params.row.index}\"的索引数据吗？",
    "texttexttextdeletetexttextparamsrowindextexttexttextdatatext": "您确定删除名为\"${params.row.index}\"的索引数据吗？",
    "texttexttextelasticsearchEsurl": "已连接ElasticSearch: {{ esUrl }}",
    "texttexttexttexttext": "按条件清理"
  },
  "views\\index\\productServiceConfig\\productServiceList.vue": {
    "texttexttexttextconfig": "产品服务配置",
    "texttexttexttextlistdivHmenuVif": ">产品服务列表</div>\r\n                    <h-menu v-if=",
    "texttexttexttextlist": "产品服务列表"
  },
  "views\\index\\productTimeDetail.vue": {
    "texttexttexttexttexttexttexttexttexttext": "交易节点委托时延明细",
    "texttexttextabuttonAbuttonType": ">上一条</a-button>\r\n        <a-button type=",
    "texttexttexttext": "时延筛选",
    "querytexttext": "查询结果",
    "querytime": "查询时间",
    "texttexttime": "委托时间",
    "texttexttexttextqueryfailed": "获取快捷查询失败!",
    "savetexttextqueryfailed": "保存快捷查询失败!",
    "deletetexttextqueryfailed": "删除快捷查询失败!",
    "texttextdatatexttexttexttexttext": "当前数据为最后一条",
    "texttexttextdeleteNamequerytexttexttext": "您确定删除 '${name}'查询标签吗？",
    "texttextdataentrustnotexttexttexttexttext": "委托${data.entrustNo}全链路拓扑",
    "texttexttext": "下一条"
  },
  "views\\index\\productTimeSummary.vue": {
    "texttexttexttexttexttextquery": "链路时延汇总查询",
    "texttexttext": "平均数",
    "text": "月",
    "texttexttexttext": "总委托数"
  },
  "views\\index\\rcmBacklogMonitor.vue": {
    "texttexttexttexttextmonitor": "上下文积压监控",
    "rcmconfiglist": "RCM配置列表",
    "rcmtexttextidProductinstno": ", // rcm文档id\r\n            productInstNo:"
  },
  "views\\index\\rcmDeploy.vue": {
    "rcmconfigmanage": "RCM配置管理",
    "deleteconfig": "删除配置",
    "texttexttexttexttexttextconfigtexttexttexttexttexttexttexttexttip": "请查看待发布配置和已发布配置告警提示",
    "texttexttextdeletetexttextthisinstancedatanametexttexttexttexttext": "您确定删除名为\"${this.instanceData.name}\"的节点实例？"
  },
  "views\\index\\rcmObservation.vue": {
    "texttexttexttexttextobservation": "上下文运行观测",
    "searchtexttexttext": "搜索上下文",
    "texttexttexttexttexttexttext": "请先去建立产品",
    "texttextsuccess": "复制成功",
    "texttext": "复制"
  },
  "views\\index\\secondAppearance\\index.vue": {
    "texttextDivClass": ">\r\n        <!-- 头部 -->\r\n        <div class=",
    "textsqltexttext": "按SQL条件",
    "texttexttexttexttext": "按资金账户",
    "texttexttexttext": "上场记录"
  },
  "views\\index\\secondAppearance\\publishStatusDetail.vue": {
    "texttexttexttexttexttexttexttexttextspanSpanClass": ">当前无上场任务执行</span>\r\n            <span class=",
    "detailhbuttonDivSlot": ">详情</h-button>\r\n            <div slot=",
    "texttextinfodivDivClass": ">上场信息</div>\r\n                    <div class=",
    "taskdetailtitletexttext": "${taskDetail.title}上场",
    "taskdetailtitleTexttexttexttexttext": "{{taskDetail.title || '-'}}执行上场中",
    "texttexttexttexttexttexttexttexttext": "当前无上场任务执行",
    "texttextinfo": "上场信息",
    "starttime": "开始时间:",
    "endtime": "结束时间:",
    "texttexttexttexttexttext": "需上场表数量:",
    "texttextsuccesstexttexttext": "上场成功表数量:",
    "texttextfailedtexttexttext": "上场失败表数量:"
  },
  "views\\index\\smsList.vue": {
    "texttexttexttextmanage": "告警通知管理",
    "texttexttexttexttexttext": "通知外发历史",
    "texttextstatus": "匹配状态",
    "texttexttexttext": "通知地址",
    "texttexttexttexttime": "告警通知时间",
    "text": "人",
    "texttext": "短信",
    "success": "成功",
    "failed": "失败",
    "texttexttext": "干系人",
    "texttexttime": "发送时间",
    "pleaseselecttexttexttexttext": "请选择通知方式",
    "pleaseselecttexttextstatus": "请选择通知状态",
    "texttexttexttexttext": "通知提供方",
    "pleaseinputtexttexttexttext": "请输入通知地址",
    "texttextsuccess": "发送成功",
    "texttextfailed": "发送失败",
    "managetexttexttexttext": "管理通知模版"
  },
  "views\\index\\sqlCores.vue": {
    "texttextspanSpanSlot": ">超时:</span>\r\n                        <span slot=",
    "texttexttexttext": "帮助手册",
    "texttexttexttexttexttexttext": "标签名不得为空",
    "texttexttexttexttexttexttexttext": "当前标签名已存在",
    "pleaseinputtexttexttext": "请输入标签名",
    "edittexttexttexttexttext1mtexttexttexttexttext": "编辑器内容超过1M不支持暂存!",
    "pleaseselecttexttexttexttexttexttext": "请选择要运行的节点！",
    "sqltexttexttexttexttexttext": "SQL输入不能为空！",
    "pleaseselecttexttexttexttextsqltexttexttexttexttexttext1text": "请选择要执行的sql语句，不得超过1条！",
    "texttexttimetexttextconfigtexttexttext0": "超时时间不能配置为空或0！",
    "texttexttimetexttexttexttext10texttext": "超时时间不得超过10分钟！",
    "texttextdatatexttexttexttexttexttexttexttext": "该页数据不存在，请重新执行",
    "texttext": "超时:",
    "texttextsql": "历史SQL"
  },
  "views\\index\\sqlTable.vue": {
    "texttexttexttexttexttext": "关联对象身份",
    "texttexttext": ">仅显示",
    "texttext": "终止",
    "texttexttexttext": "关联索引",
    "texttexttexttexttexttexttexttexttexttexttexttexttexttexttexttexttexttexttexttexttexttext": "当前服务在此路由策略下无可执行节点，请重新选择",
    "texttexttexttexttexttexttexttexttexttexttexttexttext": "此分片表在以下主节点中存在",
    "texttexttexttexttexttexttexttexttexttexttexttexttexttexttexttexttexttexttexttexttexttexttext": "当前模式下无可执行的核心节点，请调整路由策略重试",
    "texttexttexttextdatatexttexttextsqltexttexttexttexttexttexttexttexttexttexttexttext": "仅表示行数据对应的SQL执行的节点，与执行结果无关",
    "sqltexttexttexttext": "SQL执行异常",
    "sqlparamsqlTexttexttexttexttexttexttexttexttexttexttexttexttexttextsqltext": "'SQL：${param.sql} 执行异常，您确定要继续执行剩余SQL吗？",
    "texttexttexttexttexttexttexttexttexttexttext": "仅显示\"不支持主备同步\"表",
    "importsql": "导入SQL"
  },
  "views\\index\\threadInfoOverview.vue": {
    "texttexttexttextconfig": "应用检查配置"
  },
  "views\\index\\topoMonitor.vue": {
    "texttexttexttexttexttexttexttexttexttexttext": "当前产品不支持拓扑展示",
    "texttexttexttexttexttext": "应用拓扑结构",
    "rcmtexttexttexttext": "RCM拓扑结构"
  },
  "views\\index\\transaction.vue": {
    "texttexttexttext": "自由分析",
    "xtexttextnameHtmlstr": "';// x轴的名称\r\n\r\n                    htmlStr += '",
    "querytexttexttexttexttext": "查询订单不存在",
    "texttexttexttexttexttextdatafailed": "获取同比分析数据失败！",
    "texttexttexttexttexttext": "创建分析报表",
    "texttextresdatainstancenametexttextsuccess": "报表${res.data.instanceName}创建成功！",
    "query": "查询"
  },
  "views\\index\\tripartiteServiceConfig\\tripartiteServiceList.vue": {
    "texttexttexttexttexttextmanage": "三方服务集成管理",
    "texttexttexttextlistdivHmenuVif": ">三方服务列表</div>\r\n                    <h-menu v-if=",
    "texttexttexttextlist": "三方服务列表"
  },
  "views\\index\\ustTableVerification.vue": {
    "datatexttext": "数据校验",
    "texttextdatatexttexttexttext": "创建数据校验任务"
  },
  "views\\testLXY.vue": {
    "texttextinfotext": "用户信息表",
    "texttextdetailtext": "订单详情表"
  },
  "components\\accordObservation\\dataAccordDrawer.vue": {
    "texttexttexttexttexttexttexttext": "与主核心事务差量",
    "texttexttexttexttexttext": "回库事务差量",
    "texttext": "角色",
    "texttexttext": "事务号",
    "texttexttexttexttext": "事务量差(个)",
    "datatitleTexttexttexttext": "{{ dataTitle }}变化趋势"
  },
  "components\\accordObservation\\dataAccordObservation.vue": {
    "texttexttexttexttexttexttexttexttext": "备核心事务最大差量",
    "texttexttexttexttexttexttexttext": "回库事务最大差量",
    "texttextIndicatorshardingno": "分片 ${indicator.shardingNo || '-'}"
  },
  "components\\analyse\\addCaseModal.vue": {
    "texttexttexttexttexttext": "新建测试用例",
    "texttextname": "用例名称",
    "pleaseinputtexttexttexttextname": "请输入测试用例名称",
    "cancelabuttonAbuttonType": ">取消</a-button>\r\n                <a-button type=",
    "texttexttexttexttexttexttexttexttext20": "字符长度数不得超过20！",
    "texttexttexttextsuccess": "用例创建成功!",
    "texttexttexttextfailed": "用例创建失败!"
  },
  "components\\analyse\\addSceneModal.vue": {
    "texttextname": "场景名称",
    "pleaseinputtexttextname": "请输入场景名称",
    "texttexttexttextsuccess": "场景创建成功!",
    "texttexttexttextfailed": "场景创建失败!",
    "texttexttexttexttexttexttexttextexportsavetexttexttexttexttexttexttexttexttexttexttexttexttexttexttextinfo": "请确保当前场景已导出保存，新建的场景将覆盖当前使用的场景信息！"
  },
  "components\\analyse\\caseAccountModal.vue": {
    "texttextconfig": "账号配置",
    "pleaseinputtexttexttexttext": "请输入起始账号",
    "texttext": "账号...",
    "textconfirmtexttexttexttexttexttexttext": "请确认输入内容为数字！",
    "texttexttexttexttexttextsuccess": "用例账号更新成功!",
    "texttexttexttexttexttext": "用例账号更新!",
    "import": "导入",
    "texttexttexttext": "股东账号",
    "texttexttext": "深交所"
  },
  "components\\analyse\\caseConfigModal.vue": {
    "configtexttext": "配置用例",
    "pleaseinputtexttextconfig": "请输入用例配置...",
    "texttextconfigsavefailed": "用例配置保存失败!"
  },
  "components\\analyse\\caseRemarkModal.vue": {
    "texttexttexttexttexttext": "更新实例备注",
    "texttext": "内容",
    "pleaseinputtexttextname": "请输入标题名称",
    "texttexttexttextsuccess": "备注更新成功!",
    "texttexttexttextfailed": "备注更新失败!"
  },
  "components\\analyse\\eventListModal.vue": {
    "texttextlist": "事件列表",
    "texttextdetail": "事件详情",
    "tiptexttext": "提示内容",
    "texttextid": "复制id",
    "texttexttexttexttexttexttexttexttext": "该浏览器不支持复制",
    "copiedTexttextsuccessTexttextid": "{{ copied ? '复制成功' : '复制id' }}"
  },
  "components\\analyse\\importAccountModal.vue": {
    "importtexttextinfo": "导入账号信息",
    "texttexttexttexttexttexttexttexttexttexttexttexttext": "点击或将文件拖拽到这里上传"
  },
  "components\\analyse\\importFileModal.vue": {
    "importtexttextconfigtexttext": "导入场景配置文件",
    "texttexttexttexttexttexttexttextexportsavetextimporttexttexttexttexttexttexttexttexttexttexttexttexttextinfo": "请确保当前场景已导出保存，新导入的场景将覆盖当前使用的场景信息！"
  },
  "components\\analyse\\reportConfigModal.vue": {
    "texttexttexttext": "度量单位",
    "texttextns": "纳秒（ns）",
    "texttexts": "微秒（μs）",
    "texttextms": "毫秒（ms）"
  },
  "components\\analyse\\reportContrastModal.vue": {
    "texttexttexttext": "报表对比",
    "texttexttexttexttexttext": "选择对比实例"
  },
  "components\\analyse\\secInstanceListModal.vue": {
    "texttexttexttexttexttext": "选择测试实例",
    "searchtexttexttexttext": "搜索测试报告...",
    "texttextname": "实例名称",
    "texttexttexttext": "测试日期",
    "pleaseselecttexttext": "请选择实例！"
  },
  "components\\analyse\\updateCaseNameModal.vue": {
    "texttexttexttexttexttextname": "更新测试实例名称",
    "pleaseinputtexttexttexttextname": "请输入测试实例名称",
    "texttexttexttexttexttexttexttexttext30": "字符长度数不得超过30！",
    "texttextnametexttextsuccess": "实例名称更新成功!",
    "texttexttexttextfailed": "实例更新失败!"
  },
  "components\\analyseConfig\\analyseCaseInstance.vue": {
    "texttexttexttextabuttonHrowClass": ">填写备注</a-button>\r\n            <h-row class=",
    "text": "空",
    "texttexttexttext": "填写备注",
    "texttexttexttexttext": "测试实例名",
    "99texttexttexttext": "99分位时延",
    "querytexttextlistfailed": "查询实例列表失败!",
    "contentTexttexttextdelete": ",\r\n                content: `您确定删除",
    "texttexttexttexttexttexttexttexttexttextdetailtexttexttexttexttext": "您确定要跳转到该实例详情分析页面吗？",
    "texttexttexttexttexttexttexttexttexttexttext": "您确定停止当前测试实例？",
    "texttexttexttexttexttextsuccess": "测试实例停止成功!",
    "99texttexttext": "99分位数",
    "texttextinfo": "事件信息",
    "tabdatareportstatusdescTexttext": "{{ (tabData.reportStatusDesc) || '暂无' }}"
  },
  "components\\analyseConfig\\analyseConfigShell.vue": {
    "texttexttexttext": "执行测试",
    "pleaseinputtexttexttexttext": "请输入执行命令",
    "texttexttext": "部署到",
    "texttexttexttexttext": "命令行参数",
    "pleaseinputtexttexttexttexttext": "请输入部署示例数",
    "texttextip": "接入IP",
    "pleaseinput110texttexttext": "请输入1-10正整数",
    "pleaseinput1text65535texttexttexttexttexttext": "请输入1到65535之间的端口号",
    "texttexttexttextIp": ",            // 业务类型\r\n                    ip:",
    "textquerytexttexttextinfo": "未查询到场景信息!",
    "querytexttextinfotexttext": "查询场景信息异常!",
    "querytexttextinfofailed": "查询场景信息失败!",
    "texttexttexttextsuccess": "测试执行成功!",
    "texttexttextproductnametestcasenametexttexttexttext": "确定对${productName}-${testCaseName}执行测试？",
    "texttexttexttexttexttext": "打印时延日志",
    "texttexttexttextldptexttexttexttext": "连接目标LDP业务系统",
    "deletetexttext": "删除用例",
    "configtexttexttext": "配置多账号",
    "nbspnbspnbsptexttexttexttext": "④&nbsp;&nbsp;&nbsp;执行测试"
  },
  "components\\appBindingCore\\AppBindingCore.vue": {
    "texttexttexttext": "线程类型",
    "texttextinfo": "线程信息",
    "manageip": "管理IP",
    "pleaseselectmanageip": "请选择管理IP",
    "texttexttexttexttext": "线程拥有者",
    "pleaseselecttexttexttexttexttext": "请选择应用节点名",
    "pleaseinputtexttexttexttexttext": "请输入线程拥有者",
    "pleaseselecttexttext": "请选择绑核",
    "text": "否",
    "texttexttext": "线程名",
    "texttext": "绑核"
  },
  "components\\coreReplayObservation\\coreReplayDetail.vue": {
    "starttimeStarttime": "开始时间：{{ startTime || \"-\" }}",
    "texttexttexttexttexttexttexttexttexttexttexttexttexttexttexttexttexttext": ">\r\n                    当前交易日重演完成时，将展示此交易日中",
    "texttexttexttexttexttexttexttexttexttexttexttextstatustexttexttexttexttexttexttexttexttext": ">\r\n                    当前交易日消息收发的实时状态。应答比对：当前交易日",
    "texttexttexttexttexttexttexttexttextstarttexttexttext": "当前交易日重演尚未开始，请稍候",
    "texttexttexttexttexttexttexttexttextreplayfilepath": "}；  重演交易日文件目录：${replayFilePath ||",
    "texttexttexttext": "正在加载",
    "texttexttexttexttext": "应答不一致",
    "texttexttexttexttexttext": "具体任务细节",
    "texttexttext": "集群名",
    "texttexttexttextmonitor": "重演工具监控",
    "texttexttexttexttps": "应答吞吐(tps)",
    "texttexttexttexttexttexttps": "应答比对吞吐(tps)",
    "texttext": "暂停",
    "texttexttexttexttexttexttexttext": "仅显示应答不一致",
    "datatexttexttexttext": "数据不一致数",
    "texttexttexttextstatusDatestatus": ", // 重演任务状态\r\n            dateStatus:",
    "texttexttexttextstatusStarttime": ", // 重演日期状态\r\n            startTime:",
    "starttimeReplayfilepath": ", // 开始时间\r\n            replayFilePath:",
    "texttextsuccess": "恢复成功",
    "datatexttexttext": "数据更新中",
    "autoupdatetimeSDatatexttext": "{{ autoUpdateTime }}s 数据更新",
    "texttexttexttexttexttexttexttexttexttexttexttexttexttexttexttexttexttexttexttexttexttexttexttexttexttexttexttexttexttexttexttexttexttexttexttexttexttexttexttexttexttext": "当前交易日重演完成时，将展示此交易日中\"重演\"与\"持久化\"中相同/不同表数量、应答的一致/不一致数量",
    "texttexttexttexttexttexttexttexttexttexttexttextstatustexttexttexttexttexttexttexttexttexttexttexttexttexttextTextTexttexttexttexttexttexttexttexttexttext": "当前交易日消息收发的实时状态。应答比对：当前交易日\"重演的应答\" 与 \"生产已记录的应答\"比对。",
    "texttexttexttexttexttexttexttexttexttexttexttexttexttexttexttexttexttexttexttexttexttexttexttexttexttexttext": "当前交易日中，重演工具本身的发送和应答速度及资源使用情况",
    "texttexttexttextInstancetotalcount": "（ 核心总数：{{ instanceTotalCount }}",
    "texttexttexttexttextInstancecompletedcount": "已完成重演：{{ instanceCompletedCount }} ）",
    "errortexttext": "错误原因"
  },
  "components\\coreReplayObservation\\coreReplayTaskList.vue": {
    "texttextdatatexttext": "核心数据重演",
    "texttexttexttext": "同步任务",
    "texttexttexttextlistdivDivClass": ">重演任务列表</div>\r\n          <div class=",
    "texttexthoptionHoptionKey": ">一致</h-option>\r\n            <h-option key=",
    "texttexttexttexttext": "选择交易日",
    "texttexttexttextname": "输入任务名称",
    "texttextstarttime": "执行开始时间",
    "pleaseselecttimetexttext": "请选择时间范围",
    "texttextname": "任务名称",
    "texttextstatus": "执行状态",
    "texttextendtime": "执行结束时间",
    "editsuccess": "修改成功",
    "texttextsuccess": "启动成功",
    "texttexttexttexttexttextsuccesstexttextresdatasuccesscount0": "重演任务同步成功！数量：${res?.data?.successCount || 0}",
    "texttexttextdeletetexttexttext": "确定要删除此任务？",
    "deletetexttexttexttexttexttexttexttexttextlisttexttexttexttexttexttexttexttexttexttextdatatexttexttexttexttexttexttexttexttextoperation": "删除重演任务后，任务将从列表移除，同时任务所产生的数据将一并被删除。请谨慎操作。",
    "texttexttexttextlist": "重演任务列表",
    "texttext": "一致",
    "texttexttext": "不一致"
  },
  "components\\coreReplayObservation\\createCoreReplayTask.vue": {
    "texttexttexttexttexttexttexttexttexttexttexttexttexttexttexttexttexttexttexttexttexttext": "原重演任务中的重演对象可能发生变化，请仔细核对。",
    "texttexttexttext": "发送间隔:",
    "texttexttexttexttexttext": "确定重演对象",
    "configtexttexttexttext": "配置任务参数",
    "infotexttext": "信息核对",
    "texttexttexttexttexttexttexttexttexttexttexttexttexttexttexttexttext": "按需选择需要重演“交易日”与对应的“核心”。",
    "texttexttexttexttexttexttexttexttexttexttexttexttexttexttexttexttexttexttexttexttexttexttexttexttexttexttexttexttext": "基于前两个步骤的选择，将对应产生的重演任务，请仔细核对任务内容。",
    "pleaseinput01000": "请输入0~1000",
    "texttexttexttextType": ",\r\n            // 复制、创建\r\n            type:",
    "texttexttexttexttextsuccess": "创建并启动成功",
    "texttexttexttexttexttexttexttexttext": "请至少选择一个核心",
    "texttexttexttexttexttextconfigtexttexttexttexttexttexttexttext": "根据实际情况配置重演任务相关参数。",
    "confirmtexttexttexttext": "确认离开页面？",
    "typeCopyTexttexttexttexttexttextTexttexttexttexttexttext": "{{ type === 'copy'? '复制重演任务' : '创建重演任务' }}",
    "formitemssendintervalmsTexttext": "{{ formItems.sendIntervalMs }} 毫秒",
    "edittexttext": "修改路径：",
    "texttexttextSteplistcurrentstep1": "下一步：{{ stepList[currentStep + 1] }}",
    "texttexttexttexttexttexttext": "创建并启动任务"
  },
  "components\\endpointConfig\\endpointConfig.vue": {
    "addtexttexttexttext": "添加网关节点",
    "texttexttexttext": "开发平台",
    "texttexttexttexttexttext": "应用节点类型",
    "texttexttextip": "接入点IP",
    "texttexttexttexttext": "连通性测试",
    "texttexttext": "系统号",
    "texttextconfig": "批量配置",
    "addtexttext": "添加节点",
    "managetexttext": "管理端口",
    "configtexttext": "配置文件",
    "texttexttexttexttexttexttext": ", // 超出的文本隐藏",
    "texttexttexttexttexttexttexttext": ", // 溢出用省略号显示",
    "texttexttexttextid": "应用插件ID",
    "texttexttexttextconfig": "接入网关配置",
    "texttexttexttextaccess_config_titlethisendpointtype": "后端服务：${ACCESS_CONFIG_TITLE[this.endpointType]}",
    "texttexttextdeletetexttextnametexttexttexttext": "您确定删除名为\"${name}\"的节点吗？"
  },
  "components\\latencyTrendAnalysis\\settingModal.vue": {
    "texttextconfig": "链路配置",
    "texttexttexttextdivHformitemLabel": ">分析目标</div>\r\n                <h-form-item label=",
    "pleaseselecttexttext": "请选择链路",
    "texttexttexttext": "分析目标",
    "texttexttime": "统计时间",
    "pleaseselecttexttexttexttext": "请选择统计口径",
    "texttexttexttexttexttexttexttexttext18texttext": "自定义范围不得超过18小时",
    "texttext": "午盘",
    "datatexttext": "数据过滤"
  },
  "components\\ldpDataObservation\\nodeInfo.vue": {
    "texttextname": "插件名称",
    "texttexttexttext": "目录地址",
    "texttexttexttexttexttext": "应用开发平台",
    "pidtexttext": "pid文件",
    "texttexttext": "实例号",
    "managefunctiontexttext": "管理功能数量",
    "managefunctionlist": "管理功能列表",
    "texttext": "目录",
    "configtexttext": "配置地址",
    "zkconfigtexttext": "zk配置地址"
  },
  "components\\ldpLinkConfig\\appClusterConfig.vue": {
    "texttexttexttextinfo": "应用集群信息",
    "texttexttexttexttext": "服务提供者",
    "texttexttexttexttexttext": "服务集群地址",
    "texttexttexttexttexttexttexttext": "集群应用节点类型",
    "texttexttexttext": "集群类型",
    "texttexttexttexttexttexttext": "集群内成员个数",
    "texttexttexttextinfothistabledataLength": "应用集群信息：${(this.tableData || []).length}"
  },
  "components\\ldpLinkConfig\\appInstanceConfig.vue": {
    "texttexttexttexttexttext": "应用节点身份",
    "texttextinfotexttext": "节点信息同步",
    "texttexttexttexttexttexttexttexttexttext": "应用节点注册中心路径",
    "texttexttexttext": "分片编号",
    "zkconfigtexttext": "zk配置节点",
    "texttexttexttextinfo": "应用节点信息",
    "texttexttexttextinfothistabledataLength": "应用节点信息：${(this.tableData || []).length}",
    "texttexttextdeletetexttextnametexttexttexttexttexttexttexttext": "您确定删除名为\"${name}\"的应用节点实例吗？"
  },
  "components\\ldpLinkConfig\\machineRoomConfig.vue": {
    "texttexttexttext": "机房别名",
    "texttexttexttexttexttext": "关联应用个数",
    "texttextinfothistabledataLength": "机房信息：${(this.tableData || []).length}"
  },
  "components\\ldpLinkConfig\\productInfoConfig.vue": {
    "texttextinfo": "产品信息",
    "texttexttexttexttext": "主题模板数：",
    "texttext": "机房",
    "texttexttexttext": "产品类型：",
    "texttexttext": "主题数：",
    "iptexttexttexttext": "IP地址/域名",
    "texttexttextconfig": "已托管配置",
    "configtexttextname": "配置节点名称",
    "configtexttexttext": "配置提供者",
    "configtexttexttexttext": "配置服务地址",
    "texttextname": "产品名称：",
    "texttexttexttexttexttext": "集群上下文数：",
    "texttextconfigtexttext": "产品配置中心：",
    "texttexttexttexttexttexttexttexttext": "集群上下文引用模板：",
    "texttexttexttexttexttexttext": "集群上下文模板："
  },
  "components\\ldpLinkConfig\\productMonitorConfig.vue": {
    "textfunctiontexttexttexttexttexttexttexttexttexttexttexttexttexttexttexttexttexttexttexttexttext": "该功能为商业版特性,请咨询销售获取商业版本开通策略",
    "texttexttexttexttexttextTextMonitortotalText": "告警规则执行 共 ${monitorTotal} 条",
    "texttexttexttexttexttextmonitortexttextconfig": "当前产品暂无监控告警配置",
    "addtexttextsuccess": "添加规则成功！",
    "texttexttexttexttexttextsuccess": "告警规则清除成功！",
    "addtexttext": "添加规则",
    "importtexttext": "导入规则",
    "texttexttexttext": "清空规则"
  },
  "components\\ldpLinkConfig\\productServiceConfig.vue": {
    "texttext": "插件",
    "functiontexttext": "功能号名",
    "texttexttexttext": "关联集群",
    "texttexttexttexttexttextstatus": "可运行的业务状态",
    "datamanagefunctiontext": "数据管理功能号",
    "texttextfunctiontext": "业务功能号",
    "datatexttext": "数据区间",
    "datatext": "数据源",
    "texttextkey": "分片key",
    "texttexttext": "内存表",
    "datatexttextfunctiontext": "数据上场功能号",
    "texttexttexttextfunctiontext": "二次上场功能号",
    "sqlfunctiontext": "SQL功能号",
    "configinfotexttextsuccess": "配置信息同步成功！",
    "datatexttexttexttextmanagefunctiontextconfiginfotexttexttext": "数据分片、数据管理功能号配置信息已同步！",
    "configinfotexttextfailed": "配置信息同步失败！",
    "textconfig": "去配置",
    "editsuccess": "修改成功！",
    "texttexttexttextdata": "同步服务数据"
  },
  "components\\ldpLinkConfig\\rcmContext.vue": {
    "texttexttextlist": "上下文列表",
    "texttexttextname": "上下文名称",
    "texttext": "标签",
    "texttexttexttext": "引用模板"
  },
  "components\\ldpLinkConfig\\serverConfig.vue": {
    "texttexttexttexttext": "测试连通性",
    "sshconfig": "SSH配置",
    "texttextiptexttextReturnH": "; // 渲染IP地址\r\n                        return h(",
    "sshtexttexttext": "SSH用户名",
    "texttextsshtexttexttextReturnH": "; // 渲染SSH用户名\r\n                        return h(",
    "texttexttexttext": "关联机房",
    "text": "或",
    "texttexttextthistabledataLength": "服务器：${(this.tableData || []).length}"
  },
  "components\\ldpLogCenter\\ldpTodbErrorLog.vue": {
    "texttextstatusAloadingVif": ">\r\n        <!-- 加载状态 -->\r\n        <a-loading v-if=",
    "texttexterrortexttextdivDivClass": ">回库错误日志</div>\r\n                    <div class=",
    "texttextname": "集群名称",
    "texttexttexttext": "股东代码",
    "texttexttexttexttexttexttexttexttexttexttext": "多个账号以英文逗号隔开",
    "texttexttexttexttexttext": "选择不能为空",
    "texttextstatus": "回库状态：",
    "textsuccess": "未成功",
    "texttexttime": "事务时间",
    "texttexttext": "下一页",
    "texttexttexttexttexttexttexttexttexttexttexttexttexttexttext50text": "支持多账号以英文逗号分隔且最多50个",
    "texttext": "账户",
    "errortext": "错误号",
    "errortexttext": "错误消息",
    "texttexttexttexttexttexttexttexttextoperationtexttexttexttexttexttexttexterrortexttexttexttexttexttexttexttexttexttexttexttexttexttexttext": "表示在用户进行\"重试\"操作之后，本地记录的错误事务，是否重新发送到网关，完成回库。",
    "0Texttext": ")?.[0]; // 获取",
    "queryfailed": "查询失败",
    "texttexttexttexttexttexttexttexttexttexttextdetail": "点击行查看对应日志记录详情",
    "textTotalcountTexttexttext": "共 {{ totalCount }} 条记录"
  },
  "components\\ldpLogCenter\\ldpTodbErrorRetry.vue": {
    "texttexttexttext": "终止任务",
    "texttexttext": "进行中",
    "texttexterrortexttexttext": "剩余错误账户数",
    "errortexttexttexttext": "错误账户总数",
    "texttexttexttexterrortexttexttext": "剩余回库错误事务数",
    "texttexterrortexttexttexttext": "创建错误重试任务",
    "texttext": "日志",
    "texttexttexttexttexttextstatus": "最后重试执行状态",
    "texttexttexttexttexttexttexttext": "暂无集群需要重试！",
    "texttexttextsuccesstexttexttext": "重试已成功事务数",
    "texttexttexttexttext": "执行总耗时",
    "starttexttexttexttexttexttext": "开始重试消息序号",
    "starttexttexttexttexttext": "开始重试事务号",
    "endtexttexttexttexttext": "结束重试消息号",
    "endtexttexttexttext": "结束的事务号",
    "texttexttexttexttexttexttext": "暂无进行中任务！",
    "texttexttexttexttexttexttexttexttexttexttext": "已存在进行中的重试任务！",
    "textconfirmtexttexttexttexttexttexttexttexttexttexttexttexttexttexttexttext": "您确认要终止当前正在执行中的重试任务吗？",
    "textconfirmtexttexttexttexttexttexterrortexttexttexttexttexttexttexttexttext": "您确认要对所有业务错误账户执行重试处理吗？",
    "texttexttexttexttexttextsuccess": "创建重试任务成功!",
    "texttexttexttexttexttextfailed": "创建重试任务失败!",
    "texttexttexttextsuccess": "终止任务成功!",
    "texttexttexttextfailed": "终止任务失败!",
    "texttexttexttexttexttexterrorquery": "核心集群回库错误查询"
  },
  "components\\ldpLogCenter\\recordMsgPoptip.vue": {
    "texttexttexttextinfo": "记录字段信息",
    "texttexttext": "字段值"
  },
  "components\\ldpMonitor\\appTypeObserver.vue": {
    "texttexttexttextdivAloadingVif": ">全局总览</div>\r\n            <a-loading v-if=",
    "texttexttexttext": "全局总览"
  },
  "components\\ldpMonitor\\businessMonitor.vue": {},
  "components\\ldpMonitor\\clusterAndGroupMonitor.vue": {
    "text": "主",
    "texttext": "异地",
    "texttexteleshardingno": "分片${ele.shardingNo || '-'}"
  },
  "components\\ldpMonitor\\clusterProcessPerform.vue": {
    "functiontexttexttext": "功能号处理",
    "texttexttps": "吞吐(tps)",
    "texttexttexttextns": "平均时延(ns)",
    "texttexttexttext": "其他业务",
    "texttexttext": "吞吐率",
    "errortext": "错误率",
    "texttextquery": "委托查询"
  },
  "components\\ldpMonitor\\dataAccordMonitor.vue": {
    "pleaseinputtexttext": "请输入表名",
    "texttexttext": "未加载",
    "loading": "加载中",
    "texttexttexttext": "上场次数:",
    "texttextfailed": "加载失败",
    "texttexttexttexttext": "内存记录数",
    "texttexttexttexttexttext": "持久化记录数",
    "datatexttext": "数据差量",
    "texttextstatus": "加载状态",
    "textdetail": "表详情",
    "texttexttextdetail": "表加载详情",
    "datatexttexttexttext0": "数据差量不为0"
  },
  "components\\ldpMonitor\\ldpAppObserver.vue": {
    "texttexttexttextinfodivAloadingVif": ">应用连接信息</div>\r\n            <a-loading v-if=",
    "texttexttexttextinfo": "应用连接信息"
  },
  "components\\ldpMonitor\\ldpAppObserverMenu.vue": {
    "texttexttexttexttexttexttextqueryDivVif": ">\r\n        <!-- 上下游组合条件查询 -->\r\n        <div v-if=",
    "texttexttexttext": "仲裁节点",
    "texttextstatus": "连接状态",
    "searchtexttexttexttext": "搜索应用节点",
    "texttexttexttexttexthoptionHoptionValue": ">上下游关系</h-option>\r\n            <h-option value=",
    "querytexttexttexttexttexttextlisterror": "查询应用节点下拉列表-错误:",
    "texttexttexttexttexttext": "集群同步关系",
    "texttexttexttexttexttexttext": "不过滤仲裁节点",
    "texttexttexttexttext": "上下游关系"
  },
  "components\\ldpMonitor\\ldpClusterObserver.vue": {
    "texttexttexttextinfo": "集群连接信息"
  },
  "components\\ldpMonitor\\ldpClusterObserverMenu.vue": {
    "searchtexttext": "搜索集群"
  },
  "components\\ldpMonitor\\observerSetting.vue": {
    "texttexttexttextsetting": "拓扑显示设置",
    "texttexttexttexttexttexttextdivDivClass": ">显示未托管节点</div>\r\n                <div class=",
    "rcmconfigdivDivClass": ">RCM配置</div>\r\n                <div class=",
    "texttexttexttextdivDivClass": ">默认标签</div>\r\n                <div class=",
    "queryrcmconfigfailed": "查询rcm配置失败：",
    "querytexttextfailed": "查询标签失败：",
    "texttexttexttexttexttexttext": "显示未托管节点",
    "texttexttext": "上下文",
    "rcmconfig": "RCM配置",
    "texttexttexttext": "默认标签"
  },
  "components\\ldpMonitor\\operatePerform.vue": {
    "texttexttexttextInfosumbarData": ">\r\n    <!-- 指标总览 -->\r\n    <info-sum-bar :data=",
    "texttexttexttexttexttexttexttext": "委托业务性能总览",
    "texttext5texttext": "最近5分钟",
    "texttext15texttext": "最近15分钟",
    "texttext30texttext": "最近30分钟",
    "texttext1texttext": "最近1小时",
    "5text": "5秒",
    "texttext": "时延",
    "texttextqbs": "吞吐(qbs)",
    "texttextns": "时延(ns)",
    "texttexttext": "请求数",
    "text": "次"
  },
  "components\\ldpMonitor\\rcmObserver.vue": {
    "texttextdetail": "查看详情"
  },
  "components\\ldpMonitor\\rcmObserverMenu.vue": {
    "searchtexttext": "搜索主题",
    "texttexttexttext": "消息复制",
    "statustexttexttext": "状态机复制"
  },
  "components\\ldpTable\\historyModifyList.vue": {
    "operationtexttext": "操作执行",
    "texttexttext": "用户名:",
    "texttext": "失效",
    "texttexttexttext": "用户角色:",
    "datatexttexttext": "数据记录号",
    "edittext": "修改值",
    "editstatus": "修改状态",
    "edittexttext": "修改执行",
    "textedittexttexttext": "该修改未执行",
    "texttexttexttextRequestprotocol": ", // 请求路径\r\n                requestProtocol:",
    "texttexttexttextRequesttime": ", // 请求协议\r\n                requestTime:",
    "texttexttimeRequestparams": ", // 请求时间\r\n                requestParams:",
    "texttexttexttextResponse": ", // 请求参数\r\n                response:",
    "texttextdetailfailed": "查看详情失败",
    "texttexteditoperation": "批量修改操作:",
    "texttextdatatexttext": "影响数据条数:",
    "texttexttexttexttexttext": "影响字段个数:",
    "edittextsubmittime": "修改单提交时间:",
    "edittextstatus": "修改单状态:",
    "editsuccesstext": "修改成功数:",
    "editfailedtext": "修改失败数:",
    "editstarttime": "修改开始时间:",
    "editendtime": "修改结束时间:",
    "edittexttexttext": "修改总耗时:"
  },
  "components\\ldpTable\\selectBox.vue": {
    "texttexttexttexttext": "内存表属性",
    "texttexttexttextfailed": "表单验证失败!"
  },
  "components\\ldpTable\\tableManageTop.vue": {
    "texttextdatatextmanage": "内存数据表管理",
    "texttext": "连接",
    "pleaseselecttexttexttexttexttexttexttexttexttexttext": "请选择连接的产品及应用节点",
    "linkbtnTexttextTexttext": "{{ linkbtn ? '断开' : '连接' }}"
  },
  "components\\ldpTable\\tableModify.vue": {
    "texttexttexttextDivClass": ">\r\n        <!-- 筛选部分 -->\r\n        <div class=",
    "texttextedittext": "历史修改单",
    "edittexttext": "修改预览",
    "boxinfotablenameTexteditoperationdetail": "boxInfo.tableName+ ' 表修改操作详情'",
    "texttexttexttext": "原记录值",
    "edittextlistqueryfailed": "修改单列表查询失败!",
    "texttextoperationfailed": "清空操作失败!",
    "edittexttextqueryfailed": "修改预览查询失败!",
    "texttextedittexttexttextdeletesuccess": "预览修改单条目删除成功!",
    "texttextedittexttexttextdeletefailed": "预览修改单条目删除失败!",
    "texttextedittexttexttexttexttexttext": "预览修改单条目不存在!",
    "texttexteditfailed": "执行修改失败!",
    "texttexttextdeleteparamsrowfieldnametexteditoperationtext": "您确定删除\"${params.row.fieldName}\"的修改操作吗？",
    "texttexttexttexttexttexttextoperationtext": "您确定执行清空操作吗？",
    "texttextoperation": "清空操作",
    "submitedit": "提交修改"
  },
  "components\\ldpTable\\tableQuery.vue": {
    "20texttext": "20条/页",
    "30texttext": "30条/页",
    "50texttext": "50条/页"
  },
  "components\\ldpTable\\tableStructure.vue": {
    "datatextinfo": "数据表信息",
    "datatexttexttext": "数据表说明：",
    "datatexttext": "数据表名：",
    "texttexttextedit": "是否可修改",
    "querytexttexttexttext": "查询结果可见",
    "texttexttexttext": "全选字段",
    "texttexttextquerytexttext": "可作为查询条件",
    "datatexttexttexttexttext": "数据表扩展属性：",
    "texttextstructtexttexttexttexttexttextconfig": "禁用struct字段可见属性配置"
  },
  "components\\locateConfig\\diffResult.vue": {
    "configtexttext": "配置对比",
    "configtexttexttexttext": "配置文件类型",
    "texttexttext": "源节点",
    "texttexttexttext": "一键修正"
  },
  "components\\locateConfig\\locateConfigManage.vue": {
    "texttexttexttextconfiglistDivClass": ">\r\n        <!-- 定位节点配置列表 -->\r\n        <div class=",
    "saveabuttonAbuttonVif": ">保存</a-button>\r\n                <a-button v-if=",
    "texttexttexttextconfiglist": "定位节点配置列表",
    "texttexttexttexttexttextconfig": "节点定位规则配置",
    "configtexttexttexttext": "配置对象类型",
    "configtexttext": "配置对象",
    "texttext": "同步",
    "texttextconfig": "对比配置",
    "texttexttexttexttexttext16mb": "文件大小超出16MB",
    "configtexttexttexttextLocallanguage": "配置文件格式：{{ localLanguage || '-' }}",
    "save": "保存"
  },
  "components\\locateConfig\\nodeDiff.vue": {
    "texttexttexttexttextSourcediffsourcenode": "对比源节点: ${sourceDiff.sourceNode || '-'}",
    "texttexttexttexttexttextTargetdifftargetnode": "对比目标节点: ${targetDiff.targetNode || '-'}",
    "configtexttexttexttextLanguage": "配置文件格式：{{ language || '-' }}",
    "configtexttextSourcediffsourcepath": "配置路径：{{ sourceDiff.sourcePath || '-' }}",
    "configtexttextTargetdifftargetpath": "配置路径：{{ targetDiff.targetPath || '-' }}"
  },
  "components\\locateConfig\\selectDiffModal.vue": {
    "texttexttexttextconfigtexttexttext": "选择对比配置的节点",
    "configtexttext": "配置类型",
    "texttextconfigtexttext": "原始配置来源",
    "texttexttexttexttext": "对比源节点",
    "texttexttexttexttexttext": "对比目标节点",
    "texttexttexttextar": "节点定位AR"
  },
  "components\\managementQuery\\appConfigInfoDrawer.vue": {
    "texttextinfo": "基本信息",
    "functionnamespanPClass": ">功能名称：</span>\r\n            <p class=",
    "functiontexttextspanPClass": ">功能说明：</span>\r\n            <p class=",
    "texttexttextspanPClass": ">提供者：</span>\r\n            <p class=",
    "texttexttimespanPClass": ">更新时间：</span>\r\n            <p class=",
    "texttexttexttext": "出参案例",
    "texttexttexttextdivDivClass": ">出参案例</div>\r\n        <div class=",
    "texttextname": "出参名称",
    "functionname": "功能名称：",
    "functiontexttext": "功能说明：",
    "texttexttext": "提供者：",
    "texttexttime": "更新时间："
  },
  "components\\managementQuery\\batchExportModal.vue": {
    "pleaseinputmanagefunctionnamesearch": "请输入管理功能名称搜索",
    "managefunction": "管理功能",
    "texttext": "原因",
    "texttextexport": "正在导出",
    "exporttexttext": "导出停止",
    "texttextfailed": "终止失败",
    "exporttexttexttexttext": "导出终止异常",
    "exporttexttexttexttextsuccesstexttexttext": "导出任务启动成功，请稍候",
    "exporttexttexttexttextfailed": "导出任务启动失败",
    "searchmanagefunctionLeftdatalength": "搜索管理功能 ({{ leftData.length }})",
    "textexportlistRightdatalength": "待导出列表 ({{ rightData.length }})",
    "texttextlist": "清空列表"
  },
  "components\\managementQuery\\configDataDrawer.vue": {
    "table": "表格",
    "functionconfig": "功能配置",
    "texttexttexttexttexttextNbsp": "结果展示方式\r\n      &nbsp;",
    "texttexttexttexttabtexttext": "最大展示Tab个数",
    "texttextconfig": "交互配置",
    "texttexttexttexttexttexttextfunctionlisttexttext": "只保留激活插件功能列表展开",
    "texttexttexttexttexttexttexttexttexttext": "记录最后一次输入参数",
    "texttexttexttextmanagefunctiontexttext": "结果页与管理功能联动",
    "texttextgettexttextfunctiontexttexttexttexttexttexttexttexttext": "单击Get类型功能菜单时自动发起请求"
  },
  "components\\managementQuery\\exportDataModal.vue": {
    "managefunctionexport": "管理功能导出",
    "texttexttexttexttexttexttexttextexporttextmanagefunction": "根据需要，选择想要导出的管理功能。",
    "texttext": "入参"
  },
  "components\\managementQuery\\jsonPathDrawer.vue": {
    "configModaldatatitle": "`配置（`+ modalData.title + ')'",
    "saveconfigabuttonAbuttonType": ">保存配置</a-button\r\n      >\r\n      <a-button type=",
    "pleaseinputjsonpath": "请输入jsonPath",
    "texttext": "别名",
    "tabletexttextname": "表格展示名称",
    "texttexttexttexttexttext": "是否支持排序",
    "tabletexttexttexttexttexttexttexttexttexttexttexttexttexttexttexttexttexttexttext": "表格字段支持行排序(仅当表格为一维数组时生效)",
    "config": "配置（"
  },
  "components\\managementQuery\\JsonPathTable.vue": {
    "text": "值",
    "texttextdatatexttexttext": "处理数据时出错:"
  },
  "components\\managementQuery\\managementBox.vue": {
    "managefunctiontexttexttexttext": "管理功能使用说明",
    "texttext": "回库",
    "placeholderpleaseinputDisabledClass": "placeholder=\"请输入\" disabled :class=\"["
  },
  "components\\marketAllLink\\allLinkAnalyseConfig.vue": {
    "texttexttexttext": "分析指标",
    "texttexttexttextdivHformitemLabel": ">分析指标</div>\r\n                <h-form-item label=",
    "datatexttextdivHformitemLabel": ">数据范围</div>\r\n                <h-form-item label=",
    "texttexttexttexttext": "自定义范围",
    "spansTexttexttexttext": ",\r\n                spans: [ // 链路指标",
    "95texttexttext": "95%分位数",
    "5texttexttext": "5%分位数",
    "submit": "提交"
  },
  "components\\marketAllLink\\marketChart.vue": {
    "texttexttexttexttexttexttexttexttexttexttexttext": "当前行情系统暂无时延走势",
    "texttexttexttexttexttexttexttexttexttexttext": "秒级系统穿透时延标准差",
    "texttexttexttexttexttextpPClass": ">系统抖动范围</p>\r\n                            <p class=",
    "texttexttexttexttextpPClass": ">时延正常率</p>\r\n                        <p class=",
    "95texttexttext": "95分位数:",
    "5texttexttext": "5分位数:",
    "texttexttexttexttexttext": "系统抖动范围",
    "texttexttexttext": ", // 抖动范围",
    "texttextnameLinkname": ",        // 链路名称\r\n                    linkName:",
    "thisspandatanamektext": "${this.spanData.name}-K线",
    "nametexttextns": "${name}走势(ns)",
    "lasttimedatatimeTexttexttexttexttexttexttext": "{{ lastTimeData.time }} 系统全链路时延",
    "texttexttext": "最小值:"
  },
  "components\\marketAllLink\\suspend.vue": {
    "texttextstatusspanSpanClass": ">应用状态：</span>\r\n                    <span class=",
    "texttexttexttexttextpPVfor": ">时延正常率</p>\r\n            <p v-for=",
    "texttextstatus": "应用状态："
  },
  "components\\mcDataObservation\\backtrackQuery.vue": {
    "texttexttext": "生产者",
    "pleaseselecttexttexttext": "请选择主题名",
    "texttext": "最早",
    "pleaseselecttexttext": "请选择分区",
    "querytexttext": "查询方式",
    "pleaseinput11000texttexttexttext": "请输入1~1000的正整数",
    "texttextidtexttext": "消息ID范围",
    "texttextid": "消息ID",
    "texttexttexttext": "消息内容",
    "texttexttime": "发布时间"
  },
  "components\\mcDataObservation\\consumeBacklog.vue": {
    "texttext": "耗时",
    "texttexttexttexttexttexttexttexttext": "当日客户端执行耗时",
    "texttexttexttexttext": "消息积压数",
    "texttexttext": "数量(个)",
    "texttexttexttexttexttext": "最新消息序号",
    "texttexttexttext": "调用次数",
    "texttexttexttexttexttexttexttexttexttexttext": "当日客户端最小执行耗时",
    "texttexttexttexttexttexttexttext": "消息处理变化趋势",
    "texttext15texttext": "历史15分钟",
    "text": "个"
  },
  "components\\mcDataObservation\\deadLetterQueue.vue": {
    "texttexttexttext": "清空队列",
    "refresh": "刷新",
    "texttexttexttexttime": "上次接收时间",
    "texttexttexttexttext": "死信消息数"
  },
  "components\\mcDataObservation\\mcCluster.vue": {
    "texttexttexttext": "消息中心",
    "texttexttexttexttexttexttexttext": "当前队列消息个数",
    "texttexttexttexttexttexttexttexttext": "历史队列最大消息数",
    "texttexttexttexttext": "集群主机数",
    "texttext": "编号"
  },
  "components\\mcDataObservation\\mcOverview.vue": {
    "texttexttexttexttext": "订阅项总数",
    "texttexttext": "消费者",
    "texttexttexttext": "消息数(个)",
    "texttexttexttexttexttext": "推送消息总数",
    "texttexttexttexttexttexttexttexttexttexttexttext": "主题生产消费消息变化趋势",
    "texttexttexttexttexttexttexttext": "订阅消息变化差量",
    "pleaseselecttexttext": "请选择主题",
    "pleaseselecttexttexttext": "请选择消费者",
    "texttexttexttexttextBrokertype": ", // 集群下拉框\r\n            brokerType:"
  },
  "components\\mcDataObservation\\mcPublish.vue": {
    "texttexttexttexttext": "生产消息数",
    "texttexttextinfo": "生产者信息"
  },
  "components\\mcDataObservation\\mcSubscribe.vue": {
    "texttexttexttext": "订阅主题",
    "texttexttexttexttext": "补缺消息数",
    "texttexttext": "客户端",
    "texttexttime": "订阅时间",
    "texttexttextinfo": "消费者信息"
  },
  "components\\mcDataObservation\\mcTopic.vue": {
    "texttextinfo": "主题信息",
    "texttexttexttext": "全局有序",
    "texttexttext": "分区数",
    "texttexttexttexttext": "过滤条件值",
    "texttexttexttexttexttext": "发布的消息数",
    "texttexttexttexttextsavetexttexttexttexttext": "服务端是否保存消费者偏移",
    "texttexttexttexttexttexttext": "主分区所在节点",
    "texttexttexttexttexttexttexttext": "副本分区所在节点",
    "texttexttexttexttextname": "消费者实例名称",
    "texttext": "内存"
  },
  "components\\mcDeploy\\mcTopicDeploy.vue": {
    "pleaseinputtexttexttext": "请输入主题名",
    "texttexttexttexttexttext": "动态主题同步",
    "texttexttexttext": "主题编号",
    "texttexttext": "副本数",
    "texttexttexttexttime": "上次更新时间"
  },
  "components\\mdbDataObservation\\generalView.vue": {
    "datatextname": "数据库名称",
    "texttextLabel": ", // 文本\r\n                        label:",
    "datatexttexttexttext": "数据库表个数",
    "texttexttexttexttext": "加锁进程号",
    "texttexttexttexttexttext": "内存使用分布",
    "texttexttexttexttexttexttexttext": "事务处理吞吐(笔/秒)",
    "texttexttext": "总数(笔)",
    "texttexttexttext": "吞吐(笔/秒)",
    "texttextdatatexttexttextinfo": "内存数据库版本信息",
    "texttexttexttextconfig": "内存分配配置",
    "adminfiletexttext": "AdminFile文件"
  },
  "components\\mdbDataObservation\\memory.vue": {
    "texttexttexttext": "总记录数",
    "texttexttexttexttexttexttexttext": "表记录数增长统计",
    "textsearchtexttexttext": "请搜索内存表",
    "texttexttexttexttexttexttexttexttext": "表内存使用增长统计",
    "texttexttexttexttexttexttext": "表内存使用分布",
    "textdatatexttexttexttext": "表数据存储-文件",
    "texttexttextinfo": "表索引信息"
  },
  "components\\mdbDataObservation\\performance.vue": {
    "texttexttexttexttexttext": "前十最大耗时",
    "texttextdetail": "执行详情",
    "texttexttexttexttextid": "事务控制器ID",
    "texttexttexttext": "最小耗时",
    "texttexttext": "标准差",
    "texttextTexttexttexttext": "开启 (基本级别)"
  },
  "components\\mdbDataObservation\\processor.vue": {
    "texttexttexttexttexttextTop10": "事务处理吞吐 - Top10",
    "texttexttexttext": "死锁检测",
    "texttexttexttexttexttexttexttext": "只看被阻塞控制器",
    "texttexttexttexttexttexttext": "只看工作控制器",
    "undotexttextinfo": "Undo文件信息",
    "undotexttexttexttexttextinfo": "Undo文件记录数信息",
    "undotexttexttext": "Undo事务号: -",
    "undotexttexttextdatacommitsqn": "Undo事务号:${data.CommitSqn || '-'}"
  },
  "components\\mdbDataObservation\\slowTransaction.vue": {
    "texttexttime": "执行时间",
    "texttexttexttexttext": "处理表明细",
    "texttextms": "耗时（ms）",
    "texttexttext": "处理表"
  },
  "components\\mdbPrivilegeManage\\roleManage.vue": {
    "addtexttextabuttonAbuttonType": ">添加角色</a-button>\r\n                <a-button type=",
    "texttexttext": "角色名",
    "pleaseinputtexttexttext": "请输入角色名",
    "texttexttextconfigtexttext": "角色未配置权限",
    "texttexttexttext": "绑定用户",
    "edittime": "修改时间",
    "edit": "编辑",
    "texttextconfig": "权限配置",
    "texttexttexttextsuccess": "创建角色成功!",
    "edittexttextsuccess": "修改角色成功!",
    "deletesuccess": "删除成功!",
    "readOnlytexttexttexttexttexttexttexttexttextdeletetexttexttexttext": "read only为系统内置角色,不可删除,请勿勾选！",
    "texttextdeletetexttext": "批量删除角色",
    "confirmtexttexttextdeletetexttexttexttexttexttexttext": "确认要批量删除已选中的角色吗？",
    "confirmtextdeletetexttextparamsrowrolenametext": "确认要删除角色\"${params.row.roleName}\"吗？",
    "addtexttext": "添加角色",
    "deletetexttext": "删除角色"
  },
  "components\\mdbPrivilegeManage\\userManage.vue": {
    "texttext": "用户",
    "addtexttextabuttonAbuttonType": ">添加用户</a-button>\r\n                <a-button type=",
    "texttexttext": "用户名",
    "pleaseinputtexttexttext": "请输入用户名",
    "texttexttexttext": "关联角色",
    "resettexttext": "重置密码",
    "texttextoperationfailed": "用户操作失败:",
    "texttexteditsuccess": "用户修改成功！",
    "texttexteditfailed": "用户修改失败！",
    "texttextdeletetexttext": "批量删除用户",
    "confirmtexttexttextdeletetexttexttexttexttexttexttext": "确认要批量删除已选中的用户吗？",
    "confirmtextdeletetexttextparamsrowusernametext": "确认要删除用户\"${params.row.userName}\"吗？",
    "texttexttextresettexttexttext": "确定要重置密码吗？",
    "addtexttext": "添加用户",
    "deletetexttext": "删除用户"
  },
  "components\\networkSendAndRecevied\\netRecord.vue": {
    "texttexttexttext": "用例内容",
    "texttexttexteditsuccess": "用例名修改成功!",
    "texttextdeletesuccess": "用例删除成功!",
    "bodytexttexttexttexttexttexttexttexttexttext": "Body内容为空或格式不正确",
    "texttextsavesuccess": "用例保存成功!",
    "texttexttexttexttexttext": "请先选择用例",
    "texttextthismenulist0label": "用例：${this.menuList?.[0]?.label}",
    "texttextitemlabel": "用例：${item?.label}",
    "texttexttextdeleteItemlabelTexttext": "您确定删除 '${item.label}' 用例？"
  },
  "components\\networkSendAndRecevied\\netResource.vue": {
    "texttexttext": "包类型",
    "functiontext": "功能号：",
    "texttextlist": "消息列表",
    "texttext": "清空",
    "texttexttexttexttext": "用户自定义",
    "texttexttime": "抓包时间",
    "texttextinfo": "附加信息",
    "texttexttexttextfailed": "停止抓包失败",
    "texttextlisttextreset": "消息列表已重置！",
    "texttexttexttexttexttexttexttexttexttexttexttexttextrefreshlisttexttexttexttexttexttexttexttexttextquerytexttexttext": "当前页为首页无法支持翻页，请刷新列表或用序号跳转至您想查询的位置",
    "texttexttexttexttexttexttexttexttexttexttexttexttexttexttextrefreshlisttexttexttexttexttexttexttexttexttextquerytexttexttext": "当前页为最后一页无法支持翻页，请刷新列表或用序号跳转至您想查询的位置",
    "texttexttexttexttexttextdatatext": "确定清空抓包数据吗?",
    "texttexttexttexttexttexttext": "无托管应用节点",
    "texttexttexttexttexttexttexttexttexttexttexttexttexttexttexttext": "一个应用节点仅能开启一个抓包任务",
    "starttexttext": "开始抓包",
    "texttexttexttext": "跳转序号",
    "textMsgtotalText": "共 {{ msgTotal }} 条"
  },
  "components\\networkSendAndRecevied\\netSend.vue": {
    "texttexttext": "无内容",
    "texttexttexttexttexttext": "发包调用耗时：",
    "textdivDivSlot": ">开</div>\r\n                            <div slot=",
    "texttexttexttexttext": "输入用例名",
    "2Texttexttimetexttexttextdelete40texttexttexttexttext": ": {}\r\n        * 2. 关闭时间戳开关：删除40号域，即删除",
    "texttext": "引用",
    "texttextbodytexttexttexttexttexttexttexttexttexttext": "请求Body内容为空或格式不正确",
    "texttextsuccess": "发包成功！",
    "texttexttexttexttexttextconfigtexttexttexttexttexttext": "请至“产品服务配置-产品服务网关”",
    "timetextNbsp": "时间戳 &nbsp;",
    "text": "关",
    "nbspnbspnbsptexttextlist": "&nbsp;&nbsp;&nbsp;用例列表",
    "savetexttexttext": "保存为用例",
    "texttexttexttexttexttexttext": "发包调用总耗时：",
    "texttexttimetext": "查看时间戳"
  },
  "components\\networkSendAndRecevied\\netThrouth.vue": {
    "texttextlist": "日志列表",
    "texttexttexttexttexttexttexttexttexttexttexttexttexttext": "支持输入多个，采用英文分号区分",
    "pleaseinputfunctiontexttexttexttexttexttexttexttexttexttext": "请输入功能号以英文分号形式间隔",
    "texttextdata": "获取数据"
  },
  "components\\productDataStorage\\archiveModal.vue": {
    "datatexttext": "数据冷备",
    "texttexttexttexttext": "归档服务器：",
    "texttexttexttext": "归档目录：",
    "texttexttextdata": "产品元数据：",
    "texttexttexttextdata": "产品遥测数据：",
    "datatexttexttexttext": "数据日期范围：",
    "texttexttextabuttonAbuttonVif": ">下一步</a-button>\r\n                <a-button v-if=",
    "cancelabuttonAbuttonVif": ">取消</a-button>\r\n                <a-button v-if=",
    "texttexttexttexttexttext": "选择归档条件",
    "texttexttexttextconfirm": "归档条件确认",
    "monitortexttextdata": "监控指标数据",
    "texttexttexttexttexttextsuccess": "归档指令发送成功！",
    "texttextfailed": "归档失败!",
    "nextstatusTexttexttexttexttexttexttexttexttexttextconfirm": "{{ !nextStatus ? '选择归档条件':'归档条件确认'}}",
    "text": "至",
    "texttexttext": "下一步",
    "texttext": "归档"
  },
  "components\\productDataStorage\\autoConditionModal.vue": {
    "texttexttexttexttexttext": "定时清理记录",
    "datatexttexttexttext": "数据保留天数：",
    "texttext3600text": "最大3600天",
    "texttexttexttexttime": "选择清理时间",
    "texttexttexttext": "清理结果",
    "queryabuttonAbuttonStyle": ">查询</a-button>\r\n                <a-button style=",
    "datatexttexttexttext13600text": "数据保留天数1~3600天",
    "texttexttime": "清理时间",
    "failedinfo": "失败信息",
    "texttexttextdatatexttext": "被清理数据大小",
    "texttexttext": "索引号",
    "texttexttexttextconfigsuccess": "定时清理配置成功！",
    "texttextconfig": "清理配置",
    "texttexttexttexttexttexttexttexttexttexttexttexttextoperation": "(启用后生效，每日定时执行清理操作)",
    "reset": "重置"
  },
  "components\\productDataStorage\\conditionModal.vue": {
    "texttexttexttext": "资金账号：",
    "datatexttext": "数据日期：",
    "texttexttexttexttexttexttexttexttext30text": "请限制清理日期小于30天",
    "texttexttexttexttexttextsuccess": "清理指令发送成功！",
    "texttext": "清理"
  },
  "components\\productDataStorage\\conditionModal1.vue": {
    "datatexttextconfig": "数据清理配置",
    "texttexttexttext": "产品节点:",
    "datatexttext": "数据类型：",
    "texttexttexttexttexttext": "选择清理条件",
    "texttexttexttextconfirm": "清理条件确认",
    "datatexttextfailed": "数据清理失败!",
    "nextstatusTexttexttexttexttexttexttexttexttexttextconfirm": "{{ !nextStatus ? '选择清理条件':'清理条件确认'}}",
    "warningTextoperationtexttexttextdeletetexttexttexttexttexttexttextdataTexttexttexttexttext": "警告: 该操作将物理删除符合条件的所有数据, 操作不可逆!",
    "confirmtexttext": "确认清理"
  },
  "components\\productDataStorage\\fileListModal.vue": {
    "texttexttextdatatexttext": "待归档数据清单",
    "texttexttexttext": "占用空间",
    "texttexttexttexttexttext": "总计:索引文件:",
    "textTexttexttexttext": "条; 存储空间:"
  },
  "components\\productServiceConfig\\linkTopoConfig.vue": {
    "texttexttexttexttexttext": "业务系统类型",
    "texttexttexttexttexttexttext": "是否支持该特性",
    "texttexttext": "不支持",
    "texttexttexttexttexttexttexttexttextlist": "已支持业务链路模型列表",
    "texttexttexttexttexttexttexttext": "业务链路度量模型",
    "texttexttexttext": "模型预览",
    "texttextconfigsuccess": "模型配置成功",
    "texttexttexttextfailed": "切换模型失败",
    "texttexttexttexttexttexttexttexttext": "确定要切换度量模型？",
    "texttexttexttexttexttexttexttexttexttexttexttexttexttexttexttexttexttexttextconfigtexttexttexttexttexttexttexttexttexttexttexttexttexttexttexttexttexttexttexttexttexttexttexttext": "切换度量模型，本产品节点下对应的「链路日志配置」将自动同步更新。本次变动需要重启采集子系统才可生效。"
  },
  "components\\productServiceConfig\\linkTopoReview.vue": {
    "texttextdatatexttext": "链路数据定义",
    "texttexttexttexttexttexttexttexttexttexttext": "应用全链路时延度量模型",
    "texttextname": "跨度名称",
    "texttexttexttext": "字段明细",
    "texttexttexttexttexttext": "业务系统版本",
    "texttext": "简介",
    "texttextdata": "链路数据"
  },
  "components\\productServiceConfig\\logSourceConfig.vue": {
    "texttexttexttext": "日志类型",
    "texttexttexttexttexttexttexttext": "时延日志输出目录",
    "texttexttexttexttexttexttext": "时延日志关键字",
    "texttextconfigsuccess": "同步配置成功",
    "textconfirmtextdeleterowinstancenametexttexttexttexttextconfigtext": "您确认要删除\"${row.instanceName}\"的链路日志配置吗？",
    "texttexttextconfigtexttexttexttext": "当前已配置链路模型:",
    "texttextconfig": "同步配置"
  },
  "components\\productServiceConfig\\manageFunctionMeta.vue": {
    "texttexttexttextdivDivClass": ">出参示例：</div>\r\n                    <div class=",
    "texttexttexttext": "出参示例：",
    "childfuncnamecnTexttext": "{{  child.funcNameCn || '暂无'}}"
  },
  "components\\productTimeAnalysis\\exportFileModal.vue": {
    "querytexttextexport": "查询结果导出",
    "exporttexttexttext": "导出文件名:",
    "pleaseinputqueryname": "请输入查询名称",
    "exporttexttext": "导出字段:",
    "savesuccess": "保存成功!"
  },
  "components\\productTimeAnalysis\\saveModal.vue": {
    "savequery": "保存查询",
    "queryname": "查询名称",
    "texttexttexttexttexttexttexttexttext15": "字符长度数不得超过15!",
    "texttexttexttext": "不能为空",
    "texttext": "本月"
  },
  "components\\rcmBacklogMonitor\\rcmBacklog.vue": {
    "texttexttexttexttexttexttextlist": "上下文消息积压列表",
    "texttexttexttexttext0": "积压数大于0"
  },
  "components\\rcmDeploy\\rcmConfigModel.vue": {
    "texttexttexttextspanDivClass": ">主题模板</span>\r\n            <div class=",
    "texttexttexttexttexttexttextspanDivClass": ">集群上下文模板</span>\r\n            <div class=",
    "texttexttexttext": "创建模板",
    "texttexttexttexttext": "通用上下文",
    "configdetail": "配置详情",
    "texttextname": "模板名称:",
    "texttextconfigdata": "暂无配置数据",
    "texttexttexttexttextsaveType": ",   // 模板名单独保存\r\n            type:",
    "texttexttexttexttexttextsuccess": "主题模板更新成功",
    "texttexttexttexttexttexttextsuccess": "上下文模板更新成功",
    "texttexttextdeletetexttextKeynameTextobjtypetexttext": "您确定删除名为 ${keyName} 的${obj[type]}模板？",
    "texttexttexttexttexttexttext": "集群上下文模板"
  },
  "components\\rcmDeploy\\rcmContextGroup.vue": {
    "texttexttexttexttexttexttext": "上下文实例分组",
    "texttexttext": "按标签",
    "texthcolhcolSpan": ">收</h-col><h-col span=",
    "texthrowHrowStyle": ">总</h-row>\r\n                        <h-row style=",
    "texttextdeletetexttexttexttextnametexttexttexttexttexttexttext": "确定删除分组名为${name}下的所有上下文？",
    "text": "发",
    "texttexttexttext": "查看更多",
    "texttextdelete": "批量删除"
  },
  "components\\rcmDeploy\\rcmDeployContext.vue": {
    "texttextname": "模板名称",
    "pleaseinputtexttexttextname": "请输入上下文名称",
    "texttexttexttexttext": "上下文模式",
    "pleaseinputtexttextname": "请输入应用名称",
    "querytexttext": "查询标签",
    "pleaseselectquerytexttext": "请选择查询标签",
    "texttexttext": "中心名",
    "texttextconfigtexttexttextsuccess": "创建/配置上下文成功!",
    "textconfirmtexttexttextdeletetexttexttexttexttexttexttexttext": "您确认要批量删除已选中的上下文吗",
    "texttexttexttexttexttexttexttext": "所选项未绑定标签",
    "addtexttext": "添加标签",
    "deletetexttext": "删除标签",
    "addtexttextsuccess": "添加标签成功!",
    "deletetexttextsuccess": "删除标签成功!",
    "textconfirmtextdeletetexttextparamsrownametexttexttexttexttext": "您确认要删除名为\"${params.row.name}\"的上下文吗？",
    "texttextdeletetexttexttexttexttexttexttextthistotaltext": "同时删除所有匹配记录（共${this.total}条）"
  },
  "components\\rcmDeploy\\rcmDeployTopic.vue": {
    "pleaseinputtext165535texttexttexttext": "请输入在1-65535之间的值",
    "texttextname": "主题名称",
    "pleaseinputtexttextname": "请输入主题名称",
    "pleaseinputtexttexttext": "请输入分区号",
    "texttexttexttexttexttexttext": "只支持输入数字",
    "texttexttexttext": "通讯端口",
    "texttextconfigtexttextsuccess": "创建/配置主题成功!",
    "pleaseselecttexttexttexttexttextdata": "请选择一条或多条数据",
    "texttextdeletetexttext": "批量删除主题",
    "textconfirmtexttexttextdeletetexttexttexttexttexttexttext": "您确认要批量删除已选中的主题吗？",
    "textconfirmtextdeletetexttextparamsrowtopictexttexttexttext": "您确认要删除名为\"${params.row.topic}\"的主题吗？"
  },
  "components\\rcmDeploy\\rcmOverview.vue": {
    "texttext": "端口",
    "textconfirmtextdeletetexttextconfigtext": "您确认要删除该行配置吗？",
    "texttexttexttext": "分区范围：",
    "texttexttexttexttexttext": "集群同步端口：",
    "texttexttextidtexttext": "上下文ID范围：",
    "texttextidtexttext": "主题ID范围：",
    "add": "新增"
  },
  "components\\rcmDeploy\\rcmSourceFile.vue": {
    "texttexttextconfig": "已发布配置",
    "texttexttextconfigtexttexttexttext": "已发布配置告警说明",
    "texttextIClass": ">历史：\r\n                    <i :class=",
    "texttexttexttexttexttextrcmconfigtexttexttexttexttexttexttexttexttexttexttexttexttexttext": "无法在线预览RCM配置文件！请下载源文件进行本地查看。",
    "texttexttexttext": "服务异常",
    "configtexttext": "配置还原",
    "texttexttexttexttexttextconfigtexttexttexttexttexttexttexttext": "本地变更：本地配置版本已经发生变更。",
    "texttexttexttexttexttextzkconfigtexttexttexttexttexttexttext": "远程变更：远程zk配置版本已发生变更。",
    "texttext": "历史：",
    "texttexttexttexttexttext": "最后更新版本："
  },
  "components\\rcmObservation\\deliver.vue": {
    "texttexttexttextcontextid": "接收端的ContextId",
    "texttexttexttext": "缓存积压",
    "texttexttexttexttext": "消息持久化",
    "texttexttexttexttexttexttexttexttext": "已经处理的消息数量",
    "submittexttexttexttexttext": "提交的消息序号",
    "texttexttexttextfailedtexttexttexttext": "消息回调失败次数统计",
    "texttextconfirmtexttexttexttext": "集群确认消息积压",
    "texttexttexttexttexttexttexttext": "待持久化消息积压",
    "texttexttexttexttexttexttexttexttexttext": "已经持久化的消息数量"
  },
  "components\\rcmObservation\\receiver.vue": {
    "texttextinfoInfogridRef": ">\r\n    <!-- 会话信息 -->\r\n    <info-grid ref=",
    "texttextinfo": "会话信息",
    "texttexttexttext": "丢包统计",
    "texttexttexttexttexttexttexttexttexttext": "下一个待接收分片序号",
    "texttexttexttexttexttexttexttexttext": "收到的最大分片序号",
    "texttextconfirmtexttexttexttexttext": "已经确认的消息序号",
    "texttexttexttexttexttext": "通讯分片处理",
    "texttexttexttexttexttexttext": "乱序和丢包检测",
    "texttexttexttextcanceltexttexttexttexttext": "下一个待取消息分片序号",
    "texttextconfirmtexttexttexttext": "已经确认的分片号",
    "texttexttexttextacktexttexttext": "最后发送ACK的原因"
  },
  "components\\rcmObservation\\transmitters.vue": {
    "texttextinfoInfogridRef": ">\r\n        <!-- 会话信息 -->\r\n        <info-grid ref=",
    "texttexttexttexttexttexttext": "分片发送与应答",
    "texttexttexttexttexttext": "对端分片应答",
    "texttexttexttexttexttexttexttext": "当前待发送分片号",
    "texttexttextsavetexttexttexttexttexttexttexttexttext": "缓存中保存的消息的最小消息号",
    "texttexttexttexttexttexttexttexttexttexttext": "最后一次收到的应答序号",
    "texttexttexttext": "消息积压",
    "texttexttexttexttexttexttexttexttexttext": "下一个待分配的分片号",
    "texttexttexttexttexttexttexttexttext": "异步发送的分片数目",
    "texttextfailedtexttexttexttexttext": "底层失败的分片数目"
  },
  "components\\secondAppearance\\logDrawerContent.vue": {
    "texttexttexttexttexttext": "上场记录总数：",
    "successtexttexttext": "成功记录数：",
    "failedtexttexttext": "失败记录数：",
    "errorinfo": "错误信息："
  },
  "components\\sms\\addOrUpdateManagerModal.vue": {
    "modaldatatypeEdittexttexttextinfoAddtexttexttexttexttext": "modalData.type ? '修改干系人信息' : '添加干系人信息'",
    "pleaseinput": "请输入...",
    "edittexttexttextinfo": "修改干系人信息",
    "addtexttexttextinfo": "添加干系人信息",
    "texttexttexttexttexttexttexttext": "手机号格式不正确",
    "texttexttexttexttexttext": "邮箱不能为空",
    "texttexttexttexttexttexttext": "邮箱格式不正确",
    "addfailed": "添加失败",
    "editfailed": "修改失败"
  },
  "components\\sms\\noticeTempModal.vue": {
    "texttexttexttexttexttextedit": "告警通知模板编辑",
    "texttexttexttext": "模板内容",
    "texttexttexttexttext": "通知到邮箱",
    "operationsuccess": "操作成功",
    "operationfailed": "操作失败",
    "texttexttextdeletetexttextparamsrowaddresseenametexttexttexttexttext": "您确定删除名为\"${params.row.addresseeName}\"的干系人吗？",
    "texttexttexttexttexttexttexttexttexttexttextphoneTexttexttexttexttexttexttexttexttexttexttext": "告警通知参数存在手机号${phone}, 则发送模板短信至该手机"
  },
  "components\\sqlTable\\tableSqlTop.vue": {
    "iscoresPleaseselecttexttexttexttexttexttexttexttextTexttexttexttexttext": "!isCores ? '请选择节点或集群或服务' : '请选择节点'",
    "titleIscoresMdbsqltexttexttexttexttextmdbsql": "title || (isCores ? 'MDB-SQL-多核心分发':'MDB-SQL')",
    "pleaseselecttexttexttexttexttexttexttexttext": "请选择节点或集群或服务",
    "mdbsqltexttexttexttexttext": "MDB-SQL-多核心分发",
    "texttextsuccess": "登录成功!",
    "texttexttexttext": "退出登录",
    "texttexttexttexttext": "请重新登录!",
    "edittexttextsuccesstexttexttexttexttext": "修改密码成功!请重新登录!",
    "texttextCase": "; // 服务\r\n                case",
    "texttext": "登录",
    "edittexttext": "修改密码"
  },
  "components\\transaction\\instanceTimeDelayModal.vue": {
    "modaldatakeytexttextdatalist": "${modalData.key}时延数据列表"
  },
  "components\\transaction\\reportConfirmModal.vue": {
    "texttexttexttext": "创建报表",
    "h3texttext": ">\r\n                <h3>报表",
    "texttexttexttextfailed": "报表创建失败!",
    "texttextinstancenametexttextsuccess": "报表\"{{instanceName}}\"创建成功!",
    "texttexttext": "席位号：",
    "starttime": "开始时间：",
    "endtime": "结束时间：",
    "instanceidTexttextTexttext": "{{instanceId ? '查看' :\r\n                    '创建'}}"
  },
  "components\\transaction\\settingModal.vue": {
    "texttexttexttext": "每秒平均",
    "pleaseinputtexttexttext": "请输入席位号",
    "pleaseinputtexttexttexttext": "请输入资金账号",
    "texttexttexttexttext": "分位数指标",
    "texttexttime": "同比时间",
    "texttexttexttexttexttexttexttexttexttexttexttexttexttextdata": "分析指标（最多只能勾选三个指标数据）"
  },
  "components\\transaction\\topListModal.vue": {
    "texttexttexttexttexttext": "每秒时延订单",
    "texttextS": "时延 (μs)",
    "texttexttexttext": "时延订单"
  },
  "components\\tripartiteServiceConfig\\eccomServiceConfig.vue": {
    "texttextdatatexttext": "华讯数据服务",
    "configtexttexttexttexttexttext": "配置服务接入代理",
    "texttexttexttexttext": "采集器实例",
    "texttexttexttexttexttext": "绑定产品节点",
    "topictexttext": "Topic地址",
    "texttexttexttexttexttexttexttext": "已适配的业务系统",
    "texttextdatatexttexttexttext": "创建数据采集代理",
    "editdatatexttexttexttext": "修改数据采集代理",
    "addsuccess": "新增成功!",
    "editsuccess": "修改成功!",
    "texttexttextdeleteParamsrowinstancenameTexttext": "您确定删除 '${params.row?.instanceName}' 实例？"
  },
  "components\\ustTableVerification\\createVerificationTask.vue": {
    "texttexttexttext": "校验字段",
    "texttexttexttexttext": "集群内校验",
    "textdatatext": "源数据库",
    "texttextdatatext": "目标数据库",
    "errortexttext": "错误阈值",
    "formitemsthresholdunitText": "formItems.thresholdUnit === '个'",
    "texttexttexttexttexttextdatatexttextaddTabledatalengthTexttexttexttexttexttexttexttext": "根据所选校验数据，将会新增${ tableData.length }条校验任务，请核对",
    "texttexttexttexttexttexttexttext11000texttexttexttext": "输入框输入范围为1~1000的正整数",
    "texttexttexttextdata": "选择校验数据",
    "texttexttexttexttexttexttext": "至少选择一张表",
    "texttexttext": "表字段",
    "texttext": "任务",
    "textdatatexttexttexttexttexttexttexttexttextlabeltexttexttexttexttextedit": "源数据库与目标数据库选择\"${label}\"节点重复，请修改",
    "texttexttexttexttexttexttextsettingerrortexttexttexttexttexttexttexttexttexttexttexttexttexttexttexttexttexttextdatatexttext": "“按表总量校验”不设置错误阈值。即不论校验过程是否出错，均完成全量数据校验",
    "texterrortexttext": "按错误个数",
    "texttexttexttexttexttexttexttexttexttexttexttexttexttexttexttexttexttexttexttexttexttexttexttexttexttexttext": "当表字段不一致个数达到阈值时，校验任务自动停止，校验不通过"
  },
  "components\\ustTableVerification\\verificationTaskList.vue": {
    "texttexttexttextlistdivDivClass": ">校验任务列表</div>\r\n                <div class=",
    "texttexttexttext": "校验内容:",
    "texttexttexttexttexttext": "校验任务总数:",
    "texttextsuccess": "启动成功！",
    "pleaseselecttexttexttexttexttexttexttext": "请选择一条或多条规则",
    "texttexttexttexttext": "停止校验中！",
    "texttext": "停止",
    "text": "此",
    "texttextdeletesuccess": "任务删除成功！",
    "texttextname": "任务名称:",
    "textdatatext": "源数据库:",
    "texttextdatatext": "目标数据库:",
    "texttexttexttexttexttexttexttext": "查看校验任务内容",
    "texttexttextdeleteobjtexttexttexttext": "确定要删除${obj}校验任务",
    "deletetexttexttexttexttexttexttexttexttextlisttexttexttexttexttexttexttexttexttexttexttexttexttexttexttexttexttexttexttexttexttextoperation": "删除校验任务后，任务将从列表移除，同时任务所产生的执行历史不可追溯。请谨慎操作。",
    "filenamerowstarttimetexttextdetailxls": "${fileName}-${row.startTime}-比对详情.xls",
    "texttexttexttextlist": "校验任务列表"
  }
};