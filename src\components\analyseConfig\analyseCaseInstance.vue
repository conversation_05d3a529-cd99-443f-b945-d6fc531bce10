<template>
    <div class="case-instance">
        <a-title>
            <slot>
                <h-select v-model="productInstNo" :clearable="false" style="width: 220px;" set-def-select showTitle
                    :disabled="disabled" @on-change="handleSelectChange">
                    <h-option v-for="item in productList" :key="item.id" :value="item.productInstNo">{{ item.productName
                    }}</h-option>
                </h-select>
                <h-select v-model="testCaseId" style="width: 220px;" set-def-select showTitle :disabled="disabled" @on-change="handleSelectChange">
                    <h-option v-for="item in caseList" :key="item.id" :value="item.id">{{ item.name }}</h-option>
                </h-select>
                <tag-list ref='tag-list' :queryList="queryList" :selectedId="tabName" @on-click="handleTagClicK"
                    @on-close="handleTabRemove">
                </tag-list>
            </slot>
        </a-title>

        <div v-show="queryList.length && tabData.instanceName" class="case-instance-detail">
            <a-loading v-if="loading"></a-loading>
            <h-row :gutter="20" class="tab-title">
                <h-col span="6">
                    <span>测试实例名</span>
                    <h-icon class="icon-title" name="t-b-modify"
                        @on-click="updateCaseName(tabData.id, tabData.instanceName)"></h-icon>
                </h-col>
                <h-col span="6">
                    <span>事件信息</span>
                    <h-icon class="icon-title" name="document" @on-click="showEventList"></h-icon></h-col>
                <h-col span="6">
                    <span>测试状态</span>
                </h-col>
                <h-col span="6">
                    <span>报表状态</span>
                    <h-icon v-if="tabData.reportStatus === 'complete'" class="icon-title" name="document"
                        @on-click="goLink(tabData.id)"></h-icon>
                </h-col>
            </h-row>
            <a-button v-if="tabData.testStatus === 'proceed' || tabData.testStatus === 'stopping'" type='dark'
                class=" btn-stop" @click="caseStop(tabData.id)">停止测试</a-button>
            <a-button type='dark' class="btn-remark" @click="updateCaseRemark">填写备注</a-button>
            <h-row class="tab-title" style="color: var(--font-color);">
                <h-col class="text-line" span="6">{{ tabData.instanceName }}</h-col>
                <h-col class="text-line" span="6">
                    <h-icon name="manage_fill" color="#4686F2"></h-icon>
                    <h-tooltip>
                        <span>{{ tabData.events ? tabData.events[0].message : '空' }}</span>
                        <template v-slot:content>
                                <div style='white-space: normal;'>{{ tabData.events ? tabData.events[0].message : '空' }} </div>
                        </template>
                    </h-tooltip>
                </h-col>
                <h-col class="text-line" span="6">
                    <h-icon name="stealth_fill" color="#4686F2"></h-icon>
                    <span>{{ tabData.testStatusDesc }}（ {{ tabData.testTime }}s ）</span>
                </h-col>
                <h-col class="text-line" span="6">
                    <h-icon name="stealth_fill" color="#4686F2"></h-icon>
                    <span>{{ (tabData.reportStatusDesc) || '暂无' }}</span>
                </h-col>
            </h-row>
            <!-- echarts -->
            <div v-if="dynamicSwitch" style="width: 100%; height: calc(100% - 100px); min-height: 300px;">
                <div :id="tabData.id" style="width: 100%; height: 100%;" class="echarts"></div>
            </div>
            <div v-if="!dynamicSwitch" style="width: 100%; height: calc(100% - 100px); min-height: 300px;">
                <no-data width="120" height="90" text="当前实例无采集时延走势信息" />
            </div>
            <h-checkbox-group v-if="dynamicSwitch" v-model="indicators" @on-change="checkChange">
                <h-checkbox v-for="item in checkList" :key="item.label" :label="item.label"
                    :disabled="indicators.length <= 1 && indicators.includes(item.label)"><span>{{ item.name
                    }}</span></h-checkbox>
            </h-checkbox-group>
        </div>
        <!-- 事件列表 -->
        <event-list-modal v-if="eventsInfo.status" :modalInfo="eventsInfo" />
        <!-- 用例备注 -->
        <case-remark-modal v-if="remarkInfo.status" :modalInfo="remarkInfo" @update="queryInstance" />
        <!-- 更新实例名称 -->
        <update-case-name-modal v-if="caseNameInfo.status" :modalInfo="caseNameInfo" @update="queryInstance" />
    </div>
</template>

<script>
import _ from 'lodash';
import aButton from '@/components/common/button/aButton';
import tagList from '@/components/common/tag/tagList';
import aTitle from '@/components/common/title/aTitle';
import noData from '@/components/common/noData/noData';
import aLoading from '@/components/common/loading/aLoading';
import * as echarts from 'echarts';
import caseRemarkModal from '@/components/analyse/caseRemarkModal.vue';
import updateCaseNameModal from '@/components/analyse/updateCaseNameModal.vue';
import eventListModal from '@/components/analyse/eventListModal.vue';
import { queryInstanceDetail, caseStopApi, deleteCaseInfo, queryInstance } from '@/api/httpApi';
export default {
    name: 'AnalyseCaseInstance',
    props: {
        sceneId: {
            type: String,
            default: ''
        },
        productList: {
            type: Array,
            default: []
        },
        caseList: {
            type: Array,
            default: []
        }
    },
    data() {
        return {
            loading: false,
            option: {
                backgroundColor: '#262b40',
                legend: {
                    data: [],
                    left: '20',
                    top: '20',
                    textStyle: {
                        color: '#fff'
                    }
                },
                grid: {
                    left: 92,
                    right: 50
                },
                tooltip: {
                    trigger: 'axis'
                },
                title: {
                    left: 'center',
                    text: ''
                },
                // X轴配置
                xAxis: {
                    type: 'category'
                },
                yAxis: [
                    {
                        type: 'value',
                        boundaryGap: false
                    },
                    {
                        type: 'value',
                        boundaryGap: false
                    }
                ],
                dataZoom: [
                    {
                        type: 'slider',
                        show: true,
                        height: 18
                    }
                ],
                // 折线设置
                series: []
            },
            checkList: [{
                label: 'max',
                name: '最大时延'
            }, {
                label: 'min',
                name: '最小时延'
            }, {
                label: 'avg',
                name: '平均时延'
            }, {
                label: 50,
                name: '中位数时延'
            }, {
                label: 99,
                name: '99分位时延'
            }],
            indicators: ['min', 'max'],        // 可展示时延数据线条
            timer: null,
            dynamicSwitch: false,   // 图表动态开关
            queryList: [],
            testCaseId: '',
            tabName: '',
            tabData: {},
            myChart: null,
            debounce: false,
            productInstNo: '',
            eventsInfo: {
                status: false,
                events: []
            },
            remarkInfo: {
                status: false,
                testCaseInstanceId: '',
                info: {}
            },
            caseNameInfo: {
                status: false,
                testCaseInstanceId: '',
                name: ''
            },
            isChange: true,
            disabled: false
        };
    },
    mounted() {
        window.addEventListener('resize', this.resize);
    },
    beforeDestroy() {
        window.clearInterval(this.timer);
        window.removeEventListener('resize', this.resize);
    },
    watch: {
        async tabName(newVal) {
            if (newVal) {
                await this.getChart(newVal);
            }
        }
    },
    methods: {
        // 绘制chart
        async getChart(id) {
            this.clearInterval();
            await this.initChart(id);
            await this.queryInstanceDetail(id);
            if (['proceed', 'not_started', 'stopping'].indexOf(this.tabData?.testStatus) > -1) {
                this.timer = window.setInterval(() => {
                    if (!this.myChart) this.initChart(id);
                    this.queryInstanceDetail(id);
                }, 1000);
            }
            this.resize();
        },
        resize() {
            this.myChart && this.myChart.resize();
        },
        scrollReset() {
            this.queryList?.length && this.$refs['tag-list'].scrollReset();
        },
        clearInterval() {
            this.timer && window.clearInterval(this.timer);
        },
        handleSelectChange(){
            if (this.isChange){
                this.disabled = true;
                this.queryInstance(false);
                setTimeout(() => {
                    this.disabled = false;
                }, 1000);
            }
        },
        // 切换tag重置查询数据
        handleTagClicK(id) {
            this.clearInterval();
            this.loading = true;
            this.tabName = id;
            this.tabData = this.getQueryCondition(id) || {};
            setTimeout(() => {
                this.loading = false;
            }, 1000);
        },
        // 根据id获取对应的查询信息
        getQueryCondition(id) {
            const data = _.find(this.queryList, o => { return o?.id === id; });
            return data;
        },
        queryInstance(id, items, isChange = true) {
            return new Promise((resolve, reject) => {
                this.clearInterval();
                this.queryList = [];
                this.tabData = {};
                queryInstance({
                    sceneId: this.sceneId,
                    testCaseId: items?.testCaseId || this.testCaseId
                }).then(res => {
                    if (res.success) {
                        this.isChange = isChange;
                        if (items?.productInstNo){
                            this.productInstNo = items?.productInstNo;
                        }
                        if (items?.testCaseId){
                            this.testCaseId = items?.testCaseId;
                        }
                        this.queryList = res.data.filter(v => v.productInstNo === (items?.productInstNo || this.productInstNo));
                        this.$nextTick(() => {
                            this.scrollReset();
                            this.handleTagClicK(id || this.queryList?.[0]?.id || '');
                            this.isChange = true;
                        });
                    }
                    resolve(true);
                }).catch(err => {
                    reject(err);
                    this.$hMessage.error('查询实例列表失败!');
                    this.isChange = true;
                });
            });
        },
        // 根据ID查询实例详情
        async queryInstanceDetail(id) {
            // 判断是否有上一笔请求等候
            if (this.debounce) return;
            const param = {
                testCaseInstanceId: id, // 实例id
                indicators: this.indicators.filter((o) => { return isNaN(o); }),
                percentiles: this.indicators.filter((o) => { return !isNaN(o); })
            };
            try {
                this.debounce = true;
                this.option.series = [];
                this.option.legend.data = [];
                this.option.xAxis.data = [];
                const res = await queryInstanceDetail(param);
                const { data } = res;
                if (res.success) {
                    this.tabData = data;
                    if (this.eventsInfo.status && (data.testStatus === 'proceed' || data.testStatus === 'not_started' || data.testStatus === 'stopping')) {
                        this.eventsInfo.events = data.events;
                    }
                    if (['proceed', 'not_started', 'stopping'].indexOf(data?.testStatus) === -1) {
                        this.clearInterval();
                    }
                    // 画图
                    const results = data?.delayTrend?.delayTrend;
                    if (results) {
                        this.dynamicSwitch = true;
                        this.indicators.forEach(ele => {
                            const key = isNaN(ele) ? ele : `p${ele}`;
                            this.option.legend.data.push(this.queryIndicatorObj(key));
                            const param = {
                                name: this.queryIndicatorObj(key),
                                type: 'line',
                                symbol: 'none',
                                sampling: 'lttb',
                                data: results[key]
                            };
                            this.option.series.push(param);
                        });

                        // 写入X轴坐标
                        this.option.xAxis.data = results.timeStamp;
                        if (!this.myChart) {
                            await this.initChart();
                        }
                        this.myChart.setOption(this.option);

                    } else {
                        this.dynamicSwitch = false;
                        this.myChart = null;
                    }
                }
            } catch (err) {
                this.clearInterval();
                this.$hMessage.error('实例详情数据查询失败！');
            }
            this.debounce = false;
        },
        // 展示事件信息
        showEventList() {
            this.eventsInfo.status = true;
            this.eventsInfo.events = this.getQueryCondition(this.tabName)?.events;
        },
        // 更新测试用例备注信息
        updateCaseRemark() {
            this.remarkInfo.status = true;
            this.remarkInfo.testCaseInstanceId = this.tabName;
            this.remarkInfo.info = this.getQueryCondition(this.tabName)?.remark;
        },
        // 更新测试实例名称
        updateCaseName(id, name) {
            this.caseNameInfo.status = true;
            this.caseNameInfo.testCaseInstanceId = id;
            this.caseNameInfo.name = name;
        },
        // 删除实例
        handleTabRemove(id) {
            const instanceName = this.getQueryCondition(id)?.instanceName;
            this.$hMsgBoxSafe.confirm({
                title: '删除',
                content: `您确定删除 '${instanceName}' 测试实例？`,
                onOk: async () => {
                    const res = await deleteCaseInfo({
                        testCaseInstanceId: id
                    });
                    if (res.success) {
                        this.$hMessage.success('测试实例删除成功!');
                        this.queryInstance();
                    } else {
                        this.queryInstance();
                        this.$hMessage.error({
                            content: res.message,
                            closable: true,
                            duration: 0
                        });
                    }
                }
            });
        },
        // 跳转测试分析页面
        goLink(id) {
            this.$hMsgBoxSafe.confirm({
                title: '实例跳转',
                content: '您确定要跳转到该实例详情分析页面吗？',
                onOk() {
                    this.$hCore.navigate(`/analyseData`, {
                        id
                    });
                }
            });
        },
        // 修改多选框
        checkChange(e) {
            this.myChart && this.myChart.clear();
            this.queryInstanceDetail(this.tabName);
        },
        // 停止测试
        async caseStop(id) {
            this.$hMsgBoxSafe.confirm({
                title: '您确定停止当前测试实例？',
                onOk: async () => {
                    const res = await caseStopApi({
                        testCaseInstanceId: id
                    });
                    if (res.success) {
                        this.$hMessage.success('测试实例停止成功!');
                    }
                }
            });
        },
        // 根据指标查询对应枚举字典
        queryIndicatorObj(key) {
            const indicatorObj = {
                max: '最大值',
                min: '最小值',
                avg: '平均值',
                p50: '中位数',
                p99: '99分位数'
            };
            return indicatorObj[key];
        },
        // 初始化图表
        async initChart(val) {
            this.myChart && this.myChart.dispose() && this.myChart.clear();
            return new Promise((resolve, reject) => {
                this.$nextTick(() => {
                    const dom = document.getElementById(val || this.tabName);
                    if (dom) this.myChart = echarts.init(dom, 'dark');
                });
                resolve(true);
            });
        }
    },
    components: { aButton, noData, aTitle, tagList, caseRemarkModal, updateCaseNameModal, eventListModal, aLoading }
};
</script>

<style  lang="less" scoped>
.case-instance {
    width: 100%;
    height: calc(100% - 310px);
    min-height: 360px;
    padding: 10px 0;
    color: var(--font-color);

    /deep/ .monitor-list {
        left: 480px;
        width: calc(100% - 480px);
    }

    /deep/ .list-box {
        width: calc(100% - 50px);
        overflow: hidden;

        .list {
            width: calc(100% - 480px);
            display: flex;
            transform: all 2s;
            position: relative;
            left: 0;
            transition: left 1s;
        }
    }

    .case-instance-detail {
        position: relative;
        padding: 10px 20px 0;
        height: 100%;

        .tab-title {
            width: calc(100% - 200px);
            color: var(--font-color);
            margin-bottom: 6px;

            .icon-title {
                position: relative;
                left: 5px;
                top: -1px;
                cursor: pointer;
            }

            .h-col {
                display: flex;
                align-items: center;

                span {
                    line-height: 30px;
                }
            }

            .text-line {
                line-height: 18px;

                & > .h-icon {
                    padding-right: 4px;
                }

                span {
                    overflow: hidden;
                    text-overflow: ellipsis;
                    line-height: 18px;
                    // stylelint-disable property-no-vendor-prefix,value-no-vendor-prefix
                    display: -webkit-box;
                    -webkit-box-orient: vertical;
                    -webkit-line-clamp: 2;
                }
            }
        }

        .btn-stop {
            position: absolute;
            top: 15px;
            right: 15px;
        }

        .btn-remark {
            position: absolute;
            right: 100px;
            top: 15px;
        }
    }
}
</style>
