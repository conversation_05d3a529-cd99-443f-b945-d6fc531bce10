<!--
 * @Description: 连接关系
 * @Author: <PERSON><PERSON>
 * @Date: 2022-08-16 11:34:55
 * @LastEditTime: 2022-08-17 13:32:56
 * @LastEditors: <PERSON><PERSON> Ying
-->
<template>
    <div>
        <!-- 下发配置列表 -->
        <h-msg-box-safe v-model="modalData.status" :escClose="true" :mask-closable="false" title="Transport实例信息"
            :footerHide="true" maxHeight="440" width="80">
            <div class="title">连接关系</div>
            <h-row style="margin: 0 10px 16px 30px;">
                <h-col span="9">
                    <span>业务连接类型：</span>RCM-AS—TCP
                </h-col>
                <h-col span="7">
                    <span>连接名称：</span>可靠组播通信
                </h-col>
                <h-col span="4">
                    <span>通信类型：</span>RCM
                </h-col>
                <h-col span="4">
                    <span>协议类型：</span>RCM
                </h-col>
            </h-row>
            <div class="title">连接关系实例</div>
            <h-row style="margin: 0 10px 10px 30px; font-weight: 600;">
                <h-col span="7">TxContext</h-col>
                <h-col span="2">&nbsp;</h-col>
                <h-col span="6">Transport</h-col>
                <h-col span="2">&nbsp;</h-col>
                <h-col span="7">RxContext</h-col>
            </h-row>
            <h-row style="margin: 0 10px 5px 30px;">
                <h-col span="7">secu#0_ldp_offerproc_0_Proxy</h-col>
                <h-col span="2" class="link link-left link-right">---</h-col>
                <h-col span="6">report[1]</h-col>
                <h-col span="2" class="link">---</h-col>
                <h-col span="7">offer#0_ldp_offerproc_0_Singleton</h-col>
            </h-row>
        </h-msg-box-safe>
    </div>
</template>

<script>
export default {
    data() {
        return {
            modalData: {
                status: true
            },
            formItem: {}
        };
    }
};
</script>

<style lang="less" scoped>
@import url("./common.less");

.link {
    text-align: center;
}

.link-left::before {
    content: "&#60;";
}

.link-right::after {
    content: "&#62;";
}
</style>
