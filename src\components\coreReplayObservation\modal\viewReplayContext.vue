<template>
    <div>
      <h-drawer
        ref="drawer-box"
        v-model="modalData.status"
        className="view-replay-context dark-drawer"
        width="50"
        title="任务参数"
        :mask-closable="true"
        @on-close="handleDrawerClose"
      >
        <h-form :label-width="100">
          <h-form-item label="任务名称">
            <span style="color: var(--font-body-color);">{{ modalData.replayName || "-" }}</span>
          </h-form-item>
          <h-form-item label="备注">
            <span style="color: var(--font-body-color);">{{ modalData.notes || "-" }}</span>
          </h-form-item>
          <h-form-item label="重演内容" class="form-container">
              <h-tabs panelRight :labelWidth="0"  alginDre="left" class="tab-container">
                  <h-tab-pane v-for="item in tradeDayInfos"  :key="item.tradeDay" :label="item.tradeDay" :name="item.tradeDay">
                      <div class="core-label" :title="item.tradeDay">{{ item.tradeDay }} </div>
                      <div class="core-container">
                          <p v-for="core in getUniqueReplayAppArray(item.replayAppArray)" :key="core.appName">{{ core.appName }}</p>
                      </div>
                  </h-tab-pane>
              </h-tabs>
          </h-form-item>
          <h-form-item label="任务参数">
              <div class="form-box-param">
              <p>
                  <span style="color: var(--font-body-color);">发送间隔: </span
                  ><span style="color: var(--font-color);"
                    >{{ modalData.sendIntervalMs }} 毫秒</span
                  >
                </p>
              <div class="json-box">
                  <json-viewer
                    :value="jsonData"
                    :expand-depth="10"
                    copyable
                    :expanded="true"
                  >
                  </json-viewer>
              </div>
          </div>
          </h-form-item>
        </h-form>
      </h-drawer>
    </div>
  </template>

<script>
import jsonViewer from 'vue-json-viewer';
export default {
    name: 'ViewReplayContext',
    props: {
        modalInfo: {
            type: Object,
            default: () => {}
        }
    },
    components: { jsonViewer },
    data() {
        return {
            modalData: this.modalInfo,
            jsonData: {},
            tradeDayInfos: []
        };
    },
    mounted() {
        const replayConfig = JSON.parse(this.modalInfo?.replayConfig || '{}');
        this.jsonData = replayConfig?.ldpReplayBaseConfig ? JSON.parse(replayConfig.ldpReplayBaseConfig) : {};
        this.tradeDayInfos = (replayConfig?.tradeDayInfos || []);
    },
    beforeDestroy() {},
    methods: {
        handleDrawerClose() {
            this.modalData.status = false;
        },
        // appName去重
        getUniqueReplayAppArray(replayAppArray) {
            const uniqueApps = new Map();
            replayAppArray.forEach(core => {
                if (!uniqueApps.has(core.appName)) {
                    uniqueApps.set(core.appName, core);
                }
            });
            return Array.from(uniqueApps.values());
        }
    }
};
</script>

  <style scoped lang="less">
    @import url("@/assets/css/drawer.less");
    @import url("@/assets/css/json-view.less");

    /deep/ .h-drawer-body {
        padding: 10px;
    }

    .view-replay-context {
        .form-box-param {
            border: var(--border);
            border-radius: 4px;

            p {
                height: 36px;
                font-size: 12px;
                color: #fff;
                background: #2c334a;
                border-radius: 4px 4px 0 0;
                border-bottom: 1px solid #444a60;
                padding: 1px 10px;
            }
        }

        .json-box .jv-container {
            margin: 0;
            max-height: 440px;
            background: transparent;
        }

        .form-container {
            .tab-container {
                background: transparent;
                border: 1px solid #485565;
                border-radius: 4px;

                /deep/ .h-tabs-content.h-tabs-content-animated.h-tabs-content-right {
                    min-height: 118px !important;
                }

                /deep/.h-tabs-nav-right .h-tabs-tab-active i,
                /deep/.h-tabs-nav-right .h-tabs-tab:hover i {
                    display: none;
                }

                /deep/ .h-tabs-bar-right {
                    display: none;
                }

                /deep/.h-tabs-nav-right {
                    font-size: 12px;
                    color: var(--font-color);
                    padding-right: 0;
                }

                /deep/.h-tabs-content-right {
                    // border-left: 1px solid #414d5d;
                    border-left: none;
                }

                /deep/.h-tabs-nav-right .h-tabs-tab-active {
                    background: #1f3759;

                    &::after {
                        position: absolute;
                        top: 0;
                        left: 0;
                        content: "";
                        width: 4px;
                        height: 36px;
                        background: var(--link-color);
                    }
                }

                /deep/.h-tabs-content-right .h-tabs-tabpane {
                    padding: 0;
                }

                /deep/.h-tabs-nav-right .h-tabs-tab-alginleft {
                    overflow: hidden;
                    text-overflow: ellipsis;
                    white-space: nowrap;
                }

                /deep/ .h-tabs-nav-right .h-tabs-tab:hover {
                    background: #1f3759;
                    cursor: pointer;

                    &::after {
                        position: absolute;
                        top: 0;
                        left: 0;
                        content: "";
                        width: 4px;
                        height: 36px;
                        background: var(--link-color);
                    }
                }

                .core-label {
                    height: 36px;
                    font-size: 12px;
                    color: #fff;
                    background: #2c334a;
                    border-radius: 4px 4px 0 0;
                    border-bottom: 1px solid #444a60;
                    padding: 1px 10px;
                }

                .core-container {
                    max-height: 96px;
                    overflow-y: auto;

                    p {
                        display: inline-block;
                        height: 22px;
                        line-height: 22px;
                        margin: 5px 10px;
                        font-size: 12px;
                        color: #cacfd4;
                        text-align: left;
                    }
                }
            }
        }
    }
  </style>
