/*
 * @Description: 动态监控规则配置
 * @Author: <PERSON><PERSON>
 * @Date: 2023-02-16 19:54:10
 * @LastEditTime: 2023-03-20 18:05:18
 * @LastEditors: <PERSON><PERSON>
 */
import Vue from 'vue';
import _ from 'lodash';
import aInput from '@/components/common/input/aInput';
import aSelect from '@/components/common/select/aSelect';
export default {
    name: 'ldpMonitorHtml',
    props: {
        monitorDict: {
            type: Array,
            default: []
        },
        monitorData: {
            type: Object,
            default: {}
        },
        placeholder: {
            type: Object | String,
            default: ''
        },
        isEdit: {
            type: Boolean,
            default: false
        }
    },
    data() {
        return {
            html: ''
        };
    },
    methods: {
        init() {
            this.generateConfigHtml();
        },
        // 根据告警类型生成动态HTML
        generateConfigHtml() {
            this.html = Object.keys(this.monitorData).length ? this.monitorData.desc.replace(/\${.*?\}/g, item => {
                const name = item.slice(2, -1);
                const placeInfo = this.monitorData.placeholder?.[name];
                switch (placeInfo?.inputTyep) {
                    case 'select':{
                        const opList = _.find(this.monitorDict, ['categoryCode', placeInfo.sourceDict])?.dicts || [];
                        const optionValues = _.filter(opList, o => { return placeInfo.optionValues.includes(o.code); }) || [];
                        const optionList = [];
                        optionValues.forEach(option => {
                            optionList.push({
                                label: option.desc,
                                value: option.code
                            });
                        });
                        const width = placeInfo.sourceDict === 'metricsName' ? 150 : 90;
                        return `<a-select
                            prop="select"
                            :options="${JSON.stringify(optionList).replace(/"/g, "'")}"
                            :positionFixed="true"
                            :required="true"
                            placement="top"
                            value="${this.placeholder?.[name] || ''}"
                            @on-change="(val) => {selectChange(val, '${name}')}"
                            :disabled="${this.isEdit && name === 'metricsName'}"
                            width='${width}'
                            style="width: ${width}px; margin: 4px 5px;">
                            </a-select>`;
                    }
                    case 'input':
                        return `<a-input placeholder="请输入" prop="input"  placement="top" :required="true" value="${this.placeholder?.[name] || ''}" @on-blur="inputBlur($event, '${name}')" style="width: 90px; margin: 0 5px;"></a-input>`;

                    case 'text':{
                        const list = _.find(this.monitorDict, ['categoryCode', placeInfo.sourceDict])?.dicts;
                        const text = _.find(list, ['code', placeInfo.defaultValue]).desc;
                        return `<span style="padding: 0 4px; font-weight: 700;">${text}</span>`;
                    }
                    default:
                        return '';
                }
            }) : '';
        }
    },
    render(h) {
        const vHtml = Vue.extend({
            template: `<span>${this.html}</span>`,
            methods: {
                selectChange(val, name) {
                    this.$parent.$emit('ch-change', val, name);
                },
                inputBlur(event, name) {
                    this.$parent.$emit('ch-blur', event.currentTarget._value, name);
                }
            },
            components: { aInput, aSelect }
        });
        return h(vHtml, {});
    }
};
