import fetch from './fetch';
import { objectToQueryString } from '@/utils/utils';

const prefix = window['LOCAL_CONFIG']['API_HOME'] || '/';

// ***************************************** 抓包 ************************************************//
// 获取抓包开关状态
export function getPacketCaptureInstancesStatus(param) {
    return fetch({ showErrorToast: false }).post(`${prefix}/packet/capture/instances/status`, param);
}

// 获取单个应用实例抓包状态-----展示用来获取插件列表以及单个节点状态轮询
export async function getPacketCaptureInstanceStatus(param) {
    return fetch({ showErrorToast: false }).get(`${prefix}/packet/capture/instance/status?${objectToQueryString(param)}`);
}

// 当处于抓包状态中时获取功能号
export function getPacketCaptureInstanceFilter(param) {
    return fetch({ showErrorToast: false }).get(`${prefix}/packet/capture/filter?${objectToQueryString(param)}`);
}

// 设置抓包开关
export function postPacketCaptureEnable(param) {
    return fetch().post(`${prefix}/packet/capture/enable`, param);
}

// 获取抓包数据
export function getPacketCaptureData(param) {
    return fetch().get(`${prefix}/packet/capture/data?${objectToQueryString(param)}`);
}

// 清空抓包缓存数据
export function clearPacketCaptureData(param) {
    return fetch().get(`${prefix}/packet/capture/data/clear?${objectToQueryString(param)}`);
}

// 获取抓包日志列表
export function getPacketCaptureLog(param) {
    return fetch().get(`${prefix}/packet/capture/history/log/name?${objectToQueryString(param)}`);
}

// 获取抓包日志详情信息
export function getPacketCaptureLogDetail(param) {
    return fetch().get(`${prefix}/packet/capture/history/log/data?${objectToQueryString(param)}`);
}

// 抓包数据解析
export function setPacketTransferData(param) {
    return fetch().post(`${prefix}/packet/transfer`, param);
}

// ***************************************** 发包 ************************************************//
/**
 * 获取发包用例列表
 */
export function getUseCaseList(param) {
    return fetch().get(`${prefix}/packet/sender/use-cases?${objectToQueryString(param)}`);
}

/**
 * 获取发包用例详情
 */
export function getUseCaseInfo(param) {
    return fetch().get(`${prefix}/packet/sender/use-case?${objectToQueryString(param)}`);
}

/**
 * 数据包发送
 */
export function sendPacket(param) {
    return fetch().post(`${prefix}/packet/sender/send`, param);
}

/**
 * 用例信息重命名
 */
export function saveUseCaseName(param) {
    return fetch().post(`${prefix}/packet/sender/use-case/rename`, param);
}

/**
 * 保存用例信息
 */
export function saveUseCaseInfo(param) {
    return fetch().post(`${prefix}/packet/sender/use-case`, param);
}

/**
 * 导入用例信息
 */
export function importUseCaseFile(param) {
    return fetch().post(`${prefix}/packet/sender/use-cases/import`, param);
}

/**
 * 删除用例信息
 */
export function deleteUseCase(param) {
    return fetch().post(`${prefix}/packet/sender/use-case/delete`, param);
}

