import '../public.less';
import obsTable from '@/components/common/obsTable/obsTable';
import drawerContent from '@/components/secondAppearance/logDrawerContent.vue';
import importStatusTableIcon from '@/components/secondAppearance/importStatusTableIcon.vue';
import { getLoadDataHistory, getLoadDataResult } from '@/api/brokerApi';
import { EXECUTE_STATUS_OPTIONS } from '@/components/secondAppearance/constant';

function formatDateTime(input) {
    // 如果输入是字符串，将其解析为日期对象
    const date = typeof input === 'string' ? new Date(input) : input;

    if (isNaN(date.getTime())) {
        // 如果解析失败，返回一个错误提示
        return 'Invalid Date';
    }

    // 获取年、月、日、时、分、秒
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0'); // 月份从0开始，需要加1
    const day = String(date.getDate()).padStart(2, '0');
    const hours = String(date.getHours()).padStart(2, '0');
    const minutes = String(date.getMinutes()).padStart(2, '0');
    const seconds = String(date.getSeconds()).padStart(2, '0');

    // 格式化成 "YYYY-MM-DD HH:mm:ss" 的形式
    const formattedDateTime = `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;

    return formattedDateTime;
}

export default {
    name: 'playRecordList',
    props: {
        productId: {
            type: String,
            default: ''
        },
        instanceTypeOptions: {
            type: Array,
            default: () => []
        }
    },
    data() {
        return {
            historyTableLoading: false,
            historyTableData: [],
            historyTableColumn: [
                {
                    key: 'startTime',
                    title: '上场时间',
                    render: (h, params) => {
                        return h('div', [
                            h('Button',
                                {
                                    props: {
                                        size: 'small',
                                        type: 'text'
                                    },
                                    on: {
                                        click: () => {
                                            this.handleLogTableData(params.row);
                                        }
                                    }
                                },
                                formatDateTime(new Date(params.row.startTime))
                            )
                        ]);
                    }
                },
                {
                    key: 'whereCondition',
                    title: '上场方式',
                    render: (_, params) => {
                        return <div>{params.row.whereCondition === 'all' ? '按SQL条件' : '按资金账户'}</div>;
                    }
                },
                {
                    key: 'executeDetail',
                    title: '上场结果'
                }
            ],
            historyTableHeight: '245px',
            logTableLoading: false,
            logTableData: [],
            logTableColumn: [
                {
                    key: 'tableName',
                    title: '表名'
                },
                {
                    title: '集群名称',
                    key: 'clusterName'
                },
                {
                    title: '分片号',
                    key: 'shardingNo'
                },
                {
                    title: '加载方式',
                    key: 'loadMode',
                    render: (_, params) => {
                        let loadingMethodDom = <div>暂无数据</div>;
                        switch (params.row.loadMode) {
                            case '1':
                                loadingMethodDom = <div>追加</div>;
                                break;
                            case '2':
                                loadingMethodDom = <div>覆盖</div>;
                                break;
                            case '3':
                                loadingMethodDom = <div>追加+覆盖</div>;
                                break;
                        }
                        return loadingMethodDom;
                    }
                },
                {
                    title: '执行状态',
                    key: 'execStatus',
                    render: (_, params) => {
                        const text = EXECUTE_STATUS_OPTIONS.find(status => status.value === params.row.execStatus)?.label || '';
                        let sqlTableIconType;
                        switch (params.row.execStatus){
                            case 'running':
                                sqlTableIconType = 'loading';
                                break;
                            case 'succeeded':
                                sqlTableIconType = 'success';
                                break;
                            case 'failed':
                                sqlTableIconType = 'error';
                                break;
                            case 'pending':
                                sqlTableIconType = 'offline';
                                break;
                        }
                        return <div>
                            <importStatusTableIcon type={sqlTableIconType}/>
                            {text}
                        </div>;
                    }
                },
                {
                    title: '执行结果',
                    key: 'execDetail',
                    ellipsis: true
                },
                {
                    title: '操作',
                    key: 'action',
                    fixed: 'right',
                    width: 160,
                    render: (h, params) => {
                        return h('div', [
                            h('Button',
                                {
                                    props: {
                                        size: 'small',
                                        type: 'text'
                                    },
                                    on: {
                                        click: () => {
                                            this.logTableDrawerVisiable = true;
                                            this.logTableDrawerData = params.row;
                                        }
                                    }
                                },
                                '查看日志'
                            )
                        ]);
                    }
                }
            ],
            logTableHeight: '245px',
            logTableDrawerVisiable: false,
            logTableDrawerData: {}
        };
    },
    methods: {
        // 调用接口 历史上场信息查询
        async initData() {
            const param = {
                productId: this.productId
            };
            this.logTableData = [];
            this.historyTableData = [];
            try {
                this.historyTableLoading = true;
                const res = await getLoadDataHistory(param);
                if (res.success){
                    this.historyTableData = res?.data || [];
                }
            } catch (err) {
                this.$hMessage.error(err.message);
            }
            this.historyTableLoading = false;
        },
        // 调用接口，上场日志查询
        async handleLogTableData(val){
            const params = {
                execBatchId: val.execBatchId
            };
            this.logTableData = [];
            try {
                this.logTableLoading = true;
                const res = await getLoadDataResult(params);
                if (res.success){
                    this.logTableData = res?.data || [];
                }
            } catch (err) {
                this.$hMessage.error(err.message);
            }
            this.logTableLoading = false;
        },
        handleTableHeight(){
            const outerContainer = document.querySelector('.topic-box');
            const outerContainerHeight = outerContainer.clientHeight;
            const logTableHeight = Math.floor(outerContainerHeight / 2);
            const historyTableHeight = outerContainer.offsetHeight - logTableHeight - 10; // 底部编剧
            this.logTableHeight = logTableHeight - 62; // title 标题
            this.historyTableHeight = historyTableHeight - 62; // title 标题
        }
    },
    mounted(){
        window.addEventListener('resize', this.handleTableHeight);
        this.handleTableHeight();
    },
    beforeDestroy () {
        window.removeEventListener('resize', this.handleTableHeight);
    },
    render() {

        const historyTable = <obs-table
            isSimpleTable={true}
            ref="history-table"
            title={{ label: '历史上场信息' }}
            tableData={this.historyTableData}
            columns={this.historyTableColumn}
            loading={this.historyTableLoading}
            showTitle
            height={this.historyTableHeight}
            hasPage={false} />;

        const logTable = <obs-table
            isSimpleTable={true}
            ref="log-table"
            title={{ label: '上场日志' }}
            tableData={this.logTableData}
            columns={this.logTableColumn}
            loading={this.logTableLoading}
            showTitle
            height={this.logTableHeight}
            hasPage={false} />;

        const logTableDrawer = <h-drawer
            v-model={this.logTableDrawerVisiable}
            width="742"
            title={'上场日志-' + this.logTableDrawerData.tableName}
        >
            <drawerContent
                drawerData={this.logTableDrawerData}
                productId={this.productId}
            />
        </h-drawer>;

        return <div class="topic-box" style="height: auto;">
            {historyTable}
            {logTable}
            {logTableDrawer}
        </div>;
    },
    components: { obsTable, drawerContent, importStatusTableIcon }
};
