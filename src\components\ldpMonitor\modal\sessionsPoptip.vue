<template>
    <h-poptip
        v-model="visible"
        autoPlacement
        trigger="click"
        transfer
        class="poptip-search">
        <h-icon
            name="search"
            color="#298dff">
        </h-icon>
        <!-- poptip内表格 -->
        <div slot="content">
            <h-simple-table
                :columns="columns"
                :data="tableData"
                :loading="loading"
                height="150">
            </h-simple-table>
        </div>
    </h-poptip>
</template>

<script>
import { getTopoSessions } from '@/api/httpApi';
export default {
    name: 'SessionsPoptip',
    props: {
        productId: {
            type: String,
            default: ''
        },
        reqParams: {
            type: Object,
            default: () => {}
        }
    },
    data() {
        return {
            visible: false,
            columns: [
                {
                    title: '发送方插件实例',
                    key: 'senderNode'
                },
                {
                    title: '发送方地址',
                    key: 'senderAddr'
                },
                {
                    title: '连接方式',
                    key: 'connectType'
                },
                {
                    title: '接收方地址',
                    key: 'receiverAddr'
                },
                {
                    title: '接收方插件实例',
                    key: 'receiverNode'
                },
                {
                    title: '连接状态',
                    key: 'connectStatus'
                }
            ],
            tableData: [{}],
            loading: false
        };
    },
    mounted() {
    },
    methods: {
        // 获取topo会话信息
        async getTopoSessions() {
            let data = [];
            try {
                const params = {
                    productId: '',
                    type: this.nodeType,
                    senderNode: '',
                    receiverNode: '',
                    connectType: ''
                };
                const res = await getTopoSessions(params);
                if (res.code === '200') {
                    data = res?.data || [];
                } else {
                    this.$hMessage.error(res.message);
                }
            } catch (err) {
                console.error(err);
            }

            return data;
        }
    }
};
</script>

<style lang="less" scoped>
.poptip-search {
    margin-left: 2px;
    cursor: pointer;
}
</style>
