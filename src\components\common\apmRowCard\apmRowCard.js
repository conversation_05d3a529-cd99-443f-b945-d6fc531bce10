/**
 * <AUTHOR>
 * @email <EMAIL>
 * @create date 2025-01-08 09:30:23
 * @modify date 2025-01-08 09:39:58
 * @desc 通用化卡片组件
 */

import { defineComponent } from 'vue';
import './apmRowCard.less';

export default defineComponent({
    name: 'apmRowCard',
    props: {
        /**
         * 标题
         */
        title: {
            type: String
        },
        /**
         * 副标题
         */
        subTitle1: {
            type: String
        },
        /**
         * 副标题2，一般用于两个副标题直接有一个竖杠
         */
        subTitle2: {
            type: String
        },
        /**
         * 卡片显示数据集合
         * @param {IApmRowCardData}
         */
        list: {
            type: Array,
            default: () => []
        }
    },
    render() {
        return (
            <div class="apm-row-card">
                {
                    this.$slots.title
                        ? <div class="apm-row-card-soltTitle">
                            {this.$slots.title }
                        </div>
                        : (
                            <div class="apm-row-card-title">
                                <div class="apm-row-card-title-main" title={this.title}>{this.title}</div>
                                <div class="apm-row-card-title-subtile">
                                    <span title={this.subTitle1}>{this.subTitle1}</span>
                                    {!!this.subTitle1 && !!this.subTitle2 && <div class="apm-row-card-title-subtile-line" />}
                                    {!!this.subTitle2 && <div class="apm-row-card-title-subtile-subtitle2" title={this.subTitle2}>{this.subTitle2}</div>}
                                </div>
                            </div>
                        )
                }
                <div class="apm-row-card-content">
                    {
                        this.list.map(item => (
                            <div class="apm-row-card-content-item" key={item.title}>
                                <div class="apm-row-card-content-item-title" title={item.title}>
                                    {item.title}
                                </div>
                                <div class="apm-row-card-content-item-value" title={item.value}>
                                    {item.value}
                                </div>
                            </div>
                        ))
                    }
                </div>
            </div>
        );
    }
});
