<template>
      <div ref="wrapper" class="grid-layout-box">
        <grid-layout
          :layout.sync="layout"
          :col-num="colNum"
          :row-height="rowHeight"
          :is-draggable="false"
          :is-resizable="false"
          :is-mirrored="false"
          :vertical-compact="true"
          :margin="[10, 10]"
          :use-css-transforms="true"
          :style="gridItemStyle"
        >
          <grid-item
            v-for="(item, idx) in layout"
            :key="item.i"
            class="service-box"
            :x="item.x"
            :y="item.y"
            :w="item.w"
            :h="item.h"
            :i="item.i"
          >
            <component :is="component" :ref="component"
               :panel="panels[idx]"
               :selectedGroupId="selectedGroupId"
               :autoFill="autoFill"
               :spanNum="spanNum"
               @group-click="handleGroupClick" >
            </component>
          </grid-item>
        </grid-layout>
        <no-data v-if="!panels.length" />
      </div>
  </template>
<script>
import { GridLayout, GridItem } from 'vue-grid-layout';
import noData from '@/components/common/noData/noData';
import { isDivisible } from '@/utils/utils';
import businessBox from '@/components/ldpProduct/businessBox/businessBox';
import performBusinessBox from '@/components/ldpProduct/businessBox/performBusinessBox';
import clusterBusinessBox from '@/components/ldpProduct/businessBox/clusterBusinessBox';
export default {
    props: {
        panelsList: {
            type: Array,
            default: () => {
                return [];
            }
        },
        component: {
            type: String,
            default: 'performBusinessBox'
        },
        selectedGroupId: {
            type: String,
            default: ''
        },
        autoFill: {
            type: Boolean,
            default: false
        },
        spanNum: {
            type: Number,
            default: 3
        }
    },
    components: {
        GridLayout,
        GridItem,
        businessBox,
        performBusinessBox,
        clusterBusinessBox,
        noData
    },
    data() {
        return {
            layout: [],
            panels: [],
            rowHeight: 0,
            colNum: 12,
            layoutStu: true
        };
    },
    mounted() {
        window.addEventListener('resize', this.resetLayout);
        this.init();
    },
    watch: {
        panelsList() {
            this.init();
        }
    },
    methods: {
        // 初始化
        init() {
            this.generateLayout(this.panelsList);
            this.resetLayout();
        },
        handleGroupClick(id) {
            this.$emit('group-click', id);
        },
        // 重置layout
        resetLayout() {
            this.layoutStu = false;
            this.$nextTick(() => {
                this.rowHeight = parseInt(this.$refs.wrapper?.clientHeight / 10 - 5 * 2, 10);
                this.layoutStu = true;
            });
        },
        // 生成模型
        async generateLayout(panels) {
            this.colNum = isDivisible(panels.length);
            const w = this.colNum / panels.length;
            this.layout = [];
            this.panels = [];
            panels.forEach((item, index) => {
                this.layout.push({
                    x: index * w,
                    y: 0,
                    w,
                    h: 10,
                    i: item.id.toString()
                });
                this.panels.push({
                    ...item
                });
            });
        }
    },
    beforeDestroy() {
        window.removeEventListener('resize', this.resetLayout);
    },
    computed: {
        gridItemStyle() {
            return { minWidth: this.panels.length * 170 + 'px' };
        }
    }
};
</script>
  <style lang="less" scoped>
    .grid-layout-box {
        width: 100%;
        height: 100%;
        position: relative;
        overflow-x: auto;
        overflow-y: hidden;

        .service-box {
            border-radius: 8px;
            box-shadow: 0 1px 25px 0 rgba(58, 144, 247, 0.42) inset;
        }

        .app-grid-title {
            padding-left: 15px;
            color: #fff;
            font-size: 14px;
        }

        .app-grid-title::before {
            display: inline-block;
            position: relative;
            left: -10px;
            top: 2px;
            content: "";
            width: 4px;
            height: 15px;
            background: var(--link-color);
        }
    }
  </style>

