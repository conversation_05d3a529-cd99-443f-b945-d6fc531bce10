<template>
  <div class="main container">
    <table-sql-top
      v-show="isHome"
      ref="manage-top"
      class="title-line"
      :disabled="loadRuning"
      title="MDB数据导出"
      :showSelectNode="false"
      :showConfig="false"
      @loginSuccess="loginSuccess"
      @clear-data="clearData"
    >
    </table-sql-top>
    <div v-if="!isHome" class="title">
      <a-title :title="null" :class="'apm-title-create'">
        <template v-slot>
          <div class="create-title">
            <h-icon name="arrow-left-c" @on-click="backToHome"> </h-icon>
            <span>导出MDB数据</span>
          </div>
          <div class="product-select">
            <h-select
              v-model="productId"
              placeholder="请选择"
              placement="bottom"
              :positionFixed="true"
              disabled
              :clearable="false"
            >
              <h-option
                v-for="item in productList"
                :key="item.id"
                :value="item.productInstNo"
              >
                {{ item.productName }}
              </h-option>
            </h-select>
          </div>
        </template>
      </a-title>
    </div>
    <template v-if="showTab">
      <div v-if="isHome" class="container-tab">
        <h-tabs :value="tabName" @on-click="onChangeTab">
          <h-tab-pane label="MDB数据导出" name="export">
            <export-table
              ref="export"
              :productId="productId"
              @resetLogin="resetLogin"
              @goCreate="goCreate"
            />
          </h-tab-pane>
          <h-tab-pane label="导出历史" name="exportHistory">
            <export-history
              ref="exportHistory"
              :productId="productId"
              @resetLogin="resetLogin"
            />
          </h-tab-pane>
        </h-tabs>
      </div>
      <create-export
        v-if="!isHome && endpointId !== null"
        :productId="productId"
        :endpointId="endpointId"
        :instanceList="instanceList"
        @backToHome="backToHome"
        @resetLogin="resetLogin"
      />
    </template>
  </div>
</template>
<script>
import tableSqlTop from '@/components/sqlTable/tableSqlTop';
import aTitle from '@/components/common/title/aTitle';
import exportTable from './exportTable.vue';
import exportHistory from './exportHistory.vue';
import { mapState, mapActions } from 'vuex';
import createExport from './createExportTask/index.vue';

export default {
    components: {
        tableSqlTop,
        exportHistory,
        exportTable,
        createExport,
        aTitle
    },
    data() {
        return {
            loadRuning: false,
            loading: false,
            showTab: false,
            isHome: true,
            endpointId: null,
            instanceList: [],
            productId: localStorage.getItem('productInstNo'),
            tabName: 'export'
        };
    },
    computed: {
        ...mapState({
            productList: (state) => {
                return state.product.productListLight || [];
            }
        })
    },
    mounted() {
        this.init();
    // window.addEventListener('resize', this.resize);
    },
    methods: {
        ...mapActions({ getProductList: 'product/getProductListLight' }),
        async init() {
            this.loading = true;
            try {
                const productId = localStorage.getItem('productInstNo') || '';
                await this.$refs['manage-top'].init(productId);
                this.isFirstRender = true;
            } finally {
                this.loading = false;
            }
        },
        async loginSuccess() {
            this.showTab = true;
            this.onChangeTab('export');
            this.endpointId = this.$refs?.['manage-top'].endpointId;
            this.instanceList = this.$refs?.['manage-top'].instanceList;
        },
        /**
     * 点击tab切换
     */
        onChangeTab(tab) {
            this.tabName = tab;
            this.$nextTick(() => {
                if (this.$refs[tab]) {
                    this.$refs[tab].initData();
                }
            });
        },
        clearData() {
            this.showTab = false;
            this.isHome = true;
        },
        goCreate() {
            this.isHome = false;
        },
        backToHome() {
            this.isHome = true;
            this.init();
        },
        /**
     * 重置登录信息
     */
        resetLogin() {
            this.$refs?.['manage-top'] && this.$refs['manage-top'].setLoginInfo();
        }
    }
};
</script>
<style scoped lang="less">
@import url("@/assets/css/tab.less");

.main {
    // padding: 0 8px;
}

.container {
    /deep/ .apm-title {
        // width: calc(100% - 16px);
    }

    &-tab {
        height: calc(100% - 60px);

        /deep/ .h-tabs-bar {
            margin-bottom: 0;
        }

        /deep/ .h-tabs-content-wrap {
            // height: calc(100% - 60px);
            height: 100%;
        }
    }

    .title {
        min-width: 1000px;

        .product-select {
            float: right;
            margin-right: 15px;
            min-width: 200px;
            width: auto;
            display: flex;
            align-items: center;
            height: 100%;

            .h-select {
                display: block;
            }
        }

        .apm-title-create {
            &::before {
                display: none;
            }
        }
    }

    .create-title {
        float: left;

        .h-icon {
            cursor: pointer;
            margin-right: 8px;

            &:hover {
                color: var(--link-color);
            }
        }
    }
}
</style>
