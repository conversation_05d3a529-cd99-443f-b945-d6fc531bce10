/**
 * <AUTHOR>
 * @email <EMAIL>
 * @create date 2024-10-21 15:43:54
 * @modify date 2024-11-11 10:06:11
 * @desc [搜索下拉组件，支持远程搜索，或指定下拉列表搜索]
 */

import { defineComponent } from 'vue';
import VirtualList from 'vue-virtual-scroll-list';
import noData from '@/components/common/noData/noData';
import _ from 'lodash';
import SelectItem from './selectItem';
import { KEY_CODE  } from './constant';
import { getAllTarget } from './util';

import './apmSelectSearch.less';

export default defineComponent({
    name: 'apmSelectSearch',
    data() {
        return {
            keyword: null, // 搜索关键词
            loading: false, // 搜索loading
            selectedValue: this.value, // 选中的下拉项
            showList: false, // 控制显示下拉、搜索结果
            renderPlaceholder: this.placeholder, // 显示占位符
            renderAll: true, // 显示全集
            abortController: undefined, // 用户取消各种事件控制器
            focusItemIndex: undefined, // 当前按键上下滚动的数据索引
            requestList: [], // 远程搜索返回的数据
            focusStatus: false, // focus状态
            hoverContainer: false // hover状态
        };
    },
    components: { noData, VirtualList, SelectItem },
    props: {
        /**
         * 提示占位信息
         */
        placeholder: {
            type: String,
            default: () => '输入关键字'
        },
        /**
         * 下拉数据集合，仅本地搜索模式有效
         */
        list: {
            type: Array,
            default: () => []
        },
        /**
         * 搜索是否忽视大小写，仅本地搜索有效
         */
        ignoreUpperCase: {
            type: Boolean,
            default: () => true
        },
        /**
         * 是否允许清空
         */
        clearable: {
            type: Boolean,
            default: () => false
        },
        /**
         * 默认值
         */
        value: {
            default: () => undefined
        },
        /**
         * 是否将label和value一起返回给外部
         */
        labelInValue: {
            type: Boolean,
            default: () => false
        },
        /**
         * 是否启用远程搜索
         */
        remoteMode: {
            type: Boolean,
            default: () => false
        },
        /**
         * 远程搜索函数，仅在remoteMode为true时有效
         * 返回一个Promise<{label: string, value: string}[]>，
         * 也可以通过labelMapper、valueMapper来映射label、value
         * @type {(keyword: string) => Promise<{label: string, value: string}[]}
         */
        remoteMethod: {
            type: Function,
            default: () => undefined
        },
        /**
         * 远程搜索数据时，label的key映射
         */
        labelMapper: {
            type: String,
            default: () => 'label'
        },
        /**
         * 远程搜索数据时，value的key映射
         */
        valueMapper: {
            type: String,
            default: () => 'value'
        },
        /**
         * 动态宽度
         */
        width: {
            type: String,
            default: () => 'unset'
        },
        /**
         * 聚焦时宽度，一般用于，聚焦时宽度有变化
         */
        focusWidth: {
            type: String,
            default: () => 'unset'
        },
        /**
         * 最小宽度
         */
        minWidth: {
            type: String,
            default: () => 'unset'
        },
        /**
         * 最大宽度
         */
        maxWidth: {
            type: String,
            default: () => 'unset'
        },
        /**
         * 是否显示border
         */
        border: {
            type: Boolean,
            default: () => true
        },
        /**
         * 下拉框高度，不包含footer高度
         */
        dropHeight: {
            type: String,
            default: () => '333px'
        },
        /**
         * 使用模糊搜索，模糊搜索，会高亮所有匹配字符，否则只高亮从前面开始匹配的字符
         * 非模糊搜索则会从前面开始匹配。
         * 默认不启用
         */
        useFuzzy: {
            type: Boolean,
            default: () => false
        }

    },
    methods: {
        /**
         * 输入关键字搜索，默认高亮第一项
         */
        async onQuery(e) {
            try {
                this.loading = true;
                this.keyword = e.target.value;
                this.$refs['virtualList'] && this.$refs['virtualList'].scrollToIndex(0);
                this.renderAll = false;
                if (typeof this.remoteMethod !== 'function') {
                    console.warn('远程查询方法不正确');
                    return;
                }
                if (!this.remoteMode) return;
                const data = await this.remoteMethod(e.target.value);
                if (data?.length) {
                    this.requestList = data.map(item => ({
                        ...item,
                        label: item[this.labelMapper],
                        value: item[this.valueMapper]
                    }));
                } else {
                    this.requestList = [];
                }
            } finally {
                this.loading = false;
            }
        },
        /**
         * 选择搜索结果触发，参数label和value， label用来显示在输入框中
         * value用于传递到外层组件
         */
        onChangeSelect(item) {
            const label = item[this.labelMapper];
            const value = item[this.valueMapper];
            this.keyword = String(label);
            this.$refs['input'].blur();
            this.showList = false;
            this.focusItemIndex = 0;
            this.selectedValue = value;
            this.hoverContainer = false;
            this.focusStatus = false;
            this.$emit('onChange', this.labelInValue ? { value, label, ...item } : value);
        },
        /**
         * input聚焦时
         */
        onFocus() {
            this.renderAll = true;
            this.showList = true;
            this.focusStatus = true;
            if (this.remoteMode) {
                this.requestList = [];
                this.onQuery({ target: { value: this.keyword } });
            }
        },
        /**
         * 手动触发搜索
         */
        onSearch() {
            this.$refs['input'].focus();
            this.showList = true;
            if (this.remoteMode) {
                this.onQuery({ target: { value: this.keyword } });
            }
        },
        /**
         * 清空
         */
        onClear(e) {
            this.keyword = '';
            if (this.remoteMode) {
                this.requestList = [];
            } else {
                this.selectedValue = undefined;
            }
            this.$emit('onChange', this.labelInValue ? {} : null);
            e.stopPropagation();
            if (this.focusStatus) {
                this.$nextTick(() =>  this.$refs['input'].focus());
            }
        },
        /**
         * 重置输入框，不会触发onChange
         */
        reset() {
            this.keyword = '';
            this.selectedValue = undefined;
        },
        /**
         * 设置value，一般用于由外部设定值
         */
        setValue(value, keyword) {
            this.keyword = keyword;
            this.selectedValue = String(value);
        }
    },
    computed: {
        /**
         * 最终渲染的下拉框，根据搜索关键词过滤
         */
        renderList() {
            if (this.remoteMode) {
                // 远程搜索模式直接渲染搜索结果
                return this.requestList;

            }
            // 本地搜索匹配模式
            if (!Array.isArray(this.list)) return [];
            if (!this.keyword || this.renderAll) {
                return this.list.map(item => ({
                    ...item,
                    label: item[this.labelMapper],
                    value: item[this.valueMapper]
                }));
            }
            return this.list.filter((item) => {
                const keyword = String(this.keyword);
                const label = item[this.labelMapper].toString();
                if (this.useFuzzy) {
                    return this.ignoreUpperCase ? label.toLowerCase().includes(keyword.toLowerCase())
                        : label.includes(this.keyword);
                }
                return this.ignoreUpperCase ? label.toLowerCase().startsWith(keyword.toLowerCase())
                    : label.startsWith(keyword);
            });
        },
        virtualListStyle() {
            return {
                maxHeight: `calc(${this.dropHeight} - 30px)`,
                overflowY: 'auto'
            };
        }
    },
    mounted() {
        this.abortController =  new AbortController();
        const signal = this.abortController.signal;
        window.addEventListener('click', e => {
            if (this.$refs['footer'] && this.$refs['footer'].contains(e.target)) {
                // 忽视点击footer
                return;
            }
            // 整个input支持点击聚焦
            if (!this.showList && !this.focusStatus && this.$refs['container'].contains(e.target)) {
                this.onFocus();
                return;
            }
            if (!getAllTarget(this.$refs['input']).has(e.target)) {
                this.$refs['input'].blur();
                this.showList = false;
                this.focusStatus = false;
                if (!this.remoteMode) {
                    let keyword = this.renderList.find(item => item[this.valueMapper] === this.selectedValue)?.[this.labelMapper];
                    if (keyword === undefined) {
                        keyword = this.list.find(item => item[this.valueMapper] === this.selectedValue)?.[this.labelMapper];
                    }
                    this.keyword = keyword ?? this.selectedValue;
                }
            }
        }, { signal, capture: false });
        // 监听键盘上下选中、回车选择、esc退出
        window.addEventListener('keyup', e => {
            if (e.keyCode === KEY_CODE.ESC) {
                this.$refs['input'].blur();
                this.showList = false;
                this.focusStatus = false;
                return;
            }
            if (!this.$refs['virtualList']) return;
            if (e.keyCode === KEY_CODE.ENTER) {
                // 回车选中
                const curitem = this.renderList[this.focusItemIndex];
                if (this.focusItemIndex !== undefined && curitem !== undefined) {
                    this.onChangeSelect(curitem);
                }
                return;
            }
            if (e.keyCode === KEY_CODE.UP)  {
                if (this.focusItemIndex === undefined) {
                    this.focusItemIndex = 0;
                }
                if (this.focusItemIndex === 0) {
                    this.focusItemIndex = this.renderList.length - 1;
                } else {
                    this.focusItemIndex = this.focusItemIndex - 1;
                }
                this.$refs['virtualList'].scrollToIndex(this.focusItemIndex);
            } else if (e.keyCode === KEY_CODE.DOWN) {
                if (this.focusItemIndex === undefined) {
                    this.focusItemIndex = this.renderList.length - 1;
                }
                if (this.focusItemIndex === this.renderList.length - 1) {
                    this.focusItemIndex = 0;
                } else {
                    this.focusItemIndex = this.focusItemIndex + 1;
                }
                this.$refs['virtualList'].scrollToIndex(this.focusItemIndex);
            }
        }, { signal, capture: false });

        if (this.value !== undefined) {
            this.keyword = this.value;
        }
        if (this.$refs.container) {
            this.$refs.container.addEventListener('mouseenter', () => {
                this.hoverContainer = true;

            }, { signal, capture: false });
            this.$refs.container.addEventListener('mouseleave', () => {
                this.hoverContainer = false;
            }, { signal, capture: false });
        }
    },
    beforeDestroy() {
        this.abortController.abort();
    },
    // 动态宽度应该由外部决定，暂时先注释
    // watch: {
    //     width() {
    //         if (this.$refs['input'] && this.width && this.width !== 'unset') {
    //             const input = this.$refs['input'].$el?.children?.[0].children?.[0];
    //             if (!input) return;
    //             this.$nextTick(() => {
    //                 input.style.width = this.focusStatus ? '100%' : this.width;
    //                 input.style.minWidth = `calc(${this.minWidth} - 32px)`;
    //             });
    //         }
    //     },
    //     focusStatus() {
    //         if (this.focusStatus && this.width !== 'unset') {
    //             this.$nextTick(() => {
    //                 const input = this.$refs['input'].$el?.children?.[0].children?.[0];
    //                 if (!input) return;
    //                 input.style.width = '100%';;
    //             });
    //         }
    //     }
    // },
    render() {
        return (
            <div class="select-search"
                data-empty-result={this.renderList.length === 0}
                data-focus={this.focusStatus}
                data-border={this.border}
                style={{
                    width: this.showList ? this.focusWidth : !this.keyword ? 'auto' : this.width,
                    minWidth: this.minWidth,
                    maxWidth: this.maxWidth
                }}
                ref="container"
            >
                <h-input
                    placeholder={this.renderPlaceholder}
                    value={this.keyword}
                    v-on:on-change={_.debounce(this.onQuery, 300)}
                    ref='input'
                    v-on:on-focus={this.onFocus}
                >
                    <div slot="append" class="select-search-append" ref="input-append">
                        {this.clearable && (this.focusStatus || this.hoverContainer) && !!this.keyword &&
                        <svg onClick={this.onClear} ref="clearIcon" t="1730970250830" class="clear-icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="6450" width="200" height="200"><path d="M512 960C264.789333 960 64 759.210667 64 512S264.789333 64 512 64 960 264.789333 960 512 759.210667 960 512 960z m169.6-323.584L557.184 512l124.416-124.416a15.957333 15.957333 0 0 0 0-22.784l-22.784-22.784a15.957333 15.957333 0 0 0-22.826667 0L512 466.773333 387.584 342.4a15.957333 15.957333 0 0 0-22.784 0l-22.784 22.784a15.957333 15.957333 0 0 0 0 22.826667l124.373333 124.373333-124.373333 124.416a15.957333 15.957333 0 0 0 0 22.784l22.784 22.826667c6.4 6.4 16.384 6.4 22.784 0L512 557.994667l124.416 124.416c6.4 6.4 16.384 6.4 22.784 0l22.784-22.826667a16.810667 16.810667 0 0 0-0.384-23.168z" fill="#9296A1" p-id="6451"></path></svg>
                        }
                        <span onClick={this.onSearch} >
                            <h-icon class="select-search-append-search" name="search"/>
                        </span>
                    </div>
                </h-input>
                {this.showList && <div class="select-search-wrap">
                    <div>
                        <virtual-list
                            style={this.virtualListStyle}
                            data-key={this.valueMapper}
                            ref="virtualList"
                            data-sources={this.renderList}
                            data-component={SelectItem}
                            extra-props={{
                                keyword: String(this.keyword),
                                onChange: this.onChangeSelect,
                                focusItemIndex: this.focusItemIndex,
                                ignoreUpperCase: this.ignoreUpperCase,
                                useFuzzy: this.useFuzzy,
                                labelMapper: this.labelMapper
                            }}
                        />
                        {this.loading &&  <h-spin fix>
                            <h-icon name="load-c" size="18" class="spin-icon-load"></h-icon>
                        </h-spin> }
                    </div>
                    {this.renderList.length > 0 ? <div ref="footer" class="footer">
                        <div class="footer-select">
                            <svg t="1731292263317" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="3552" width="200" height="200"><path d="M866.084 235.428v386.288c0 17.952-12.936 32.884-29.996 35.98l-6.572 0.588H365.716v117.42a16 16 0 0 1-24.48 13.568L94.852 635.28a16 16 0 0 1 0-27.136l246.384-153.992a16 16 0 0 1 24.48 13.568v117.42h427.224V235.428a16 16 0 0 1 16-16h41.144a16 16 0 0 1 16 16z" p-id="3553" data-spm-anchor-id="a313x.manage_type_myprojects.0.i0.18bf3a81QZqys0" class="selected" fill="#9296A1"></path></svg>
                         选择</div>
                        <div class="footer-line" />
                        <div class="footer-esc">
                            <svg t="1731292351144" class="icon" viewBox="0 0 1795 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="3887" id="mx_n_1731292351145" width="256" height="256"><path d="M0 0m87.094738 0l1219.326335 0q87.094738 0 87.094739 87.094738l0 620.55001q0 87.094738-87.094739 87.094738l-1219.326335 0q-87.094738 0-87.094738-87.094738l0-620.55001q0-87.094738 87.094738-87.094738Z" fill="#262D43" p-id="3888" data-spm-anchor-id="a313x.manage_type_myprojects.0.i8.18bf3a81QZqys0" class=""></path><path d="M1306.421073 0c48.103513 0 87.094738 38.991226 87.094739 87.094738v620.55001c0 48.103513-38.991226 87.094738-87.094739 87.094738H87.094738c-48.103513 0-87.094738-38.991226-87.094738-87.094738V87.094738C0 38.991226 38.991226 0 87.094738 0h1219.326335z m0 65.321054H87.094738a21.773685 21.773685 0 0 0-21.746467 20.685L65.321054 87.094738v620.55001a21.773685 21.773685 0 0 0 20.685 21.746468l1.088684 0.027217h1219.326335a21.773685 21.773685 0 0 0 21.746468-20.685001l0.027217-1.088684V87.094738a21.773685 21.773685 0 0 0-20.685-21.746467L1306.421073 65.321054z" fill="#9296A1" p-id="3889"></path><path d="M198.009887 217.758619V596.054615h281.359552v-49.273848H255.873454V426.508377h201.733187v-49.273849h-201.733187V267.032467h214.470793v-49.273848zM671.010524 210.339236c-40.346637 0-73.791017 9.008862-100.333139 27.021143-28.137044 19.073748-41.936116 45.038867-41.936116 78.417925 0 32.845603 14.332528 58.277267 43.531039 75.761535 11.676138 6.357916 40.341194 16.428245 87.062077 31.261568 41.936116 12.715832 66.888759 21.191238 75.380496 25.431663 23.891175 12.18782 36.100769 28.610622 36.100769 49.807304 0 16.950813-8.491737 30.194657-25.486097 39.736974-16.983474 9.53143-39.813182 14.833323-68.478238 14.833323-31.854901 0-55.212621-5.829904-70.601172-17.484269-16.994361-12.715832-27.609032-34.440526-31.854901-64.112614H517.059687c3.184401 47.684369 20.173319 82.652907 51.494764 104.905612 26.01411 18.017724 62.109435 27.554598 108.296864 27.554598 47.776907 0 84.939143-9.536874 111.481265-28.610622 26.542121-19.607203 39.813182-46.09489 39.813182-80.007404 0-34.968537-16.455462-61.98968-48.838375-81.591439-14.86054-9.008862-48.310363-21.191238-99.271671-37.086028-35.567314-10.598341-57.335555-18.545736-65.827292-23.314173-19.106408-10.070329-28.137044-23.314173-28.137044-40.264986 0-19.073748 7.963725-32.851047 24.419188-41.326454 13.271061-7.419383 32.388356-11.126353 57.335554-11.126352 28.665056 0 49.899842 5.296449 64.765825 16.422801 14.86054 10.598341 24.419187 28.610622 29.726523 52.980818h57.335555c-3.717857-41.326453-18.58384-71.526554-45.125961-91.128313-24.947199-19.073748-59.453046-28.08261-103.51754-28.08261zM1046.334411 210.339236c-60.519956 0-106.173929 19.607203-138.028829 59.872189-28.665056 35.496549-42.469572 81.063428-42.469572 137.228647 0 57.2158 13.271061 102.782678 40.880093 136.689748 31.321445 39.208962 78.036885 59.344177 140.14632 59.344178 40.880093 0 75.913951-11.659808 105.112462-34.968538 31.321445-24.903652 50.961309-59.344177 59.453046-103.316133h-56.268644c-7.435713 29.666645-20.706774 51.919351-39.813182 66.758117-18.050384 13.771855-41.408105 20.663227-69.017137 20.663227-42.464128 0-73.785574-13.249287-93.958892-39.736975-19.111852-24.903652-28.670499-59.872189-28.670499-105.439067 0-43.971956 9.558648-78.940493 29.198511-104.372157 20.706774-28.08261 51.494764-41.859909 92.374856-41.859909 27.598145 0 49.899842 5.829904 67.944783 18.017724 18.050384 12.182377 30.265422 31.256124 36.634224 56.687788h56.268644c-5.840791-38.147495-22.824265-68.875608-50.433296-91.128313-28.665056-23.314173-65.293837-34.440526-109.358331-34.440526z" fill="#9296A1" p-id="3890"></path></svg>
                             隐藏搜索</div>
                    </div>
                        : <no-data width="80" height="52" text="没有找到匹配的数据，可尝试其它关键词进行搜索"/>
                    }
                </div>
                }
            </div>
        );
    }
});
