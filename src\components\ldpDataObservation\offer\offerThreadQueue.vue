<template>
    <div ref="tab-box" class="tab-box">
        <a-loading v-if="loading"></a-loading>
        <div v-else class="tab-child-box">
            <obs-table ref="table" :title="title" showTitle :tableData="tableData" :columns="columns" highlightRow rowSelectOnly :maxHeight="200" @on-current-change="tableRowcheckedChange" />
            <div v-if="tableData.length && Object.keys(currentRow).length" class="detail-grid-box">
                <obs-title :title="queueTitle" />
                <info-sum-bar class="detail-box" :data="offerQueueReqs"></info-sum-bar>
            </div>
        </div>
    </div>
</template>

<script>
import _ from 'lodash';
import aLoading from '@/components/common/loading/aLoading';
import obsTitle from '@/components/common/title/obsTitle';
import obsTable from '@/components/common/obsTable/obsTable';
import infoSumBar from '@/components/common/infoBar/infoSumBar';
import { getManagerProxy } from '@/api/mcApi';
const TOP_COUNT = 30;
export default {
    name: 'OfferThreadQueue',
    props: {
        nodeData: {
            type: Object,
            default: () => { }
        }
    },
    components: { aLoading, obsTable, infoSumBar, obsTitle },
    data() {
        return {
            loading: true,
            title: {
                label: '报盘工作线程队列信息'
            },
            tableData: [],
            columns: [
                {
                    title: '报盘序号',
                    key: 'OfferIndex',
                    ellipsis: true
                },
                {
                    title: '报盘名',
                    key: 'OfferName',
                    ellipsis: true
                },
                {
                    title: '报盘类型',
                    key: 'OfferType',
                    ellipsis: true
                },
                {
                    title: '报盘是否已启动',
                    key: 'Started',
                    ellipsis: true
                },
                {
                    title: '队列请求个数',
                    key: 'QueueCount',
                    ellipsis: true
                },
                {
                    title: '队列使用内存',
                    key: 'InUseSizeKB',
                    ellipsis: true
                }
            ],
            queueTitle: {
                label: {
                    labelDic: [{ key: 'OfferIndex', label: '报盘工作线程队列请求' }],
                    labelInfo: {
                        OfferIndex: '-'
                    }
                },
                slots: []
            },
            offerQueueReqs: {
                direction: 'row',
                details: [
                    {
                        type: 'text',
                        title: {
                            label: '队列前30个功能号',
                            slots: [
                                {
                                    type: 'text',
                                    label: '队列请求个数',
                                    value: ''
                                }
                            ]
                        },
                        info: {
                            key: 'topFunctionNo',
                            value: '-'
                        }
                    },
                    {
                        type: 'obj',
                        title: '报盘请求类型分组统计',
                        infoDic: [
                            {
                                label: 'TIMERMSG',
                                key: 'TIMERMSG'
                            },
                            {
                                label: 'TRADEMSG',
                                key: 'TRADEMSG'
                            },
                            {
                                label: 'POSTMSG',
                                key: 'POSTMSG'
                            },
                            {
                                label: 'FRAMEMSG',
                                key: 'FRAMEMSG'
                            }
                        ],
                        info: {}
                    },
                    {
                        type: 'obj',
                        title: '待出队功能号详情',
                        infoDic: [
                            {
                                label: '功能号',
                                key: 'FunctionNo'
                            },
                            {
                                label: '请求类型',
                                key: 'ReqType'
                            }
                        ],
                        info: {}
                    }
                ]
            },
            queueReqsList: [],
            currentRow: {}
        };
    },
    methods: {
        async initData() {
            this.loading = true;
            this.currentRow = {};
            await this.getFileData();
            this.loading = false;
        },
        // 表格切换选中行
        tableRowcheckedChange(currentRow) {
            if (currentRow?.OfferIndex !== this.currentRow?.OfferIndex){
                this.cleanOfferQueueReqs();
            }
            this.currentRow = currentRow;
            this.tableData.forEach(item => {
                item._highlight = item?.OfferIndex === this.currentRow?.OfferIndex; // 表格刷新后默认选中
            });
            this.setOfferQueueReqs(currentRow);
        },
        // 处理报盘工作线程队列请求数据
        setOfferQueueReqs(currentRow){
            this.queueTitle.label.labelInfo.OfferIndex = currentRow?.OfferIndex;
            const queueReqs = _.find(this.queueReqsList, ['OfferIndex', currentRow?.OfferIndex])?.Requests;
            // 获取队列前30个功能号列表
            const topFunctionNoList = [];
            // 请求类型分组统计
            const reqsGroupCount = {};
            Array.isArray(queueReqs) && queueReqs.forEach((item, idx) => {
                idx <= TOP_COUNT && topFunctionNoList.push(item?.FunctionNo);
                if (reqsGroupCount[item?.ReqType]) {
                    reqsGroupCount[item?.ReqType]++;
                } else {
                    reqsGroupCount[item?.ReqType] = 1;
                }
            });
            this.offerQueueReqs.details[0].title.slots[0].value = queueReqs?.length;
            this.offerQueueReqs.details[0].info.value = topFunctionNoList.join('、');
            this.offerQueueReqs.details[1].info = reqsGroupCount || {};
            this.offerQueueReqs.details[2].info = queueReqs?.[0] || {};
        },
        cleanOfferQueueReqs(){
            this.offerQueueReqs.details[0].info.value = '';
            this.offerQueueReqs.details[1].info = {};
            this.offerQueueReqs.details[2].info.value = {};
        },
        async getFileData() {
            const { getOfferQueueInfo, getOfferQueueReqs } = await this.getAPi();
            this.tableData = [...getOfferQueueInfo];
            this.queueReqsList = getOfferQueueReqs || [];
            if (this.tableData.length){
                const row = this.tableData?.find(v => v?.OfferIndex === this.currentRow?.OfferIndex);
                this.tableRowcheckedChange(Object.keys(this.currentRow).length && row ? row : this.tableData?.[0]);
            } else {
                this.cleanOfferQueueReqs();
            }
        },
        // 接口请求
        async getAPi() {
            const data = {
                getOfferQueueInfo: [],
                getOfferQueueReqs: []
            };
            const param = [
                {
                    manageProxyIp: this.nodeData.ip,
                    manageProxyPort: this.nodeData.manageProxyPort,
                    pluginName: 'ldp_offerproc',
                    funcName: 'GetOfferQueueInfo'
                },
                {
                    manageProxyIp: this.nodeData.ip,
                    manageProxyPort: this.nodeData.manageProxyPort,
                    pluginName: 'ldp_offerproc',
                    funcName: 'GetOfferQueueReqs'
                }
            ];
            try {
                const res = await getManagerProxy(JSON.stringify(param));
                if (res.code === '200') {
                    res.data?.[0]?.OfferQueueInfo?.length && (data.getOfferQueueInfo = res.data[0].OfferQueueInfo);
                    res.data?.[1]?.OfferQueueReqs?.length && (data.getOfferQueueReqs = res.data[1].OfferQueueReqs);
                }
                return data;
            } catch (err) {
                this.$emit('clear');
            }
        }
    }
};
</script>
<style lang="less" scoped>
.tab-box {
    height: 100%;
    overflow: hidden;

    .tab-child-box {
        height: 100%;

        .obs-table {
            height: auto;
        }
    }

    /deep/ .info-process-text {
        span {
            overflow: hidden;
            white-space: pre-wrap;
            text-overflow: ellipsis;
            // stylelint-disable property-no-vendor-prefix,value-no-vendor-prefix
            word-break: break-all;
            display: -webkit-box;
            -webkit-box-orient: vertical;
            -webkit-line-clamp: 3;
        }
    }

    .detail-grid-box {
        & > .obs-title {
            background: var(--wrapper-color);
            margin-top: 15px;
        }
    }

    .detail-box {
        margin-top: 0;

        /deep/ .obs-title {
            padding: 0 0 0 10px;

            .title-text {
                max-width: 40%;
            }

            .title-text,
            .title-box p {
                display: inline-block;
                color: var(--font-opacity-color);
                line-height: 42px;
                margin-left: 0;
                font-size: 12px;
                font-size: var(--font-size);
                padding: 0 5px;
                overflow: hidden;
                white-space: nowrap;
                text-overflow: ellipsis;
            }

            &::before {
                display: none;
            }
        }
    }
}
</style>
