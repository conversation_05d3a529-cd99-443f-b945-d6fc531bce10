<template>
    <div class="select-content">
        <div class="box-left">
            <h-form ref="formDynamic" :model="formDynamic" :label-width="80" :showTipsOnlyFocus="true">
                <h-form-item label="SELECT">
                    <h-row>
                        <h-col span="24">
                            <p class="select-tooltip">{{ selectNames }}</p>
                        </h-col>
                    </h-row>
                </h-form-item>
                <h-form-item label="FROM">
                    <h-row class="row">
                        <h-col span="11">
                            <h-input v-model="tableName" :disabled="true"></h-input>
                        </h-col>
                        <h-col span="1">&nbsp;</h-col>
                        <h-col span="11">
                            <h-select v-model="formDynamic.expandAttributes" filterable placeholder="内存表属性" :transfer="true">
                                <h-option v-for="item in boxInfo.expandAttributes" :key="item" :value="item">{{
                                    item
                                }}</h-option>
                            </h-select>
                        </h-col>
                        <h-col span="1">
                            <h-poptip class="popo-icon apm-poptip" placement="right">
                                <h-icon name="help-circled" size="20"></h-icon>
                                <template v-slot:content>
                                    <div style="width: 180px; white-space: normal;">
                                        内存表属性：该属性用于按业务场景来区分表内数据。不同的业务场景下，可能会重用相同的内存表结构，但实际代表的数据语义不同。即内存表+内存表属性代表唯一的业务内存表。
                                    </div>
                                </template>
                            </h-poptip>
                        </h-col>
                    </h-row>
                </h-form-item>
                <h-form-item label="WHERE">
                    <h-row v-for="(item, index) in formDynamic.items" :key="'items' + index" class="row">
                        <h-col span="6">
                            <h-form-item :prop="'items.' + index + '.fieldName'" required>
                                <h-select v-model="item.fieldName" set-def-select :positionFixed="true" :clearable="false"
                                    @on-change="changeFile(index)">
                                    <h-option v-for="fieldName in fieldNames" :key="fieldName" :value="fieldName">{{
                                        fieldName }}</h-option>
                                </h-select>
                            </h-form-item>
                        </h-col>
                        <h-col span="1">&nbsp;</h-col>
                        <h-col span="4">
                            <h-form-item :prop="'items.' + index + '.sign'" required>
                                <h-select v-model="item.sign" set-def-select :positionFixed="true" :clearable="false">
                                    <h-option v-for="signItem in item.signList" :key="signItem.value"
                                        :value="signItem.value">{{ signItem.label }}</h-option>
                                </h-select>
                            </h-form-item>
                        </h-col>
                        <h-col span="1">&nbsp;</h-col>
                        <h-col span="5">
                            <h-form-item :prop="'items.' + index + '.value'" :valid-rules="item.validateFunc" >
                                <h-input v-model.trim="item.value" placeholder="字段值"></h-input>
                            </h-form-item>
                        </h-col>
                        <h-col span="1">&nbsp;</h-col>
                        <h-col span="4">
                            <h-select v-model="item.logical" set-def-select :positionFixed="true" :clearable="false"
                                :disabled="index !== 0" @on-change="changelogical">
                                <h-option value="AND">AND</h-option>
                                <h-option value="OR">OR</h-option>
                            </h-select>
                        </h-col>
                        <h-col span="1">&nbsp;</h-col>
                        <h-col span="1">
                            <h-form-item>
                                <a-button type="error" shape="circle" size="small" icon="minus-round"
                                    @click="handleRemove(index)"></a-button>
                            </h-form-item>
                        </h-col>
                    </h-row>
                    <h-row>
                        <h-col span="12"> <h-form-item class="row">
                                <a-button type="ghost" shape="circle" size="small" icon="plus-round"
                                    @click="handleAdd"></a-button>
                            </h-form-item></h-col>
                    </h-row>
                </h-form-item>
            </h-form>
        </div>
        <div class="box-right">
            <a-button
                type="primary"
                :disabled="loading"
                style="margin-bottom: 5px;"
                @click="handleQuery">查询
            </a-button>
        </div>
    </div>
</template>

<script>
import aButton from '@/components/common/button/aButton';
import { comparatorConfig } from '@/config/exchangeConfig';
import { matchValidate } from '@/utils/validate';
import { loopAllData } from '@/utils/utils';
export default {
    components: { aButton },
    props: {
        loading: {
            type: Boolean,
            default: false
        },
        boxInfo: {
            tableName: {
                type: String,
                default: ''
            },
            expandAttributes: {
                type: Array,
                default: []
            },
            fields: {
                type: Array,
                default: []
            },
            describe: {
                type: String,
                default: ''
            }
        }
    },
    data() {
        return {
            formDynamic: {
                expandAttributes: '',
                items: []
            }
        };
    },
    mounted() {
    },
    computed: {
        selectNames: function () {
            const data = this.getfieldNames(this.boxInfo.fields)?.[0];
            return data.length ? data.join(', ') : '';
        },
        fieldNames: function () {
            const data = this.getfieldNames(this.boxInfo.fields)?.[1];
            return data || [];
        },
        tableName: function () {
            // 切换表名,手动清空
            this.formDynamic.expandAttributes = '';
            this.formDynamic.items = [];
            return this.boxInfo.tableName || '';
        }
    },
    methods: {
        // 遍历所有字段名
        getfieldNames(fields) {
            const selectNames = [];
            const fieldNames = [];
            loopAllData(fields, (data) => {
                if (data.visible) {
                    selectNames.push(data.path);
                }
                if (data.hasCondition) {
                    fieldNames.push(data.path);
                }
            });
            return [selectNames, fieldNames];
        },
        // 切换字段名
        changeFile(index) {
            const fieldName = this.formDynamic.items[index]?.fieldName;
            const { type, size } = this.getTypeOrSize(fieldName);
            this.formDynamic.items[index].value = '';
            this.formDynamic.items[index].signList = type === 'bool' ? comparatorConfig['bool'] : type.indexOf('char') !== -1 ? comparatorConfig['char'] : comparatorConfig['default'];
            this.formDynamic.items[index].validateFunc = matchValidate(type, size);
        },
        // 切换比较符
        changelogical() {
            const logical = this.formDynamic.items[0]?.logical;
            this.formDynamic.items.map((item) => {
                item.logical = logical;
                return item;
            });
        },
        // 判断字段名类型,判断字段名size
        getTypeOrSize(fieldName) {
            let type = '';
            let size = '';
            loopAllData(this.boxInfo.fields, (data) => {
                if (data.path === fieldName) {
                    type = data.type;
                    size = data.size;
                }
            });
            return { type, size };
        },
        // 删除where条件
        handleRemove(index) {
            this.formDynamic.items.splice(index, 1);
        },
        // 增加where条件
        handleAdd() {
            this.formDynamic.items.push({
                fieldName: '',
                logical: '',
                value: '',
                sign: '',
                signList: [],
                validateFunc: null
            });
        },
        // 获取筛选条件
        getQueryCondition() {
            const { items, expandAttributes } = this.formDynamic;
            const filterCondition = {
                tableName: this.tableName,
                selectFields: expandAttributes || '',
                logical: 'AND',
                whereCondition: ''
            };
            let validFailed = false;

            if (items.length){
                this.$refs['formDynamic'].validate((valid) => {
                    if (valid) {
                        const condition = [];
                        let logic = '';
                        items.forEach((item) => {
                            const { fieldName, sign, value, logical } = item;
                            logic = logical;
                            condition.push([fieldName, sign, value].join('@'));
                        });
                        filterCondition.logical = logic;
                        filterCondition.whereCondition = condition.join('@' + logic + '@');
                    } else {
                        validFailed = true;
                    }
                });
            }
            return { filterCondition, validFailed };
        },
        // 查询
        handleQuery() {
            const { items, expandAttributes } = this.formDynamic;

            const filterCondition = {
                tableName: this.tableName,
                selectFields: expandAttributes || '',
                logical: 'AND',
                whereCondition: ''
            };

            if (items.length){
                this.$refs['formDynamic'].validate((valid) => {
                    if (valid) {
                        const condition = [];
                        let logic = '';
                        items.forEach((item) => {
                            const { fieldName, sign, value, logical } = item;
                            logic = logical;
                            condition.push([fieldName, sign, value].join('@'));
                        });
                        filterCondition.logical = logic;
                        filterCondition.whereCondition = condition.join('@' + logic + '@');
                        this.$emit('query', filterCondition);
                    } else {
                        this.$hMessage.error('表单验证失败!');
                        return;
                    }
                });
            } else {
                this.$emit('query', filterCondition);
            }
        }
    }
};
</script>

<style lang="less" scoped>
@import url("@/assets/css/input.less");
@import url("@/assets/css/poptip.less");

.select-content {
    width: 100%;
    padding: 10px 5px 10px 10px;
    display: flex;

    .select-tooltip {
        color: var(--font-color);
        max-height: 100px;
        overflow: auto;
    }

    .box-left {
        flex: 1;
        height: 100%;
    }

    .box-right {
        width: 100px;
        height: 100%;
        text-align: center;
    }

    .h-form-item {
        margin-bottom: 5px;
    }

    .popo-icon {
        margin: 0 0 0 5px;
        color: var(--font-color);
        cursor: pointer;
    }

    /* stylelint-disable-next-line selector-class-pattern  */
    /deep/ .h-form-item-required .h-form-item-requiredIcon {
        display: none;
    }

    .row {
        .h-col {
            max-width: 300px;
        }
    }
}
</style>

