<template>
    <div class="manage-box">
        <normal-table ref="table" formTitle="筛选条件" tableTitle="角色" :formItems="formItems" :columns="columns"
            :loading="loading" :tableData="tableData" showTitle :hasPage="false" :hasSetTableColumns="false"
            @query="handleQuery" @selection='tableSelection'>
            <div slot='btns' class='table-slot-box'>
                <a-button type="primary" :disabled="editable" @click="addOrEditRoleModal(false, 'add')">添加角色</a-button>
                <a-button type="dark" :disabled="editable" @click="handleClearConfirm">删除角色</a-button>
            </div>
        </normal-table>
        <!-- 添加、编辑角色 -->
        <add-or-edit-role-modal v-if="addOrEditRoleInfo.status" :modalInfo="addOrEditRoleInfo"
            @add-or-edit-role="addOrEditRole"></add-or-edit-role-modal>
        <!-- 权限管理 -->
        <role-pass-config-modal v-if="rolesPassInfo.status" :modalInfo="rolesPassInfo" @update="handleRolePermissionsRefresh"></role-pass-config-modal>
        <!-- 删除角色 -->
        <apm-msg-box v-if="deleteRoleInfo.status" :modalInfo="deleteRoleInfo"></apm-msg-box>
    </div>
</template>

<script>
import { getMdbRoles, createMdbRoles, updateMdbRoles, deleteMdbRoles } from '@/api/mdbPrivilegeApi';
import normalTable from '@/components/common/bestTable/normalTable/normalTable';
import addOrEditRoleModal from '@/components/mdbPrivilegeManage/modal/addOrEditRoleModal';
import rolePassConfigModal from '@/components/mdbPrivilegeManage/modal/rolePassConfigModal';
import apmMsgBox from '@/components/common/apmMsgBox/apmMsgBox';
import aButton from '@/components/common/button/aButton';
import { MDB_DELETE_ERROR } from '@/config/errorCode';
export default {
    name: 'RoleManage',
    components: { aButton, normalTable, addOrEditRoleModal, rolePassConfigModal, apmMsgBox },
    props: {
        productId: {
            type: String,
            default: ''
        },
        editable: {
            type: Boolean,
            default: false
        }
    },
    data() {
        return {
            loading: false,
            formItems: [
                {
                    type: 'input',
                    key: 'roleName',
                    label: '角色名：',
                    value: '',
                    placeholder: '请输入角色名'
                }
            ],
            columns: [
                {
                    type: 'selection',
                    width: 60,
                    align: 'center'
                },
                {
                    title: '角色名',
                    key: 'roleName',
                    ellipsis: true,
                    render: (h, params) => {
                        return h('div', [
                            h('span', {
                                attrs: {
                                    title: params?.row?.roleName
                                },
                                style: {
                                    display: 'inline-block',
                                    maxWidth: '95%',
                                    marginRight: '5px',
                                    overflow: 'hidden',
                                    textOverflow: 'ellipsis',
                                    whiteSpace: 'nowrap',
                                    verticalAlign: '-2px'
                                }
                            }, params?.row?.roleName),
                            !params?.row?.enableAuth ? h('Poptip',
                                {
                                    class: 'apm-poptip',
                                    props: {
                                        trigger: 'hover',
                                        placement: 'top',
                                        positionFixed: true
                                    }
                                },
                                [
                                    h('icon', {
                                        props: {
                                            name: 'prompt',
                                            color: '#FF9901'
                                        }
                                    }),
                                    h('div',
                                        {
                                            slot: 'content',
                                            class: 'pop-content'
                                        },
                                        '角色未配置权限'
                                    )
                                ]) : ''
                        ]);
                    }
                },
                {
                    title: '角色描述',
                    key: 'roleDescribe',
                    ellipsis: true
                },
                {
                    title: '修改时间',
                    key: 'gmtModified',
                    ellipsis: true
                },
                {
                    title: '操作',
                    key: 'action',
                    fixed: 'right',
                    width: 180,
                    render: (h, params) => {
                        return h('div', [
                            h('Button',
                                {
                                    props: {
                                        size: 'small',
                                        type: 'text',
                                        disabled: params.row?.id?.includes('read-only') || this.editable
                                    },
                                    on: {
                                        click: () => { this.addOrEditRoleModal(params.row, 'edit'); }
                                    }
                                },
                                '编辑'
                            ),
                            h('Button',
                                {
                                    props: {
                                        size: 'small',
                                        type: 'text',
                                        disabled: params.row?.id?.includes('read-only') || this.editable
                                    },
                                    on: {
                                        click: () => {
                                            this.$hMsgBoxSafe.confirm({
                                                title: `删除`,
                                                content: `确认要删除角色"${params.row.roleName}"吗？`,
                                                onOk: () => {
                                                    this.handleDeleteRole(params.row.id);
                                                }
                                            });
                                        }
                                    }
                                },
                                '删除'
                            ),
                            h('Button',
                                {
                                    props: {
                                        size: 'small',
                                        type: 'text',
                                        disabled: params.row?.id?.includes('read-only') || this.editable
                                    },
                                    on: {
                                        click: () => { this.handleRolePermissionsModal(params.row); }
                                    }
                                },
                                '权限配置'
                            )
                        ]);
                    }
                }
            ],
            tableData: [],
            selection: [],
            // 增加、编辑弹窗
            addOrEditRoleInfo: {
                status: false
            },
            // 权限管理
            rolesPassInfo: {
                status: false
            },
            // 删除角色
            deleteRoleInfo: {
                status: false,
                width: '600',
                title: '',
                contentType: 'table',
                columns: [
                    {
                        title: '角色名',
                        key: 'role',
                        ellipsis: true
                    },
                    {
                        title: '绑定用户',
                        key: 'users',
                        ellipsis: true,
                        render: (h, params) => {
                            return h(
                                'span',
                                {
                                    attrs: {
                                        title: params.row?.users?.join(',') || '-'
                                    }
                                },
                                params.row?.users?.join(',') || '-'
                            );
                        }
                    }
                ],
                tableData: []
            }
        };
    },
    mounted() {
    },
    methods: {
        // 初始化数据
        initData() {
            // 表格重查询数据
            this.$nextTick(() => {
                this.$refs['table'].$_handleResetPageDataAndQuery();
            });
        },
        // 查询
        async handleQuery(val) {
            this.selection = [];
            try {
                this.loading = true;
                const param = {
                    productId: this.productId,
                    roleName: val.roleName
                };
                const res = await getMdbRoles(param);
                if (res.code === '200') {
                    this.tableData = res?.data || [];
                    Array.isArray(this.tableData) && this.tableData.forEach(item => {
                        item._disabled = item?.id?.includes('read-only') || this.editable;
                    });
                } else if (res.code.length === 8) {
                    this.$hMessage.error(res.message);
                }
                this.$emit('check-status');
                this.loading = false;
            } catch (err) {
                this.loading = false;
                this.tableData = [];
            }
        },
        // 新增、编辑
        addOrEditRoleModal(param, type) {
            this.addOrEditRoleInfo = param ? {
                ...param,
                type: type,
                status: true
            } : {
                roleName: '',
                roleDescribe: '',
                type: type,
                status: true
            };
        },
        async addOrEditRole(val) {
            let param = {};
            let apiFunction = null;
            let successMessage = '';

            if (val.type === 'add') {
                param = {
                    productId: this.productId,
                    roleName: val.roleName,
                    roleDescribe: val.roleDescribe
                };
                apiFunction = createMdbRoles;
                successMessage = '创建角色成功!';
            } else if (val.type === 'edit') {
                param = {
                    id: val.id,
                    roleName: val.roleName,
                    roleDescribe: val.roleDescribe
                };
                apiFunction = updateMdbRoles;
                successMessage = '修改角色成功!';
            }

            try {
                const res = await apiFunction(param);
                if (res?.code === '200') {
                    this.$nextTick(() => {
                        this.$refs['table'].$_handleQuery();
                    });
                    this.$hMessage.success(successMessage);

                    // 角色新增时默认打开权限管理
                    if (val.type !== 'add') return;
                    this.handleRolePermissionsModal({
                        id: res?.data?.id,
                        roleName: res?.data?.roleName
                    });

                } else if (res.code.length === 8) {
                    this.$hMessage.error(res.message);
                }
            } catch (error) {
                console.log(error);
            }
        },
        // 选中行
        tableSelection(selection) {
            this.selection = selection;
        },
        // 删除行确认
        async handleDeleteRole(id) {
            const ids = [id.toString()];
            await this.deleteRole(ids);
        },
        // 删除
        async deleteRole(ids) {
            const param = {
                productId: this.productId,
                ids: ids
            };
            const res = await deleteMdbRoles(param);
            if (res.code === '200') {
                this.$hMessage.success('删除成功!');
            } else if (res.code === MDB_DELETE_ERROR) {
                const roles = res?.data.map(o => o.role);
                this.deleteRoleInfo = {
                    ...this.deleteRoleInfo,
                    status: true,
                    title: `角色"${roles?.length <= 1 ? roles?.join(',') : roles?.slice(0, 1)?.join(',') + '...'}"被用户绑定使用中，无法删除。请从以下用户中解绑该角色后再删除！`,
                    tableData: res?.data || []
                };
            } else if (res.code.length === 8 && res.code !== MDB_DELETE_ERROR) {
                this.$hMessage.error(res.message);
            }
            this.$refs['table'].$_handleQuery();
            this.selection = [];
        },
        // 批量删除
        async handleClear() {
            const ids = this.selection.map(v => v.id.toString());
            await this.deleteRole(ids);
        },
        // 批量删除确认
        handleClearConfirm() {
            this.$hMessage.destroy();
            if (!this.selection.length) {
                this.$hMessage.info('请选择一条或多条数据');
                return;
            }
            const ids = this.selection.map(v => v.id.toString());
            const id = ids.filter(o => o?.includes('read-only'));
            if (id?.length){
                this.$hMessage.warning('read only为系统内置角色,不可删除,请勿勾选！');
                return;
            }
            this.$hMsgBoxSafe.confirm({
                title: '批量删除角色',
                content: '确认要批量删除已选中的角色吗？',
                onOk: async () => {
                    await this.handleClear();
                }
            });
        },
        // 权限管理
        handleRolePermissionsModal(param) {
            this.rolesPassInfo = {
                status: true,
                productId: this.productId,
                roleId: param.id,
                roleName: param.roleName,
                permissionsType: param?.type || 'TABLE'
            };
        },
        handleRolePermissionsRefresh(){
            this.$refs['table'].$_handleQuery();
        }
    }
};
</script>
<style lang="less">
@import url("@/assets/css/poptip-1.less");
</style>
<style lang="less" scoped>
.manage-box {
    width: 100%;
    height: 100%;

    .table-slot-box > .h-btn {
        margin-right: 10px;
    }

    .best-table {
        padding: 0;
    }

    /deep/.table-slot-box > .h-btn.h-btn-disable {
        background-color: #33394e;
        border-color: #33394e;
        color: #969797;
    }
}
</style>
