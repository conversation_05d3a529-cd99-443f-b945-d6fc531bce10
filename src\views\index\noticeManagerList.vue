<!--
 * @Description: 告警管理人查询页面
 * @Author: <PERSON><PERSON>
 * @Date: 2022-09-06 09:31:38
 * @LastEditTime: 2023-02-22 11:04:24
 * @LastEditors: liuxy <EMAIL>
-->
<template>
    <div class="main">
        <a-title title="告警人员管理"></a-title>
        <normal-title-table ref="table" title="告警人员" :formItems="formItems" :columns="columns" :loading="loading"
            :tableData="tableData" :total="total" :hasSetTableColumns="false" @query="handleQuery">
            <slot>
                <a-button type="dark" @click="addAddressee">添加</a-button>
            </slot>
        </normal-title-table>
        <add-or-update-manager-Modal :modalInfo="modalInfo" @update="handleResetPageDataAndQuery" />
    </div>
</template>

<script>
import normalTitleTable from '@/components/common/bestTable/normalTitleTable';
import aButton from '@/components/common/button/aButton';
import aTitle from '@/components/common/title/aTitle';
import addOrUpdateManagerModal from '@/components/sms/addOrUpdateManagerModal.vue';
import { getAddressee, deleteAddressee } from '@/api/httpApi';
export default {
    name: 'NoticeManagerList',
    data() {
        return {
            loading: false,
            formItems: [
                {
                    type: 'input',
                    label: '干系人名称',
                    key: 'addresseeName',
                    value: ''
                },
                {
                    type: 'input',
                    key: 'mobile',
                    label: '手机号',
                    value: ''
                },
                {
                    type: 'input',
                    key: 'email',
                    label: '邮箱',
                    value: ''
                }
            ],
            modalInfo: {
                status: false,
                type: 0,
                data: {
                    id: '',
                    mobile: '',
                    email: '',
                    addresseeName: ''
                }
            },
            columns: [
                {
                    title: '通知干系人',
                    key: 'addresseeName'
                },
                {
                    title: '手机号',
                    key: 'mobile'
                },
                {
                    title: '邮箱',
                    key: 'email'
                },
                {
                    title: '操作',
                    key: 'action',
                    fixed: 'right',
                    width: 110,
                    render: (h, params) => {
                        return h('div', [
                            h('Button',
                                {
                                    props: {
                                        size: 'small',
                                        type: 'text'
                                    },
                                    on: {
                                        click: () => { this.updateAddressee(params.row); }
                                    }
                                },
                                '修改'
                            ),
                            h('Button',
                                {
                                    props: {
                                        size: 'small',
                                        type: 'text'
                                    },
                                    on: {
                                        click: () => {
                                            this.deleteAddressee(params.row);
                                        }
                                    }
                                },
                                '删除'
                            )
                        ]);
                    }
                }
            ],
            tableData: [],
            total: 0
        };
    },
    async mounted() {
        this.handleResetPageDataAndQuery();
    },
    methods: {
        handleResetPageDataAndQuery() {
            // 表格重查询数据
            this.$nextTick(() => {
                this.$refs['table'].$_clearCurrentCheckedKeys();
                this.$refs['table'].$_handleResetPageDataAndQuery();
            });
        },
        async handleQuery(val) {
            await this.queryAddressData(val);
            // 更新表格列和侧边栏多选项数据
            this.$refs['table'].$_init();
        },
        async queryAddressData(val) {
            try {
                const param = {
                    addresseeName: val.addresseeName,
                    mobile: val.mobile,
                    email: val.email,
                    page: val.page,
                    pageSize: val.pageSize
                };
                this.loading = true;
                const res = await getAddressee(param);
                this.loading = false;
                if (res.success) {
                    this.tableData = res.data.list || [];
                    this.total = res.data.totalCount;
                } else {
                    this.$hMessage.success('查询失败!');
                }
            } catch (err) {
                this.loading = false;
                this.$hMessage.error(err.message);
                this.tableData = [];
                console.log(err);
            }
        },
        // 添加干系人
        addAddressee() {
            this.modalInfo.status = true;
            this.modalInfo.type = 0;
            Object.keys(this.modalInfo.data).forEach(ele => {
                this.modalInfo.data[ele] = '';
            });
        },
        // 修改干系人弹窗
        updateAddressee(param) {
            this.modalInfo.status = true;
            this.modalInfo.type = 1;
            this.modalInfo.data = { ...param };
        },
        // 删除干系人
        deleteAddressee(param) {
            this.$hMsgBoxSafe.confirm({
                title: `删除`,
                content: `您确定删除名为"${param.addresseeName}"的干系人信息？`,
                onOk: async () => {
                    const res = await deleteAddressee({
                        id: param.id
                    });
                    if (res.success) {
                        this.$hMessage.success('删除成功');
                        this.handleResetPageDataAndQuery();
                    } else {
                        this.$hMessage.warning(res.message || '删除失败');
                    }
                }
            });
        }
    },
    components: { addOrUpdateManagerModal, normalTitleTable, aButton, aTitle }
};
</script>
