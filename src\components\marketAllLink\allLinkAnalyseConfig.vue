<template>
    <div>
        <h-msg-box
            v-model="modalData.status"
            width="70"
            height="70"
            :escClose="true"
            :max-height="400"
            title="链路配置"
            @on-open="handleOpen"
        >
            <h-form
                ref="formValid"
                :model="formItem"
                :rules="timeRule"
                :label-width="100">
                <div class="title">分析目标</div>
                <h-form-item label="选择市场" prop="exchangeId" required>
                    <h-radio-group v-model="formItem.exchangeId">
                        <h-radio v-for="item in formItem.exchangeList" :key="item.label" :label="item.label">{{item.value}}</h-radio>
                    </h-radio-group>
                </h-form-item>
                <h-form-item label="数据类型" prop="bizType" required>
                    <h-radio-group v-model="formItem.bizType">
                        <h-radio v-for="item in checkList" :key="item.name" :label="item.label">{{item.name}}</h-radio>
                    </h-radio-group>
                </h-form-item>
                <h-form-item label="传输链路" prop="spanFlag" required>
                    <h-radio-group v-model="formItem.spanFlag">
                        <h-radio v-for="item in marketLoopList" :key="item.value" :label="item.value">{{item.label}}</h-radio>
                    </h-radio-group>
                </h-form-item>
                <div class="title">分析指标</div>
                <h-form-item label="统计方式" prop="indicators" required>
                    <h-radio-group v-model="formItem.indicators">
                        <h-radio v-for="item in lineNormList" :key="item.label" :label="item.label"> {{item.value}}</h-radio>
                    </h-radio-group>
                </h-form-item>
                <div class="title">数据范围</div>
                <h-form-item label="选择日期" prop="date" required>
                    <h-date-picker
                        v-model="formItem.date"
                        type="date"
                        placeholder="选择日期"
                        :positionFixed="true"
                        placement="top-start"
                    ></h-date-picker>
                </h-form-item>
                <h-form-item label="统计范围" prop="exchangeTime" required>
                    <h-radio-group v-model="formItem.exchangeTime" @on-change="handleChangeRange">
                        <h-radio v-for="item in rangeList" :key="item.label" :label="item.label">{{item.value}}</h-radio>
                    </h-radio-group>
                </h-form-item>
                <h-form-item label="自定义范围" prop="time" required>
                    <h-time-picker
                        v-model="formItem.time"
                        confirm
                        type="timerange"
                        placeholder="选择时间"
                        :positionFixed="true"
                        placement="top-start"
                    ></h-time-picker>
                </h-form-item>
            </h-form>
            <template v-slot:footer>
                <a-button type="ghost" @click="cancel">取消</a-button>
                <a-button type="primary" style="margin-left: 8px;" @click="submitForm">提交</a-button>
            </template>
        </h-msg-box>
    </div>
</template>

<script>
import { marketLoopList, checkList } from '@/config/exchangeConfig';
import aButton from '@/components/common/button/aButton';
import _ from 'lodash';
export default ({
    props: {
        modalInfo: {
            type: Object,
            default: null
        }
    },
    data() {
        const timeRule = function (rule, values, callback) {
            if (values.length) {
                let startTime, endTime;
                values.forEach((ele, index) => {
                    const _arr = ele.split(':');
                    if (index) {
                        endTime = (Number(_arr[0])) * 3600 + (Number(_arr[1])) * 60 + (Number(_arr[2]));
                    } else {
                        startTime = (Number(_arr[0])) * 3600 + (Number(_arr[1])) * 60 + (Number(_arr[2]));
                    }
                });
                if ((endTime - startTime) > 18 * 3600) {
                    return callback(new Error('自定义范围不得超过18小时'));
                }
            }
            callback();
        };
        return {
            modalData: this.modalInfo,
            formItem: {
                spanFlag: 'NSQ',
                exchangeId: 'SSE',
                spans: [ // 链路指标
                    'LFL',
                    'ETC'
                ],
                indicators: 'avg',
                switch: true,
                date: new Date(),
                acountId: '',
                time: ['09:15:00', '15:00:00'],
                slider: [20, 50],
                exchangeTime: '',
                exchangeList: [],
                bizType: 'SM'
            },
            timeRule: {
                time: [{
                    validator: timeRule, trigger: 'blur'
                }]
            },
            checkList: checkList,
            // 链路数据集合
            marketLoopList,
            // 统计方式集合
            lineNormList: [
                {
                    label: 'avg',
                    value: '平均'
                },
                {
                    label: 'p95',
                    value: '95%分位数'
                },
                {
                    label: 'p5',
                    value: '5%分位数'
                },
                {
                    label: 'max',
                    value: '每秒最大'
                },
                {
                    label: 'min',
                    value: '每秒最小'
                }
            ],
            // 分位数指标
            quantileNormList: [
                {
                    label: 50,
                    value: '50%'
                },
                {
                    label: 60,
                    value: '60%'
                },
                {
                    label: 70,
                    value: '70%'
                },
                {
                    label: 80,
                    value: '80%'
                },
                {
                    label: 90,
                    value: '90%'
                },
                {
                    label: 95,
                    value: '95%'
                },
                {
                    label: 96,
                    value: '96%'
                },
                {
                    label: 97,
                    value: '97%'
                },
                {
                    label: 98,
                    value: '98%'
                },
                {
                    label: 99,
                    value: '99%'
                }
            ],
            // 时间范围
            rangeList: [
                {
                    label: 'ALL_DAY',
                    value: '日盘'
                },
                {
                    label: 'BIDDING',
                    value: '集合竞价'
                },
                {
                    label: 'MORNING',
                    value: '早盘'
                },
                {
                    label: 'AFTERNOON',
                    value: '午盘'
                }
            ]
        };
    },
    methods: {
        handleOpen() {
            Object.keys(this.modalInfo.data).forEach(key => {
                this.formItem[key] = this.modalInfo.data[key];
            });
        },
        submitForm() {
            this.$refs['formValid'].validate(valid => {
                if (valid) {
                    this.$emit('setInfoData', this.formItem);
                    this.modalInfo.status = false;
                }
            });
        },
        handleChangeRange(e) {
            const timeObj = _.find(this.formItem.exchangeList, o => { return o.label === this.formItem.exchangeId; })?.exchangeTimeMap[e];
            this.formItem.time = [timeObj.startTime, timeObj.endTime];
        },
        cancel() {
            this.modalInfo.status = false;
        }
    },
    components: {  aButton }
});
</script>

<style lang="less" scoped>
.title {
    position: relative;
    padding: 0 0 16px 20px;

    &::before {
        content: "";
        display: inline-block;
        position: absolute;
        left: 0;
        width: 4px;
        height: 17px;
        background: var(--link-color);
    }
}
</style>
