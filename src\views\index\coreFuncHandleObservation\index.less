.content {
    display: flex;
    flex-direction: column;
    height: calc(100% - 44px);
    overflow-x: auto;

    &-top {
        display: flex;
        height: 44px;
        width: calc(100vw - 16px);

        &-tab {
            flex: 1;

            .h-tabs-bar {
                border-bottom: none;
            }

            .h-tabs-nav-container {
                border-bottom: var(--border);
                padding-left: 20px;

                .h-tabs-tab {
                    height: 44px;
                    color: #fff;
                    padding: 10px 0;
                    margin-right: 20px;

                    &:last-child {
                        margin-right: 0;
                    }
                }

                .h-tabs-return,
                .h-tabs-enter {
                    height: 43px;
                    width: 20px;
                    padding: 0;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                }

                .h-tabs-ink-bar {
                    left: 8px;
                    width: unset !important;
                }

                .h-tabs-nav .h-tabs-tab-active {
                    color: #298dff;
                }

                .h-tabs-tab-disabled {
                    cursor: not-allowed;
                }
            }

            &[data-loading="true"] {
                .h-tabs-nav-container {
                    cursor: not-allowed;
                }
            }

            &[data-showarrow="true"] {
                .h-tabs-nav-container {
                    padding-left: 0;
                }
            }
        }

        &-operation {
            display: flex;
            align-items: center;
            border-bottom: var(--border);
            flex-wrap: nowrap;

            .h-select-selection {
                color: var(--font-color);
                background-color: var(--input-bg-color);
                border: var(--border);
                margin-right: 8px;
                flex: 1;
            }

            &-line {
                background: #474e6f;
                height: 26px;
                width: 1px;
                margin-right: 8px;
            }

            &-setting {
                background: #262d43;
                border: 1px solid #485565;
                border-radius: 4px;
                cursor: pointer;

                .h-icon {
                    color: #fff;
                    width: 32px;
                    height: 32px;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                }
            }

            &-type {
                width: 96px;
                background: #262d43;
                border: 1px solid #485565;
                border-radius: 4px;
                display: flex;
                flex-wrap: nowrap;
                align-items: center;
                height: 32px;
                justify-content: space-between;
                padding: 4px;
                margin-right: 8px;

                &-item {
                    cursor: pointer;
                    width: 44px;
                    text-align: center;
                    height: 100%;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    color: #9296a1;

                    &[data-current="true"] {
                        background: #383f59;
                        border-radius: 2px;
                        color: #fff;
                    }
                }
            }
        }
    }

    &-wrap {
        height: calc(100% - 44px);
        flex: 1;
        overflow-y: auto;

        &-table {
            td,
            .h-table-cell {
                text-align: right !important;
            }

            .core-func-left {
                .h-table-cell {
                    text-align: left !important;
                }
            }
        }
    }

    &-list {
        display: flex;
        flex-wrap: wrap;
        height: inherit;
        align-content: baseline;

        &-row {
            display: flex;

            &-item {
                margin-top: 15px;
                height: 109px;
                transition: all 0.3s;
            }
        }

        .empty-funcs {
            margin-top: 200px;
        }
    }
}

@media only screen and (max-width: 1014px) {
    .content-list {
        min-width: 983px;

        &[data-big-than-two="true"] {
            overflow-x: auto;
        }

        &[data-big-than-two="false"] {
            overflow-x: unset;
            min-width: unset;

            .content-list-row-item {
                min-width: 480px;
            }
        }
    }

    .content-list-row-item {
        width: 50%;
        box-sizing: border-box;

        &:nth-child(2n-1) {
            padding-right: 10px;
        }
    }
}

@media only screen and (min-width: 1014px) {
    .content-list-row-item {
        width: 50%;
        box-sizing: border-box;

        &:nth-child(2n-1) {
            padding-right: 10px;
        }
    }
}

@media only screen and (min-width: 1681px) {
    .content-list-row-item {
        width: 33.3%;
        margin-top: 15px;
        margin-right: 0 !important;
        margin-left: 0 !important;
        box-sizing: border-box;
        padding-right: 0 !important;

        &[data-is-third-count="true"] {
            padding-left: 10px;
            padding-right: 10px !important;
        }
    }
}

.main-loading-wraper {
    position: fixed;
    width: 300px;
    height: 300px;
    top: 50%;
    left: 50%;
    transform: translateX(-50%) translateY(-50%);

    .spin-container {
        background: var(--main-color);
    }
}
