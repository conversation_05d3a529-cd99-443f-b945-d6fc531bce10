<!--
 * @Description: 行情监控
 * @Author: <PERSON><PERSON>
 * @Date: 2022-10-10 15:24:23
 * @LastEditTime: 2023-07-05 14:27:59
 * @LastEditors: <PERSON><PERSON>
-->
<template>
    <div class="main">
        <div class="main-top">
            <span class="currenttime">今天是: {{ currentTime }}</span>
            <p>{{ $getProductType(productInfo.productType) }}时延监控</p>
            <h-select v-show="productList.length > 1" v-model="productInstNo" class="securities"
                placeholder="请选择" :positionFixed="true"
                :clearable="false" @on-change="checkProduct">
            <h-option v-for="item in productList" :key="item.id" :value="item.productInstNo">{{ item.productName
            }}</h-option>
        </h-select>
        </div>
        <a-loading v-if="loading"></a-loading>
        <div v-if="exchangeIdList.length" class="wrapper">
            <template v-if="applicationViewMode">
                <a-title :title="getExchangeName(exchangeId)">
                    <slot>
                        <span style="position: absolute; left: 150px;">
                            显示拓扑
                            <h-switch v-model="switch1"></h-switch>
                        </span>
                        <!--市场类型选择-->
                        <h-select v-model="exchangeId" class="title-select echange-select" :setDefSelect="true"
                            :clearable="false" @on-change="handleExchange">
                            <h-option v-for="item in exchangeIdList" :key="item.value" :value="item.value">{{ item.label
                            }}</h-option>
                        </h-select>
                        <!--度量数据类型选择-->
                        <h-select v-model="timeValue" class="title-select time-interval" :clearable="false">
                            <h-option v-for="item in timeIntervalList" :key="item.value" :value="item.value">{{ item.label
                            }}</h-option>
                        </h-select>
                        <!--度量数据类型选择-->
                        <h-select v-model="secondType" class="title-select second-select" :clearable="false">
                            <h-option v-for="item in secondTypeList" :key="item.key" :value="item.key">{{ item.value
                            }}</h-option>
                        </h-select>
                    </slot>
                </a-title>
                <div v-if="switch1" class="topo-box">
                    <allLink-topo
                        ref="topo"
                        :template="applicationViewMode"
                        :monitor="true"
                        :clickable="true"
                        :selectedNode="selectedNode"
                        style="position: absolute; left: 0; top: 0;"
                        @selectedChange="handleSelectedChange" />
                </div>
                <div v-if="selectedNode" class="echart-box" :style="updateChartBoxHeight()">
                    <line-chart v-for="(item, index) in marketList" :ref= "`line-chart${index}`" :key="index" :title="item.name"
                        :xData="item.xaxis" :yData="item.yaxis" :valueName="getNameByKey(secondType)"
                        style="width: 50%; max-height: 50%; min-height: 200px;" />
                </div>
                <div v-if="!selectedNode" class="bg-none" :style="updateChartBoxHeight()">
                    <no-data text="当前实例无采集时延走势信息"/>
                </div>
            </template>
            <template v-else>
                <no-data text="该模型文件暂无拓扑结构"/>
            </template>
        </div>
        <div v-else class="bg-none">
            <no-data text="该模型文件暂无拓扑结构"/>
        </div>
    </div>
</template>

<script>
import { formatDate, isJSON, cutZero, timeAccumulator } from '@/utils/utils';
import aLoading from '@/components/common/loading/aLoading';
import lineChart from '@/components/common/lineChart/lineChart';
import aTitle from '@/components/common/title/aTitle';
import noData from '@/components/common/noData/noData';
import allLinkTopo from '@/components/common/topo/allLinkTopo';
import { getTraceModels } from '@/api/productApi';
import { getMarketMonitorData } from '@/api/httpApi';
import _ from 'lodash';
import { mapState, mapActions } from 'vuex';
export default {
    data() {
        return {
            loading: false,
            switch1: true,
            exchangeId: '',
            currentTime: new Date(),
            interval: 1000,
            productInstNo: '',
            productInfo: {},
            exchangeTime: '', // 市场时间
            timer: null,
            secondType: 'avg',
            secondTypeList: [{
                key: 'avg',
                value: '平均值'
            }, {
                key: 'p50',
                value: '中位数'
            }, {
                key: 'max',
                value: '最大值'
            }, {
                key: 'min',
                value: '最小值'
            }],
            // 监控时间区间选择
            timeIntervalList: [{
                label: '最近五分钟',
                value: 300
            }, {
                label: '最近十五分钟',
                value: 900
            }, {
                label: '最近三十分钟',
                value: 1800
            }],
            marketList: [
                {
                    key: 'SM',
                    name: '快照行情(μs)',
                    xaxis: [],
                    yaxis: []
                }, {
                    key: 'IM',
                    name: '指数行情(μs)',
                    xaxis: [],
                    yaxis: []
                }, {
                    key: 'OBOE',
                    name: '逐笔委托(μs)',
                    xaxis: [],
                    yaxis: []
                }, {
                    key: 'TYT',
                    name: '逐笔成交(μs)',
                    xaxis: [],
                    yaxis: []
                }
            ],
            timeValue: 900,
            selectedNode: '', // 模型选中状态
            traceModel: {},
            exchangeIdList: []
        };
    },
    computed: {
        ...mapState({
            productList: state => {
                return state.product.productListLight;
            }
        }),
        // 链路模型
        applicationViewMode() {
            const tempList =
                this.traceModel?.latencyTopology && isJSON(this.traceModel?.latencyTopology)
                    ? JSON.parse(this.traceModel?.latencyTopology)
                    : [];
            let info = '';
            for (const ele of tempList) {
                if (ele?.meta?.modelPattern === 'link') {
                    info = ele;
                    break;
                }
            }
            return info;
        }
    },
    watch: {
        switch1(newVal, oldVal) {
            this.$nextTick(() => {
                if (newVal) {
                    this.$refs['topo'] && this.$refs['topo'].init();
                }
                for (const index of Object.keys(this.marketList)){
                    this.$refs[`line-chart${index}`] && this.$refs['line-chart' + index][0].resize();
                }
            });
        }
    },
    mounted() {
        this.init();
    },
    methods: {
        // 获取市场名称
        getExchangeName(value) {
            return _.find(
                this.exchangeIdList,
                ['value', value])?.label || '';
        },
        ...mapActions({ getProductList: 'product/getProductListLight' }),
        async init(){
            try {
                this.loading = true;
                await this.getProductList({ filter: 'supportMarketLatency' });
                const productInstNo = localStorage.getItem('productInstNo');
                this.productInstNo = _.find(this.productList, ['productInstNo', productInstNo])?.productInstNo || this.productList?.[0]?.productInstNo;
                this.currentTime = formatDate(new Date());
            } catch (e) {
                console.error(e);
            } finally {
                this.loading = false;
            }
        },
        resetData() {
            this.timer && clearInterval(this.timer);
            this.exchangeIdList = [];
            this.exchangeId = '';
            this.traceModel = {};
        },
        // 切换产品
        async checkProduct(e) {
            this.loading = true;
            this.resetData();
            if (this.productList.length) {
                this.productInfo = e ? _.find(this.productList, ['productInstNo', e]) : this.productList[0];
                localStorage.setItem('productInstNo', this.productInfo.productInstNo);
                try {
                    await this.getTraceModels();
                } catch (error) {
                    console.error(error);
                }
                this.$nextTick(() => {
                    this.checkAppViewMode();
                    this.handleUpdateTimer();
                });
            }
            setTimeout(() => {
                this.loading = false;
            }, 1000);
        },
        checkAppViewMode() {
            if (!this.applicationViewMode) return;
            this.$nextTick(() => {
                const links = this?.applicationViewMode?.links;
                const edges = this?.applicationViewMode?.edges;
                const nodes = this?.applicationViewMode?.nodes;
                for (const item of [links, edges, nodes]) {
                    if (Array.isArray(item)) {
                        for (const ele of item) {
                            if (ele?.attributes?.defaultSelect) {
                                this.selectedNode = ele.attributes.timeDelayTrendField;
                                // 手动更新下topo视图, 因为数据每次都会重置所以topo组件在全局只有一个
                                this.$nextTick(() => {
                                    this.$refs['topo'] && this.$refs['topo'].init();
                                });
                                return;
                            }
                        }
                    }
                }
            });
        },
        // 根据产品节点获取链路模型
        async getTraceModels() {
            const res = await getTraceModels({ productId: this.productInstNo });
            if (res?.code === '200') {
                this.traceModel = res?.data?.[0] || {};
                this.exchangeIdList = this.traceModel?.bizTags?.filter(o => o.key === 'exchangeId')?.[0]?.options || [];
                this.exchangeId = this.traceModel?.bizTags?.filter(o => o.key === 'exchangeId')?.[0]?.value || '';
            } else {
                this.traceModel = {};
                this.exchangeIdList = [];
                this.exchangeId = '';
            }
        },
        // 获取监控时延数据
        getMonitorDelayTrend() {
            const param = {
                productInstNo: this.productInstNo,      // 产品节点名：必填
                date: this.currentTime,                 // 交易日期
                exchangeId: this.exchangeId,            // 交易市场
                indicator: this.secondType,             // 查询指标
                instanceNameList: [this.selectedNode],  // 实例名列表
                dataTypes: ['SM', 'IM', 'OBOE', 'TYT'], // 数据类型，选填，默认所有
                interval: 1000,             // 间隔时间
                timeWindow: this.timeValue  // 时延趋势的时间窗口
            };
            getMarketMonitorData(param).then(res => {
                if (res.success) {
                    const list = res.data?.[0].trendChartDoInfo || [];
                    this.marketList.forEach((ele, index) => {
                        if (list.length) {
                            ele.xaxis = list[index]?.trendChart?.xaxis;
                            ele.yaxis = list[index]?.trendChart?.yaxis.map(e => { return cutZero((Number(e) / 1000 || 0).toFixed(3)); });
                        } else {
                            ele.xaxis = timeAccumulator(this.timeValue);
                            ele.yaxis = Array(this.timeValue).fill(0);
                        }
                    });
                    return;
                }
                this.$hMessage.error('获取图表监控时延数据失败！');
                this.timer && clearInterval(this.timer);
            }).catch(err => {
                console.log(err);
                this.timer && clearInterval(this.timer);
            });
        },
        // 更新定时器
        handleUpdateTimer() {
            this.timer && clearInterval(this.timer);
            this.getMonitorDelayTrend();
            this.timer = window.setInterval(() => {
                this.getMonitorDelayTrend();
            }, 5000);
        },
        // 切换市场时重置图表
        handleExchange() {
            this.$refs['topo'] && this.$refs['topo'].init();
        },
        // 动态计算echart-box 高度
        updateChartBoxHeight() {
            return { height: this.switch1 ? `calc(58% - 79px)` : '100%' };
        },
        // 根据KEY获取数组中对应value值
        getNameByKey(key) {
            return _.find(this.secondTypeList, ['key', key])?.value;
        },
        // 获取到拓扑点击变化
        handleSelectedChange(name) {
            this.selectedNode = name;
            this.getMonitorDelayTrend();
        }
    },
    beforeDestroy() {
        this.timer && clearInterval(this.timer);
    },
    components: { aLoading, lineChart, allLinkTopo, aTitle, noData }
};
</script>

<style lang="less" scoped>
@import url("@/assets/css/monitor.less");
@import url("@/assets/css/input.less");

// 区别于businessMonitor
.wrapper {
    display: flex;
    flex-direction: column;

    .topo-box {
        position: relative;
        width: 100%;
        height: 25%;
        margin: 2.5% auto;
        transition: height 1s;
    }

    .echart-box {
        position: relative;
        width: 100%;
        height: auto;
        overflow-x: hidden;
        overflow-y: auto;
        flex-grow: 1;
        display: flex;
        flex-wrap: wrap;
    }
}
</style>
