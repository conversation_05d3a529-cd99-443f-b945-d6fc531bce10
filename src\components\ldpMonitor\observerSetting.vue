<template>
    <div>
        <h-drawer v-model="formValid.status" class="setting-drawer" :mask-closable="true" title="拓扑显示设置" width="400"
            @on-close="handleDrawerClose">
            <div v-show="$route.query.scene !== 'rcmTopologyObservation'" class="title">
                应用
            </div>
            <div v-show="$route.query.scene !== 'rcmTopologyObservation'" class="item">
                <div class="item-label">显示未托管节点</div>
                <div class="item-view">
                    <h-switch v-model="formValid.excludeUnmanagedNode" />
                </div>
            </div>
            <div class="title">
                上下文
            </div>
            <div class="item">
                <div class="item-label">RCM配置</div>
                <div class="item-view">
                    <h-select v-model="rcmId"
                        :clearable="false" placeholder="配置" @on-change="onChangeRcm">
                        <h-option v-for="item in rcmList" :key="item.id" :value="item.id">
                            {{ item.name }}
                        </h-option>
                    </h-select>
                </div>
            </div>
            <div class="item">
                <div class="item-label">默认标签</div>
                <div class="item-view">
                    <h-select v-model="tags" :clearable="false"
                        multiple placeholder="标签" isCheckall @on-item-remove="saveCache" @on-drop-change="saveCache">
                        <h-option v-for="item in tagList" :key="item" :value="item">
                            {{ item }}
                        </h-option>
                    </h-select>
                </div>
            </div>
        </h-drawer>
    </div>
</template>

<script>
import { getRcmInstanceList, queryContextTags } from '@/api/rcmApi';
import { isJSON } from '@/utils/utils';
export default {
    props: {
        settingData: {
            type: Object,
            default: () => { }
        },
        productId: {
            type: String
        }
    },
    data() {
        return {
            formValid: this.settingData,
            rcmList: [],
            rcmId: '',
            tagList: [],
            tags: []
        };
    },
    name: 'ObserverSetting',
    methods: {
        handleDrawerClose() {
            this.$emit('onClose');
        },
        async init() {
            try {
                const res = await getRcmInstanceList({ productId: this.productId });
                this.rcmList = res?.data || [];
                if (this.rcmList[0]) {
                    this.rcmId = this.rcmList[0].id;
                }
            } catch (error) {
                console.log('查询rcm配置失败：', error);
            }
        },
        /**
         * 选择rcm配置
         */
        async onChangeRcm(id) {
            this.tagList = [];
            this.tags = [];
            try {
                const res = await queryContextTags({
                    rcmId: id
                });
                this.tagList = res?.data?.tags || [];
                const cache = localStorage.getItem('__rcm_tags_map__');
                if (this.tagList.length && isJSON(cache)) {
                    const config = JSON.parse(cache);
                    this.tags = config[this.productId]?.[id] || [];
                }
            } catch (error) {
                console.log('查询标签失败：', error);
            }
        },
        /**
         * 保存标签
         */
        saveCache() {
            if (!this.rcmId || !this.tagList.length) return;
            const cache = localStorage.getItem('__rcm_tags_map__');
            const config = isJSON(cache) ? JSON.parse(cache) : {};
            config[this.productId] = {
                ...config[this.productId],
                [this.rcmId]: this.tags
            };
            localStorage.setItem('__rcm_tags_map__', JSON.stringify(config));
            this.$emit('onChangeSetting', ({ key: 'tags', value: config[this.productId] }));
        }
    },
    mounted() {
        this.init();
    },
    watch: {
        'formValid.excludeUnmanagedNode'() {
            localStorage.setItem('rcm_excludeUnmanagedNode', this.formValid.excludeUnmanagedNode);
            this.$emit('onChangeSetting', ({ key: 'excludeUnmanagedNode', value: this.formValid.excludeUnmanagedNode }));
        }
    }
};
</script>
<style lang="less" scoped>
@import url("@/assets/css/drawer.less");

.setting-drawer {
    .title {
        padding-left: 6px;
        border-left: 4px solid #2d8de5;
        margin-top: 20px;
        color: #fff;
    }

    .item {
        margin-top: 15px;
        display: flex;
        align-items: center;

        .item-label {
            width: 84px;
            text-align: right;
            margin-right: 15px;
            color: #fff;
        }

        .item-view {
            width: 200px;
            position: relative;
        }
    }
}
</style>
