/*
 * 自定义steps组件样式
*/
@steps-tail-bg-color: #444a60;
@steps-text-color: #9296a1;

/deep/ .h-steps-item .h-steps-tail > i {
    background-color: @steps-tail-bg-color;
}

/deep/ .h-steps-status-wait .h-steps-head-inner {
    background-color: var(--wrapper-color);
    border-color: @steps-text-color;

    span {
        color: @steps-text-color;
    }
}

/deep/ .h-steps-status-finish .h-steps-head-inner {
    background-color: var(--wrapper-color);
    border-color: var(--link-color);
}

/deep/ .h-steps-status-process .h-steps-main .h-steps-title, {
    color: var(--link-color);
}

/deep/ .h-steps-status-finish .h-steps-main .h-steps-title,
/deep/ .h-steps-status-wait .h-steps-main .h-steps-title {
    color: @steps-text-color;
}

/deep/ .h-steps-item.h-steps-status-finish .h-steps-tail > i::after {
    background: var(--link-color);
}

/deep/ .h-steps-item .h-steps-main {
    position: absolute;
    top: -3px;
    left: 26px;
    padding: 0 10px;
    font-size: 14px;
    font-weight: 500;
    background: var(--wrapper-color);
}
