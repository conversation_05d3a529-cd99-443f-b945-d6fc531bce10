<!-- eslint-disable vue/no-deprecated-slot-attribute -->
<!--新增，编辑页面-->
<template>
    <div>
        <h-msg-box-safe
            v-model="dataInfo.status"
            title="配置物料生成范围"
            width="800"
            :mask-closable="false"
            @on-open="getCollections">
            <h-form
                v-if="Object.keys(formValidate).length"
                ref="formValidate"
                class="page-form"
                :model="formValidate"
                :rules="ruleValidate"
                :label-width="140"
                cols="2">
                <h-form-item label="传输地址">
                    <h-row>
                        <h-col span="11">
                            <h-form-item prop="multicastAddr.minValue" :validRules="ip4Rule">
                                <h-input v-model="formValidate.multicastAddr.minValue"></h-input>
                            </h-form-item>
                        </h-col>
                        <h-col span="2" class="tac">—</h-col>
                        <h-col span="11">
                            <h-form-item prop=".multicastAddr.maxValue" :validRules="ip4Rule">
                                <h-input v-model="formValidate.multicastAddr.maxValue"></h-input>
                            </h-form-item>
                        </h-col>
                    </h-row>
                </h-form-item>

                <h-form-item label="传输端口">
                    <h-row>
                        <h-col span="11">
                            <h-form-item prop="localPort.minValue">
                                <h-input
                                    v-model="formValidate.localPort.minValue"
                                    type="int"
                                    :maxlength="5" @on-blur="validateInput('localPort.maxValue')"></h-input>
                            </h-form-item>
                        </h-col>
                        <h-col span="2" class="tac">—</h-col>
                        <h-col span="11">
                            <h-form-item prop="localPort.maxValue">
                            <h-input
                                v-model="formValidate.localPort.maxValue"
                                type="int"
                                :maxlength="5"
                                 @on-blur="validateInput('localPort.minValue')"
                            ></h-input>
                            </h-form-item>
                        </h-col>
                    </h-row>
                </h-form-item>

                <h-form-item label="集群同步地址">
                    <h-row>
                        <h-col span="11">
                            <h-form-item prop="syncAddr.minValue" :validRules="ip4Rule">
                                <h-input v-model="formValidate.syncAddr.minValue"></h-input>
                            </h-form-item>
                        </h-col>
                        <h-col span="2" class="tac">—</h-col>
                        <h-col span="11">
                            <h-form-item prop="syncAddr.maxValue" :validRules="ip4Rule">
                                <h-input v-model="formValidate.syncAddr.maxValue"></h-input>
                            </h-form-item>
                        </h-col>
                    </h-row>
                </h-form-item>

                <h-form-item label="集群同步端口">
                    <h-row>
                        <h-col span="11">
                            <h-form-item prop="syncPort.minValue">
                                <h-input
                                    v-model="formValidate.syncPort.minValue"
                                    type="int"
                                    :maxlength="5"
                                    @on-blur="validateInput('syncPort.maxValue')"
                                ></h-input>
                            </h-form-item>
                        </h-col>
                        <h-col span="2" class="tac">—</h-col>
                        <h-col span="11">
                            <h-form-item prop="syncPort.maxValue">
                                <h-input
                                    v-model="formValidate.syncPort.maxValue"
                                    type="int"
                                    :maxlength="5"
                                    @on-blur="validateInput('syncPort.minValue')"></h-input>
                            </h-form-item>
                        </h-col>
                    </h-row>
                </h-form-item>

                <h-form-item label="补缺端口">
                    <h-row>
                        <h-col span="11">
                            <h-form-item prop="multicastPort.minValue">
                                <h-input
                                    v-model="formValidate.multicastPort.minValue"
                                    type="int"
                                    :maxlength="5"
                                    @on-blur="validateInput('multicastPort.maxValue')"></h-input>
                            </h-form-item>
                        </h-col>
                        <h-col span="2" class="tac">—</h-col>
                        <h-col span="11">
                            <h-form-item prop="multicastPort.maxValue">
                                <h-input
                                    v-model="formValidate.multicastPort.maxValue"
                                    type="int"
                                    :maxlength="5"
                                    @on-blur="validateInput('multicastPort.minValue')"></h-input>
                            </h-form-item>
                        </h-col>
                    </h-row>
                </h-form-item>

                <h-form-item label="上下文ID范围">
                    <h-row>
                        <h-col span="11">
                            <h-form-item prop="contextId.minValue">
                                <h-input
                                    v-model="formValidate.contextId.minValue"
                                    type="int"
                                    :maxlength="5"
                                    @on-blur="validateInput('contextId.maxValue')"></h-input>
                            </h-form-item>
                        </h-col>
                        <h-col span="2" class="tac">—</h-col>
                        <h-col span="11">
                            <h-form-item prop="contextId.maxValue">
                                <h-input
                                    v-model="formValidate.contextId.maxValue"
                                    type="int"
                                    :maxlength="5"
                                    @on-blur="validateInput('contextId.minValue')"></h-input>
                            </h-form-item>
                        </h-col>
                    </h-row>
                </h-form-item>

                <h-form-item label="主题ID范围">
                    <h-row>
                        <h-col span="11">
                            <h-form-item prop="transportId.minValue">
                                <h-input
                                    v-model="formValidate.transportId.minValue"
                                    type="int"
                                    :maxlength="5"
                                    @on-blur="validateInput('transportId.maxValue')"></h-input>
                            </h-form-item>
                        </h-col>
                        <h-col span="2" class="tac">—</h-col>
                        <h-col span="11">
                            <h-form-item prop="transportId.maxValue">
                                <h-input
                                    v-model="formValidate.transportId.maxValue"
                                    type="int"
                                    :maxlength="5"
                                    @on-blur="validateInput('transportId.minValue')"></h-input>
                            </h-form-item>
                        </h-col>
                    </h-row>
                </h-form-item>

                <h-form-item label="分区范围">
                    <h-row>
                        <h-col span="11">
                            <h-form-item prop="partitionNo.minValue">
                                <h-input
                                    v-model="formValidate.partitionNo.minValue"
                                    type="int"
                                    :maxlength="5"
                                    @on-blur="validateInput('partitionNo.maxValue')"></h-input>
                            </h-form-item>
                        </h-col>
                        <h-col span="2" class="tac">—</h-col>
                        <h-col span="11">
                            <h-form-item prop="partitionNo.maxValue">
                                <h-input
                                    v-model="formValidate.partitionNo.maxValue"
                                    type="int"
                                    :maxlength="5"
                                    @on-blur="validateInput('partitionNo.minValue')"></h-input>
                            </h-form-item>
                        </h-col>
                    </h-row>
                </h-form-item>
            </h-form>
            <template v-slot:footer>
                <h-button @click="cancelMethod">取消</h-button>
                <h-button :loading="btnLoading" type="primary" @click="saveSubmit">确定</h-button>
            </template>
        </h-msg-box-safe>
    </div>
</template>
<script>
import _ from 'lodash';
import { materialRange } from '@/config/rcmDefaultConfig';
import { updateRcmMaterialInfo } from '@/api/rcmApi';
export default {
    props: {
        modalInfo: {
            type: Object,
            default: null
        }
    },
    data() {
        const validatePortMin = (rule, value, callback) => {
            const key = rule.field.split('.')[0];
            if (value === '') {
                return callback(new Error('参数不能为空'));
            } else if (value < 1 || value > 65535) {
                return callback(new Error('请输入1到65535之间的端口值'));
            } else if (value > Number(this.formValidate[key].maxValue)) {
                return callback(new Error('minValue不能大于maxValue'));
            }
            callback();
        };
        const validatePortMax = (rule, value, callback) => {
            const key = rule.field.split('.')[0];
            if (value === '') {
                return callback(new Error('参数不能为空'));
            } else if (value < 1 || value > 65535) {
                return callback(new Error('请输入1到65535之间的端口值'));
            } else if (value < Number(this.formValidate[key].minValue)) {
                return callback(new Error('minValue不能大于maxValue'));
            }
            callback();
        };
        const validateNotEmpty = (rule, value, callback) => {
            if (value === '') {
                return callback(new Error('参数不能为空'));
            }
            callback();
        };

        return {
            btnLoading: false, // 按钮加载中状态，默认为false，防止重复点击
            formValidate: {},
            dataInfo: this.modalInfo,
            ip4Rule: ['ip4', { test: validateNotEmpty, trigger: 'change, blur' }],
            ruleValidate: {
                'localPort.minValue': [{ validator: validatePortMin, trigger: 'blur' }],
                'localPort.maxValue': [{ validator: validatePortMax, trigger: 'blur' }],
                'multicastPort.minValue': [{ validator: validatePortMin, trigger: 'blur' }],
                'multicastPort.maxValue': [{ validator: validatePortMax, trigger: 'blur' }],
                'syncPort.minValue': [{ validator: validatePortMin, trigger: 'blur' }],
                'syncPort.maxValue': [{ validator: validatePortMax, trigger: 'blur' }],
                'contextId.minValue': [{ validator: validatePortMin, trigger: 'blur' }],
                'contextId.maxValue': [{ validator: validatePortMax, trigger: 'blur' }],
                'transportId.minValue': [{ validator: validatePortMin, trigger: 'blur' }],
                'transportId.maxValue': [{ validator: validatePortMax, trigger: 'blur' }],
                'partitionNo.minValue': [{ validator: validatePortMin, trigger: 'blur' }],
                'partitionNo.maxValue': [{ validator: validatePortMax, trigger: 'blur' }]
            }
        };
    },
    methods: {
        validateInput(key) {
            this.$refs.formValidate.validateField(key);
        },
        getCollections() {
            this.formValidate = _.cloneDeep(this.dataInfo.materialRange);
            if (!Object.keys(this.formValidate || {})?.length) this.formValidate = materialRange;
        },
        // 更新应用配置
        editHandle() {
            this.btnLoading = true;
            const param = this.formatOrganization({ ...this.formValidate });
            updateRcmMaterialInfo({
                rcmId: this.modalInfo.rcmId,
                materialRange: param
            }).then((res) => {
                if (res.code === '200') {
                    this.$emit('update');
                    this.$hMessage.success('更新成功');
                    this.btnLoading = false;
                    this.modalInfo.status = false;
                } else {
                    this.btnLoading = false;
                }
            }).catch((err) => {
                console.log(err);
                this.btnLoading = false;
            });
        },
        formatOrganization(data) {
            Object.keys(data).forEach(item => {
                if (item !== 'multicastAddr' && item !== 'syncAddr') {
                    data[item].minValue = Number(data[item].minValue);
                    data[item].maxValue = Number(data[item].maxValue);
                }
            });
            return data;
        },
        saveSubmit() {
            this.$refs['formValidate'].validate(valid => {
                if (valid) { // 验证通过后，调取创建接口
                    this.editHandle();
                }
            });
        },
        cancelMethod() {
            this.dataInfo.status = false;
        }
    }
};
</script>

<style lang="less" scoped>
@blue: #2d8de5;

.tac {
    text-align: center;
}

/deep/ .h-modal-content {
    overflow: auto;
}

/deep/ .h-modal-body {
    height: ~"calc(100% - 115px)" !important;
}

/deep/ .h-select-dropdown {
    margin: 0;
}

/deep/ .verify-tip-inner {
    white-space: normal;
    line-height: 150%;
    text-align: left;
}

.page-form {
    padding: 0 15px 0 0;
}
</style>
