<!--
 * @Description: 短信外发数据查询页面
 * @Author: <PERSON><PERSON>
 * @Date: 2022-09-06 09:31:38
 * @LastEditTime: 2023-04-17 14:34:40
 * @LastEditors: <PERSON><PERSON>
-->
<template>
    <div class="main">
        <a-title title="告警通知管理">
            <slot>
                <div class="slot-box">
                    <a-button type="dark" class="btn-notice" @click="modalInfo.status = true">管理通知模版</a-button>
                </div>
            </slot>
        </a-title>
        <tab-title-table ref="tab" :tabTableData="tabTableData" :tableLoading="tableLoading" :remoteMethod="remoteMethod"
            style="margin-top: 10px;" :hasSetTableColumns="false" @query="handleQuery" />
        <noticeTempModal :modalInfo="modalInfo" />
        <h-msg-box v-model="noticeStatus" :escClose="true" title="告警通知参数" @on-ok="noticeStatus = false"
            @on-cancel="noticeStatus = false">
            <json-viewer :value="jsonData" :expand-depth="3" :expanded="true"></json-viewer>
        </h-msg-box>
        <h-msg-box v-model="outNoticeStatus1" :escClose="true" title="通知状态" @on-ok="outNoticeStatus1 = false"
            @on-cancel="outNoticeStatus1 = false">
            <json-viewer :value="jsonData" :expand-depth="3" :expanded="true"></json-viewer>
        </h-msg-box>
        <h-msg-box v-model="outNoticeStatus2" :escClose="true" title="通知内容" @on-ok="outNoticeStatus2 = false"
            @on-cancel="outNoticeStatus2 = false">
            <p style="line-height: 24px; color: #777;">{{ noticeContent }}</p>
        </h-msg-box>
    </div>
</template>

<script>
import tabTitleTable from '@/components/common/bestTable/tabTitleTable';
import aTitle from '@/components/common/title/aTitle';
import aButton from '@/components/common/button/aButton';
import noticeTempModal from '@/components/sms/noticeTempModal.vue';
import jsonViewer from 'vue-json-viewer';
import { formatDate, isJSON } from '@/utils/utils';
import { getAlertMessageList, getMsgTemplateList, getAddressee, getOutNoticeList } from '@/api/httpApi';
export default {
    name: 'SmsList',
    data() {
        return {
            tabTableData: [
                {
                    label: '告警通知',
                    name: 'notice',
                    tableData: [],
                    columns: [
                        {
                            title: '告警通知时间',
                            key: 'createTime',
                            minWidth: 100
                        },
                        {
                            title: '匹配通知模板',
                            key: 'msgTemplateName'
                        },
                        {
                            title: '通知干系人',
                            key: 'addresseeNum',
                            render: (h, params) => {
                                return h('span', {}, params.row.addresseeNum + '人');
                            }
                        },
                        {
                            title: '自动通知',
                            key: 'autoNotifySwitch',
                            render: (h, params) => {
                                return h('span', {}, params.row.autoNotifySwitch ? '开启' : '关闭');
                            }
                        },
                        {
                            title: '匹配状态',
                            key: 'matchStatus',
                            render: (h, params) => {
                                return h('span', {}, params.row.matchStatus ? '成功' : '失败');
                            }
                        },
                        {
                            title: '通知状态',
                            key: 'notifyStatus',
                            render: (h, params) => {
                                return h('span', {}, params.row.notifyStatus ? '已通知' : '未通知');
                            }
                        },
                        {
                            title: '告警通知参数',
                            key: 'action',
                            fixed: 'right',
                            width: 110,
                            render: (h, params) => {
                                return h('Button',
                                    {
                                        props: {
                                            size: 'small',
                                            type: 'text'
                                        },
                                        on: {
                                            click: () => {
                                                this.noticeStatus = true;
                                                const data = params.row.msgPlaceholder;
                                                this.jsonData = isJSON(data) ? JSON.parse(data) : data;
                                            }
                                        }
                                    },
                                    '查看'
                                );
                            }
                        }
                    ],
                    formItems: [
                        {
                            type: 'daterange',
                            key: 'date',
                            label: '通知时间',
                            value: []
                        },
                        {
                            type: 'select',
                            label: '通知模板',
                            key: 'msgTemplateId',
                            options: [],
                            value: '',
                            placeholder: '请选择通知模板'
                        },
                        {
                            type: 'select',
                            label: '匹配状态',
                            key: 'match',
                            options: [
                                {
                                    label: '全部',
                                    value: '0'
                                }, {
                                    label: '匹配',
                                    value: '1'
                                }, {
                                    label: '未匹配',
                                    value: '2'
                                }
                            ],
                            value: '0',
                            placeholder: '请选择匹配状态'
                        },
                        {
                            type: 'select',
                            label: '通知状态',
                            key: 'notify',
                            options: [
                                {
                                    label: '全部',
                                    value: '0'
                                }, {
                                    label: '已通知',
                                    value: '1'
                                }, {
                                    label: '未通知',
                                    value: '2'
                                }
                            ],
                            value: '2',
                            placeholder: '请选择通知状态'
                        }
                    ],
                    hasPage: true,
                    total: 0
                },
                {
                    label: '通知外发历史',
                    name: 'outNotice',
                    tableData: [],
                    columns: [
                        {
                            title: '创建时间',
                            key: 'createTime',
                            minWidth: 124
                        },
                        {
                            title: '通知干系人',
                            key: 'addresseeName'
                        },
                        {
                            title: '通知方式',
                            key: 'notifyType',
                            render: (h, params) => {
                                return h('span', {}, params.row.notifyType === 'SMS' ? '短信' : '电子邮件');
                            }
                        },
                        {
                            title: '通知地址',
                            key: 'notifyAddress'
                        },
                        {
                            title: '通知提供方',
                            key: 'notifyChannel'
                        },
                        {
                            title: '通知状态',
                            key: 'notifyStatus',
                            render: (h, params) => {
                                return h('Button',
                                    {
                                        props: {
                                            size: 'small',
                                            type: 'text'
                                        },
                                        on: {
                                            click: () => {
                                                this.outNoticeStatus1 = true;
                                                const data = params.row.channelReturnMsg;
                                                this.jsonData = isJSON(data) ? JSON.parse(data) : data;
                                            }
                                        }
                                    },
                                    params.row.notifyStatus === 'SUCCESS' ? '成功' : '失败'
                                );
                            }
                        },
                        {
                            title: '通知时间',
                            key: 'notifyTime',
                            minWidth: 124
                        },
                        {
                            title: '通知内容',
                            key: 'action',
                            fixed: 'right',
                            width: 85,
                            render: (h, params) => {
                                return h('Button',
                                    {
                                        props: {
                                            size: 'small',
                                            type: 'text'
                                        },
                                        on: {
                                            click: () => {
                                                this.outNoticeStatus2 = true;
                                                this.noticeContent = params.row.msgContent;
                                            }
                                        }
                                    },
                                    '查看'
                                );
                            }
                        }
                    ],
                    formItems: [
                        {
                            type: 'selectSearch',
                            key: 'addresseeName',
                            label: '干系人',
                            options: [],
                            value: '',
                            loading: false,
                            remoteMethod: this.remoteMethod
                        },
                        {
                            type: 'select',
                            label: '通知方式',
                            key: 'notifyType',
                            options: [{
                                value: '0',
                                label: '全部'
                            }, {
                                value: 'SMS',
                                label: '短信'
                            }, {
                                value: 'EMAIL',
                                label: '电子邮件'
                            }],
                            value: '0',
                            placeholder: '请选择通知方式'
                        },
                        {
                            type: 'input',
                            label: '通知内容',
                            key: 'msgContent',
                            value: '',
                            placeholder: '请输入通知内容'
                        },
                        {
                            type: 'daterange',
                            key: 'date',
                            label: '发送时间',
                            value: []
                        },
                        {
                            type: 'select',
                            label: '通知状态',
                            key: 'notifyStatus',
                            options: [{
                                value: '0',
                                label: '全部'
                            }, {
                                value: 'SUCCESS',
                                label: '发送成功'
                            }, {
                                value: 'FAIL',
                                label: '发送失败'
                            }],
                            value: '0',
                            placeholder: '请选择通知状态'
                        },
                        {
                            type: 'input',
                            label: '通知地址',
                            key: 'notifyAddress',
                            value: '',
                            placeholder: '请输入通知地址'
                        }
                    ],
                    hasPage: true,
                    total: 0
                }
            ],
            modalInfo: {
                status: false
            },
            tableLoading: false,
            noticeStatus: false,
            outNoticeStatus1: false,
            outNoticeStatus2: false,
            jsonData: '',
            noticeContent: ''
        };
    },
    async mounted() {
        this.getMsgTemplateList();
        // 表格重查询数据
        this.$nextTick(() => {
            this.$refs['tab'].$_clearCurrentCheckedKeys();
            this.$refs['tab'].$_handleResetPageDataAndQuery();
        });
    },
    methods: {
        // 查询告警模版列表
        async getMsgTemplateList() {
            return new Promise((resolve, reject) => {
                getMsgTemplateList().then(res => {
                    const { data } = res;
                    this.tabTableData[0].formItems[1].options = [];
                    Array.isArray(data) && data.forEach(item => {
                        this.tabTableData[0].formItems[1].options.push({
                            value: item.id,
                            label: item.msgTemplateName
                        });
                    });
                    resolve(true);
                }).catch(err => {
                    reject(err);
                });
            });
        },
        remoteMethod(name) {
            if (name) this.getAddressee(name);
        },
        // 查询干系人列表
        async getAddressee(name) {
            this.tabTableData[1].formItems[0].loading = true;
            this.tabTableData[1].formItems[0].options = [];
            const { data } = await getAddressee({
                addresseeName: name,
                page: 1,
                pageSize: 10000
            });
            if (data?.length) {
                this.tabTableData[1].formItems[0].options = [];
                data.forEach(item => {
                    this.tabTableData[1].formItems[0].options.push({
                        value: item.id,
                        label: item.addresseeName
                    });
                });
                this.tabTableData[1].formItems[0].loading = false;
            } else {
                this.tabTableData[1].formItems[0].loading = false;
            }

        },
        // 告警通知查询
        async handleNoticeQuery(val) {
            try {
                this.tableLoading = true;
                const param = {
                    startTime: val.date[0] ? `${formatDate(val?.date[0])} 00:00:00` : '',
                    endTime: val?.date[1] ? `${formatDate(val?.date[1])} 23:59:59` : '',
                    msgTemplateId: val.msgTemplateId,
                    matchStatus: val.match === '1' ? true : val.match === '2' ? false : '',
                    notifyStatus: val.notify === '1' ? true : val.notify === '2' ? false : '',
                    page: val.page,
                    pageSize: val.pageSize
                };
                const res = await getAlertMessageList({ ...param });
                this.tableLoading = false;
                if (res.success) {
                    this.tabTableData[0].tableData = res.data.list || [];
                    this.tabTableData[0].total = res.data.totalCount || 0;
                } else {
                    this.$hMessage.success('查询失败!');
                }
            } catch (err) {
                this.tableLoading = false;
                this.$hMessage.error(err.message);
                this.tabTableData[0].tableData = [];
                console.log(err);
            }
        },
        // 通知外发历史
        async handleOutNoticeQuery(val) {
            try {
                this.tableLoading = true;
                const param = {
                    startTime: val.date[0] ? `${formatDate(val?.date[0])} 00:00:00` : '',
                    endTime: val?.date[1] ? `${formatDate(val?.date[1])} 23:59:59` : '',
                    addresseeName: val.addresseeName,
                    msgContent: val.msgContent,
                    notifyStatus: val.notifyStatus === '0' ? '' : val.notifyStatus,
                    notifyType: val.notifyType === '0' ? '' : val.notifyType,
                    page: val.page,
                    pageSize: val.pageSize
                };
                const res = await getOutNoticeList({ ...param });
                this.tableLoading = false;
                if (res.success) {
                    this.tabTableData[1].tableData = res.data.list || [];
                    this.tabTableData[1].total = res.data.totalCount || 0;
                } else {
                    this.$hMessage.success('查询失败!');
                }
            } catch (err) {
                this.tableLoading = false;
                this.$hMessage.error(err.message);
                this.tabTableData[1].tableData = [];
                console.log(err);
            }
        },
        // 数据查询
        async handleQuery(val) {
            if (val.tabName === 'notice') {
                await this.handleNoticeQuery(val);
            } else if (val.tabName === 'outNotice') {
                await this.handleOutNoticeQuery(val);
            }
            // 更新表格列和侧边栏多选项数据
            this.$refs['tab'].$_init();
        }
    },
    components: { noticeTempModal, tabTitleTable, aTitle, aButton, jsonViewer }
};
</script>

<style lang="less" scoped>
@import url("@/assets/css/jsonViewer.less");
@import url("@/assets/css/input.less");

.main {
    width: 100%;
    height: 100%;
    overflow: hidden;
}

.slot-box {
    float: right;
    padding-right: 10px;
}
</style>
