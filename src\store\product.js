/*
 * @Description: 产品相关
 * @Author: <PERSON><PERSON>
 * @Date: 2022-12-21 10:30:33
 * @LastEditTime: 2023-10-10 10:46:45
 * @LastEditors: Zale Ying
 */
import { getProductManageList, getAccountList } from '@/api/httpApi';
import { getProductListLight, getAdaptedBizSysTypeDict } from '@/api/productApi';

export const state = () => ({
    productList: [],     // 产品节点列表
    productListLight: [],     // 产品节点列表(拆)
    accountList: [],     // 资金账号列表
    adaptedBizSysTypeDict: []   // 业务系统列表
});

export const mutations = {
    setProductList(data, list) {
        data.productList = list;
    },
    setProductListLight(data, list) {
        data.productListLight = list;
    },
    setAccountList(data, list) {
        const accounts = list;
        accounts[0] !== '全部资金账号' && list.unshift('全部资金账号');
        data.accountList = accounts;
    },
    setAdaptedBizSysTypeDict(data, list) {
        data.adaptedBizSysTypeDict = list;
    }
};

export const actions = {
    // 获取产品节点信息
    getProductList(context) {
        return new Promise((resolve, reject) => {
            getProductManageList().then(res => {
                if (res.success) {
                    context.commit('setProductList', res.data || []);
                    resolve(true);
                } else {
                    resolve(false);
                }
            }).catch(err => {
                reject(err);
            });
        });
    },
    // 获取产品列表(拆)
    getProductListLight(context, param = {}) {
        return new Promise((resolve, reject) => {
            getProductListLight(param).then(res => {
                if (res.success) {
                    context.commit('setProductListLight', res.data || []);
                    resolve(true);
                } else {
                    resolve(false);
                }
            }).catch(err => {
                reject(err);
            });
        });
    },
    // 获取资金账号信息
    getAccountList(context, param) {
        context.commit('setAccountList', []);
        return new Promise((resolve, reject) => {
            getAccountList(param).then(res => {
                if (res.success) {
                    context.commit('setAccountList', res.data || []);
                    resolve(true);
                } else {
                    resolve(false);
                }
            });
        });
    },
    // 业务系统列表
    getAdaptedBizSysTypeDict(context) {
        return new Promise((resolve, reject) => {
            getAdaptedBizSysTypeDict().then(res => {
                if (res.success) {
                    context.commit('setAdaptedBizSysTypeDict', res.data || []);
                    resolve(true);
                } else {
                    resolve(false);
                }
            }).catch(err => {
                reject(err);
            });
        });
    }
};
