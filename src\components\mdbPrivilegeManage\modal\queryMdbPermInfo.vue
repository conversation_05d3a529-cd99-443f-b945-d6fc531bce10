<template>
  <div>
    <h-msg-box-safe
      v-model="modalData.status"
      title="MDB权限"
      :mask-closable="false"
      width="80"
      height="490"
      @on-open="getInit"
    >
        <a-tips :tipText="`当前APM本地权限版本号：${authVersion}`" style="margin-top: 5px;"></a-tips>
        <div class="perm-wrapper">
            <div class="user-menu">
                <h-input
                    v-model="filterUser"
                    class="user-input"
                    placeholder="请输入用户名"
                    @on-change="handleFilterUser" />
                <ul v-if="userShowList.length" class="user-menu-list">
                    <li
                        v-for="item in userShowList"
                        :key="item.userId"
                        :class="{'user-li-active': item.userId=== selectUserId}"
                        :title="item.userName || '本地存在用户,但MDB未获取到'"
                        @click="handleUserInfo(item)">{{ item.userName || '未知用户⚠' }}</li>
                </ul>
                <div v-else style="text-align: center;">暂无数据</div>
            </div>
            <div class="version-box">
                <div class="version-menu">
                    <h-tree
                        ref="tree"
                        class="menu-tree"
                        :data="baseData"
                        isAlwaysSelect
                        @on-select-change="handleSelectChange"></h-tree>
                </div>
                <div class="version-table">
                    <h-table
                        height="390"
                        :columns="columns"
                        :data="tableData"></h-table>
                </div>
            </div>
        </div>
        <template v-slot:footer>
            <a-button @click="modalData.status = false">关闭</a-button>
        </template>
    </h-msg-box-safe>
  </div>
</template>

<script>
import aTips from '@/components/common/apmTips/aTips';
import { getMdbPerUserCluster, getMdbPerUserClusterAuth, getMdbPerVersion } from '@/api/mdbPrivilegeApi';
import aButton from '@/components/common/button/aButton';
export default {
    props: {
        productId: {
            type: String,
            default: ''
        },
        modalInfo: {
            type: Object,
            default: null
        }
    },
    components: { aButton, aTips },
    data() {
        return {
            modalData: this.modalInfo,
            filterUser: '',
            selectUserId: '',
            userList: [],
            authVersion: '',
            columns: [
                {
                    title: '表名',
                    key: 'tableName'
                },
                {
                    title: '权限',
                    key: 'permission'
                }
            ],
            tableData: [],
            baseData: []
        };
    },
    computed: {
        /**
         * 这是一个compute，需要过滤userList中hidden不为true的子项，形成一个新的list
         */
        userShowList() {
            const key = this.filterUser;
            if (!key) {
                // 如果没有输入关键字，则返回原始列表，并重置hidden属性
                return this.userList;
            }
            // 过滤条件为hidden属性不为true的用户
            return this.userList.filter(user => {
                const userName = user.userName;
                return userName.includes(key);
            });
        }
    },
    methods: {
        async getInit() {
            await this.getMdbPerVersion();
            await this.getMdbPerUserCluster();
            if (this.userList.length) {
                const data = this.userList[0];
                this.handleUserInfo(data);
                this.$nextTick(() => {
                    if (this.baseData.length) {
                        this.$refs['tree'].nodeSelect('clusterId', this.baseData[0]?.children?.[0]?.clusterId);
                        this.handleSelectChange([this.baseData[0]?.children?.[0]]);
                    }
                });
            }
        },
        // 选择MDB用户
        handleUserInfo(item) {
            this.selectUserId = item.userId;
            this.baseData = [];
            this.tableData = [];
            if (Array.isArray(item.clusterGroups) && item.clusterGroups.length > 0) {
                item.clusterGroups.forEach(ele => {
                    const data = {};
                    data.title = ele.clusterType;
                    data.expand = true;
                    data.disabled = true;
                    if (Array.isArray(ele.clusters) && ele.clusters.length > 0) {
                        data.children = [];
                        ele.clusters.forEach(key => {
                            data.children.push({
                                title: key.clusterName + `（版本号:${key.authVersion}）`,
                                clusterId: key.clusterId
                            });
                        });
                        this.baseData.push(data);
                    }
                });
            }
        },
        /**
         * 这是一个搜索过滤函数
         * 1、根据输入的字符自动对列表userList里的userName值过滤，不区分大小写
         * 2、要求过滤为包含过滤
         * 3、如果该子项不在包含过滤当中，则增加hidden属性，为true
         * 4、返回一个新的userList
         */
        handleFilterUser() {
            const key = this.filterUser;
            if (!key) {
                // 如果没有输入关键字，则返回原始列表，并重置hidden属性
                return this.userList.map(user => ({ ...user, hidden: false }));
            }
            return this.userList.map(user => {
                const userName = user.userName; // 将用户名转换为小写
                // 检查用户名是否包含关键字，设置hidden属性
                const hidden = !userName.includes(key);
                return { ...user, hidden };
            });
        },
        // 点击树菜单
        async handleSelectChange(data) {
            this.tableData = [];
            const privileges = await this.getMdbPerUserClusterAuth(data?.[0]?.clusterId);
            privileges.forEach(ele => {
                const data = {};
                data.tableName = ele.tableName || '-';
                const permission = [];
                if (ele.select) {
                    permission.push('select');
                }
                if (ele.update) {
                    permission.push('update');
                }
                if (ele.insert) {
                    permission.push('insert');
                }
                if (ele.delete) {
                    permission.push('delete');
                }
                data.permission = permission.join(',') || '-';
                this.tableData.push(data);
            });
        },
        // 获取用户列表
        async getMdbPerUserCluster() {
            try {
                const res = await getMdbPerUserCluster({
                    productId: this.productId
                });
                this.userList = res?.data || [];
            } catch (error) {
                console.error(error);
                this.userList = [];
            }
        },
        // 获取权限列表
        async getMdbPerUserClusterAuth(clusterId) {
            try {
                const res = await getMdbPerUserClusterAuth({
                    productId: this.productId,
                    clusterId: clusterId,
                    userId: this.selectUserId
                });
                return res?.data?.ldpMdbAuthPrivileges || [];
            } catch (error) {
                console.error(error);
            }
            return [];
        },
        // 获取版本信息
        async getMdbPerVersion() {
            try {
                const res = await getMdbPerVersion({
                    productId: this.productId
                });
                this.authVersion = res?.data?.authVersion || '-';
            } catch (error) {
                console.error(error);
            }

        }
    }
};
</script>

<style lang="less" scoped>
/deep/ .h-modal-body {
    padding: 5px 20px 16px;

    p {
        color: #495060;
    }
}

.perm-wrapper {
    display: flex;
    width: 100%;
    height: 420px;
    margin-top: 10px;

    .user-menu {
        width: 144px;
        height: 420px;
        border: 1px solid #ccc;
        border-radius: 4px;

        .user-input {
            width: 120px;
            margin: 12px;
        }

        & > .user-menu-list {
            width: 144px;
            height: 360px;
            overflow-y: auto;

            & > li {
                width: 100%;
                height: 32px;
                line-height: 32px;
                padding-left: 10px;
                color: #495060;
                cursor: pointer;
                white-space: nowrap;      /* 禁止换行 */
                overflow: hidden;         /* 溢出内容隐藏 */
                text-overflow: ellipsis;  /* 溢出内容显示省略号 */
            }

            li:hover {
                background-color: #298dff4d;
            }

            .user-li-active {
                background-color: #298dff4d;

                &::before {
                    display: inline-block;
                    position: relative;
                    left: -6px;
                    top: 6px;
                    content: "";
                    width: 4px;
                    height: 20px;
                    background: var(--link-color);
                }
            }
        }
    }

    .version-box {
        display: flex;
        width: calc(100% - 160px);
        border: 1px solid #ccc;
        border-radius: 4px;
        margin-left: 14px;

        .version-menu {
            width: 250px;
            height: 430px;
            overflow-y: auto;

            // 菜单树组件
            .menu-tree {
                height: calc(100% - 40px);
                overflow: auto;
                padding: 0 8px;
                border: 1px solid #ccc;
                margin: 15px 0 0 10px;

                /deep/ .h-tree-empty {
                    text-align: center;
                    line-height: 360px;
                }

                /deep/ .h-tree-title {
                    font-size: 12px;
                    background: none;

                    &:hover {
                        background: none;
                    }
                }

                /deep/ .h-tree-arrow {
                    color: var(--link-color);
                    position: relative;

                    & > .h-icon-ios-arrow-right::before {
                        content: "\25B8";
                        font-size: 24px;
                        position: relative;
                        bottom: -4px;
                        right: 0;
                    }
                }

                /deep/ .h-tree-arrow i {
                    transition: none;
                }

                /deep/ .h-tree-arrow-open {
                    .h-icon-ios-arrow-right::before {
                        content: "\25B8";
                        font-size: 24px;
                        position: relative;
                        right: -4px;
                        bottom: 0;
                    }
                }

                /deep/ .h-tree-children {
                    & > li > .h-tree-item > .h-tree-title {
                        font-size: 12px;

                        &:hover {
                            background: none;
                        }
                    }

                    .h-tree-title-selected {
                        color: var(--link-color);
                        font-weight: 600;
                    }
                }

                /deep/.h-tree-title-disabled,
                .h-tree-title-disabled:hover {
                    color: #575e6c;
                    cursor: not-allowed;
                    background-color: transparent;
                }
            }
        }

        .version-table {
            width: calc(100% - 240px);
            margin: 15px;
        }
    }
}
</style>
