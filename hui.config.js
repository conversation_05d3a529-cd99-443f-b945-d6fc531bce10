/*
 * @Description:
 * @Author: <PERSON><PERSON>
 * @Date: 2022-08-01 16:23:30
 * @LastEditTime: 2024-08-28 11:29:41
 * @LastEditors: yingzx38608 <EMAIL>
 */
const MonacoWebpackPlugin = require('monaco-editor-webpack-plugin');
module.exports = {
    // 应用 id
    id: 'b0eba3b0-03f0-11ec-b899-159bdcaf60dd',
    // 应用类型
    type: 'app',
    // 打开之后将使用内置的路由引擎，自动处理路由关系，否则需要手动设置路由
    autoRouting: false,
    // 设置路由前缀，通常用于部署到非根目录
    base: '/',
    // 配置路由模式
    mode: 'hash',
    // 插件
    plugins: ['hui-plugin-micro-app', 'hui-plugin-lint', 'hui-plugin-jsx'],
    // 是一个函数，会接收一个基于 webpack-chain 的 ChainableConfig 实例。允许对内部的 webpack 配置进行更细粒度的修改。
    chainWebpack (config) {
        config.plugin('monaco').use(new MonacoWebpackPlugin(
            { languages: ['json', 'xml'] }
        ));
    },
    // 代理
    proxy: {
        '/api': {
            // target: 'http://10.20.144.248:8089',
            target: 'http://10.20.144.249:8089',
            // target: 'http://10.25.6.52:8089', // 德望
            // target: 'http://10.25.7.215:8089', // 邵峰
            // target: 'http://10.188.117.26:8089', // 衍恒
            // target: 'http://10.20.144.248:3000', // mock
            changeOrigin: true, // 打开跨域
            logLevel: 'debug',
            pathRewrite: {
                '^/api': '/ldplt/api' // mock时不需要pathRewrite
                // '^/api': '/mock/19/ldplt/api'
                // 因为真实的服务器端的地址中不包含/api ，所以应该将程序中的、api删除（替换空字符串），再和target中的基础路径拼接起来作为发送到服务器端的最终请求地址
            }
        }
    },
    // 设置静态资源文件打包时的相对路径
    publicPath: './',
    // 部署配置
    see: {
        // 系统类型
        systemType: 'HUI'
    },
    // 使用 Vuex 进行状态管理
    vuex: true,
    configureWebpack: {
        module: {
            rules: [{
                test: /\.md$/,
                use: 'raw-loader'
            }]
        }
    }
};
