<template>
    <div>
        <h-drawer
            v-model="modalInfo.status"
            width="580"
            title="SQL执行失败详情"
            class="drawer">
            <a-table
                :columns="columns"
                :hasPage="false"
                :tableData="modalData.instanceErrorMsg"></a-table>
        </h-drawer>
    </div>
</template>

<script>
import aTable from '@/components/common/table/aTable';
export default ({
    props: {
        modalInfo: {
            type: Object,
            default: null
        }
    },
    components: { aTable },
    data() {
        return {
            modalData: this.modalInfo,
            searchValue: '',
            newHistoryList: [],
            columns: [{
                width: 150,
                title: '节点',
                key: 'instanceName'
            },
            {
                title: '错误原因',
                key: 'errorMsg'
            }]
        };
    },
    mounted() {

    },
    methods: {

    }
});
</script>

<style lang="less" scoped>
@import url("@/assets/css/drawer.less");

/deep/ .h-modal-body {
    padding: 15px;

    & > .box-records {
        margin-top: 10px;
        height: calc(100% - 42px);
        overflow-y: auto;
    }
}
</style>
