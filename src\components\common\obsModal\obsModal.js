import './obsModal.less';
import infoGrid from '@/components/common/infoBar/infoGrid';
export default {
    name: 'ObsModal',
    components: { infoGrid },
    props: {
        modalInfo: {
            type: Object,
            default: () => {}
        },
        loading: {
            type: Boolean,
            default: true
        }
    },
    data() {
        return {
            modalData: this.modalInfo
        };
    },
    mounted() {
    },
    methods: {

    },
    render() {
        return (
            <h-msg-box-safe
                v-model={this.modalData.status}
                mask-closable={false}
                escClose={false}
                footerHide={true}
                class-name="obs-modal"
                title={this.modalData.title}
                width={this.modalData.width || 520}
                height={this.modalData.height || 500}
                // v-on:on-ok="ok"
                // v-on:on-cancel="cancel"
            >
                {
                    this.loading && <div class="demo-spin-container" >
                        <h-spin fix>
                            <h-icon name="load-c" size="18" class="demo-spin-icon-load"></h-icon>
                            <div>加载中...</div>
                        </h-spin>
                    </div>
                }
                <info-grid gridData={this.modalData.gridData} />
            </h-msg-box-safe>
        );
    }
};
