<template>
    <div ref="history-modify" class="history-modify">
        <div class="operation-group">
            <div class="operation-info">
                <a-title title="操作内容"></a-title>
                <ul>
                    <li><span class="label">目标表:</span>{{operationContext.targetTable}}</li>
                    <li><span class="label">批量修改操作:</span>{{operationContext.batchModification}}</li>
                    <li><span class="label">影响数据条数:</span>{{operationContext.affectedItemNum}}</li>
                    <li><span class="label">影响字段个数:</span>{{operationContext.affectedFieldNum}}</li>
                </ul>
            </div>
            <!-- <div class="operation-info">
                <a-title title="操作用户"></a-title>
                <ul>
                    <li><span class="label">用户名:</span>{{operationContext.userName}}</li>
                    <li><span class="label">用户角色:</span>{{operationContext.userRole}}</li>
                    <li><span class="label">修改单提交时间:</span>{{operationContext.commitTime}}</li>
                </ul>
            </div> -->
            <div class="operation-info">
                <a-title title="操作执行"></a-title>
                <ul>
                    <li><span class="label">修改单状态:</span>{{getOrderStatus(operationContext.status)}}
                        <a-button v-if="operationContext.status === 'inProgress'"
                        type="dark" size="small" style="margin: 0 5px;" @click="orderExecute">查看</a-button>
                    </li>
                    <li><span class="label">修改成功数:</span>{{operationContext.successItemNum}}</li>
                    <li><span class="label">修改失败数:</span>{{operationContext.failItemNum}}</li>
                    <li><span class="label">修改开始时间:</span>{{operationContext.startTime}}</li>
                    <li><span class="label">修改结束时间:</span>{{operationContext.endTime}}</li>
                    <li><span class="label">修改总耗时:</span>{{operationContext.duration}}&nbsp;ms</li>
                </ul>
            </div>
        </div>
        <a-title title="内存修改操作详情">
            <slot>
                <h-radio-group v-model="status" class="radio-group" @on-change="changeOrderStatus">
                    <h-radio text="成功" label="success"></h-radio>
                    <h-radio text="失败" label="failed"></h-radio>
                    <h-radio text="未执行" label="notPerformed"></h-radio>
                    <h-radio text="失效" label="invalid"></h-radio>
                </h-radio-group>
            </slot>
        </a-title>
        <a-table
            ref="table"
            :columns="columns"
            :tableData="tableData"
            :height="tableHeight"
            showTitle
            :loading="loading"
            :total="total"
            @query='handleQuery(modificationId)'>
        </a-table>

        <operation-details-modal v-if="modalInfo.status" :modalInfo="modalInfo"></operation-details-modal>
    </div>
</template>

<script>
import _ from 'lodash';
import { getModificationLists, getProcessDetail } from '@/api/httpApi';
import aTitle from '@/components/common/title/aTitle';
import aTable from '@/components/common/table/aTable';
import aButton from '@/components/common/button/aButton';
import operationDetailsModal from '@/components/ldpTable/modal/operationDetailsModal';
const itemStatusList = [
    {
        value: 'success',
        name: '成功'
    },
    {
        value: 'failed',
        name: '失败'
    },
    {
        value: 'notStart',
        name: '未执行'
    },
    {
        value: 'invalid',
        name: '失效'
    }
];
const orderStatusList = [
    {
        value: 'notPerformed',
        name: '未执行'
    },
    {
        value: 'inProgress',
        name: '执行中'
    },
    {
        value: 'finished',
        name: '执行完毕'
    }
];
export default {
    name: 'HistoryModifyList',
    props: {
        endpointInfo: {
            type: Object,
            default: () => {}
        }
    },
    data() {
        return {
            status: 'success',
            columns: [
                {
                    title: '数据记录号',
                    align: 'center',
                    width: 120,
                    render: (h, params) => {
                        return h(
                            'Poptip', {
                                props: {
                                    trigger: 'hover',
                                    content: `表序号：${params.row.tableId} 线程号：${params.row.threadId} 记录号：${params.row.recordId}`,
                                    placement: 'bottom-start'
                                },
                                style: {
                                    width: '100%'
                                }
                            }, [
                                h('div', [params.row.recordId])
                            ]
                        );
                    }
                },
                {
                    title: '字段名',
                    key: 'fieldName',
                    ellipsis: true
                },
                {
                    title: '字段类型',
                    key: 'fieldType',
                    ellipsis: true
                },
                {
                    title: '修改值',
                    key: 'updateValue',
                    ellipsis: true,
                    className: 'table-info-column'
                },
                {
                    title: '原纪录值',
                    key: 'currentValue',
                    ellipsis: true
                },
                {
                    title: '修改状态',
                    key: 'status',
                    render: (h, param) => {
                        return h('span', [this.getItemStatus(param.row.status)]);
                    }
                },
                {
                    title: '修改执行',
                    key: 'action',
                    render: (h, params) => {
                        return h('div', [
                            params.row.requestRecordId
                                ? h(
                                    'Button',
                                    {
                                        props: {
                                            type: 'text',
                                            size: 'small'
                                        },
                                        on: {
                                            click: () => {
                                            // 查看修改记录执行明细
                                                this.getProcessDetail(params);
                                            }
                                        }
                                    },
                                    '查看'
                                )
                                : h('span', ['该修改未执行'])
                        ]);
                    }
                }
            ],
            tableData: [],
            tableHeight: 0,
            total: 0,
            loading: false,
            operationContext: {},
            modalInfo: {
                status: false,
                requestUrl: '', // 请求路径
                requestProtocol: '', // 请求协议
                requestTime: 'yyyy-MM-dd HH-mm-SS', // 请求时间
                requestParams: '', // 请求参数
                response: '' // 请求响应
            },
            modificationId: ''
        };
    },
    mounted() {
        window.addEventListener('resize', this.fetTableHeight);
    },
    beforeDestroy() {
        window.removeEventListener('resize', this.fetTableHeight);
    },
    methods: {
        resetHeight() {
            return new Promise((resolve, reject) => {
                this.tableHeight = 0;
                resolve();
            });
        },
        // 设置table高度
        fetTableHeight() {
            this.resetHeight().then(res => {
                const tableHeight = this.$refs['history-modify'].getBoundingClientRect().height - 310;
                this.tableHeight = tableHeight;
            });
        },
        // 匹配修改单状态
        getOrderStatus(value) {
            return _.find(orderStatusList, ['value', value])?.name;
        },
        // 匹配修改条目状态
        getItemStatus(value) {
            return _.find(itemStatusList, ['value', value])?.name;
        },
        // 查询历史修改单详情列表
        async handleQuery(id) {
            this.modificationId = id;
            this.loading = true;
            const param = {
                modificationOrderId: id,
                status: this.status,
                instanceId: this.endpointInfo?.instanceId,
                ...this.$refs['table']?.getPageData()
            };
            try {
                const res = await getModificationLists(param);
                if (res) {
                    this.operationContext = { ...res };
                    this.total = res.totalCount;
                    this.tableData = res.items;
                    this.loading = false;
                }
            } catch (err) {
                console.error(err);
                this.loading = false;
            }

            this.fetTableHeight();
        },
        // 切换修改单状态查询
        changeOrderStatus() {
            this.$refs['table'].resetPage();
            this.handleQuery(this.modificationId);
        },
        // 查看修改记录详情
        async getProcessDetail(params) {
            const param = {
                instanceId: this.endpointInfo?.instanceId,
                requestId: params.row.requestRecordId
            };
            try {
                const res = await getProcessDetail(param);
                if (res) {
                    this.modalInfo = { ...res };
                    this.$set(this.modalInfo, 'status', true);
                }
            } catch (err) {
                console.error(err);
                this.$hMessage.error('查看详情失败');
            }
        },
        // 修改单执行
        orderExecute() {
            this.$emit('order-execute', this.modificationId);
        }
    },
    components: { aTitle, aTable, aButton, operationDetailsModal }
};
</script>

<style lang="less" scoped>
.history-modify {
    position: relative;
    width: 100%;
    height: 100%;
    min-height: 400px;

    .operation-group {
        display: flex;
        flex-direction: row;

        .operation-info {
            flex: 1;
            margin-right: 10px;

            & > ul {
                font-size: var(--font-size-base);
                color: var(--font-opacity-color);
                padding: 10px 20px;

                & > li {
                    padding-top: 10px;
                    padding-right: 20px;
                    line-height: 15px;

                    & > span {
                        width: 100px;
                        display: inline-block;
                    }
                }
            }
        }

        /deep/ .h-poptip {
            width: 100%;
        }
    }

    .radio-group {
        position: absolute;
        right: 6px;
        top: 5px;
    }

    /deep/ .h-table td.table-info-column {
        background-color: var(--primary-color);
        font-weight: 600;
    }
}
</style>
