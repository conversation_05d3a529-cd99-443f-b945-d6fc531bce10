<template>
    <div>
        <h-msg-box-safe
            :value="modalData.status"
            :escClose="true"
            title="添加路由"
            width="600"
            height="420"
            @on-close="onClose"

        >
            <template v-slot:footer>
                    <a-button type="dark" @click="onClose">取消</a-button>
                    <a-button type="primary" @click="onSave">确定</a-button>
            </template>
            <router-info-form ref="form" type="add" :node="node" />
        </h-msg-box-safe>
    </div>
</template>
<script>
import routerInfoForm from './routeInfoForm.vue';
import aButton from '@/components/common/button/aButton';
export default {
    components: { routerInfoForm, aButton },
    props: {
        modalInfo: {
            type: Object,
            default: null
        },
        node: {
            type: Object
        }
    },
    data() {
        return {
            modalData: this.modalInfo
        };
    },
    methods: {
        onClose() {
            this.modalData.status = false;
            this.$emit('onClose');
        },
        async onSave() {
            try {
                const data = await this.$refs['form'].validateAndGetAll();
                this.$emit('inserTable', data);
            } catch (error) {
                console.log('添加路由失败', error);
            }
        }
    }
};
</script>
<style lang="less" scoped>
@import url("@/assets/css/modal.less");
</style>
