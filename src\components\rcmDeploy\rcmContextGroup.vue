<template>
    <div v-if="rcmId" ref="rcm-content" class="rem-overview-content">
        <a-title title="上下文实例分组">
            <slot>
                <h-radio-group v-model="groupType" style="margin-left: 20px;" @on-change="changeCtxGroup">
                    <h-radio text="按主题" label="topic"></h-radio>
                    <h-radio text="按应用" label="appName"></h-radio>
                    <h-radio text="按标签" label="tags"></h-radio>
                </h-radio-group>
            </slot>
        </a-title>
        <div v-if="groupList.length" class="context-instance-list">
            <div v-for="item in groupList" :key="item.groupName" class="context-list">
                    <div v-if="groupType === 'topic'" class="classify-label">
                        <h-row class="list-title">{{item.groupName || '-'}}</h-row>
                        <h-row style="color: #cacfd4;">
                            <h-col span="8">总</h-col><h-col span="8">收</h-col><h-col span="8">发</h-col>
                        </h-row>
                        <h-row style="font-size: 15px;">
                            <h-col span="8">{{item.totalNum || 0}}</h-col><h-col span="8">{{item.rxTopicNum || 0}}</h-col><h-col span="8">{{item.txTopicNum || 0}}</h-col>
                        </h-row>
                    </div>
                    <div v-else class="classify-label">
                        <h-row class="list-title">{{item.groupName || '-'}}</h-row>
                        <h-row style="color: #cacfd4;">总</h-row>
                        <h-row style="font-size: 15px;">{{item.totalNum || 0}}</h-row>
                    </div>
                    <div class="context-instances">
                        <div class="instances">
                            <img v-for="child in item.contexts.slice(0, ctxMaxNum)" :key="item.groupName + child.id" src="static/normalCtx.png" class="ctx-image"
                            @click="selectCtx(child, item.groupName)" />
                            <span v-show="item.totalNum > ctxMaxNum" @click="tabChange">查看更多</span>
                        </div>
                        <a-button type="dark" class="button" @click="delContextGroup(item.groupName)">批量删除</a-button>
                    </div>
            </div>
        </div>
        <h-page v-show="groupList.length" :total="totalCount" :current="page" :pageSize="pageSize" showElevator showSizer placement="top"
            style="height: 42px; margin-right: 10px;" @on-change="changePage" @on-page-size-change="changePageSize"></h-page>
        <no-data v-if="!groupList.length"></no-data>

        <a-loading v-if="loading"></a-loading>

        <!--上下文配置信息弹窗-->
        <context-info-modal v-if="ctxConfigInfo.status" :modalInfo="ctxConfigInfo" :rcmId="rcmId" />
    </div>
</template>

<script>
import { getRcmContextGroup, delContextGroup, getContextDetial } from '@/api/rcmApi';
import aTitle from '@/components/common/title/aTitle';
import aButton from '@/components/common/button/aButton';
import aLoading from '@/components/common/loading/aLoading';
import noData from '@/components/common/noData/noData';
import contextInfoModal from './modal/context/contextInfoModal.vue';

export default {
    props: {
        rcmId: {
            type: String,
            default: ''
        }
    },
    data() {
        return {
            groupType: 'topic',
            totalCount: 0,
            page: 1,
            pageSize: 10,
            groupList: [],
            loading: false,
            ctxConfigInfo: {
                status: false,
                ctxName: '',
                data: {}
            },
            ctxMaxNum: 20
        };
    },
    mounted() {
        this.getInstanceWidth();
        window.addEventListener('resize', this.getInstanceWidth);
    },
    beforeDestroy() {
        window.removeEventListener('resize', this.getInstanceWidth);
    },
    methods: {
        // 获取上下文分组宽度计算显示上下文个数
        getInstanceWidth() {
            const width = this.$refs['rcm-content']?.getBoundingClientRect().width - 360;
            this.ctxMaxNum = isNaN(width) ? 20 : parseInt(width / 42, 10) * 2 - 2;
        },
        // 初始化总览页面
        initData() {
            this.getRcmContextGroup();
        },
        // 获取rcm上下文分组信息
        getRcmContextGroup() {
            this.loading = true;
            this.groupList = [];
            const param = {
                rcmId: this.rcmId,
                groupBy: this.groupType,
                page: this.page,
                pageSize: this.pageSize
            };
            getRcmContextGroup(param).then(res => {
                if (res.code === '200' && this.rcmId === param.rcmId) {
                    this.groupList = res.data.list || [];
                    this.totalCount = res.data.totalCount;
                    this.page = res.data.currPage || 1;
                }
                this.$nextTick(() => {
                    this.loading = false;
                });
            }).catch(err => {
                this.groupList = [];
                this.loading = false;
            });
        },
        // 批量删除分组上下文
        async delContextGroup(name) {
            this.$hMsgBoxSafe.confirm({
                title: `删除`,
                content: `确定删除分组名为${name}下的所有上下文？`,
                onOk: async () => {
                    const param = {
                        rcmId: this.rcmId,
                        groupBy: this.groupType,
                        groupName: name
                    };
                    const res = await delContextGroup(param);
                    if (res.code === '200') {
                        this.initData();
                    }
                }
            });
        },
        // 选择上下文展示详情信息
        async selectCtx(ctx, groupName) {
            const param = {
                rcmId: this.rcmId,
                id: ctx.id
            };
            const res = await getContextDetial(param);
            if (res.code === '200') {
                this.ctxConfigInfo.status = true;
                this.ctxConfigInfo.name = ctx.name;
                this.ctxConfigInfo.data = res.data;
            }
        },
        // 切换上下文实例分组方式
        changeCtxGroup() {
            this.getRcmContextGroup(this.rcmId);
        },
        // 切换上下文分组页码
        changePage(page) {
            this.page = page;
            this.getRcmContextGroup();
        },
        // 切换上下文分组每页条数
        changePageSize(value) {
            this.page = 1;
            this.pageSize = value;
            this.getRcmContextGroup();
        },
        tabChange() {
            this.$emit('tab-change');
        }
    },
    components: { aTitle, aButton, aLoading, noData, contextInfoModal }

};
</script>

<style lang="less" scoped>
@import url("@/assets/css/table.less");
@import url("@/assets/css/input.less");

.rem-overview-content {
    position: relative;
    width: 100%;
    height: 100%;
    overflow: hidden;

    .context-instance-list {
        height: calc(100% - 104px);
        color: var(--font-color);
        margin: 10px 0;
        overflow: auto;

        .context-list:first-child {
            margin-top: 0;
        }

        .context-list:last-child {
            margin-bottom: 0;
        }

        .context-list {
            margin: 10px;
            display: flex;
            height: 100px;

            .classify-label {
                width: 200px;
                background: var(--primary-color);
                text-align: center;
                padding: 10px;
                margin-right: 10px;
                border-radius: 5px;

                & > .h-row {
                    padding: 3px 0;
                    white-space: nowrap;
                    overflow: hidden;
                    text-overflow: ellipsis;

                    .h-col {
                        white-space: nowrap;
                        overflow: hidden;
                        text-overflow: ellipsis;
                    }
                }

                & > .list-title {
                    margin-bottom: 8px;
                    white-space: nowrap;
                    overflow: hidden;
                    text-overflow: ellipsis;
                }
            }

            .context-instances {
                position: relative;
                flex: 1;
                height: 100px;
                background: var(--primary-color);
                padding: 10px;
                border-radius: 5px;

                .instances {
                    display: inline-block;
                    width: calc(100% - 100px);
                    height: 100%;
                    padding: 0 10px 0 0;
                    border-right: 1px solid var(--base-color);
                    overflow: hidden;

                    .ctx-image {
                        display: inline-block;
                        filter: blur(0);
                        width: 36px;
                        height: 36px;
                        margin: 1px 3px;

                        &:hover {
                            cursor: pointer;
                            content: url("static/selectCtx.png");
                        }
                    }

                    & > span {
                        position: absolute;
                        right: 140px;
                        top: 65px;
                        color: var(--link-color);

                        &:hover {
                            cursor: pointer;
                            color: #54e8fc;
                        }
                    }
                }

                .button {
                    position: absolute;
                    right: 15px;
                    top: 50%;
                    transform: translateY(-50%);
                }
            }
        }
    }
}
</style>
