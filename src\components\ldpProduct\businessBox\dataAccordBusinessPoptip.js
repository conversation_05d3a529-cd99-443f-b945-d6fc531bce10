import './businessPoptip.less';
import { transferVal, autoConvertTime, convertToBestUnit, formatNumTruncateDecimals } from '@/utils/utils';
export default {
    name: 'dataAccordBBusinessPoptip',
    props: {
        placement: {
            type: String,
            default: 'right'
        },
        node: {
            type: Object,
            default: function() {
                return {};
            }
        }
    },
    data() {
        return {
        };
    },
    mounted() {
    },
    beforeDestroy() {
    },
    methods: {
        getStatusDesc(key) {
            switch (key) {
                case 'runing':
                    return '运行中';

                case 'warning':
                    return '警告';

                case 'exception':
                    return '异常';

                case 'stop':
                    return '停止';

                default:
                    return '未托管';
            }
        },
        // 核心数据上场业务状态与应用状态墙和应用topo页面字典不一致  0: 未上场，1:上场中，2:上场完成，3:上场失败
        getBizStatusDesc(key) {
            switch (key) {
                case 0:
                    return '未上场';

                case 1:
                    return '上场中';

                case 2:
                    return '上场完成';

                case 3:
                    return '上场失败';

                default:
                    return '';
            }
        },
        // 查看
        handleDrawerOpen(title, instanceId){
            this.$emit('drawser-open', {
                title,
                instanceId
            });
        },
        // 记录数量
        transferRecordNumber(loadTableRecordNumber, totalTableRecordNumber) {
            const num1 = transferVal(loadTableRecordNumber);
            const num2 = transferVal(totalTableRecordNumber);

            if (num1 === '0' || num2 === '0') {
                return '0%';
            } else {
                const percentage = (Number(num1) / Number(num2)) * 100;
                return num1 && num2 ? formatNumTruncateDecimals(percentage) + '%' : '- %';
            }
        }
    },
    // eslint-disable-next-line complexity
    render() {
        const node = this.node;
        const instanceName = node?.target?.resourceName || node?.target?.runningInfo?.instanceName || '-';
        const instanceDesc = this.$store.state.apmDirDesc?.appTypeDictDesc?.[node?.target?.runningInfo?.instanceType] || node?.target?.runningInfo?.instanceType || '-';
        const instanceId = this.node?.target?.resourceId || '';
        const runningInfo = node?.target?.runningInfo || {};
        const instanceInfo = node?.target?.instanceInfo || {};
        const loadInfo = runningInfo?.loadInfo || {};
        const mdbInfo = runningInfo?.mdbInfo || {};
        const clusterRole = runningInfo?.clusterRole || '';
        const spendTime =  transferVal(loadInfo?.spendTime) ? autoConvertTime(loadInfo?.spendTime, 'ms') : '-';
        const userMemory = transferVal(mdbInfo?.userMemory) ? convertToBestUnit(mdbInfo?.userMemory, 'MB') : '-';
        return <div>
            <h-poptip
                trigger="click"
                placement={this.placement}
                autoPlacement
                transfer={true}
                customTransferClassName="pop-business data-accord-business-poptip"
                title={`${instanceDesc}(${instanceName})`}
                style="width: 100%;">
                { clusterRole === 'ARB_ACTIVE' ? (transferVal(loadInfo?.loadTableNumber) || '-') + ' / ' + (transferVal(loadInfo?.totalTableNumber) || '-')
                    : this.transferRecordNumber(loadInfo?.loadTableRecordNumber, loadInfo?.totalTableRecordNumber)}
                <div class="pop-content large-pop-content" slot="content">
                    {
                        clusterRole === 'ARB_ACTIVE' && <div  class="data-accord-business-detail-btn">
                            <a onClick={() => this.handleDrawerOpen('表加载详情', instanceId)}>加载详情</a>
                        </div>
                    }
                    {
                        clusterRole === 'ARB_INACTIVE' && <div class="data-accord-business-detail-btn">
                            <a onClick={() => this.handleDrawerOpen('表同步详情', instanceId)}>同步详情</a>
                        </div>
                    }
                    {
                        clusterRole === 'ARB_ACTIVE' && <div>
                            <div class="pop-content-info">
                                <div class="info-title">加载信息</div>
                                <ul>
                                    <li>
                                        <span>已加载表数量：</span>{transferVal(loadInfo?.loadTableNumber) || '-'}
                                    </li>
                                    <li>
                                        <span>需加载表数量：</span>{transferVal(loadInfo?.totalTableNumber) || '-'}
                                    </li>
                                    <li>
                                        <span>已加载表记录数量：</span>{ transferVal(loadInfo?.loadTableRecordNumber) || '-'}
                                    </li>
                                    <li>
                                        <span>需加载表记录数量：</span>{ transferVal(loadInfo?.totalTableRecordNumber) || '-'}
                                    </li>
                                    <li>
                                        <span>已耗时：</span>{ spendTime?.value || '-'} {spendTime?.unit || 'ms'}
                                    </li>
                                    <li>
                                        <span>数据加载时间：</span>{ loadInfo?.loadTime || '-'}
                                    </li>
                                </ul>
                            </div>
                            <br />
                        </div>
                    }
                    {
                        clusterRole !== 'ARB_ACTIVE' && <div>
                            <div class="pop-content-info">
                                <div class="info-title">同步信息</div>
                                <ul>
                                    <li>
                                        <span>需同步表数量：</span>{transferVal(loadInfo?.totalTableNumber) || '-'}
                                    </li>
                                    <li>
                                        <span>需同步表记录数量：</span>{ transferVal(loadInfo?.totalTableRecordNumber) || '-'}
                                    </li>
                                    <li>
                                        <span>已同步表记录数量：</span>{ transferVal(loadInfo?.loadTableRecordNumber) || '-'}
                                    </li>
                                    <li>
                                        <span>主备数据差量：</span>{ transferVal(loadInfo?.activeStandbyDataDiff) || '-'}
                                    </li>
                                </ul>
                            </div>
                            <br />
                        </div>
                    }
                    <div>
                        <div class="pop-content-info">
                            <div class="info-title">状态</div>
                            <ul>
                                <li>
                                    <span>进程状态：</span>{this.getStatusDesc(runningInfo?.appStatus) || '-'}
                                </li>
                                <li>
                                    <span>业务状态：</span>{this.getBizStatusDesc(runningInfo?.loadStatus) || '-'}
                                </li>
                            </ul>
                        </div>
                        <br />
                    </div>
                    <div class="pop-content-info">
                        <div class="info-title">MDB内存</div>
                        <ul>
                            <li>
                                <span>占用内存：</span>{ userMemory?.value || '-'} {userMemory?.unit || 'MB'}
                            </li>
                        </ul>
                    </div>
                    <br />
                    <div class="pop-content-info">
                        <div class="info-title">基础信息</div>
                        <ul>
                            <li>
                                <span>应用节点名：</span>{instanceName}
                            </li>
                            <li>
                                <span>应用节点类型：</span>{instanceDesc}
                            </li>
                            <li>
                                <span>应用节点版本：</span>{runningInfo?.version || '-'}
                            </li>
                            <li>
                                <span>应用开发平台：</span>{runningInfo?.developPlatform || '-'}
                            </li>
                            <li>
                                <span>主机名：</span>{instanceInfo?.hostName || '-'}
                            </li>
                            <li>
                                <span>IP地址/域名：</span>{instanceInfo?.ips?.[0] || '-'}
                            </li>
                        </ul>
                    </div>
                </div>
            </h-poptip>
        </div>;
    }
};
