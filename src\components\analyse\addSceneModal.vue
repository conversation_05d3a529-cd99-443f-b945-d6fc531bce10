<template>
    <div>
        <!-- 下发配置列表 -->
        <h-msg-box-safe
            v-model="modalData.status"
            :escClose="true"
            :mask-closable="false"
            title="新建场景"
            width="50"
        >
            <h-form
                ref="formValidate"
                :model="formValidate"
                :label-width="100"
                >
                <h-form-item label="场景名称" prop="name" required>
                    <h-input
                        v-model="formValidate.name"
                        placeholder="请输入场景名称"
                         onkeydown="if(event.keyCode==13){event.keyCode=0;event.returnValue=false;}"
                    ></h-input>
                </h-form-item>
                <p style="color: var(--error-color); margin-left: 100px;">请确保当前场景已导出保存，新建的场景将覆盖当前使用的场景信息！</p>
            </h-form>

            <template v-slot:footer>
                <a-button @click="modalData.status = false">取消</a-button>
                <a-button type="primary" :loading="loading" @click="submitConfig">确定</a-button>
            </template>
        </h-msg-box-safe>
    </div>
</template>

<script>
import { addScene } from '@/api/httpApi';
import aButton from '@/components/common/button/aButton';
export default {
    props: {
        modalInfo: {
            type: Object,
            default: null
        }
    },
    data() {
        return {
            modalData: this.modalInfo,
            loading: false,
            formValidate: {
                name: ''
            }
        };
    },
    methods: {
        submitConfig() {
            const that = this;
            this.$refs['formValidate'].validate(async (valid) => {
                if (valid) {
                    that.loading = true;
                    try {
                        const res = await addScene({ sceneName: this.formValidate.name });
                        if (res.success) {
                            this.$emit('update', res?.data?.sceneId);
                            that.$hMessage.success('场景创建成功!');
                            that.modalInfo.status = false;
                        } else {
                            that.loading = false;
                            this.$hMessage.error('场景创建失败!');
                        }
                    } catch (err) {
                        that.loading = false;
                    }
                }
            });
        }
    },
    components: { aButton }
};
</script>
