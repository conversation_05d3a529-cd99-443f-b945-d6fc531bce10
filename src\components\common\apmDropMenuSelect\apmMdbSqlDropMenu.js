import _ from 'lodash';
import './apmMdbSqlDropMenu.less';
import aButton from '@/components/common/button/aButton';
import noData from '@/components/common/noData/noData';
import aTag from '@/components/common/tag/aTag';
import aTips from '@/components/common/apmTips/aTips';
import { renderTextWithMatch } from '@/components/common/apmSelectSearch/util.js';
export default {
    name: 'apmMdbSqlDropMenu',
    components: { aTips, aTag, noData, aButton },
    props: {
        // tips
        tipType: {
            type: String,
            default: 'info'
        },
        tipText: {
            type: String,
            default: ''
        },
        tipCloseable: {
            type: Boolean,
            default: false
        },
        /**
         * 列表数据--从外部传入，使用初始化页面后只需获取一次的情况
         */
        menuList: {
            type: Array,
            default: () => []
        },
        menuMode: {
            type: String,
            default: 'input' // input  select
        },
        /**
         * 列表数据，每次点击面板时重新获取
         */
        dropSelectMode: {
            type: Boolean,
            default: false
        },
        /**
         * 远程搜索函数，仅在dropSelectMode为true时有效
         * 返回一个Promise
         */
        queryListMethod: {
            type: Function,
            default: () => undefined
        },
        /**
         * 是否支持多选
         */
        isMultiple: {
            type: Boolean,
            default: false
        },
        /**
         * 是否分组使用checkBox勾选
         */
        showGroupCheckBox: {
            type: Boolean,
            default: true
        },
        /**
         * 是否分组使用radio选择------单选
         */
        showGroupRadioBox: {
            type: Boolean,
            default: false
        },
        /**
         * 是否节点使用checkBox勾选
         */
        showCheckBox: {
            type: Boolean,
            default: false
        },
        /**
         * 是否子分组使用checkBox勾选------- 必须在showCheckBox为true的情况下使用
         */
        showSubCheckBox: {
            type: Boolean,
            default: true
        },
        /**
         * 特殊性功能----慎用！！！
         * 子分组支持单选点击, 和节点选择互斥
         */
        groupCanClick: {
            type: Boolean,
            default: false
        },
        /**
         * 特殊性功能----慎用！！！
         * 子分组支持单选点击, 和节点选择互斥
         */
        subCanClick: {
            type: Boolean,
            default: false
        },
        /** s
         * 压缩显示多选项
         */
        collapseTags: {
            type: Boolean,
            default: false
        },
        /**
         * 压缩显示多选项时，展示个数
         */
        collapseTagsNumber: {
            type: Number,
            default: 1
        },
        /**
         * 提示占位信息
         */
        placeholder: {
            type: String,
            default: () => '请选择'
        },
        /**
         * 内部提示占位信息
         */
        innerPlaceholder: {
            type: String,
            default: () => '请输入'
        },
        /**
         * 用于禁止聚焦弹窗面板
         */
        disabled: {
            type: Boolean,
            default: () => false
        },
        /**
         * 是否允许清空
         */
        clearable: {
            type: Boolean,
            default: () => true
        },
        /**
         * 搜索是否忽视大小写，仅本地搜索有效
         */
        ignoreUpperCase: {
            type: Boolean,
            default: () => false
        },
        /**
         * 是否使用模糊搜索
         */
        useFuzzy: {
            type: Boolean,
            default: () => true
        }

    },
    data() {
        return {
            loading: false, // 页面加载
            requestMenuList: [], // 每次都重新请求的数据
            filterDropMenuList: [],
            selectedMenu: [], // 选中节点
            searchValue: '', // 内部搜索值
            focusStatus: false, // 面板显示隐藏
            abortController: undefined, // 用户取消各种事件控制器
            tabName: '', // 选中分类,
            selectAllList: [], // 用于全选的组id存储
            selectSubAllList: [], // 用于全选的子组id存储
            isChange: false // 跟踪页面数据是否被更改，未修改则关闭面板时不触发confirm方法
        };
    },
    beforeDestroy() {
        this.abortController.abort();
    },
    mounted() {
        // 点击事件 控制面板显示i隐藏
        this.abortController =  new AbortController();
        const signal = this.abortController.signal;
        window.addEventListener('click', e => {
            // 禁止状态下,关闭面板
            if (this.disabled) {
                this.focusStatus = false;
                return;
            };
            const container = document.getElementById('apm-drop-menu-select-container');
            const mask = document.getElementById('apm-drop-menu-select-wrap-mask');
            if (container?.contains(e.target)) {
                this.onFocus();
                return;
            }
            if (!container?.contains(e.target) || mask?.contains(e.target)){
                this.menuMode === 'input' && this.$refs['input'].blur();
                this.focusStatus && this.isChange && this.$emit('confirm', this.selectedMenu);
                this.focusStatus = false;
                return;
            }
            return;
        }, { signal, capture: false });
    },
    computed: {
        // 全部选项列表
        dropMenuList(){
            // 每次重新请求接口获取数据
            if (this.dropSelectMode) return this.requestMenuList;
            // 外部传入固定数据
            return this.menuList || [];
        },
        // input类型  输入框内显示的label
        selectedLabel() {
            const labels = this.selectedMenu.map(o => o?.label);
            return labels?.join(',');
        },
        // 所有组选项
        allGroupNodes() {
            const keys = this.dropMenuList.flatMap(menu => (menu.groupList || [])
            ).map(node => node?.key) || [];
            return [...new Set(keys)];
        },
        // 所有节点key选项
        allNodes() {
            const keys = this.dropMenuList.flatMap(menu =>
                (menu.groupList || []).flatMap(group =>
                    (group?.subGroupList || []).flatMap(subGroup => subGroup.nodes || [])
                )
            ).map(node => node?.value) || [];
            return [...new Set(keys)];
        },
        // 所有子分组key选项
        allSubNodes() {
            const keys =  this.dropMenuList.flatMap(menu =>
                (menu.groupList || []).flatMap(group => (group?.subGroupList || []))
            ).map(node => node?.key) || [];
            return [...new Set(keys)];
        }
    },
    methods: {
        /**
         * 重置内部搜索值
         */
        clearInnerData(){
            this.tabName = '';
            this.searchValue = '';
        },
        /**
         * 清空选中值，不会触发onChange
         */
        clearSelectedMenu(){
            this.selectedMenu = [];
        },
        /**
         * 手动设置初始值，外部调用方法，hasConfirm为true时赋值立刻生效
         */
        setSelectMenu(node, hasConfirm = true){
            if (!node?.length) return;
            this.$nextTick(() => {
                this.selectedMenu = !this.isMultiple ? node : [...node];
                // 所选择的节点可能不存在则去除
                this.selectedMenu = this.selectedMenu.filter(o => this.allNodes.includes(o.value) || this.allSubNodes.includes(o.value) || this.allGroupNodes.includes(o.value));
                hasConfirm && this.$emit('confirm', this.selectedMenu);
            });
        },
        /**
         * 获取最新选中值
         */
        getSelectMenu(){
            return this.selectedMenu;
        },
        /**
         * 控制列表是否显示
         */
        async onFocus(e){
            e && e.preventDefault();
            e && e.stopPropagation();
            // 面板已经展开时避免重复获取数据
            if (this.disabled || this.focusStatus) return;
            this.focusStatus = true;
            try {
                this.loading = true;
                this.isChange = false;
                this.clearInnerData();
                // 每次点击都重新获取数据
                if (this.dropSelectMode){
                    if (typeof this.queryListMethod !== 'function') {
                        console.warn('查询方法不正确');
                        return;
                    }
                    const data = await this.queryListMethod();
                    if (data?.length) {
                        this.requestMenuList = [...data];
                    } else {
                        this.requestMenuList = [];
                    }
                }
                // 重新加载列表后  所选择的节点可能不存在则去除
                this.selectedMenu = this.selectedMenu.filter(o => this.allNodes.includes(o.value) || this.allSubNodes.includes(o.value) || this.allGroupNodes.includes(o.value));

                // 判断显示的第一个节点在哪个机房
                if (this.selectedMenu?.length){
                    this.tabName = this.findMenuIdByNodeId();
                }

                this.getFilterDropMenuList();

            } finally {
                this.loading = false;
            }
        },
        /**
         * 外层输入框全部清空
         */
        onClear(e){
            this.searchValue = '';
            this.selectedMenu = [];
            this.selectAllList = [];
            this.selectSubAllList = [];
            e.preventDefault();
            e.stopPropagation();
            this.$emit('confirm', this.selectedMenu);
        },
        /**
         * 收缩展开
         */
        onPack(e){
            if (this.disabled) {
                this.focusStatus = false;
                return;
            }
            e.preventDefault();
            e.stopPropagation();
            const status = !this.focusStatus;
            if (!status){
                this.focusStatus = false;
                this.isChange && this.$emit('confirm', this.selectedMenu);
            } else {
                this.onFocus(e);
            }
        },
        /**
         * 点击选择事件,再次点击取消
         */
        handleSelectedClick(node){
            const index = this.selectedMenu.findIndex(o => o?.value === node?.value);
            if (index > -1){
                // 多选时 已存在再次点击取消
                if (this.isMultiple){
                    this.isChange = true;
                    this.selectedMenu.splice(index, 1);
                } else {
                    this.selectedMenu = [];
                }
            } else {
                this.isChange = true;
                const vnode = {
                    ...node,
                    label: node?.label,
                    value: node?.value
                };
                if (this.isMultiple) {
                    this.selectedMenu.push(vnode);
                } else {
                    this.selectedMenu = [vnode];
                    // 单选选中立即关闭
                    this.focusStatus = false;
                    this.$emit('confirm', this.selectedMenu);
                }
            }

            // 更新联动状态
            this.processKeys();
        },
        /**
         * 特殊功能----- 分组和子分组、节点选择互斥------单选
         * 分组点击选择事件,再次点击取消
         */
        handleGroupSelectedClick(group){
            const index = this.selectedMenu.findIndex(o => o?.value === group?.key);
            if (index > -1){
                this.selectedMenu = [];
            } else {
                this.isChange = true;
                const vnode = {
                    label: group?.label,
                    value: group?.key,
                    type: 'group'
                };
                this.selectedMenu = [vnode];
                // 单选选中立即关闭
                this.focusStatus = false;
                this.$emit('confirm', this.selectedMenu);
            }
        },
        /**
         * 特殊功能----- 子分组和节点选择互斥-------单选
         * 子分组点击选择事件,再次点击取消
         */
        handleSubSelectedClick(subGroup){
            const index = this.selectedMenu.findIndex(o => o?.value === subGroup?.key);
            if (index > -1){
                this.selectedMenu = [];
            } else {
                this.isChange = true;
                const vnode = {
                    label: subGroup?.label,
                    value: subGroup?.key,
                    type: 'subGroup'
                };
                this.selectedMenu = [vnode];
                this.focusStatus = false;
                this.$emit('confirm', this.selectedMenu);
            }
        },
        /**
         * 获取匹配任何组的菜单项、移除没有匹配任何组的菜单项
         */
        getFilterDropMenuList(){
            const that = this;
            this.loading = true;
            this.filterDropMenuList = this.dropMenuList.map(menuItem => {
                const filteredGroups = menuItem?.groupList?.map(group => {
                    const filteredShardingNos = group?.subGroupList?.map(shard => ({
                        ...shard,
                        // eslint-disable-next-line max-nested-callbacks
                        nodes: shard?.nodes?.filter(node => node?.label?.indexOf(that.searchValue) !== -1)
                    })).filter(shard => shard?.nodes?.length > 0);

                    if (filteredShardingNos.length === 0) {
                        return null;
                    }
                    return {
                        ...group,
                        subGroupList: filteredShardingNos
                    };
                }).filter(group => group !== null);

                if (filteredGroups.length === 0) {
                    return null;
                }
                return {
                    ...menuItem,
                    groupList: filteredGroups
                };
            }).filter(menuItem => menuItem !== null);

            // 搜索后机房不存在 则 默认选中第一个
            this.tabName = _.find(this.filterDropMenuList, ['menuId', this.tabName])?.menuId || this.filterDropMenuList?.[0]?.menuId || '';

            // 更新联动状态
            this.processKeys();

            this.loading = false;
        },
        /**
         * tag删除
         */
        handleTagClose(value){
            const index = this.selectedMenu.findIndex(o => o?.value === value);
            if (index > -1){
                this.selectedMenu.splice(index, 1);
            }
            // 更新联动状态
            this.processKeys();
        },
        /**
         * tag点击---无点击事件、主要用于聚焦显示面板
         */
        handleTagClick(_id){
            this.onFocus();
        },
        // 匹配对应分组、子分组下的全部数据
        matchAllData(groupKey, subKey){
            let nodes = [];
            for (const menu of this.filterDropMenuList) {
                const group = menu.groupList.find(group => group?.key === groupKey);
                if (!group) continue;
                if (subKey){
                    const subGroup = group.subGroupList.find(sharding => sharding?.key === subKey);
                    if (subGroup) {
                        nodes = subGroup.nodes || [];
                        break;
                    }
                } else {
                    for (const sharding of group.subGroupList) {
                        nodes.push(...(sharding.nodes || []));
                    }
                }
            }
            return nodes;
        },
        /**
         * 分组全选\全不选
         */
        handleSelectedPartsClick(groupKey){
            if (!(this.showGroupCheckBox && this.showCheckBox && this.isMultiple)) return;
            this.isChange = true;
            const nodes = this.matchAllData(groupKey);
            const index = this.selectAllList.findIndex(o => o === groupKey);
            if (index > -1){
                nodes.forEach(o => this.handleTagClose(o?.value));
            } else {
                nodes.forEach(node => {
                    const index = this.selectedMenu.findIndex(o => node?.value === o?.value);
                    if (index === -1){
                        this.selectedMenu.push({
                            label: node?.label,
                            value: node?.value
                        });
                    }
                });
            }
            // 更新联动状态
            this.processKeys();
        },
        /**
         * 子分组全选\全不选
         */
        handleSelectedSubSubPartsClick(groupKey, subKey){
            if (!(this.showSubCheckBox && this.showCheckBox && this.isMultiple)) return;
            this.isChange = true;
            const nodes = this.matchAllData(groupKey, subKey);
            const index = this.selectSubAllList.findIndex(o => o === subKey);
            if (index > -1){
                nodes.forEach(o => this.handleTagClose(o?.value));
            } else {
                nodes.forEach(node => {
                    const index = this.selectedMenu.findIndex(o => node?.value === o?.value);
                    if (index === -1){
                        this.selectedMenu.push({
                            label: node?.label,
                            value: node?.value
                        });
                    }
                });
            }
            // 更新联动状态
            this.processKeys();
        },
        /**
         * 更新分组菜单、子组菜单、选项节点是否选中联动
         */
        processKeys() {
            // 未选中、联动全部取消
            if (!this.selectedMenu?.length){
                this.selectSubAllList = [];
                this.selectAllList = [];
                return;
            }
            const savedShardingKeys = new Set();
            const savedGroupKeys = new Set();
            const groupList = this.filterDropMenuList.map(o => o.groupList)?.flat();

            groupList.forEach(group => {
                let allShardingNosProcessed = true;

                group.subGroupList.forEach(subGroup => {
                    const allNodesProcessed = subGroup.nodes.every(node => this.hasNodeKey(node.value));
                    if (allNodesProcessed) {
                        savedShardingKeys.add(subGroup.key);
                    } else {
                        allShardingNosProcessed = false;
                    }
                });

                if (allShardingNosProcessed) {
                    savedGroupKeys.add(group.key);
                }
            });
            this.selectSubAllList = Array.from(savedShardingKeys);
            this.selectAllList =  Array.from(savedGroupKeys);
        },
        /**
         * 找寻当前选中项所在第一个最上层分类-----用于打开面板时回显
         */
        findMenuIdByNodeId() {
            const ids = this.selectedMenu.map(o => o?.value);
            for (const menu of this.dropMenuList) {
                for (const group of (menu?.groupList || [])) {
                    if (ids.includes(group?.key)) {
                        return menu.menuId;
                    }
                    for (const subGroup of (group?.subGroupList || [])) {
                        if (ids.includes(subGroup?.key)) {
                            return menu.menuId;
                        }
                        for (const node of (subGroup?.nodes || [])) {
                            if (ids.includes(node?.value)) {
                                return menu.menuId;
                            }
                        }
                    }
                }
            }
            return '';
        },
        /**
         * 判断所选节点是否存在
         */
        hasNodeKey(key){
            const node = _.find(this.selectedMenu, ['value', key]);
            return !!node;
        },
        /**
         * 判断所选子分组节点是否存在
         */
        hasSubKey(key){
            const node = _.find(this.selectedMenu, ['value', key]);
            return !!node;
        },
        /**
         * 判断所选分组节点是否存在
         */
        hasGroupKey(key){
            const node = _.find(this.selectedMenu, ['value', key]);
            return !!node;
        },
        /**
         * 判断所选分组是否存在
         */
        hasSelectAllKey(key){
            return this.selectAllList.includes(key);
        },
        /**
         * 判断所选子分组是否存在
         */
        hasSelectSubAllKey(key){
            return this.selectSubAllList.includes(key);
        },

        // ------------------------------------------------------------------------- 以下是element元素代码------------------------------------------------

        // ----------------------------------外部框
        // 渲染输入框
        renderInput() {
            return (
                <h-input
                    ref="input"
                    titleShow
                    placeholder={this.placeholder}
                    value={this.selectedLabel}
                    readonly // 触发focus事件，disabled无法触发focus事件
                    v-on:on-focus={_.debounce(this.onFocus, 300)}
                >
                    <div slot="append" class="select-search-append" ref="input-append">
                        {this.clearable && this.selectedMenu?.length ? this.renderClearIcon() : ''}
                        <h-icon
                            v-on:on-click={this.onPack}
                            class="select-search-append-search"
                            name={this.focusStatus ? 'unfold' : 'packup'}
                        />
                    </div>
                </h-input>
            );
        },
        // 渲染选择标签
        renderSelectTags() {
            return (
                <div v-on:click={this.onFocus} ref="input" class="apm-drop-menu-select-tag-container">
                    <div class="apm-drop-menu-select-tag" v-on:click={this.onFocus}>
                        {this.selectedMenu?.length > 0 ? this.renderSelectedMenu() : <span class="placeholder-span">{this.placeholder}</span>}
                    </div>
                    <div class="apm-drop-menu-select-tag-append" ref="input-append">
                        {this.clearable && !this.disabled && this.selectedMenu?.length ? this.renderClearIcon() : ''}
                        <h-icon
                            v-on:on-click={this.onPack}
                            class="select-search-append-search"
                            name={this.focusStatus ? 'unfold' : 'packup'}
                        />
                    </div>
                </div>
            );
        },
        // 渲染文本标签
        renderSelectText(){
            return (
                <div v-on:click={this.onFocus} ref="input" class="apm-drop-menu-select-tag-container apm-drop-menu-text" >
                    <div class="apm-drop-menu-select-tag" v-on:click={this.onFocus}>
                        {this.selectedMenu?.length > 0 ?  <span>
                            {this.selectedMenu.slice(0, this.collapseTagsNumber).map(menu => menu?.label).join(',')}
                            {this.selectedMenu.length > this.collapseTagsNumber && '等' + String(this.selectedMenu.length) + '个'}
                        </span> : <span class="placeholder-span" style="margin-left: 0">{this.placeholder}</span>}
                    </div>
                    <div class="apm-drop-menu-select-tag-append" ref="input-append">
                        {this.clearable && !this.disabled && this.selectedMenu?.length ? this.renderClearIcon() : ''}
                        <h-icon
                            v-on:on-click={this.onPack}
                            class="select-search-append-search"
                            name={!this.focusStatus ? 'unfold' : 'packup'}
                        />
                    </div>
                </div>
            );
        },
        // 渲染清除图标
        renderClearIcon() {
            return (
                <svg
                    onClick={this.onClear}
                    ref="clearIcon"
                    t="1730970250830"
                    class="clear-icon"
                    viewBox="0 0 1024 1024"
                    version="1.1"
                    xmlns="http://www.w3.org/2000/svg"
                    p-id="6450"
                    width="200"
                    height="200"
                >
                    <path
                        d="M512 960C264.789333 960 64 759.210667 64 512S264.789333 64 512 64 960 264.789333 960 512 759.210667 960 512 960z m169.6-323.584L557.184 512l124.416-124.416a15.957333 15.957333 0 0 0 0-22.784l-22.784-22.784a15.957333 15.957333 0 0 0-22.826667 0L512 466.773333 387.584 342.4a15.957333 15.957333 0 0 0-22.784 0l-22.784 22.784a15.957333 15.957333 0 0 0 0 22.826667l124.373333 124.373333-124.373333 124.416a15.957333 15.957333 0 0 0 0 22.784l22.784 22.826667c6.4 6.4 16.384 6.4 22.784 0L512 557.994667l124.416 124.416c6.4 6.4 16.384 6.4 22.784 0l22.784-22.826667a16.810667 16.810667 0 0 0-0.384-23.168z"
                        fill="#9296A1"
                        p-id="6451"
                    />
                </svg>
            );
        },

        // 渲染已选择的菜单项
        renderSelectedMenu() {
            return this.collapseTags
                ? (
                    <div>
                        {this.selectedMenu.slice(0, this.collapseTagsNumber).map(menu => this.renderTag(menu))}
                        {this.selectedMenu.length > this.collapseTagsNumber && this.renderCollapsedTag()}
                    </div>
                )
                : (
                    this.selectedMenu.map(menu => this.renderTag(menu))
                );
        },

        // 渲染单个标签
        renderTag(menu) {
            return (
                <a-tag
                    ref={'tag' + menu.value}
                    key={menu.value}
                    closable
                    id={menu.value}
                    size='small'
                    name={menu.label}
                    v-on:on-close={this.handleTagClose}
                    v-on:on-click={this.handleTagClick}
                />
            );
        },

        // 渲染折叠标签
        renderCollapsedTag() {
            return (
                <a-tag
                    ref='tag-collapse'
                    closable={false}
                    id='tag-collapse'
                    size='small'
                    name={'+' + String(this.selectedMenu.length - this.collapseTagsNumber)}
                    v-on:on-click={this.handleTagClick}
                />
            );
        },

        // ---------------------------------------------下拉面板内容
        // 渲染分组
        renderGroup(group) {
            return (
                <div class="apm-drop-menu-select-wrap-list">
                    <div class="apm-drop-menu-select-wrap-key panel-key">
                        {this.showCheckBox && this.showGroupCheckBox && this.isMultiple
                            ? this.renderCheckbox(group.label, this.hasSelectAllKey(group.key), () => { this.handleSelectedPartsClick(group.key); })
                            :  this.showGroupRadioBox && this.groupCanClick && !this.isMultiple
                                ? this.renderRadioBox(group.label, this.hasGroupKey(group.key), () => { this.handleGroupSelectedClick(group); })
                                : this.renderLabel(group.label)}
                    </div>
                    <div class="apm-drop-menu-select-wrap-sub-part">
                        {group.subGroupList?.map(subGroup => this.renderSubGroup(group.key, subGroup))}
                    </div>
                </div>
            );
        },
        // 渲染子分组
        renderSubGroup(groupKey, subGroup) {
            return (
                <div class="apm-drop-menu-select-wrap-nodes">
                    <div class="apm-drop-menu-select-wrap-key sub-key">
                        {this.showCheckBox && this.showSubCheckBox && this.isMultiple
                            ? this.renderCheckbox(subGroup.label, this.hasSelectSubAllKey(subGroup.key), () => { this.handleSelectedSubSubPartsClick(groupKey, subGroup.key); })
                            : this.subCanClick && !this.isMultiple ? this.renderRadioBox(subGroup.label, this.hasSubKey(subGroup.key), () => { this.handleSubSelectedClick(subGroup); })
                                : this.renderLabel(subGroup.label)}
                    </div>
                    <div class="apm-drop-menu-select-wrap-values">
                        {subGroup.nodes?.map(node => this.renderNode(node))}
                    </div>
                </div>
            );
        },
        // 渲染节点
        renderNode(node) {
            return !this.showCheckBox
                ? <h-radio
                    value={this.hasNodeKey(node.value) ? true : false}
                    class={'apm-drop-menu-select-wrap-value'}
                    v-on:on-change={() => this.handleSelectedClick(node)}
                    title={node.label}
                >
                    {this.renderLabelContent(node)}
                    <span class={node?.badge ? 'main-flag' : ''}></span>
                </h-radio>
                : <h-checkbox
                    value={this.hasNodeKey(node.value) ? true : false}
                    v-on:on-change={() => { this.handleSelectedClick(node); }}
                    class={'apm-drop-menu-select-wrap-value'}
                    title={node.label}
                >
                    {this.renderLabelContent(node)}
                    <span class={node?.badge ? 'main-flag' : ''}></span>
                </h-checkbox>;
        },
        // 动态匹配关键字 element样式
        renderLabelContent(node) {
            return renderTextWithMatch({
                label: node?.label,
                keyword: this.searchValue,
                ignoreUpperCase: this.ignoreUpperCase,
                useFuzzy: this.useFuzzy
            }).map((labelItem, index) => (
                <span key={index} class="option-text" data-match={labelItem.match}>
                    {labelItem.value}
                </span>
            ));
        },

        // 渲染复选框
        renderCheckbox(label, value, onChange) {
            return (
                <h-checkbox
                    class="apm-drop-menu-select-wrap-key-part"
                    value={value}
                    v-on:on-change={onChange}
                    title={label}
                >
                    {label}
                </h-checkbox>
            );
        },
        // 渲染分组标签
        renderLabel(label) {
            return (
                <div class="apm-drop-menu-select-wrap-key-part" title={label}>
                    <span class="apm-drop-menu-select-wrap-key-part-span">{label}</span>
                </div>
            );
        },
        // 渲染支持点的分组标签
        renderRadioBox(label, value, onChange){
            return <h-radio
                class="apm-drop-menu-select-wrap-key-part"
                v-on:on-change={onChange}
                value={value}
                title={label}
            >
                {label}
            </h-radio>;
        }
    },
    // eslint-disable-next-line complexity
    render() {
        return  <div class='apm-drop-menu-select-box'>
            {/* 遮罩层 */}
            { this.focusStatus && <div class="apm-drop-menu-select-wrap-mask" id="apm-drop-menu-select-wrap-mask"></div>}
            {/* 具体面板 */}
            <div class="apm-drop-menu-select" id="apm-drop-menu-select-container">
                {/* ----------------------------------------------------------------------- 外部框 ------------------------------------------------------------------------------- */}
                {
                    this.menuMode === 'input' && this.renderInput()
                }
                {
                    this.menuMode === 'select' && this.renderSelectTags()
                }
                {
                    this.menuMode === 'text' && this.renderSelectText()
                }
                {/* ----------------------------------------------------------------------- 下拉面板内容 ------------------------------------------------------------------------------- */}
                {
                    this.focusStatus && this.dropMenuList.length ? <div
                        class="apm-drop-menu-select-wrap"
                    >
                        {/* 搜索 */}
                        <h-input
                            ref="inner-input"
                            placeholder={this.innerPlaceholder}
                            v-model={this.searchValue}
                            clearable
                            icon="search"
                            v-on:on-click={this.getFilterDropMenuList}
                            v-on:on-clear={this.getFilterDropMenuList}
                            v-on:on-change={_.debounce(this.getFilterDropMenuList, 300)}
                        ></h-input>
                        {/* tips */}
                        {
                            this.tipText
                                ? <a-tips
                                    style="margin-top: 10px"
                                    theme="dark"
                                    type={this.tipType}
                                    tipText={this.tipText}
                                    closable={this.tipCloseable}
                                ></a-tips> : ''
                        }
                        {
                            this.filterDropMenuList?.length > 1 ? <h-tabs class="apm-drop-menu-select-tab" style={{ height: this.tipText ? 'calc(100% - 60px)' : 'calc(100% - 20px)' }} size="small" showArrow  type="card" ref="tab" v-model={this.tabName} >
                                {
                                    this.filterDropMenuList.map((item, _itemIdx) => {
                                        return <h-tab-pane label={item.menuName} name={item.menuId}>{
                                            item.groupList.map((group, _groupIdx) => this.renderGroup(group))
                                        }</h-tab-pane>;
                                    })
                                }
                            </h-tabs> : null
                        }

                        {
                            this.filterDropMenuList?.length === 1 && this.filterDropMenuList?.[0]?.menuId === 'roomDefault' ? <div class="apm-drop-menu-select-simple-tab">
                                {
                                    this.filterDropMenuList.map((item, _itemIdx) => {
                                        return item.groupList.map((group, _groupIdx) => this.renderGroup(group));
                                    })
                                }
                            </div> : null
                        }

                        {
                            !this.filterDropMenuList?.length ? <no-data text="无数据"  style={{ height: this.tipText ? 'calc(100% - 60px)' : '100%' }} /> : ''
                        }
                        {this.loading &&  <h-spin fix>
                            <h-icon name="load-c" size="18" class="spin-icon-load"></h-icon>
                        </h-spin> }
                    </div> : ''
                }
                {  this.focusStatus && !this.dropMenuList.length &&
                <div class="apm-drop-menu-select-wrap"
                >
                    <no-data text="无数据" style={{ height: this.tipText ? 'calc(100% - 70px)' : '100%' }}/>
                </div>
                }
            </div>
        </div>;
    }
};
