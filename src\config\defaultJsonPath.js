// 默认的 jsonpath 配置
export const defaultJsonPathConfig = {
    // 示例配置
    'ldp_mproxy/GetAppJsonInfo': [
        {
            jsonPath: '$.AppInfo.Plugins[*].Routes',
            aliasName: '对象数组',
            sortable: true
        },
        {
            jsonPath: '$.AppInfo.Plugins[*].Routes',
            aliasName: '对象数组',
            sortable: false
        },
        {
            jsonPath: '$.AppInfo.*',
            aliasName: '混合',
            sortable: true
        },
        {
            jsonPath: '$.AppInfo.*',
            aliasName: '混合',
            sortable: false
        },
        {
            jsonPath: '$.AppInfo.Plugins[0].Lib',
            aliasName: '空数组',
            sortable: true
        },
        {
            jsonPath: '$.AppInfo.Plugins[0].Lib',
            aliasName: '空数组',
            sortable: false
        },
        {
            jsonPath: '$.AppInfo.Plugins[*]',
            aliasName: '混合',
            sortable: true
        },
        {
            jsonPath: '$.AppInfo.Plugins[*]',
            aliasName: '混合',
            sortable: false
        },
        {
            jsonPath: '$.AppInfo.Plugins[0].Tcp',
            aliasName: '空对象',
            sortable: true
        },
        {
            jsonPath: '$.AppInfo.Plugins[0].Tcp',
            aliasName: '空对象',
            sortable: false
        },
        {
            jsonPath: '$.AppInfo.Plugins[0].TimerIntervalMilli',
            aliasName: '基础类型',
            sortable: true
        },
        {
            jsonPath: '$.AppInfo.Plugins[0].TimerIntervalMilli',
            aliasName: '基础类型',
            sortable: false
        },
        {
            jsonPath: '$.AppInfo.Plugins[*].Singleton',
            aliasName: '复杂对象',
            sortable: true
        },
        {
            jsonPath: '$.AppInfo.Plugins[*].Singleton',
            aliasName: '复杂对象',
            sortable: false
        },
        {
            jsonPath: '$.',
            aliasName: '',
            sortable: false
        }
    ]
    // 可以添加更多默认配置...
};
