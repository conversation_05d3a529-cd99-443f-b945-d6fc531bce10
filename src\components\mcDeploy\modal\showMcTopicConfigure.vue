<template>
    <div>
        <h-msg-box
            v-model="modalData.status"
            :mask-closable="false"
            :escClose="true"
            :title="`${modalData.TopicName}主题配置信息`"
            width="60"
            max-height="450"
            :allowCopy="true"
        >
            <div class="config-title">基本信息</div>
            <h-form :label-width="160" cols="4">
                <h-form-item
                    v-for="item in basicInfo" :key="item.value"
                    :label="item.label + '：'">
                    <p>{{ modalData[item.value] }}</p>
                </h-form-item>
            </h-form>

            <div class="config-title"> 分区分布 </div>
            <h-table :columns="partitionColumns" :data="partitionData"></h-table>

            <div class="config-title">过滤条件</div>
            <h-table :columns="filterColumns" :data="filterData"></h-table>
        </h-msg-box>
    </div>
</template>

<script>
export default {
    name: 'ShowMcTopicConfigure',
    components: { },
    props: {
        modalData: {
            type: Object,
            default: () => {}
        }
    },
    data() {
        return {
            basicInfo: [
                {
                    value: 'Topic',
                    label: '主题名'
                },
                {
                    value: 'TopicNo',
                    label: '主题编号'
                },
                {
                    value: 'Desc',
                    label: '描述'
                },
                {
                    value: 'ReliableLevel',
                    label: '可靠等级'
                },
                {
                    value: 'copyMode',
                    label: '集群间复制模式'
                },
                {
                    value: 'OrderLevel',
                    label: '有序等级'
                },
                {
                    value: 'LifeTime',
                    label: '有效期等级'
                },
                {
                    value: 'BusinessVerify',
                    label: '是否业务校验'
                },
                {
                    value: 'Local',
                    label: '是否局部主题'
                },
                {
                    value: 'PartitionCount',
                    label: '分区数'
                },
                {
                    value: 'ReplicationFactor',
                    label: '副本数'
                },
                {
                    value: 'IsStatic',
                    label: '是否静态'
                },
                {
                    value: 'OffsetSave',
                    label: '是否服务端记录消息偏移'
                }
            ],
            partitionColumns: [
                {
                    title: '分区主所在节点编号',
                    key: 'Leader'
                },
                {
                    title: '分区备所在节点编号',
                    key: 'Replicas'
                },
                {
                    title: '分区服务状态',
                    key: 'ServiceStatus'
                },
                {
                    title: '分区版本号',
                    key: 'Version'
                }
            ],
            partitionData: [],
            filterColumns: [
                {
                    title: '过滤条件名',
                    key: 'Name'
                },
                {
                    title: '是否参与计算分片',
                    key: 'PartitionCalc'
                }
            ],
            filterData: []
        };
    }
};
</script>

<style lang="less" scoped>
/deep/ .h-modal-body {
    padding: 0 16px 16px;
}

/deep/ .h-table-wrapper {
    margin: 10px 10px 0;
}

.config-title {
    padding: 10px 0 0 30px;
    font-size: 12px;
    color: #333;
    font-weight: 500;
    height: 40px;
    border-bottom: 1px solid #ddd;
    margin-top: 16px;

    &::before {
        display: inline-block;
        position: relative;
        left: -13px;
        top: 3px;
        content: "";
        width: 4px;
        height: 15px;
        background: var(--link-color);
    }
}
</style>
