<template>
  <div>
    <h-drawer
        v-model="modalInfo.status"
        width="60"
        title="历史执行SQL"
        class="drawer">
        <div v-if="historyList.length" style="height: 100%;">
            <div class="header">
                <h-input
                    v-model="searchQuery"
                    icon="search"
                    placeholder="搜索历史SQL"
                    style="flex: 1;"
                    @on-change="filterResults"></h-input>
                <h-checkbox v-model="showExactTime" style="width: 130px; padding-left: 10px;">显示具体执行时间</h-checkbox>
            </div>

            <div class="collapse-wrapper">
                <h-collapse
                    v-for="(item, index) in historyList"
                    v-show="panelVisibility[`${index}`]"
                    :key="index"
                    v-model="activePanel"
                    style="margin-bottom: 10px;">
                    <h-panel :name="index.toString()">
                        <div class="panel-header">
                            <span>{{ formatDates(new Date(item[0].time)) }}<span style="color: #485565;"> | </span>{{item[0].serviceName || '-'}}<span style="color: #485565;"> | </span>{{item[0].productName || '-'}}</span>
                            <h-button @click.stop="importSQL(item)">导入SQL</h-button>
                        </div>
                        <div slot="content" class="pane-content">
                            <div v-show="showExactTime" class="pane-content-left">
                                <p v-for="(ele, idx) in item"
                                    v-show="showExactTime"
                                    :key="`t-${idx}`" >{{ formatDates(new Date(ele.time)) }}</p>
                            </div>
                            <div class="pane-content-right">
                                <h-timeline-item
                                    v-for="(ele, idx) in item"
                                    :key="`c-${idx}`"
                                    color="#444A60">
                                    <p class="pane-content-info">
                                        <span style="font-weight: bold; color: #fff;">{{ ele.label }}
                                            <span v-if="ele.badge" class="main-flag"></span> : </span>
                                        <span style="color: green;">{{ ele.sql }}</span>
                                    </p>
                                </h-timeline-item>
                            </div>
                        </div>
                    </h-panel>
                </h-collapse>
            </div>
        </div>
        <no-data v-else isWhite />
    </h-drawer>
  </div>
</template>

<script>
import _ from 'lodash';
import { formatDates } from '@/utils/utils';
import noData from '@/components/common/noData/noData';
export default {
    props: {
        modalInfo: {
            type: Object,
            default: null
        },
        historyList: {
            type: Array,
            default: () => []
        }
    },
    components: { noData },
    data() {
        return {
            modalData: this.modalInfo,
            searchQuery: '',
            showExactTime: false,
            activePanel: [],
            panelVisibility: {},
            newHistoryList: [],
            sqlData: {}
        };
    },
    mounted() {
        this.newHistoryList = _.cloneDeep(this.historyList);
        this.newHistoryList.forEach((ele, idx) => {
            this.activePanel.push(idx.toString());
            this.panelVisibility[idx] = true;
            this.sqlData[idx] = [];
            ele.forEach(item => {
                this.sqlData[idx].push({
                    sql: item.sql
                });
            });
        });
    },
    methods: {
        togglePanel(name) {
            const index = this.activePanel.indexOf(name);
            if (index > -1) {
                this.activePanel.splice(index, 1);
            } else {
                this.activePanel.push(name);
            }
        },
        formatDates(time) {
            return formatDates(time);
        },
        importSQL(data) {
            const list = data.map(o => { return o.sql; });
            const uniqueList = [...new Set(list)];
            this.$emit('addSqlToEdit', uniqueList);
            this.$hMessage.success(`导入${uniqueList.length}条SQL成功`);
        },
        filterResults() {
            /**
                * 遍历sqlData中的每个面板，检查面板中的SQL语句是否包含搜索查询，并更新panelVisibility。
            */
            for (const panel of Object.keys(this.sqlData)) {
                const sqlStatements = this.sqlData[panel].map(item => item.sql);
                this.panelVisibility[panel] = sqlStatements.some(sql => sql.includes(this.searchQuery));
            }
        }

    }
};
</script>

<style scoped lang="less">
@import url("@/assets/css/drawer.less");

.header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
}

.collapse-wrapper {
    height: calc(100% - 60px);
    overflow-y: auto;

    /deep/ .h-collapse-header {
        background-color: rgb(44, 51, 74);
        font-weight: 500 !important;
        color: #fff !important;
    }

    .panel-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
    }

    .panel-header h-icon {
        cursor: pointer;
    }

    .panel-header button {
        background: none;
        border: none;
        color: #1890ff;
        cursor: pointer;
    }

    /deep/ .h-collapse {
        border: 1px solid #31364a;
    }

    .pane-content {
        display: flex;

        & > .pane-content-left {
            width: 125px;

            & > p {
                height: 36px;
                color: #fff;
            }
        }

        & > .pane-content-right {
            flex: 1;

            & > p {
                height: 28px;
            }
        }

        /deep/ .h-timeline-item-head {
            width: 8px;
            height: 8px;
            left: 1px;
            top: 4px;
            background-color: #444a60;
        }

        /deep/ .h-timeline-item-tail {
            border-left: 1px solid #444a60;
            top: 4px;
        }
    }

    .pane-content li {
        height: 36px;

        .pane-content-info {
            // stylelint-disable property-no-vendor-prefix,value-no-vendor-prefix
            display: -webkit-box;
            -webkit-box-orient: vertical;
            -webkit-line-clamp: 2; /* 限制显示2行 */
            overflow: hidden;
            text-overflow: ellipsis;
            cursor: pointer;
            user-select: text;

            .main-flag {
                .node-flag("static/mainFlag.png");
            }

            .node-flag(@url) {
                display: inline-block;
                width: 11px;
                height: 11px;
                background: url(@url);
                background-size: 11px 10px;
                background-repeat: no-repeat;
                position: relative;
                top: 2px;
                margin-left: 5px;
            }
        }
    }

    .pane-content li:last-child {
        padding: 0;
    }

    /deep/ .h-collapse-content {
        background-color: var(--main-color);
        border: none;
    }

    /deep/ .h-collapse-content-box {
        padding-bottom: 0;
    }
}
</style>
