/**
 * 根据关键字显示文本，高亮显示关键字
 * @param {string} label
 * @param {string} keyword
 * @param {boolean} ignoreUpperCase
 * @param {boolean} useFuzzy
 * @return {Array<{value: typeof label, match: boolean}>}
 */
export function renderTextWithMatch({ label, keyword, ignoreUpperCase, useFuzzy }) {
    if (keyword === undefined || String(keyword).trim() === '') return [{ value: label, match: false }];
    const _keyword = ignoreUpperCase ? keyword.toLowerCase() : keyword;
    const keywordLength = keyword.length;
    const labelString = typeof label === 'string' ? label : `${label}`;
    const result = [];
    if (!useFuzzy) {
        // 非模糊搜索，只高亮从前面开始匹配到的字符
        const labelStartStr = labelString.substring(0, keywordLength);
        const _labelStartStr = ignoreUpperCase ? labelStartStr.toLowerCase() : labelStartStr;
        const match = _labelStartStr === _keyword;
        for (let i = 0; i < labelString.length; i++) {
            result.push({
                value: labelString[i],
                match: i <= keywordLength - 1 && match
            });
        }
        return result;
    }
    for (let i = 0; i < labelString.length; i++) {
        const checkString = labelString.substring(i, keywordLength + i);
        const _checkString = ignoreUpperCase ? checkString.toLowerCase() : checkString;
        result[i] = {
            value: labelString[i],
            match: result[i]?.match ?? _checkString === _keyword
        };
        if (_checkString === _keyword) {
            for (let j = 0; j < keyword.length; j++) {
                result[i + j] = {
                    value: labelString[i + j],
                    match: true
                };
            }
            // 跳过已经匹配到的字符序号，减少for循环，提升性能
            i += keyword.length - 1;
        }
    }
    return result;
}

/**
 * 通过refs获取当前refs的所有元素
 * @param {any} vNode
 */
export function getAllTarget(vNode) {
    const result = new Set();
    result.add(vNode?.$el);
    Object.values(vNode.$refs).forEach(item => {
        result.add(item);
    });
    if (vNode.$children.length) {
        vNode.$children.forEach(node => {
            getAllTarget(node).forEach(item => result.add(item));
        });
    }
    return result;
}
