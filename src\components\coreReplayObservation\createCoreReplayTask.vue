<template>
  <div class="create-core-replay-box">
    <div class="title">
      <a-title class="apm-title-create">
        <template v-slot>
          <div class="create-title">
            <h-icon name="arrow-left-c" @on-click="handleCancel"> </h-icon>
            <span>{{ type === 'copy'? '复制重演任务' : '创建重演任务' }}</span>
          </div>
          <div class="product-select">
            <h-select v-model="productInstNo" disabled>
              <h-option
                v-for="item in productList"
                :key="item.id"
                :value="item.productInstNo"
              >
                {{ item.productName }}
              </h-option>
            </h-select>
          </div>
        </template>
      </a-title>
    </div>
    <div class="create-step">
      <h-steps :current="currentStep" class="steps-box">
        <h-step v-for="item in stepList" :key="item" :title="item"> </h-step>
      </h-steps>

      <div class="content-box">
        <a-loading v-if="loading"></a-loading>
        <!-- 提示信息 -->
        <div class="tips-box">
          <div class="tip-content">
            {{ createTips[currentStep].content }}
          </div>
        </div>

        <a-tips
          v-if="type === 'copy'"
          style="margin: 10px 0; width: 80%;"
          type="warning"
          theme="dark"
          tipText="原重演任务中的重演对象可能发生变化，请仔细核对。"
        ></a-tips>
        <!-- 确定校验对象 -->
        <div v-show="currentStep === 0" class="form-box-0">
          <h-form
            ref="formStep0"
            :model="formItems"
            :label-width="100"
            :rules="ruleValidate"
          >
            <h-form-item label="交易日期" prop="tradingDays" required>
              <h-select
                v-model="formItems.tradingDays"
                positionFixed
                :clearable="true"
                @on-change="handleDayChange"
              >
                <h-option
                  v-for="item in Object.keys(allTradeDayList)"
                  :key="item"
                  :value="item"
                  >{{ item }}
                </h-option>
              </h-select>
            </h-form-item>
            <!-- 核心-->
            <h-form-item prop="replayParams">
              <template v-slot:label><span class="core-list">核心集群</span></template>
              <record-verify-content
                ref="record-verify"
                :tradeDayList="tradeDayList"
                @choose-tables="handleChooseTable"
              >
              </record-verify-content>
              <div v-if="formItems.tradingDays && errMsg" class="error-bottom">
                <div class="error-arrow"></div>
                <div class="error-inner"> {{ errMsg }}</div>
              </div>
            </h-form-item>
          </h-form>
        </div>

        <!-- 选择校验数据 -->
        <div v-show="currentStep === 1" class="form-box-0">
          <h-form
            ref="formStep1"
            :model="formItems"
            :label-width="100"
            :rules="ruleValidate"
          >
            <h-form-item label="发送间隔" prop="sendIntervalMs" required>
              <h-input
                v-model.number="formItems.sendIntervalMs"
                type="int"
                style="width: 250px;"
                placeholder="请输入"
                clearable
              >
                <span slot="append">毫秒</span>
              </h-input>
            </h-form-item>
            <h-form-item label="任务名" prop="replayName" required>
              <h-input
                v-model.trim="formItems.replayName"
                placeholder="请输入"
                clearable
                :maxlength="20"
              >
              </h-input>
            </h-form-item>
            <h-form-item label="备注" prop="notes">
              <h-input
                v-model.trim="formItems.notes"
                placeholder="请输入"
                type="textarea"
                :rows="3"
                :canResize="false"
                :maxlength="100"
              >
              </h-input>
            </h-form-item>
          </h-form>
        </div>

        <!-- 信息核对 -->
        <div v-show="currentStep === 2" class="confirm-box">
          <h-form ref="formStep2" :model="formItems" :label-width="100">
            <h-form-item label="任务名称" prop="replayName">
              <span>{{ formItems.replayName || "-" }}</span>
            </h-form-item>
            <h-form-item label="备注" prop="notes">
              <span>{{ formItems.notes || "-" }}</span>
            </h-form-item>
            <h-form-item label="重演内容" class="form-container">
              <h-tabs
                ref="tab2"
                :value="formItems.tradingDays"
                panelRight
                :labelWidth="0"
                alginDre="left"
                class="tab-core-container"
              >
                <h-tab-pane
                  v-for="item in formItems.replayParams"
                  :key="item.tradeDay"
                  :label="item.tradeDay"
                  :name="item.tradeDay"
                >
                  <div class="core-label">{{ item.tradeDay }}</div>
                  <div class="core-container">
                    <p v-for="core in item.replayAppArray" :key="core.appName">
                      {{ core.appName }}
                    </p>
                  </div>
                </h-tab-pane>
              </h-tabs>
            </h-form-item>
            <h-form-item label="任务参数" class="form-box">
              <div class="form-box-param">
                <p>
                <span>发送间隔: </span
                ><span style="color: var(--font-color);"
                  >{{ formItems.sendIntervalMs }} 毫秒</span
                >
              </p>
              <div class="json-box">
                <json-viewer
                  :value="jsonData"
                  :expand-depth="10"
                  copyable
                  :expanded="true"
                >
                </json-viewer>
              </div>
              </div>
              <!-- <p>
                <span>修改路径：</span
                ><span class="text-load" title="jsonLocal">{{ jsonLocal }}</span>
              </p> -->
            </h-form-item>
          </h-form>
        </div>
      </div>

      <div class="buttom-box">
        <a-button
          v-show="currentStep"
          type="dark"
          @click="handleStepChange('-')"
        >
          上一步
        </a-button>
        <a-button
          v-show="currentStep !== 2"
          type="primary"
          :loading="nextStepLoading"
          @click="handleStepChange('+')"
        >
          下一步：{{ stepList[currentStep + 1] }}
        </a-button>
        <a-button
          v-show="currentStep === 2"
          type="primary"
          :loading="confirmLoading"
          @click="() => handleCreateTask('imde')"
        >
          创建并启动任务
        </a-button>
        <a-button
          v-show="currentStep === 2"
          :loading="confirm2Loading"
          type="dark"
           @click="() => handleCreateTask()"
        >
          创建
        </a-button>
        <a-button type="dark" @click="handleCancel">取消</a-button>
      </div>
    </div>
  </div>
</template>

<script>
import aTips from '@/components/common/apmTips/aTips';
import aTitle from '@/components/common/title/aTitle';
import aButton from '@/components/common/button/aButton';
import aLoading from '@/components/common/loading/aLoading';
import jsonViewer from 'vue-json-viewer';
import recordVerifyContent from './modal/recordVerifyContent.vue';
import {
    setReplayCreate,
    setReplayCreateAndStart,
    getReplayTradeDay,
    getReplayConfig
} from '@/api/coreReplayObservationApi';
import { getCurrentDatetime } from '@/utils/utils';

export default {
    name: 'CreateCoreReplayTask',
    components: { aTips, aTitle, aButton, aLoading, jsonViewer, recordVerifyContent },
    props: {
        productInstNo: {
            type: String,
            default: ''
        },
        productList: {
            type: Array,
            default: () => []
        }
    },
    data() {
        return {
            loading: false,
            currentStep: 0,
            confirmLoading: false,
            confirm2Loading: false,
            nextStepLoading: false,
            stepList: ['确定重演对象', '配置任务参数', '信息核对'],
            createTips: {
                0: {
                    content: '按需选择需要重演“交易日”与对应的“核心”。'
                },
                1: {
                    content: `根据实际情况配置重演任务相关参数。`
                },
                2: {
                    content:
            '基于前两个步骤的选择，将对应产生的重演任务，请仔细核对任务内容。'
                }
            },
            ruleValidate: {
                sendIntervalMs: [
                    {
                        required: true,
                        type: 'number',
                        min: 0,
                        max: 1000,
                        message: '请输入0~1000',
                        trigger: 'blur'
                    }
                ]
            },
            formItems: {
                tradingDays: '',
                sendIntervalMs: 1,
                replayParams: [],
                replayName: '重演任务' + getCurrentDatetime(),
                notes: ''
            },
            jsonData: {},
            // jsonLocal: '',
            // 交易日、以及交易日核心列表
            tradeDayList: {},
            allTradeDayList: {},
            errMsg: '',
            // 复制、创建
            type: 'create',
            isFirstRender: true
        };
    },
    methods: {
        async initData(params) {
            this.currentStep = 0;
            this.loading = true;
            try {
                await this.getReplayTradeDay();
                !params && await this.getReplayConfig();
                this.$nextTick(() => {
                    if (params) {
                        this.type = 'copy';
                        // 清理掉不在 this.allTradeDayList 中的 tradeDay 和 appName
                        const replayConfig = params?.replayConfig ? JSON.parse(params?.replayConfig) : {};
                        const tradeDayList = replayConfig?.tradeDayInfos?.map(v => v?.tradeDay).filter(day => this.allTradeDayList[day]) || [];
                        const filteredReplayConfig = replayConfig?.tradeDayInfos?.filter(o => o?.tradeDay && this.allTradeDayList[o.tradeDay]) || [];
                        const replayParams = filteredReplayConfig.map(o => {
                            const validAppNames = (o?.replayAppArray || [])
                                .filter(v => this.allTradeDayList[o.tradeDay].includes(v.appName));

                            return {
                                tradeDay: o?.tradeDay,
                                replayAppArray: validAppNames
                            };
                        });

                        this.jsonData = this.formatJsonData(replayConfig?.ldpReplayBaseConfig);

                        this.formItems = {
                            tradingDays: tradeDayList?.[0] || '',
                            sendIntervalMs: params?.sendIntervalMs ?? 1,
                            replayParams: replayParams,
                            replayName: '重演任务' + getCurrentDatetime(),
                            notes: params?.notes
                        };

                        // 空字符串不会触发change事件
                        if (!this.formItems?.tradingDays){
                            this.isFirstRender = false;
                        }
                    }
                });
            } finally {
                this.loading = false;
            }
        },
        // 交易日切换
        handleDayChange(val) {
            this.hasAllTradeDays = true;
            if (!val){
                this.tradeDayList = {};
                this.formItems.replayParams = [];
                this.$refs['record-verify'] && this.$refs['record-verify'].clearData();
                return;
            }
            this.tradeDayList = {};
            this.tradeDayList[val] = [...this.allTradeDayList[val]];
            // 复制默认值
            const params =
        this.isFirstRender && this.type === 'copy'
            ? this.convertReplayParams(this.formItems.replayParams || [], true) || {}
            : '';
            this.$nextTick(() => {
                if (this.$refs['record-verify']) {
                    this.$refs['record-verify'].initData(params);
                }
            });
            this.isFirstRender = false;
        },
        // 创建并启动
        async handleCreateTask(type) {
            if (type === 'imde') {
                this.confirmLoading = true;
            } else {
                this.confirm2Loading = true;
            }

            try {
                const param = {
                    productId: this.productInstNo,
                    sendIntervalMs: this.formItems?.sendIntervalMs,
                    replayParams: this.formItems?.replayParams,
                    replayName: this.formItems?.replayName,
                    notes: this.formItems?.notes,
                    ldpReplayBaseConfig: this.jsonData ? JSON.stringify(this.jsonData) : ''
                };

                // 选择API方法
                const apiFunction =
          type === 'imde' ? setReplayCreateAndStart : setReplayCreate;
                const successMessage = type === 'imde' ? '创建并启动成功' : '创建成功';

                const res = await apiFunction(param);

                if (res.code === '200') {
                    this.$hMessage.success(successMessage);
                } else if (res?.code?.length === 8) {
                    this.$hMessageSafe.error(res?.message);
                }

                this.$emit('to-task-list', 'task-list');
            } catch (err) {
                console.error(err);
            } finally {
                this.confirmLoading = false;
                this.confirm2Loading = false;
            }
        },
        // 取消-返回表格页
        handleCancel() {
            const needBack = this.currentStep > 0 ? true : false;
            this.goBack(needBack);
        },
        // 取消创建
        goBack(needVerify) {
            if (needVerify === true) {
                this.$hMsgBoxSafe.confirm({
                    title: `确认离开页面？`,
                    content: `离开后当前操作将不会保存，数据会丢失，请谨慎操作！`,
                    onOk: () => {
                        this.$emit('to-task-list', 'task-list');
                    },
                    cancelText: '留下'
                });
                return;
            }
            this.$emit('to-task-list', 'task-list');
        },
        // 切换步骤
        handleStepChange(ope) {
            if (ope === '+') {
                // 校验内容
                if (this.errMsg) return;
                this.$refs[`formStep${this.currentStep}`].validate((valid) => {
                    if (valid) {
                        try {
                            this.nextStepLoading = true;
                            this.currentStep += 1;
                        } finally {
                            this.nextStepLoading = false;
                        }
                    }
                });
                this.$refs['tab2'] && this.$refs['tab2'].updateBar();
            } else {
                this.currentStep -= 1;
            }
        },
        // 整理选中核心数据
        handleChooseTable(tables) {
            this.formItems.replayParams = [];
            this.formItems.replayParams = Object.keys(tables).map((o) => {
                return {
                    tradeDay: o,
                    replayAppArray: (tables?.[o] || []).map(v => { return { appName: v }; })
                };
            });
            // 模拟一个假校验tip
            this.errMsg = this.formItems.replayParams
                .map((o) => o.replayAppArray)
                ?.flat()?.length
                ? ''
                : '请至少选择一个核心';
        },
        // 接口---获取核心可重演交易日列表
        async getReplayTradeDay() {
            const param = {
                productId: this.productInstNo
            };
            const res = await getReplayTradeDay(param);
            if (res.code === '200') {
                this.allTradeDayList = this.convertReplayParams(res?.data || []) || [];
            }
        },
        // 任务参数不可修改
        async getReplayConfig() {
            const param = {
                productId: this.productInstNo
            };
            const res = await getReplayConfig(param);
            if (res.code === '200') {
                this.jsonData = this.formatJsonData(res?.data);
            }
        },
        // 函数转换
        convertReplayParams(params, isCopy = false) {
            const result = {};
            params.forEach((param) => {
                if (isCopy) {
                    result[param.tradeDay] = [...new Set((param?.replayAppArray || []).map(o => o?.appName))];
                } else {
                    result[param.tradeDay] = [...new Set(param?.appNameList || [])];

                }
            });
            return result;
        },
        // json转换防止报错
        formatJsonData(str) {
            if (str) {
                try {
                    const data = JSON.parse(str);
                    return data;
                } catch (error) {
                    return str;
                }
            }
            return '';
        }
    }
};
</script>

<style lang="less" scoped>
@import url("@/components/coreReplayObservation/createTask.less");
@import url("@/assets/css/json-view.less");

.create-core-replay-box {
    width: 100%;
    height: 100%;

    .title {
        min-width: 800px;

        .product-select {
            float: right;
            margin-right: 15px;
            top: 5px;
            min-width: 200px;
            width: auto;
        }

        .apm-title-create {
            &::before {
                display: none;
            }
        }
    }

    .core-list {
        &::before {
            content: "*";
            display: inline-block;
            margin-right: 2px;
            line-height: 0;
            font-size: 12px;
            color: #f5222d;
        }
    }

    .error-bottom {
        display: block;
        font-size: 12px;
        font-style: normal;
        letter-spacing: normal;
        opacity: 1;
        position: absolute;
        text-align: start;
        text-decoration: none;
        text-shadow: none;
        text-transform: none;
        white-space: nowrap;
        word-break: nowrap;
        word-spacing: normal;
        word-wrap: nowrap;
        line-height: 24px;
        z-index: 9;
    }

    .error-bottom > .error-arrow {
        width: 8px;
        height: 8px;
        margin: 0 auto;
        background-color: #f5222d;
        transform: rotate(45deg);
        transform-origin: center;
    }

    .error-inner {
        background-color: #f5222d;
        border-radius: 4px;
        color: #fff;
        max-width: 200px;
        padding: 0 8px;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        text-align: center;
        margin-top: -4px;
    }

    .create-title {
        float: left;

        .h-icon {
            cursor: pointer;
            margin-right: 8px;

            &:hover {
                color: var(--link-color);
            }
        }
    }

    .create-step {
        min-width: 800px;
        height: calc(100% - 53px);
        margin-top: 10px;
        border-radius: 4px;

        /deep/.h-steps-item .h-steps-main {
            background: var(--main-color);
        }
    }

    /deep/ .h-radio-group-item {
        background: none;
    }

    .form-box {
        .form-container {
            /deep/ .h-tabs-bar-right {
                display: none;
            }

            /deep/  .h-tabs-content-right {
                border-left: none;
            }
        }

        .form-box-param {
            border: var(--border);
            border-radius: 4px;
            padding: 0 5px 5px;
            background: var(--wrapper-color);
        }

        .json-box .jv-container {
            margin: 0;
            max-height: 262px;
        }

        p {
            height: 32px;
            line-height: 32px;

            .text-load {
                color: var(--font-color);
                width: calc(80% - 20px);
                display: inline-block;
                overflow: hidden;
                white-space: nowrap;
                word-wrap: break-word;
                text-overflow: ellipsis;
                position: relative;
                top: 7px;
            }
        }
    }
}
</style>
