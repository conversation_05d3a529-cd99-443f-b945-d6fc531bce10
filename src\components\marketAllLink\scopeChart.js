import * as echarts from 'echarts';
import { formatDate } from '@/utils/utils';
import { getMarketAllLinkPerspectiveDelay } from '@/api/httpApi';
import '@/assets/css/scopeChart.less';

export default {
    name: 'scopeChart',
    props: ['modalInfo', 'compareSpan', 'compareSpanFlag'],
    data() {
        return {
            myChart: null,
            option: {
                title: {
                    text: '',
                    textStyle: {
                        color: '#fff',
                        fontSize: 13,
                        fontWeight: 500
                    },
                    top: 0,
                    left: 26
                },
                grid: {
                    left: 50,
                    right: 1,
                    bottom: 20,
                    top: 50
                },
                tooltip: {
                    trigger: 'axis',
                    backgroundColor: 'rgba(88,94,106,0.40)',
                    borderColor: 'rgba(88,94,106,0.40)',
                    textStyle: {
                        color: '#fff'
                    },
                    formatter: function(params) {
                        let relVal = params[0].name;
                        for (let i = 0, l = params.length; i < l; i++) {
                            relVal += '<br/>' + params[i].marker + params[i].seriesName + '：' + params[i].value + ' μs';
                        }
                        return relVal;
                    },
                    // eslint-disable-next-line max-params
                    position: function (point, params, dom, rect, size) {
                        // 鼠标坐标和提示框位置的参考坐标系是：以外层div的左上角那一点为原点，x轴向右，y轴向下
                        // 提示框位置
                        let x = 0; // x坐标位置
                        let y = 0; // y坐标位置

                        // 当前鼠标位置
                        const pointX = point[0];
                        const pointY = point[1];

                        // 外层div大小
                        // var viewWidth = size.viewSize[0];
                        // var viewHeight = size.viewSize[1];

                        // 提示框大小
                        const boxWidth = size.contentSize[0];
                        const boxHeight = size.contentSize[1];

                        // boxWidth > pointX 说明鼠标左边放不下提示框
                        if (boxWidth > pointX) {
                            x = 5;
                        } else {
                            // 左边放的下
                            x = pointX - boxWidth;
                        }

                        // boxHeight > pointY 说明鼠标上边放不下提示框
                        if (boxHeight > pointY) {
                            y = 5;
                        } else {
                            // 上边放得下
                            y = pointY - boxHeight;
                        }

                        return [x, y];
                    }
                },
                legend: {
                    textStyle: {
                        color: '#fff',
                        fontSize: 11,
                        padding: [0, 0, 0, 10]
                    },
                    itemHeight: 1,
                    itemWidth: 16,
                    top: 24,
                    left: 26
                },
                xAxis: {
                    type: 'category',
                    boundaryGap: false,
                    axisLine: {
                        show: true,
                        lineStyle: {
                            color: '#31364a'
                        }
                    },
                    axisTick: {
                        show: false
                    },
                    axisLabel: {
                        color: '#fff'
                    },
                    data: []
                },
                yAxis: [
                    {
                        type: 'value',
                        axisLine: {
                            show: true,
                            lineStyle: {
                                color: '#31364a'
                            }
                        },
                        axisLabel: {
                            color: '#fff'
                        },
                        splitLine: {
                            lineStyle: {
                                color: '#31364a'
                            }
                        }
                    },
                    {
                        axisLine: {
                            show: true,
                            lineStyle: {
                                color: '#31364a'
                            }
                        }
                    }
                ],
                toolbox: {
                    right: 10
                },
                series: [
                    {
                        name: '本地全系统时延',
                        type: 'line',
                        showSymbol: false,
                        lineStyle: {
                            color: '#2D8DE5'
                        },
                        itemStyle: {
                            color: '#2D8DE5'
                        },
                        data: []
                    },
                    {
                        name: `${this.compareSpanFlag}系统时延走势分析`,
                        type: 'line',
                        showSymbol: false,
                        lineStyle: {
                            color: '#5AD8A6'
                        },
                        itemStyle: {
                            color: '#5AD8A6'
                        },
                        data: []
                    }
                ]
            },
            spanFlags: {
                SSE: {
                    NSQ: {
                        Local: 'vde.x.1-nsq.x.2',
                        App: 'nsq.x.1-nsq.x.2'
                    },
                    FPGA: {
                        Local: 'vde.x.1-fpga.x.2',
                        App: 'fpga.x.1-fpga.x.2'
                    },
                    VDE: {
                        NSQ: {
                            Local: 'vde.x.1-nsq.x.2',
                            App: 'vde.x.1-vde.x.2'
                        },
                        FPGA: {
                            Local: 'vde.x.1-fpga.x.2',
                            App: 'vde.x.1-vde.x.2'
                        }
                    }
                },
                SZSE: {
                    NSQ: {
                        Local: 'mdgw.x.1-nsq.x.2',
                        App: 'nsq.x.1-nsq.x.2'
                    },
                    FPGA: {
                        Local: 'mdgw.x.1-fpga.x.2',
                        App: 'fpga.x.1-fpga.x.2'
                    },
                    MDGW: {
                        NSQ: {
                            Local: 'mdgw.x.1-nsq.x.2',
                            App: 'mdgw.x.1-mdgw.x.2'
                        },
                        FPGA: {
                            Local: 'mdgw.x.1-fpga.x.2',
                            App: 'mdgw.x.1-mdgw.x.2'
                        }
                    }
                }
            }
        };
    },
    mounted() {
        this.$nextTick(async () => {
            this.myChart = echarts.init(this.$refs.chart, '#262B40');
            this.init();
        });
    },
    methods: {
        async init(times) {
            if (times && times.length) {
                const list = await this.getMarketAllLinkPerspectiveDelay(times);
                if (list && list.length) {
                    list.forEach((element) => {
                        if (element.dimensionName === 'LFL') {
                            this.option.series[0].data = [];
                            element.trendChart.yaxis.forEach(ele => {
                                this.option.series[0].data.push(ele / 1000);
                            });
                        } else {
                            this.option.series[1].data = [];
                            element.trendChart.yaxis.forEach(ele => {
                                this.option.series[1].data.push(ele / 1000);
                            });
                        }
                    });
                    this.option.xAxis.data = list[0].trendChart.xaxis;
                }
            } else {
                this.option.series.forEach((ele) => {
                    ele.data = [];
                });
                this.option.xAxis.data = [];
            }
            this.option.title.text = `${this.compareSpanFlag}行情解码时延(μs)`;
            this.myChart && this.myChart.clear();
            this.myChart.setOption(this.option);

            window.addEventListener('resize', () => {
                this.myChart.resize();
            });
        },
        getSpanFlag(key) {
            if (key === 'VDE' || key === 'MDGW') {
                return this.spanFlags[this.modalInfo.data.exchangeId][key][this.modalInfo.data.spanFlag];
            } else {
                return this.spanFlags[this.modalInfo.data.exchangeId][key];
            }
        },
        getMarketAllLinkPerspectiveDelay(times) {
            const date = formatDate(this.modalInfo.data.date);
            const param = {
                exchangeId: this.modalInfo.data.exchangeId,
                bizType: this.modalInfo.data.bizType,
                spanFlag: this.getSpanFlag(this.compareSpanFlag)['Local'],
                span: 'LFL',
                compareSpan: this.compareSpan,
                compareSpanFlag: this.getSpanFlag(this.compareSpanFlag)['App'],
                indicators: [this.modalInfo.data.indicators],
                interval: 1000,
                startTime: `${date} ${times[0]}`,
                endTime: `${date} ${times[1]}`
            };
            return new Promise((resolve, reject) => {
                getMarketAllLinkPerspectiveDelay(param).then(res => {
                    if (res.success) {
                        resolve(res.data);
                    } else {
                        resolve(false);
                    }
                });
            });
        }
    },
    render() {
        return <div ref="chart" class="chart"></div>;
    }
};
