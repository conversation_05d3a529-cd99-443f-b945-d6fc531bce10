.sql-editor-wrapper {
    padding: 10px 5px;
    transition: height 0.2s;
    will-change: transform;
    /* stylelint-disable-next-line selector-class-pattern */
    .CodeMirror {
        transition: height 0.2s;
        background: #3f495d5c !important;
        color: #cacfd4;
    }

    .cm-string {
        color: #e46dff !important;
    }

    .cm-keyword {
        color: #20c16f !important;
    }

    .cm-s-xq-light span.cm-variable-2 {
        color: #e89696 !important;
    }

    .cm-number {
        color: #a7fff5 !important;
    }

    /* stylelint-disable-next-line selector-class-pattern */
    .CodeMirror-cursor {
        border-left: 1px solid #d8d8d8 !important;
    }

    /* stylelint-disable-next-line selector-class-pattern */
    .CodeMirror-selected {
        background: #7777775c !important;
    }

    /* stylelint-disable-next-line selector-class-pattern */
    .CodeMirror-gutters {
        border-right: 1px solid var(--base-color) !important;
        background-color: var(--box-color) !important;
    }

    /* stylelint-disable-next-line selector-class-pattern */
    .CodeMirror-placeholder {
        // color: rgb(84, 84, 84) !important;
        // font-size: 14px !important;
        padding-left: 10px;
    }
}

