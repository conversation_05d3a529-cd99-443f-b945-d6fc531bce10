<template>
    <div class="main">
        <a-title title="">
            <slot>
                <h-select
                    v-model="productInstNo"
                    placeholder="请选择"
                    class="title-select"
                    widthAdaption
                    :positionFixed="true"
                    :clearable="false"
                    @on-change="checkProduct">
                    <h-option
                        v-for="item in productList"
                        :key="item.id"
                        :value="item.productInstNo">
                        {{ item.productName }}</h-option>
                </h-select>
            </slot>
        </a-title>
        <a-loading v-if="loading"></a-loading>
        <div v-show="!loading && menuList.length" class="container">
            <menu-layout customMenu @menu-fold="menuFold">
                <template v-slot:menu>
                    <div class="menu">
                        <div class="header-menu" :style="{ padding: menuFoldStatus ? '0' : '0px 10px' }">已托管应用节点</div>
                        <h-menu theme="dark" :active-name="selectMenu" :open-names="openName"
                            style="height: calc(100% - 44px);" @on-select="selectMenuChange">
                            <submenu v-for="item in menuList" :key="item.groupName" :name="item.groupName">
                                <template v-slot:title>{{ item.groupName }}
                                </template>
                                <h-menu-item v-for="child in item.menuItems" :key="child.id" :name="child.id">
                                    <span style="display: inline-block; width: 25px;">
                                        {{ child.instanceName }}</span>
                                </h-menu-item>
                            </submenu>
                        </h-menu>
                    </div>
                </template>
                <template v-slot:right>
                    <h-tabs v-if="selectMenu" v-model="tabName" @on-click="tabClick(tabName)">
                        <h-tab-pane v-for="item in tabList" :key="item.name" :label="item.describe" :name="item.name">
                            <component :is="item.name" :ref="item.name" :timerInterval="item.timerInterval" :nodeData="menu" @clear="clearPolling"></component>
                        </h-tab-pane>
                    </h-tabs>
                    <no-data v-else></no-data>
                </template>
            </menu-layout>
        </div>
        <div v-if="loading || menuList.length === 0" style="height: calc(100% - 110px);">
            <no-data></no-data>
        </div>
    </div>
</template>

<script>
import _ from 'lodash';
import { formatDate } from '@/utils/utils';
import { mapState, mapActions } from 'vuex';
import { getDashboardConfig } from '@/api/mcApi';
import { getProductInstances } from '@/api/productApi';
import aLoading from '@/components/common/loading/aLoading';
import noData from '@/components/common/noData/noData';
import menuLayout from '@/components/common/menuLayout/menuLayout';
import generalView from '@/components/mcDataObservation/monitorRule.vue';
import processor from '@/components/mdbDataObservation/processor.vue';
import memory from '@/components/mdbDataObservation/memory.vue';
import serial from '@/components/mdbDataObservation/serial.vue';
import performance from '@/components/mdbDataObservation/performance.vue';
import slowTransaction from '@/components/mdbDataObservation/slowTransaction';
export default {
    name: 'MdbObservation',
    components: {
        noData,
        menuLayout,
        aLoading,
        generalView,
        processor,
        memory,
        serial,
        performance,
        slowTransaction
    },
    data() {
        return {
            loading: false,
            timer: null,
            currentTime: new Date(),
            productInstNo: '',
            productInfo: {},
            productType: '',
            tabName: 'generalView',
            openName: [],   // menu默认展开
            selectMenu: '', // menu默认选择
            menu: {}, // 选中的menu信息
            menuFoldStatus: false,
            menuList: [],
            tabList: []
        };
    },
    mounted() {
        this.init();
    },
    computed: {
        ...mapState({
            productList: (state) => {
                return state.product.productListLight || [];
            }
        }),
        appTypeDictDesc() {
            return this.$store?.state?.apmDirDesc?.appTypeDictDesc || {};
        },
        // 节点分类
        typeDict() {
            const apmDirDesc = this.$store?.state?.apmDirDesc;
            const data = {
                frontTypeDict: apmDirDesc?.frontTypeDict || {},
                coreTypeDict: apmDirDesc?.coreTypeDict || {},
                offerTypeDict: apmDirDesc?.offerTypeDict || {},
                offerbusTypeDict: apmDirDesc?.offerbusTypeDict || {},
                todbTypeDict: apmDirDesc?.todbTypeDict || {}
            };
            return data;
        }
    },
    methods: {
        ...mapActions({ getProductList: 'product/getProductListLight' }),
        // 菜单展开收起状态
        menuFold(status) {
            this.menuFoldStatus = status;
        },
        // 初始化页面
        async init() {
            try {
                this.loading = true;
                await this.getProductList({
                    filter: 'supportMemoryMdb'
                });
                const productInstNo = localStorage.getItem('productInstNo');
                this.productInstNo =  _.find(this.productList, ['productInstNo', productInstNo])?.productInstNo || this.productList?.[0]?.productInstNo;
                this.currentTime = formatDate(new Date());
                await this.getDashboardConfig();
            } catch (e) {
                console.error(e);
            } finally {
                this.loading = false;
            }
        },
        // 获取dashboard参数
        async getDashboardConfig() {
            const res = await getDashboardConfig({
                type: 'mdb',
                productId: this.productInstNo
            });
            if (res.code === '200'){
                this.tabList = res?.data?.filter(v => v?.visible === true) || [];
            }
        },
        // 切换产品
        async checkProduct(val) {
            this.clearData();
            this.loading = true;
            if (this.productList.length) {
                this.productInfo = val ? _.find(this.productList, ['productInstNo', val]) : this.productList[0];
                localStorage.setItem('productInstNo', this.productInfo.productInstNo);
                await this.getProductInstances();
            }
            this.loading = false;
            setTimeout(() => {
                this.getMenuList(val);
            });
        },
        // 手动清空数据
        clearData(){
            this.menu = {};
            this.selectMenu = '';
            this.menuList = [];
            this.productInfo = {};
            this.productType = '';
            this.tabName = '';
        },
        // 判断分组类型
        hasType(key){
            return Object.keys(this.typeDict?.[key] || {}).includes(this.productType);
        },
        // 获取应用节点列表
        async getProductInstances() {
            const res = await getProductInstances({ productId: this.productInfo.id });
            if (res.code === '200') {
                const instances = res?.data?.instances || [];
                this.productInfo.instances = _.filter(instances, o => { return o.supportMemoryMdb; });
            }
        },
        // 获取 menuList
        getMenuList(){
            const menuList = {};
            const instances = this.productInfo.instances;
            instances.forEach((v) => {
                menuList[v.instanceType] = menuList[v.instanceType] || {};
                menuList[v.instanceType].type = v.instanceType;
                menuList[v.instanceType].groupName = this.appTypeDictDesc[v.instanceType] || v.instanceType;
                menuList[v.instanceType].menuItems = menuList[v.instanceType].menuItems || [];
                menuList[v.instanceType].menuItems.push({ ...v });
            });
            this.menuList = Object.values(menuList);
            const id = this.menuList?.[0]?.menuItems?.[0]?.id;
            this.$nextTick(() => {
                this.openName = [this.menuList?.[0]?.groupName];
                this.selectMenu = id;
                id && this.selectMenuChange(id);
            });
        },
        // 切换列表
        async selectMenuChange(id) {
            this.menu = _.find(this.productInfo.instances, ['id', id]);
            if (!this.menu?.manageProxyIp || !this.menu?.manageProxyPort) {
                this.$hMessage.error('当前节点不支持观测！请重新选择节点');
                this.menu = {};
                this.editableTabs = [];
                return;
            }
            this.selectMenu = id;
            const tab = _.find(this.tabList, ['name', this.tabName]);
            this.tabName = tab ? this.tabName : this.tabList?.[0]?.name || 'generalView';
            await this.tabClick(this.tabName);
        },
        // 切换tab
        async tabClick(name) {
            this.clearPolling();
            const currentTab = _.find(this.tabList, ['name', name]);
            name !== 'performance' && currentTab?.timerSwitch && this.setPolling(name, currentTab?.timerInterval || 5);
            this.$nextTick(async () => {
                this.$refs?.[name]?.[0] && await this.$refs[name][0].initData();
            });
        },
        // 设置轮询
        setPolling(name, timerInterval) {
            this.pollTime = timerInterval;
            this.timer = setInterval(() => {
                this.$refs?.[name]?.[0] && this.$refs[name][0].getFileData();
            }, timerInterval * 1000);
        },
        // 清理定时器
        clearPolling() {
            this.timer && clearInterval(this.timer);
        }
    },
    beforeDestroy() {
        this.clearPolling();
    }
};
</script>

<style lang="less" scoped>
@import url("@/assets/css/input.less");
@import url("@/assets/css/tab.less");
@import url("@/assets/css/menu.less");

.main {
    .container {
        width: 100%;
        height: calc(100% - 60px);
        border-radius: var(--border-radius);
        background: none;
        margin-top: 10px;

        /deep/ .h-tabs-nav-wrap {
            float: none !important;
        }

        /deep/ .h-tabs-nav-right {
            position: absolute;
            right: 0;
            top: 5px;
        }

        & > .apm-box {
            height: 100%;
            margin-top: 0;
            background: none;
        }

        /deep/ .right-box {
            background: none;
            padding: 0 10px;
        }

        /deep/ .h-tabs-bar {
            margin-bottom: 5px;
        }

        /deep/ .h-tabs-content-wrap {
            height: calc(100% - 50px);
        }
    }
}
</style>
