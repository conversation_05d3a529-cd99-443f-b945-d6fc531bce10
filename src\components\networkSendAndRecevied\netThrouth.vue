<template>
  <div ref="result-box" class="net-send">
    <menu-layout ref="menu" customMenu @menu-fold="menuFold">
      <template v-slot:menu>
        <div class="menu">
          <div
            class="header-menu"
            :style="{ padding: menuFoldStatus ? '0' : '0px 10px' }"
          >
            已托管应用节点
          </div>
          <h-menu
            v-show="instanceList.length"
            theme="dark"
            :activeName="selectInstanceId"
            @on-select="selectInstanceChange"
          >
            <h-menu-item
              v-for="item in instanceList"
              :key="item.id"
              :name="item.id"
            >
              <span>{{ item.instanceName }}</span>
            </h-menu-item>
          </h-menu>
          <p v-show="!menuFoldStatus && !instanceList.length" style="padding: 10px; text-align: center;">无托管应用节点</p>
        </div>
      </template>
      <template v-slot:right>
        <h-form
          ref="formItems"
          :model="formItems"
          :label-width="70"
          :cols="3"
          style="background: var(--wrapper-color);"
          @submit.native.prevent
        >
          <h-form-item label="日志列表" prop="log" required>
            <h-select v-model="formItems.log" :clearable="false" filterable>
              <h-option v-for="item in logList" :key="item" :value="item"
                >{{ item }}
              </h-option>
            </h-select>
          </h-form-item>
          <h-form-item label="功能号" prop="funcNos" :validRules="regRule">
            <h-input
              v-model.trim="formItems.funcNos"
              placeholder="支持输入多个，采用英文分号区分"
              :maxlength="50"
            ></h-input>
          </h-form-item>
          <h-form-item class="form-no-label">
            <a-button
              type="primary"
              @click="getPacketCaptureLogDetail"
              >获取数据</a-button
            >
          </h-form-item>
        </h-form>
        <div class="result-box">
          <h-split v-model="splitVal" min="400px" max="400px">
            <div slot="left" class="demo-split-pane">
              <div class="table-box">
                <obs-table
                  ref="msgTableData1"
                  highlightRow
                  :height="tableHeight"
                  :loading="tableLoading"
                  border
                  :title="msgTitle"
                  :columns="columns"
                  :tableData="msgTableData"
                  :hasPage="false"
                  showTitle
                  rowSelectOnly
                  @on-current-change="tableRowcheckedChange"
                  @button-click="handleButtonClick"
                />
                <br />
                <div class="page-box">
                  <span>共 {{ msgTotal }} 条</span>&nbsp;&nbsp;
                  <a-button type="dark" :disabled="!msgTableData.length" :loading="buttonLoading"  @click="handlePrev"
                    >上一页</a-button
                  >&nbsp;&nbsp;
                  <a-button type="dark" :disabled="!msgTableData.length" :loading="buttonLoading"  @click="handleNext"
                    >下一页</a-button
                  >&nbsp;&nbsp;,
                  <span>
                    跳转序号
                    <h-input
                      ref="jumpInput"
                      v-model="jumpIndex"
                      style="width: 70px;"
                      type="int"
                      specialFilter
                      :specialLength="10"
                      :specialDecimal="0"
                      :maxlength="20"
                      @on-blur="handlePageChange"
                      @on-enter="handlePageChange"
                    ></h-input>
                  </span>
                </div>
              </div>
            </div>
            <div slot="right" class="demo-split-pane">
              <div class="json-box">
                <obs-title :title="jsonTitle" />
                <json-viewer
                 v-if="!isShowTextFunc(jsonData)"
                  :value="jsonData"
                  :expand-depth="10"
                  copyable
                  :expanded="true"
                  @copied="onCopy"
                >
                </json-viewer>
                <monaco-code-editor
                    v-if="isShowTextFunc(jsonData)"
                    ref="monaco-code-editor"
                    :options="options"
                    :value="JSON.stringify(jsonData || {}, null, 4)"
                    language="json"
                />
              </div>
            </div>
          </h-split>
        </div>
      </template>
    </menu-layout>
    <draggable-table-configModal
      v-if="draggableTableConfigInfo.status"
      :modalData="draggableTableConfigInfo"
      @set-config="setNewConfigList"
    />
  </div>
</template>

<script>
import _ from 'lodash';
import obsTitle from '@/components/common/title/obsTitle';
import obsTable from '@/components/common/obsTable/obsTable';
import aButton from '@/components/common/button/aButton';
import menuLayout from '@/components/common/menuLayout/menuLayout';
import jsonViewer from 'vue-json-viewer';
import MonacoCodeEditor from '@/components/locateConfig/MonacoCodeEditor.vue';
import draggableTableConfigModal from '@/components/networkSendAndRecevied/modal/draggableTableConfigModal';
import { getProductInstances } from '@/api/productApi';
import { getPacketCaptureLog, getPacketCaptureLogDetail, setPacketTransferData } from '@/api/networkApi';
import { transferVal, getByteSize } from '@/utils/utils';
export default {
    props: {
        productInstNo: {
            type: String,
            default: ''
        }
    },
    components: {
        menuLayout,
        aButton,
        obsTable,
        obsTitle,
        jsonViewer,
        MonacoCodeEditor,
        draggableTableConfigModal
    },
    data() {
        return {
            buttonLoading: false,
            tableLoading: false,
            splitVal: 0.6,
            menuFoldStatus: false,
            selectInstanceId: '',
            instanceList: [],
            logList: [],
            formItems: {
                log: '',
                funcNos: ''
            },
            regRule: [
                { test: /^(\d+[;])*(\d+)$/, message: '请输入功能号以英文分号形式间隔', trigger: 'blur' }
            ],
            jsonTitle: {
                label: '消息体'
            },
            jsonData: '',
            msgTitle: {
                label: '消息列表',
                slots: [
                    {
                        type: 'button',
                        key: 'config',
                        iconSize: '12px',
                        iconName: 't-b-setting'
                    }
                ]
            },
            msgTotal: 0,
            count: 10,
            jumpIndex: undefined,
            startIdx: 1,
            endIndex: 1,
            AllColumns: [
                {
                    title: '功能号',
                    key: 'FunctionID',
                    ellipsis: true,
                    isChecked: true
                },
                {
                    title: '系统号',
                    key: 'SystemNo',
                    ellipsis: true,
                    isChecked: false
                },
                {
                    title: 'Token',
                    key: 'Token',
                    ellipsis: true,
                    isChecked: false
                },
                {
                    title: '用户自定义',
                    key: 'UserDefined',
                    ellipsis: true,
                    isChecked: false
                },
                {
                    title: '抓包时间',
                    key: 'Time',
                    minWidth: 200,
                    ellipsis: true,
                    isChecked: true,
                    sortable: false
                },
                {
                    title: '包类型',
                    key: 'MsgType',
                    ellipsis: true,
                    isChecked: true
                },
                {
                    title: '附加信息',
                    key: 'Remark',
                    minWidth: 200,
                    ellipsis: true,
                    isChecked: true
                }
            ],
            columns: [
                {
                    title: '序号',
                    key: 'Index'
                }
            ],
            msgTableData: [],
            tableHeight: 0,
            draggableTableConfigInfo: {
                status: false
            },
            options: {
                readOnly: true
            }
        };
    },
    mounted() {
        this.fetTableHeight();
        window.addEventListener('resize', this.fetTableHeight);
    },
    beforeDestroy() {
        window.removeEventListener('resize', this.fetTableHeight);
    },
    methods: {
        onCopy() {
            this.$hMessage.success('Copied！');
        },
        menuFold(status) {
            this.menuFoldStatus = status;
        },
        // 设置table高度
        fetTableHeight() {
            this.tableHeight = this.$refs['result-box'] ? this.$refs['result-box'].offsetHeight - 190 : 0;
        },
        // 清空数据
        clearData() {
            this.logList = [];
            this.formItems = {
                log: '',
                funcNos: ''
            };
            this.msgTableData = [];
            this.jsonData = '';
            this.msgTotal = 0;
            this.jumpIndex = undefined;
            this.startIdx = 1;
            this.endIndex = 1;
            this.$refs['formItems'] && this.$refs['formItems'].resetFields();
        },
        clearInstanceData() {
            this.instanceList = [];
            this.selectInstanceId = '';
        },
        // 初始化
        async initData() {
            this.clearInstanceData();
            this.clearData();

            // 合并默认配置和本地存储的配置
            this.AllColumns = this.AllColumns.map((defaultCol) => {
                const savedCol = (JSON.parse(localStorage.getItem('apm-msg-list-config')) || []).find(col => col.key === defaultCol.key) || {};
                return { ...defaultCol, ...savedCol, sortable: defaultCol.sortable };
            });

            // 筛选选中的列
            this.columns = [
                {
                    title: '序号',
                    key: 'Index'
                },
                ...this.AllColumns.filter(v => v.isChecked)
            ];

            await this.getProductInstances();
            this.fetTableHeight();
        },

        // ******************************* 菜单功能 *********************************************
        async selectInstanceChange(id) {
            this.clearData();
            this.selectInstanceId = id;
            sessionStorage.setItem('apm.netSendInstanceId', id);
            if (this.selectInstanceId) {
                await this.getLogList();
            }
        },
        // 获取应用节点列表
        async getProductInstances() {
            const res = await getProductInstances({ productId: this.productInstNo });
            if (res.code === '200') {
                this.instanceList = (res?.data?.instances || []).filter(o => {
                    return o?.supportPacketCapture !== undefined
                        ? o.supportPacketCapture === true
                        : o?.instanceType !== 'zk' && o?.instanceType !== 'ldpadmin';
                });
                this.$nextTick(async () => {
                    if (this.instanceList?.length) {
                        const netSendInstanceId = sessionStorage.getItem('apm.netSendInstanceId') || '';
                        const id = _.find(this.instanceList, ['id', netSendInstanceId])?.id || this.instanceList?.[0]?.id || '';
                        await this.selectInstanceChange(id);
                    }
                });
            }
        },

        // ******************************* 日志表单 *********************************************
        // 获取日志列表
        async getLogList() {
            const param = {
                instanceId: this.selectInstanceId
            };
            const res = await getPacketCaptureLog(param);
            if (this.selectInstanceId !== param?.instanceId) return;
            if (res?.code === '200') {
                this.logList = res?.data || [];
                this.formItems.log = this.logList?.[0] || '';
                if (this.formItems.log) {
                    this.getPacketCaptureLogDetail();
                }
            }
        },
        // 获取数据
        getPacketCaptureLogDetail(start) {
            if (!transferVal(start)){
                this.jumpIndex = undefined;
                this.startIdx = 1;
                this.endIndex = 1;
            }
            this.$refs['formItems'].validate(async (valid) => {
                if (valid) {
                    this.tableLoading = true;
                    const param = {
                        instanceId: this.selectInstanceId,
                        logFileName: this.formItems.log,
                        filterType: 'functionNo',
                        filterValue: this.formItems.funcNos,
                        start: !transferVal(start) ? 1 : Number(start),
                        count: this.count
                    };
                    try {
                        const res = await getPacketCaptureLogDetail(param);
                        if (this.selectInstanceId !== param?.instanceId) return;
                        if (res?.code === '200') {
                            this.msgTableData = res?.data?.list || [];
                            this.msgTotal = res?.data?.totalCount || 0;
                            if (!transferVal(start)){
                                this.startIdx = this.msgTableData?.[0]?.Index || 1;
                                this.endIndex = this.startIdx + this.msgTotal - 1;
                            }
                            this.jsonData = '';
                        } else {
                            this.msgTableData = [];
                            this.msgTotal = 0;
                            this.jsonData = '';
                        }
                    } catch (err) {
                        this.msgTableData = [];
                        this.msgTotal = 0;
                        this.jsonData = '';
                    } finally {
                        this.tableLoading = false;
                        this.buttonLoading = false;
                    }
                }
            });
        },
        // ******************************* 表格功能 *********************************************
        // 消息列表-行选中
        tableRowcheckedChange(row) {
            this.setPacketTransferData(row?.Data || '');
        },
        // 抓包数据解析
        async setPacketTransferData(msg) {
            const param = {
                instanceId: this.selectInstanceId,
                ldpMsgBase64: msg
            };
            const res = await setPacketTransferData(param);
            if (this.selectInstanceId !== param.instanceId || msg !== param.ldpMsgBase64) return;
            if (res?.code === '200') {
                this.jsonData = this.formatJsonData(res?.data);
            } else {
                this.jsonData = msg;
            }
        },
        // 消息列表
        handleButtonClick(key) {
            if (key === 'config') {
                this.draggableTableConfigInfo = {
                    status: true,
                    configList: _.cloneDeep([...this.AllColumns]),
                    defaultCheckedList: _.cloneDeep(
            [...this.AllColumns]
                ?.filter((v) => v?.isChecked)
                ?.map((o) => o?.key)
                    )
                };
            }
        },
        // 配置列表显示字段-排序、筛选表头
        setNewConfigList(configList) {
            const columnsList = configList.filter((v) => v.isChecked);
            this.AllColumns = [...configList]; // 拖拽结束后的列表顺序
            localStorage.setItem('apm-msg-list-config', JSON.stringify(configList));
            this.columns = [
                // 表格显示
                {
                    title: '序号',
                    key: 'Index'
                },
                ...columnsList
            ];
        },
        // 上一个
        handlePrev() {
            this.buttonLoading = true;
            const firstMsgIndex = this.msgTableData?.[0]?.Index;
            if (firstMsgIndex && firstMsgIndex > this.startIdx) {
                if (firstMsgIndex - this.count <= 0){
                    this.getPacketCaptureLogDetail(1);
                }  else {
                    this.getPacketCaptureLogDetail(firstMsgIndex - this.count);
                }
            } else {
                this.$hMessage.warning('当前页为首页无法支持翻页，请重新获取数据或用序号跳转至您想查询的位置');
            }
            this.buttonLoading = false;
        },
        // 下一个
        handleNext() {
            this.buttonLoading = true;
            const lastMsgIndex = this.msgTableData?.slice(-1)?.[0]?.Index;
            if (lastMsgIndex && lastMsgIndex + 1 <= this.endIndex) {
                this.getPacketCaptureLogDetail(lastMsgIndex + 1);
            } else {
                this.$hMessage.warning('当前页为最后一页无法支持翻页，请重新获取数据或用序号跳转至您想查询的位置');
            }
            this.buttonLoading = false;
        },
        handlePageChange() {
            if (this.jumpIndex?.toString() === '0'){
                this.jumpIndex = 1;
            }
            this.getPacketCaptureLogDetail(this.jumpIndex);
            this.$refs.jumpInput.blur();
        },
        // 计算json大小，判断文本展示类型
        isShowTextFunc(data) {
            const jsonStr = JSON.stringify(data);
            if (getByteSize(jsonStr) >  600 * 1024) {
                return true;
            }
            return false;
        },
        // json转换防止报错
        formatJsonData(str) {
            if (str) {
                try {
                    const data = JSON.parse(str);
                    return data;
                } catch (error) {
                    return str;
                }
            }
            return '';
        }
    }
};
</script>

<style lang="less" scoped>
@import url("@/assets/css/menu.less");
@import url("@/assets/css/input.less");
@import url("@/assets/css/json-view.less");
@import url("@/assets/css/poptip-1.less");

/deep/.h-menu-dark.h-menu-vertical .h-menu-item:hover,
.h-menu-dark.h-menu-vertical .h-menu-submenu-title:hover {
    background: var(--link-opacity-color) !important;

    /deep/ &::after {
        position: absolute;
        top: 0;
        left: 0;
        content: "";
        width: 4px;
        height: 33px;
        background: var(--link-color);
    }
}

.net-send {
    height: 100%;

    .h-menu {
        height: calc(100% - 60px);
    }

    /deep/ .h-form-row .h-form-item {
        padding: 7.5px 10px;
    }

    .form-no-label {
        /deep/.h-form-item-content {
            margin-left: 0 !important;
        }
    }

    /deep/ .h-input-group-append,
    /deep/ .h-input-group-prepend {
        color: var(--font-color);
        background-color: var(--base-color);
        border: var(--border);
    }

    .icon-setting {
        cursor: pointer;

        &:hover {
            color: var(--link-color);
        }
    }

    .result-box {
        height: calc(100% - 65px);
        margin-top: 15px;

        /deep/ .obs-title .title-box .h-icon:hover {
            text-decoration: none;
        }

        .demo-split-pane {
            height: 100%;
        }

        .table-box {
            .obs-table {
                height: auto;
                margin-top: 0;

                /deep/.h-btn {
                    padding: 6px 10px;
                }

                /deep/.h-table-sort {
                    display: none;
                }
            }

            .page-box {
                margin-right: 10px;
                color: var(--font-color);
                float: right;
            }
        }

        .json-box {
            height: 100%;
            background-color: var(--wrapper-color);
            border-radius: var(--border-radius);

            /deep/ .jv-code {
                padding: 20px 10px;
            }
        }

        .monaco-code-editor {
            margin: 5px;
            height: calc(100% - 48px);
            width: calc(100% - 7px);
        }
    }

    /deep/ .h-split-trigger {
        border: none;
    }

    /deep/ .h-split-trigger-vertical {
        width: 4px;
        background: #1b2130;
    }

    /deep/.h-split-trigger-vertical .h-split-trigger-bar {
        background: #485565;
    }
}
</style>
