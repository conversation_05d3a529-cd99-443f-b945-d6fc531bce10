<template>
    <div class="container">
        <!-- 标题部分 -->
        <obs-title
            :title="pageTitle"
            class="obs-title"
            @button-click="handleButtonClick">
        </obs-title>

        <a-loading v-if="loading"></a-loading>
        <div class="main-content">
            <!-- 侧边菜单栏 -->
            <div class="sidebar">
                <h-input
                    v-model.trim="filterText"
                    placeholder="搜索管理功能"
                    icon="search"
                    class="menu-search">
                </h-input>
                <!-- 菜单 -->
                <h-menu
                    v-if="filterMenuList.length"
                    :active-name="activeMenu"
                    :open-names="openMenu"
                    theme="dark"
                    class="menu"
                    @on-select="changeActiveMenu">
                    <submenu
                        v-for='item in filterMenuList'
                        :key="item.pluginName"
                        :name="item.pluginName">
                        <!-- 菜单项标题 -->
                        <template v-slot:title>
                            {{ item.pluginName }}
                        </template>
                        <!-- 子菜单项 -->
                        <h-menu-item v-for='child in item.funcs'
                            :key="`${item.pluginName}-${child.method}-${child.funcName}`"
                            :class="isOnlyEn ? 'h-menu-cn' : ''"
                            :name="`${item.pluginName}-${child.method}-${child.funcName}`">
                            <!-- 非纯英文显示 -->
                            <div v-show="!isOnlyEn" class="text-method ellipsis" :title="`${child.method} ${child.funcName}`">
                                <span class="text-method-left">{{ child.method === 'GET' ? 'G' : 'P'  }}</span>
                                <span class="text-method-right"> {{ child.funcName }}</span>
                            </div>
                            <!-- 纯英文显示 -->
                            <div v-show="isOnlyEn" class="text-method ellipsis" :title="`${child.method} ${child.funcName} \n${child.funcNameCn || '暂无'}`" >
                                <span class="text-method-left text-method-left-chi">{{ child.method === 'GET' ? 'G' : 'P'  }}</span>
                                <span class="text-method-right"> {{ child.funcName }}</span>
                                <div class="ellipsis" style="color: #9296a1; margin-left: 30px;">{{  child.funcNameCn || '暂无'}}</div>
                            </div>
                        </h-menu-item>
                    </submenu>
                </h-menu>
                <p v-show="!filterMenuList.length" class="no-results">
                    未查询到管理功能
                </p>
                <h-checkbox
                    v-model="isOnlyEn"
                    class="menu-footer"
                    :label="true">
                    <span style="color: #fff;">显示"中文译名"</span>
                </h-checkbox>
            </div>

            <!-- 内容部分 -->
            <div class="content">
                <div v-for="item in infoItems" :key="item.label" class="info-item">
                    <div class="label">{{ item.label }}</div>
                    <div :class="['value', 'multiline']">{{ item.value }}</div>
                </div>
                <div class="info-item">
                    <div class="label table-label">入参说明：</div>
                    <div class="value">
                        <tree-table
                            ref="treeTable"
                            showTitle
                            :columns="requestColumns"
                            :treeData="requestTableData"
                            :height="300">
                        </tree-table>
                    </div>
                </div>
                <div class="info-item">
                    <div class="label">入参示例：</div>
                    <div class="json-box">
                        <json-viewer
                            :value="requestJsonData"
                            :expand-depth="10"
                            copyable
                            :expanded="true"
                            @copied="onCopy">
                        </json-viewer>
                    </div>
                </div>
                <div class="info-item">
                    <div class="label table-label">出参说明：</div>
                    <div class="value">
                        <tree-table
                            ref="treeTable"
                            showTitle
                            :columns="responseColumns"
                            :treeData="responseTableData">
                        </tree-table>
                    </div>
                </div>
                <div class="info-item">
                    <div class="label">出参示例：</div>
                    <div class="json-box">
                        <json-viewer
                            :value="responseJsonData"
                            :expand-depth="10"
                            copyable
                            :expanded="true"
                            @copied="onCopy">
                        </json-viewer>
                    </div>
                </div>
                <a-loading v-if="mateLoading"></a-loading>
            </div>
        </div>

        <!-- 导入弹窗 -->
        <import-manage-file-modal
            v-if="modalInfo.status"
            :productId="productInfo.id"
            :modalInfo="modalInfo"
            @update="initData"
        ></import-manage-file-modal>
    </div>
</template>
<script>
import obsTitle from '@/components/common/title/obsTitle';
import treeTable from '@/components/common/treeTable/treeTable';
import aLoading from '@/components/common/loading/aLoading';
import jsonViewer from 'vue-json-viewer';
import importManageFileModal from '@/components/productServiceConfig/modal/importManageFileModal.vue';
import { getLdpManageApis, getManageMeta } from '@/api/productApi';
import { transformSchema } from '@/components/managementQuery/schema-parser';

// import { getLdpManageApis, getManageMeta } from './mock.js';
export default {
    name: 'ManageFunctionMeta',
    components: { obsTitle, treeTable, aLoading, jsonViewer, importManageFileModal },
    props: {
        productInfo: {
            type: Object,
            default: () => {}
        }
    },
    data() {
        return {
            // 页面标题
            pageTitle: {
                label: '管理功能使用说明',
                slots: [
                    {
                        type: 'button',
                        key: 'import',
                        buttonType: 'dark',
                        value: '导入'
                    }
                ]
            },

            filterText: '', // 搜索输入框内容
            openMenu: [], // 默认展开
            activeMenu: 'GetAppJsonInfo', // 当前激活的菜单项
            // 菜单项列表
            menuItems: [],
            isOnlyEn: false, // 控制是否只显示英文

            loading: false,
            mateLoading: false,
            // 元信息属性
            infoItems: [
                { label: '功能名称：', value: '-' },
                { label: '功能备注：', value: '-' },
                { label: '版本号：', value: '-' },
                { label: '更新日期：', value: '-' },
                { label: '提供者：', value: '-' },
                { label: '功能说明：', value: '-' }
            ],
            // 入参说明
            requestColumns: [
                {
                    title: '入参名称',
                    key: 'paramName',
                    width: 240
                },
                {
                    title: '参数类型',
                    key: 'paramType',
                    width: 140
                },
                {
                    title: '参数说明',
                    key: 'paramDesc'
                }
            ],
            requestTableData: [],
            requestJsonData: {},
            // 出参说明
            responseColumns: [
                {
                    title: '出参名称',
                    key: 'paramName',
                    width: 240
                },
                {
                    title: '参数类型',
                    key: 'paramType',
                    width: 140
                },
                {
                    title: '参数说明',
                    key: 'paramDesc'
                }
            ],
            responseTableData: [],
            responseJsonData: {},

            // 弹窗
            modalInfo: {
                status: false
            }
        };
    },
    computed: {
        filterMenuList() {
            return (this.menuItems || []).map(plugin => {
                const list = plugin.funcs || [];
                list.sort((a, b) => {
                    if (a?.request?.method === 'GET' && b?.request?.method === 'POST') {
                        return -1;
                    } else if (a?.request?.method === 'POST' && b?.request?.method === 'GET') {
                        return 1;
                    }
                    return a.funcName.localeCompare(b.funcName);
                });
                const filteredFuns = list.filter(fun =>
                    String(fun.funcName).toLowerCase().includes(String(this.filterText).toLowerCase())
                );
                return { ...plugin, funcs: filteredFuns };
            }).filter(plugin => plugin.funcs.length > 0);
        }
    },
    watch: {
        filterText() {
            this.openDefaultMenu();
        }
    },
    methods: {
        async initData() {
            this.loading = true;
            try {
                await this.handleLdpManageList();
            } catch (err) {
                console.error(err);
            }
            this.loading = false;
        },
        /**
         * 功能描述: 切换激活的菜单项
         */
        changeActiveMenu(name) {
            this.activeMenu = name;
            this.handleMetaInfo();
        },
        /**
         * 根据搜索结果默认展开插件，展开全部or展开第一个取决于交互配置
         */
        openDefaultMenu() {
            let openMenu;
            const menuListLength = this.filterMenuList.length;
            if (this.filterText && menuListLength > 0) {
                // 若有搜索内容，则展开插件
                openMenu = this.filterMenuList.map(item => item.pluginName);
            } else if (this.activeMenu && menuListLength > 0) {
                // 没有搜索内容但有上次选中的插件，则恢复选中
                const plugin = this.filterMenuList.find(({ pluginName, funcs }) =>
                    funcs.length && funcs.some(func =>
                        `${pluginName}-${func.request?.method}-${func.name}` === this.activeMenu
                    )
                );
                if (plugin) openMenu = [plugin.pluginName];
            }
            this.openMenu = openMenu;
        },
        /**
         * 功能描述: 处理并展示管理功能菜单列表
         */
        async handleLdpManageList() {
            // 管理功能列表
            const manageList = await this.getLdpManageApis();
            // 菜单列表
            const menuList = [];
            manageList.forEach(item => {
                const pluginIndex = menuList.findIndex(o => o.pluginName === item.pluginName);
                if (pluginIndex !== -1) {
                    menuList[pluginIndex].funcs.push({
                        funcName: item.funcName,
                        funcNameCn: item.funcNameCn,
                        describe: item.describe,
                        method: item.method
                    });
                } else {
                    menuList.push({
                        pluginName: item.pluginName,
                        funcs: [
                            {
                                funcName: item.funcName,
                                funcNameCn: item.funcNameCn,
                                describe: item.describe,
                                method: item.method
                            }
                        ]
                    });
                }
            });

            this.menuItems = menuList;
            // 初始化菜单选择
            this.initializeMenuSelection(menuList);
        },
        /**
         * 菜单默认选中列表第一项
         * @param menuList 菜单列表
         */
        initializeMenuSelection(menuList) {
            // 确保 menuList 是一个数组，并且数组不为空
            if (Array.isArray(menuList) && menuList.length > 0) {
                const firstItem = menuList[0];
                const { pluginName, funcs } = firstItem;

                // 菜单默认选中列表第一项
                this.openMenu = [pluginName];

                if (funcs && funcs.length > 0) {
                    const { method, funcName } = funcs[0];
                    this.activeMenu = `${pluginName}-${method}-${funcName}`;
                } else {
                    // 如果 funcs 为空或不存在，处理该情况
                    this.activeMenu = '';
                }
            } else {
                // 处理 menuList 为空或不是数组的情况
                this.openMenu = [];
                this.activeMenu = '';
            }

            if (this.activeMenu) {
                this.changeActiveMenu(this.activeMenu);
            }
        },

        /**
         * 功能描述: 处理并展示接口获取的元信息数据
         */
        async handleMetaInfo() {
            this.mateLoading = true;
            const info = await this.getManageMeta();
            const {
                funcName, remark, version, updateDate, provider, description,
                example, schema
            } = info;

            // infoItems数据处理
            this.infoItems = [
                { label: '功能名称：', value: funcName || '-' },
                { label: '功能备注：', value: remark || '-' },
                { label: '版本号：', value: version || '-' },
                { label: '更新日期：', value: updateDate || '-' },
                { label: '提供者：', value: provider || '-' },
                { label: '功能说明：', value: description || '-' }
            ];

            // 入参示例
            this.requestJsonData = example?.request || '';
            // 出参示例
            this.responseJsonData = example?.response || '';

            // 入参说明表格数据
            this.requestTableData = transformSchema(schema?.request);
            // 出参说明表格数据
            this.responseTableData = transformSchema(schema?.response);
            this.mateLoading = false;
        },

        /**
         * 按钮操作处理
         */
        handleButtonClick(key) {
            if (key === 'import') {
                // 导入弹窗
                this.modalInfo.status = true;
            } else if (key === 'export') {
                // 导出现有的管理功能元信息
            }
        },
        // 清空原数据
        clearData() {
            // 清空数据
            this.infoItems = [
                { label: '功能名称：', value: '-' },
                { label: '功能备注：', value: '-' },
                { label: '版本号：', value: '-' },
                { label: '更新日期：', value: '-' },
                { label: '提供者：', value: '-' },
                { label: '功能说明：', value: '-' }
            ];
            this.requestTableData = [];
            this.responseTableData = [];
            this.requestJsonData = {};
            this.responseJsonData = {};
        },

        // 复制操作提示
        onCopy() {
            this.$hMessage.success('Copied！');
        },

        /**
         * 获取管理功能列表接口
         */
        async getLdpManageApis() {
            let data = [];
            try {
                const params = {
                    productId: this.productInfo.id
                };
                const res = await getLdpManageApis(params);
                if (res.code === '200') {
                    data = res.data || [];
                } else if (res.code.length === 8) {
                    this.$hMessage.error(res.message);
                }
            } catch (err) {
                console.error(err);
            }
            return data;
        },
        /**
         * 获取管理功能元信息接口
         */
        async getManageMeta() {
            let data = [];
            try {
                const activeMenuParts = this.activeMenu?.split('-');
                const params = {
                    productId: this.productInfo.id,
                    pluginName: activeMenuParts?.[0],  // 第一部分作为 pluginName
                    funcName: activeMenuParts?.[2]    // 第三部分作为 funcName
                };
                const res = await getManageMeta(params);
                if (res.code === '200') {
                    data = res.data || {};
                } else if (res.code.length === 8) {
                    this.$hMessage.error(res.message);
                    this.clearData();
                }
            } catch (err) {
                this.clearData();
                console.error(err);
            }
            return data;
        }
    }
};
</script>
<style lang="less" scoped>
@import url("@/assets/css/menu.less");
@import url("@/assets/css/json-view.less");

.container {
    display: flex;
    flex-direction: column;
    height: 100%;
    background: var(--wrapper-color);

    .obs-title {
        border-bottom: 1px solid #31364a;
    }
}

.main-content {
    display: flex;
    width: 100%;
    overflow: hidden;

    .sidebar {
        width: 240px;
        min-width: 240px;
        padding: 0 5px;
        border-right: 1px solid #31364a;

        .menu-search {
            padding: 10px 5px 0 0;
            margin-bottom: 10px;
        }

        .menu {
            height: calc(100% - 86px);

            .h-menu-cn.h-menu-item {
                height: 40px;
                line-height: 20px;
            }

            .h-menu-item {
                padding-left: 30px;
                cursor: pointer;

                .text-method {
                    position: relative;
                }

                .text-method-left {
                    position: absolute;
                    left: 0;
                    top: 5px;
                    width: 22px;
                    height: 22px;
                    line-height: 23px;
                    text-align: center;
                    display: inline-block;
                    background: #1f3759;
                    border-radius: 4px;
                    user-select: none;
                }

                .text-method-left-chi {
                    top: 9px;
                }

                .text-method-right {
                    margin-left: 30px;
                }
            }

            .h-menu-item:hover {
                .text-method-left {
                    background: #202637;
                }
            }

            .h-menu-item-selected {
                .text-method-left {
                    background: #202637;
                }
            }
        }

        .no-results {
            width: 100%;
            height: calc(100% - 86px);
            font-size: 12px;
            text-align: center;
            top: 100px;
            color: var(--font-body-color);
        }

        .menu-footer {
            width: 100%;
            border-top: 1px solid #31364a;
            padding: 10px;
        }
    }

    .content {
        width: 100%;
        padding: 10px 15px 0;
        overflow-y: auto;
        position: relative;

        .common-style {
            /* stylelint-disable-next-line font-family-no-missing-generic-family-keyword */
            font-family: PingFangSC-Regular;
            font-size: 12px;
            text-align: left;
            line-height: 28px;
        }

        .info-item {
            display: flex;
            width: 100%;
            margin-bottom: 10px;

            .label {
                min-width: 70px;
                color: var(--font-body-color);
                .common-style;
            }

            .table-label {
                line-height: 60px;
            }

            .value {
                width: 100%;
                color: var(--font-color);
                .common-style;
            }

            .multiline {
                white-space: pre-wrap;
                word-wrap: break-word;
            }
        }
    }
}

.a-table {
    /deep/ .h-table th {
        font-size: 12px;
    }
}

.json-box {
    height: 100%;
    max-height: 300px;
    background-color: var(--wrapper-color);
    border-radius: var(--border-radius);

    /deep/ .jv-container {
        margin: 0;
        max-height: 300px;
    }
}
</style>
