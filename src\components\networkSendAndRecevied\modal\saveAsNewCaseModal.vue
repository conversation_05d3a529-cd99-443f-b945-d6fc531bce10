<template>
  <div>
    <!-- 另存为新用例 -->
    <h-msg-box-safe
        v-model="modalData.status"
        width="400"
        title="另存为新用例"
        @on-open="getCollections">
        <h-form
            ref="forms"
            :model="formItem"
            :label-width="80">
            <h-form-item label="用户协议" prop="protocol" required>
                <h-select
                    v-model="formItem.protocol"
                    :clearable="false"
                    :positionFixed="true"
                    :setDefSelect="true"
                    >
                    <h-option
                        v-for="item in protocolList" :key="item.value" :value="item.value"
                        >{{ item.label }}
                    </h-option>
                </h-select>
            </h-form-item>
            <h-form-item label="用例名称" prop="caseName" required :validRules="stringRule">
                <h-input
                    v-model.trim="formItem.caseName"
                    placeholder="请输入用例名称">
                </h-input>
            </h-form-item>
        </h-form>
        <p slot="footer">
            <h-button @click="cancelMethod">取消</h-button>
            <h-button type="primary" @click="confirmSaveAs">确定</h-button>
        </p>
    </h-msg-box-safe>
  </div>
</template>
<script>
import { saveUseCaseInfo } from '@/api/networkApi';
import { stringLengthRule } from '@/utils/validate';
export default {
    name: 'SaveAsNewCaseModal',
    props: {
        modalInfo: {
            type: Object,
            default: function() {
                return {};
            }
        }
    },
    data() {
        return {
            modalData: this.modalInfo,
            stringRule: [
                { test: stringLengthRule(64), trigger: 'change, blur' }
            ],
            protocolList: [
                {
                    value: 'LDP_MSG',
                    label: 'LDPMSG'
                }
            ],
            formItem: {
                protocol: '',
                caseName: ''
            }
        };
    },
    methods: {
        getCollections() {
            this.$refs['forms'].resetFields();
            this.formItem.protocol = this.protocolList?.[0]?.value;
        },
        cancelMethod() {
            this.modalData.status = false;
        },
        // 保存用例信息
        async saveUseCaseInfo(params) {
            try {
                const res = await saveUseCaseInfo(params);
                if (res.code === '200') {
                    this.$hMessage.success('保存用例成功。');
                    this.$emit('update', res.data?.id);
                    this.modalData.status = false;
                } else if (res.code?.length === 8) {
                    this.$hMessage.error('保存用例失败。');
                }
            } catch (err) {
                console.error(err);
            }
        },
        // 确定另存
        confirmSaveAs() {
            this.$refs['forms'].validate((valid) => {
                if (valid) {
                    const params = {
                        ...this.modalData.params,
                        id: '',
                        protocol: this.formItem.protocol,
                        name: this.formItem.caseName
                    };
                    this.saveUseCaseInfo(params);
                }
            });
        }
    }
};
</script>
