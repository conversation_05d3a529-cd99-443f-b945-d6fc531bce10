/**
 * <AUTHOR>
 * @email <EMAIL>
 * @create date 2024-09-11 10:40:20
 * @modify date 2024-09-11 10:40:20
 * @desc [中心配置查询hook]
 */
import { getZones } from '@/api/rcmApi';
import { ref } from 'vue';

const list = ref([]);
/**
 * 中心内配置查询
 */
export function useZonesList() {
    const fetchData = async (rcmId) => {
        const res = await getZones({ rcmId });
        list.value = res?.data || [];
    };

    return {
        list, fetchData
    };
}
