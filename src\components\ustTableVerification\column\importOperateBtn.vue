<template>
    <div class="btn-group">
        <h-button
            type="text"
            :disabled="startDisabled"
            @click="handleRowOperate('start')">启动
        </h-button>
        <h-button
            type="text"
            :disabled="stopDisabled"
            @click="handleRowOperate('stop')">停止
        </h-button>
        <!-- <h-button
            type="text"
            @click="handleRowOperate('view')">查看
        </h-button> -->
        <h-dropdown @on-click="handleRowOperate">
            <a href="javascript:void(0)" class="more">···</a>
            <h-dropdown-menu slot="list">
                <h-dropdown-item name="delete" :disabled="startDisabled">
                    删除
                </h-dropdown-item>
                <h-dropdown-item name="record">
                    查看执行记录
                </h-dropdown-item>
                <!-- <h-dropdown-item name="tag">
                    修改标签
                </h-dropdown-item> -->
            </h-dropdown-menu>
        </h-dropdown>
    </div>
</template>

<script>
export default {
    name: 'ImportOperateBtn',
    props: {
        row: {
            type: Object,
            default: () => {}
        },
        startLoading: {
            type: Boolean,
            default: false
        }
    },
    computed: {
        startDisabled() {
            return this.row?.compareResult === 'running';
        },
        stopDisabled() {
            return this.row?.compareResult !== 'running';
        }
    },
    methods: {
        // 处理表格行操作
        handleRowOperate(operate) {
            this.$emit('row-operate', operate);
        }
    }
};
</script>

<style lang="less" scoped>
.btn-group {
    display: inline-block;

    /deep/ .h-btn-text {
        padding: 6px 3px;
    }

    /deep/ .h-btn-disable {
        color: #969797;
    }

    .more {
        font-weight: 600;
        margin: 0 3px;
    }
}
</style>
