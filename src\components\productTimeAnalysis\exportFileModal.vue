<template>
    <div>
        <h-msg-box-safe
            v-model="modalData.status"
            :escClose="true"
            :mask-closable="false"
            title="查询结果导出"
            width="50"
            maxHeight="240"
            @on-open="getCollections"
        >
            <h-form
                ref="formValidate"
                :model="formValidate"
                :label-width="100"
                >
                <h-form-item label="导出文件名:" prop="name" required>
                    <h-input
                        v-model="formValidate.name"
                        placeholder="请输入查询名称"
                    >
                    <template v-slot:append>
                        <h-select v-model="formValidate.type" size="small" style="width: 80px; height: 24px;" :clearable="false">
                        <h-option value="csv">.csv</h-option>
                        <h-option value="doc">.doc</h-option>
                    </h-select>
                   </template>
                </h-input>
                </h-form-item>
                <h-form-item  label="导出字段:" prop="indicators" required>
                    <h-checkbox-group v-model="formValidate.indicators">
                        <h-checkbox v-for="item in indicatorList" :key="item.key" :label="item.key">{{ item.title
                        }}</h-checkbox>
                    </h-checkbox-group>
                </h-form-item>
            </h-form>

            <template v-slot:footer>
                <a-button @click="modalData.status = false">取消</a-button>
                <a-button type="primary" :loading="loading" @click="submitConfig">确定</a-button>
            </template>
        </h-msg-box-safe>
    </div>
</template>

<script>
import aButton from '@/components/common/button/aButton';
export default {
    props: {
        modalInfo: {
            type: Object,
            default: null
        }
    },
    data() {
        return {
            modalData: this.modalInfo,
            loading: false,
            indicatorList: [],
            formValidate: {
                name: '',
                type: 'csv',
                indicators: []
            }
        };
    },
    methods: {
        getCollections() {
            this.modalData.columnData.forEach((ele, index) => {
                if (index) {
                    this.formValidate.indicators.push(ele.key);
                    this.indicatorList.push({
                        key: ele.key,
                        title: ele.title
                    });
                }
            });
        },
        submitConfig() {
            const that = this;
            this.$refs['formValidate'].validate(async (valid) => {
                if (valid) {
                    that.loading = true;
                    try {
                        this.$emit('update', { ...this.formValidate });
                        that.$hMessage.success('保存成功!');
                        that.modalInfo.status = false;
                    } catch (err) {
                        that.loading = false;
                    }
                }
            });
        }
    },
    components: { aButton }
};
</script>
<style  lang="less" scoped>
.h-form-item {
    margin-bottom: 0;
}
</style>
