<!-- 核心同步详情弹窗 -->
<template>
    <div>
        <h-drawer
            ref="drawer-box"
            v-model="modalData.status"
            :title="drawerTitle"
            width="50"
            @on-close="handleDrawerClose">
            <div class="drawer-box">
                <div class="content-box">
                    <div class="table-title">{{ dataTitle }}</div>
                    <a-table
                        showTitle
                        :hasPage="false"
                        :row-class-name="rowClassName"
                        :tableData="tableData"
                        :columns="columns">
                    </a-table>
                </div>

                <div class="content-box">
                    <div class="chart-title">{{ dataTitle }}变化趋势</div>
                    <chart
                        v-if="showChart"
                        ref="chartDeploy"
                        :basicOpiton="chartDeploy.basicOpiton"
                        :chartData="chartDeploy.chartData"
                        class="chart-style">
                    </chart>
                    <no-data v-if="!showChart" class="no-data-style" />
                </div>
            </div>
        </h-drawer>
    </div>
</template>

<script>
import chart from '@/components/common/chart/chart';
import noData from '@/components/common/noData/noData';
import { formatChartNumber } from '@/utils/utils';
import aTable from '@/components/common/table/aTable';

// title名枚举
const TITLE_ENUM = {
    masterCommitSqn: '主核心事务数',
    standbyMaxCommitSqnDiff: '主备核心事务差量',
    todbMaxCommitSqnDiff: '回库事务差量'
};

export default {
    name: 'DataAccordDrawer',
    components: { chart, noData, aTable },
    props: {
        modalInfo: {
            type: Object,
            default: null
        }
    },
    computed: {
        // 侧弹窗标题
        drawerTitle() {
            // 获取某一个核心的类型
            const instanceType = this.modalData?.insData?.[0]?.instanceType;
            const appTypeDictDesc = this.$store?.state?.apmDirDesc?.appTypeDictDesc;
            const coreName = appTypeDictDesc?.[instanceType];
            return `${coreName || instanceType}(${this.modalData?.shardingNo ?? '-'}分片)`;
        },
        // 表格及趋势图标题
        dataTitle() {
            return TITLE_ENUM?.[this.modalData?.dataType];
        }
    },
    data() {
        return {
            modalData: this.modalInfo,
            loading: true,
            columns: [
                {
                    title: '核心/回库',
                    key: 'instanceName',
                    ellipsis: true
                },
                {
                    title: '角色',
                    key: 'clusterRole',
                    render: (h, params) => {
                        const appInstanceClusterRoleDict = this.$store.state.apmDirDesc?.appInstanceClusterRoleDict;
                        return h('div', appInstanceClusterRoleDict?.[params?.row?.clusterRole] || '-');
                    }
                },
                {
                    title: '事务号',
                    key: 'commitSqn',
                    render: (h, params) => {
                        return h('div', params.row.commitSqn ?? '-');
                    }
                },
                {
                    title: '与主核心事务差量',
                    key: 'commitSqnDiff',
                    minWidth: 130,
                    render: (h, params) => {
                        return h('div', params.row.commitSqnDiff ?? '-');
                    }
                }
            ],
            tableData: [],
            showChart: true,
            chartDeploy: {
                basicOpiton: {
                    chartType: 'axis',
                    lineDeploy: [],
                    yAxiDeploy: [
                        {
                            name: '事务量差(个)',
                            axisLabel: {
                                formatter: formatChartNumber
                            }
                        }
                    ]
                },
                chartData: {
                    xData: [],
                    data: {}
                }
            }
        };
    },
    mounted() {
        this.handleDrawerOpen();
    },
    beforeDestroy() {
    },
    methods: {
        // 打开弹窗
        async handleDrawerOpen() {
            const insData = this.modalData?.insData || [];
            const filterData = this.handleDataFilter(insData);
            // 初始化折线图
            this.$nextTick(() => {
                this.initDrawerData(filterData);
            });
        },
        // 处理数据过滤
        handleDataFilter(insData) {
            let tableData = [];

            if (this.modalData?.dataType === 'masterCommitSqn') {
                // 点击【主核心事务数】时：显示主核心应用节点
                this.columns = this.columns.slice(0, 3);
                tableData = insData.filter(o => o?.clusterRole === 'ARB_ACTIVE');
            } else if (this.modalData?.dataType === 'standbyMaxCommitSqnDiff') {
                // 点击【备核心最大事务差量】时：显示身份为bizproc的应用节点
                tableData = insData.filter(o => o?.instanceIdentities?.includes('bizproc'));
            } else if (this.modalData?.dataType === 'todbMaxCommitSqnDiff') {
                // 点击【回库最大事务差量】时：显示身份为todb的应用节点，和主核心应用节点
                tableData = insData.filter(o =>
                    (Array.isArray(o?.instanceIdentities) && o.instanceIdentities?.includes('todb')) ||
                     o?.clusterRole === 'ARB_ACTIVE'
                );
            }

            // 将 clusterRole 为 ARB_ACTIVE 的项移到第一个位置
            const activeIndex = tableData.findIndex(o => o.clusterRole === 'ARB_ACTIVE');
            if (activeIndex > -1) {
                const [activeItem] = tableData.splice(activeIndex, 1);
                tableData.unshift(activeItem);
            }

            return tableData;
        },
        // 初始化折线图数据
        initDrawerData(data) {
            this.showChart = false;
            // echart折线图销毁再创建-解决线条变化echart（option有缓存）不更新的问题
            setTimeout(() => {
                if (data && data.length) {
                    this.showChart = true;
                } else return;
            }, 0);
            const lineDeploy = [];
            const yData = {};
            Array.isArray(data) && data.forEach((item) => {
                // 事务量差折线图数据初始化
                const lineObj = {
                    name: item.instanceName,
                    type: 'line',
                    data: [item.commitSqnDiff],
                    smooth: true
                };
                // 主核心事务数折线图数据初始化
                const masterLineObj = {
                    name: item.instanceName,
                    type: 'line',
                    data: [item.commitSqn],
                    smooth: true
                };
                /**
                 * 备核心及回库节点事务差量
                 * 需过滤掉主核心节点不进行展示
                 * 展示与主核心比较的备核心或回库的差量变化趋势
                 */
                if (item.clusterRole !== 'ARB_ACTIVE') {
                    lineDeploy.push(lineObj);
                    yData[item.instanceName] = [];
                /**
                 * 主核心事务量
                 * 仅在点击【主核心事务数】时，弹窗展示主核心事务数变化趋势
                 */
                } else if (this.modalData?.dataType === 'masterCommitSqn') {
                    lineDeploy.push(masterLineObj);
                    yData[item.instanceName] = [];
                }
            });

            this.chartDeploy.basicOpiton.lineDeploy = lineDeploy;
            this.chartDeploy.chartData.data = yData;

            this.handleDataUpdate();
        },
        // 轮询设置弹窗数据
        handleDataUpdate() {
            const insData = this.modalData?.insData || [];
            const filterData = this.handleDataFilter(insData);
            this.tableData = filterData;

            const newTime = this.$getCurrentLocalTime();
            const yData = {};
            Array.isArray(filterData) && filterData.forEach(item => {
                if (this.modalData?.dataType === 'masterCommitSqn') {
                    // 主核心事务量
                    yData[item.instanceName] = item.commitSqn;
                } else {
                    // 与主核心事务差量
                    yData[item.instanceName] = item.commitSqnDiff;
                }
            });

            const chartData = this.chartDeploy.chartData;
            if (chartData.xData.length > 60) {
                chartData.xData.shift();
                Object.values(chartData.data).forEach(item => {
                    item.shift();
                });
            }
            chartData.xData.push(newTime);
            Object.keys(chartData.data).forEach(item => {
                chartData.data[item].push(yData?.[item]);
            });
        },
        // 关闭侧弹窗 清理数据
        handleDrawerClose() {
            this.modalData.status = false;
            this.tableData = [];
            this.chartDeploy.chartData = {
                xData: [],
                data: {}
            };

            this.$emit('drawer-close');
        },
        // 修改表格行样式
        rowClassName(row, index) {
            if (index === 0 && row?.clusterRole === 'ARB_ACTIVE') {
                return 'demo-table-info-row';
            }
        }
    }
};
</script>

<style lang="less" scoped>
@import url("@/assets/css/drawer.less");

.content-box {
    margin-bottom: 30px;

    .table-title,
    .chart-title {
        position: relative;
        font-size: 14px;
        text-align: left;
        font-weight: 500;
        color: var(--font-color);
        line-height: 20px;
        padding-left: 16px;
        margin-bottom: 12px;

        &::before {
            content: "";
            display: inline-block;
            position: absolute;
            left: 0;
            width: 4px;
            height: 20px;
            background: var(--link-color);
        }
    }

    .chart-style {
        width: 100%;
        height: 230px;
        margin-top: 10px;
    }

    .no-data-style {
        height: 250px;
    }
}
</style>
