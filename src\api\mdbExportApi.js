import fetch from './fetch';
import { objectToQueryString } from '@/utils/utils';

const prefix = window['LOCAL_CONFIG']['API_HOME'] || '/';

/**
 * 查询最近一次导出任务
 */
export function getLatestExportData(param) {
    return fetch().get(`${prefix}/mdb/table/export/info?${objectToQueryString(param)}`);
}

/**
 * 查询mdb导出历史
 */
export function getExportHistory(param) {
    return fetch().get(`${prefix}/mdb/table/export/his/info?${objectToQueryString(param)}`);
}

/**
 * 导出下载
 */
export function downloadMdb(param) {
    return fetch({ responseType: 'blob', includeResponseHeaders: true, timeout: 60000 * 60 * 2 }).get(`${prefix}/mdb/table/export/download?${objectToQueryString(param)}`);
}

/**
 * 查询表信息
 */
export function getExportTables(param) {
    return fetch().get(`${prefix}/mdb/tables?${objectToQueryString(param)}`);
}

/**
 * 创建导出任务
 */
export function createExportTask(param) {
    return fetch().post(`${prefix}/mdb/table/export/start`, param);
}

/**
 * 删除历史导出任务
 */
export function deleteExportHistory(param) {
    return fetch().get(`${prefix}/mdb/table/export/delete?${objectToQueryString(param)}`);
}

/**
 * 获取路径
 */
export function getExportPath() {
    return fetch().get(`${prefix}/mdb/table/export/path`);
}
