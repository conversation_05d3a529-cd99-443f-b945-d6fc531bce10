<!--
 * @Description:
 * @Author: <PERSON><PERSON>
 * @Date: 2022-03-08 10:28:21
 * @LastEditTime: 2022-03-25 10:20:59
 * @LastEditors: <PERSON><PERSON> Ying
-->
<template>
    <div>
        <!-- 下发配置列表 -->
        <h-msg-box-safe
            v-model="modalData.status"
            :escClose="true"
            :mask-closable="false"
            title="更新测试实例名称"
            width="50"
            @on-open="getCollections"
        >
            <h-form
                ref="formValidate"
                :model="formValidate"
                :label-width="100"
                >
                <h-form-item label="实例名称" prop="name" required>
                    <h-input
                        v-model="formValidate.name"
                        placeholder="请输入测试实例名称"
                         onkeydown="if(event.keyCode==13){event.keyCode=0;event.returnValue=false;}"
                    ></h-input>
                </h-form-item>
            </h-form>

            <template v-slot:footer>
                <a-button @click="modalData.status = false">取消</a-button>
                <a-button type="primary" :loading="loading" @click="submitConfig">确定</a-button>
            </template>
        </h-msg-box-safe>
    </div>
</template>

<script>
import { updateCaseInfo } from '@/api/httpApi';
import aButton from '@/components/common/button/aButton';
export default {
    props: {
        modalInfo: {
            type: Object,
            default: null
        }
    },
    data() {
        return {
            modalData: this.modalInfo,
            loading: false,
            formValidate: {
                name: ''
            }
        };
    },
    methods: {
        getCollections() {
            this.formValidate.name = this.modalData.name;
        },
        submitConfig() {
            const that = this;
            this.$refs['formValidate'].validate(async (valid) => {
                if (valid) {
                    if (this.formValidate.name.length > 30) {
                        this.$hMessage.error('字符长度数不得超过30！');
                        return;
                    }
                    that.loading = true;
                    try {
                        const res = await updateCaseInfo({
                            testCaseInstanceId: this.modalInfo.testCaseInstanceId,
                            testCaseInstanceName: this.formValidate.name
                        });
                        if (res.success) {
                            this.$emit('update', this.modalInfo.testCaseInstanceId);
                            that.$hMessage.success('实例名称更新成功!');
                            that.modalInfo.status = false;
                        } else {
                            that.loading = false;
                            this.$hMessage.error('实例更新失败!');
                        }
                    } catch (err) {
                        that.loading = false;
                    }
                }
            });
        }
    },
    components: {  aButton }
};
</script>
