import fetch from './fetch';
import { objectToQueryString } from '@/utils/utils';

const prefix = window['LOCAL_CONFIG']['API_HOME'] || '/';
const prefix2 = window['LOCAL_CONFIG']['API_HOME2'] || '/';

/**
 * 执行sql
 */
export function execSql(param) {
    return fetch().get(`${prefix}/monitor/data-check/sql?${objectToQueryString(param)}`);
}

/**
  * 保存规则
*/
export function saveRule(param) {
    return fetch().post(`${prefix2}/monitor/rule`, param);
}

/**
 * 查询监控规则列表
 */
export function getMonitorRules(param) {
    return fetch().get(`${prefix2}/monitor/rules?${objectToQueryString(param)}`);
}

/**
 * 开启/关闭监控规则
 */
export function exchangeRuleEnable(param) {
    return fetch().post(`${prefix}/monitor/rule/enable`, param);
}

/**
 * 删除监控规则
 */
export function delMonitorRule(param) {
    return fetch().post(`${prefix}/monitor/rule/delete`, param);
}

/**
 * 查询监控规则T3监视器配置
 */
export function getMonitorRuleT3Api(param) {
    return fetch().get(`${prefix}/monitor/rules/t3-api/config?${objectToQueryString(param)}`);
}
