<template>
    <div ref="table-box" class="table-box">
        <a-simple-table
            showTitle
            :columns="columns"
            :tableData="tableData"
            :loading="tableLoading"
            :hasPage="false"
            :height="tableHeight">
        </a-simple-table>

        <appearanceResultDrawer
            v-if="drawerInfo.status"
            :productId="productId"
            :modalInfo="drawerInfo">
        </appearanceResultDrawer>
    </div>
</template>

<script>
import aSimpleTable from '@/components/common/table/aSimpleTable';
import importStatusTableIcon from '@/components/secondAppearance/importStatusTableIcon.vue';
import { getAppearHistoryData } from '@/api/brokerApi';
import appearanceResultDrawer from './modal/appearanceResultDrawer.vue';
import { TASK_EXECUTE_STATUS } from '@/components/dataSecondAppearance/constant.js';

export default {
    components: {
        aSimpleTable, appearanceResultDrawer
    },
    props: {
        productId: {
            type: String,
            default: ''
        }
    },
    data() {
        return {
            tableLoading: false,
            tableHeight: 200,
            columns: [
                {
                    title: '上场时间',
                    key: 'startTime',
                    minWidth: 150,
                    ellipsis: true
                },
                {
                    title: '执行状态',
                    minWidth: 120,
                    render: (_, params) => {
                        const text = TASK_EXECUTE_STATUS.find(status => status.value === params.row.importStatus)?.label || '';
                        let sqlTableIconType;
                        switch (params.row.importStatus) {
                            case 'warn':
                                sqlTableIconType = 'warn';
                                break;
                            case 'succeeded':
                                sqlTableIconType = 'success';
                                break;
                            case 'failed':
                                sqlTableIconType = 'error';
                                break;
                        }
                        return <div>
                            <importStatusTableIcon type={sqlTableIconType}/>
                            { text }
                            { params.row.failInfo
                                ? <h-poptip
                                    autoPlacement
                                    transfer
                                    customTransferClassName='apm-poptip'
                                    content={params.row.failInfo}
                                    trigger="click">
                                    <span>，
                                        <span class="click-text hover-underline">查看原因</span>
                                    </span>
                                </h-poptip>
                                : '' }
                        </div>;
                    }
                },
                {
                    title: '结束时间',
                    key: 'endTime',
                    minWidth: 150,
                    ellipsis: true
                },
                {
                    title: '上场结果',
                    key: 'result',
                    minWidth: 200,
                    ellipsis: true,
                    render: (h, { row }) => {
                        const spanText = `需上场表数量 ${row.tableTotalCount}（成功 ${row.tableSuccessCount} / 失败 ${row.tableFailCount}）`;
                        return h('div', {
                            attrs: {
                                class: 'h-table-cell-ellipsis hover-underline',
                                title: spanText
                            },
                            style: {
                                color: '#2D8DE5',
                                cursor: 'pointer'
                            },
                            on: {
                                click: () => {
                                    this.drawerInfo.status = true;
                                    this.drawerInfo.importId = row.importId;
                                }
                            }
                        }, spanText);
                    }
                }
            ],
            tableData: [],
            drawerInfo: {
                status: false
            }
        };
    },
    mounted() {
        window.addEventListener('resize', this.fetTableHeight);
        this.fetTableHeight();
    },
    beforeDestroy() {
        window.removeEventListener('resize', this.fetTableHeight);
    },
    methods: {
        // 初始化数据方法
        async initData() {
            this.tableLoading = true;
            try {
                // 调用接口获取表格数据
                const params = {
                    productId: this.productId
                };
                const res = await getAppearHistoryData(params);
                if (res.code === '200') {
                    this.tableData = res.data || [];
                } else {
                    this.tableData = [];
                    this.$hMessage.error(res.message);
                }
            } catch (err) {
                // 捕获错误并打印
                console.error(err);
                this.tableData = [];
            }
            this.tableLoading = false;
        },
        // 设置表格高度方法
        fetTableHeight() {
            this.tableHeight = this.$refs['table-box']?.offsetHeight;
        }
    }
};
</script>
<style lang="less">
@import url("@/assets/css/poptip-1.less");

.apm-poptip .h-poptip-inner {
    .h-poptip-body-content {
        max-width: 400px;
        max-height: 200px;
        white-space: normal;
        word-break: break-all;
    }

    .h-poptip-body-content-inner {
        color: var(--font-color);
    }
}

.hover-underline:hover {
    text-decoration: underline;
    text-decoration-color: #2d8de5;
}
</style>

<style lang="less" scoped>
/* 组件样式 */
.table-box {
    height: calc(100% - 15px);
    padding: 0 10px;
}

.click-text {
    color: var(--link-color);

    &:hover {
        cursor: pointer;
    }
}
</style>
