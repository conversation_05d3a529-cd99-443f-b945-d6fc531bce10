<template>
    <div ref="tab-box" class="tab-box">
        <a-loading v-if="loading"></a-loading>
        <div v-else>
            <obs-table ref="table" :height="tableHeight" :title="title" showTitle :tableData="tableData" :columns="columns" />
        </div>
    </div>
</template>

<script>
import aLoading from '@/components/common/loading/aLoading';
import obsTable from '@/components/common/obsTable/obsTable';
import { formatTimeAgo } from '@/utils/utils';
import { getManagerProxy } from '@/api/mcApi';
export default {
    name: 'ConnectionInfo',
    components: { aLoading, obsTable },
    props: {
        nodeData: {
            type: Object,
            default: () => { }
        },
        pollTime: {
            type: Number,
            default: 0
        }
    },
    data() {
        return {
            loading: true,
            title: {
                label: '连接信息'
            },
            tableData: [],
            columns: [
                {
                    title: '连接号',
                    key: 'ConnectIndex'
                },
                {
                    title: '发送次数',
                    key: 'SendTimes'
                },
                {
                    title: '接收次数',
                    key: 'RecvTimes'
                },
                {
                    title: '最近一次连接建立时间',
                    key: 'ConnectedTimeSec',
                    minWidth: 160,
                    render: (h, params) => {
                        const res = formatTimeAgo(params.row.ConnectedTimeSec);
                        return h('span', [res]);
                    }
                },
                {
                    title: '最近一次发送完成时间',
                    key: 'LastSendedTimeSec',
                    minWidth: 160,
                    render: (h, params) => {
                        const res = formatTimeAgo(params.row.LastSendedTimeSec);
                        return h('span', [res]);
                    }
                },
                {
                    title: '最近一次接收完成时间',
                    key: 'LastRecvedTimeSec',
                    minWidth: 160,
                    render: (h, params) => {
                        const res = formatTimeAgo(params.row.LastRecvedTimeSec);
                        return h('span', [res]);
                    }
                },
                {
                    title: '客户端地址',
                    key: 'Address',
                    minWidth: 110
                },
                {
                    title: '客户端端口',
                    key: 'Port'
                },
                {
                    title: '协议类型',
                    key: 'Protocol'
                }
            ],
            tableHeight: 0
        };
    },
    mounted() {
        window.addEventListener('resize', this.fetTableHeight);
    },
    beforeDestroy() {
        window.removeEventListener('resize', this.fetTableHeight);
    },
    methods: {
        async initData() {
            this.loading = true;
            await this.getFileData();
            this.loading = false;
            this.fetTableHeight();
        },
        // 设置table高度
        fetTableHeight() {
            this.tableHeight = this.$refs['tab-box']?.offsetHeight - 64;
        },
        // 构造页面数据
        async getFileData() {
            const { GetConnectInfo } = await this.getAPi();
            this.tableData = GetConnectInfo;
        },
        // 接口请求
        async getAPi() {
            let data = {};
            const param = [
                {
                    manageProxyIp: this.nodeData.manageProxyIp,
                    manageProxyPort: this.nodeData.manageProxyPort,
                    pluginName: 'ldp_bizproc',
                    funcName: 'GetConnectInfo'
                }
            ];
            try {
                const res = await getManagerProxy(JSON.stringify(param));
                if (res.code === '200') {
                    !res.data?.[0]?.ErrorNo && Object.keys(res.data?.[0]).length && (data = res.data[0]);
                    return data;
                }
            } catch (err) {
                this.$emit('clear');
            }
        }
    }
};
</script>

<style lang="less" scoped>
.tab-box {
    height: 100%;
    overflow: hidden;

    .obs-table {
        margin: 0;
    }
}
</style>
