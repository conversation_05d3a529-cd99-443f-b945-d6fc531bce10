<!--
 * @Description: 产品数据存储
 * @Author: <PERSON><PERSON>
 * @Date: 2023-01-05 16:29:42
 * @LastEditTime: 2023-11-21 18:13:40
 * @LastEditors: yingzx38608 <EMAIL>
-->
<template>
  <div class="main" style="overflow-y: auto; background: #262b40;">
    <a-title title="监控数据管理">
      <slot>
        <span style="float: right; padding-right: 10px;"
          >已连接ElasticSearch: {{ esUrl }}</span
        >
      </slot>
    </a-title>
    <h-tabs
      ref="table-box"
      v-model="tabName"
      class="product-box"
      @on-click="tabClick(tabName)"
    >
      <h-tab-pane
        v-for="(item, index) in tabTableData"
        :key="item.name"
        :label="item.label"
        :name="item.name"
      >
        <div class="clear-btn">
          <a-button type="dark" @click="handleClearModal(item.type, item.name)"
            >按条件清理</a-button
          >
        </div>
        <a-table
          :ref="'table' + index"
          class="table-storage"
          :height="tableHeight"
          :tableData="item.tableData"
          :columns="column"
          :hasPage="!!item.tableData.length"
          :total="item.total"
          @query="getManageIndexList(item.type, item.name, index)"
        />
      </h-tab-pane>
      <a-button slot="extra" type="primary" @click="showSettingModel"
        >定时清理</a-button
      >
    </h-tabs>
    <condition-modal v-if="conditionInfo.status" :modalInfo="conditionInfo" />
    <auto-condition-modal
      v-if="autoConditionInfo.status"
      :modalInfo="autoConditionInfo"
    />
  </div>
</template>

<script>
import _ from 'lodash';
import aTitle from '@/components/common/title/aTitle';
import aTable from '@/components/common/table/aTable';
import aButton from '@/components/common/button/aButton';
import conditionModal from '@/components/productDataStorage/conditionModal.vue';
import autoConditionModal from '@/components/productDataStorage/autoConditionModal.vue';
import {
    getManageIndexList,
    clearManageIndexData,
    delManageIndexData,
    getEsUrl
} from '@/api/httpApi';
export default {
    name: 'ProductDataStorage',
    components: { aTitle, aButton, aTable, conditionModal, autoConditionModal },
    data() {
        return {
            tableHeight: 0,
            tabName: 'ldp-entrust-latency',
            column: [
                {
                    title: '索引名',
                    key: 'index'
                },
                {
                    title: '主分片数',
                    key: 'priNum'
                },
                {
                    title: '从分片数',
                    key: 'replicasNum'
                },
                {
                    title: '文档条数',
                    key: 'docsCount'
                },
                {
                    title: '空间占用',
                    key: 'storeSize',
                    render: (h, params) => {
                        return h(
                            'span',
                            {},
                            `${params.row.storeSize
                                .replace(/[a-z](?=\d)|\d(?=[a-z])/gi, '$& ')
                                .toUpperCase()}`
                        );
                    }
                },
                {
                    title: '操作',
                    key: 'action',
                    fixed: 'right',
                    width: 130,
                    render: (h, params) => {
                        const tabIndex = _.findIndex(this.tabTableData, (o) => {
                            return params.row.index.indexOf(o?.name) !== -1;
                        });
                        const tableData = this.tabTableData[tabIndex].tableData;
                        const indexType = this.tabTableData[tabIndex].type;
                        return h('div', [
                            h(
                                'Button',
                                {
                                    props: {
                                        type: 'text',
                                        size: 'small',
                                        loading: tableData[params.index].dataStatus === 'clear',
                                        disabled:
                      tableData[params.index].dataStatus === 'notOperable' ||
                      tableData[params.index].dataStatus === 'export'
                                    },
                                    on: {
                                        click: () => {
                                            this.$hMsgBoxSafe.confirm({
                                                title: `清空`,
                                                content: `您确定清空名为"${params.row.index}"的索引数据吗？`,
                                                onOk: async () => {
                                                    tableData[params.index].clearStu = true;
                                                    await this.clearManageIndexData(
                                                        params.row.index,
                                                        indexType
                                                    );
                                                    tableData[params.index].clearStu = false;
                                                }
                                            });
                                        }
                                    }
                                },
                                '清空'
                            ),
                            h(
                                'Button',
                                {
                                    props: {
                                        type: 'text',
                                        size: 'small',
                                        loading:
                      tableData[params.index].dataStatus === 'delete' ||
                      tableData[params.index].delStu,
                                        disabled:
                      ['notOperable', 'clear'].indexOf(
                          tableData[params.index].dataStatus
                      ) > -1
                                    },
                                    on: {
                                        click: () => {
                                            this.$hMsgBoxSafe.confirm({
                                                title: `删除`,
                                                content: `您确定删除名为"${params.row.index}"的索引数据吗？`,
                                                onOk: async () => {
                                                    tableData[params.index].delStu = true;
                                                    await this.delManageIndexData(
                                                        params.row.index,
                                                        indexType
                                                    );
                                                    tableData[params.index].delStu = false;
                                                }
                                            });
                                        }
                                    }
                                },
                                '删除'
                            )
                        ]);
                    }
                }
            ],
            tabTableData: [
                {
                    label: '委托交易时延',
                    name: 'ldp-entrust-latency',
                    type: 'entrustLatency',
                    tableData: [],
                    total: 0
                },
                // {
                //     label: '委托查询时延数据',
                //     name: 'ldp-query-latency',
                //     type: 'entrustLatency',
                //     tableData: [],
                //     total: 0
                // },
                {
                    label: '时延跨度',
                    name: 'jaeger-span',
                    type: 'latency',
                    tableData: [],
                    total: 0
                },
                {
                    label: '监控指标',
                    name: 'ldp-apm-metrics',
                    type: 'monitor',
                    tableData: [],
                    total: 0
                }
            ],
            conditionInfo: {
                status: false,
                indexName: '',
                indexType: ''
            },
            esUrl: '',
            timer: null,
            autoConditionInfo: {
                status: false
            }
        };
    },
    async mounted() {
        this.getEsUrl();
        await this.tabClick(this.tabName);
        this.fetTableHeight();
        window.addEventListener('resize', this.fetTableHeight);
    },
    beforeDestroy() {
        this.timer && clearInterval(this.timer);
        window.removeEventListener('resize', this.fetTableHeight);
    },
    methods: {
        init(tabName) {
            const tabIndex = _.findIndex(this.tabTableData, ['name', tabName]);
            this.getManageIndexList(
                this.tabTableData[tabIndex].type,
                tabName,
                tabIndex
            );
        },
        // 设置table高度
        fetTableHeight() {
            this.tableHeight = this.$refs['table-box']?.$el?.offsetHeight - 150;
        },
        // 获取索引记录
        async getManageIndexList(indexType, indexName, index) {
            const param = {
                indexType,
                indexName,
                ...this.$refs[`table${index}`]?.[0]?.getPageData()
            };
            try {
                const res = await getManageIndexList(param);
                Array.isArray(res.data.list) &&
          res.data.list.forEach((ele) => {
              ele.delStu = false;
          });
                this.tabTableData[index].tableData = res.data.list;
                this.tabTableData[index].total = res.data.totalCount;
            } catch (err) {
                this.timer && clearInterval(this.timer);
            }
        },
        // 清空索引
        async clearManageIndexData(indexName, indexType) {
            await clearManageIndexData({
                indexName,
                indexType
            });
            this.init(this.tabName);
        },
        // 删除索引
        async delManageIndexData(indexName, indexType) {
            const res = await delManageIndexData({ indexName, indexType });
            if (res.success) {
                this.$hMessage.success('数据删除成功！');
                this.init(this.tabName);
            }
        },
        // 点击出现条件单弹窗
        handleClearModal(indexType, indexName) {
            this.conditionInfo.status = true;
            this.conditionInfo.indexName = indexName;
            this.conditionInfo.indexType = indexType;
        },
        // 获取ES地址
        async getEsUrl() {
            const { data } = await getEsUrl();
            this.esUrl = data?.url || '';
        },
        // 自动配置清理弹窗
        showSettingModel() {
            this.autoConditionInfo = {
                status: true
            };
        },
        // 切换tab
        tabClick(name) {
            this.tabName = name;
            this.timer && clearInterval(this.timer);
            this.init(name);
            this.timer = setInterval(() => {
                this.init(name);
            }, 3000);
        }
    }
};
</script>

<style scoped lang="less">
@import url("@/assets/css/tab.less");

.table-storage {
    /deep/ .h-btn-disable {
        color: #ccc;

        &:hover {
            text-decoration: none;
            color: #ccc !important;
        }
    }
}

.clear-btn {
    display: flex;
    justify-content: right;
    align-items: center;
    font-size: 14px;
    color: #fff;
    padding: 0 10px;
}

.product-box {
    /deep/ .h-tabs-nav-wrap {
        float: none !important;
    }

    /deep/ .h-tabs-nav-right {
        position: absolute;
        right: 0;
        top: 5px;
    }
}
</style>
