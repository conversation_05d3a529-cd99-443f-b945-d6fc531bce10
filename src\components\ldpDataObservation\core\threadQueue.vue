<template>
    <div class="tab-box">
        <a-loading v-if="loading"></a-loading>
        <div v-else>
            <!-- 全局队列信息 GetGlobalQueueInfo -->
            <info-grid :gridData="globalData"></info-grid>
            <!-- 线程处理吞吐详情 GetWorkQueueRequests GetWorkQueueInfo -->
            <obs-table ref="table" :title="title" :tableData="tableData" :columns="columns" showTitle highlightRow rowSelectOnly :maxHeight="200"
                @select-change="selectChange" @on-current-change="tableRowcheckedChange"/>
            <!-- 工作线程 -->
            <info-grid v-if="currentThreadId || currentThreadId === 0" :gridData="threadIdData"></info-grid>
            <!-- redo线程 GetTransactionNo -->
            <description-bar :data="redoInfo"></description-bar>
        </div>
    </div>
</template>

<script>
import _ from 'lodash';
import { getManagerProxy } from '@/api/mcApi';
import { compareObjArr, formatChartNumber, calculateTimeDifference, formatNumber } from '@/utils/utils';
import aLoading from '@/components/common/loading/aLoading';
import obsTable from '@/components/common/obsTable/obsTable';
import infoGrid from '@/components/common/infoBar/infoGrid';
import descriptionBar from '@/components/common/description/descriptionBar';
const POINT_COUNT = 60;
export default {
    name: 'ThreadQueue',
    components: { aLoading, infoGrid, obsTable, descriptionBar },
    props: {
        nodeData: {
            type: Object,
            default: () => { }
        },
        pollTime: {
            type: Number,
            default: 0
        }
    },
    data() {
        return {
            timer: null,
            loading: true,
            // 全局队列信息
            globalData: {
                title: {
                    label: '全局队列信息'
                },
                layout: [
                    { x: 0, y: 0, w: 3, h: 9, i: 'info1' },
                    { x: 3, y: 0, w: 6, h: 9, i: 'info2' },
                    { x: 9, y: 0, w: 3, h: 9, i: 'info3' }
                ],
                details: [
                    {
                        type: 'obj',
                        title: '队列处理吞吐',
                        infoDic: [
                            {
                                label: 'ProcessedCount',
                                key: 'ProcessedCount'
                            },
                            {
                                label: 'ProcessedRate(个/秒)',
                                key: 'ProcessedRate'
                            }
                        ],
                        info: {}
                    },
                    {
                        type: 'chart',
                        info: {
                            height: '170',
                            basicOpiton: {
                                chartType: 'axis',
                                lineDeploy: [
                                    {
                                        name: 'ProcessedRate',
                                        type: 'line'
                                    },
                                    {
                                        name: 'ProcessedCount',
                                        type: 'line',
                                        yAxisIndex: 1
                                    }
                                ],
                                yAxiDeploy: [
                                    {
                                        name: 'ProcessedRate',
                                        alignTicks: true,
                                        axisLabel: {
                                            formatter: formatChartNumber
                                        }
                                    },
                                    {
                                        name: 'ProcessedCount',
                                        show: true,
                                        alignTicks: true,
                                        axisLabel: {
                                            formatter: formatChartNumber
                                        }
                                    }
                                ]
                            },
                            additionalOpiton: {
                                backgroundColor: '#2d334c'
                            },
                            chartData: {
                                xData: [],
                                data: {
                                    ProcessedRate: [],
                                    ProcessedCount: []
                                }
                            }
                        }
                    },
                    {
                        type: 'combine',
                        title: '全局队列容量',
                        info: [
                            {
                                type: 'process-bar',
                                info: [
                                    {
                                        key: 'sizeKB',
                                        scale: '0',
                                        text: '0 / 0'
                                    },
                                    {
                                        key: 'queueCount',
                                        scale: '0',
                                        text: '0 / 0'
                                    }
                                ]
                            },
                            {
                                type: 'obj',
                                infoDic: [
                                    {
                                        label: 'QueueExpandCount',
                                        key: 'QueueExpandCount'
                                    }
                                ],
                                info: {}
                            }
                        ]
                    }
                ]
            },
            // 线程处理吞吐详情
            selectTopValue: 30,
            title: {
                label: '线程处理吞吐详情',
                slots: [
                    {
                        key: 'topSelect',
                        type: 'select',
                        defaultValue: 30,
                        options: [
                            {
                                value: 10,
                                label: 'Top10'
                            },
                            {
                                value: 30,
                                label: 'Top30'
                            },
                            {
                                value: 50,
                                label: 'Top50'
                            }
                        ]
                    }
                ]
            },
            columns: [
                { key: 'ThreadId', title: '线程序号', sortable: true, minWidth: 100 },
                { key: 'ProcessedRate', title: '队列处理吞吐', sortable: true, minWidth: 120 },
                { key: 'ProcessedCount', title: '线程执行任务数', sortable: true, minWidth: 135 },
                { key: 'QueueCount', title: '队列积压消息个数', sortable: true, minWidth: 155 },
                { key: 'InUseSizeKB', title: '队列使用内存', sortable: true, minWidth: 120 },
                { key: 'MaxHisQueueCount', title: '工作队列历史缓存最大消息个数', sortable: true, minWidth: 240 },
                { key: 'Running', title: '最后处理的功能号是否正在处理', sortable: true, minWidth: 240 },
                { key: 'SystemNo', title: '最后处理的功能号对应的子系统号', sortable: true, minWidth: 250 },
                { key: 'FunctionNo', title: '最后处理的功能号', sortable: true, minWidth: 155 }
            ],
            tableData: [],
            currentThreadId: null, // 表格当前选中行ThreadId
            // 工作线程
            threadIdData: {
                title: {
                    label: '工作线程',
                    slots: [
                        {
                            type: 'text', // 文本
                            label: '最后处理的功能号是否正在处理',
                            value: '-',
                            poptipInfo: {
                                placement: 'left-end',
                                title: '',
                                contentDic: {
                                    Running: '最后处理的功能号是否正在处理',
                                    PollingLoops: '线程轮询次数',
                                    SleepTimeUS: '睡眠时间'
                                },
                                content: {}
                            }
                        }
                    ]
                },
                layout: [
                    { x: 0, y: 0, w: 3, h: 9, i: 'info1' },
                    { x: 3, y: 0, w: 9, h: 9, i: 'info2' },
                    { x: 0, y: 1, w: 3, h: 9, i: 'info3' },
                    { x: 3, y: 1, w: 9, h: 9, i: 'info4' },
                    { x: 0, y: 2, w: 3, h: 9, i: 'info5' },
                    { x: 3, y: 2, w: 3, h: 9, i: 'info6' },
                    { x: 6, y: 2, w: 3, h: 9, i: 'info7' },
                    { x: 9, y: 2, w: 3, h: 9, i: 'info8' }
                ],
                details: [
                    {
                        type: 'obj',
                        title: '队列处理吞吐',
                        infoDic: [
                            {
                                label: 'ProcessedCount',
                                key: 'ProcessedCount'
                            },
                            {
                                label: 'ProcessedRate(个/秒)',
                                key: 'ProcessedRate'
                            }
                        ],
                        info: {}
                    },
                    {
                        type: 'chart',
                        info: {
                            height: '170',
                            basicOpiton: {
                                chartType: 'axis',
                                lineDeploy: [
                                    {
                                        name: 'ProcessedRate',
                                        type: 'line'
                                    },
                                    {
                                        name: 'ProcessedCount',
                                        type: 'line',
                                        yAxisIndex: 1
                                    }
                                ],
                                yAxiDeploy: [
                                    {
                                        name: 'ProcessedRate',
                                        alignTicks: true,
                                        axisLabel: {
                                            formatter: formatChartNumber
                                        }
                                    },
                                    {
                                        name: 'ProcessedCount',
                                        show: true,
                                        alignTicks: true,
                                        axisLabel: {
                                            formatter: formatChartNumber
                                        }
                                    }
                                ]
                            },
                            additionalOpiton: {
                                backgroundColor: '#2d334c'
                            },
                            chartData: {
                                xData: [],
                                data: {
                                    ProcessedRate: [],
                                    ProcessedCount: []
                                }
                            }
                        }
                    },
                    {
                        type: 'combine',
                        title: '线程队列容量',
                        info: [
                            {
                                type: 'process-bar',
                                info: [
                                    {
                                        key: 'queueCount',
                                        scale: '0',
                                        text: 'QueueCount/MaxQueueSize'
                                    }
                                ]
                            },
                            {
                                type: 'obj',
                                infoDic: [
                                    {
                                        label: 'MaxHisQueueCount',
                                        key: 'MaxHisQueueCount'
                                    }
                                ],
                                info: {}
                            }
                        ]
                    },
                    {
                        type: 'chart',
                        info: {
                            height: '170',
                            basicOpiton: {
                                chartType: 'axis',
                                lineDeploy: [
                                    {
                                        name: 'QueueCount',
                                        type: 'line'
                                    },
                                    {
                                        name: 'MaxQueueSize',
                                        type: 'line'
                                    },
                                    {
                                        name: 'MaxHisQueueCount',
                                        type: 'line'
                                    }
                                ],
                                yAxiDeploy: [
                                    {
                                        name: '个',
                                        alignTicks: true,
                                        axisLabel: {
                                            formatter: formatChartNumber
                                        }
                                    }
                                ]
                            },
                            additionalOpiton: {
                                backgroundColor: '#2d334c'
                            },
                            chartData: {
                                xData: [],
                                data: {
                                    QueueCount: [],
                                    MaxQueueSize: [],
                                    MaxHisQueueCount: []
                                }
                            }
                        }
                    },
                    {
                        type: 'text',
                        title: '队列前30个功能号',
                        info: {
                            key: 'topFunctionNo',
                            value: ''
                        }
                    },
                    {
                        type: 'obj',
                        title: '功能号分组统计',
                        infoDic: [
                            {
                                label: '功能号',
                                key: 'FunctionNo'
                            }
                        ],
                        info: {
                            FunctionNo: '积压数'
                        }
                    },
                    {
                        type: 'obj',
                        title: '待出队功能号',
                        infoDic: [
                            {
                                label: '功能号',
                                key: 'FunctionID'
                            },
                            {
                                label: '请求数据长度',
                                key: 'Length'
                            },
                            {
                                label: '入队时间',
                                key: 'EnqueueTimeNs'
                            }
                        ],
                        info: {}

                    },
                    {
                        type: 'obj',
                        title: '正在处理功能号',
                        infoDic: [
                            {
                                label: '功能号',
                                key: 'FunctionNo'
                            },
                            {
                                label: '功能号对应的子系统号',
                                key: 'SystemNo'
                            }
                        ],
                        info: {}
                    }
                ]
            },
            // redo线程
            redoInfo: {
                title: {
                    label: 'redo线程'
                },
                details: [
                    {
                        dataDic: [
                            { label: '高可用模式', key: 'HaMode', span: 4 },
                            { label: '是否本地模式', key: 'IsLocalMode', span: 4 },
                            { label: '主备状态', key: 'ArbStatus', span: 4 },
                            { label: '下一个重放redo事务号', key: 'ReplayTransactionNo', span: 4 },
                            { label: '下一个写redo事务号', key: 'WriterTransactionNo', span: 4 },
                            { label: '写redo故障状态', key: 'IsWriteRedoFault', span: 4 }
                        ],
                        data: {}
                    }
                ]
            },
            lastGlobalProcessedCount: 0,
            wholeData: {}
        };
    },
    methods: {
        async initData() {
            this.loading = true;
            this.cleanGlobalChartData();
            this.cleanThreadIdChartData();
            await this.getFileData();
            setTimeout(() => {
                this.loading = false;
            }, 500);
        },
        // 处理折线图数据方法
        handleChartData(newTime) {
            const chartDataG = this.globalData.details[1].info.chartData;
            const chartData1 = this.threadIdData.details[1].info.chartData;
            const chartData3 = this.threadIdData.details[3].info.chartData;
            const chartDataList = [chartDataG, chartData1, chartData3];
            Array.isArray(chartDataList) && chartDataList.forEach(chartData => {
                if (chartData.xData.length > POINT_COUNT) {
                    chartData.xData.shift();
                    Object.values(chartData.data).forEach(item => {
                        item.shift();
                    });
                }
                if (!chartData.xData.includes(newTime)) chartData.xData.push(newTime);
            });
        },
        // 构造页面数据
        async getFileData() {
            // 全局折线图x轴处理
            const newTime = this.$getCurrentLocalTime();
            this.handleChartData(newTime);

            const chartDataG = this.globalData.details[1].info.chartData;
            const index = chartDataG.xData.indexOf(newTime);
            const pollTime = chartDataG.xData[index - 1] ? calculateTimeDifference(newTime, chartDataG.xData[index - 1]) : 1;

            // 调用接口前保存上一次数据
            this.lastGlobalProcessedCount = this.wholeData?.globalQueueInfo?.ProcessedCount;
            const lastData = [];
            Array.isArray(this.wholeData?.workQueueInfo) && this.wholeData.workQueueInfo.forEach(item => {
                lastData.push({ lastProcessedCount: item.ProcessedCount, ThreadId: item.ThreadId });
            });
            const { GetGlobalQueueInfo, GetTransactionNo, GetWorkQueueInfo, GetWorkQueueRequests } = await this.getAPi();
            this.wholeData = { globalQueueInfo: GetGlobalQueueInfo?.GlobalQueueInfo, workQueueInfo: GetWorkQueueInfo?.WorkQueueInfo };
            // 处理线程吞吐信息-表格保存每行上次执行次数
            Array.isArray(GetWorkQueueInfo?.WorkQueueInfo) && GetWorkQueueInfo.WorkQueueInfo.forEach(item => {
                lastData.forEach(value => {
                    if (value.ThreadId === item.ThreadId) item.lastProcessedCount = value.lastProcessedCount;
                });
            });
            // redo线程数据处理
            this.redoInfo.details[0].data = GetTransactionNo;
            // 全局队列信息数据处理
            this.handleGlobalData(GetGlobalQueueInfo?.GlobalQueueInfo, newTime, pollTime);
            // 线程处理吞吐表格数据处理
            this.handleTableData(GetWorkQueueInfo?.WorkQueueInfo, pollTime);
            // 工作线程数据处理
            this.handleWorkQueue(_.find(this.tableData, ['ThreadId', this.currentThreadId]), _.find(GetWorkQueueRequests?.GetWorkQueueRequests, ['ThreadId', this.currentThreadId]), newTime);
        },
        // 全局队列信息数据处理
        handleGlobalData(data, newTime, pollTime) {
            const yData = {
                ProcessedCount: (data?.ProcessedCount || data?.ProcessedCount === 0) ? formatNumber(data?.ProcessedCount) : undefined,
                ProcessedRate: (this.lastGlobalProcessedCount || this.lastGlobalProcessedCount === 0) ? (data?.ProcessedCount - this.lastGlobalProcessedCount) / pollTime : undefined
            };
            this.globalData.details[0].info = yData;
            this.globalData.details[2].info[0].info[0].text = `${data?.InUsedSizeKB || 0} / ${data?.QueueSizeKB || 0}`;
            this.globalData.details[2].info[0].info[0].scale = data?.QueueSizeKB ? String((data?.InUsedSizeKB / data?.QueueSizeKB) * 100) : '0';
            this.globalData.details[2].info[0].info[1].text = `${data?.QueueCount || 0} / ${data?.MaxHisQueueCount || 0}`;
            this.globalData.details[2].info[0].info[1].scale = data?.MaxHisQueueCount ? String((data?.QueueCount / data?.MaxHisQueueCount) * 100) : '0';
            this.globalData.details[2].info[1].info = data || {};
            // 处理全局队列折线图数据
            const chartData = this.globalData.details[1].info.chartData;
            const index = chartData.xData.lastIndexOf(newTime);
            Object.keys(chartData.data).forEach(item => {
                this.$set(chartData.data[item], index, yData?.[item]);
            });
        },
        // 线程处理吞吐表格数据处理
        handleTableData(data, pollTime) {
            this.title.label = `线程处理吞吐详情 - Top${this.selectTopValue}`;
            // 以ProcessedRate字段排序 - top
            this.tableData = data?.sort(compareObjArr('ProcessedRate'))?.slice(0, this.selectTopValue);

            this.currentThreadId = (this.currentThreadId || this.currentThreadId === 0) ? this.currentThreadId : this.tableData?.[0]?.ThreadId;
            // 判断选中线程号是否还存在
            const threadNo = _.find(this.tableData, ['ThreadId', this.currentThreadId])?.ThreadId || this.tableData?.[0]?.ThreadId;
            if (this.currentThreadId !== threadNo) this.cleanThreadIdChartData();
            this.currentThreadId = threadNo;

            Array.isArray(this.tableData) && this.tableData.forEach(item => {
                item._highlight = this.currentThreadId === item.ThreadId; // 表格刷新后默认选中
                // 吞吐计算公式:(Next.TotalExecNum - Last.TotalExecNum)/ (NextTime - LastTime)
                item.ProcessedRate = (item?.lastProcessedCount || item?.lastProcessedCount === 0) ? (item.ProcessedCount - item.lastProcessedCount) / pollTime : undefined ;
            });

        },
        // 工作线程数据处理
        handleWorkQueue(info, req, newTime) {
            if (!info || !req) return;
            this.threadIdData.title.label = `工作线程 - ${info?.ThreadId}`;
            this.threadIdData.title.slots[0].value = info?.Running;
            this.threadIdData.title.slots[0].poptipInfo.content = {
                Running: info?.Running,
                PollingLoops: info?.PollingLoops,
                SleepTimeUS: info?.SleepTimeUS
            };
            this.threadIdData.details[0].info = {
                ProcessedCount: formatNumber(info?.ProcessedCount),
                ProcessedRate: info?.ProcessedRate
            };
            this.threadIdData.details[2].info[0].info[0].text = `${info?.QueueCount || 0} / ${info?.MaxQueueSize || 0}`;
            this.threadIdData.details[2].info[0].info[0].scale = info?.MaxQueueSize ? String((info?.QueueCount / info?.MaxQueueSize) * 100) : '0';
            this.threadIdData.details[2].info[1].info = {
                MaxHisQueueCount: formatNumber(info?.MaxHisQueueCount)
            };
            // 获取队列前30个功能号列表
            const topFunctionNoList = [];
            // 功能号分组统计-遍历req.Requests获取每个功能号的个数
            const funcGroupCount = {};
            Array.isArray(req?.Requests) && req.Requests.forEach((item, idx) => {
                idx <= 30 && topFunctionNoList.push(item?.FunctionID);
                if (funcGroupCount[item?.FunctionID]) {
                    funcGroupCount[item?.FunctionID]++;
                } else {
                    funcGroupCount[item?.FunctionID] = 1;
                }
            });
            this.threadIdData.details[4].info.value = topFunctionNoList.join('、');
            const infoDicFunc = [{
                label: '功能号',
                key: 'FunctionNo'
            }];
            const infoFunc = { FunctionNo: '积压数' };
            Object.keys(funcGroupCount).forEach(item => {
                infoDicFunc.push({ label: item, key: item });
                infoFunc[item] = funcGroupCount[item];
            });
            this.threadIdData.details[5].infoDic = infoDicFunc;
            this.threadIdData.details[5].info = infoFunc;

            this.threadIdData.details[6].info = req?.Requests?.[0] || {};
            this.threadIdData.details[7].info = info?.Running ? info : {}; // 正在处理的功能号

            // 刷新折线图数据
            const chartNameList = [1, 3];
            const yData = {
                ProcessedCount: info?.ProcessedCount,
                ProcessedRate: info?.ProcessedRate,
                QueueCount: info?.QueueCount,
                MaxQueueSize: info?.MaxQueueSize,
                MaxHisQueueCount: info?.MaxHisQueueCount
            };
            this.handleThreadChartData(chartNameList, yData, newTime);
        },
        // 处理折线图数据
        handleThreadChartData(chartNameList, yData, newTime) {
            Array.isArray(chartNameList) && chartNameList.forEach(ele => {
                const chartData = this.threadIdData.details[ele].info.chartData;
                const index = chartData.xData.lastIndexOf(newTime);
                Object.keys(chartData.data).forEach(item => {
                    this.$set(chartData.data[item], index, yData?.[item]);
                });
            });
        },
        // 清理全局折线图数据
        cleanGlobalChartData() {
            this.globalData.details[1].info.chartData = {
                xData: [],
                data: {
                    ProcessedCount: [],
                    ProcessedRate: []
                }
            };
        },
        // 清理单线程折线图数据
        cleanThreadIdChartData() {
            this.threadIdData.details[1].info.chartData = {
                xData: [],
                data: {
                    ProcessedCount: [],
                    ProcessedRate: []
                }
            };
            this.threadIdData.details[3].info.chartData = {
                xData: [],
                data: {
                    QueueCount: [],
                    MaxQueueSize: [],
                    MaxHisQueueCount: []
                }
            };
        },
        // 表格切换下拉 TopN
        selectChange(value) {
            this.selectTopValue = value;
            this.getFileData();
        },
        // 表格切换选中行
        tableRowcheckedChange(currentRow) {
            this.currentThreadId = currentRow.ThreadId;
            this.cleanThreadIdChartData();
            this.getFileData();
        },
        // 接口请求
        async getAPi() {
            const data = {
                GetGlobalQueueInfo: {},
                GetWorkQueueRequests: {},
                GetTransactionNo: {},
                GetWorkQueueInfo: {}
            };
            const funNameList = ['GetGlobalQueueInfo', 'GetWorkQueueRequests', 'GetTransactionNo', 'GetWorkQueueInfo'];
            const param = [];
            funNameList.forEach(item => {
                param.push({
                    manageProxyIp: this.nodeData.manageProxyIp,
                    manageProxyPort: this.nodeData.manageProxyPort,
                    pluginName: 'ldp_bizproc',
                    funcName: item
                });
            });
            try {
                const res = await getManagerProxy(JSON.stringify(param));
                if (res.code === '200') {
                    !res.data?.[0]?.ErrorNo && Object.keys(res.data?.[0]).length && (data.GetGlobalQueueInfo = res.data[0]);
                    !res.data?.[1]?.ErrorNo && Object.keys(res.data?.[1]).length && (data.GetWorkQueueRequests = res.data[1]);
                    !res.data?.[2]?.ErrorNo && Object.keys(res.data?.[2]).length && (data.GetTransactionNo = res.data[2]);
                    !res.data?.[3]?.ErrorNo && Object.keys(res.data?.[3]).length && (data.GetWorkQueueInfo = res.data[3]);
                    return data;
                }
            } catch (err) {
                this.$emit('clear');
            }
        }
    }
};
</script>

<style lang="less" scoped>
/deep/ .process .process-bar-box {
    background: none;
}

/deep/ .info-process-text {
    span {
        overflow: hidden;
        white-space: pre-wrap;
        text-overflow: ellipsis;
        // stylelint-disable property-no-vendor-prefix,value-no-vendor-prefix
        display: -webkit-box;
        -webkit-box-orient: vertical;
        -webkit-line-clamp: 3;
    }
}

/deep/ .info-bar-detail {
    overflow: auto;
}
</style>
