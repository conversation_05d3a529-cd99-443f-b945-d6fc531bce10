<template>
    <div>
        <!-- 创建/编辑名单 -->
        <h-msg-box-safe
            v-model="modalData.status"
            :mask-closable="false"
            :title="modalData.title"
            :canDrag="false"
            width="600"
            maxHeight="400"
            @on-open="getCollections">
            <h-form
                ref="formValidate"
                :model="formValidate"
                :rules="ruleValidate"
                :label-width="120">
                <h-form-item label="名称" prop="ruleName">
                    <h-input v-model.trim="formValidate.ruleName" placeholder="最大输入长度20" :maxlength="20"></h-input>
                </h-form-item>
                <h-form-item label="绑定对象" prop="interest">
                    <div style="display: flex;">
                        <h-checkbox-group v-model="formValidate.interest">
                            <h-checkbox label="groups">功能号/资金账号组</h-checkbox>
                            <h-checkbox label="functionNos">功能号</h-checkbox>
                            <h-checkbox label="accountIds">资金账号</h-checkbox>
                        </h-checkbox-group>
                        <h-poptip placement="right" content="至少选择一个对象进行绑定，可多选" :transfer="true" positionFixed>
                            <h-icon name="feedback_fill" size="24" color="#999" style="cursor: pointer; margin-left: 6px;"></h-icon>
                        </h-poptip>
                    </div>
                </h-form-item>
                <h-form-item v-if="formValidate.interest.indexOf('groups') > -1" label="功能号/资金账号组" prop="groups">
                    <div style="display: flex;">
                        <h-select
                            v-model.trim="formValidate.groups"
                            placeholder="请选择功能号/资金账号组"
                            multiple filterable
                            :disabled="groupLoading"
                            @on-drop-change="handleDropChange">
                            <h-option v-for="item in groupList" :key="item.id" :value="item.id">{{ item.groupName }}</h-option>
                        </h-select>
                        <h-button type="text" @click="groupInfo.status = true">创建组</h-button>
                    </div>
                </h-form-item>
                <h-form-item v-if="formValidate.interest.indexOf('functionNos') > -1" label="功能号" prop="ruleMap.functionNos">
                    <div style="display: flex;">
                        <h-input v-model.trim="formValidate.ruleMap.functionNos" placeholder="最大输入长度1000" :maxlength="1000"></h-input>
                        <h-poptip placement="left" :transfer="true" positionFixed width="360">
                            <h-icon name="feedback_fill" size="24" color="#999" style="cursor: pointer; margin-left: 6px;"></h-icon>
                            <pre slot="content">{{`1.功能号可以包含数字和英文字符“?”号\n2.英文字符“?”号表示0~9任意一个数字，只能出现在最后\n（比如“22??”表示2200~2299这一段功能号）\n3.多个功能号使用英文字符“;”号分隔`}}</pre>
                        </h-poptip>
                    </div>
                </h-form-item>
                <h-form-item v-if="formValidate.interest.indexOf('accountIds') > -1" label="资金账号" prop="ruleMap.accountIds">
                    <div style="display: flex;">
                        <h-input v-model.trim="formValidate.ruleMap.accountIds" placeholder="最大输入长度1000" :maxlength="1000"></h-input>
                        <h-poptip placement="left" :transfer="true" positionFixed>
                            <h-icon name="feedback_fill" size="24" color="#999" style="cursor: pointer; margin-left: 6px;"></h-icon>
                            <pre slot="content">{{`1.需要输入具体账号\n2.只能是数字\n3.多个账号使用英文字符“;”号分隔`}}</pre>
                        </h-poptip>
                    </div>
                </h-form-item>
                <h-form-item label="分片号" prop="ruleMap.shardingNos">
                    <div style="display: flex;">
                        <h-input v-model.trim="formValidate.ruleMap.shardingNos" placeholder="最大输入长度1000" :maxlength="1000"></h-input>
                        <h-poptip placement="left" :transfer="true" positionFixed>
                            <h-icon name="feedback_fill" size="24" color="#999" style="cursor: pointer; margin-left: 6px;"></h-icon>
                            <pre slot="content">{{`1.分片号为空，代表所有分片\n2.多个分片号使用英文字符“;”号分隔`}}</pre>
                        </h-poptip>
                    </div>
                </h-form-item>
                <h-form-item v-if="modalData.ruleType === 'tps'" label="限流值" prop="ruleMap.fc">
                    <h-input v-model.number="formValidate.ruleMap.fc" type="int" style="margin-bottom: 10px;">
                        <span slot="append">条/秒</span>
                    </h-input>
                </h-form-item>
                <h-form-item label="是否启用" prop="enable">
                    <h-switch v-model="formValidate.enable" size="large">
                        <span slot="open">启用</span>
                        <span slot="close">停用</span>
                    </h-switch>
                </h-form-item>
            </h-form>
            <template v-slot:footer>
                <a-button type="primary" :loading="loading" @click="handleSubmit">提交</a-button>
                <a-button @click="modalData.status = false">取消</a-button>
            </template>
        </h-msg-box-safe>
        <add-or-edit-group-modal v-if="groupInfo.status" :modalInfo="groupInfo" :productId="productId" />
    </div>
</template>

<script>
import { getRuleGroupList, createOrUpdateConfig } from '@/api/brokerApi';
import addOrEditGroupModal from './addOrEditGroupModal.vue';
import aButton from '@/components/common/button/aButton';
export default {
    props: {
        productId: {
            type: String,
            default: ''
        },
        modalInfo: {
            type: Object,
            default: null
        }
    },
    data() {
        return {
            modalData: this.modalInfo,
            groupLoading: false,
            loading: false,
            formValidate: {
                id: '',
                ruleName: '',
                ruleMap: {
                    accountIds: '',
                    functionNos: '',
                    shardingNos: '',
                    fc: ''
                },
                enable: true,
                groups: [],
                interest: ['groups', 'functionNos', 'accountIds']
            },
            groupList: [],
            ruleValidate: {
                ruleName: [{ required: true, message: '名称不能为空', trigger: 'blur' }],
                interest: [
                    {
                        required: true,
                        type: 'array',
                        min: 1,
                        message: '至少选择一个绑定对象',
                        trigger: 'change'
                    }
                ],
                groups: [
                    {
                        required: true,
                        type: 'array',
                        min: 1,
                        message: '至少选择一个组',
                        trigger: 'change'
                    }
                ],
                'ruleMap.functionNos': [{ required: true, message: '功能号不能为空', trigger: 'blur' }],
                'ruleMap.accountIds': [{ required: true, message: '资金账号不能为空', trigger: 'blur' }],
                'ruleMap.fc': [{
                    required: true,
                    type: 'number',
                    min: 0,
                    max: 100000000000000000,
                    message: '请输入大于0小于100000000000000000的正整数',
                    trigger: 'blur'
                }]
            },
            groupInfo: {
                status: false,
                title: '创建组'
            }
        };
    },
    computed: {

    },
    methods: {
        getCollections() {
            this.getGropuList();
            this.$nextTick(async() => {
                if (this.modalInfo.type === 'edit') {
                    const data = this.modalInfo.data || {};
                    if (data?.ruleMap?.fc) {
                        data.ruleMap.fc = Number(data.ruleMap.fc);
                    }
                    this.formValidate.id = data?.id;
                    this.formValidate.interest = [];
                    this.formValidate.ruleName = data?.ruleName;
                    this.formValidate.enable = data?.enable;
                    this.formValidate.groups = (data?.groups || []).map(v => { return v.id; });
                    this.formValidate.ruleMap = { ...data?.ruleMap };
                    data?.ruleMap?.functionNos && this.formValidate.interest.push('functionNos');
                    data?.ruleMap?.accountIds && this.formValidate.interest.push('accountIds');
                    data?.groups.length && this.formValidate.interest.push('groups');
                }
            });
        },
        // 获取组列表数据
        async getGropuList() {
            const param = {
                productId: this.productId
            };
            try {
                this.groupLoading = true;
                const res = await getRuleGroupList(param);
                const list = res.data || [];
                this.groupList = list.map(v => { return { id: v.id, groupName: v.groupName }; });
            } catch (error) {
                console.error(error);
            }
            this.groupLoading = false;
        },
        handleSubmit() {
            this.$refs['formValidate'].validate(async (valid) => {
                if (valid) {
                    const param = {
                        productId: this.productId,
                        ruleMap: {
                            fc: this.formValidate.ruleMap.fc,
                            shardingNos: this.formValidate.ruleMap.shardingNos
                        },
                        ruleName: this.formValidate.ruleName,
                        id: this.formValidate.id,
                        enable: this.formValidate.enable,
                        ruleType: this.modalData.ruleType
                    };
                    this.formValidate.interest.forEach(ele => {
                        if (ele === 'groups') {
                            param.groups = this.formValidate.groups.map(id => { return { id }; });
                        } else {
                            param.ruleMap[ele] = this.formValidate.ruleMap[ele];
                        }
                    });
                    try {
                        this.loading = true;
                        const res = await createOrUpdateConfig(param);
                        if (res.success) {
                            this.$hMessage.success('操作成功!');
                            this.$emit('query');
                            this.modalData.status = false;
                        } else if (res.code.length === 8) {
                            this.$hMessage.error(res.message);
                        }
                    } catch (error) {
                        console.error(error);
                    }
                    this.loading = false;
                }
            });
        },
        handleDropChange(stu) {
            if (stu) this.getGropuList();
        }
    },
    components: { aButton, addOrEditGroupModal }
};
</script>

<style lang="less" scoped>
    /deep/ .h-modal-body {
        padding: 16px;
    }

    .alarm-title {
        display: flex;
        justify-content: space-between;
        border-bottom: 1px solid #ccc;
        padding-bottom: 10px;
    }

    .group-title {
        font-weight: bold;
        font-size: 14px;
    }

    .inline-html {
        display: inline-block;
        padding-bottom: 10px;
    }
</style>
