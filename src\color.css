:root {
    /* 背景色--从深到浅排列 */
    --main-color: #202637;  /* 参考色：main背景色 */
    --wrapper-color: #262d43; /* 参考色：input或者容器背景色 */
    --primary-color: #2c334a; /* 参考色：title背景色 */
    --box-color: #2f3956;
    --base-color: #383f59; /* 参考色：边框或者业务字段背景色 */
    --monitor-color: #323952; /* 参考色：总览性能指标背景色 */


    /* --------------自定义需修改----------------------------------------------- */
    --title-font-size: 15px;
    /* 通用字体、边框 */
    --font-size-base: 12px;
    --font-size: 14px;
    --font-weight: 400;

    /* 文本 */
    --font-color: #fff; /* 标题(主/次)/正文 */
    --font-color-dark: #333; /* 标题(主/次)/正文 */
    --font-body-color: #cacfd4; /* 辅助（详情lalel）/表体正文 */
    --font-tip-color: #9296a1; /* 提示/描述 */
    --font-disabled-color: #969797; /* 禁用 */
    --font-color-2: #777;
    --font-opacity-color: #cacfd4;

    /* 边框 */
    --border-radius: 4px;
    --border: 1px solid #485565; /* 边框 */
    --border-color: #485565;
    --base-border: 1px solid #31364a; /* 页面分割线 */
    --base-border-color: #31364a;
    --base-inner-border: 1px solid #444a60; /* 控件内部分割线 */
    --base-inner-border-color: #444a60;
    --border-radius: 4px;

    /* 信号色 */
    --link-color: #2d8de5;
    --link-color-selected: #1d6abf;
    --link-color-hover: #57acf2;
    --link-opacity-color: #1f3759; /* Feedback背景、常规面板hover.. */

    --success-color: #52c41a;
    --success-color-selected: #3b990c;
    --success-color-hover: #93f05d;
    --success-opacity-color: #eeffde;

    --warning-color: #ff9901;
    --warning-color-selected: #bd7101;
    --warning-color-hover: #ffb84f;
    --warning-opacity-color: #4e3b29;

    --error-color: #f5222d;
    --error-color-selected: #c41218;
    --error-color-hover: #fe5e66;
    --error-opacity-color: #4c2132;

    /* tag颜色 */
    --tag-icon-color: #33394e;

    /* 图标 */
    --icon-color: #999;
    --icon-hover: var(--link-color-hover);
    --icon-press-down: var(--link-color-selected);
    --icon-disabled-color: var(--font-disabled-color);
    --table-icon-color: var(--font-body-color); /* 表格内图标 */
    --input-icon-color: var(--font-tip-color); /* 输入框内图标 */
    --icon-selected: var(----link-color);

    /* modal遮罩层颜色 */
    --modal-mask-color: rgba(0, 0, 0, 0.5);

    /* 表格 */
    --table-bg-color: var(--wrapper-color);
    --table-border-color: var(--wrapper-color);
    --table-border: 1px solid  var(--wrapper-color);
    --table-button-hover-bgcolor: var(--table-icon-color);

    /* 输入框 */
    --input-bg-color: var(--wrapper-color);

    /* 按钮 */
    --button-bg-color: var(--primary-color);
    --button-hover-bgcolor: var(--wrapper-color);
    --btn-disabled-color: #33394e;

    /* poptip */
    --poptip-bg-color: rgb(104 110 121);
    --poptip-title-bgcolor: var(--base-color);
    --poptip-line-color: rgba(255, 255, 255, 0.15);
}
