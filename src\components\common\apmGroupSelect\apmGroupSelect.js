/*
 * @Description: 复合下拉框
 * @Author: <PERSON><PERSON>
 * @Date: 2023-08-29 14:02:31
 * @LastEditTime: 2023-09-11 14:15:55
 * @LastEditors: <PERSON>ale Ying
 */
import './apmGroupSelect.less';
export default {
    name: 'apmGroupSelect',
    props: {
        placement: {
            type: String,
            default: 'bottom'
        },
        placeholder: {
            type: String,
            default: '请输入'
        },
        subheading: {
            type: String,
            default: ''
        },
        groupList: {
            type: Array,
            default: () => []
        },
        tagList: {
            type: Array,
            default: () => []
        }
    },
    data() {
        return {
            value: '',
            loading: false
        };
    },
    methods: {
        // 创建分组菜单子项
        generateOptionGroup(group) {
            return <h-option-group label={group.label}>
                {
                    group.optionList.map(item => {
                        return <h-option
                            key={item.value}
                            value={item.value}
                        >
                            {item.label}
                        </h-option>;
                    })
                }
            </h-option-group>;
        },
        // 监听下拉值变化
        handleValueChange(e) {
            this.$emit('change', e);
        },
        // 动态远程搜索
        remoteMethod(e) {
            this.$emit('remote', e);
        },
        // 删除tag标签
        handleTagClose(e) {
            this.$emit('removeTag', e);
        }
    },
    render() {
        return <h-select
            class="apm-group-select"
            v-model={this.value}
            placement={this.placement}
            placeholder={this.placeholder}
            filterable
            remote
            remote-method={this.remoteMethod}
            remoteIcon="search"
            loading={this.loading}
            v-on:on-change={(e) => this.handleValueChange(e)}
        >
            {
                this.groupList.map(item => {
                    return this.generateOptionGroup(item);
                })
            }
            <li>
                <p class='line-sub'><span class="title-sub">{this.subheading}</span> <h-icon class="title-trash" name="trash"></h-icon></p>
                <div class="box-tag">
                    {
                        this.tagList.map(item => {
                            return <h-tag closable v-on:on-close={() => { this.handleTagClose(item); }}>{item}</h-tag>;
                        })
                    }
                </div>
            </li>
        </h-select>;
    }
};
