/*
 * @Description: 公共组件 form表单
 * @Author: <PERSON><PERSON>
 * @Date: 2022-11-29 14:53:36
 * @LastEditTime: 2023-03-20 17:56:33
 * @LastEditors: <PERSON><PERSON>
 */
import aForm from '@/components/common/form/aForm';
import aTable from '@/components/common/table/aTable';
import aButton from '@/components/common/button/aButton';
import aTitle from '@/components/common/title/aTitle';
import './bestTable.less';
export default {
    name: 'rcmNormalTitleTable',
    props: {
        title: {
            type: String,
            default: ''
        },
        loading: {
            type: Boolean,
            default: false
        },
        formItems: {
            type: Array,
            default: []
        },
        tableData: {
            type: Array,
            default: []
        },
        columns: {
            type: Array,
            default: []
        },
        hasPage: {
            type: Boolean,
            default: true
        },
        total: {
            type: Number,
            default: 0
        },
        hasSetTableColumns: {
            type: Boolean,
            default: true
        },
        showTitle: {
            type: Boolean,
            default: false
        }
    },
    data() {
        return {
            formStatus: true,
            visible: false,
            styles: {
                height: 'calc(100% - 55px)',
                paddingBottom: '53px'
            },
            checkList: [],
            currentCheckedKeys: [],
            columnData: [],
            tableHeight: 0
        };
    },
    mounted() {
        this.$_init();
        window.addEventListener('resize', this.fetTableHeight);
        this.$nextTick(() => {
            this.fetTableHeight();
        });
    },
    beforeDestroy() {
        window.removeEventListener('resize', this.fetTableHeight);
    },
    methods: {
        resetHeight() {
            return new Promise((resolve, reject) => {
                this.tableHeight = 0;
                resolve();
            });
        },
        // 设置table高度
        fetTableHeight() {
            this.resetHeight().then(res => {
                const height = this.formStatus ? Math.ceil(this.formItems.length / 3) * 45 : 0;
                this.tableHeight = this.$refs['table-box'].getBoundingClientRect().height - height - 150;
            });
        },
        $_clearCurrentCheckedKeys(){
            this.currentCheckedKeys = [];
        },
        $_init(){
            this.checkList = [];
            this.columnData = [];
            if (this.currentCheckedKeys?.length){
                this.checkList = [...this.currentCheckedKeys];
                this.columns.forEach(element => {
                    if (this.currentCheckedKeys.includes(element.key)){
                        this.columnData.push(element);
                    }
                });
            } else {
                this.columns.forEach(element => {
                    this.checkList.push(element.key);
                    this.columnData.push(element);
                });
            }
        },
        changeFormStatus(){
            this.formStatus = !this.formStatus;
            this.fetTableHeight();
        },
        setTableColumns() {
            this.visible = true;
        },
        $_handleCheckbox(value) {
            this.currentCheckedKeys = [...value];
            this.columnData = [];
            this.columns.forEach(ele => {
                if (value.includes(ele.key)){
                    this.columnData.push(ele);
                }
            });
            this.columnData.forEach(ele => {
                ele.hiddenCol = !value.includes(ele.key);
            });
        },
        $_handleQuery() {
            const param = this.$refs['forms'].query();
            if (!param) return;
            const pageParam = this.hasPage ? this.$refs['table'].getPageData() : {};
            this.$emit('query', { ...param, ...pageParam });
        },
        $_handleReset() {
            this.$refs['forms'].reset();
        },
        handleClickQuery() {
            this.$refs['table'].resetPage();
            this.$_handleQuery();
        },
        $_handleResetPageDataAndQuery() {
            this.$refs['table'].resetPage();
            this.$refs['table'].resetPageSize();
            this.$_handleQuery();
        },
        $_handleClear(){
            const param = this.$refs['forms'].query();
            if (!param) return;
            const pageParam = this.hasPage ? this.$refs['table'].getPageData() : {};
            this.$emit('clear', { ...param, ...pageParam });
        },
        $_handleCreate(){
            this.$emit('create');
        },
        $_addTag(){
            this.$emit('add-tag');
        },
        $_delTag(){
            this.$emit('del-tag');
        },
        $_tableSelection(selection){
            this.$emit('selection', selection);
        },
        $_echoFormData(param){
            this.$refs['forms'].echoFormData(param);
        }
    },
    components: { aForm, aTable, aButton, aTitle },
    render() {
        return <div class="best-table">
            <a-title title={this.title}>
                <slot>
                    <div class="slot-box">
                        <a-button
                            type="primary"
                            onClick={this.handleClickQuery}
                            disabled={this.loading}>查询</a-button>
                        <a-button type="dark" onClick={this.$_handleReset}>重置</a-button>
                        {this.hasSetTableColumns && <a-button type="dark" onClick={this.setTableColumns}>配置表格</a-button>}
                        <a-button type="dark" onClick={this.changeFormStatus}>
                            {this.formStatus ? '收起搜索' : '展开搜索'}
                            <h-icon name={this.formStatus ? 'packup' : 'unfold'}></h-icon></a-button>
                        {this.$slots.default}
                    </div>
                </slot>
            </a-title>
            <div class="form-box" style={{ height: this.formStatus ? Math.ceil(this.formItems.length / 3) * 45 + 'px' : 0, overflow: this.formStatus ?  'visible' : 'hidden'  }}>
                <a-form ref="forms" formItems={this.formItems} />
            </div>
            <a-title title="查询结果">
                <slot>
                    <div class="slot-box">
                        {this.$slots.btns}
                        <a-button type="primary" onClick={this.$_handleCreate}>创建</a-button>
                        <a-button type="dark" onClick={this.$_addTag}>添加标签</a-button>
                        <a-button type="dark" onClick={this.$_delTag}>删除标签</a-button>
                        <a-button type="dark" onClick={this.$_handleClear}>批量删除</a-button>
                    </div>
                </slot>
            </a-title>
            <div ref="table-box" class='table-box'>
                <a-table
                    ref="table"
                    tableData={this.tableData}
                    height={this.tableHeight}
                    columns={this.columnData}
                    hasPage={this.hasPage}
                    total={this.total}
                    loading={this.loading}
                    showTitle={this.showTitle}
                    v-on:query={this.$_handleQuery}
                    v-on:selection={this.$_tableSelection}/>
            </div>
            <h-drawer
                v-model={this.visible}
                width="280"
                title="表格配置"
                styles={this.styles}>
                <h-checkbox-group
                    v-model={this.checkList}
                    onInput={this.$_handleCheckbox}
                    vertical>
                    {
                        this.columns.map(item => {
                            return <h-checkbox key={item.key}
                                label={item.key}
                                disabled={item.disabled || (this.checkList.length <= 1 && this.checkList.includes(item.key))} style="display: flex; align-items: center;">
                                <span style="white-space:nowrap;">{item.title}</span>
                            </h-checkbox>;
                        })
                    }
                </h-checkbox-group>
            </h-drawer>
            <style jsx>
                {
                    `    
                        .slot-box {
                            float: right;
                            padding-right: 10px
                        }
                        .slot-box  .h-btn {
                            margin-left:10px;
                        }
                    `
                }
            </style>
        </div>;
    }
};
