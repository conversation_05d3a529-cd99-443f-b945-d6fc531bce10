import * as echarts from 'echarts';
import _ from 'lodash';
import obsTitle from '@/components/common/title/obsTitle';
let myChart = null;
export default {
    name: 'commonTopo',
    components: { obsTitle },
    props: {
        title: {
            type: Object,
            default: () => {}
        },
        nodes: {
            type: Array,
            default: () =>  []
        },
        edges: {
            type: Array,
            default: () => []
        },
        nodeStyle: {
            type: Object,
            default: () => {}
        },
        edgeStyle: {
            type: Object,
            default: () => {}
        },
        // 类型字典
        categoryDic: {
            type: Object,
            default: () => {}
        },
        // 是否自定义Tooltip
        customTooltip: {
            type: Boolean,
            default: false
        }
    },
    data() {
        return {
        };
    },
    mounted() {
        this.$refs?.chart && this.init();
        window.addEventListener('resize', this.resize, false);
    },
    methods: {
        init() {
            myChart = echarts.init(this.$refs.chart, '#262B40');
            const nodes = [], links = [], categories = [];
            this.nodes.forEach(ele => {
                const category = this.categoryDic?.[ele?.category] || ele?.category;
                nodes.push({
                    id: ele.id,
                    name: ele.name,
                    symbolSize: 20,
                    category: category,
                    info: ele.info,
                    itemStyle: {
                        color: this.nodeStyle?.[ele?.category] || '#7DFCB2'
                    }
                });
                if (categories.indexOf(category) === -1) {
                    categories.push({
                        name: category,
                        itemStyle: {
                            color: this.nodeStyle?.[ele?.category] || '#7DFCB2'
                        }
                    });
                }
            });

            this.edges.forEach(ele => {
                links.push({
                    source: ele.from,
                    target: ele.to,
                    info: ele.info,
                    lineStyle: {
                        color: this.edgeStyle?.[ele?.connectStatus]?.color || '#7DFCB2',
                        type: this.edgeStyle?.[ele?.connectStatus]?.type || 'solid'
                    }
                });
            });

            const graph = {
                links,
                nodes,
                categories
            };

            const that = this;
            const option = {
                tooltip: that.customTooltip ? {
                    trigger: 'item',
                    enterable: true,
                    confine: true,
                    backgroundColor: 'rgba(88,94,106,0.70)',
                    textStyle: {
                        color: '#fff'
                    },
                    triggerOn: 'click',
                    formatter: function(params) {
                        let html = '';
                        that.$emit('set-tooltip', params, val => {
                            html = val;
                        });
                        return html;
                    }
                } : {},
                legend: [
                    {
                        data: graph.categories.map(function (a) {
                            return a.name;
                        }),
                        textStyle: {
                            color: '#fff'
                        }
                    }
                ],
                animationDurationUpdate: 1500,
                animationEasingUpdate: 'quinticInOut',
                series: [
                    {
                        name: '',
                        type: 'graph',
                        layout: 'circular',
                        circular: {
                            rotateLabel: false
                        },
                        data: graph.nodes,
                        links: graph.links,
                        categories: graph.categories,
                        roam: true,
                        edgeSymbol: ['', 'arrow'],
                        edgeSymbolSize: 8,
                        label: {
                            show: true,
                            width: 80,
                            overflow: 'break',
                            // rotate: 10,
                            color: '#fff',
                            position: 'bottom',
                            formatter: '{b}'
                        },
                        labelLayout: {
                            draggable: true
                        },
                        lineStyle: {
                            color: 'source',
                            curveness: 0.3
                        },
                        emphasis: {
                            focus: 'adjacency',
                            lineStyle: {
                                width: 10
                            }
                        }
                    }
                ]
            };
            myChart.setOption(option);
        },
        // title
        handleButtonClick(key) {
            this.$emit('button-click', key);
        },
        handleSelectChange(val, key) {
            val && this.$emit('select-change', val, key);
        },
        setSelectVal(key, val) {
            this.$refs['obs-title'].setSelectVal(key, val);
        },
        getSelectVal(key) {
            return this.$refs['obs-title'].getSelectVal(key);
        },
        setCheckVals(key, val) {
            this.$refs['obs-title'].setCheckVals(key, val);
        },
        getCheckVals(key) {
            return this.$refs['obs-title'].getCheckVals(key);
        },
        resize: _.throttle(() => {
            myChart && myChart.resize();
        }, 500)
    },
    computed: {
        chartStyle() {
            const style = this.title ? {
                padding: '15px 5px 0',
                width: '100%',
                height: `calc(100% - 50px)`
            } : {
                width: '100%',
                height: '100%'
            };
            return style;
        }
    },
    render() {
        return <div style="width: 100%; height: 100%">
            {this.title && <obs-title ref='obs-title' title={this.title}
                v-on:button-click={this.handleButtonClick}
                v-on:select-change={this.handleSelectChange}/>
            }
            <div ref='chart' style={this.chartStyle}></div>
        </div>;
    },
    beforeDestroy() {
        window.removeEventListener('resize', this.resize);
    }
};
