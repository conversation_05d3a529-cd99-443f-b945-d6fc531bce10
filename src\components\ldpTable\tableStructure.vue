<template>
    <div class="tab-box">
        <a-title title="数据表信息"></a-title>
        <div class="product-info">
            <p><span>数据表名：</span>{{ tableMetadata.tableName }}</p>
            <p><span>数据表扩展属性：</span>{{ tableMetadata.expandAttributes.join(',') }}
                <span>
                    <h-icon name="t-b-modify" @on-click="modifyTableInfo(0)"></h-icon>
                </span>
            </p>
            <p><span>数据表说明：</span>{{ tableMetadata.tableDescribe }}
                <span>
                    <h-icon name="t-b-modify" @on-click="modifyTableInfo(1)"></h-icon>
                </span>
            </p>
        </div>
        <a-title title="数据表字段">
            <template v-slot>
                <div style="float: right; margin-right: 8px;">
                    <h-checkbox
                        v-model="structDeployDisabled"
                        @on-change="setStructVisibleDisable">
                        禁用struct字段可见属性配置
                    </h-checkbox>
                </div>
            </template>
        </a-title>
        <div ref="table-box" class="table-info">
            <tree-table
                ref="treeTable"
                :key="tKey"
                class="struct-info"
                :columns="columns"
                :treeData="data"
                :height="tableHeight">
            </tree-table>
        </div>

        <!-- 修改内存表属性、说明弹窗 -->
        <manage-table-info-modal
            v-if="tableInfo.status"
            :modalInfo="tableInfo"
            @updata-info="updataTableInfo">
        </manage-table-info-modal>
    </div>
</template>

<script>
import _ from 'lodash';
import { loopAllData } from '@/utils/utils';
import aTitle from '@/components/common/title/aTitle';
import treeTable from '@/components/common/treeTable/treeTable';
import manageTableInfoModal from '@/components/ldpTable/modal/manageTableInfoModal.vue';

export default {
    name: 'TableStructure',
    props: {
        databaseName: {
            type: String,
            default: ''
        },
        endpointInfo: {
            type: Object,
            default: () => {}
        },
        boxInfo: {
            tableName: {
                type: String,
                default: ''
            },
            expandAttributes: {
                type: Array,
                default: []
            },
            fields: {
                type: Array,
                default: []
            },
            tableDescribe: {
                type: String,
                default: ''
            }
        }
    },
    data() {
        return {
            resizeObserver: null,
            tableHeight: 300,
            tableInfo: {
                status: false,
                expandAttributes: '',
                tableDescribe: '-',
                type: 0
            },
            tableMetadata: this.boxInfo,
            saveMetadata: {},
            columns: [
                {
                    title: '字段名',
                    key: 'name',
                    render: (h, params) => {
                        return h('span', {
                            attrs: {
                                title: params.row.name
                            }
                        }, params.row.name);
                    }
                },
                {
                    title: '数据类型',
                    key: 'type'
                },
                {
                    title: '是否可修改',
                    key: 'readOnly',
                    render: (h, param) => {
                        return h('div', !param.row.readOnly);
                    }
                },
                {
                    title: '查询结果可见',
                    key: 'visible',
                    renderHeader: (h, col) => {
                        return h(
                            'checkbox',
                            {
                                props: {
                                    value: this.getInitChecked('visible', 'disVisible')
                                },
                                on: {
                                    'on-change': (val) => {
                                        this.onChangeAllCol(val, 'visible', 'disVisible');
                                    }
                                }
                            }, '查询结果可见'
                        );
                    },
                    render: (h, param) => {
                        const defaultCheckbox = h(
                            'checkbox',
                            {
                                props: {
                                    value: param.row.visible,
                                    disabled: param.row.disVisible
                                },
                                on: {
                                    'on-change': (val) => {
                                        param.row.visible = val;
                                        this.saveFiledAttrbute(param.row.path, 'visible', val);
                                    }
                                }
                            }
                        );
                        if (!param.row.children?.length) {
                            return defaultCheckbox;
                        }
                        const disabledKey = 'disVisible';
                        const childHasCanCheck = this.checkChildHasCanCheck(param.row, disabledKey);
                        return h('div', [
                            defaultCheckbox,
                            h(
                                'checkbox',
                                {
                                    props: {
                                        value: this.checkChildHasChecked(param.row.children, 'visible', disabledKey) && childHasCanCheck,
                                        disabled: !childHasCanCheck
                                    },
                                    style: {
                                        'margin-left': '20px'
                                    },
                                    on: {
                                        'on-change': (val) => {
                                            this.callOnSelectAllChild({ row: param.row, attr: 'visible', value: val, disabledKey });
                                        }
                                    }
                                }, '全选字段'
                            )
                        ]);
                    }
                },
                {
                    title: '可作为查询条件',
                    key: 'hasCondition',
                    renderHeader: (h, col) => {
                        return h(
                            'checkbox',
                            {
                                props: {
                                    value: this.getInitChecked('hasCondition', 'disFilter')
                                },
                                on: {
                                    'on-change': (val) => {
                                        this.onChangeAllCol(val, 'hasCondition', 'disFilter');
                                    }
                                }
                            }, '可作为查询条件'
                        );
                    },
                    render: (h, param) => {
                        const disabledKey = 'disFilter';
                        const defaultCheckbox = h(
                            'checkbox',
                            {
                                props: {
                                    value: param.row.hasCondition,
                                    disabled: param.row.disFilter
                                },
                                on: {
                                    'on-change': (val) => {
                                        param.row.hasCondition = val;
                                        this.saveFiledAttrbute(param.row.path, 'hasCondition', val);
                                    }
                                }
                            }
                        );
                        if (!param.row.children?.length) {
                            return defaultCheckbox;
                        }
                        const childHasCanCheck = this.checkChildHasCanCheck(param.row, disabledKey);
                        return h('div', [
                            defaultCheckbox,
                            h(
                                'checkbox',
                                {
                                    props: {
                                        value: this.checkChildHasChecked(param.row.children, 'hasCondition', disabledKey) && childHasCanCheck,
                                        disabled: !childHasCanCheck
                                    },
                                    style: {
                                        'margin-left': '20px'
                                    },
                                    on: {
                                        'on-change': (val) => {
                                            this.callOnSelectAllChild({ row: param.row, attr: 'hasCondition', value: val, disabledKey });
                                        }
                                    }
                                }, '全选字段'
                            )
                        ]);
                    }
                }
            ],
            data: [],
            structDeployDisabled: true,
            tKey: 1
        };
    },
    async mounted() {
        // 刷新数据
        await this.initData();
        this.resizeObserver = new ResizeObserver(entries => {
            this.onResize();
        });
        this.resizeObserver.observe(this.$refs['table-box']);
    },
    beforeDestroy() {
        this.resizeObserver.disconnect(this.$refs['table-box']);
    },
    methods: {
        // tree-table宽度调整节流函数
        onResize: _.throttle(function () {
            this.$nextTick(() => {
                this.tableHeight = this.$refs['table-box']?.offsetHeight;
            });
            window.dispatchEvent(new Event('resize'));
        }, 200),
        initData() {
            this.onResize();
            this.tableMetadata = this.boxInfo;
            this.data = this.addDisabledAttribute(this.tableMetadata.fields); // 数据表字段数据构建
        },
        // 嵌套json添加是否禁用可见和可筛选属性
        addDisabledAttribute(json, disFilter) {
            if (Array.isArray(json)) {
                json.forEach(item => this.addDisabledAttribute(item));
            } else if (json instanceof Object) {
                if (disFilter) {
                    json.disFilter = true;
                    json.disVisible = true;
                    Array.isArray(json.children) && json.children.forEach(item => this.addDisabledAttribute(item, true));
                } else if (json.type === 'array') {
                    json.disFilter = true;
                    Array.isArray(json.children) && json.children.forEach(item => this.addDisabledAttribute(item, true));
                } else if (json.type === 'struct') {
                    json.disVisible = this.structDeployDisabled;
                    json.disFilter = true;
                    Array.isArray(json.children) && json.children.forEach(item => this.addDisabledAttribute(item));
                }
            }
            return json;
        },
        // 遍历树保存可见和可筛选属性
        saveTreeAttrbute(fields) {
            let tableFieldConfig = JSON.parse(localStorage.getItem('tableFieldConfig'));
            const currentConfig = {};
            loopAllData(fields, (data) => {
                currentConfig[data.path] = {};
                currentConfig[data.path].visible = data?.visible;
                currentConfig[data.path].hasCondition = data?.hasCondition;
            });
            if (!tableFieldConfig) tableFieldConfig = {};
            tableFieldConfig[`${this.databaseName}.${this.tableMetadata.tableName}`] = currentConfig;
            localStorage.setItem('tableFieldConfig', JSON.stringify(tableFieldConfig));
        },
        // 配置struct字段visible属性禁用状态
        setStructVisibleDisable() {
            this.data = this.setStructAttribute(this.data, this.structDeployDisabled);
            // 保存配置-上述方法会取消掉原勾选的struct字段，需重新保存配置
            this.saveTreeAttrbute(this.data);
            this.tKey++;
        },
        // 遍历设置struct字段visible属性
        setStructAttribute(json, status) {
            if (Array.isArray(json)) {
                json.forEach(item => this.setStructAttribute(item, status));
            } else if (json instanceof Object) {
                if (json.type === 'struct') {
                    json.disVisible = status;
                    status && (json.visible = false);
                    json.children.forEach(item => this.setStructAttribute(item, status));
                }
            }
            return json;
        },
        // 保存可见和可筛选属性
        saveFiledAttrbute(path, attr, value) {
            const tableFieldConfig = JSON.parse(localStorage.getItem('tableFieldConfig'));
            tableFieldConfig[`${this.databaseName}.${this.tableMetadata.tableName}`][path][attr] = value;
            localStorage.setItem('tableFieldConfig', JSON.stringify(tableFieldConfig));
            this.$emit('updataMetadata', this.tableMetadata);
        },
        /**
         * 全选子级
         */
        onSelectAllChild({ row, attr, value, disabledKey }) {
            row.children.forEach(item => {
                if (!item[disabledKey]) {
                    item[attr] = value;
                    const tableFieldConfig = JSON.parse(localStorage.getItem('tableFieldConfig'));
                    tableFieldConfig[`${this.databaseName}.${this.tableMetadata.tableName}`][item.id][attr] = value;
                    localStorage.setItem('tableFieldConfig', JSON.stringify(tableFieldConfig));
                    item[attr] = value;
                }
                if (item.children?.length) {
                    this.onSelectAllChild({ row: item, attr, value, disabledKey });
                }
            });
        },
        /**
         * 调用检查子级
        */
        callOnSelectAllChild(arg) {
            this.onSelectAllChild(arg);
            this.$emit('updataMetadata', this.tableMetadata);
        },
        /**
         * 检查自己是否包含能选的
         */
        checkChildHasCanCheck(row, disabledKey) {
            if (!row.children?.length) return false;
            return !!row.children.find(item => !item[disabledKey]);
        },
        /**
         * 获取初始全选状态
         */
        getInitChecked(attr, disabledKey) {
            return this.checkChildHasChecked(this.data, attr, disabledKey);
        },
        /**
         * 检查child是否已经全部选中(非disabled的子项)
         */
        checkChildHasChecked(children, attr, disabledKey) {
            const stack = [...children];
            while (stack.length) {
                const node = stack.shift();
                if (!node[disabledKey] && node[attr] === false) {
                    return false;
                }
                if (node.children?.length) {
                    stack.push(...node.children);
                }
            }
            return true;
        },
        /**
         * 切换全选表头
         */
        onChangeAllCol(value, attr, disabledKey) {
            this.loopUpdateKey({ children: this.data, value, attr, disabledKey });
            this.$emit('updataMetadata', this.tableMetadata);
        },
        /**
         * 递归更新字段
         */
        loopUpdateKey({ children, value, attr, disabledKey }) {
            children.forEach(item => {
                const hasChildren = !!item.children?.length;
                const disabled = item[disabledKey] === true;
                if (disabled && !hasChildren) {
                    return;
                }
                if (!disabled) {
                    const tableFieldConfig = JSON.parse(localStorage.getItem('tableFieldConfig'));
                    tableFieldConfig[`${this.databaseName}.${this.tableMetadata.tableName}`][item.id][attr] = value;
                    localStorage.setItem('tableFieldConfig', JSON.stringify(tableFieldConfig));
                    item[attr] = value;
                }
                if (item.children?.length) {
                    this.loopUpdateKey({ children: item.children, value, attr, disabledKey });
                }
            });
        },
        // 取消未保存修改
        cancelModify() {
            this.tableMetadata = _.cloneDeep(this.boxInfo);
            this.data = this.tableMetadata.fields;
        },
        // 开启修改属性和表说明弹窗
        modifyTableInfo(type) {
            this.tableInfo.status = true;
            this.tableInfo.expandAttributes = this.tableMetadata.expandAttributes;
            this.tableInfo.tableDescribe = this.tableMetadata.tableDescribe;
            this.tableInfo.endpointInfo = this.endpointInfo;
            this.tableInfo.databaseName = this.databaseName;
            this.tableInfo.tableName = this.tableMetadata.tableName;
            this.tableInfo.type = type;
        },
        // 更新修改后的表信息
        updataTableInfo(data) {
            this.tableMetadata.expandAttributes = data.expandAttributes || [];
            this.tableMetadata.tableDescribe = data.tableDescribe;
        }
    },
    components: { aTitle, treeTable, manageTableInfoModal }
};
</script>

<style lang="less" scoped>
.tab-box {
    display: flex;
    position: relative;
    width: 100%;
    height: 100% !important;
    flex-direction: column;

    .btn-add {
        position: absolute;
        right: 6px;
        top: 5px;
    }

    .product-info {
        width: 100%;
        height: auto;
        padding: 10px 0 20px;
        word-wrap: break-word;

        & > p {
            display: inline-block;
            color: var(--font-color);
            padding-top: 10px;
            padding-right: 20px;
            line-height: 15px;

            & > span {
                padding-left: 13px;
                color: var(--font-opacity-color);
            }
        }
    }

    .table-info {
        position: relative;
        width: 100%;
        height: ~"calc(100% - 155px)";

        .footer-btn {
            position: absolute;
            bottom: 10px;
            left: ~"calc(50% - 60px)";
        }
    }
}
</style>
