<template>
    <div ref="tab-box" class="tab-box">
        <a-loading v-if="loading"></a-loading>
        <div v-else class="tab-child-box">
            <obs-table ref="table" :height="tableHeight" :title="title" showTitle :tableData="tableData" :columns="columns" highlightRow rowSelectOnly :hasPage="false" @on-current-change="tableRowcheckedChange" />
            <description-bar :data="offerTypeDes"></description-bar>
        </div>
    </div>
</template>

<script>
import aLoading from '@/components/common/loading/aLoading';
import obsTable from '@/components/common/obsTable/obsTable';
import descriptionBar from '@/components/common/description/descriptionBar';
import { getManagerProxy } from '@/api/mcApi';
export default {
    props: {
        nodeData: {
            type: Object,
            default: () => { }
        },
        pollTime: {
            type: Number,
            default: 0
        }
    },
    components: { aLoading, obsTable, descriptionBar },
    data() {
        return {
            loading: true,
            title: {
                label: '报盘组统计信息'
            },
            tableData: [],
            columns: [
                {
                    title: 'OfferType',
                    key: 'OfferType',
                    ellipsis: true
                },
                {
                    title: 'OnMsgExecNum',
                    key: 'OnMsgExecNum',
                    ellipsis: true
                },
                {
                    title: 'OnLpcExecNum',
                    key: 'OnLpcExecNum',
                    ellipsis: true
                }
            ],
            offerTypeDes: {
                title: {
                    label: {
                        labelDic: [{ key: 'OfferType', label: '报盘组' }],
                        labelInfo: {
                            OfferType: '-'
                        }
                    },
                    slots: []
                },
                details: [
                    {
                        dataDic: [
                            {
                                label: 'OfferType',
                                key: 'OfferType',
                                span: 5
                            },
                            {
                                label: 'BizVersion',
                                key: 'BizVersion',
                                span: 6
                            },
                            {
                                label: 'LibName',
                                key: 'LibName',
                                span: 6
                            },
                            {
                                label: 'Version',
                                key: 'Version',
                                span: 7
                            }
                        ],
                        data: {
                            OfferType: '-',
                            BizVersion: '-',
                            LibName: '-',
                            Version: '-'
                        }
                    }
                ]
            },
            currentRow: {},
            tableHeight: 0
        };
    },
    mounted() {
        window.addEventListener('resize', this.fetTableHeight);
    },
    beforeDestroy() {
        window.removeEventListener('resize', this.fetTableHeight);
    },
    methods: {
        async initData() {
            this.loading = true;
            this.currentRow = {};
            await this.getFileData();
            this.loading = false;
        },
        // 设置table高度
        fetTableHeight() {
            this.tableHeight = this.$refs['tab-box']?.offsetHeight - 180;
        },
        // 表格切换选中行
        tableRowcheckedChange(currentRow) {
            if (currentRow?.OfferType !== this.currentRow?.OfferType){
                this.cleanOfferTypeData();
            }
            this.currentRow = currentRow;
            this.tableData.forEach(item => {
                item._highlight = item?.OfferType === this.currentRow?.OfferType; // 表格刷新后默认选中
            });
            this.setOfferTypeData(currentRow);
        },
        setOfferTypeData(currentRow){
            this.offerTypeDes.title.label.labelInfo.OfferType = currentRow?.OfferType;
            this.offerTypeDes.details[0].data = { ...currentRow };
        },
        cleanOfferTypeData(){
            this.offerTypeDes.title.label.labelInfo.OfferType = '-';
            this.offerTypeDes.details[0].data = {};
        },
        async getFileData() {
            const { getOfferGroupInfo } = await this.getAPi();
            this.tableData = [...getOfferGroupInfo];
            if (this.tableData.length){
                const row = this.tableData.find(v => v?.OfferType === this.currentRow?.OfferType);
                this.tableRowcheckedChange(Object.keys(this.currentRow).length && row ? row : this.tableData?.[0]);
            } else {
                this.cleanOfferTypeData();
            }
            this.fetTableHeight();
        },
        // 接口请求
        async getAPi() {
            const data = {
                getOfferGroupInfo: []
            };
            const param = [
                {
                    manageProxyIp: this.nodeData.ip,
                    manageProxyPort: this.nodeData.manageProxyPort,
                    pluginName: 'ldp_offerproc',
                    funcName: 'GetOfferGroupInfo'
                }
            ];
            try {
                const res = await getManagerProxy(JSON.stringify(param));
                if (res.code === '200') {
                    res.data?.[0]?.OfferGroupInfo?.length && (data.getOfferGroupInfo = res.data[0].OfferGroupInfo);
                }
                return data;
            } catch (err) {
                this.$emit('clear');
            }
        }
    }
};
</script>
<style lang="less" scoped>
.tab-box {
    height: 100%;
    overflow: hidden;

    .tab-child-box {
        height: 100%;

        .obs-table {
            height: auto;
        }
    }
}
</style>
