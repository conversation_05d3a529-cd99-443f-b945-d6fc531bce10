<template>
    <div class="tab-box">
        <a-loading v-if="loading"></a-loading>
        <div v-else>
            <info-sum-bar :data="infoData"></info-sum-bar>
            <obs-table ref="table1" maxHeight="210" :title="accessTitle" :tableData='accessData' showTitle notSetWidth autoHeadWidth :columns="accessColumns" :hasPage="false" />
            <obs-table ref="table2" maxHeight="210" :title="flowTitle" :tableData='flowTableData' showTitle notSetWidth autoHeadWidth :columns="flowColumns" :hasPage="false" />
            <!-- <info-sum-bar :data="versionData" class="version"></info-sum-bar> -->
        </div>
    </div>
</template>

<script>
import obsTable from '@/components/common/obsTable/obsTable';
import infoSumBar from '@/components/common/infoBar/infoSumBar';
import aLoading from '@/components/common/loading/aLoading';
import { formatTimeAgo } from '@/utils/utils';
import { getManagerProxy } from '@/api/mcApi';
export default {
    props: {
        nodeData: {
            type: Object,
            default: () => { }
        },
        pollTime: {
            type: Number,
            default: 0
        }
    },
    components: { obsTable, infoSumBar, aLoading },
    data() {
        function setRender(h, params) {
            const poptipInfo = {
                title: '客户端信息',
                contentDic: {
                    ConnectIndex: '连接号',
                    ClientType: '类型',
                    ClientVersion: '版本',
                    ClientPid: '进程号',
                    Address: '地址',
                    Port: '端口',
                    ClientAddressByProxy: '真实ip地址',
                    ClientPortByProxy: '真实端口'
                },
                content: {
                    ConnectIndex: params.row.ConnectIndex,
                    ClientType: params.row.ClientType,
                    ClientVersion: params.row.ClientVersion,
                    ClientPid: params.row.ClientPid,
                    Address: params.row.Address,
                    Port: params.row.Port,
                    ClientAddressByProxy: params.row.ClientAddressByProxy,
                    ClientPortByProxy: params.row.ClientPortByProxy
                }
            };
            return h('div', [
                h(
                    'Poptip',
                    {
                        class: 'apm-poptip',
                        props: {
                            title: poptipInfo.title,
                            placement: 'top-end',
                            positionFixed: true
                        }
                    },
                    [
                        h(
                            'span',
                            [params.row.InternetType]
                        ),
                        h(
                            'div',
                            {
                                slot: 'content',
                                class: 'pop-content'
                            },
                            [
                                ...Object.keys(poptipInfo.content).map((key) => {
                                    return h(
                                        'p',
                                        [
                                            h(
                                                'span',
                                                {
                                                    class: 'pop-label'
                                                },
                                                `${poptipInfo.contentDic[key]}`
                                            ),
                                            h(
                                                'span',
                                                {
                                                    class: 'pop-value',
                                                    attrs: {
                                                        title: `${params.row[key]}`
                                                    }
                                                },
                                                `${params.row[key]}`
                                            )
                                        ]
                                    );
                                })
                            ]
                        )
                    ]
                )
            ]);
        };
        return {
            loading: true,
            // 客户端接入统计
            infoData: {
                title: {
                    label: '客户端接入统计',
                    slots: []
                },
                direction: 'row',
                details: [
                    {
                        type: 'text',
                        title: '客户端接入统计',
                        info: {
                            key: 'accessCount',
                            value: '-'
                        }
                    },
                    {
                        type: 'process-bar',
                        title: '局域网接入容量',
                        info: [
                            {
                                key: 'lanCount',
                                scale: '0',
                                text: '-'
                            }
                        ]
                    },
                    {
                        type: 'process-bar',
                        title: '广域网接入总量',
                        info: [
                            {
                                key: 'wanCount',
                                scale: '0',
                                text: '-'
                            }
                        ]
                    }
                ]
            },
            // 客户端接入统计
            accessTitle: {
                label: '客户端接入统计'
            },
            accessColumns: [
                {
                    title: '连接Id',
                    key: 'ConnectIndex',
                    ellipsis: true
                },
                {
                    title: '接收次数',
                    key: 'RecvTimes',
                    ellipsis: true
                },
                {
                    title: '非法报文拦截次数',
                    key: 'InterceptTimes',
                    minWidth: 140,
                    ellipsis: true
                },
                {
                    title: '发送成功次数',
                    key: 'SendSuccessTimes',
                    minWidth: 120,
                    ellipsis: true,
                    render: (h, params) => {
                        const res = params.row?.SendSuccessTimes ?? params.row?.SendSuccTimes;
                        return h('span', { attrs: { title: res } }, [res]);
                    }
                },
                {
                    title: '发送失败次数',
                    key: 'SendFailTimes',
                    minWidth: 120,
                    ellipsis: true
                },
                {
                    title: '网络链接时间戳',
                    key: 'ConnectedTimeSec',
                    minWidth: 130,
                    ellipsis: true,
                    render: (h, params) => {
                        const res = formatTimeAgo(params.row.ConnectedTimeSec);
                        return h('span', { attrs: { title: res } }, [res]);
                    }
                },
                {
                    title: '最近发送数据时间戳',
                    key: 'LastSendedTimeSec',
                    minWidth: 155,
                    ellipsis: true,
                    render: (h, params) => {
                        const res = formatTimeAgo(params.row.LastSendedTimeSec);
                        return h('span', { attrs: { title: res } }, [res]);
                    }
                },
                {
                    title: '最近接收数据时间戳',
                    key: 'LastRecvedTimeSec',
                    minWidth: 155,
                    ellipsis: true,
                    render: (h, params) => {
                        const res = formatTimeAgo(params.row.LastRecvedTimeSec);
                        return h('span', { attrs: { title: res } }, [res]);
                    }
                },
                {
                    title: '客户端',
                    key: 'InternetType',
                    ellipsis: true,
                    render: (h, params) => {
                        return setRender(h, params);
                    }
                }
            ],
            accessData: [],
            // 客户端流控统计
            flowTitle: {
                label: '客户端流控统计'
            },
            flowColumns: [
                {
                    title: '连接Id',
                    key: 'ConnectIndex',
                    ellipsis: true
                },
                {
                    title: '每秒实时接收数',
                    key: 'RecvRealTimeCount',
                    minWidth: 130,
                    ellipsis: true
                },
                {
                    title: '每秒接收数峰值',
                    key: 'RecvPeakCount',
                    minWidth: 130,
                    ellipsis: true
                },
                {
                    title: '每秒接收数峰值时间戳',
                    key: 'RecvPeakTimeSec',
                    minWidth: 165,
                    ellipsis: true,
                    render: (h, params) => {
                        const res = formatTimeAgo(params.row.RecvPeakTimeSec);
                        return h('span', { attrs: { title: res } }, [res]);
                    }
                },
                {
                    title: '每秒实时拒绝数',
                    key: 'RefuseRealTimeCount',
                    minWidth: 130,
                    ellipsis: true
                },
                {
                    title: '每秒拒绝数峰值',
                    key: 'RefusePeakCount',
                    minWidth: 130,
                    ellipsis: true
                },
                {
                    title: '每秒拒绝数峰值时间戳',
                    key: 'RefusePeakTimeSec',
                    minWidth: 165,
                    ellipsis: true,
                    render: (h, params) => {
                        const res = formatTimeAgo(params.row.RefusePeakTimeSec);
                        return h('span', { attrs: { title: res } }, [res]);
                    }
                },
                {
                    title: '客户端',
                    key: 'InternetType',
                    ellipsis: true,
                    render: (h, params) => {
                        return setRender(h, params);
                    }
                }
            ],
            flowTableData: [],
            // 版本分布
            versionData: {
                title: {
                    label: '客户端版本分布',
                    slots: []
                },
                direction: 'row',
                details: [
                    {
                        type: 'chart',
                        info: {
                            basicOpiton: {
                                chartType: 'pie'
                            },
                            chartData: {
                                data: []
                            },
                            height: '270'
                        },
                        infoWidth: '500px',
                        hasBackgroundColor: false
                    },
                    {
                        type: 'table',
                        hasBackgroundColor: false,
                        info: {
                            tableData: [],
                            height: '270',
                            columns: [
                                {
                                    title: 'ClientVersion',
                                    key: 'ClientVersion',
                                    ellipsis: true
                                },
                                {
                                    title: 'Count',
                                    key: 'Count',
                                    ellipsis: true
                                },
                                {
                                    title: 'Rate(%)',
                                    key: 'Rate',
                                    ellipsis: true
                                }
                            ]
                        }
                    }
                ]
            }
        };
    },
    mounted() {
    },
    methods: {
        async initData() {
            this.loading = true;
            await this.getFileData();
            this.loading = false;
        },
        async getFileData() {
            const { WanClientStat, LanClientStat } = await this.getAPi();
            // 客户端接入统计
            this.infoData.details[0].info.value = WanClientStat.WanClientCount + LanClientStat.LanClientCount || '0';
            this.infoData.details[1].info = [
                {
                    key: 'lanCount',
                    scale: LanClientStat.MaxLanCount ? String((LanClientStat.LanClientCount / LanClientStat.MaxLanCount) * 100) : '0',
                    text: (LanClientStat.LanClientCount || '0') + ' / ' + (LanClientStat.MaxLanCount || '0')
                }
            ];
            this.infoData.details[2].info = [
                {
                    key: 'WanCount',
                    scale: WanClientStat.MaxWanCount ? String((WanClientStat.WanClientCount / WanClientStat.MaxWanCount) * 100) : '0',
                    text: (WanClientStat.WanClientCount || '0')  + ' / ' + (WanClientStat.MaxWanCount || '0')
                }
            ];
            // 客户端流控统计  版本分布信息汇总
            this.accessData = [];
            this.flowTableData = [];
            const ClientVersion = {};
            WanClientStat?.Connections?.length && WanClientStat.Connections.forEach((v) => {
                const versionMatch = v?.ClientVersion?.match(/V\d+\.\d+\.\d+\.\d+/) || [];
                const key = versionMatch?.[0];
                if (key){
                    ClientVersion[key] = ClientVersion[key] || [];
                    ClientVersion[key].push({ ...v });
                }
                this.accessData.push({ ...v, InternetType: 'Wan' });
                this.flowTableData.push({ ...v, InternetType: 'Wan' });
            });

            LanClientStat?.Connections?.length && LanClientStat.Connections.forEach((v) => {
                const versionMatch = v?.ClientVersion?.match(/V\d+\.\d+\.\d+\.\d+/) || [];
                const key = versionMatch?.[0];
                if (key){
                    ClientVersion[key] = ClientVersion[key] || [];
                    ClientVersion[key].push({ ...v });
                }
                this.accessData.push({ ...v, InternetType: 'Lan' });
                this.flowTableData.push({ ...v, InternetType: 'Lan' });
            });

            // 客户端版本分布
            this.versionData.details[1].info.tableData = [];
            this.versionData.details[0].info.chartData.data =  [];
            let totalVersionLength = 0;
            Object.keys(ClientVersion).forEach(key => {
                if (Array.isArray(ClientVersion[key])) {
                    totalVersionLength += ClientVersion[key].length;
                }
            });
            Object.keys(ClientVersion).forEach(key => {
                this.versionData.details[1].info.tableData.push({
                    ClientVersion: key,
                    Count: ClientVersion[key].length,
                    Rate: (ClientVersion[key].length / totalVersionLength) * 100
                });

                this.versionData.details[0].info.chartData.data.push({
                    name: key,
                    value: ClientVersion[key].length
                });
            });
        },
        // 接口请求
        async getAPi() {
            const data = {
                WanClientStat: {},
                LanClientStat: {}
            };
            const param = [
                {
                    manageProxyIp: this.nodeData.ip,
                    manageProxyPort: this.nodeData.manageProxyPort,
                    pluginName: 'ldp_front',
                    funcName: 'WanClientStat'
                },
                {
                    manageProxyIp: this.nodeData.ip,
                    manageProxyPort: this.nodeData.manageProxyPort,
                    pluginName: 'ldp_front',
                    funcName: 'LanClientStat'
                }
            ];
            try {
                const res = await getManagerProxy(JSON.stringify(param));
                if (res.code === '200') {
                    !res.data?.[0]?.ErrorNo && Object.keys(res.data?.[0]).length && (data.WanClientStat = res.data[0]);
                    !res.data?.[1]?.ErrorNo && Object.keys(res.data?.[1]).length  && (data.LanClientStat = res.data[1]);
                }
                return data;
            } catch (err) {
                this.$emit('clear');
            }
        }
    }
};
</script>
<style lang="less">
// render函数生成的节点无法匹配scoped id， 需要用全局去处理
@import url("@/assets/css/poptip-1.less");
</style>

<style lang="less" scoped>
.tab-box {
    /deep/ .process .process-bar-box {
        background: none;
    }

    .version {
        /deep/ .obs-table .h-table-wrapper {
            border: 1px solid var(--primary-color);
        }
    }

    /deep/ .h-poptip-popper {
        width: 300px;

        .pop-content {
            .pop-label {
                display: inline-block;
                width: 60%;
                margin-right: 10px;
                overflow: hidden;
                white-space: nowrap;
                text-overflow: ellipsis;
            }

            .pop-value {
                display: inline-block;
                width: 35%;
                overflow: hidden;
                white-space: nowrap;
                text-overflow: ellipsis;
            }
        }
    }
}
</style>
