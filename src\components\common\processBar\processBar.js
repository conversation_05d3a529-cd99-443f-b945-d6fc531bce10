import './processBar.less';
export default {
    name: 'process-bar',
    props: {
        width: {
            type: String,
            default: '0'
        },
        name: {
            type: String,
            default: ''
        },
        label: {
            type: String,
            default: ''
        }
    },
    data() {
        return {
        };
    },
    methods: {
        setWidth(width){
            if (width && width > 0 && width < 100){
                return Number(width).toFixed(2) + '%';
            }

            if (width && width <= 0){
                return '0%';
            }

            if (width && width >= 100){
                return '100%';
            }

            return '0%';
        }
    },
    render() {
        return <div class="process">
            {this.label && <div class="process-label">{this.label}</div>}
            <div class="process-bar-box">
                <div class='process-box' title={this.setWidth(this.width) }>
                    <div class='progress-bar'  style={{ width: this.setWidth(this.width) }}></div>
                    <span>{this.name || (this.width || 0) + '%' }</span>
                </div>
            </div>
        </div>;
    }
};
