<template>
    <div class="cluster-menu">
        <h-select ref="context" v-model="formValid.connectType" filterable placeholder="连接类型"
            style="width: 160px; margin-right: 8px;" @on-change="handleExchange">
            <h-option v-for="item in communicationTypeList" :key="item.value" :value="item.value">
                {{ item.label }}
            </h-option>
        </h-select>

        <!--搜索-->
        <h-select ref="context" v-model="formValid.nodeIds" filterable placeholder="搜索集群" remoteIcon="search"
            style="width: 200px;" showBottom widthAdaption isCheckall multiple @on-change="handleExchange">
            <h-option v-for="item in showCluster" :key="item.id" :value="item.id">
                {{ item.name }}
            </h-option>
        </h-select>
        <div class="cluster-menu-line"></div>
        <h-select v-model="formValid.topologyView" :clearable="false" class="cluster-menu-relation">
            <h-option value="UpstreamDownstream">上下游关系</h-option>
        </h-select>
    </div>
</template>

<script>
import { getObservableAppTypeTopology } from '@/api/topoApi';

export default {
    name: 'LdpClusterObserver',
    props: {
        productId: {
            type: String,
            default: ''
        }
    },
    data() {
        return {
            formValid: {
                topologyView: 'UpstreamDownstream',
                connectType: '',
                nodeIds: []
            },
            nodeList: [],
            edgesList: [],
            timer: null,
            loading: false,
            timeLoading: false,
            showCluster: []
        };
    },
    computed: {
        // 边连接类型字典
        communicationTypeList() {
            const communicationTypeList = [];
            const communicationTypeDict = this.$store?.state?.apmDirDesc?.communicationTypeDict || {};
            Object.keys(communicationTypeDict).forEach(ele => {
                communicationTypeList.push({
                    label: communicationTypeDict[ele],
                    value: ele
                });
            });
            return communicationTypeList;
        }
    },
    mounted() {
        this.queryClusterList();
    },
    methods: {
        /**
         * 参数变更，触发搜索
         */
        handleExchange() {
            this.$emit('changeParam', this.formValid);
        },
        /**
         * 设置查询参数
         */
        setQueryParam(arg = {}) {
            Object.keys(arg).forEach(key => {
                if (key in this.formValid) {
                    // 更新字段
                    this.formValid[key] = arg[key];
                }
            });
            this.handleExchange();
        },
        /**
         * 重置查询参数
         */
        resetParam() {
            this.formValid = {
                topologyView: 'UpstreamDownstream',
                connectType: '',
                nodeIds: []
            };
        },
        /**
         * 查询集群列表
         */
        async queryClusterList() {
            this.timeLoading = true;
            const param = {
                productId: this.productId,
                type: 'cluster'
            };
            this.deepQueryParam = { ...this.deepQueryParam, ...param };
            try {
                const res = await getObservableAppTypeTopology(param);
                if (this.productId !== param.productId) return;
                if (res.success) {
                    // 向菜单组件暴露集群信息
                    this.showCluster =  [...new Set((res?.data?.nodes || []).map(item => {
                        return { id: item.id, name: item.name };
                    }))];
                }
            } catch (error){
            }
        }
    }
};
</script>
<style lang="less" scoped>
@import url("@/assets/css/input.less");

.cluster-menu {
    display: flex;
    align-items: center;
    justify-content: space-between;

    &-line {
        background: #474e6f;
        width: 1px;
        height: 26px;
        margin: 0 8px;
    }

    &-relation {
        width: 120px;
    }

    /deep/ .h-select {
        height: 32px;
    }

    /deep/ .h-select-dropdown {
        input {
            color: #afafaf;
        }
    }
}
</style>
