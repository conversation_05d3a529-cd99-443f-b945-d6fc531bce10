/*
 * @Description: 配合新版本查询表格的form表单组件
 */
import './form.less';
import _ from 'lodash';
import aButton from '@/components/common/button/aButton';
import { cutZero } from '@/utils/utils';
export default {
    name: 'apm-form',
    components: { aButton },
    props: {
        loading: {
            type: Boolean,
            default: false
        },
        formItems: {
            type: Array,
            default: () => []
        },
        formCols: {
            type: Number,
            default: 4
        },
        proposalList: {
            type: Array,
            default: () => []
        }
    },
    data() {
        return {
            forms: {},
            queryFieldCondition: 'gt',
            beforeFiledValue: 0,
            unit: '2',
            columns: [
                {
                    title: '周期',
                    key: 'date',
                    width: 110
                },
                {
                    title: '最小值',
                    key: 'min',
                    width: 100,
                    render: (h, params) => {
                        return this.dynamicTable(h, params);
                    }
                },
                {
                    title: '平均数',
                    key: 'avg',
                    width: 100,
                    render: (h, params) => {
                        return this.dynamicTable(h, params);
                    }
                },
                {
                    title: '中位数',
                    key: 'p50',
                    width: 100,
                    render: (h, params) => {
                        return this.dynamicTable(h, params);
                    }
                },
                {
                    title: '最大值',
                    key: 'max',
                    width: 100,
                    render: (h, params) => {
                        return this.dynamicTable(h, params);
                    }
                }
            ],
            proposalDict: {
                yesterday: '产品昨日时延',
                week: '产品上周时延',
                month: '产品上月时延'
            }
        };
    },
    computed: {
        // 时延筛选数据建议表格数据
        proposalTableList: function() {
            const list = [];
            this.proposalList.forEach(ele => {
                const multiple = Math.pow(1000, 3 - this.unit);
                const param = {
                    date: this.proposalDict[ele.date]
                };
                ['avg', 'min', 'p50', 'max'].forEach(item => {
                    param[item] = cutZero((ele[item] / multiple || 0).toFixed(3));
                });
                list.push(param);
            });
            return list;
        },
        // 时延筛选数据建议表格title
        proposalTitle: function() {
            const list = ['s', 'ms', 'μs', 'ns'];
            return '时延指标数据建议(单位:' + list[this.unit] + ')';
        }
    },
    mounted() {
    },
    methods: {
        init() {
            this.formItems.forEach(ele => {
                this.$set(this.forms, ele.key, _.cloneDeep(ele.value));
            });
        },
        setDefaultVal(obj) {
            const objKeys = Object.keys(obj);
            this.formItems.forEach(ele => {
                if (objKeys.includes(ele.key)) {
                    this.$set(this.forms, ele.key, _.cloneDeep(obj[ele.key]));
                }
            });
        },
        // 回显数据
        echoFormData(param){
            if (!Object.keys(param)?.length) return;
            Object.keys(param).forEach(key => {
                if (key === 'startDate' || key === 'endDate') return;
                if (key === 'unit'){
                    this.unit = param.unit;
                    return;
                }
                if (key === 'queryFieldCondition'){
                    this.queryFieldCondition = param.queryFieldCondition;
                    return;
                }
                if (key === 'beforeFiledValue'){
                    this.beforeFiledValue = param.beforeFiledValue;
                    return;
                }
                this.$set(this.forms, key, param[key]);
            });
        },
        // 重置查询数据
        reset() {
            this.init();
            this.$emit('reset');
        },
        // 重置对应formItem数据
        // TODO:后续需考虑默认选中的下拉框重置数据方法，当前重置是将对应项置为''
        resetItem(key) {
            this.forms[key] = '';
        },
        // 点击查询
        query() {
            let isQuery = false, queryData = {};
            this.$refs['formValidate'].validate(valid => {
                if (valid) {
                    const param = _.find(this.formItems, { type: 'timescreen' }) ? {
                        queryFieldCondition: this.queryFieldCondition,
                        beforeFiledValue: this.beforeFiledValue,
                        unit: this.unit
                    } : {};
                    queryData = { ...this.forms, ...param };
                    isQuery = true;
                }
            });
            return isQuery ? queryData : false;
        },
        // 时延筛选数值框失去焦点
        fieldValueBlur() {
            this.beforeFiledValue = this.beforeFiledValue ? isNaN(this.beforeFiledValue) ? 0 : parseFloat(this.beforeFiledValue) : 0;
        },
        // 查询时延筛选参照数据
        queryProposal(span) {
            span && this.$emit('queryProposal', span);
        },
        // 监听下拉框变化
        handleSelectChange(key, val) {
            this.$emit('handleSelectChange', key, val);
        },
        // 动态渲染表格数据
        dynamicTable(h, param) {
            return h(
                'div',
                {
                    class: 'td-time',
                    on: {
                        click: () => {
                            this.beforeFiledValue = isNaN(param.row[param.column.key]) ? 0 : cutZero(Number(param.row[param.column.key]).toFixed(1));
                        }
                    }
                },
                cutZero(Number(param.row[param.column.key]).toFixed(1))
            );
        },
        // 处理input图标点击
        handleInputIconClick(icon, key) {
            // clearable需要聚焦才展示,可通过图标方式展示无需聚焦也展示清除图标
            icon === 'close' && (this.forms[key] = '');
        },
        handleClickQuery() {
            this.$emit('click-query');
        }
    },
    render() {
        this.init();
        // 生成输入框
        const generateInput = (item) => {
            return (
                <h-input
                    v-model={this.forms[item.key]}
                    placeholder={item.placeholder || '请输入'}
                    icon={item.icon}
                    clearable={item.clearable}
                    v-on:on-click={() => this.handleInputIconClick(item.icon, item.key)}
                ></h-input>
            );
        };
        // 生成普通下拉框
        const generateSelect = (item) => {
            return (
                <h-select v-model={this.forms[item.key]}
                    placeholder={item.placeholder || '请选择'}
                    positionFixed={true}
                    multiple={item.multiple || false}
                    v-on:on-change={(val) => { this.handleSelectChange(item.key, val); }}
                >
                    {
                        item.options?.map(opt => (
                            <h-option key={opt.value} value={opt.value}>{opt.label}</h-option>
                        ))
                    }
                </h-select>
            );
        };
        // 生成可搜索的下拉框
        const generateSelectSearch = (item) => {
            return <h-select
                v-model={this.forms[item.key]}
                filterable
                placeholder={item.placeholder || '请选择'}
                positionFixed={true}
                loading={item.loading}
                remote
                remote-method={item.remoteMethod}
                remoteIcon="search"
                loading-text="加载中...">
                {
                    item.options.map(opt => { return <h-option key={opt.value} value={opt.value}>{opt.label}</h-option>; })
                }
            </h-select>;
        };
        // 生成日期筛选框
        const generateDate = (item) => {
            return <h-datePicker
                type="date"
                placeholder="选择日期"
                v-model={this.forms[item.key]}
                positionFixed={true}
                editable={item?.editable || false}
                placement={item?.placement || 'bottom-start'}
            ></h-datePicker>;
        };
        // 生成日期范围选择框
        const generateDaterange = (item) => {
            return <h-date-picker
                type="daterange"
                confirm
                placement={item?.placement || 'bottom-start'}
                editable={item?.editable || false}
                placeholder="选择日期"
                positionFixed={true}
                v-model={this.forms[item.key]}></h-date-picker>;
        };
        // 生成时间选择
        const generateDateTime = (item) => {
            return <h-date-picker
                type="datetime"
                placeholder="选择日期"
                v-model={this.forms[item.key]}
                positionFixed={true}
                editable={item?.editable || false}
                placement={item?.placement || 'bottom-start'}
            ></h-date-picker>;
        };
        // 生成时间范围选择框
        const generateTimerange = (item) => {
            return <h-time-picker
                confirm
                placement={item?.placement || 'bottom-start'}
                type="timerange"
                placeholder={item.placeholder || '选择时间'}
                v-model={this.forms[item.key]}
                positionFixed={true}
                editable={item?.editable || false}
            ></h-time-picker>;
        };
        // 生成日期事件范围
        const generateDateTimerange = (item) => {
            return <h-date-picker
                confirm
                placement={item?.placement || 'bottom-start'}
                type="datetimerange"
                placeholder="选择日期时间"
                v-model={this.forms[item.key]}
                positionFixed={true}
                editable={item?.editable || false}
            ></h-date-picker>;
        };
        // 时延筛选
        const generateTimescreen = (item) => {
            return <div style="display: flex;">
                <h-select
                    v-model={this.forms[item.key]}
                    placeholder={item.placeholder || '请选择'}
                    positionFixed={true}
                    v-on:on-change={this.queryProposal}
                    style="padding-right: 4px;">
                    {
                        item.options.map(opt => {
                            return <h-option key={opt.value} value={opt.value}>{opt.label}</h-option>;
                        })
                    }
                </h-select>
                <h-select
                    v-model={this.queryFieldCondition}
                    positionFixed
                    clearable={false}
                    style="width: 50px; padding-right: 4px;">
                    <h-option value="gt">&gt;=</h-option>
                    <h-option value="lt">&lt;=</h-option>
                </h-select>
                <h-poptip
                    // trigger="focus"
                    placement={item.placement || 'bottom'}
                    transfer
                    title={this.proposalTitle}
                    positionFixed
                    width={545}>
                    <h-input
                        v-model={this.beforeFiledValue}
                        disabled={!this.forms[item.key]}
                        placeholder="请输入"
                        v-on:on-blur={this.fieldValueBlur}
                        style="width: 60px; top: 0px; padding-right: 4px;"></h-input>
                    <div slot="content" class="api">
                        <h-table border columns={this.columns} data={this.proposalTableList} size='small'></h-table>
                    </div>
                </h-poptip>

                <h-select
                    v-model={this.unit}
                    clearable={false}
                    positionFixed={true}
                    style="width: 50px;"
                >
                    <h-option value="0">s</h-option>
                    <h-option value="1">ms</h-option>
                    <h-option value="2">μs</h-option>
                    <h-option value="3">ns</h-option>
                </h-select>
            </div>;
        };

        const inputGenerators = {
            input: generateInput,
            select: generateSelect,
            selectSearch: generateSelectSearch,
            date: generateDate,
            daterange: generateDaterange,
            dateTime: generateDateTime,
            timerange: generateTimerange,
            datetimerange: generateDateTimerange,
            timescreen: generateTimescreen
        };

        return <h-form
            ref="formValidate"
            props={{ model: this.forms }}
            label-width={85}
            cols={this.formCols}
            class="best-form query-form"
        >
            {
                this.formItems.map(item => {
                    return <h-form-item
                        labelTitle={item.label}
                        label={item.label}
                        prop={item.key || ''}
                        required={item.required || false}
                        labelWidth={item?.labelWidth || undefined}
                    >
                        {(() => {
                            const generator = inputGenerators[item.type];
                            if (generator) {
                                return generator(item);
                            } else {
                                return '';
                            }
                        })}
                    </h-form-item>;
                })
            }
            {/* 查询和重置按钮 */}
            <h-form-item labelWidth={10}>
                <a-button
                    type="primary"
                    onClick={this.handleClickQuery}
                    disabled={this.loading}
                    style="margin-right: 10px">
                        查询
                </a-button>
                <a-button type="dark"
                    onClick={this.reset}>
                        重置
                </a-button>
            </h-form-item>

            <style jsx>
                {
                    `
                        .h-poptip-popper[x-placement^="bottom"] {
                            .h-poptip-arrow {
                                border-bottom-color: #fff !important;

                                &::after {
                                    border-bottom-color: #fff !important;
                                }
                            }
                        }
                    `
                }
            </style>
            <input type="text" hidden />
        </h-form>;

    }
};
