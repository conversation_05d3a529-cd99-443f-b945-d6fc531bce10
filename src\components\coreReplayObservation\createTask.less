@import url("@/assets/css/input.less");
@import url("@/assets/css/steps.less");
@import url("@/assets/css/checkbox.less");

.create-step {
    position: relative;
    height: 100%;
    width: 100%;
    min-width: 900px;

    .steps-box {
        padding: 15px;
    }

    .content-box {
        height: calc(100% - 100px);
        padding: 10px 15px 0;
        overflow: auto;

        .tips-box {
            width: 80%;
            background: #2d334c;
            border-radius: 4px;
            padding: 15px;
            line-height: 20px;
            margin: 0 0 15px;

            .tip-title {
                color: var(--font-color);
                padding-bottom: 10px;
                font-weight: 600;
            }

            .tip-content {
                color: #cacfd4;
            }
        }
    }

    /deep/ .h-select-dropdown {
        margin: 0;
    }

    /deep/ .h-form-item {
        margin-bottom: 34px;
    }

    /deep/ .h-form-item-content {
        color: var(--font-body-color);
    }

    /deep/.h-input-group-append {
        background-color: var(--input-bg-color);
        border: 1px solid #485565;
        color: #cacfd4;
    }

    .form-box-0 {
        width: 80%;
        padding: 10px 15px;
        color: var(--font-color);

        /deep/ .h-input-wrapper {
            textarea {
                resize: none;
                line-height: 20px;
            }

            textarea::placeholder {
                line-height: 20px;
                color: #9296a1;
                font-family: PingFangSC-Regular;
            }
        }
    }

    .form-container {
        .tab-core-container {
            background: transparent;
            border: 1px solid #485565;
            border-radius: 4px;
            height: 300px;
            // overflow-y: auto;

            /deep/.h-tabs-nav-right .h-tabs-tab-active i,
            /deep/.h-tabs-nav-right .h-tabs-tab:hover i {
                display: none;
            }

            /deep/.h-tabs-nav-right {
                font-family: PingFangSC-Regular;
                font-size: 12px;
                color: #fff;
                padding-right: 0;
                height: 300px;
                overflow-y: auto;
            }

            /deep/.h-tabs-content-right {
                border-left: 1px solid #414d5d;
            }

            /deep/.h-tabs-nav-right .h-tabs-tab-active {
                background: #1f3759;

                &::after {
                    position: absolute;
                    top: 0;
                    left: 0;
                    content: "";
                    width: 4px;
                    height: 36px;
                    background: var(--link-color);
                }
            }

            /deep/.h-tabs-content-right .h-tabs-tabpane {
                padding: 0;
            }

            /deep/.h-tabs-nav-right .h-tabs-tab-alginleft {
                overflow: hidden;
                text-overflow: ellipsis;
                white-space: nowrap;
            }

            /deep/ .h-tabs-nav-right .h-tabs-tab:hover {
                background: #1f3759;
                cursor: pointer;

                &::after {
                    position: absolute;
                    top: 0;
                    left: 0;
                    content: "";
                    width: 4px;
                    height: 36px;
                    background: var(--link-color);
                }
            }

            .core-label {
                height: 36px;
                font-family: PingFangSC-Regular;
                font-size: 12px;
                color: #fff;
                font-weight: 400;
                background: #2c334a;
                border-radius: 4px 4px 0 0;
                border-bottom: 1px solid #444a60;
                padding: 1px 10px;
            }

            .core-container {
                height: 262px;
                overflow-y: auto;

                p {
                    display: inline-block;
                    height: 22px;
                    line-height: 22px;
                    margin: 5px 10px;
                    font-family: PingFangSC-Regular;
                    font-size: 12px;
                    color: #cacfd4;
                    text-align: left;
                }
            }
        }
    }

    .confirm-box {
        width: 80%;
        padding: 15px 0;

        .confirm-title {
            color: var(--font-color);
            font-size: 14px;
            padding: 10px;

            &::before {
                display: inline-block;
                position: relative;
                left: -10px;
                top: 3px;
                content: "";
                width: 4px;
                height: 16px;
                background: var(--link-color);
            }

            span {
                font-size: 12px;
                padding-left: 10px;
                color: #cacfd4;
            }
        }

        .confirm-tips {
            height: 32px;
            line-height: 32px;
            background: #1f3759;
            border-radius: 4px;
            padding: 0 5px;
            color: var(--font-color);
        }

        .tags-input {
            color: var(--font-color);
            padding-left: 15px;

            .h-select {
                padding-left: 10px;
                width: 400px;

                /deep/ .h-select-dropdown-content .h-select-content-input {
                    color: inherit;
                }

                /deep/ .h-checkbox-inner {
                    border: 1px solid #d7dde4;
                    border-radius: 2px;
                    background-color: #fff;
                }
            }
        }
    }

    .buttom-box {
        position: absolute;
        bottom: 0;
        width: 100%;
        height: 48px;
        line-height: 48px;
        padding: 0 15px;
        border-top: 1px solid #444a60;

        .h-btn {
            margin-right: 5px;
        }

        .h-btn:first-child {
            margin-left: 15px;
        }
    }

    .table-select-title {
        // 清除搜索框校验样式
        /deep/ .h-form-item-error .h-input {
            border-color: #9ea7b4 !important;
        }

        /deep/ .h-form-item-error .h-input-icon {
            color: #9ea7b4;
        }
    }
}
