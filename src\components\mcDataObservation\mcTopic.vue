<template>
    <div class="tab-box">
        <a-loading v-if="loading"></a-loading>
        <div v-else>
            <div class="title">
                <p>主题信息</p>
                <apm-select-search
                    ref="topicSearch"
                    minWidth="150px"
                    focusWidth="350px"
                    dropHeight="200px"
                    class="securities"
                    :list="topicList"
                    clearable
                    placeholder="请选择主题"
                    :border="true"
                    @onChange="(val) => handleSelectChange(val, 'topic')"
                />
                <a-button type="dark" @click="handleButtonClick">刷新</a-button>
            </div>
            <!--基础信息-->
            <description-bar ref='description1' :data="description1"></description-bar>
            <!--发布订阅信息-->
            <description-bar ref='description2' :data="description2"></description-bar>
            <!--过滤条件信息-->
            <description-bar ref='description3' :data="description3"></description-bar>
            <!--分区详情、消费者详情 -->
            <h-tabs class="sub-table-tabs">
                <h-tab-pane label="生产者信息">
                    <self-table
                        ref="self-table"
                        :height="250"
                        :tableData="pubTableData"
                        :columns="pubColumns"
                        showTitle
                        border
                        canDrag
                        :loading="tableLoading"
                        searchKey="PubName"
                        placeholder="请选择生产者"
                        searchType="select"
                     />
                </h-tab-pane>
                <h-tab-pane label="消费者信息">
                    <self-table
                        ref="self-table"
                        :height="250"
                        :tableData="subTableData"
                        :columns="subColumns"
                        showTitle
                        border
                        canDrag
                        :loading="tableLoading"
                        searchKey="SubName"
                        placeholder="请选择消费者"
                        searchType="select"
                     />
                </h-tab-pane>
                <h-tab-pane label="分区信息">
                    <obs-table
                        ref="table"
                        :height="300"
                        :tableData="tableData"
                        :columns="columns"
                        showTitle
                        :hasPage="false"
                        :loading="tableLoading"
                    />
                </h-tab-pane>
            </h-tabs>
        </div>
    </div>
</template>

<script>
import aButton from '@/components/common/button/aButton';
import aLoading from '@/components/common/loading/aLoading';
import descriptionBar from '@/components/common/description/descriptionBar';
import apmSelectSearch from '@/components/common/apmSelectSearch/apmSelectSearch';
import obsTable from '@/components/common/obsTable/obsTable';
import selfTable from '@/components/common/table/selfTable';
import { getMcAPi } from '@/api/mcApi';
import { removeDuplicates } from '@/components/mcDataObservation/constant';
export default {
    name: 'McTopic',
    props: {
        nodeData: {
            type: Object,
            default: () => {}
        }
    },
    components: { descriptionBar, aLoading, aButton, obsTable, selfTable, apmSelectSearch },
    data() {
        return {
            loading: false,
            tableLoading: false,
            // 基础信息
            description1: {
                title: {
                    label: '基础信息'
                },
                details: [
                    {
                        dataDic: [
                            {
                                label: '主题名',
                                key: 'Topic',
                                span: '4'
                            },
                            {
                                label: '主题描述',
                                key: 'Desc',
                                span: '4'
                            },
                            {
                                label: '分区数',
                                key: 'PartitionCount',
                                span: '4'
                            },
                            {
                                label: '分区副本数',
                                key: 'ReplicationFactor',
                                span: '4'
                            },
                            {
                                label: '是否全局主题',
                                key: 'Local',
                                span: '4'
                            },
                            {
                                label: '有序级别',
                                key: 'OrderLevel',
                                span: '4'
                            },
                            {
                                label: '业务校验',
                                key: 'BusinessVerify',
                                span: '4'
                            },
                            {
                                label: '可靠级别',
                                key: 'ReliableLevel',
                                span: '4'
                            },
                            {
                                label: '消息有效期',
                                key: 'LifeTime',
                                span: '4'
                            },
                            {
                                label: '服务端是否保存消费者偏移',
                                key: 'OffsetSave',
                                span: '6'
                            }
                        ],
                        data: {
                            Topic: '-',
                            Desc: '-',
                            PartitionCount: '-',
                            ReplicationFactor: '-',
                            Local: '-',
                            OrderLevel: '-',
                            BusinessVerify: '-',
                            ReliableLevel: '-',
                            LifeTime: '-',
                            OffsetSave: '-'
                        }
                    }
                ]
            },
            // 发布订阅消息
            description2: {
                title: {
                    label: '发布订阅信息'
                },
                details: [
                    {
                        dataDic: [
                            {
                                label: '生产者数',
                                key: 'PubCount',
                                span: '4'
                            },
                            {
                                label: '消费者数',
                                key: 'SubCount',
                                span: '4'
                            },
                            {
                                label: '订阅项数',
                                key: 'SubscrCount',
                                span: '4'
                            },
                            {
                                label: '推送消息总数',
                                key: 'PushMsgCount',
                                span: '4'
                            },
                            {
                                label: '生产消息总数',
                                key: 'PubMsgCount',
                                span: '4'
                            }
                        ],
                        data: {
                            PubCount: '-',
                            SubCount: '-',
                            SubscrCount: '-',
                            PushMsgCount: '-',
                            PubMsgCount: '-'
                        }
                    }
                ]
            },
            // 过滤条件
            description3: {
                title: {
                    label: '过滤条件'
                },
                details: [
                    {
                        type: 'text',
                        dataDic: []
                    }
                ]
            },
            // 分区详情
            tableData: [],
            columns: [
                {
                    title: '分区编号',
                    key: 'PartitionId',
                    ellipsis: true
                },
                {
                    title: '主分区所在节点',
                    key: 'Leader',
                    ellipsis: true,
                    render: (h, params) => {
                        const leader = params?.row?.Leader?.split(',') || [];
                        const res = leader.filter(v => v.trim())?.map(o => 'mc#' + o)?.join(',');
                        return h(
                            'span',
                            {
                                attrs: {
                                    title: res
                                }
                            },
                            [res]
                        );
                    }
                },
                {
                    title: '副本分区所在节点',
                    key: 'Replicas',
                    ellipsis: true,
                    render: (h, params) => {
                        const replicas = params?.row?.Replicas?.split(',') || [];
                        const res = replicas.filter(v => v.trim())?.map(o => 'mc#' + o)?.join(',');
                        return h(
                            'span',
                            {
                                attrs: {
                                    title: res
                                }
                            },
                            [res]
                        );
                    }
                },
                {
                    title: '持久化消息数',
                    key: 'MsgNo',
                    ellipsis: true
                }
            ],
            // 消费者信息
            subTableData: [],
            subColumns: [
                {
                    title: '消费者名称',
                    key: 'SubName',
                    ellipsis: true
                },
                {
                    title: '消费者实例名称',
                    key: 'InstanceName',
                    ellipsis: true
                },
                {
                    title: '订阅时间',
                    key: 'SubTime',
                    ellipsis: true
                },
                {
                    title: '订阅方式',
                    key: 'PartitionNo',
                    ellipsis: true,
                    render: (h, params) => {
                        const partitionNo = params?.row?.PartitionNo?.toString() === '-1' ? '广播消费' : '集群消费';
                        return h(
                            'span',
                            {
                                attrs: {
                                    title: partitionNo
                                }
                            },
                            [partitionNo]
                        );
                    }
                },
                {
                    title: '主题前缀',
                    key: 'TopicPrefix',
                    ellipsis: true
                },
                {
                    title: '已推送消息数',
                    key: 'PushCount',
                    ellipsis: true
                },
                {
                    title: '补缺个数',
                    key: 'RebuildCount',
                    ellipsis: true
                },
                {
                    title: '过滤条件值',
                    key: 'Filters',
                    render: (h, params) => {
                        const res = [];
                        Object.keys(params.row.Filter).forEach(key => {
                            const str = '(' + key + ':' + params.row.Filter[key] + ')';
                            res.push(str);
                        });

                        return h(
                            'div',
                            {
                                class: 'h-table-cell-ellipsis',
                                attrs: {
                                    title: res?.join(',') || '-'
                                }
                            },
                            [res?.join(',') || '-']
                        );
                    }
                }
            ],
            // 生产者信息
            pubTableData: [],
            pubColumns: [
                {
                    title: '生产者实例名',
                    key: 'PubName',
                    ellipsis: true
                },
                {
                    title: '发布的消息数',
                    key: 'PublishCount',
                    ellipsis: true
                },
                {
                    title: '发布序号',
                    key: 'NextMsgNo',
                    ellipsis: true,
                    render: (h, params) => {
                        const res = params?.row?.NextMsgNo ? params?.row?.NextMsgNo - 1 : 0 ;
                        return h(
                            'span',
                            {
                                attrs: {
                                    title: res
                                }
                            },
                            [res]
                        );
                    }
                },
                {
                    title: '分区号',
                    key: 'PartitionNo',
                    ellipsis: true,
                    render: (h, params) => {
                        const str = String(params?.row?.TopicPartition);
                        const partition = Number(str.substring(str.length - 4));
                        return h(
                            'span',
                            {
                                attrs: {
                                    title: partition
                                }
                            },
                            [partition]
                        );
                    }
                }
            ],
            topic: '',
            topicList: [],
            fileData: {},
            getTopicInfo: []
        };
    },
    mounted() {
    },
    methods: {
        // 手动清空数据
        clearData(){
            this.topic = '';
            this.topicList = [];
            this.fileData = {};
            this.tableData = [];
            this.pubTableData = [];
            this.subTableData = [];
            this.description1.details[0].data = {};
            this.description2.details[0].data = {};
            this.description3.details[0].dataDic = [];
        },
        // 初始化构建数据结构
        async initData(val) {
            this.loading = true;
            await this.getTopicList();
            this.loading = false;
            this.$nextTick(() => {
                this.setDefaultTopic(val);
            });
        },
        // 刷新---重新构建数据结构, 默认选择第一个主题。若之前的主题存在，则页面更新为当前主题信息
        async handleButtonClick() {
            await this.initData(this.topic);
        },
        // 判断主题是否存在, 使用历史选中值用于刷新，否则默认选中第一个
        setDefaultTopic(val) {
            const hasTopic = this.topicList.filter(v => v.value === val)?.[0] || '';
            const searchTopic = hasTopic ? hasTopic : this.topicList?.[0] || '';
            if (!searchTopic){
                this.clearData();
                return;
            }
            this.$refs['topicSearch'].onChangeSelect(searchTopic);
        },
        // 主题切换,partitionId切换
        async handleSelectChange(val, key) {
            // 主题
            if (key === 'topic') {
                this.topic =  val;
                await this.getFileData();

                this.tableLoading = true;

                const data = this.fileData?.[val] || [];
                const filters = [...new Set(data?.FilterFields?.map(v => v.Name) || [])];
                this.description1.details[0].data = {
                    Topic: data.Topic,
                    Desc: data.Desc,
                    PartitionCount: data.PartitionCount,
                    ReplicationFactor: data.ReplicationFactor,
                    Local: data.Local,
                    OrderLevel: data.OrderLevel,
                    BusinessVerify: data.BusinessVerify,
                    ReliableLevel: data.ReliableLevel,
                    LifeTime: data.LifeTime,
                    OffsetSave: data.OffsetSave
                };
                this.description2.details[0].data = {
                    PubCount: data.PubCount,
                    SubCount: data.SubCount,
                    SubscrCount: data.SubscrCount,
                    PushMsgCount: data.PushMsgCount,
                    PubMsgCount: data.PubMsgCount
                };
                this.description3.details[0].dataDic = [];
                filters.forEach(v => {
                    this.description3.details[0].dataDic.push({
                        key: v,
                        label: v,
                        span: 4
                    });
                });

                this.tableData =  Object.values(data?.PartitionsData || {})?.flat() || [];
                this.pubTableData = (data?.PubTableData || [])?.sort((a, b) => a?.PubName?.localeCompare(b?.PubName));
                this.subTableData = (data?.SubTableData || [])?.sort((a, b) => a?.SubName?.localeCompare(b?.SubName));

                setTimeout(() => {
                    this.tableLoading = false;
                }, 200);
                return;
            }
        },
        // 主题slot
        async getTopicList() {
            this.topic = '';
            this.topicList = [];
            const topicList = [];
            this.getTopicInfo = (await this.getTopicAPi())?.GetTopicInfo || [];
            const topics = [...new Set(this.getTopicInfo.map(o => o?.Topic))];
            topics.forEach((v) => {
                topicList.push({
                    label: v,
                    value: v
                });
            });
            this.topicList = topicList?.sort((a, b) => a?.value?.localeCompare(b?.value));
        },
        // 构建页面数据
        async getFileData() {
            this.fileData = {};
            const { GetPubMsgNo } = await this.getPubAPi();
            const { GetAllSubscribeInfo, GetTopicSubPubInfo } = await this.getAPi();
            const { GetIndexInfo } = await this.getIndexAPi();
            for (const v of Object.values(this.getTopicInfo)) {
                const key = v.Topic;
                if (key !== this.topic) continue;
                this.fileData[key] = [];
                // 基础信息
                const publishersInfos = GetPubMsgNo.filter(msg => {
                    const str = String(msg?.TopicPartition);
                    const topicNo = str.substring(0, str.length - 4);
                    return topicNo === String(v.TopicNo);
                });
                const publishers = publishersInfos.map(msg => msg.PubName);
                const subcriberInfos = GetAllSubscribeInfo.filter(msg => msg.TopicName === v.Topic);
                const subcribers = subcriberInfos.map(msg => msg.SubName + msg.SessionId);

                // 消费消息数--每个线程的数量都要相加，就是不同主机之间的也要相加
                const pushMsgCountInfo = GetTopicSubPubInfo.filter(msg => String(msg.TopicNo) === String(v.TopicNo) && String(msg.ThreadNo) !== '-1');
                const pushMsgCount = pushMsgCountInfo.map(v => v.PushMsgCount).reduce((a, b) => a + b, 0);
                // 发布消息数只取其中一个线程的就行，所有线程的数据都是复制的  取最大值
                const pubMsgCountInfo = GetTopicSubPubInfo.filter(msg => String(msg.TopicNo) === String(v.TopicNo) && String(msg.ThreadNo) === '0');
                const pubMsgCount = pubMsgCountInfo.map(v => v.PubMsgCount).sort((a, b) => b - a)?.[0] || 0;

                // 表格数据
                const partitionsData = this.getPartitionsData(GetIndexInfo, v);
                // 主题信息
                this.fileData[key] = {
                    ...v,
                    Local: v.Local ? '是' : '否',
                    OrderLevel: v.OrderLevel ? '分区有序' : '全局有序',
                    BusinessVerify: v.BusinessVerify ? '是' : '否',
                    ReliableLevel: v.ReliableLevel ? '文件' : '内存',
                    LifeTime: v.LifeTime,
                    OffsetSave: v.OffsetSave ? '是' : '否',
                    PubCount: [...new Set(publishers)]?.length,
                    SubCount: [...new Set(subcribers)]?.length,
                    SubscrCount: subcriberInfos?.length,
                    PushMsgCount: pushMsgCount,
                    PubMsgCount: pubMsgCount,
                    PartitionsData: partitionsData,
                    SubTableData: this.getSubData(v?.FilterFields, subcriberInfos),
                    PubTableData: publishersInfos
                };
            }
        },
        // 获取消费者详情表格数据
        getSubData(filterFields, subcriberInfos) {
            const subData = [];
            for (const sub of Object.values(subcriberInfos)) {
                const filters = {};
                Object.keys(sub).forEach(v => {
                    if (v.indexOf('Filter') !== -1) {
                        const idx = v.replace('Filter', '') || 0;
                        const name = idx && filterFields[idx - 1]?.Name;
                        name && (filters[name] = sub[v] || '-');
                    }
                });

                subData.push({
                    ...sub,
                    Filter: filters
                });
            }
            return subData;
        },
        // 获取表格数据
        getPartitionsData(getIndexInfo, topic) {
            const partitionsData = {};
            for (const [idx, item] of Object.entries(topic.Partitions)) {
                const key = '#' + idx;
                partitionsData[key] = [];
                const MsgNo = getIndexInfo?.filter(v => {
                    const str = String(v?.TopicNoPartNo);
                    const topicNo = str.substring(0, str.length - 4);
                    const partition = Number(str.substring(str.length - 4));
                    return topicNo === String(topic.TopicNo) && String(partition) === idx;
                })?.[0]?.CurOffset || 0;
                partitionsData[key] = [{
                    ...item,
                    PartitionId: idx,
                    MsgNo: MsgNo / 24
                }];
            }
            return partitionsData;
        },
        // 接口请求
        async getTopicAPi() {
            const funcNameList = ['GetTopicInfo'];
            const data = await getMcAPi(this.nodeData?.productInstances || [], funcNameList, 'ldp_mc', false);
            return data;
        },
        async getIndexAPi(){
            const funcNameList = ['GetIndexInfo'];
            const data = await getMcAPi(this.nodeData?.productInstances || [], funcNameList, 'ldp_mc', false);
            return data;
        },
        // 接口请求
        async getAPi() {
            const funcNameList = ['GetAllSubscribeInfo', 'GetTopicSubPubInfo'];
            const data = await getMcAPi(this.nodeData?.productInstances || [], funcNameList, 'ldp_mc');
            return data;
        },
        // 接口请求
        async getPubAPi() {
            const funcNameList = ['GetPubMsgNo'];
            const data = await getMcAPi(this.nodeData?.productInstances || [], funcNameList, 'ldp_mc');
            const res = removeDuplicates(data?.GetPubMsgNo || []);
            return { GetPubMsgNo: res };
        }
    }
};
</script>

<style lang="less" scoped>
.tab-box {
    .title {
        color: var(--font-color);
        font-size: 14px;
        width: 100%;
        height: 40px;
        line-height: 40px;
        margin: 0 10px;
        position: relative;

        .securities {
            position: absolute;
            width: auto;
            top: 9px;
            right: 80px;
            min-width: 200px;
        }

        button {
            position: absolute;
            top: 9px;
            right: 10px;
        }
    }

    .sub-table-tabs {
        background: var(--wrapper-color);
        margin-top: 15px;
        padding: 0 0 5px;

        .h-tabs-tabpane {
            height: 340px;
        }

        .self-table {
            padding: 0 10px;
        }

        .obs-table {
            height: auto;
        }
    }
}
</style>
