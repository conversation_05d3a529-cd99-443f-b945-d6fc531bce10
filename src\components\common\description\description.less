.descriptions-bar {
    width: 100%;
    background: var(--wrapper-color);
    margin-top: 15px;
}

.descriptions {
    width: 100%;
    padding: 5px 20px;
    overflow: hidden;

    .descriptions-title {
        color: var(--font-color);
        margin-bottom: 5px;
        font-weight: 600;
    }

    .descriptions-item {
        padding-right: 15px;
        height: 30px;
        line-height: 30px;

        .descriptions-item-content {
            display: flex;
            justify-content: flex-start;
            align-items: center;
            width: 100%;

            .descriptions-item-label {
                flex-grow: 0;
                flex-shrink: 0;
                color: var(--font-opacity-color);
            }

            .descriptions-item-value {
                // flex-grow: 1;
                overflow: hidden;
                color: var(--font-color);
                text-overflow: ellipsis;
                white-space: nowrap;
            }
        }
    }

    .slot-button.h-btn-text {
        padding: 0;
        margin: 0 0 0 5px;
        color: var(--link-color);

        :hover {
            color: var(--link-color);
            text-decoration: underline;
        }
    }
}

