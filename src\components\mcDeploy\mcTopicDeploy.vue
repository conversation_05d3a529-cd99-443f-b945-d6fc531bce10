<template>
    <div class="topic-box">
        <normal-table
            ref="tableRef"
            formTitle="筛选条件"
            tableTitle="查询结果"
            :hasSetTableColumns="false"
            :hasPage="false"
            :formItems="formItems"
            :columns="columns"
            :tableData="tableData"
            :loading="loading"
        >
        <div slot='btns' class='table-slot-box'>
            <a-button
                type="dark"
                @click="dynamicTopicSync"
            >
                动态主题同步
            </a-button>
        </div>
        </normal-table>

        <!-- 查看主题配置信息 -->
        <show-mc-topic-configure v-model="configureInfo.status" :modalData="configureInfo"></show-mc-topic-configure>
    </div>
</template>

<script>
import normalTable from '@/components/common/bestTable/normalTable/normalTable';
import aButton from '@/components/common/button/aButton';
import showMcTopicConfigure from './modal/showMcTopicConfigure.vue';
export default {
    name: 'McTopicDeploy',
    components: { normalTable, aButton, showMcTopicConfigure },
    props: {
        nodeData: {
            type: Object,
            default: () => {}
        }
    },
    data() {
        return {
            loading: false,
            formItems: [
                {
                    type: 'input',
                    key: 'TopicName',
                    label: '主题名',
                    value: '',
                    placeholder: '请输入主题名'
                },
                {
                    type: 'date',
                    key: 'date',
                    label: '上次更新日期',
                    value: '',
                    placement: 'bottom-end',
                    clearable: true
                }

            ],
            columns: [
                {
                    title: '主题名',
                    key: 'Topic',
                    ellipsis: true
                },
                {
                    title: '主题编号',
                    key: 'TopicNo',
                    ellipsis: true
                },
                {
                    title: '分区数',
                    key: 'PartitionCount',
                    ellipsis: true
                },
                {
                    title: '副本数',
                    key: 'ReplicationFactor',
                    ellipsis: true
                },
                {
                    title: '上次更新时间',
                    key: 'LastRecvTime',
                    ellipsis: true
                },
                {
                    title: '过滤条件',
                    key: 'FilterFields',
                    ellipsis: true
                },
                {
                    title: '操作',
                    key: 'Msg',
                    width: 80,
                    render: (h, params) => {
                        return h('div', [
                            h(
                                'Button',
                                {
                                    props: {
                                        type: 'text',
                                        size: 'small'
                                    },
                                    on: {
                                        click: () => {
                                            this.showTopicInfo(params.row);
                                        }
                                    }
                                },
                                '查看'
                            )
                        ]);
                    }
                }

            ],
            tableData: [],
            syncInfo: {
                status: false
            },
            configureInfo: {
                status: false
            }
        };
    },
    methods: {
        initData() {

        },
        // 动态主题同步
        dynamicTopicSync() {

        },
        // 查看主题详情
        showTopicInfo(row) {
            this.configureInfo = {
                status: true,
                ...row
            };
        }
    }
};
</script>

<style lang="less" scoped>
.table-slot-box {
    margin-right: 10px;
}
</style>
