<template>
    <div class="tab-box">
        <a-title title="告警规则配置"></a-title>
        <template v-if="APM_LICENSE !== apmLicense">
            <div style="height: calc(100% - 90px);">
                <no-data text="该功能为商业版特性,请咨询销售获取商业版本开通策略" />
            </div>
        </template>
        <template v-else>
            <div v-if="productMonitorList.length" class="line-box">
                <h-select v-model="productMonitorId" :clearable="false" style="width: 175px; margin-left: 10px;"
                    @on-change="handleMonitorChange">
                    <h-option v-for="item in productMonitorList" :key="item.monitorType"
                        :value="item.monitorType">{{ item.monitorName }}</h-option>
                </h-select>
                <monitor-html v-if="monitorHtmlStatus" ref="monitorHtml" :placeholder="defalutPlaceholder"
                    :monitorData="monitorData" :monitorDict="monitorDict" @ch-change="handleSelect"
                    @ch-blur="handleBlur"></monitor-html>
                <a-button type="dark" :loading="addRuleLoading" @click="addProductMonitorConfig">添加规则</a-button>
            </div>
            <a-title v-if="productMonitorList.length" :title="`告警规则执行 共 ${monitorTotal} 条`"
                style="margin-top: 10px;">
                <slot>
                    <a-button type="dark" style="margin-left: 20px;"
                        @click="importMonitorConfig">导入规则</a-button>
                    <a-button type="dark" @click="clearMonitorConfig">清空规则</a-button>
                    <h-switch v-model="monitorEnable" size="small" style="position: absolute; right: 5px; top: 10px;"
                        @on-change="monitorConfigAllSwitchChange"></h-switch>
                </slot>
            </a-title>
            <div v-for="ele in alertRuleGroups" :key="ele.groupId" class="group-box">
                <div class="group-title">{{ ele.groupDesc }}</div>
                <ul class="tab-box" style="overflow: hidden;">
                    <li v-for="(item, idx) in ele.metrics" :key="idx" @mouseenter="item.optStatus = true"
                        @mouseleave="item.optStatus = false">
                        <div class="inline-html" v-html="item.vHtml"></div>
                        <h-icon v-show="item.optStatus" name="t-b-modify" color="#999" size="18"
                            @on-click="handleMonitorConfigEdit(item, ele.groupId)"></h-icon>
                        <h-icon v-show="item.optStatus" name="trash" color="#999" size="18"
                            @on-click="deleteMonitorConfig(item.metricsId)"></h-icon>
                        <h-switch v-model="item.enable" size="small"
                            @on-change="(val) => { monitorConfigSingleSwitchChange(val, item, ele.groupId) }"></h-switch>
                    </li>
                </ul>
            </div>
            <div v-if="productMonitorList.length === 0" style="height: calc(100% - 90px);">
                <no-data text="当前产品暂无监控告警配置" />
            </div>
        </template>
        <monitorConfigModal v-if="monitorInfo.status" :modalInfo="monitorInfo" @handleEditChange="handleDataChange" />
        <importMonitorModal v-if="importInfo.status" :modalInfo="importInfo" :productList="productList"
            @handleImportChange="handleDataChange" />
    </div>
</template>

<script>
import _ from 'lodash';
import { getMonitorDictQuery, addProductMonitorConfig, clearMonitorConfig, updateMonitorConfig, monitorConfigSwitchChange, getProductMonitorConfig, deleteMonitorConfig } from '@/api/httpApi';
import monitorHtml from '@/components/ldpProduct/monitor/monitorHtml';
import aButton from '@/components/common/button/aButton';
import aTitle from '@/components/common/title/aTitle';
import noData from '@/components/common/noData/noData';
import importMonitorModal from '@/components/ldpProduct/ldpModal/importMonitorModal.vue';
import monitorConfigModal from '@/components/ldpProduct/ldpModal/monitorConfigModal.vue';
import { mapState } from 'vuex';
export default {
    name: 'ProductMonitorConfig',
    props: {
        productInfo: {
            type: Object,
            default: () => {}
        },
        productList: {
            type: Array,
            default: () => []
        }
    },
    data() {
        return {
            productMonitorList: [],         // 产品监控规则配置列表
            productMonitorId: '',           // 产品监控配置ID
            monitorHtmlStatus: true,
            defalutPlaceholder: {},
            monitorData: {},
            monitorDict: [],
            monitorTotal: 0,
            monitorEnable: false,
            addRuleLoading: false,
            alertRuleGroups: [],            // 产品监控配置规则
            importInfo: {
                status: false
            },
            monitorInfo: {
                status: false,
                data: {}
            }
        };
    },
    async mounted() {
        // 获取监控配置枚举
        const { data } = await getMonitorDictQuery();
        this.monitorDict = data;
    },
    methods: {
        initData() {
            // 清理规则手动配置缓存，防止脏数据
            this.monitorHtmlStatus = false;
            this.$nextTick(() => {
                this.monitorHtmlStatus = true;
                this.$refs['monitorHtml'] && this.$refs['monitorHtml'].init();
                this.getProductMonitorConfig(this.productInfo.id);
            });
        },
        // 监控告警配置全量开关
        async monitorConfigAllSwitchChange(val) {
            const res = await monitorConfigSwitchChange({
                productId: this.productInfo.id,
                enable: val
            });
            res.success && this.getProductMonitorConfig(this.productInfo.id, this.productMonitorId);
        },
        // 根据监控类型code获取监控名称
        getMonitorNameByCode(code) {
            return this.monitorDict.length ? _.find(this.monitorDict[1].dicts, ['code', code]).desc : '';
        },
        // 根据产品id获取监控配置列表
        async getProductMonitorConfig(productId, monitorId) {
            const { data } = await getProductMonitorConfig({ productId });
            this.monitorTotal = data?.alertRuleExecute?.count || 0;
            this.monitorEnable = data?.alertRuleExecute?.enable || false;
            // 处理告警规则列表
            this.alertRuleGroups = data?.alertRuleExecute?.monitorConfig || [];
            this.alertRuleGroups.forEach(item => {
                Array.isArray(item.metrics) && item.metrics.forEach(ele => {
                    const types = _.find(this.monitorDict, ['categoryCode', 'monitorType'])?.dicts || [];
                    const typeName = _.find(types, ['code', ele.monitorType])?.desc || '';
                    const vHtml = `<span style="font-weight: bold; padding-right: 20px;">${typeName}</span>${ele.metricsDesc.replace(/\${.*?\}/g, (t, i) => {
                        const type = t.slice(2, -1);
                        let name = ele.placeholder[type];
                        if (type === 'metricsName') {
                            const list = _.find(this.monitorDict, ['categoryCode', 'metricsName'])?.dicts || [];
                            name = _.find(list, ['code', ele.placeholder[type]])?.desc || '';
                        }
                        return `<span style="
                                    padding: 2px 4px;
                                    margin: 0 2px;
                                    font-style: normal;
                                    color: #fff;
                                    background: #485565;
                                    border-radius: 2px;">${name}</span>`;
                    })}`;
                    ele.vHtml = vHtml;
                    this.$set(ele, 'optStatus', false);
                });
            });
            // 处理告警规则配置
            this.productMonitorList = data?.alertRuleConfig || [];
            this.productMonitorList.forEach(item => {
                item.monitorName = this.getMonitorNameByCode(item.monitorType);
                item.values = {};
                Object.keys(item.placeholder).forEach(ele => {
                    item.values[ele] = '';
                });
            });
            this.productMonitorId = monitorId || this.productMonitorList?.[0]?.monitorType;
            // 如果是修改或者删除的话，不需要重置组件
            !monitorId && this.productMonitorId && this.handleMonitorChange(this.productMonitorId);
        },
        // 切换监控类型选择
        handleMonitorChange(id) {
            for (const ele of this.productMonitorList) {
                if (ele.monitorType === id) {
                    this.defalutPlaceholder = {};
                    Object.keys(ele.values).forEach(item => {
                        ele.values[item] = ele.placeholder[item].defaultValue;
                        this.defalutPlaceholder[item] = ele.placeholder[item].defaultValue;
                    });
                    this.monitorData = ele;
                    this.$nextTick(() => {
                        this.$refs['monitorHtml'].init();
                    });
                    break;
                }
            }
        },
        // 监控告警配置新增
        async addProductMonitorConfig() {
            // 判断用户是否未输入
            for (const item of Object.keys(this.monitorData.values)) {
                if (!this.monitorData.values[item]) {
                    this.$hMessage.error('填入参数不得为空！');
                    return;
                }
            }
            const param = {
                ...this.monitorData,
                productId: this.productInfo.id,
                enable: true
            };
            delete param.placeholder;
            param.placeholder = { ...param.values };
            this.addRuleLoading = true;
            const res = await addProductMonitorConfig(param);
            this.addRuleLoading = false;
            if (res.success) {
                this.getProductMonitorConfig(param.productId, this.productMonitorId);
                this.$hMessage.success('添加规则成功！');
            }
        },
        // 清空告警规则
        clearMonitorConfig() {
            this.$hMsgBoxSafe.confirm({
                title: `您确定清空当前产品的监控告警规则吗？`,
                onOk: async () => {
                    const res = await clearMonitorConfig({
                        productId: this.productInfo.id
                    });
                    if (res.success) {
                        this.getProductMonitorConfig(this.productInfo.id, this.productMonitorId);
                        this.$hMessage.success('告警规则清除成功！');
                    }
                }
            });
        },
        // 修改告警规则
        handleMonitorConfigEdit(item, groupId) {
            this.monitorInfo.status = true;
            this.monitorInfo.data = {
                ..._.cloneDeep(item),
                productId: this.productInfo.id,
                monitorGroupId: groupId
            };
            this.monitorInfo.monitorData = _.find(this.productMonitorList, ['monitorType', item.monitorType]);
            this.monitorInfo.monitorDict = this.monitorDict;
        },
        // 监控告警规则删除
        deleteMonitorConfig(metricsId) {
            this.$hMsgBoxSafe.confirm({
                title: `您确定删除当前选中的监控告警规则吗？`,
                onOk: async () => {
                    const res = await deleteMonitorConfig({
                        productId: this.productInfo.id,
                        metricsId
                    });
                    if (res.success) {
                        this.getProductMonitorConfig(this.productInfo.id, this.productMonitorId);
                        this.$hMessage.success('删除成功！');
                    }
                }
            });
        },
        // 监控告警规则导入
        importMonitorConfig() {
            this.importInfo.productId = this.productInfo.id;
            this.importInfo.status = true;
        },
        // 监控告警单个开关
        async monitorConfigSingleSwitchChange(val, data, monitorGroupId) {
            data.enable = val;
            const param = {
                ...data,
                productId: this.productInfo.id,
                desc: data.metricsDesc,
                monitorGroupId
            };
            const res = await updateMonitorConfig(param);
            res.success && this.getProductMonitorConfig(this.productInfo.id, this.productMonitorId);
        },
        // 修改告警规则回调
        handleDataChange() {
            this.getProductMonitorConfig(this.productInfo.id, this.productMonitorId);
        },
        // 自定义下拉事件回调
        handleSelect(val, name) {
            this.monitorData.values[name] = val;
        },
        // 自定义输入框事件回调
        handleBlur(val, name) {
            this.monitorData.values[name] = val.trim();
        }
    },
    components: {
        monitorHtml, aButton, aTitle, importMonitorModal, monitorConfigModal, noData
    },
    computed: {
        ...mapState({
            apmLicense: state => {
                return state.apmLicense || '';
            }
        })
    }
};
</script>

<style lang="less" scoped>
.tab-box {
    position: relative;
    width: 100%;
    height: 100% !important;
    overflow-y: auto;
    cursor: pointer;

    & > li {
        position: relative;
        display: flex;
        align-items: center;
        flex-wrap: wrap;
        width: 100%;
        box-sizing: border-box;
        padding: 0 12px;
        font-size: var(--font-size-base);
        color: var(--font-color);

        & > .inline-html {
            display: inline-block;
            max-width: calc(100% - 75px);
            line-height: 40px;
            white-space: break-word;
        }

        & > .h-icon {
            padding-left: 4px;
        }

        & > .h-switch {
            position: absolute;
            right: 5px;
            top: 12px;
        }
    }

    & > li:hover {
        background: var(--link-opacity-color);
    }

    .line-box {
        position: relative;
        width: calc(100% - 100px);
        height: auto;
        line-height: 40px;
        padding: 10px 10px 0;
        box-sizing: border-box;

        & > span {
            font-size: var(--font-size-base);
            color: var(--font-color);
        }

        /deep/ .h-input {
            // top: -4px;
        }

        & > button {
            position: absolute;
            top: 19px;
            right: -90px;
        }
    }

    .group-box {
        margin-top: 15px;
        background: var(--primary-color);
        border-radius: 2px;

        & > .group-title {
            line-height: 40px;
            font-weight: bold;
            padding-left: 10px;
            color: var(--font-color);
            border-bottom: var(--border);
        }
    }
}
</style>
