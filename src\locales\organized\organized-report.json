{"summary": {"totalCategories": 3, "totalRoutes": 88, "totalTexts": 2045, "organizeTime": "2025-07-31T05:49:38.986Z"}, "categories": {"common": {"routeCount": 0, "textCount": 0, "routes": {}}, "pages": {"routeCount": 56, "textCount": 629, "routes": {"accordMonitor": {"count": 7}, "accordObservation": {"count": 3}, "analyseConfig": {"count": 10}, "analyseData": {"count": 45}, "apmMonitorConfig": {"count": 2}, "appRunningState": {"count": 19}, "brokerDataLimit": {"count": 12}, "displaySettingDrawer": {"count": 12}, "index": {"count": 31}, "createRule": {"count": 48}, "dataSecondAppearance": {"count": 5}, "addModal": {"count": 3}, "helpModal": {"count": 1}, "routeConfig": {"count": 19}, "routeInfoForm": {"count": 6}, "latencyTrendAnalysis": {"count": 28}, "ldpDataObservation": {"count": 7}, "ldpLinkConfig": {"count": 9}, "ldpLogCenter": {"count": 5}, "clusterMonitor": {"count": 2}, "ldpAppMonitor": {"count": 1}, "ldpTable": {"count": 6}, "locateConfig": {"count": 3}, "managementQuery": {"count": 30}, "marketAllLink": {"count": 9}, "marketMonitor": {"count": 11}, "marketNodeDelayList": {"count": 14}, "marketPenetrateList": {"count": 28}, "marketTimeDelay": {"count": 10}, "mcDataObservation": {"count": 1}, "mcDeploy": {"count": 2}, "detailDrawer": {"count": 11}, "exportHistory": {"count": 14}, "exportTable": {"count": 4}, "mdbDataObservation": {"count": 0}, "mdbPrivilegeManage": {"count": 31}, "businessMonitor": {"count": 7}, "networkSendAndRecevied": {"count": 4}, "noticeManagerList": {"count": 9}, "productDataStorage": {"count": 15}, "productServiceList": {"count": 3}, "productTimeDetail": {"count": 16}, "productTimeSummary": {"count": 5}, "rcmBacklogMonitor": {"count": 3}, "rcmDeploy": {"count": 4}, "rcmObservation": {"count": 5}, "publishStatusDetail": {"count": 12}, "smsList": {"count": 38}, "sqlCores": {"count": 15}, "sqlTable": {"count": 23}, "threadInfoOverview": {"count": 1}, "topoMonitor": {"count": 3}, "transaction": {"count": 10}, "tripartiteServiceList": {"count": 3}, "ustTableVerification": {"count": 2}, "common": {"count": 2}}}, "components": {"routeCount": 32, "textCount": 1416, "routes": {"accordObservation": {"count": 10}, "analyse": {"count": 63}, "analyseConfig": {"count": 47}, "appBindingCore": {"count": 15}, "coreReplayObservation": {"count": 106}, "endpointConfig": {"count": 25}, "latencyTrendAnalysis": {"count": 17}, "ldpDataObservation": {"count": 19}, "ldpLinkConfig": {"count": 107}, "ldpLogCenter": {"count": 68}, "ldpMonitor": {"count": 70}, "ldpTable": {"count": 69}, "locateConfig": {"count": 33}, "managementQuery": {"count": 63}, "marketAllLink": {"count": 33}, "mcDataObservation": {"count": 133}, "mcDeploy": {"count": 6}, "mdbDataObservation": {"count": 50}, "mdbPrivilegeManage": {"count": 36}, "networkSendAndRecevied": {"count": 56}, "productDataStorage": {"count": 66}, "productServiceConfig": {"count": 41}, "productTimeAnalysis": {"count": 13}, "rcmBacklogMonitor": {"count": 2}, "rcmDeploy": {"count": 86}, "rcmObservation": {"count": 60}, "secondAppearance": {"count": 4}, "sms": {"count": 19}, "sqlTable": {"count": 13}, "transaction": {"count": 28}, "tripartiteServiceConfig": {"count": 13}, "ustTableVerification": {"count": 45}}}}}