/*
 * 自定义Table组件样式
*/
.tree-table {
    width: 100%;

    .h-editgird {
        background: none;
        border-color: var(--table-border-color);
        margin: 10px 0;

        &::before {
            display: none;
        }

        &::after {
            display: none;
        }
    }

    .h-editgird table {
        width: 100% !important;
    }

    .h-editgird-wrapper {
        width: 100%;
        height: auto;
        max-height: calc(100% - 50px);
        border: none;
    }

    .h-editgird th {
        background: var(--primary-color);
        border-bottom: 1px solid #31364a;
        border-right-color: #383c51;
        color: var(--font-color);
        font-size: var(--font-size);
        font-weight: var(--font-weight);
    }

    .h-editgird td {
        border-bottom: 1px solid #31364a;
        border-right: 1px solid transparent;
        background: var(--input-bg-color);
        color: var(--font-color);
    }

    .h-editgird-row-checked,
    .h-editgird-row-checked:hover,
    .h-editgird-row-highlight,
    .h-editgird-row-highlight:hover,
    .h-editgird-row-hover,
    .h-editgird-row-hover td {
        background: var(--link-color) !important;
    }

    .h-editgird-row-hover td button {
        color: var(--table-button-hover-bgcolor) !important;
    }

    .h-editgird-row-hover .h-btn-text {
        color: var(--font-color);
    }

    td .h-btn-text:hover {
        color: var(--link-color);
        text-decoration: underline;
    }

    .h-editgird-tiptext {
        width: 100% !important;
    }

    .h-editgird-wrapper > .h-spin-fix {
        position: absolute;
        top: 0;
        left: 0;
        z-index: 8;
        width: 100%;
        height: 100%;
        border: var(--table-border);
        background-color: var(--input-bg-color);
    }

    .h-editgird-fixed-body-shadow {
        border: none;
    }

    .h-editgird-fixed-right-patch {
        background-color: var(--input-bg-color);
        border-bottom: var(--table-border);
    }
}
