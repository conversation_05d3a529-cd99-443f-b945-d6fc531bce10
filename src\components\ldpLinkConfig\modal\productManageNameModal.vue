<!--
 * @Description: 修改产品信息
-->
<template>
    <div>
        <h-msg-box-safe v-model="modalData.status" :escClose="true" :mask-closable="false" title="更新产品信息" width="600"
            @on-open="getCollections" @submit.native.prevent>
            <h-form ref="formValidate" :model="formValidate" :label-width="100">
                <h-form-item
                    v-if="modalData.type === 0"
                    label="节点名称："
                    prop="productName"
                    required
                    :validRules="stringRule">
                    <h-input
                        v-model.trim="formValidate.productName"
                        placeholder="请输入节点名称">
                    </h-input>
                </h-form-item>

                <h-form-item
                    v-if="modalData.type === 3"
                    label="关联业务系统："
                    prop="bizSysTypes"
                    :label-width="120"
                    required>
                    <h-select
                        v-model="formValidate.bizSysTypes"
                        multiple
                        placeholder="请选择关联业务系统"
                        placement="bottom"
                        :clearable="false">
                        <h-option
                            v-for="item in bizSysTypeList"
                            :key="item.key"
                            :value="item.key"
                            >{{ item.name }}
                        </h-option>
                    </h-select>
                </h-form-item>
            </h-form>

            <template v-slot:footer>
                <a-button @click="modalData.status = false">取消</a-button>
                <a-button type="primary" :loading="loading" @click="submitConfig">确定</a-button>
            </template>
        </h-msg-box-safe>
    </div>
</template>

<script>
import { stringLengthRule } from '@/utils/validate';
import { addOrUpdateProduct } from '@/api/httpApi';
import aButton from '@/components/common/button/aButton';
export default {
    props: {
        modalInfo: {
            type: Object,
            default: null
        }
    },
    data() {
        return {
            modalData: this.modalInfo,
            loading: false,
            formValidate: {
                productName: '',
                bizSysTypes: []
            },
            stringRule: [{ test: stringLengthRule(50), trigger: 'change, blur' }]
        };
    },
    methods: {
        async getCollections() {
            this.$refs.formValidate.resetFields();
            this.formValidate.productName = this.modalData.productName;
            this.formValidate.bizSysTypes = [...this.modalData.bizSysTypes] || [];
        },
        submitConfig() {
            const that = this;
            this.$refs['formValidate'].validate(async (valid) => {
                if (valid) {
                    if (this.formValidate?.productName?.length > 50) {
                        this.$hMessage.error('字符长度数不得超过50！');
                        return;
                    }
                    that.loading = true;
                    try {
                        const params = {
                            id: this.modalInfo.id,
                            ...this.formValidate
                        };
                        const res = await addOrUpdateProduct(params);
                        if (res.success) {
                            this.$emit('update', this.modalInfo.id);
                            that.$hMessage.success('更新成功!');
                            that.modalInfo.status = false;
                        }
                    } catch (err) {
                        that.loading = false;
                    }
                }
            });
        }
    },
    computed: {
        bizSysTypeList() {
            const list = [];
            const dicName = `${this.modalData.productType}BizSysTypeDict`;
            Object.keys(this.$store?.state?.apmDirDesc?.[dicName] || {}).forEach(ele => {
                list.push({
                    key: ele,
                    name: this.$store?.state?.apmDirDesc?.[dicName][ele]
                });
            });
            return list;
        }
    },
    components: { aButton }
};
</script>
<style lang="less" scoped>
    p {
        color: var(--font-opacity-color);
    }

</style>
