<template>
  <div>
    <h-msg-box-safe
      v-model="modalData.status"
      :escClose="true"
      :mask-closable="false"
      title="表格列显示配置"
      width="300"
      height="350"
    >
      <h-checkbox-group v-model="checkedList" @on-change="onCheckChange">
        <draggable
          v-model="configList"
          class="drag-area"
          handle=".drag-handle"
          @end="onEnd"
        >
          <transition-group>
            <div v-for="item in configList" :key="item.key"  class="list-item">
              <h-checkbox
                v-model="item.isChecked"
                :label="item.key"
                :disabled="item.disabled"
              >
                <span style="color: var(--main-color);">{{ item.title }}</span>
              </h-checkbox>
              <span class="icon drag-handle">
                <h-icon name="navicon" color="var(--main-color)"></h-icon>
              </span>
            </div>
          </transition-group>
        </draggable>
      </h-checkbox-group>
      <template v-slot:footer>
          <a-button @click="modalData.status = false">取消</a-button>
          <a-button type="primary" @click="setNewConfigList">确认</a-button>
      </template>
    </h-msg-box-safe>
  </div>
</template>

<script>
import draggable from 'vuedraggable';
import aButton from '@/components/common/button/aButton';

export default {
    props: {
        modalData: {
            type: Object,
            default: function () {
                return {};
            }
        }
    },
    components: {
        draggable,
        aButton
    },
    data() {
        return {
            checkedList: this.modalData?.defaultCheckedList || [], // 默认勾选项
            configList: this.modalData.configList // 需要拖拽的列表数据
        };
    },
    mounted(){
    },
    methods: {
        // 拖拽结束后的列表顺序
        onEnd(_event) {
            // console.log('拖拽结束后的列表顺序: ', this.configList);
        },
        // 切换勾选项
        onCheckChange(_val) {
        },
        // configList 拖拽结束后的列表 checkedList 勾选中的选项key集合
        setNewConfigList(){
            this.$emit('set-config', this.configList, this.checkedList);
            this.modalData.status = false;
        }
    }
};
</script>

<style lang="less" scoped>

.drag-area {
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.list-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 10px;

    &:hover {
        background: #cacfd46b;
    }
}

.icon {
    cursor: pointer;
}

.drag-handle {
    cursor: grab;
}

.drag-handle:active {
    cursor: grabbing;
}

/deep/ .h-modal-body {
    padding: 16px;
}
</style>
