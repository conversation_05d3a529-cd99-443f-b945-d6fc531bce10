export default {
  "common": {
    "warningItemRowApplicationDescription": "没有找到框架应用事件总线,如果业务应用为独立运行请忽略这条警告！",
    "label": "对象数组",
    "item": "混合",
    "emptyItem": "空数组",
    "emptyItem1": "空对象",
    "typeBasic": "基础类型",
    "label1": "复杂对象",
    "securities": "证券",
    "item1": "两融",
    "option": "期权",
    "futures": "期货",
    "riskControl": "风控",
    "quote": "行情",
    "securitiesTradeLabel": "上海证券交易所",
    "monitorRowMessage": "上交所行情时延监控",
    "securitiesTradeLabel1": "深圳证券交易所",
    "monitorRowMessage1": "深交所行情时延监控",
    "tradeMessage": "郑州商品交易所",
    "tradeMessage1": "大连商品交易所",
    "futuresTradeLabel": "上海期货交易所",
    "futuresTradeMessage": "中国金融期货交易所",
    "tradeDescription": "上海国际能源交易中心股份有限公司",
    "dayItem": "全天",
    "item2": "上午",
    "item3": "下午",
    "label2": "自定义",
    "coreItem": "核心应答",
    "confirmCore": "核心确认",
    "confirmTradeItem": "交易所确认",
    "tradeDealItem": "交易所成交",
    "item4": "NSQ链路",
    "item5": "FPGA链路",
    "orderMessage": "委托全链路时延",
    "systemMessage": "系统内部全链路时延",
    "rowOrderLabel": "委托上行时延",
    "rowSystemMessage": "系统内部上行时延",
    "tradeMessage2": "交易所全链路时延",
    "rowSystemMessage1": "系统内部下行时延",
    "rowOrderLabel1": "委托下行时延",
    "item6": "默认",
    "strategyCustomerItem": "策略客户端",
    "label3": "前置机",
    "item7": "报盘",
    "coreTradeLabel": "交易核心和报盘",
    "tradeLabel": "交易所网关",
    "message": "深交所网关",
    "rowLabel": "FPGA行情解码器",
    "riskcontrolItem": "风控前置",
    "coreRiskcontrol": "风控核心",
    "coreLabel": "收益互换核心",
    "message1": "终端总时延",
    "rowCustomerLabel": "客户端上行时延",
    "rowCustomerLabel1": "客户端下行时延",
    "customerLabel": "客户端总时延",
    "customerMessage": "客户端下游处理时延",
    "rowCoreMessage": "收益互换核心上行时延",
    "rowCoreMessage1": "收益互换核心下行时延",
    "coreMessage": "收益互换核心总时延",
    "coreDescription": "收益互换核心下游处理时延",
    "rowMessage": "前置机上行时延",
    "rowMessage1": "前置机下行时延",
    "message2": "前置机总时延",
    "description": "前置机下游处理时延",
    "rowRiskcontrolMessage": "前置机风控上行时延",
    "rowMessage2": "⻛控前置机上行时延",
    "rowMessage3": "⻛控前置机下行时延",
    "message3": "⻛控前置机总时延",
    "description1": "⻛控前置下游处理时延",
    "rowLabel1": "⻛控上行时延",
    "rowLabel2": "⻛控下行时延",
    "label4": "⻛控总时延",
    "message4": "⻛控下游处理时延",
    "rowCoreLabel": "核心上行时延",
    "rowCoreLabel1": "核心下行时延",
    "coreLabel1": "核心总时延",
    "coreMessage1": "核心下游处理时延",
    "rowMessage4": "报盘上行时延",
    "rowMessage5": "报盘下行时延",
    "message5": "报盘总时延",
    "message6": "报盘下游处理时延",
    "message7": "全链路总时延",
    "rowLabel3": "柜台上行",
    "rowLabel4": "柜台下行",
    "rowMessage6": "防火墙穿透上行时延",
    "rowMessage7": "防火墙穿透下行时延",
    "rowTradeLabel": "行情-交易总时延",
    "rowTradeLabel1": "交易-行情总时延",
    "tradeLabel1": "交易所总时延",
    "rowMessage8": "TGW网关穿透上行时延",
    "rowMessage9": "TGW网关穿透下行时延",
    "message8": "TGW网关穿透总时延",
    "applicationLabel": "应用时延度量",
    "message9": "穿透时延度量",
    "message10": "自动化性能测试",
    "monitorProductManage": "产品监控管理",
    "configManage": "RCM配置管理",
    "dataManageItem": "内存数据管理",
    "versionNodeApplication": "应用节点版本",
    "serviceDeployItem": "部署服务器",
    "label5": "集成方式",
    "address": "KafKa地址",
    "address1": "Promethus地址",
    "item8": "Topic主题",
    "dataMessage": "时延数据存储索引",
    "dataMessage1": "时延数据接收端口号",
    "logNodeLabel": "时延日志输出节点",
    "logDirectoryItem": "日志输出目录",
    "logFileMessage": "时延日志文件名关键字",
    "rowTradeLabel2": "交易api上行时延",
    "tradeMessage3": "交易api下游处理时延",
    "tradeLabel2": "交易api总时延",
    "rowTradeLabel3": "交易api下行时延",
    "rowTradeMessage": "交易前置机上行时延",
    "rowCoreTradeMessage": "交易前置机转发交易核心上行时延",
    "tradeDescription1": "交易前置机下游处理时延",
    "tradeMessage4": "交易前置机总时延",
    "rowTradeMessage1": "交易前置机下行时延",
    "rowRiskcontrolMessage1": "风控前置机上行时延",
    "rowTradeRiskcontrolDescription": "风控前置机转发交易网关上行时延",
    "riskcontrolDescription": "风控前置机下游处理时延",
    "riskcontrolMessage": "风控前置机总时延",
    "rowRiskcontrolMessage2": "风控前置机下行时延",
    "rowCoreRiskcontrolLabel": "风控核心上行时延",
    "coreRiskcontrolMessage": "风控核心下游处理时延",
    "coreRiskcontrolLabel": "风控核心总时延",
    "rowCoreRiskcontrolLabel1": "风控核心下行时延",
    "rowCoreTradeLabel": "交易核心上行时延",
    "coreTradeLabel1": "交易核心总时延",
    "rowCoreTradeLabel1": "交易核心下行时延",
    "coreTradeMessage": "交易核心下游处理时延",
    "rowCoreManageDescription": "竞价核心接收额度管理核心上行时延",
    "rowCoreMessage2": "竞价核心下行时延",
    "coreMessage2": "竞价核心总时延",
    "coreMessage3": "竞价核心下游时延",
    "rowTradeMessage2": "交易报盘上行时延",
    "tradeMessage5": "交易报盘总时延",
    "tradeMessage6": "交易报盘下游处理时延",
    "rowTradeMessage3": "交易报盘下行时延",
    "rowMessage10": "报盘总线上行时延",
    "message11": "报盘总线总时延",
    "description2": "报盘总线下游处理时延",
    "rowMessage11": "报盘总线下行时延",
    "rowCoreManageMessage": "额度管理核心上行时延",
    "coreMessage4": "竞价核心处理总时延",
    "item9": "中文",
    "abnormalMenuMessage": "菜单结构初始化异常:",
    "infoUserItem": "用户信息表",
    "detailLabel": "订单详情表"
  },
  "api": {
    "message": "网络请求超时",
    "abnormalItem": "网络异常",
    "abnormalService": "服务异常"
  },
  "components": {
    "accordObservation": {
      "coreMessage": "主备核心事务差量",
      "message": "回库事务差量",
      "role": "角色",
      "label": "事务号",
      "coreMessage1": "与主核心事务差量",
      "countLabel": "事务量差(个)",
      "coreMessage2": "备核心事务最大差量",
      "message1": "回库事务最大差量"
    },
    "analyse": {
      "message": "新建测试用例",
      "nameItem": "用例名称",
      "namePleaseinputLabel": "请输入测试用例名称",
      "description": "字符长度数不得超过20！",
      "createSuccessItem": "用例创建成功!",
      "createFailedItem": "用例创建失败!",
      "cancel": "取消",
      "item": "确定",
      "label": "新建场景",
      "nameItem1": "场景名称",
      "namePleaseinputItem": "请输入场景名称",
      "createSuccessItem1": "场景创建成功!",
      "createFailedItem1": "场景创建失败!",
      "saveExportInfoDescription": "请确保当前场景已导出保存，新建的场景将覆盖当前使用的场景信息！",
      "configItem": "账号配置",
      "pleaseinputLabel": "请输入起始账号",
      "item1": "账号...",
      "confirmContentMessage": "请确认输入内容为数字！",
      "updateSuccessLabel": "用例账号更新成功!",
      "updateLabel": "用例账号更新!",
      "fundItem": "资金账号",
      "import": "导入",
      "label1": "起始账号",
      "countLabel": "生成个数",
      "label2": "自动生成",
      "label3": "股东账号",
      "label4": "上交所",
      "label5": "深交所",
      "codeSecurities": "证券代码",
      "configItem1": "配置用例",
      "configPleaseinputItem": "请输入用例配置...",
      "saveFailedConfigItem": "用例配置保存失败!",
      "updateRemarkInstance": "更新实例备注",
      "title": "标题",
      "nameTitlePleaseinput": "请输入标题名称",
      "content": "内容",
      "updateSuccessRemark": "备注更新成功!",
      "updateFailedRemark": "备注更新失败!",
      "listItem": "事件列表",
      "detailItem": "事件详情",
      "tipContent": "提示内容",
      "copySuccess": "复制成功",
      "copy": "复制id",
      "copyMessage": "该浏览器不支持复制",
      "detail": "详情",
      "importInfoItem": "导入账号信息",
      "uploadFileDescription": "点击或将文件拖拽到这里上传",
      "importConfigFileItem": "导入场景配置文件",
      "saveExportImportInfoDescription": "请确保当前场景已导出保存，新导入的场景将覆盖当前使用的场景信息！",
      "configReport": "配置报表",
      "orderItem": "委托回路",
      "label6": "可视跨度",
      "metricItem": "可视指标",
      "label7": "度量单位",
      "item2": "最小（min）",
      "label8": "中位数（p50）",
      "item3": "平均（avg）",
      "item4": "95分位（p95）",
      "item5": "99分位（p99）",
      "item6": "最大（max）",
      "label9": "标准差（stdDeviation）",
      "secondItem": "纳秒（ns）",
      "secondItem1": "微秒（μs）",
      "secondItem2": "毫秒（ms）",
      "reportItem": "报表对比",
      "selectInstanceItem": "选择对比实例",
      "selectInstanceItem1": "选择测试实例",
      "searchReportItem": "搜索测试报告...",
      "nameInstance": "实例名称",
      "productNode": "产品节点",
      "dateItem": "测试日期",
      "selectInstanceItem2": "请选择实例！",
      "updateNameInstanceItem": "更新测试实例名称",
      "namePleaseinputInstanceItem": "请输入测试实例名称",
      "description1": "字符长度数不得超过30！",
      "updateSuccessNameInstance": "实例名称更新成功!",
      "updateFailedInstance": "实例更新失败!"
    },
    "analyseConfig": {
      "infoNoneInstanceMessage": "当前实例无采集时延走势信息",
      "empty": "空",
      "none": "暂无",
      "label": "最大时延",
      "hourItem": "最小时延",
      "label1": "平均时延",
      "message": "中位数时延",
      "label2": "99分位时延",
      "queryFailedListInstance": "查询实例列表失败!",
      "detailAnalysisPageInstanceDescription": "您确定要跳转到该实例详情分析页面吗？",
      "instanceDescription": "您确定停止当前测试实例？",
      "successInstanceLabel": "测试实例停止成功!",
      "valueItem": "最大值",
      "valueItem1": "最小值",
      "valueItem2": "平均值",
      "label3": "中位数",
      "label4": "99分位数",
      "instanceLabel": "测试实例名",
      "infoItem": "事件信息",
      "statusItem": "测试状态",
      "statusReport": "报表状态",
      "label5": "停止测试",
      "remarkItem": "填写备注",
      "toolItem": "发单工具",
      "pleaseinputToolItem": "请输入发单工具",
      "deployItem": "部署到",
      "addressPleaseinputDeploy": "请输入部署地址",
      "instanceDeployItem": "部署实例数",
      "pleaseinputDeployLabel": "请输入部署示例数",
      "pleaseSelect": "请选择",
      "nodeApplication": "应用节点",
      "item": "接入IP",
      "label6": "接入端口",
      "label7": "测试用例",
      "paramRowItem": "命令行参数",
      "pleaseinputRowLabel": "请输入执行命令",
      "pleaseinputLabel": "请输入1-10正整数",
      "pleaseinputMessage": "请输入1到65535之间的端口号",
      "queryInfoLabel": "未查询到场景信息!",
      "queryInfoAbnormalItem": "查询场景信息异常!",
      "queryFailedInfoItem": "查询场景信息失败!",
      "successRowLabel": "测试执行成功!",
      "selectToolItem": "选择发单工具",
      "connectSystemLabel": "连接目标LDP业务系统",
      "selectLabel": "选择测试用例",
      "label8": "新建用例",
      "deleteItem": "删除用例",
      "configLabel": "配置多账号",
      "rowLabel": "④&nbsp;&nbsp;&nbsp;执行测试",
      "logLabel": "打印时延日志",
      "rowLabel1": "执行测试"
    },
    "appBindingCore": {
      "itemLabel": "筛选条件",
      "infoItem": "线程信息",
      "manage": "管理IP",
      "selectManageItem": "请选择管理IP",
      "nodeApplicationItem": "应用节点名",
      "selectNodeApplicationItem": "请选择应用节点名",
      "message": "线程拥有者",
      "pleaseinputMessage": "请输入线程拥有者",
      "label": "是否绑核",
      "selectLabel": "请选择绑核",
      "item": "是",
      "item1": "否",
      "label1": "线程名",
      "typeItem": "线程类型",
      "item2": "绑核"
    },
    "brokerDataLimit": {
      "listItem": "名单列表",
      "pleaseinputFundItem": "请输入资金账号",
      "functionItem": "功能号",
      "pleaseinputFunctionItem": "请输入功能号",
      "label": "分片号",
      "pleaseinputLabel": "请输入分片号",
      "nameItem": "组名称",
      "namePleaseinputItem": "请输入组名称",
      "openStatus": "开启状态",
      "all": "全部",
      "enable": "启用",
      "item": "停用",
      "name": "名称",
      "editTimeTimesLabel": "最近一次编辑时间",
      "enableItem": "是否启用",
      "item1": "操作",
      "edit": "编辑",
      "createItem": "创建名单",
      "editItem": "修改名单",
      "deleteSuccess": "删除成功",
      "deleteFailed": "删除失败",
      "delete": "删除",
      "create": "创建",
      "listItem1": "组列表",
      "descriptionItem": "组描述",
      "createItem1": "创建组",
      "editItem1": "修改组",
      "valueSecondItemItem": "限流值（条/秒）",
      "message": "最大输入长度20",
      "label1": "绑定对象",
      "selectCountRowDescription": "至少选择一个对象进行绑定，可多选",
      "functionFundLabel": "功能号/资金账号组",
      "selectFunctionFundMessage": "请选择功能号/资金账号组",
      "message1": "最大输入长度1000",
      "valueItem": "限流值",
      "nameEmptyLabel": "名称不能为空",
      "selectCountMessage": "至少选择一个绑定对象",
      "selectCountLabel": "至少选择一个组",
      "emptyFunctionLabel": "功能号不能为空",
      "emptyFundMessage": "资金账号不能为空",
      "pleaseinputMessage": "请输入大于0小于100000000000000000的正整数",
      "successItem": "操作成功!",
      "countDescription": "1.需要输入具体账号\\n2.只能是数字\\n3.多个账号使用英文字符“;”号分隔",
      "emptyAllCountDescription": "1.分片号为空，代表所有分片\\n2.多个分片号使用英文字符“;”号分隔",
      "secondItem": "条/秒",
      "submit": "提交",
      "label2": "组对象",
      "emptyMessage": "组名不能为空",
      "selectCountMessage1": "至少选择一个组对象",
      "typeItem": "名单类型",
      "label3": "白名单",
      "label4": "限流名单"
    },
    "businessMonitor": {
      "label": "T3监视器",
      "selectLogMetricConfigTimeDescription": "根据需要选择链路指标，生成T3监视器配置。“时间范围”以时延日志打点时间为准。",
      "selectItem": "请选择span",
      "second": "5秒",
      "second1": "10秒",
      "second2": "30秒",
      "second3": "60秒",
      "minuteLabel": "最近五分钟",
      "minuteLabel1": "最近十五分钟",
      "minuteLabel2": "最近三十分钟",
      "selectTypeLabel": "请选择业务类型!",
      "selectTypeLabel1": "请选择链路类型!",
      "selectLabel": "请选择SPAN链路!",
      "configItem": "生成配置",
      "configLabel": "T3监视器配置",
      "copy": "复制",
      "configDescription": "点击“生成配置”获取对应T3监视器配置",
      "item": "JSON示例",
      "close": "关闭"
    },
    "common": {
      "showNoneLabel": "图片无法显示",
      "contentDescription": "常用于各种自定义下拉内容的场景。",
      "selectNodeItem": "请选择节点",
      "infoNoneNodeMenuItem": "当前菜单无节点信息",
      "dataNone": "无数据",
      "queryMessage": "查询方法不正确",
      "item": "条",
      "count": "> 10 个",
      "coreTrade": "交易核心",
      "pleaseInput": "请输入",
      "confirm": "确认",
      "tip": "提示",
      "label": ") 或数组 [",
      "expand": "展开",
      "collapse": "收起",
      "searchDataRowDescription": "没有找到匹配的数据，可尝试其它关键词进行搜索",
      "queryMessage1": "远程查询方法不正确",
      "select": "选择",
      "searchHide": "隐藏搜索",
      "exportData": "数据导出",
      "queryFieldItem": "查询字段名",
      "pleaseinputItem": "请输入别名",
      "exportFile": "导出文件:",
      "exportItem": "导出进度:",
      "exportItem1": "导出中",
      "exportStart": "开始导出",
      "label1": "终止中",
      "exportItem2": "终止导出",
      "exportFailed": "导出失败！",
      "successItem": "操作成功",
      "failedItem": "操作失败",
      "exportConfigField": "导出字段配置",
      "exportFieldItem": "导出字段名",
      "exportStatusRowItem": "导出执行状态",
      "download": "下载",
      "infoRowItem": "执行信息:",
      "exportTime": "导出时间",
      "exportFile1": "导出文件",
      "fileItem": "文件大小",
      "fileDirectoryItem": "文件存储目录：",
      "clearData": "清空数据",
      "queryItem": "快捷查询",
      "description": "当前窗口高度小于500,请放大窗口查看!",
      "queryExpand": "展开查询",
      "query": "查询",
      "reset": "重置",
      "tableConfig": "配置表格",
      "save": "保存",
      "exportData1": "导出数据",
      "exportItem3": "导出历史",
      "tableConfig1": "表格配置",
      "searchExpand": "展开搜索",
      "loading": "加载中",
      "selectDate": "选择日期",
      "selectTimeDate": "选择日期时间",
      "weekItem": "周期",
      "label2": "平均数",
      "productLabel": "产品昨日时延",
      "weekProductLabel": "产品上周时延",
      "monthProductLabel": "产品上月时延",
      "dataMetricMessage": "时延指标数据建议(单位:",
      "selectTime": "选择时间",
      "clear": "清空",
      "queryItem1": "查询结果",
      "addTag": "添加标签",
      "deleteTag": "删除标签",
      "deleteItem": "批量删除",
      "detailItem": ">告警详情:</span> <span onclick=",
      "clusterItem": ">集群拓扑:</span> <span onclick=",
      "quantityItem": "预警数量",
      "item1": "或者",
      "nameItem": "><span style=\"color: #cacfd4;\">机房名称:</span> ${params.name ||",
      "item2": "><span style=\"color: #cacfd4;\">区域: </span> ${data?.area ||",
      "status": "><span style=\"color: #cacfd4;\">状态: </span> ${data?.status ||",
      "message": "><span style=\"color: #cacfd4;\">当日告警总数: </span> ${data?.count ??",
      "timeTimesMessage": "><span style=\"color: #cacfd4;\">最近一次告警时间:</span> ${data?.lastAlertTime ||",
      "detailItem1": "><span style=\"color: #cacfd4;\">告警详情:</span> <span onclick=\"roomTopoJump(",
      "clusterItem1": "><span style=\"color: #cacfd4;\">集群拓扑:</span> <span onclick=\"roomTopoJump(",
      "count1": "4 - 10 个",
      "count2": "1 - 3 个",
      "quantityItem1": "机房数量",
      "infoItem": "机房信息",
      "nameItem1": "机房名称:",
      "item3": "区域:",
      "status1": "状态:",
      "message1": "当日告警总数:",
      "timeTimesMessage1": "最近一次告警时间:",
      "detailItem2": "告警详情:",
      "item4": "查看",
      "clusterItem2": "集群拓扑:",
      "paramEmptyLabel": "参数不能为空",
      "pleaseinputLabel": "请输入正整数",
      "valueDescription": "输入超出最大安全整数值",
      "valueMessage": "最小值不能大于最大值",
      "valueMessage1": "范围差值不能>=1000",
      "valueMessage2": "最大值不能小于最小值",
      "message2": "不全是字母",
      "emptyMessage": "输入不能为空",
      "message3": "只能输入url格式",
      "message4": "只能输入中文格式",
      "message5": "只能输入ACSII字符格式",
      "pleaseinputDescription": "邮编输入格式不正确，请输入6位编码",
      "description1": "移动电话格式不正确",
      "addressMessage": "只能输入ip4地址格式",
      "message6": "只能输入图片格式",
      "fileMessage": "只能输入压缩文件格式",
      "dateMessage": "日期格式不正确",
      "message7": "QQ号码格式不正确",
      "description2": "电话号码格式不正确",
      "countDescription": "只能输入由数字、26个英文字母或者下划线组成的字符串",
      "message8": "只能输入字母格式",
      "description3": "只能输入大写字母格式",
      "description4": "只能输入小写字母格式",
      "noData": "暂无数据",
      "label3": "集合竞价",
      "tradeLabel": "早盘竞价交易",
      "item5": "盘休",
      "tradeLabel1": "午盘竞价交易",
      "label4": "盘后定价",
      "pleaseinputLabel1": "请输入关键字",
      "dataCoreItem": ">核心数据上场:</span> <span onclick=",
      "dataCoreItem1": ">核心数据同步:</span> <span onclick=",
      "connectItem": ">连接关系:</span> <span onclick=",
      "dataManageItem": ">内存数据管理:</span> <span onclick=",
      "functionManage": ">管理功能:</span> <span onclick=",
      "observationInstance": ">实例观测:</span> <span onclick=",
      "label5": ">上下文ID: </span> <span onclick=",
      "nameLabel": ">上下文名称: </span> <span onclick=",
      "detailConfig": ">配置详情: </span> <span onclick=",
      "metricRowItem": ">运行指标: </span> <span onclick=",
      "connectItem1": ">连接关系: </span> <span onclick=",
      "item6": ")\" >告警：${data?.runningInfo?.alertNumber ??",
      "serviceItem": "><span style=\"color: #cacfd4;\">服务名: </span> ${params.data.name ||",
      "countClusterLabel": "><span style=\"color: #cacfd4;\">下一层集群个数: </span> ${data?.baseInfo?.clusterNum ||",
      "label6": "><span style=\"color: #cacfd4;\">分片数:</span> ${data?.baseInfo?.shardingNum ||",
      "dataCoreItem2": "><span style=\"color: #cacfd4;\">核心数据上场:</span> <span onclick=\"clusterGoLink(",
      "dataCoreItem3": "><span style=\"color: #cacfd4;\">核心数据同步:</span> <span onclick=\"clusterGoLink(",
      "countClusterLabel1": "><span style=\"color: #cacfd4;\">集群成员个数: </span> ${data?.runningInfo?.memberNumber.toString() ||",
      "countMessage": "><span style=\"color: #cacfd4;\">存活成员个数: </span> ${data?.runningInfo?.survivingMember?.toString() ||",
      "statusCluster": "><span style=\"color: #cacfd4;\">集群状态: </span> ${ params.data.category ||",
      "timeLabel": "><span style=\"color: #cacfd4;\">最后响应时间: </span> ${data?.runningInfo?.heartbeatTime ||",
      "connectItem2": "><span style=\"color: #cacfd4;\">连接关系:</span> <span onclick=\"handleSearchNode(",
      "dataManageItem1": "><span style=\"color: #cacfd4;\">内存数据管理:</span> <span onclick=\"clusterGoLink(",
      "item7": ")\" style=\"color: #2d8de5; cursor: pointer;\">查看</span></li>` :",
      "functionManage1": "><span style=\"color: #cacfd4;\">管理功能:</span> <span onclick=\"clusterGoLink(",
      "observationInstance1": "><span style=\"color: #cacfd4;\">实例观测:</span> <span onclick=\"clusterGoLink(",
      "versionNodeApplication": "><span style=\"color: #cacfd4;\">应用节点版本: </span> ${data?.baseInfo?.version ||",
      "applicationPlatformItem": "><span style=\"color: #cacfd4;\">应用开发平台:</span> ${data?.baseInfo?.developPlatform ||",
      "serviceDeployItem": "><span style=\"color: #cacfd4;\">部署服务器:</span> ${data?.runningInfo?.ip ||",
      "manageItem": "><span style=\"color: #cacfd4;\">管理端口: </span> ${data?.runningInfo?.port ||",
      "statusNode": "><span style=\"color: #cacfd4;\">节点状态: </span> ${that.appWorkStatusDict[data?.runningInfo?.status] ||",
      "timeLabel1": "><span style=\"color: #cacfd4;\">最后心跳时间:</span> ${data?.runningInfo?.heartbeatTime ||",
      "item8": "备",
      "copyItem": "消息复制",
      "copyStatusItem": "状态机复制",
      "label7": "><span style=\"color: #cacfd4;\">上下文ID: </span> <span onclick=\"topoRcmObsGoLink(",
      "nameLabel1": "><span style=\"color: #cacfd4;\">上下文名称: </span> <span onclick=\"topoRcmObsGoLink(",
      "nameCluster": "><span style=\"color: #cacfd4;\">集群名称: </span> ${contextDetail?.tierName ||",
      "role": "><span style=\"color: #cacfd4;\">角色: </span> ${ctxRole ||",
      "versionLabel": "><span style=\"color: #cacfd4;\">上下文版本: </span> ${contentData?.baseInfo?.version ||",
      "onlineItem": "><span style=\"color: #cacfd4;\">是否在线: </span> ${params.data?.category ||",
      "timeLabel2": "><span style=\"color: #cacfd4;\">最后心跳时间: </span> ${contentData?.runningInfo?.heartbeatTime ||",
      "detailConfig1": "><span style=\"color: #cacfd4;\">配置详情: </span> <span onclick=\"rcmConfigGoLink(",
      "metricRowItem1": "><span style=\"color: #cacfd4;\">运行指标: </span> <span onclick=\"openManageInfo(",
      "connectItem3": "><span style=\"color: #cacfd4;\">连接关系: </span> <span onclick=\"handleSearchNode(",
      "dataLabel": "数据上场中",
      "dataLabel1": "数据上场完成",
      "systemItem": "系统就绪",
      "dataLabel2": "数据下场中",
      "dataLabel3": "数据下场完成",
      "systemLabel": "系统已下线",
      "failedDataItem": "数据上场失败",
      "failedDataItem1": "数据下场失败",
      "label8": "回切中",
      "label9": "已回切",
      "infoBasic": "基础信息:",
      "serviceItem1": "服务名:",
      "countClusterLabel2": "下一层集群个数:",
      "label10": "分片数:",
      "label11": "快捷导航:",
      "dataCoreItem4": "核心数据上场:",
      "dataCoreItem5": "核心数据同步:",
      "clusterItem3": "集群成员:",
      "clusterRole": "集群角色",
      "statusNode1": "节点状态",
      "infoNode": "节点信息",
      "nameCluster1": "集群名称:",
      "clusterItem4": "集群模式:",
      "statusRowItem": "运行状态:",
      "countClusterLabel3": "集群成员个数:",
      "countMessage1": "存活成员个数:",
      "statusCluster1": "集群状态:",
      "timeLabel3": "最后响应时间:",
      "connectItem4": "连接关系:",
      "dataManageItem2": "内存数据管理:",
      "functionManage2": "管理功能:",
      "observationInstance2": "实例观测:",
      "infoNode1": "节点信息:",
      "nodeApplicationItem": "应用节点名:",
      "typeNodeApplication": "应用节点类型:",
      "versionNodeApplication1": "应用节点版本:",
      "applicationPlatformItem1": "应用开发平台:",
      "serviceDeployItem1": "部署服务器:",
      "clusterRole1": "集群角色:",
      "manageItem1": "管理端口:",
      "statusNode2": "节点状态:",
      "statusItem": "业务状态:",
      "timeLabel4": "最后心跳时间:",
      "label12": "上下文ID:",
      "nameLabel2": "上下文名称:",
      "role1": "角色:",
      "versionLabel1": "上下文版本:",
      "label13": "同步模式:",
      "onlineItem1": "是否在线:",
      "detailConfig2": "配置详情:",
      "metricRowItem2": "运行指标:"
    },
    "coreReplayObservation": {
      "label": "不一致",
      "item": "一致",
      "label1": "未启动",
      "rowItem": "执行中",
      "finished": "已完成",
      "paused": "已暂停",
      "label2": "已终止",
      "failed": "失败",
      "paramItem": "任务参数",
      "item1": "启动",
      "editConfig": "修改配置",
      "item2": "暂停",
      "item3": "终止",
      "item4": "恢复",
      "label3": "单步调试",
      "timeStart": ">开始时间：{{ startTime ||",
      "item5": "与",
      "startTradeDescription": "当前交易日重演尚未开始，请稍候",
      "fileDirectoryTradeLabel": "}；  重演交易日文件目录：${replayFilePath ||",
      "label4": "重演结果",
      "quantityLabel": "相同表数量",
      "quantityLabel1": "不同表数量",
      "label5": "应答一致",
      "message": "应答不一致",
      "label6": "消息收发",
      "message1": "应答比对进度(%)",
      "label7": "需比对：",
      "label8": "已比对：",
      "label9": "发送进度(%)",
      "label10": "需发送：",
      "label11": "已发送：",
      "label12": "应答进度(%)",
      "label13": "需应答：",
      "label14": "已应答：",
      "monitorToolItem": "重演工具监控",
      "label15": "发送吞吐(tps)",
      "label16": "应答吞吐(tps)",
      "message2": "应答比对吞吐(tps)",
      "item6": "内存(%)",
      "showMessage": "仅显示应答不一致",
      "clusterItem": "集群名",
      "label17": "发送进度",
      "label18": "应答进度",
      "message3": "应答比对进度",
      "message4": "应答不一致数",
      "dataLabel": "数据比对进度",
      "dataLabel1": "数据不一致数",
      "successItem": "终止成功",
      "successItem1": "暂停成功",
      "successItem2": "恢复成功",
      "downloadSuccess": "下载成功!",
      "downloadFailed": "下载失败",
      "message5": "具体任务细节",
      "label19": "正在加载",
      "updateDataItem": "数据更新中",
      "tradeQuantityDescription": "当前交易日重演完成时，将展示此交易日中\"重演\"与\"持久化\"中相同/不同表数量、应答的一致/不一致数量",
      "recordStatusTradeDescription": "当前交易日消息收发的实时状态。应答比对：当前交易日\"重演的应答\" 与 \"生产已记录的应答\"比对。",
      "toolTradeDescription": "当前交易日中，重演工具本身的发送和应答速度及资源使用情况",
      "finishedItem": "已完成重演：{{ instanceCompletedCount }} ）",
      "errorItem": "错误原因",
      "dataCoreItem": "核心数据重演",
      "moreItemItem": "更多条件",
      "rowLabel": "执行结果",
      "selectTradeItem": "选择交易日",
      "nameLabel": "输入任务名称",
      "label20": "任务来源",
      "timeStartRowItem": "执行开始时间",
      "selectTimeLabel": "请选择时间范围",
      "nameItem": "任务名称",
      "idItem": "任务标识：",
      "statusRowItem": "执行状态",
      "timeEndRowItem": "执行结束时间",
      "label21": "(不一致数：",
      "label22": "不一致数",
      "editSuccess": "修改成功",
      "successItem3": "启动成功",
      "deleteMessage": "确定要删除此任务？",
      "deleteRemoveDataListDescription": "删除重演任务后，任务将从列表移除，同时任务所产生的数据将一并被删除。请谨慎操作。",
      "listLabel": "重演任务列表",
      "label23": "任务总数:",
      "label24": "同步任务",
      "description": "原重演任务中的重演对象可能发生变化，请仔细核对。",
      "dateTrade": "交易日期",
      "label25": "发送间隔",
      "remark": "备注",
      "contentItem": "重演内容",
      "copyLabel": "复制重演任务",
      "createLabel": "创建重演任务",
      "message6": "确定重演对象",
      "paramConfigItem": "配置任务参数",
      "infoItem": "信息核对",
      "selectCoreTradeDescription": "按需选择需要重演“交易日”与对应的“核心”。",
      "selectContentCountStepDescription": "基于前两个步骤的选择，将对应产生的重演任务，请仔细核对任务内容。",
      "pleaseinput": "请输入0~1000",
      "label26": "重演任务",
      "createSuccessLabel": "创建并启动成功",
      "createSuccess": "创建成功",
      "item7": "留下",
      "selectCountCoreLabel": "请至少选择一个核心",
      "paramConfigDescription": "根据实际情况配置重演任务相关参数。",
      "confirmPageLabel": "确认离开页面？",
      "saveDataDescription": "离开后当前操作将不会保存，数据会丢失，请谨慎操作！",
      "coreCluster": "核心集群",
      "secondItem": "毫秒",
      "label27": "发送间隔:",
      "editPath": "修改路径：",
      "label28": "上一步",
      "createMessage": "创建并启动任务",
      "label29": "任务名",
      "queryNameCoreItem": "输入核心名称查询",
      "selectAll": "全选",
      "label30": "同步结果",
      "idItem1": "任务标识",
      "failedItem": "失败原因"
    },
    "dataSecondAppearance": {
      "item": "追加",
      "item1": "覆盖",
      "label": "追加+覆盖",
      "label1": "待上场",
      "label2": "上场中",
      "successItem": "上场成功",
      "failedItem": "上场失败",
      "abnormal": "异常",
      "success": "成功",
      "selectTimesDescription": "按需选择需要二次上场的表",
      "ruleDescription": "确定表上场的加载方式和规则（不写规则默认上场全表）",
      "contentNoneTimesRuleDescription": "请仔细核对二次上场内容、加载方式和上场规则。无误请点击“上场”。",
      "editMessage": "批量修改上场方式",
      "cluster": "集群",
      "item2": "分片",
      "label3": "加载方式",
      "ruleItem": "上场规则",
      "queryDataSettingCountItemFormRuleDescription": "可对每个表单独设置上场数据的查询条件；不设置规则即表示全表上场。",
      "ruleLabel": "输入上场规则",
      "contentLabel": "确定上场内容",
      "configRuleItem": "配置上场规则",
      "item3": "表名",
      "rowLabel": "执行上场中!",
      "failedRowLabel": "执行上场失败!",
      "message": "表示下一步，",
      "selectTimesMessage": "请先选择需二次上场的表",
      "item4": "上场",
      "searchClusterMessage": "输入表名/集群/分片搜索",
      "label4": "可选表（{{ availableTables.length }}）",
      "label5": "已选表（{{ selectedTables.length }}）",
      "timeItem": "上场时间",
      "endTime": "结束时间",
      "label6": "上场结果",
      "label7": "查看原因",
      "dataTimesLabel": "数据二次上场",
      "label8": "上场历史",
      "statusMessage": "上场状态和结果",
      "timesMessage": "最近一次上场",
      "failedItem1": "&nbsp;&nbsp;失败原因",
      "createItem": "创建任务",
      "errorInfo": "错误信息"
    },
    "eccomProduct": {
      "fieldItem": "业务字段",
      "timeItem": "时间范围：",
      "valueItem": "最大值：",
      "valueItem1": "最小值："
    },
    "endpointConfig": {
      "manageLabel": "内存表管理",
      "functionManage": "管理功能API",
      "applicationLabel": "应用抓包发包",
      "configManage": "Locate配置管理",
      "message": "MDB访问测试结果",
      "message1": "USTTable访问测试结果",
      "functionManageMessage": "管理功能访问测试结果",
      "message2": "访问测试结果",
      "message3": "Locate访问测试结果",
      "connectSuccess": "连接成功",
      "connectFailed": "连接失败",
      "systemItem": "系统号",
      "applicationItem": "应用插件ID",
      "addNodeItem": "添加网关节点",
      "label": "接入点名",
      "nodeApplicationItem": "接入应用节点",
      "label1": "接入点IP",
      "message4": "接入点端口",
      "label2": "接入协议",
      "label3": "字符集",
      "edit": "修改",
      "configItem": "批量配置",
      "message5": "连通性测试",
      "addNode": "添加节点",
      "typeNodeApplication": "应用节点类型",
      "applicationItem1": "应用身份",
      "manageItem": "管理端口",
      "clusterApplication": "应用集群",
      "label4": "支持抓包",
      "label5": "支持发包",
      "configFile": "配置文件",
      "hideTextLabel": ", // 超出的文本隐藏",
      "showMessage": ", // 溢出用省略号显示",
      "config": "配置",
      "platformItem": "开发平台",
      "configLabel": "接入网关配置",
      "label6": "测试中",
      "successItem": "访问成功",
      "failedItem": "访问失败",
      "typeMessage": "不支持的接入点类型",
      "noneNode": "暂无节点",
      "online": "在线",
      "offline": "离线",
      "manage": "管理ip",
      "selectLabel": "选择接入点:",
      "item": "测试",
      "label7": "接入点名：",
      "pleaseinputLabel": "请输入接入点名",
      "label8": "接入点IP：",
      "pleaseinputLabel1": "请输入接入点IP",
      "message6": "接入点端口：",
      "pleaseinputMessage": "请输入接入点端口",
      "label9": "接入协议：",
      "label10": "字符集：",
      "item1": "T2证书",
      "configFileItem": "发包配置文件",
      "configPlatformItem": "平台出厂配置",
      "configMessage": "自定义业务配置",
      "uploadFileItem": "点击上传文件",
      "connectItem": "连接测试：",
      "connect": "连接",
      "add": "添加",
      "node": "节点",
      "nodeItem": "网关节点",
      "uploadFileItem1": "请上传文件",
      "file": "文件",
      "uploadMessage": "格式不正确，请上传",
      "fileLabel": "格式的文件。",
      "fileMessage": "文件大小超出限制",
      "uploadFileDescription": "文件大小超出限制，请上传 1MB 以内大小的文件。",
      "uploadSuccess": "上传成功",
      "typeMessage1": "不支持的接入类型,",
      "uploadMessage1": "点击或拖拽上传证书",
      "uploadItem": "上传中",
      "uploadFailed": "上传失败",
      "configService": "配置MDB-SQL服务",
      "pleaseinputMessage1": "请输入1-65536范围的整数",
      "description": "字符长度数不得超过255!",
      "label11": "自动获取",
      "successConfig": "配置成功",
      "failedLabel": "自动获取失败",
      "pleaseinput": "请输入...",
      "loading": "加载中......",
      "configLabel1": "恢复默认配置",
      "configNode": "配置节点",
      "nodeApplication": "应用节点：",
      "configFileItem1": "发包配置文件：",
      "uploadFileDescription1": "格式不正确，请上传cfg格式的文件。",
      "configServiceManageLabel": "配置内存表管理服务",
      "addressMessage": "只能输入ip地址格式"
    },
    "latencyTrendAnalysis": {
      "configItem": "链路配置",
      "selectLabel": "请选择链路",
      "selectItem": "选择跨度",
      "statisticsDate": "统计日期",
      "statisticsTime": "统计时间",
      "statisticsItem": "统计方式",
      "selectStatisticsLabel": "请选择统计方式",
      "statisticsItem1": "统计口径",
      "selectStatisticsLabel1": "请选择统计口径",
      "hourDescription": "自定义范围不得超过18小时",
      "item": "90分位",
      "item1": "95分位",
      "item2": "99分位",
      "allItem": "全部市场",
      "tradeLabel": "深圳交易所",
      "tradeLabel1": "上海交易所",
      "item3": "日盘",
      "item4": "早盘",
      "item5": "午盘",
      "analysisItem": "分析目标",
      "dataItem": "数据范围",
      "dataItem1": "数据过滤",
      "selectItem1": "选择链路"
    },
    "ldpDataObservation": {
      "connectInfo": "连接信息",
      "connectItem": "连接号",
      "timesLabel": "发送次数",
      "timesLabel1": "接收次数",
      "connectTimeTimesMessage": "最近一次连接建立时间",
      "timeTimesMessage": "最近一次发送完成时间",
      "timeTimesMessage1": "最近一次接收完成时间",
      "addressCustomerItem": "客户端地址",
      "customerLabel": "客户端端口",
      "typeItem": "协议类型",
      "timesRowDescription": "执行单次平均耗时(不含排队)",
      "timeColumnMessage": "队列积压的平均时间",
      "timeLabel": "平均定位时间",
      "timesRowDescription1": "执行单次平均耗时(含排队)",
      "label": "redo平均耗时",
      "timesRowDescription2": "执行单次最小耗时(不含排队)",
      "timeColumnMessage1": "队列积压的最小时间",
      "timeLabel1": "最小定位时间",
      "timesRowDescription3": "执行单次最小耗时(含排队)",
      "label1": "redo最小耗时",
      "timesRowDescription4": "执行单次最大耗时(不含排队)",
      "timeColumnMessage2": "队列积压的最大时间",
      "timeLabel2": "最大定位时间",
      "timesRowDescription5": "执行单次最大耗时(含排队)",
      "label2": "redo最大耗时",
      "timeRowMessage": "累计执行时间(不含排队)",
      "timeColumnMessage3": "队列积压的总时间",
      "timeLabel3": "定位的总时间",
      "timeRowMessage1": "累计执行时间(含排队)",
      "label3": "Redo累计耗时",
      "timeTimesRowMessage": "最后一次执行时间(不含排队)",
      "timeTimesColumnMessage": "最后一次队列积压时间",
      "timeTimesMessage2": "最后一次定位时间",
      "timeTimesRowMessage1": "最后一次执行时间(含排队)",
      "timesMessage": "redo最后一次调用耗时",
      "timesRowLabel": "执行次数大于0",
      "secondTimesItem": "吞吐(次/秒)",
      "secondTimesRowMessage": "执行单次平均耗时(纳秒)",
      "timesRowMessage": "累计执行总次数",
      "failedTimesRowLabel": "累计执行失败次数",
      "errorItem": "错误率",
      "item": "吞吐",
      "item1": "时延",
      "functionMessage": "功能号处理时延分布",
      "errorTimesItem": "错误次数",
      "timesRowItem": "执行次数",
      "timesItem": "次数",
      "errorItem1": "错误率(‱)",
      "infoConfigItem": "网络配置信息",
      "addressServiceLabel": "服务器侦听地址",
      "serviceMessage": "服务器侦听端口",
      "message": "是否端口复用",
      "timeLabel4": "心跳时间间隔",
      "timesMessage1": "心跳超时次数",
      "connectLabel": "最大连接数",
      "message1": "最大发送缓存",
      "message2": "最小接收缓存",
      "message3": "是否合并收发线程",
      "message4": "是否异步发送消息",
      "description": "是否采用直接接收模式",
      "timeLabel5": "发送等待时间",
      "countMessage": "收发线程个数",
      "countMessage1": "发送线程个数",
      "countMessage2": "接收线程个数",
      "connectQuantityItem": "最大连接数量",
      "indexItem": "绑定CPU序号",
      "enableLabel": "是否启用快速ACK",
      "enableItem": "是否启用Nagle",
      "infoFunctionItem": "业务功能信息",
      "functionItem": "功能号名",
      "systemItem": "子系统号",
      "label4": "读写标志",
      "label5": "定位模式",
      "label6": "请求结构",
      "fieldLabel": "请求可选字段",
      "label7": "应答结构",
      "fieldLabel1": "应答可选字段",
      "infoDataComponentItem": "业务数据组件信息",
      "path": "路径",
      "version": "版本",
      "message5": "是否初始化",
      "infoFunctionComponentItem": "业务功能组件信息",
      "columnMessage": "队列处理吞吐",
      "secondCount": "ProcessedRate(个/秒)",
      "columnMessage1": "全局队列容量",
      "indexItem1": "线程序号",
      "rowMessage": "线程执行任务数",
      "countColumnMessage": "队列积压消息个数",
      "columnMessage2": "队列使用内存",
      "countColumnDescription": "工作队列历史缓存最大消息个数",
      "functionDescription": "最后处理的功能号是否正在处理",
      "functionSystemDescription": "最后处理的功能号对应的子系统号",
      "functionMessage1": "最后处理的功能号",
      "label8": "工作线程",
      "timesMessage2": "线程轮询次数",
      "timeItem": "睡眠时间",
      "columnMessage3": "线程队列容量",
      "countColumnFunctionLabel": "队列前30个功能号",
      "statisticsFunctionLabel": "功能号分组统计",
      "label9": "积压数",
      "functionLabel": "待出队功能号",
      "dataLabel": "请求数据长度",
      "timeItem1": "入队时间",
      "functionMessage2": "正在处理功能号",
      "functionSystemMessage": "功能号对应的子系统号",
      "message6": "高可用模式",
      "message7": "是否本地模式",
      "statusItem": "主备状态",
      "countMessage3": "下一个重放redo事务号",
      "countMessage4": "下一个写redo事务号",
      "statusLabel": "写redo故障状态",
      "nameNode": "节点名称",
      "node": "节点ID",
      "nodeItem": "节点索引",
      "node1": "节点IP",
      "nodeItem1": "节点端口",
      "statusNodeItem": "节点激活状态",
      "statusItem1": "激活状态",
      "connectStatus": "连接状态",
      "connectSuccessTimesItem": "连接成功次数",
      "message8": "接收消息计数",
      "successLabel": "发送成功计数",
      "failedLabel": "发送失败计数",
      "infoItem": "会话信息",
      "label10": "拦截计数",
      "errorMessage": "接收错误应答计数",
      "successCoreMessage": "发送主核心成功消息计数",
      "failedCoreMessage": "发送主核心失败消息计数",
      "successCoreMessage1": "发送备核心成功消息计数",
      "failedCoreMessage1": "发送备核心失败消息计数",
      "registerCountMessage": "收到注册应答个数",
      "registerSuccessInfoCountLabel": "发送注册信息成功个数",
      "registerFailedInfoCountLabel": "发送注册信息失败个数",
      "countMessage5": "未就绪过滤消息个数",
      "countMessage6": "发送应答个数",
      "type": "redo类型",
      "configName": "配置名称",
      "typeLabel": "通信消息类型",
      "productItem": "产品名",
      "countNodeLabel": "维护的节点个数",
      "label11": "是否仲裁",
      "nodeCoreLabel": "主核心节点索引",
      "nodeCoreItem": "主核心节点名",
      "statisticsLabel": "路由转发统计",
      "functionLabel1": "目标功能号",
      "nodeLabel": "目标节点号",
      "label12": "目标端ID",
      "timesLabel2": "转发次数",
      "infoItem1": "端信息",
      "item2": "别名",
      "infoCustomerItem": "客户端信息",
      "label13": "进程号",
      "address": "地址",
      "item3": "端口",
      "addressItem": "真实ip地址",
      "label14": "真实端口",
      "statisticsCustomerLabel": "客户端接入统计",
      "message9": "局域网接入容量",
      "message10": "广域网接入总量",
      "connect": "连接Id",
      "timesMessage3": "非法报文拦截次数",
      "successTimesLabel": "发送成功次数",
      "failedTimesLabel": "发送失败次数",
      "timeLinkLabel": "网络链接时间戳",
      "dataTimeMessage": "最近发送数据时间戳",
      "dataTimeMessage1": "最近接收数据时间戳",
      "customerItem": "客户端",
      "secondMessage": "每秒实时接收数",
      "valueSecondMessage": "每秒接收数峰值",
      "timeValueSecondMessage": "每秒接收数峰值时间戳",
      "secondMessage1": "每秒实时拒绝数",
      "valueSecondMessage1": "每秒拒绝数峰值",
      "timeValueSecondMessage1": "每秒拒绝数峰值时间戳",
      "statusMessage": "流量控制开关状态",
      "monitorStatusLabel": "流量监控开关状态",
      "statusMessage1": "接入控制开关状态",
      "systemLabel": "目标系统号",
      "nameApplication": "应用名称",
      "versionApplication": "应用版本",
      "typeApplication": "应用类型",
      "applicationPlatformItem": "应用开发平台",
      "nameProduct": "产品名称",
      "applicationItem": "应用简称",
      "numberApplication": "应用编号",
      "instanceApplication": "应用实例",
      "file": "pid文件",
      "nameItem": "插件名称",
      "instanceItem": "实例号",
      "versionItem": "插件版本",
      "dateRelease": "发布日期",
      "functionManageQuantity": "管理功能数量",
      "listFunctionManage": "管理功能列表",
      "directory": "目录",
      "addressDirectory": "目录地址",
      "configAddress": "配置地址",
      "configAddress1": "zk配置地址",
      "infoStatisticsLabel": "报盘组统计信息",
      "label15": "报盘组",
      "label16": "报盘对象",
      "statisticsMessage": "报盘消息处理统计",
      "message11": "报盘消息处理时延",
      "infoColumnMessage": "报盘工作线程队列信息",
      "indexItem2": "报盘序号",
      "label17": "报盘名",
      "typeItem1": "报盘类型",
      "message12": "报盘是否已启动",
      "countColumnLabel": "队列请求个数",
      "columnDescription": "报盘工作线程队列请求",
      "statisticsTypeMessage": "报盘请求类型分组统计",
      "detailFunctionLabel": "待出队功能号详情",
      "typeItem2": "请求类型",
      "infoStatisticsInstanceItem": "报盘实例统计信息",
      "statusItem2": "报盘状态",
      "statusTradeItem": "交易所状态",
      "countLabel": "申报个数",
      "successCountLabel": "申报成功个数",
      "failedCountLabel": "申报失败个数",
      "countMessage7": "重复申报个数",
      "confirmCountItem": "确认个数",
      "confirmSuccessCountLabel": "确认回报成功个数",
      "confirmFailedCountLabel": "确认回报失败个数",
      "countDealItem": "成交个数",
      "successCountDealLabel": "成交回报成功个数",
      "failedCountDealLabel": "成交回报失败个数",
      "infoStatisticsMessage": "席位号报盘处理统计信息",
      "label18": "席位号",
      "typePlatform": "平台类型",
      "productCoreItem": "核心产品名",
      "coreClusterItem": "核心集群名",
      "coreApplicationItem": "核心应用名",
      "numberCoreApplication": "核心应用编号",
      "addressItem1": "回库地址",
      "connectLabel1": "连接的网关",
      "message13": "网关发送主备模式",
      "registerItem": "注册名",
      "pathFile": "redo文件路径",
      "fileMessage": "共享内存文件名",
      "pathNodeDescription": "业务框架在zk上的回库节点路径",
      "pathNodeMessage": "回库在zk上的节点路径",
      "message14": "多线程模式",
      "quantityItem": "线程数量",
      "message15": "重传发送间隔",
      "timesMessage4": "最大重传次数",
      "errorItem2": "错误号",
      "errorQuantityItem": "错误号数量",
      "message16": "最大事务号",
      "description1": "当前已同步最大事务号",
      "label19": "同步流速",
      "countMessage8": "暂未处理事务号个数",
      "timeMessage": "预计同步完成时间",
      "valueItem": "初始值",
      "item4": "线程",
      "secondMessage2": "5min内每秒读redo吞吐量",
      "item5": "读",
      "label20": "吞吐量",
      "item6": "耗时",
      "statisticsFunctionItem": "功能号统计",
      "secondDescription": "5min内每秒发送/应答吞吐量",
      "item7": "发送",
      "item8": "应答",
      "message17": "发送吞吐量",
      "message18": "应答吞吐量",
      "infoItem2": "耗时信息",
      "item9": "结果",
      "label21": "单线程",
      "errorItem3": "错误类",
      "label22": "重发间隔(s)",
      "timesMessage5": "最大重发次数",
      "allTimesLabel": "所有redo重发次数",
      "metricType": "类型/指标",
      "timesLabel3": "重试次数",
      "timesLabel4": "丢弃次数",
      "statusFileColumnLabel": "回库文件队列状态",
      "errorMessage1": "重发错误处理跟踪",
      "coreClusterItem1": "核心集群数",
      "coreClusterMessage": "核心集群消息处理吞吐",
      "observationMetricStageLabel": "Redo分阶段处理观测指标",
      "infoCluster": "集群信息",
      "quantityLabel": "请求处理数量",
      "valueMessage": "请求处理的平均值",
      "quantityLabel1": "应答处理数量",
      "valueMessage1": "应答处理的平均值",
      "message19": "处理最小事务号",
      "message20": "处理最大事务号",
      "infoMessage": "超时检测处理耗时信息",
      "message21": "网络应答性能",
      "message22": "网络应答时延分布",
      "label23": "Redo处理性能",
      "message23": "redo处理时延分布",
      "addressItem2": "网关地址",
      "message24": "网关端口号"
    },
    "ldpLinkConfig": {
      "infoServiceItem": "仲裁服务信息",
      "serviceLabel": "服务提供者",
      "addressClusterService": "服务集群地址",
      "pathServiceLabel": "集中仲裁服务路径",
      "typeCluster": "集群类型",
      "typeNodeClusterApplication": "集群应用节点类型",
      "countClusterLabel": "集群内成员个数",
      "infoClusterApplication": "应用集群信息",
      "registerApplicationItem": "应用注册中心",
      "infoNodeItem": "节点信息同步",
      "registerPathNodeApplicationItem": "应用节点注册中心路径",
      "applicationItem": "短应用名",
      "numberNode": "节点编号",
      "numberItem": "分片编号",
      "nodeApplicationItem": "应用节点身份",
      "configNode": "zk配置节点",
      "infoNodeApplication": "应用节点信息",
      "label": "机房别名",
      "countApplicationLabel": "关联应用个数",
      "configProductLabel": "对接产品配置中心",
      "typeProduct": "产品类型：",
      "systemItem": "业务系统：",
      "configTypeItem": "配置中心类型：",
      "configAddressItem": "配置中心地址：",
      "addressPleaseinputClusterService": "请输入服务集群地址",
      "userItem": "用户名：",
      "pleaseinputUserItem": "请输入用户名",
      "item": "密码：",
      "pleaseinputItem": "请输入密码",
      "configNodeItem": "配置节点对接：",
      "createConfigNode": "创建配置节点",
      "configNodeItem1": "对接配置节点",
      "configPathNode": "配置节点路径：",
      "uploadConfigFile": "上传配置文件：",
      "description": "允许输入a-z、A-Z、0-9、_、-；长度不超过255",
      "selectTypeProductItem": "请选择产品类型",
      "selectCountSystemMessage": "至少选择一个业务系统",
      "fileLabel": "文件大小超出16MB",
      "uploadFileDescription": "文件格式校验不通过,请校验文件格式并重新拖动或点击文件上传",
      "addSuccess": "添加成功!",
      "addressCountDescription": "使用IP:PORT或域名:PORT形式，多个地址使用英文逗号分隔",
      "createUploadSelectConfigFileEmptySystemDescription": "如果已存在配置好的本地配置文件，可以选择上传，否则系统默认创建空的配置文件",
      "closeStatusProductNodeFunctionEnvironmentDescription": "测试环境下可支持该产品节点的性能测试特性，生产环境该功能处于关闭状态。",
      "infoConfigItem": "配置身份信息：",
      "rowProductEnvironmentItem": "产品运行环境：",
      "confirmConfigPathColumnProductNodeDescription": "检测到下列配置路径已被产品节点关联，请确认是否继续对接产品配置中心",
      "configPath": "配置路径：",
      "edit": "'修改'+ modalData.title",
      "editDataItem": "编辑数据区间",
      "label1": "输入数字",
      "item1": "区间",
      "item2": "枚举",
      "editLabel": "修改机房别名",
      "pleaseinputLabel": "请输入机房别名",
      "label2": "未识别",
      "deleteConfirm": "删除确认",
      "add": "新增",
      "addListNoneNodeDescription": "以下节点为zk识别到的新节点（本地无）。同步后会将添加至本地节点列表。",
      "update": "更新",
      "updateListNodeDescription": "以下节点为zk识别到的有更新的节点，同步后会将对应节点更新至本地节点列表。",
      "failedItem": "识别失败",
      "label3": "是否忽略",
      "selectFailedNoneNodeApplicationDescription": "以下节点识别失败，无法同步。可重新识别或选择“忽略”。“忽略”即表示此应用节点不会被同步。",
      "failedNodeApplicationMessage": "请先处理“识别失败”应用节点！",
      "updateInfoListNodeApplicationItem": "应用节点信息列表已更新！",
      "infoNodeMessage": "正在识别节点信息，请稍等......",
      "item3": "同步",
      "deleteItem": "不删除",
      "detailItem": "查看详情",
      "failedNodeMessage": "节点识别超时或失败，请重试!",
      "infoNodeMessage1": "确定要同步节点信息吗？",
      "runningNodeApplicationDescription": "识别到运行中的LDP应用节点与当前本地应用节点存在以下差异，请确定是否要同步：",
      "item4": "重试",
      "updateInfoProduct": "更新产品信息",
      "nameNode": "节点名称：",
      "namePleaseinputNode": "请输入节点名称",
      "systemLabel": "关联业务系统：",
      "selectSystemMessage": "请选择关联业务系统",
      "description1": "字符长度数不得超过50！",
      "updateSuccess": "更新成功!",
      "item5": "机房",
      "selectMessage": "请选择要关联的机房",
      "label4": "主机别名",
      "pleaseinputLabel1": "请输入主机别名",
      "item6": "或",
      "label5": "关联机房",
      "editLabel1": "修改主机别名",
      "config": "SSH配置",
      "item7": "账号",
      "pleaseinputItem1": "请输入账号",
      "item8": "密码",
      "label6": "端口号",
      "pleaseinputLabel2": "请输入端口号",
      "label7": "连通测试",
      "label8": "主机名",
      "addressItem": "IP地址/域名",
      "userItem1": "SSH用户名",
      "message": "连通性测试结果",
      "serviceApplicationRuleItem": "应用服务识别规则",
      "pleaseinputServiceItem": "请输入服务名",
      "item9": "未知",
      "saveSuccess": "保存成功",
      "serviceItem": "服务名",
      "addRule": "添加规则",
      "saveConfig": "保存配置",
      "message1": "是否分片表",
      "message2": "是否主备同步",
      "updateInfoClusterServiceItem": "更新服务集群身份信息",
      "updateSuccess1": "更新成功",
      "updateFailed": "更新失败",
      "updateSubmitFailedItem": "提交更新密码 失败",
      "infoProduct": "产品信息",
      "item10": "主题",
      "message3": "单例上下文",
      "clusterLabel": "集群上下文",
      "item11": "模板",
      "application": "应用",
      "applicationItem1": "关联应用",
      "service": "服务",
      "typeService": "服务类型",
      "serviceItem1": "服务器",
      "label9": "机房名",
      "applicationItem2": "应用分片",
      "label10": "分片名",
      "configLabel": "已托管配置",
      "configNameNode": "配置节点名称",
      "configDirectoryItem": "配置根目录",
      "configLabel1": "配置提供者",
      "configAddressService": "配置服务地址",
      "productInstance": "产品实例：",
      "nameProduct": "产品名称：",
      "configProductItem": "产品配置中心：",
      "label11": "主题数：",
      "message4": "主题引用模板：",
      "message5": "单例上下文数：",
      "description2": "单例上下文引用模板：",
      "clusterLabel1": "集群上下文数：",
      "clusterMessage": "集群上下文引用模板：",
      "message6": "主题模板数：",
      "message7": "通用上下文模板：",
      "clusterMessage1": "集群上下文模板：",
      "configRuleItem": "告警规则配置",
      "versionFunctionStrategyDescription": "该功能为商业版特性,请咨询销售获取商业版本开通策略",
      "monitorConfigNoneProductLabel": "当前产品暂无监控告警配置",
      "addSuccessRule": "添加规则成功！",
      "successRuleLabel": "告警规则清除成功！",
      "importRule": "导入规则",
      "clearRule": "清空规则",
      "item12": "插件",
      "nameFunctionItem": "功能号名称",
      "descriptionFunction": "功能说明",
      "idItem": "读写标识",
      "statusRowMessage": "可运行的业务状态",
      "dataFunctionManageItem": "数据管理功能号",
      "functionPlatformItem": "平台功能号",
      "functionLabel": "业务功能号",
      "dataItem": "数据分片",
      "dataItem1": "数据源",
      "item13": "分片key",
      "label12": "定位串",
      "dataItem2": "数据区间",
      "clusterItem": "关联集群",
      "dataFunctionLabel": "数据上场功能号",
      "timesFunctionLabel": "二次上场功能号",
      "functionItem": "SQL功能号",
      "label13": "内存表",
      "successInfoConfigItem": "配置信息同步成功！",
      "infoDataConfigFunctionManageMessage": "数据分片、数据管理功能号配置信息已同步！",
      "failedInfoConfigItem": "配置信息同步失败！",
      "configItem": "去配置",
      "failedInfoDataConfigProductNodeServiceFunctionManageDescription": "数据分片、数据管理功能号配置信息同步失败，请至\"产品节点管理-产品服务配置-产品服务网关\"配置MDB-SQL接入网关信息。",
      "editSuccess": "修改成功！",
      "dataServiceItem": "同步服务数据",
      "listLabel": "上下文列表",
      "nameLabel": "上下文名称",
      "item14": "模式",
      "label14": "发送主题",
      "label15": "接收主题",
      "label16": "引用模板",
      "tag": "标签",
      "message8": "测试连通性"
    },
    "ldpLogCenter": {
      "nameCluster": "集群名称",
      "typeAccount": "账户类型",
      "countDescription": "多个账号以英文逗号隔开",
      "selectEmptyLabel": "选择不能为空",
      "statusItem": "回库状态",
      "successItem": "已成功",
      "timeItem": "事务时间",
      "label": "线程组",
      "countDescription1": "支持多账号以英文逗号分隔且最多50个",
      "account": "账户",
      "label1": "线程号",
      "label2": "事务长度",
      "errorItem": "错误消息",
      "statusItem1": "回库状态：",
      "errorRecordRowUserDescription": "表示在用户进行\"重试\"操作之后，本地记录的错误事务，是否重新发送到网关，完成回库。",
      "successItem1": "未成功",
      "item": ")?.[0]; // 获取",
      "queryFailed": "查询失败",
      "codeItem": "股东代码",
      "errorLogItem": "回库错误日志",
      "detailRecordLogRowMessage": "点击行查看对应日志记录详情",
      "recordItemItem": "共 {{ totalCount }} 条记录",
      "pageItem": "上一页",
      "pageItem1": "下一页",
      "allAccount": "全部账户：",
      "rowItem": "进行中：",
      "normalItem": "正常完成：",
      "label3": "终止完成：",
      "rowItem1": "进行中",
      "normalItem1": "正常完成",
      "label4": "终止完成",
      "errorAccountLabel": "剩余错误账户数",
      "errorAccountItem": "错误账户总数",
      "errorMessage": "剩余回库错误事务数",
      "errorMessage1": "回库错误事务总数",
      "log": "日志",
      "statusRowMessage": "最后重试执行状态",
      "allItem": "全部重试",
      "allItem1": "全部终止",
      "allAccount1": "全部账户",
      "message": "需要重试的事务数",
      "successMessage": "重试已成功事务数",
      "rowLabel": "执行进度",
      "rowLabel1": "执行总耗时",
      "startIndexLabel": "开始重试消息序号",
      "startMessage": "开始重试事务号",
      "endMessage": "结束重试消息号",
      "endLabel": "结束的事务号",
      "message1": "最新重试消息号",
      "message2": "最新重试的事务号",
      "rowDescription": "已存在进行中的重试任务！",
      "label5": "终止任务",
      "confirmRowDescription": "您确认要终止当前正在执行中的重试任务吗？",
      "createErrorLabel": "创建错误重试任务",
      "confirmErrorAllRowAccountDescription": "您确认要对所有业务错误账户执行重试处理吗？",
      "noneClusterLabel": "暂无集群需要重试！",
      "noneRowLabel": "暂无进行中任务！",
      "createSuccessLabel": "创建重试任务成功!",
      "createFailedLabel": "创建重试任务失败!",
      "successLabel": "终止任务成功!",
      "failedLabel": "终止任务失败!",
      "queryErrorCoreClusterItem": "核心集群回库错误查询",
      "createDataLabel": "创建数据重试任务",
      "confirmErrorRecordRowDescription": "您确认要对以下错误记录进行重试处理吗？",
      "clusterItem": "集群总数：",
      "fundAccountItem": "资金账户数：",
      "errorRecordItem": "错误记录总数：",
      "infoDescriptionItem": "的描述信息",
      "label6": "重试模式：",
      "clusterItem1": "指定集群",
      "accountItem": "指定账户",
      "label7": "下一步",
      "detailRecordLog": "日志记录详情",
      "recordItem": "记录号",
      "recordItem1": "操作记录",
      "infoRecordField": "记录字段信息",
      "errorAccountItem1": "账户回库错误",
      "countAccountDescription": "多个账户使用英文逗号隔开",
      "infoTypeEmptyAccountMessage": "账户类型为空时，账户信息必须为空",
      "pleaseinputAccount": "请输入账户",
      "errorTimesLabel": "最后一次错误号",
      "errorMessage2": "仅显剩余错误不为0",
      "label8": "等待中：",
      "fieldItem": "字段名",
      "valueField": "字段值"
    },
    "ldpMonitor": {
      "label": "全局总览",
      "message": "展示链路拓扑",
      "fileNoneMessage": "该模型文件暂无拓扑结构",
      "label1": "时延趋势",
      "failedDataMonitorMessage": "获取图表监控时延数据失败！",
      "failedDataLabel": "获取聚合数据失败！",
      "item": "主",
      "item1": "同城",
      "item2": "异地",
      "functionLabel": "功能号处理",
      "item3": "吞吐(tps)",
      "label2": "平均时延(ns)",
      "errorItem": "错误率(%)",
      "tradeOrder": "委托交易",
      "label3": "吞吐率",
      "queryOrder": "委托查询",
      "otherItem": "其他业务",
      "pleaseinputItem": "请输入表名",
      "label4": "未加载",
      "label5": "加载完成",
      "failedItem": "加载失败",
      "recordLabel": "内存记录数",
      "recordLabel1": "持久化记录数",
      "dataItem": "数据差量",
      "statusItem": "加载状态",
      "detailItem": "表详情",
      "timesLabel": "上场次数:",
      "detailLabel": "表加载详情",
      "dataLabel": "数据差量不为0",
      "connectInfoApplication": "应用连接信息",
      "connectType": "连接类型",
      "statusApplication": "应用状态",
      "nodeItem": "仲裁节点",
      "searchNodeApplication": "搜索应用节点",
      "queryErrorListNodeApplicationItem": "查询应用节点下拉列表-错误:",
      "nodeLabel": "过滤仲裁节点",
      "nodeMessage": "不过滤仲裁节点",
      "message1": "上下游关系",
      "clusterLabel": "集群同步关系",
      "connectInfoCluster": "集群连接信息",
      "searchCluster": "搜索集群",
      "connectInfoLabel": "上下文连接信息",
      "label6": "发送端",
      "countLabel": "RCM通讯个数",
      "countLabel1": "TCP通讯个数",
      "label7": "接收端",
      "numberCountLabel": "下一个消息编号",
      "message2": "当前最大消息号",
      "numberTimesMessage": "上次应答消息编号",
      "message3": "缓存积压消息数",
      "message4": "网络积压消息数",
      "label8": "主题分区",
      "message5": "对端上下文",
      "description": "下钻主题不存在，不支持跳转查看！",
      "configDescription": "rcm配置不存在，不支持跳转查看！",
      "instanceMessage": "发送方插件实例",
      "addressLabel": "发送方地址",
      "connectItem": "连接方式",
      "addressLabel1": "接收方地址",
      "instanceMessage1": "接收方插件实例",
      "showSettingItem": "拓扑显示设置",
      "queryFailedConfig": "查询rcm配置失败：",
      "queryFailedTag": "查询标签失败：",
      "showNodeLabel": "显示未托管节点",
      "label9": "上下文",
      "config": "RCM配置",
      "tagItem": "默认标签",
      "orderMessage": "委托业务性能总览",
      "minuteItem": "最近5分钟",
      "minuteItem1": "最近15分钟",
      "minuteItem2": "最近30分钟",
      "hourItem": "最近1小时",
      "item4": "吞吐(qbs)",
      "item5": "时延(ns)",
      "label10": "请求数",
      "times": "次",
      "searchItem": "搜索主题",
      "label11": "同步模式",
      "searchLabel": "搜索上下文"
    },
    "ldpProduct": {
      "infoItem": "告警信息",
      "warning": "警告",
      "info": "信息",
      "error": "错误",
      "running": "运行中",
      "item": "停止",
      "label": "未托管",
      "statusItem": "初始状态",
      "item1": "就绪",
      "label1": "未就绪",
      "infoItem1": "扩展信息",
      "nodeItem": "节点操作",
      "dataManageItem": "内存数据管理：",
      "functionManage": "管理功能：",
      "observationInstance": "实例观测：",
      "infoBasic": "基础信息",
      "nodeApplicationItem": "应用节点名：",
      "typeNodeApplication": "应用节点类型：",
      "versionNodeApplication": "应用节点版本：",
      "applicationPlatformItem": "应用开发平台：",
      "statusRowItem": "运行状态",
      "label2": "主机名：",
      "addressItem": "IP地址/域名：",
      "manageItem": "管理端口：",
      "clusterRole": "集群角色：",
      "statusItem1": "进程状态：",
      "statusItem2": "业务状态：",
      "statusItem3": "插件状态：",
      "timeLabel": "最后心跳时间：",
      "nameCluster": "集群名称：",
      "clusterItem": "集群模式：",
      "countClusterLabel": "集群成员个数：",
      "countMessage": "存活成员个数：",
      "statusCluster": "集群状态：",
      "timeLabel1": "最后响应时间：",
      "clusterItem1": "集群成员",
      "label3": "未上场",
      "label4": "上场完成",
      "detailLabel": "表同步详情",
      "detailItem": "加载详情",
      "detailItem1": "同步详情",
      "infoItem2": "加载信息",
      "quantityLabel": "已加载表数量：",
      "quantityLabel1": "需加载表数量：",
      "recordQuantityLabel": "已加载表记录数量：",
      "recordQuantityLabel1": "需加载表记录数量：",
      "label5": "已耗时：",
      "dataTimeItem": "数据加载时间：",
      "infoItem3": "同步信息",
      "quantityLabel2": "需同步表数量：",
      "recordQuantityLabel2": "需同步表记录数量：",
      "recordQuantityLabel3": "已同步表记录数量：",
      "dataLabel": "主备数据差量：",
      "status": "状态",
      "item2": "MDB内存",
      "label6": "占用内存：",
      "monitorOrderMessage": "委托全链路时延监控",
      "enableRule": "启用规则",
      "configPleaseinputItem": "请输入插件配置...",
      "clusterSecuritiesItem": "证券前置集群",
      "clusterApplication": "应用集群：",
      "clusterLabel": "集群实现方式：",
      "item3": "成组",
      "productItem": "产品分片：",
      "none": "无",
      "instanceSecuritiesItem": "证券前置实例",
      "instanceApplication": "应用实例：",
      "versionApplication": "应用版本：",
      "nameApplication": "应用名称：",
      "securitiesItem": "证券前置",
      "typeApplication": "应用类型：",
      "clusterItem2": "归属集群：",
      "clusterItem3": "集群身份：",
      "listItem": "插件列表",
      "infoInstance": "Transport实例信息",
      "connectItem": "连接关系",
      "connectTypeItem": "业务连接类型：",
      "connectName": "连接名称：",
      "message": "可靠组播通信",
      "typeItem": "通信类型：",
      "typeItem1": "协议类型：",
      "connectInstanceItem": "连接关系实例",
      "importRuleItem": "导入告警规则",
      "productNode": "产品节点：",
      "ruleItem": "告警规则：",
      "importSuccess": "导入成功!",
      "connectSystemItem": "连接LDP业务系统",
      "registerAddressItem": "注册中心地址：",
      "registerLabel": "注册中心端口：",
      "listProductNode": "产品节点列表：",
      "label7": "北京市",
      "label8": "上海市",
      "label9": "深圳市",
      "productDeployItem": "产品部署结构：",
      "editRuleItem": "修改告警规则",
      "editSuccessRule": "规则修改成功！",
      "editValueItem": "输入修改值",
      "label10": "时延走势(μs)"
    },
    "ldpTable": {
      "contentItem": "操作内容",
      "userItem": "操作用户",
      "rowLabel": "操作执行",
      "rowItem": "未执行",
      "item": "失效",
      "rowLabel1": "执行完毕",
      "dataRecordItem": "数据记录号",
      "typeField": "字段类型",
      "editValue": "修改值",
      "valueLabel": "原纪录值",
      "editStatus": "修改状态",
      "editRowItem": "修改执行",
      "editRowLabel": "该修改未执行",
      "failedDetailItem": "查看详情失败",
      "label": "目标表:",
      "editLabel": "批量修改操作:",
      "dataItemLabel": "影响数据条数:",
      "countFieldLabel": "影响字段个数:",
      "userItem1": "用户名:",
      "userRole": "用户角色:",
      "editSubmitTimeItem": "修改单提交时间:",
      "editStatusItem": "修改单状态:",
      "editSuccessItem": "修改成功数:",
      "editFailedItem": "修改失败数:",
      "editTimeStart": "修改开始时间:",
      "editTimeEnd": "修改结束时间:",
      "editLabel1": "修改总耗时:",
      "editDetailLabel": "内存修改操作详情",
      "updateInfoDataItem": "更新数据表信息",
      "dataPropertyLabel": "数据表扩展属性",
      "selectDataPropertyLabel": "请选择数据表扩展属性",
      "dataDescriptionItem": "数据表说明",
      "dataDescriptionPleaseinputItem": "请输入数据表说明",
      "queryItem": "查询模式",
      "editItem": "编辑模式",
      "recordItemPageMessage": "此为当前页第一条记录",
      "recordItemPageMessage1": "此为当前页最后一条记录",
      "editDataNone": "无数据修改",
      "editLabel2": "加入修改单",
      "editLabel3": "加入修改预览",
      "editSuccessLabel": "加入修改预览成功",
      "editLabel4": "查看修改预览",
      "editFailedLabel": "加入修改预览失败",
      "editDetailRowLabel": "修改操作执行详情",
      "label1": "操作请求：",
      "timeItem": "请求时间：",
      "label2": "请求入参：",
      "label3": "请求返回：",
      "editNameItem": "修改单名称：",
      "editNamePleaseinputItem": "请输入修改单名称",
      "editLabel5": "批量修改操作：",
      "editDataItemItem": "待修改数据条数：",
      "editCountFieldItem": "待修改字段个数：",
      "createEditConfirmItem": "确认创建修改单",
      "editRowItem1": "修改单执行",
      "description": "字符长度数不得超过15",
      "editItem1": "修改单-",
      "emptyLabel": "不能为空",
      "editItem2": "自由编辑",
      "editDataRowMessage": "正在执行数据修改，请耐心等待...",
      "editItem3": "修改完成",
      "editItem4": "放弃修改",
      "editRowItem2": "执行修改",
      "functionItem": "功能简介",
      "failedFormItem": "表单验证失败!",
      "createEditItem": "创建修改预览",
      "propertyLabel": "内存表属性",
      "dataPropertyDescription": "内存表属性：该属性用于按业务场景来区分表内数据。不同的业务场景下，可能会重用相同的内存表结构，但实际代表的数据语义不同。即内存表+内存表属性代表唯一的业务内存表。",
      "dataManageLabel": "内存数据表管理",
      "disconnect": "断开",
      "selectConnectProductNodeApplicationLabel": "请选择连接的产品及应用节点",
      "editLabel6": "历史修改单",
      "editItem5": "修改预览",
      "recordValueItem": "原记录值",
      "queryEditFailedListItem": "修改单列表查询失败!",
      "clearFailedItem": "清空操作失败!",
      "queryEditFailedItem": "修改预览查询失败!",
      "editDeleteSuccessItemLabel": "预览修改单条目删除成功!",
      "editDeleteFailedItemLabel": "预览修改单条目删除失败!",
      "editItemMessage": "预览修改单条目不存在!",
      "editFailedRowItem": "执行修改失败!",
      "clearRowMessage": "您确定执行清空操作吗？",
      "clearItem": "清空操作",
      "editSubmit": "提交修改",
      "itemPage": "20条/页",
      "itemPage1": "30条/页",
      "itemPage2": "50条/页",
      "queryLabel": "查询结果集",
      "infoDataItem": "数据表信息",
      "dataFieldItem": "数据表字段",
      "dataType": "数据类型",
      "editLabel7": "是否可修改",
      "queryLabel1": "查询结果可见",
      "selectallField": "全选字段",
      "queryItemLabel": "可作为查询条件",
      "dataItem": "数据表名：",
      "dataPropertyLabel1": "数据表扩展属性：",
      "dataDescriptionItem1": "数据表说明：",
      "disableConfigPropertyFieldItem": "禁用struct字段可见属性配置"
    },
    "locateConfig": {
      "configNodeLabel": "节点定位插件配置",
      "configNodeRuleItem": "节点定位规则配置",
      "configItem": "配置对比",
      "configTypeFile": "配置文件类型",
      "nodeItem": "源节点",
      "nodeItem1": "目标节点",
      "label": "是否一致",
      "label1": "一键修正",
      "listConfigNodeItem": "定位节点配置列表",
      "configTypeItem": "配置对象类型",
      "configItem1": "配置对象",
      "configItem2": "对比配置",
      "saveFailed": "保存失败",
      "editConfigRowNodeMessage": "你确定要进行节点配置修改吗？",
      "configNode": "配置节点：",
      "configType": "配置类型：",
      "editConfigFileTimesNodeDescription": "! 本次修改仅对节点内存中的配置生效，重启后将重新加载本地配置文件",
      "configItem3": "同步配置",
      "configRowNodeMessage": "你确定要进行节点配置同步吗？",
      "nodeItem2": "源节点：",
      "nodeItem3": "目标节点：",
      "label2": "等待中",
      "statusItem": "同步状态",
      "configNodeMessage": "节点配置同步中，请稍后...",
      "configNodeLabel1": "节点配置同步完成",
      "nodeLabel": "`对比源节点: ${sourceDiff.sourceNode ||",
      "nodeLabel1": "`对比目标节点: ${targetDiff.targetNode ||",
      "selectConfigNodeLabel": "选择对比配置的节点",
      "configType1": "配置类型",
      "configLabel": "原始配置来源",
      "nodeLabel2": "对比源节点",
      "nodeLabel3": "对比目标节点",
      "nodeItem4": "节点定位AR"
    },
    "managementQuery": {
      "infoItem": "基本信息",
      "descriptionItem": "入参说明",
      "descriptionItem1": "出参说明",
      "descriptionItem2": "案例说明",
      "nameItem": "入参名称",
      "paramType": "参数类型",
      "paramDescription": "参数说明",
      "nameItem1": "出参名称",
      "nameFunction": "功能名称：",
      "remarkFunction": "功能备注：",
      "versionItem": "版本号：",
      "updateTime": "更新时间：",
      "label": "提供者：",
      "descriptionFunction": "功能说明：",
      "label1": "入参案例",
      "label2": "出参案例",
      "exportItem": "批量导出",
      "searchNamePleaseinputFunctionManage": "请输入管理功能名称搜索",
      "functionManage": "管理功能",
      "remove": "移除",
      "exportItem1": "正在导出",
      "exportItem2": "导出停止",
      "exportSuccess": "导出成功",
      "exportFailed": "导出失败",
      "failedItem": "终止失败",
      "exportAbnormalItem": "导出终止异常",
      "exportSuccessMessage": "导出任务启动成功，请稍候",
      "exportFailedLabel": "导出任务启动失败",
      "searchFunctionManage": "搜索管理功能 ({{ leftData.length }})",
      "exportListItem": "待导出列表 ({{ rightData.length }})",
      "item": "原因",
      "clearList": "清空列表",
      "export": "导出",
      "table": "表格",
      "configFunction": "功能配置",
      "countMessage": "最大展示Tab个数",
      "configItem": "交互配置",
      "expandListFunctionMessage": "只保留激活插件功能列表展开",
      "recordParamTimesMessage": "记录最后一次输入参数",
      "pageFunctionManageMessage": "结果页与管理功能联动",
      "typeFunctionMenuDescription": "单击Get类型功能菜单时自动发起请求",
      "exportFunctionManage": "管理功能导出",
      "exportSelectFunctionManageMessage": "根据需要，选择想要导出的管理功能。",
      "item1": "入参",
      "config": "`配置（`+ modalData.title + ')'",
      "tableConfigCountFunctionManageDescription": "通过配置JsonPath，将管理功能表格展示的一个结果集拆分为多个表格呈现（一个JsonPath表示拆分一个表格）。",
      "pleaseinput": "请输入jsonPath",
      "tableNameItem": "表格展示名称",
      "message": "是否支持排序",
      "tableRowFieldDescription": "表格字段支持行排序(仅当表格为一维数组时生效)",
      "config1": "配置（",
      "config2": "`配置（`+ modalData.title +",
      "value": "值",
      "dataMessage": "处理数据时出错:",
      "descriptionFunctionManageItem": "管理功能使用说明",
      "item2": "前置",
      "core": "核心",
      "item3": "回库",
      "pleaseinput1": "placeholder=\"请输入\" disabled :class=\"["
    },
    "marketAllLink": {
      "label": "传输链路",
      "statisticsItem": "统计范围",
      "message": "自定义范围",
      "item": "平均",
      "label1": "95%分位数",
      "label2": "5%分位数",
      "secondLabel": "每秒最大",
      "secondLabel1": "每秒最小",
      "analysisMetric": "分析指标",
      "selectItem": "选择市场",
      "noneRowSystemMessage": "当前行情系统暂无时延走势",
      "secondSystemMessage": "秒级系统穿透时延标准差",
      "label3": "95分位数",
      "label4": "5分位数",
      "systemLabel": "系统抖动范围",
      "normalLabel": "时延正常率",
      "rowLabel": "快照行情",
      "rowLabel1": "指数行情",
      "orderItem": "逐笔委托",
      "dealItem": "逐笔成交",
      "label5": ", // 抖动范围",
      "valueItem": "最大值:",
      "label6": "95分位数:",
      "label7": "5分位数:",
      "valueItem1": "最小值:",
      "rowItem": "行情源",
      "label8": "交换机",
      "systemMessage": "本地全系统时延",
      "valueItem2": "{this.scopeNotifyInfo.data.original.name}最大值：",
      "valueItem3": "{this.scopeNotifyInfo.data.original.name}最小值：",
      "valueItem4": "{this.scopeNotifyInfo.data.contrast.name}最大值：",
      "valueItem5": "{this.scopeNotifyInfo.data.contrast.name}最小值：",
      "item1": "透视",
      "statusApplication": "应用状态："
    },
    "mcDataObservation": {
      "label": "主题名",
      "selectLabel": "请选择主题名",
      "item": "分区",
      "selectLabel1": "请选择分区",
      "queryItem": "查询方式",
      "pleaseinputLabel": "请输入1~1000的正整数",
      "item1": "最近",
      "item2": "最早",
      "label1": "消息ID范围",
      "label2": "分区号",
      "item3": "消息ID",
      "label3": "生产者",
      "indexRelease": "发布序号",
      "timeRelease": "发布时间",
      "label4": "消息大小",
      "itemLabel": "过滤条件",
      "contentItem": "消息内容",
      "allClusterLabel": "全部消费者集群",
      "allItem": "全部主题",
      "message": "按消息积压数排序",
      "customerDescription": "按客户端平均处理耗时排序",
      "timeCustomerMessage": "按客户端平均排队时间排序",
      "timeRowCustomerMessage": "按客户端平均执行时间排序",
      "clusterMessage": "消费者集群消息处理",
      "clusterLabel": "消费者集群",
      "instanceItem": "实例名",
      "message1": "消息积压数",
      "indexLabel": "消费消息序号",
      "indexLabel1": "最新消息序号",
      "timesLabel": "调用次数",
      "customerDescription1": "当日客户端平均处理耗时",
      "customerDescription2": "当日客户端平均排队耗时",
      "rowCustomerMessage": "当日客户端平均执行耗时",
      "customerDescription3": "当日客户端最大处理耗时",
      "customerDescription4": "当日客户端最大排队耗时",
      "rowCustomerMessage1": "当日客户端最大执行耗时",
      "customerDescription5": "当日客户端最小处理耗时",
      "customerDescription6": "当日客户端最小排队耗时",
      "rowCustomerMessage2": "当日客户端最小执行耗时",
      "message2": "消息处理变化趋势",
      "minuteItem": "最新15分钟",
      "minuteItem1": "历史15分钟",
      "countQuantity": "数量(个)",
      "item4": "最大",
      "item5": "最小",
      "customerMessage": "当日客户端处理耗时",
      "customerMessage1": "当日客户端排队耗时",
      "rowCustomerMessage3": "当日客户端执行耗时",
      "columnLabel": "死信队列",
      "refresh": "刷新",
      "label5": "死信主题",
      "label6": "消息主题",
      "label7": "消息分区",
      "timeTimesLabel": "上次接收时间",
      "message3": "死信消息数",
      "clearColumnItem": "清空队列",
      "numberItem": "线程编号",
      "countColumnMessage": "当前队列消息个数",
      "columnMessage": "历史队列最大消息数",
      "label8": "处理总数",
      "versionProduct": "产品版本",
      "typeProduct": "产品类型",
      "clusterLabel1": "集群主机数",
      "number": "编号",
      "addressItem": "主机地址",
      "releaseItem": "发布线程",
      "label9": "会话线程",
      "label10": "订阅线程",
      "label11": "推送线程",
      "label12": "主题线程",
      "label13": "消息中心",
      "statisticsLabel": "全主题统计",
      "statisticsLabel1": "单主题统计",
      "nodeItem": "节点数",
      "label14": "会话数",
      "label15": "主题总数",
      "message4": "持久化主题数",
      "label16": "分区总数",
      "message5": "分区副本总数",
      "message6": "生产者总数",
      "message7": "生产消息总数",
      "label17": "消费者",
      "message8": "消费者总数",
      "itemLabel1": "订阅项总数",
      "message9": "推送消息总数",
      "columnClusterDescription": "集群线程队列消息变化趋势",
      "description": "主题生产消费消息变化趋势",
      "releaseMessage": "发布消息变化差量",
      "message10": "订阅消息变化差量",
      "countLabel": "消息数(个)",
      "selectLabel2": "请选择主题",
      "selectLabel3": "请选择生产者",
      "selectLabel4": "请选择消费者",
      "releaseLabel": "发布主题数",
      "releaseLabel1": "发布分区数",
      "message11": "生产消息数",
      "infoLabel": "生产者信息",
      "itemLabel2": "订阅项数",
      "message12": "订阅主题数",
      "message13": "推送消息数",
      "message14": "补缺消息数",
      "numberItem1": "订阅编号",
      "timeItem": "订阅时间",
      "label18": "订阅方式",
      "label19": "广播消费",
      "clusterItem": "集群消费",
      "label20": "订阅主题",
      "infoLabel1": "消费者信息",
      "infoItem": "分区信息",
      "descriptionItem": "主题描述",
      "label21": "分区数",
      "message15": "分区副本数",
      "message16": "是否全局主题",
      "label22": "有序级别",
      "label23": "业务校验",
      "label24": "可靠级别",
      "validLabel": "消息有效期",
      "saveServiceMessage": "服务端是否保存消费者偏移",
      "label25": "生产者数",
      "label26": "消费者数",
      "nodeMessage": "主分区所在节点",
      "nodeMessage1": "副本分区所在节点",
      "message17": "持久化消息数",
      "nameInstanceLabel": "消费者实例名称",
      "label27": "主题前缀",
      "message18": "已推送消息数",
      "countLabel1": "补缺个数",
      "valueItemLabel": "过滤条件值",
      "releaseLabel2": "发布的消息数",
      "label28": "分区有序",
      "label29": "全局有序",
      "item6": "内存",
      "infoItem1": "主题信息",
      "clearItem": "清空中",
      "clearAllColumnMessage": "是否清空队列所有死信消息？",
      "clearSuccess": "清空成功！",
      "label30": "死信消息",
      "indexLabel2": "死信消息序号",
      "message19": "死信消息ID范围：",
      "listMessage": "，列表最多展示最近",
      "dataItem": "条数据",
      "versionProduct1": "产品版本：",
      "versionPleaseinputProduct": "请输入产品版本",
      "addressServiceManage": "服务管理地址:",
      "addressPleaseinputCluster": "请输入MC集群地址",
      "label31": "消息中心3.0",
      "createProductNode": "创建产品节点",
      "editProductNode": "修改产品节点",
      "editSuccess": "修改成功!",
      "addressCountDescription": "使用IP:PORT形式，多个地址使用英文逗号分隔"
    },
    "mcDeploy": {
      "pleaseinputLabel": "请输入主题名",
      "updateDateTimesItem": "上次更新日期",
      "numberItem": "主题编号",
      "label": "副本数",
      "updateTimeTimesItem": "上次更新时间",
      "message": "动态主题同步",
      "description": "描述",
      "label1": "可靠等级",
      "copyClusterLabel": "集群间复制模式",
      "label2": "有序等级",
      "validLabel": "有效期等级",
      "message1": "是否业务校验",
      "message2": "是否局部主题",
      "label3": "是否静态",
      "recordServiceMessage": "是否服务端记录消息偏移",
      "numberNodeMessage": "分区主所在节点编号",
      "numberNodeMessage1": "分区备所在节点编号",
      "statusServiceItem": "分区服务状态",
      "versionLabel": "分区版本号",
      "itemLabel": "过滤条件名",
      "message3": "是否参与计算分片",
      "label4": "分区分布"
    },
    "mdbDataObservation": {
      "dataNameItem": "数据库名称",
      "dataCountLabel": "数据库表个数",
      "message": "主控进程号",
      "message1": "加锁进程号",
      "message2": "已处理事务号",
      "message3": "事务处理性能",
      "message4": "事务处理总数",
      "secondMessage": "事务处理吞吐(笔/秒)",
      "message5": "事务处理吞吐",
      "label": "总数(笔)",
      "secondLabel": "吞吐(笔/秒)",
      "message6": "内存使用分布",
      "infoDataVersionLabel": "内存数据库版本信息",
      "configLabel": "工作进程配置",
      "file": "AdminFile文件",
      "configLabel1": "事务处理配置",
      "configLabel2": "内存分配配置",
      "recordItem": "总记录数",
      "message7": "占已使用内存比率",
      "searchLabel": "请搜索内存表",
      "recordStatisticsLabel": "表记录数增长统计",
      "statisticsMessage": "表内存使用增长统计",
      "message8": "表内存使用分布",
      "dataFileLabel": "表数据存储-文件",
      "infoLabel": "表索引信息",
      "analysisLabel": "性能分析开关",
      "detailRowItem": "执行详情",
      "message9": "事务控制器ID",
      "label1": "平均耗时",
      "label2": "最小耗时",
      "message10": "前十最大耗时",
      "label3": "标准差",
      "openNormalItem": "开启 (正常级别)",
      "openLabel": "开启 (基本级别)",
      "message11": "事务处理吞吐 - Top10",
      "label4": "死锁检测",
      "message12": "只看被阻塞控制器",
      "message13": "只看工作控制器",
      "infoFile": "Undo文件信息",
      "infoRecordFileItem": "Undo文件记录数信息",
      "label5": "Undo事务号: -",
      "timeRowItem": "执行时间",
      "message14": "事务控制器",
      "item": "耗时（ms）",
      "label6": "处理表",
      "message15": "处理表明细"
    },
    "mdbPrivilegeManage": {
      "infoConfigBasicRolePermissionDescription": "请先完成角色的基础信息配置，完成后可为角色配置权限。",
      "roleItem": "角色名：",
      "pleaseinputRoleItem": "请输入角色名",
      "description": "描述：",
      "descriptionPleaseinput": "请输入描述",
      "addRole": "添加角色",
      "editRole": "编辑角色",
      "userItem": "用户名",
      "pleaseinputMessage": "请输入或自动生成密码",
      "roleItem1": "关联角色",
      "roleLabel": "暂未关联角色",
      "configPermissionItem": "!item.enableAuth ? '暂未配置权限' : item.roleName",
      "addUser": "添加用户",
      "editUser": "编辑用户",
      "countUserRoleMessage": "每个用户至多关联10个角色",
      "message": "请填写密码",
      "pleaseinputLabel": "请输入密码8~20位",
      "description1": "长度8-20位，必须包含字母 (a~zA~Z)、数字(0~9)、特殊符号(!、$、#、@、*、_)3种",
      "permission": "MDB权限",
      "copyConfig": "复制配置",
      "editItem": "修改密码",
      "message1": "输入当前密码",
      "label": "当前密码",
      "message2": "输入新密码",
      "pleaseinputLabel1": "请输入新密码",
      "timesMessage": "再次输入新密码",
      "timesMessage1": "请再次输入新密码",
      "timesMessage2": "请再次输入密码",
      "timesMessage3": "两次输入密码不一致!",
      "importPermission": "导入权限",
      "selectFileItem": "请选择文件",
      "importSelectDataConfigFileNoneLabel": "暂无数据,请先选择要导入的配置文件",
      "importCluster": "导入集群",
      "confirmImportConfigFilePermissionDescription": "导入的权限配置文件将覆盖已有的本地权限配置，请确认。",
      "fileMessage": "文件格式不正确",
      "uploadFileDescription": "文件大小超出限制，请上传 10MB 以内大小的文件。",
      "importConfigPermission": "导入MDB权限配置",
      "selectFile": "选择文件",
      "clusterLabel": "集群关系匹配",
      "importNameClusterPermissionDescription": "权限中涉及的本地集群名称与导入的集群需一一匹配",
      "fileMessage1": "文件内部格式出错",
      "selectInfoFileNoneClusterDescription": "文件内部格式出错，无法读取集群信息。请重新选择文件！",
      "selectItem": "重新选择",
      "loginUser": "MDB用户登录",
      "confirmResetSelectLoginFailedProductNodeManageAccountDescription": "若登录失败，请先确认产品节点是否选择正确。或联系管理员重置账户。",
      "selectProductItem": "请选择产品",
      "label1": "接入点",
      "selectLabel": "请选择接入点",
      "userLabel": "请填写用户名",
      "configLabel": "配置接入点",
      "login": "登录",
      "userDescription": "item.userName || '本地存在用户,但MDB未获取到'",
      "userDescription1": "本地存在用户,但MDB未获取到",
      "userItem1": "未知用户⚠",
      "resetItem": "重置密码",
      "label2": "赋权方式",
      "successConfigPermission": "权限配置成功！",
      "failedConfigPermission": "权限配置失败！",
      "message3": "按操作赋权",
      "label3": "按表赋权",
      "addClusterRolePermissionDescription": "为角色赋予MDB集群对应权限（select/update/delete/insert）。集群中新增表时，角色可控表同步增加。",
      "addDetailClusterRolePermissionDescription": "为角色赋予详细表权限（select/update/delete/insert）。集群中新增表时，角色可控表不变。",
      "pleaseinputClusterItem": "请输入集群名",
      "selectClusterItem": "请选择集群",
      "label4": "全不选",
      "user": "用户",
      "roleItem2": "关联角色：",
      "copyInfoUserLabel": "复制用户名、密码信息",
      "label5": "操作结果",
      "addSuccessUser": "用户添加成功！",
      "addFailedUser": "用户添加失败！",
      "resetSuccess": "重置成功！",
      "resetFailed": "重置失败！",
      "label6": "操作完成",
      "copyInfo": "复制信息",
      "label7": "查看密钥",
      "dataConfigManageToolMessage": "此密钥用于配置三方工具管理MDB数据。",
      "user1": "用户ID：",
      "item": "密钥：",
      "userItem2": "查看用户",
      "editTimeTimesItem": "上次修改时间：",
      "noneRoleItem": "暂无关联角色",
      "message4": "按操作赋权：",
      "clusterRolePermissionDescription": "角色拥有MDB集群对应权限（select/update/delete/insert），集群中新建表时，角色可控表同步增加。",
      "label8": "按表赋权：",
      "detailClusterRolePermissionDescription": "角色拥有详细表对应权限（select/update/delete/insert），集群中新建表时，角色可控表不变。",
      "roleItem3": "角色名",
      "configRolePermissionItem": "角色未配置权限",
      "descriptionRole": "角色描述",
      "modifyTime": "修改时间",
      "configPermission": "权限配置",
      "userItem3": "绑定用户",
      "createSuccessRole": "创建角色成功!",
      "editSuccessRole": "修改角色成功!",
      "deleteSuccess": "删除成功!",
      "deleteSystemRoleDescription": "read only为系统内置角色,不可删除,请勿勾选！",
      "deleteRoleItem": "批量删除角色",
      "deleteConfirmRoleMessage": "确认要批量删除已选中的角色吗？",
      "deleteRole": "删除角色",
      "descriptionUser": "用户描述",
      "failedUserItem": "用户操作失败:",
      "editSuccessUser": "用户修改成功！",
      "editFailedUser": "用户修改失败！",
      "deleteUserItem": "批量删除用户",
      "deleteConfirmUserMessage": "确认要批量删除已选中的用户吗？",
      "resetMessage": "确定要重置密码吗？",
      "deleteUser": "删除用户"
    },
    "networkSendAndRecevied": {
      "showTableConfigColumn": "表格列显示配置",
      "pleaseinputLabel": "请输入用例名",
      "description": "用例名长度不能超过64",
      "contentNone": "无内容",
      "index": "序号",
      "importItem": "导入用例",
      "userItem": "用户协议",
      "uploadFile": "上传文件",
      "infoItem": "用例信息",
      "uploadContentFileDescription": "文件内容校验不通过,请上传正确的json文件",
      "fileLabel": "文件大小超出15MB",
      "infoMessage": "未识别到用例信息",
      "message": "检测到非法用例名",
      "importSuccessItem": "用例导入成功",
      "message1": "另存为新用例",
      "namePleaseinputItem": "请输入用例名称",
      "saveSuccessItem": "保存用例成功。",
      "saveFailedItem": "保存用例失败。",
      "saveItem": "保存用例",
      "updateConfirmContentDescription": "用例「{{modalData.params ? modalData.params.name : '-'}}」内容发生变化，请确认是否直接更新原用例？或将新内容另存为新用例？",
      "label": "另存为",
      "timeItem": "时间戳",
      "timeItem1": "时间戳(μs)",
      "valueItem": "差值(μs)",
      "dataMessage": "未找到对应数据",
      "label1": "方法将",
      "propertyLabel": "属性为传入的",
      "expandItem": "代表展开，",
      "expandAll": "全部展开",
      "collapseAll": "全部收起",
      "contentItem": "用例内容",
      "editSuccessLabel": "用例名修改成功!",
      "deleteSuccessItem": "用例删除成功!",
      "contentEmptyMessage": "Body内容为空或格式不正确",
      "saveSuccessItem1": "用例保存成功!",
      "selectLabel": "请先选择用例",
      "label2": "抓包中",
      "functionItem": "功能号：",
      "label3": "消息体",
      "listItem": "消息列表",
      "userLabel": "用户自定义",
      "timeItem2": "抓包时间",
      "typeItem": "包类型",
      "infoItem1": "附加信息",
      "openFailedItem": "开启抓包失败",
      "failedLabel": "停止抓包失败",
      "resetListLabel": "消息列表已重置！",
      "queryRefreshListIndexNonePageDescription": "当前页为首页无法支持翻页，请刷新列表或用序号跳转至您想查询的位置",
      "queryRefreshListIndexNonePageDescription1": "当前页为最后一页无法支持翻页，请刷新列表或用序号跳转至您想查询的位置",
      "countFunctionDescription": "1.功能号可以包含数字和英文字符“?”\\n2.英文字符“?”表示单个占位符，使用该占位符则表示使用模糊匹配\\n3.多个功能号使用英文字符“;”分隔",
      "clearDataMessage": "确定清空抓包数据吗?",
      "nodeApplicationLabel": "已托管应用节点",
      "noneNodeApplicationItem": "无托管应用节点",
      "openCountNodeApplicationMessage": "一个应用节点仅能开启一个抓包任务",
      "startItem": "开始抓包",
      "label4": "停止抓包",
      "itemItem": "共 {{ msgTotal }} 条",
      "indexItem": "跳转序号",
      "message2": "输入用例名",
      "item": "请求",
      "item1": "响应",
      "contentEmptyDescription": "请求Body内容为空或格式不正确",
      "successItem": "发包成功！",
      "noneMessage": "无可用接入点",
      "configProductServiceLabel": "请至“产品服务配置-产品服务网关”",
      "timeItem3": "时间戳 &nbsp;",
      "item2": "开",
      "item3": "关",
      "listItem1": "&nbsp;&nbsp;&nbsp;用例列表",
      "item4": "引用",
      "saveLabel": "保存为用例",
      "message3": "发包调用总耗时：",
      "message4": "发包调用耗时：",
      "timeLabel": "查看时间戳",
      "label5": "发包协议",
      "listLog": "日志列表",
      "countDescription": "支持输入多个，采用英文分号区分",
      "pleaseinputFunctionDescription": "请输入功能号以英文分号形式间隔",
      "dataItem": "获取数据"
    },
    "productDataStorage": {
      "dataItem": "数据冷备",
      "serviceLabel": "归档服务器:",
      "directoryItem": "归档目录:",
      "dataProductItem": "产品元数据:",
      "dataProductItem1": "产品遥测数据:",
      "dataDateItem": "数据日期范围:",
      "selectItemLabel": "选择归档条件",
      "confirmItemLabel": "归档条件确认",
      "dataLabel": "时延跟踪数据",
      "dataMonitorMetric": "监控指标数据",
      "successMessage": "归档指令发送成功！",
      "failedItem": "归档失败!",
      "serviceLabel1": "归档服务器：",
      "directoryItem1": "归档目录：",
      "dataProductItem2": "产品元数据：",
      "dataProductItem3": "产品遥测数据：",
      "dataDateItem1": "数据日期范围：",
      "item": "至",
      "item1": "归档",
      "message": "每日定时清理",
      "dataDayLabel": "数据保留天数：",
      "dayItem": "最大3600天",
      "selectTimeItem": "选择清理时间",
      "enableItem": "是否启用：",
      "dateItem": "清理日期：",
      "label": "清理结果：",
      "dataDayLabel1": "数据保留天数1~3600天",
      "timeItem": "清理时间",
      "label1": "清理结果",
      "failedInfo": "失败信息",
      "dataMessage": "被清理数据范围",
      "label2": "索引号",
      "dataMessage1": "被清理数据大小",
      "successConfigLabel": "定时清理配置成功！",
      "configItem": "清理配置",
      "enableRowDescription": "(启用后生效，每日定时执行清理操作)",
      "day": "天",
      "recordLabel": "定时清理记录",
      "dataTimeItem": "数据清理时间：",
      "itemLabel": "按条件清理",
      "dataDate": "数据日期：",
      "fundItem": "资金账号：",
      "dateDayMessage": "请限制清理日期小于30天",
      "successMessage1": "清理指令发送成功！",
      "item2": "清理",
      "dataConfigItem": "数据清理配置",
      "productNode": "产品节点:",
      "dataDate1": "数据日期:",
      "dataType": "数据类型:",
      "selectItemLabel1": "选择清理条件",
      "confirmItemLabel1": "清理条件确认",
      "failedDataItem": "数据清理失败!",
      "dataType1": "数据类型：",
      "deleteWarningDataAllItemDescription": "警告: 该操作将物理删除符合条件的所有数据, 操作不可逆!",
      "confirmItem": "确认清理",
      "dataMessage2": "待归档数据清单",
      "label3": "索引名",
      "fileItemItem": "文件条数",
      "emptyLabel": "占用空间",
      "fileLabel": "总计:索引文件:",
      "fileCountItemItem": "个; 文件条数:",
      "emptyItemLabel": "条; 存储空间:"
    },
    "productServiceConfig": {
      "message": "不支持该特性",
      "message1": "是否支持该特性",
      "label": "不支持",
      "listDescription": "已支持业务链路模型列表",
      "typeItem": "业务类型",
      "message2": "业务链路度量模型",
      "selectItem": "选择模型",
      "typeSystemItem": "业务系统类型",
      "label1": "模型预览",
      "successConfigItem": "模型配置成功",
      "failedLabel": "切换模型失败",
      "description": "说明",
      "updateLogConfigTimesProductNodeSystemDescription": "切换度量模型，本产品节点下对应的「链路日志配置」将自动同步更新。本次变动需要重启采集子系统才可生效。",
      "dataLabel": "链路数据定义",
      "applicationDescription": "应用全链路时延度量模型",
      "nameItem": "模型名称",
      "systemItem": "业务系统",
      "versionSystemItem": "业务系统版本",
      "versionItem": "模型版本",
      "item": "简介",
      "label2": "跨度定义",
      "nameItem1": "跨度名称",
      "fieldItem": "字段明细",
      "dataItem": "链路数据",
      "label3": "链路模型",
      "logType": "日志类型",
      "logDirectoryLabel": "时延日志输出目录",
      "logMessage": "时延日志关键字",
      "successConfigItem1": "同步配置成功",
      "configMessage": "当前已配置链路模型:",
      "searchFunctionManage": "搜索管理功能",
      "show": ">显示",
      "updateDate": "更新日期：",
      "queryFunctionManageItem": "未查询到管理功能",
      "showLabel": "显示\"中文译名\"",
      "descriptionItem": "入参说明：",
      "label4": "入参示例：",
      "descriptionItem1": "出参说明：",
      "label5": "出参示例：",
      "allNodeClusterMessage": "强制将当前集群所有节点加入集群",
      "infoMessage": "获取普通上下文的信息",
      "infoDescription": "获取多进程上下文的信息",
      "infoRoleDescription": "获取多进程上下文的信息，展示多进程上下文的Id、Name、InstanceName、是否单机模式，和当前的身份角色等相关信息。",
      "clusterMessage": "允许指定备机加入集群",
      "nameFunctionManage": "管理功能名称",
      "nameItem2": "中文名称",
      "updateTime": "更新时间",
      "message3": "提供者，默认hs-ldp",
      "item1": "访问URL",
      "paramDescription": ", // 参数说明",
      "selectParamFieldMessage": "存在enumValues字段时，该参数为下拉选择框",
      "open": "开启",
      "listFunction": "功能列表",
      "nameFunction": "功能名称",
      "paramMessage": "原始请求参数范例",
      "paramMessage1": "原始响应参数范例",
      "addLogConfigItem": "添加链路日志配置",
      "pleaseinputNodeApplicationItem": "请输入应用节点名",
      "pleaseinputServiceItem": "请输入服务器",
      "logDirectoryPleaseinputLabel": "请输入时延日志输出目录",
      "logPleaseinputMessage": "请输入时延日志关键字",
      "addSuccess": "添加成功",
      "propertyValueComponentLabel": "并赋值到组件属性",
      "editFailed": "修改失败",
      "editValueMessage": "仅支持告警阈值修改",
      "importInfoFunctionManageItem": "导入管理功能元信息",
      "importSuccess": "导入成功",
      "importFailed": "导入失败",
      "successItem": "部分成功",
      "uploadFileDescription": "格式不正确，请上传adoc格式的文件。",
      "fileCountMessage": "文件个数超出限制",
      "uploadFileCountLabel": "最多只能上传30个文件。",
      "selectFileFunctionManageItem": "请选择管理功能adoc文件",
      "importInfoFunctionManageLabel": "确定要导入管理功能元信息？",
      "importInfoFileFunctionManageDescription": "导入新文件时，原有管理功能元信息将被覆盖，请谨慎操作！",
      "selectFile": "选择adoc文件",
      "fileNone": "暂无文件",
      "item2": "完成",
      "failedConfigMessage": "获取T3监视器配置失败",
      "rule": "规则：",
      "addressItem": "主机地址：",
      "item3": "端口：",
      "monitorRule": "监控规则",
      "monitorConfigRuleDescription": "当前的监控规则均需要在“SEE-监控”做监视器配置，APM负责计算。",
      "nameRule": "规则名称",
      "ruleItem": "规则启停",
      "monitorDescriptionRule": "监控规则说明",
      "editTimeItem": "最后修改时间",
      "detailMonitorContentRuleItem": "查看监控规则内容详情",
      "nameRule1": "规则名称:",
      "contentRule": "规则内容:",
      "descriptionRule": "规则说明:",
      "editMonitorRule": "修改监控规则",
      "selectConfigRuleDescription": "请先选择要查看T3监视器配置的规则",
      "selectCountRuleDescription": "请选择五个以下监视器规则查看",
      "monitorConfigContentCountRowRuleDescription": "请至SEE-监控-T3监视器配置中填写相关内容。一个监视器同时执行的监控规则建议不要超过5个。",
      "deleteSelectRuleLabel": "请先选择要删除的规则",
      "deleteRuleMessage": "确定要批量删除选中规则吗？",
      "enableRuleMessage": "确定要批量启用选中规则吗？",
      "ruleDescription": "确定要批量停用选中规则吗？",
      "metricPleaseinputItem": "请输入指标名",
      "valuePleaseinputItem": "请输入阈值",
      "label6": "告警等级",
      "valueLabel": "阈值与等级",
      "label7": "不能大于999999999",
      "valueItem": "差值",
      "label8": "时告警"
    },
    "productTimeAnalysis": {
      "queryExportItem": "查询结果导出",
      "exportFileItem": "导出文件名:",
      "queryNamePleaseinput": "请输入查询名称",
      "exportField": "导出字段:",
      "saveSuccess": "保存成功!",
      "querySave": "保存查询",
      "queryTime": "查询时间",
      "timeTrade": "交易时间",
      "queryName": "查询名称",
      "description": "字符长度数不得超过15!",
      "item": "每日",
      "item1": "昨日",
      "thisWeek": "本周",
      "thisMonth": "本月"
    },
    "rcmBacklogMonitor": {
      "message": "发送端上下文",
      "indexMessage": "已接收应答消息序号",
      "message1": "接收端上下文",
      "item": "最慢Acker",
      "indexMessage1": "已应答消息序号",
      "description": "按缓存积压消息数排序",
      "description1": "按网络积压消息数排序",
      "listMessage": "上下文消息积压列表",
      "message2": "积压数大于0"
    },
    "rcmDeploy": {
      "namePleaseinputProduct": "请输入产品名称",
      "configItem": "配置来源：",
      "uploadConfig": "上传配置：",
      "releaseItem": "发布目标：",
      "addressCluster": "ZK集群地址:",
      "addressPleaseinputCluster": "请输入ZK集群地址",
      "configPath": "配置路径:",
      "message": "可靠一致性主播",
      "importConfigFile": "配置文件导入",
      "addressLabel": "同步网卡地址:",
      "addressLabel1": "同步通讯地址:",
      "message1": "同步通讯端口:",
      "secondMessage": "同步心跳间隔(毫秒):",
      "secondMessage1": "同步心跳超时(毫秒):",
      "secondMessage2": "同步Ack间隔(毫秒):",
      "secondMessage3": "同步Ack超时(毫秒):",
      "message2": "同步发送窗口大小:",
      "message3": "同步最大传输单元:",
      "label": "中心名:",
      "queryTag": "查询标签",
      "item": "单例",
      "addLabel": "添加发送主题",
      "addLabel1": "添加接收主题",
      "paramConfigCluster": "集群参数配置",
      "configClusterLabel": "集群上下文配置",
      "message4": "上下文模式:",
      "statisticsRowItem": "运行时统计",
      "description": "上下文主题收发关系",
      "configInstance": "实例配置",
      "configCluster": "集群配置",
      "searchMessage": "请搜索匹配主题",
      "label1": "'共计' + dataNodes.length + '已选' + checkedNodes.length",
      "item1": "已选",
      "item2": "共计",
      "paramConfigLabel": "上下文参数配置",
      "applicationItem": "应用名:",
      "addressItem": "网卡地址:",
      "directoryLabel": "持久化目录:",
      "startMessage": "补缺端口范围开始:",
      "endMessage": "补缺端口范围结束:",
      "timeItem": "存活时间:",
      "message5": "最大传输单元:",
      "message6": "发送窗口大小:",
      "message7": "发送缓存大小(MB):",
      "description1": "套接字接收缓存大小(KB):",
      "openLabel": "开启本地回环:",
      "valuePleaseinputMessage": "请输入1到65535之间的端口值",
      "message8": "是否统一排序:",
      "message9": "是否持久化:",
      "indexMessage": "接收端是否序号协商:",
      "addressItem1": "传输地址",
      "label2": "传输端口",
      "editMonitorService": "修改监控服务",
      "monitorServiceItem": "对接监控服务",
      "updateFailed": "更新失败!",
      "configMessage": "配置物料生成范围",
      "addressClusterItem": "集群同步地址",
      "clusterLabel": "集群同步端口",
      "label3": "补缺端口",
      "message10": "上下文ID范围",
      "label4": "主题ID范围",
      "label5": "分区范围",
      "editConfigFile": "编辑配置文件",
      "versionReleaseItem": "历史发布版本",
      "remark": "备注：",
      "release": "发布",
      "item3": "还原",
      "versionItem": "}} --> 远程版本：{{ modalData.remoteVersion ||",
      "item4": "历史",
      "item5": "远程",
      "versionItem1": "}} --> 本地版本：{{ modalData.localVersion ||",
      "successConfigRelease": "RCM配置发布成功",
      "failedRelease": "发布失败",
      "successConfigItem": "RCM配置还原成功",
      "configRelease": "配置发布",
      "confirmConfigReleaseMessage": "您确认要{{ isPublish ? '发布' : '还原'}}配置吗？",
      "nameItem": "模板名称:",
      "label6": "继承模板:",
      "configLabel": "主题配置模板",
      "configClusterItem": "集群配置模板",
      "configLabel1": "通用配置模板",
      "createSuccessLabel": "主题模板创建成功",
      "createFailedLabel": "主题模板创建失败",
      "createSuccessMessage": "上下文模板创建成功",
      "secondMessage4": "心跳间隔(毫秒):",
      "secondMessage5": "心跳超时(毫秒):",
      "secondLabel": "ACK间隔(毫秒):",
      "secondLabel1": "ACK超时(毫秒):",
      "message11": "使用共享内存:",
      "message12": "是否为旁路主题:",
      "message13": "是否采用异步发送:",
      "item6": "主题:",
      "label7": "分区号:",
      "addressItem2": "通讯地址:",
      "item7": "端口:",
      "queryTag1": "查询标签:",
      "configAdvanced": "高级配置",
      "pleaseinputTagItem": "请输入匹配标签",
      "label8": "'共计' + tagList.length + '已选' + selection.length",
      "label9": "继承关系",
      "message14": "未选中模板",
      "detailConfig": "配置详情",
      "dataConfigNone": "暂无配置数据",
      "message15": "通用上下文",
      "updateSuccessLabel": "主题模板更新成功",
      "updateSuccessMessage": "上下文模板更新成功",
      "label10": "主题模板",
      "createItem": "创建模板",
      "message16": "通用上下文模板",
      "clusterMessage": "集群上下文模板",
      "instanceMessage": "上下文实例分组",
      "label11": "按主题",
      "applicationItem1": "按应用",
      "tagItem": "按标签",
      "item8": ">总</h-col><h-col span=",
      "item9": ">收</h-col><h-col span=",
      "item10": "总",
      "item11": "收",
      "item12": "发",
      "moreItem": "查看更多",
      "nameItem1": "模板名称",
      "namePleaseinputLabel": "请输入上下文名称",
      "message17": "上下文模式",
      "namePleaseinputApplication": "请输入应用名称",
      "querySelectTagItem": "请选择查询标签",
      "label12": "中心名",
      "createSuccessConfigLabel": "创建/配置上下文成功!",
      "deleteConfirmDescription": "您确认要批量删除已选中的上下文吗",
      "optionTagLabel": "所选项未绑定标签",
      "addSuccessTag": "添加标签成功!",
      "deleteSuccessTag": "删除标签成功!",
      "valuePleaseinputLabel": "请输入在1-65535之间的值",
      "nameItem2": "主题名称",
      "namePleaseinputItem": "请输入主题名称",
      "pleaseinputLabel": "请输入分区号",
      "message18": "只支持输入数字",
      "addressItem3": "通讯地址",
      "label13": "通讯端口",
      "createSuccessConfigItem": "创建/配置主题成功!",
      "selectDataItemLabel": "请选择一条或多条数据",
      "deleteLabel": "批量删除主题",
      "deleteConfirmDescription1": "您确认要批量删除已选中的主题吗？",
      "label14": "半同步",
      "label15": "全同步",
      "configLabel2": "中心内配置（Zones）",
      "valueNodeLabel": "同中心节点间RPO值",
      "confirmMessage": "消息同步的确认时刻",
      "countMessage": "关联上下文个数",
      "message19": "最大输入长度100",
      "label16": "同中心RPO",
      "typePleaseinputItem": "请输入数字类型",
      "confirmLabel": "同步确认模式",
      "label17": "已存在",
      "item13": "新建",
      "errorTypePleaseinputLabel": "rpo填写错误：请输入数字类型",
      "errorDescription": "rpo填写错误：\\n超出输入范围，输入范围在-9223372036854775808 ~ 9223372036854775807",
      "configLabel3": "中心间配置（Inter Zones）",
      "label18": "右中心",
      "valueNodeLabel1": "跨中心节点间RPO值",
      "deleteConfigItem": "该配置已删除\"",
      "deleteConfirmConfigLabel": "您确认要删除该配置吗？",
      "label19": "左中心",
      "label20": "跨中心RPO",
      "deleteConfirmConfigRowLabel": "您确认要删除该行配置吗？",
      "addressItem4": "传输地址：",
      "label21": "传输端口：",
      "addressClusterItem1": "集群同步地址：",
      "clusterLabel1": "集群同步端口：",
      "label22": "补缺端口：",
      "message20": "上下文ID范围：",
      "label23": "主题ID范围：",
      "label24": "分区范围：",
      "message21": "物料生成范围",
      "monitorService": "监控服务",
      "configReleaseItem": "待发布配置",
      "configReleaseItem1": "已发布配置",
      "configDescriptionReleaseLabel": "已发布配置告警说明",
      "downloadOnlineConfigFileNoneRowDescription": "无法在线预览RCM配置文件！请下载源文件进行本地查看。",
      "configItem1": "配置还原",
      "configVersionDescription": "本地变更：本地配置版本已经发生变更。",
      "configVersionDescription1": "远程变更：远程zk配置版本已发生变更。",
      "item14": "本地：",
      "updateVersionItem": "最后更新版本：",
      "item15": "远程：",
      "item16": "历史："
    },
    "rcmObservation": {
      "label": "发送端的ContextId",
      "label1": "接收端的ContextId",
      "label2": "消息排队",
      "label3": "消息投递",
      "label4": "缓存积压",
      "message": "消息持久化",
      "indexCountMessage": "下一个排队消息序号",
      "submitIndexLabel": "提交的消息序号",
      "indexCountMessage1": "下一个处理消息序号",
      "quantityMessage": "已经处理的消息数量",
      "failedStatisticsTimesMessage": "消息回调失败次数统计",
      "confirmClusterLabel": "集群确认消息积压",
      "applicationMessage": "应用投递消息积压",
      "message1": "待持久化消息积压",
      "indexCountMessage2": "下一个持久化消息序号",
      "quantityMessage1": "已经持久化的消息数量",
      "label5": "消息接收",
      "label6": "消息应答",
      "indexCountMessage3": "下一个待接收消息序号",
      "indexMessage": "收到的最大消息序号",
      "pendingIndexCountLabel": "下一个待处理消息序号",
      "confirmIndexMessage": "已经确认的消息序号",
      "message2": "通讯分片处理",
      "label7": "分片接收",
      "label8": "分片应答",
      "label9": "分片补缺",
      "message3": "乱序和丢包检测",
      "indexCountMessage4": "下一个待接收分片序号",
      "cancelIndexCountMessage": "下一个待取消息分片序号",
      "indexMessage1": "收到的最大分片序号",
      "confirmMessage": "已经确认的分片号",
      "message4": "最后发送ACK的原因",
      "rowLabel": "补缺执行",
      "statisticsItem": "补缺统计",
      "label10": "丢包检测",
      "statisticsItem1": "丢包统计",
      "message5": "消息发送与应答",
      "numberCountLabel": "下个消息编号",
      "message6": "消息缓存与持久化",
      "saveDescription": "缓存中保存的消息的最小消息号",
      "countDescription": "下一个待持久化的消息号",
      "message7": "已持久化消息数",
      "label11": "消息积压",
      "message8": "对端消息应答",
      "message9": "分片发送与应答",
      "countDescription1": "下一个待分配的分片号",
      "message10": "当前待发送分片号",
      "indexTimesMessage": "最后一次收到的应答序号",
      "statisticsLabel": "分片发送统计",
      "description": "异步发送的分片数目",
      "failedMessage": "底层失败的分片数目",
      "message11": "对端分片应答"
    },
    "secondAppearance": {
      "dataLabel": "持久化数据表",
      "itemItem": "条件",
      "logItem": "查看日志",
      "editItemItem": "编辑条件",
      "selectMessage": "请选择需要上场的表!",
      "logItem1": "上场日志-",
      "editItem": "全量编辑",
      "label": "批量上场",
      "label1": "加载方式:",
      "selectMessage1": "请选择加载方式",
      "fundAccount": "资金账户:",
      "itemMessage": "加载方式为必填项",
      "dataTimesRowDescription": "确定要对以下数据表进行二次上场吗？",
      "rowLabel": "确定执行",
      "recordLabel": "上场记录总数：",
      "successRecordItem": "成功记录数：",
      "failedRecordItem": "失败记录数：",
      "errorInfo": "错误信息：",
      "label2": "上场方式",
      "itemItem1": "按SQL条件",
      "fundAccountItem": "按资金账户",
      "infoLabel": "历史上场信息",
      "logItem2": "上场日志",
      "clearItemMessage": "点击清空选中的表条件",
      "editItem1": "批量编辑",
      "clearSelectItemMessage": "请选择需要清空条件的表!",
      "clearItemItem": "清空条件",
      "item": "表名:",
      "itemLabel": "过滤条件:",
      "pleaseinputItemLabel": "请输入SQL语句的where条件",
      "fieldMessage": "加载方式为必填字段",
      "itemFieldMessage": "过滤条件为必填字段",
      "emptyItemDescription": "1.输入框内请填写sql语句的where条件\\n2.条件为空时默认全表上场\\n"
    },
    "sms": {
      "label": "干系人",
      "label1": "手机号",
      "item": "邮箱",
      "editInfoLabel": "修改干系人信息",
      "addInfoLabel": "添加干系人信息",
      "message": "手机号格式不正确",
      "emptyMessage": "邮箱不能为空",
      "message1": "邮箱格式不正确",
      "addFailed": "添加失败",
      "editMessage": "告警通知模板编辑",
      "contentItem": "模板内容",
      "message2": "通知干系人",
      "message3": "外发投资者",
      "selectLabel": "选择干系人",
      "message4": "通知到手机",
      "message5": "通知到邮箱",
      "label2": "通知模板"
    },
    "sqlTable": {
      "rowLabel": "历史执行SQL",
      "searchItem": "搜索历史SQL",
      "importSuccessLabel": "导入成功，可继续导入",
      "showTimeRowLabel": "显示具体执行时间",
      "import": "导入SQL",
      "descriptionItem": "使用说明",
      "ruleItem": "SQL路由规则",
      "descriptionItem1": "SQL编写说明",
      "rowNodeLabel": "执行节点校验",
      "message": "主备同步表",
      "message1": "主备不同步表",
      "selectRowNodeItem": "请选择执行节点",
      "nodeLabel": "当前已选节点",
      "closeItem": "关闭校验",
      "item": "校验：",
      "propertyRowNodeUserDescription": "根据用户SQL所在的表属性，提供建议执行的节点",
      "item1": "注：",
      "editConfigRowPageNodeMessage": "执行节点校验配置可在页面“配置”中修改",
      "confirmRowItem": "执行确认",
      "confirmContentRowMessage": "请确认SQL执行内容与执行对象。",
      "contentRowItem": "执行SQL内容",
      "rowNodeItem": "执行节点",
      "confirmItem": "不再确认",
      "queryStrategyItem": "查询路由策略",
      "confirmRowItem1": "SQL执行确认",
      "editConfirmItem": "修改操作确认",
      "countNodeLabel": "第一个备节点",
      "nodeItem": "仅主节点",
      "updateItem": "更新操作",
      "confirmTimesRowUserDescription": "用户SQL为\"UPDATE｜DELETE｜INSERT\"操作时，执行前需进行二次确认",
      "confirmNoneItem": "无需确认",
      "confirmTypeNoneRowDescription": "不区分SQL操作类型，直接执行，无需确认",
      "confirmRowServiceDescription": "在“服务”层执行SQL，含update/delete/insert操作时，执行前需确认",
      "confirmTypeNoneRowServiceDescription": "在“服务”层执行SQL，不区分操作类型直接执行，无需确认",
      "failedDetailRowItem": "SQL执行失败详情",
      "importFile": "导入SQL文件",
      "fileRowItem": "执行SQL文件",
      "failedItem": "部分失败",
      "errorDetail": "错误详情",
      "abnormalRowItem": "执行异常",
      "emptyMessage": "SQL输入不能为空！",
      "abnormalRowItem1": "SQL执行异常",
      "abnormalItem": "异常终止",
      "rowDescription": "您有批量SQL正在执行，是否要强制终止？",
      "selectFileRowLabel": "选择要执行的文件",
      "confirmShowListAllRowNodeDescription": "SQL将在以下列表所显示的所有节点中执行，请确认。",
      "label": "不再询问",
      "item2": "选中：",
      "confirmTimesRowDescription": "表示后续SQL执行“写”操作时可直接执行，不再进行二次确认；",
      "label1": "不选中：",
      "confirmTimesRowDescription1": "表示后续SQL执行“写”操作时仍需进行二次确认；",
      "editConfirmConfigPageMessage": "修改操作确认配置可在页面“配置”中修改；",
      "editSaveConfirmContentMessage": "您确认保存当前编辑器中的内容吗？",
      "fileEmptyLabel": "文件名不得为空",
      "filePleaseinputItem": "请输入文件名",
      "rowFunctionDescription": "超过1000行语句不支持美化功能!",
      "export": "导出SQL",
      "user": "用户SQL",
      "type": "SQL类型",
      "fieldItem": "表字段",
      "selectFieldItem": "请选择表字段",
      "fieldLabel": "不存在表字段",
      "label2": "唯一索引",
      "message2": "不存在唯一索引",
      "selectMessage": "请选择唯一索引",
      "item3": "SQL模板",
      "exportFailed": "导出SQL失败",
      "exportItemLabel": "最多导出1万条",
      "failedAllRowNodeItem": "全部节点执行失败",
      "failedRowNodeLabel": "部分节点执行失败",
      "dataItem": "数据操作",
      "export1": "导出CSV",
      "exportClusterServiceMessage": "1、服务、集群模式不支持导出SQL；",
      "queryExportLabel": "2、子查询不支持导出SQL；",
      "exportItemLabel1": "3、最多导出1万条；",
      "timeRowItem": "执行时间：-ms&nbsp;",
      "timeRowItem1": "执行时间：{data.totalTime}ms&nbsp;",
      "label3": "整体耗时：",
      "item4": "APM耗时：",
      "item5": "MDB耗时：",
      "queryDataNoneLabel": "当前查询无数据返回",
      "exportTable": "导出表格",
      "timeRowItem2": "执行时间：{item.totalTime}ms&nbsp;",
      "exportDataItemLabel": "最多导出一万条数据。",
      "selectNodeClusterServiceLabel": "!isCores ? '请选择节点或集群或服务' : '请选择节点'",
      "queryNodeLabel": "输入节点名查询",
      "coreLabel": "title || (isCores ? 'MDB-SQL-多核心分发':'MDB-SQL')",
      "selectNodeClusterServiceLabel1": "请选择节点或集群或服务",
      "coreLabel1": "MDB-SQL-多核心分发",
      "loginSuccess": "登录成功!",
      "userItem": "未知用户",
      "loginLabel": "请重新登录!",
      "editLoginSuccessMessage": "修改密码成功!请重新登录!",
      "loginItem": "退出登录"
    },
    "transaction": {
      "createReport": "创建报表",
      "analysisItem": "自由分析",
      "analysisApplicationItem": "应用时延分析",
      "createFailedReport": "报表创建失败!",
      "createSuccessReport": "报表\"{{instanceName}}\"创建成功!",
      "tradeItem": "交易所：",
      "label": "席位号：",
      "timeStart": "开始时间：",
      "timeEnd": "结束时间：",
      "statisticsAnalysis": "统计分析",
      "selectMetric": "选择指标",
      "label1": "时延跨度",
      "pleaseinputLabel": "请输入席位号",
      "metricLabel": "分位数指标",
      "analysisItem1": "同比分析",
      "dateItem": "基线日期",
      "timeItem": "基线时间",
      "dateItem1": "同比日期",
      "timeItem1": "同比时间",
      "secondLabel": "每秒平均",
      "dataAnalysisMetricCountMessage": "分析指标（最多只能勾选三个指标数据）",
      "selectProductNode": "选择产品节点",
      "metricItem": "线性指标",
      "secondMessage": "每秒时延订单",
      "numberTradeLabel": "交易所申报编号",
      "item": "时延 (μs)",
      "label2": "时延订单"
    },
    "tripartiteServiceConfig": {
      "dataServiceItem": "华讯数据服务",
      "configServiceLabel": "配置服务接入代理",
      "instanceLabel": "采集器实例",
      "typeServiceItem": "接入服务类型",
      "addressServiceItem": "接入服务地址",
      "address": "Topic地址",
      "systemMessage": "已适配的业务系统",
      "productNodeItem": "绑定产品节点",
      "createDataLabel": "创建数据采集代理",
      "editDataLabel": "修改数据采集代理",
      "addSuccess": "新增成功!",
      "systemMessage1": "已适配业务系统:",
      "selectSystemLabel": "请选择业务系统",
      "address1": "KafKa地址:",
      "selectProductNodeItem": "请选择产品节点",
      "countRowMessage": "一个主题占一行",
      "item": "Topic主题:",
      "productNodeItem1": "绑定产品节点:"
    },
    "ustTableVerification": {
      "recordRowLabel": "查看执行记录",
      "editTag": "修改标签",
      "label": "表总量",
      "label1": "校验中",
      "item": "通过",
      "abnormalItem": "程序异常",
      "valueLabel": "达到阈值",
      "label2": "不通过",
      "label3": "主动停止",
      "dataItem": "源数据库",
      "dataLabel": "目标数据库",
      "typeItem": "校验类型",
      "errorValueItem": "错误阈值",
      "rowMessage": "最近执行情况",
      "rowMessage1": "当前执行进度",
      "rowLabel": "执行耗时",
      "label4": "校验结果",
      "selectDataDescription": "确定校验对象 - 选择【源数据库】与【目标数据库】。",
      "selectDataCoreClusterDescription": "根据实际需要，可选择指定核心集群下的源数据库（默认）与目标数据库",
      "selectDataContentMessage": "确定校验内容 - 选择校验数据",
      "createSelectDataContentNoneCountStepDescription": "基于前两个步骤的选择，将对应产生的任务数与任务对应的校验内容汇总。请仔细核对，无误后点击“创建数据校验任务”即可。",
      "contentItem": "校验内容",
      "endIdNoneDescription": "视为SQL输入结束的标识符，如果SQL语句中无",
      "label5": "校验范围",
      "clusterLabel": "集群内校验",
      "label6": "按表总量",
      "fieldItem": "按表字段",
      "fieldItem1": "校验字段",
      "descriptionItem": "SQL语法说明.md",
      "countMessage": "最大输入长度500个字符",
      "itemMessage": "只允许写一条select语句",
      "description": "输入框输入范围为1~1000的正整数",
      "selectDataItem": "选择校验数据",
      "selectMessage": "至少选择一张表",
      "item1": "任务",
      "errorDataSettingValueDescription": "“按表总量校验”不设置错误阈值。即不论校验过程是否出错，均完成全量数据校验",
      "downloadLabel": "下载SQL语法指南",
      "errorCountItem": "按错误个数",
      "valueCountFieldDescription": "当表字段不一致个数达到阈值时，校验任务自动停止，校验不通过",
      "createDataLabel": "创建数据校验任务",
      "recordRowItem": "执行记录",
      "label7": "校验耗时",
      "dataLabel1": "源表数据总数",
      "dataMessage": "目标表数据总数",
      "fieldMessage": "校验不一致字段数",
      "dataItem1": "源数据库:",
      "dataLabel2": "目标数据库:",
      "typeItem1": "校验类型:",
      "errorValueItem1": "错误阈值:",
      "contentItem1": "校验内容:",
      "selectItem": "选择表",
      "selectField": "选择字段",
      "selectLabel": "直接选择表",
      "message": "使用预定义SQL模板",
      "item2": "SQL模板01",
      "dataCountDescription": "比对两个选定数据库，其中库里面的表结构一致，并且使用指定主键",
      "queryLabel": "输入表名查询",
      "editNameItem": "修改任务名称",
      "namePleaseinputItem": "请输入任务名称",
      "label8": "目标表",
      "recordLabel": "源表记录数",
      "recordLabel1": "目标表记录数",
      "finishedContentMessage": "已完成的校验内容结果如下：",
      "fieldDescription": "「表字段」校验暂不支持线上查看结果，可点击",
      "downloadItem": "下载结果",
      "message1": "至本地查看",
      "successItem": "启动成功！",
      "selectItemRuleLabel": "请选择一条或多条规则",
      "message2": "停止校验中！",
      "item3": "所选",
      "item4": "此",
      "deleteSuccessItem": "任务删除成功！",
      "nameItem": "任务名称:",
      "contentMessage": "查看校验任务内容",
      "deleteRemoveListRowDescription": "删除校验任务后，任务将从列表移除，同时任务所产生的执行历史不可追溯。请谨慎操作。",
      "listLabel": "校验任务列表",
      "message3": "校验任务总数:"
    }
  },
  "store": {
    "allFundItem": "全部资金账号"
  },
  "utils": {
    "tagItem": ", label: 标签名 vaule: [], lineType:",
    "clearSuccessDataItem": "数据库已成功清空",
    "clearErrorDataLabel": "清空数据库时发生错误:",
    "failedItem": "RSA 加密失败:",
    "clusterItem": "未知集群",
    "serviceItem": "未知服务",
    "item": "万亿",
    "item1": "亿",
    "item2": "万",
    "failedItem1": "JSON解析失败:",
    "pleaseinputLabel": "请输入整型数字",
    "description": "超出输入范围，输入范围在-2147483648 ~ 2147483647",
    "description1": "超出输入范围，输入范围在0 ~ 4294967295",
    "description2": "超出输入范围，输入范围在-32768 ~ 32767",
    "description3": "超出输入范围，输入范围在0 ~ 65535",
    "description4": "超出输入范围，输入范围在-9223372036854775808 ~ 9223372036854775807",
    "description5": "超出输入范围，输入范围在0 ~ 18446744073709551615",
    "description6": "所占字节数超出输入范围",
    "description7": "超出输入范围，输入范围为-3.40282e38 ~ 3.40282e38",
    "description8": "超出输入范围，输入范围为-1.79769e308 ~ 1.79769e308",
    "description9": "超出输入范围，输入范围为-1.19e4932 ~ 1.19e4932",
    "pleaseinputItem": "请输入0或1",
    "description10": "至少要包含字母 (a~zA~Z)、数字(0~9)、特殊符号(!、$、#、@、*、_)3种字符,长度8~20位",
    "pleaseinputItem1": "请输入数字",
    "typeMessage": "布尔类型只支持true、false",
    "validPleaseinputMessage": "请输入有效的数组格式",
    "validPleaseinputMessage1": "请输入有效的对象格式",
    "emptyNodeMessage": "单击空白处放置新节点。",
    "addConnectItem": "添加连接线",
    "item3": "返回",
    "item4": "關閉",
    "connectNoneMessage": "无法将连接线连接到群集。",
    "deleteItem": "删除选定",
    "deleteNoneLabel": "无法删除群集。",
    "connectCountNodeDescription": "单击某个节点并将该连接线拖动到另一个节点以连接它们。",
    "editNoneLabel": "无法编辑群集。",
    "editConnectItem": "编辑连接线",
    "connectNodeDescription": "单击控制节点并将它们拖到节点上连接。",
    "editNode": "编辑节点",
    "closeConnect": "ws连接关闭",
    "abnormal": "WS异常",
    "connectAbnormal": "ws连接异常！"
  },
  "pages": {
    "accordMonitor": {
      "dataCoreMessage": "主备核心数据同步差量告警：",
      "refreshDataLabel": "数据自动刷新频率：",
      "second": "秒",
      "dataCoreItem": "核心数据上场"
    },
    "accordObservation": {
      "dataCoreItem": "核心数据同步",
      "item": "总览"
    },
    "analyseConfig": {
      "importInfoEmptyDescription": "场景信息为空,请新建或导入场景信息",
      "exportSuccessDataItem": "导出场景数据成功!",
      "exportFailedItem": "导出场景失败!",
      "exportItem": "导出场景",
      "importItem": "导入场景"
    },
    "analyseData": {
      "noneItem": "暂无图表",
      "metricItem": "跨度/指标",
      "detailMetric": "指标详情",
      "orderLabel": "柜台委托号",
      "dataItem": "时延数据",
      "secondItem": "纳秒",
      "secondItem1": "微秒",
      "selectInstance": "选择实例",
      "instanceItem": "测试实例",
      "label": "来源用例",
      "label1": "归属场景",
      "startTime": "开始时间",
      "paramItem": "测试参数",
      "analysisMessage": "`时延场景测试分析（${selectInstance.instanceName ||"
    },
    "apmMonitorConfig": {
      "selectObservationNodeMessage": "当前节点不支持观测！请重新选择节点"
    },
    "appRunningState": {
      "statusApplicationItem": "应用状态墙",
      "saveConfigAllMessage": "您确定保存当前所有配置吗？",
      "serviceItem": "服务视图",
      "deployItem": "部署视图",
      "configBasic": "基础配置",
      "showNodeItem": "显示仲裁节点",
      "editSaveConfigTimeStatusNodeCoreDescription": "可配置核心节点在各时间段内的标准状态。当节点在某一时间内的状态与预设标准不符，将会告警。注意：同一时间只存在一种状态，修改后记得及时保存。",
      "addRule": "新增规则"
    },
    "brokerDataLimit": {
      "configLabel": "降级熔断配置",
      "listReleaseItem": "发布名单列表",
      "configLabel1": "黑名单配置",
      "configLabel2": "白名单配置",
      "configLabel3": "限流名单配置",
      "configItem": "组配置",
      "releaseItem": "发布中",
      "enableListReleaseDescription": "确定要发布列表中已启用的黑名单、白名单、限流名单吗？"
    },
    "displaySettingDrawer": {
      "selectCountFunctionDescription": "可通过开关选择你想要在此分类下展示的功能号。功能号过多可能导致界面拥挤，影响查看。建议选择不超过30个。",
      "queryFunctionLabel": "输入功能号查询",
      "label": "是否展示",
      "selectCountDescription": "为保证查看体验，建议选择不超过30个。",
      "queryAbnormalFunctionItem": "查询功能号异常",
      "successSetting": "设置成功",
      "failedSetting": "设置失败",
      "failedSettingLabel": "设置是否展示失败",
      "functionLabel": "已展示功能号："
    },
    "index": {
      "coreFunctionLabel": "核心功能号处理",
      "selectLabel": "请选择分片",
      "rowLabel": "执行吞吐(tps)",
      "rowMessage": "平均执行耗时(ns)",
      "errorTimesItem": "错误次数(次)",
      "columnLabel": "队列积压",
      "queryFailedItem": "查询分类失败",
      "queryInfoAbnormalItem": "查询分片信息异常",
      "queryInfoCluster": "查询集群信息",
      "loadingClusterItem": "分片、集群加载中",
      "showSettingFunctionItem": "功能号显示设置",
      "editConfigItem": "配置应急修改",
      "nameNodeItem": "输入节点名称",
      "failedConfigMessage": "初始化配置应急失败",
      "item": "盘前",
      "item1": "盘中",
      "item2": "盘后",
      "item3": "，在",
      "item4": "和",
      "exportContentItem": "确定导出内容",
      "exportRowItem": "执行导出中!",
      "exportFailedRowItem": "执行导出失败!",
      "exportSelectLabel": "请先选择导出的表",
      "directoryItem": "获取目录err,",
      "dataDirectoryMessage": "拷贝至APM数据存储目录",
      "exportData": "导出MDB数据",
      "recordItem": "上场记录"
    },
    "createRule": {
      "monitorMetricItem": "获取监控指标",
      "configContentRule": "配置规则内容",
      "contentRowItem": "预执行内容",
      "selectCoreItem": "请选择核心",
      "rowItem": "执行SQL",
      "label": "测试结果",
      "noneItem": "暂无结果",
      "pleaseinputRuleMessage": "请输入规则名（不超过20字符）",
      "descriptionRule": "规则说明",
      "descriptionPleaseinputRuleMessage": "请输入规则说明（不超过200字符）",
      "metric": "指标1",
      "metric1": "指标2",
      "metricLabel": "指标名重复",
      "successLabel": "获取变量成功",
      "failedMessage": "变量获取失败，请重试！",
      "failedLabel": "获取变量失败,",
      "failedMessage1": "测试失败，请重试",
      "failedItem": "测试sql失败",
      "createFailedRule": "规则创建失败",
      "queryFailedCore": "查询核心失败",
      "createMonitorRuleLabel": "创建监控规则-自定义SQL",
      "rowItem1": "预执行SQL",
      "rowDescription": "“预执行SQL”执行后可得到对应变量，变量可在下方“执行SQL”被引用。点击",
      "label1": "获取变量",
      "showDescription": "点击“获取变量”，生成对应可引用变量，在下方显示",
      "message": "可引用变量",
      "contentRowLabel": "正式执行内容",
      "label2": "SQL语法指南",
      "rowColumnStrategyDescription": "统一策略：取结果集的第一行第一列，结果集必须是数字",
      "item": "SQL结果",
      "dataMetricSettingValueDescription": "针对上一步获取的比对数据所形成的告警指标，设置对应的告警阈值。",
      "configContentRuleLabel": "下一步：配置规则内容",
      "createMonitorRule": "创建监控规则"
    },
    "dataSecondAppearance": {
      "createTimesMessage": "创建二次上场任务"
    },
    "addModal": {
      "addItem": "添加路由",
      "addFailedItem": "添加路由失败"
    },
    "helpModal": {
      "configDescription": "配置说明"
    },
    "routeConfig": {
      "configItem": "路由配置",
      "functionLabel": "输入功能号",
      "configItem1": "配置json预览",
      "failedConfigLabel": "获取路由配置失败",
      "updateConfigLabel": "确定要更新配置吗？",
      "updateParamConfigDescription": "参数配置更新后实时生效，重启后失效。",
      "deleteMessage": "确定要删除该路由？",
      "addEditDeleteInfoConfigNoneLabel": "路由配置无version信息，不可添加、删除、修改配置。",
      "updateConfig": "更新配置"
    },
    "routeInfoForm": {
      "editSelectMessage": "请选择要修改的路由",
      "failedMessage": "获取目标端id失败",
      "valueCountFunctionDescription": "支持*、?、数字(不为负值)。多个功能号使用英文分号分隔",
      "valueCountFunctionDescription1": "支持*、数字(不为负值、最大255)。多个功能号使用英文分号分隔",
      "valueDescription": "支持*、数字(不为负值、最大65535)",
      "nodeItem": "节点号"
    },
    "util": {
      "otherDescription": "*只能单独存在，不能与其他组合",
      "message": "不能以问号开头",
      "description": "英文问号必须连续且在最后",
      "message1": "数字不合法",
      "valueMessage": "数字不能为负值",
      "message2": "数字不能大于255",
      "message3": "数字不能大于65535",
      "selectLabel": "请选择目标端ID"
    },
    "latencyTrendAnalysis": {
      "analysisMessage": "链路时延趋势分析",
      "statusItem": "链路状态",
      "applicationMessage": "应用分段时延趋势",
      "statisticsApplicationLabel": "应用分段时延统计",
      "item": "90分位（p90）",
      "message": "查看时延分布",
      "applicationMessage1": "应用分段时延分布",
      "statisticsApplicationLabel1": "应用逐笔时延统计",
      "querySelectPageItem": "请选择查询分页",
      "orderItem": "委托笔数:",
      "item1": "时延:",
      "configRowProductServiceDescription": "当前产品尚未配置模型，请前往“产品服务配置”进行模型配置",
      "configItem": "前往配置"
    },
    "ldpDataObservation": {
      "selectObservationNodeApplicationMessage": "当前应用节点不支持观测！请重新选择节点"
    },
    "ldpLinkConfig": {
      "configProductNodeManage": "产品节点配置管理",
      "productNodeItem": "对接产品节点",
      "registerProductNodeItem": "已注册产品节点",
      "deleteProductNode": "删除产品节点",
      "infoNodeItem": "同步节点信息"
    },
    "ldpLogCenter": {
      "errorMessage": "回库错误重试运维",
      "errorLabel": "回库错误重试",
      "queryLogTimeItem": "日志查询超时时间："
    },
    "clusterMonitor": {
      "statusCluster": "集群状态"
    },
    "ldpAppMonitor": {
      "coreItem": "核心性能"
    },
    "ldpTable": {
      "queryLabel": "查询内存表",
      "queryData": "数据查询",
      "editData": "数据修改",
      "message": "内存表结构",
      "selectRowMenuDescription": "请从左侧菜单选择内存表进行查看！",
      "selectConnectNodeApplicationItem": "请选择并连接应用节点！"
    },
    "locateConfig": {
      "configMessage": "Locate配置一致性校验",
      "configNodeManage": "节点配置管理",
      "configNodeItem": "节点配置校验"
    },
    "managementQuery": {
      "selectFunctionManageMessage": "请选择管理功能手动发起请求",
      "selectFunctionManageItem": "请选择管理功能",
      "activeNoneNodeItem": "当前暂无活跃节点",
      "dataOther": "其他数据",
      "failedConfigMessage": "初始化默认jsonPath配置失败:",
      "querySaveParamTimesFunctionDescription": "如需保存当前选中功能号输入参数，请手动触发一次查询请求！",
      "failedConfigItem": "获取jsonPath配置失败:",
      "dataNoneFunctionLabel": "功能号无返回数据！",
      "exportDataAllProductFunctionManageMessage": "支持批量导出当前产品下的所有管理功能数据",
      "exportItem": "快捷导出",
      "exportAllProductCoreFunctionManageMessage": "点击一键导出所选产品管理所有核心的「GetFuncDetailInfo」管理功能",
      "nodeApplicationItem": "应用节点耗时：",
      "closeAll": "关闭全部"
    },
    "marketAllLink": {
      "rowNodeApplicationMessage": "行情全链路应用节点关系",
      "monitorCustomerMessage": "终端客户全链路质量监控",
      "dataMonitorRowMessage": "行情数据时延趋势监控",
      "label": "FPGA全链路",
      "statusNodeApplication": "应用节点状态",
      "label1": "NSQ全链路"
    },
    "marketMonitor": {
      "rowLabel": "快照行情(μs)",
      "rowLabel1": "指数行情(μs)",
      "orderItem": "逐笔委托(μs)",
      "dealItem": "逐笔成交(μs)",
      "showItem": "显示拓扑"
    },
    "marketNodeDelayList": {
      "analysisRowProductNodeMessage": "行情产品节点穿透时延分析",
      "queryItem": "汇总查询",
      "tradeItem": "交易日",
      "item": "最小(min)",
      "label": "1分位数(p1)",
      "label1": "中位数(p50)",
      "label2": "平均数(avg)",
      "label3": "95分位数(p95)",
      "label4": "99分位数(p99)",
      "item1": "最大(max)",
      "statisticsItem": "统计总数",
      "queryFailed": "查询失败!"
    },
    "marketPenetrateList": {
      "label": "下单数",
      "message": "全链路时延",
      "rowMessage": "柜台上行时延",
      "rowMessage1": "柜台下行时延",
      "rowLabel": "TGW上行时延",
      "rowLabel1": "TGW下行时延",
      "item": "TGW时延",
      "tradeLabel": "交易所时延",
      "rowTradeLabel": "行情-交易时延",
      "rowTradeMessage": "交易-收到行情时延",
      "rowMessage2": "防火墙上行时延",
      "rowMessage3": "防火墙下行时延",
      "label1": "拓扑结构",
      "tradeItem": "交易市场",
      "label2": "报盘网关",
      "rowLabel2": "行情网关",
      "fundAccount": "资金账户",
      "label3": "汇总间隔",
      "label4": "汇总方式",
      "queryDetail": "详情查询",
      "date": "日期"
    },
    "marketTimeDelay": {
      "selectTradeItem": "选择交易所",
      "selectDataType": "选择数据类型",
      "analysisSystemMessage": "本地全系统时延走势分析(μs)",
      "tradeMessage": "交易所到消费端总时延(ms)",
      "queryDataLabel": "查询的数据不存在",
      "analysisRowSystemMessage": "行情系统时延走势分析"
    },
    "mcDataObservation": {
      "clusterLabel": "已托管MC3.0集群"
    },
    "mcDeploy": {
      "configManage": "MC3.0配置管理"
    },
    "constant": {
      "exportData": "MDB数据导出",
      "exportItem": "待导出",
      "exportSelectMessage": "按需选择需要导出的表",
      "exportContentNoneRuleDescription": "请仔细核对导出内存表内容和规则。无误请点击“导出”。",
      "configLabel": "链路模型配置",
      "configItem": "模型配置",
      "logConfigItem": "链路日志配置",
      "productServiceItem": "产品服务网关",
      "monitorRowApplicationItem": "应用运行监控",
      "configService": "服务配置"
    },
    "tableSelector": {
      "searchMessage": "输入表名/中文名搜索",
      "label": "中文名",
      "queryFailedTableInstanceItem": "切换实例，查询表格失败,"
    },
    "detailDrawer": {
      "pathItem": "远程路径",
      "exportStatusLabel": "导出状态和结果",
      "failedDetailLabel": "查看历史详情失败"
    },
    "exportHistory": {
      "item": "任务ID",
      "exportStatus": "导出状态",
      "exportQuantityLabel": "导出内存表数量",
      "deleteDataServiceLabel": "删除远程服务器数据",
      "deleteDataServiceItem": "删除APM服务器数据",
      "deleteItem": "已删除",
      "deleteFailedLabel": "删除历史任务失败,",
      "downloadFailedLabel": "下载单张表失败,",
      "deleteExportDataContentServiceDescription": "删除导出任务的同时，可同时删除该任务导出在服务器的内存表数据。请勾选需要删除的内容。",
      "deleteExportDataContentServiceDescription1": "删除导出任务，可同时删除该任务导出在服务器的内存表数据。请勾选需要删除的内容。"
    },
    "exportTable": {
      "exportTimesLabel": "最近一次导出"
    },
    "mdbDataObservation": {},
    "mdbPrivilegeManage": {
      "managePermission": "MDB权限管理",
      "label": "(headerMessage || '- ')+'(最新下发：'+ (hisPermissionsInfo.headerTime || '- ') + ' ' + headerStatus + '）'",
      "userPermissionMessage": "将本地用户权限下发至MDB",
      "editPermissionItem": ">权限可编辑  &nbsp;&nbsp;&nbsp;&nbsp;<h-switch v-model=",
      "contentPermissionLabel": "）。若当前权限内容为",
      "editContentPagePermissionDescription": "权限内容。如需编辑，请至当前页面顶部",
      "label1": "(最新下发：",
      "label2": "}}(最新下发：{{hisPermissionsInfo.headerTime ||",
      "failedItem": "下发失败",
      "successItem": "下发成功",
      "manageUser": "用户管理",
      "manageRole": "角色管理",
      "label3": "下发结果",
      "permissionMessage": "MDB权限与本地权限一致。",
      "permissionMessage1": "MDB权限与本地权限不一致。",
      "nonePermissionDescription": "暂时无法获取MDB权限，请稍后查看。",
      "successPermissionItem": "权限下发成功！",
      "abnormalPermissionItem": "权限下发异常！",
      "failedPermissionItem": "权限下发失败！",
      "editEditImportOpenOnlineVersionContentUserRolePermissionDescription": "开启后可在线修改MDB权限内容（包括\"用户\"与\"角色\"）。若当前权限内容为\"导入\"产生，编辑可能导致主备权限版本不一致。请谨慎！",
      "exportSuccess": "导出成功!",
      "importSuccessPermission": "导入权限成功",
      "editImportOpenSuccessOnlineContentPageManagePermissionDescription": "MDB权限导入成功。为保证主备机房间权限一致，导入权限后默认不可在线\"编辑\"权限内容。如需编辑，请至当前页面顶部\"MDB权限管理-权限操作\"中开启。",
      "importFailedPermission": "导入权限失败",
      "failedDetailTimesUserPermissionDescription": "用户权限部分下发失败，请稍后重新尝试。本次下发详细如下：",
      "failedDetailTimesUserPermissionDescription1": "用户权限下发失败，请稍后重新尝试。本次下发详细如下：",
      "editEditSuccessStatusPermissionItem": "权限可编辑状态修改成功!",
      "permissionItem": "权限操作",
      "permissionItem1": "同步权限",
      "permissionItem2": "查看MDB权限",
      "exportPermission": "导出权限",
      "editPermissionItem1": "权限可编辑  &nbsp;&nbsp;&nbsp;&nbsp;"
    },
    "businessMonitor": {},
    "networkSendAndRecevied": {
      "applicationItem": "应用抓包",
      "label": "抓包回放",
      "applicationItem1": "应用发包",
      "label1": "发包用例"
    },
    "noticeManagerList": {
      "manageLabel": "告警人员管理",
      "label": "告警人员",
      "nameLabel": "干系人名称"
    },
    "productDataStorage": {
      "dataMonitorManage": "监控数据管理",
      "label": "主分片数",
      "label1": "从分片数",
      "itemLabel": "文档条数",
      "emptyLabel": "空间占用",
      "tradeOrderItem": "委托交易时延",
      "queryDataOrderItem": "委托查询时延数据",
      "monitorMetric": "监控指标",
      "label2": "定时清理"
    },
    "productServiceList": {
      "configProductService": "产品服务配置",
      "listProductService": "产品服务列表"
    },
    "productTimeDetail": {
      "nodeTradeOrderLabel": "交易节点委托时延明细",
      "typeItem": "链路类型",
      "queryDate": "查询日期",
      "label": "时延筛选",
      "timeOrder": "委托时间",
      "queryFailedLabel": "获取快捷查询失败!",
      "querySaveFailedItem": "保存快捷查询失败!",
      "queryDeleteFailedItem": "删除快捷查询失败!",
      "dataItemMessage": "当前数据为最后一条",
      "itemItem": "上一条",
      "itemItem1": "下一条"
    },
    "productTimeSummary": {
      "queryMessage": "链路时延汇总查询",
      "week": "周",
      "month": "月",
      "orderItem": "总委托数"
    },
    "rcmBacklogMonitor": {
      "monitorMessage": "上下文积压监控",
      "listConfig": "RCM配置列表"
    },
    "rcmDeploy": {
      "deleteConfig": "删除配置",
      "tipConfigReleaseMessage": "请查看待发布配置和已发布配置告警提示"
    },
    "rcmObservation": {
      "observationRowLabel": "上下文运行观测",
      "productMessage": "请先去建立产品"
    },
    "publishStatusDetail": {
      "noneRowMessage": "当前无上场任务执行",
      "infoItem": "上场信息",
      "timeStart": "开始时间:",
      "timeEnd": "结束时间:",
      "quantityLabel": "需上场表数量:",
      "successQuantityLabel": "上场成功表数量:",
      "failedQuantityLabel": "上场失败表数量:"
    },
    "smsList": {
      "manageLabel": "告警通知管理",
      "paramLabel": "告警通知参数",
      "statusItem": "通知状态",
      "contentItem": "通知内容",
      "label": "告警通知",
      "timeLabel": "告警通知时间",
      "message": "匹配通知模板",
      "item": "人",
      "label1": "自动通知",
      "statusItem1": "匹配状态",
      "label2": "已通知",
      "label3": "未通知",
      "timeItem": "通知时间",
      "selectMessage": "请选择通知模板",
      "item1": "匹配",
      "label4": "未匹配",
      "selectStatusLabel": "请选择匹配状态",
      "selectStatusLabel1": "请选择通知状态",
      "message1": "通知外发历史",
      "createTime": "创建时间",
      "label5": "通知方式",
      "item2": "短信",
      "label6": "电子邮件",
      "addressItem": "通知地址",
      "message2": "通知提供方",
      "selectMessage1": "请选择通知方式",
      "contentPleaseinputItem": "请输入通知内容",
      "timeItem1": "发送时间",
      "successItem": "发送成功",
      "failedItem": "发送失败",
      "addressPleaseinputItem": "请输入通知地址",
      "manageLabel1": "管理通知模版"
    },
    "sqlCores": {
      "label": "帮助手册",
      "emptyTagLabel": "标签名不得为空",
      "tagMessage": "当前标签名已存在",
      "pleaseinputTagItem": "请输入标签名",
      "editContentMessage": "编辑器内容超过1M不支持暂存!",
      "selectRowNodeLabel": "请选择要运行的节点！",
      "selectItemRowDescription": "请选择要执行的sql语句，不得超过1条！",
      "configTimeEmptyMessage": "超时时间不能配置为空或0！",
      "timeMinuteMessage": "超时时间不得超过10分钟！",
      "dataRowPageMessage": "该页数据不存在，请重新执行",
      "rowItem": "执行",
      "item": "历史SQL",
      "item1": "超时:"
    },
    "sqlTable": {
      "message": "输入表名过滤",
      "showItem": ">仅显示",
      "item": "索引",
      "label": "关联关系",
      "typeItem": "索引类型",
      "fieldItem": "索引字段",
      "message1": "关联对象身份",
      "typeItem1": "关联类型",
      "item1": "包含",
      "item2": "关联",
      "fieldItem1": "关联字段",
      "label1": "关联索引",
      "selectNoneRowNodeServiceStrategyDescription": "当前服务在此路由策略下无可执行节点，请重新选择",
      "nodeDescription": "此分片表在以下主节点中存在",
      "noneRowNodeCoreStrategyDescription": "当前模式下无可执行的核心节点，请调整路由策略重试",
      "dataNoneRowNodeDescription": "仅表示行数据对应的SQL执行的节点，与执行结果无关",
      "showDescription": "仅显示\"不支持主备同步\"表"
    },
    "threadInfoOverview": {
      "configApplicationItem": "应用检查配置"
    },
    "topoMonitor": {
      "applicationMessage": "scene === `topologyObservation` ? `应用拓扑结构` : `RCM拓扑结构`",
      "productDescription": "当前产品不支持拓扑展示",
      "applicationLabel": "应用拓扑结构",
      "label": "RCM拓扑结构"
    },
    "transaction": {
      "selectProduct": "选择产品",
      "queryMessage": "查询订单不存在",
      "failedDataAnalysisLabel": "获取同比分析数据失败！",
      "createAnalysisReport": "创建分析报表",
      "message": "链路历史时延走势",
      "queryLabel": "订单时延查询"
    },
    "tripartiteServiceList": {
      "serviceManageLabel": "三方服务集成管理",
      "listServiceItem": "三方服务列表"
    },
    "ustTableVerification": {
      "dataItem": "数据校验"
    }
  }
};