<template>
    <div>
        <h-msg-box-safe
            v-model="modalData.status"
            :escClose="true"
            :mask-closable="false"
            title="修改密码"
            width="600"
        >
            <h-form
                ref="formValidate"
                :model="formValidate"
                :label-width="120"
                :rules="ruleValidate"
                >
                <h-form-item label="输入当前密码" prop="curPasswd" required>
                    <h-input
                        v-model.trim="formValidate.curPasswd"
                        placeholder="当前密码"
                        type="password"
                        :maxlength="20"
                    ></h-input>
                </h-form-item>
                <h-form-item label="输入新密码" prop="passwd" required>
                    <h-input
                        v-model.trim="formValidate.passwd"
                        placeholder="请输入新密码"
                        type="password"
                        :maxlength="20"
                    ></h-input>
                </h-form-item>
                <h-form-item label="再次输入新密码" prop="passwdCheck" required>
                    <h-input
                        v-model.trim="formValidate.passwdCheck"
                        placeholder="请再次输入新密码"
                        type="password"
                        :maxlength="20"
                    ></h-input>
                </h-form-item>
            </h-form>

            <template v-slot:footer>
                <a-button @click="modalData.status = false">取消</a-button>
                <a-button type="primary" :loading="loading" @click="submitConfig">确定</a-button>
            </template>
        </h-msg-box-safe>
    </div>
</template>

<script>
import aButton from '@/components/common/button/aButton';
import { validatePass } from '@/utils/validate';
export default {
    props: {
        modalInfo: {
            type: Object,
            default: null
        }
    },
    data() {
        const validateNewPass = (rule, value, callback) => {
            if (value === '') {
                callback(new Error('输入新密码'));
            } else {
                if (this.formValidate.passwdCheck !== '') {
                    // 对第二个密码框单独验证
                    this.$refs.formValidate.validateField('passwdCheck');
                }
                callback();
            }
        };
        const validatePassCheck = (rule, value, callback) => {
            if (value === '') {
                callback(new Error('请再次输入密码'));
            } else if (value !== this.formValidate.passwd) {
                callback(new Error('两次输入密码不一致!'));
            } else {
                callback();
            }
        };
        const stringRule = {
            type: 'string',
            min: 8,
            max: 20,
            message: '请输入密码8~20位',
            trigger: 'blur'
        };
        return {
            modalData: this.modalInfo,
            loading: false,
            formValidate: {
                curPasswd: '',
                passwd: '',
                passwdCheck: ''
            },
            ruleValidate: {
                curPasswd: [
                    stringRule,
                    { validator: validatePass, trigger: 'blur' }
                ],
                passwd: [
                    { validator: validateNewPass, trigger: 'blur' },
                    stringRule,
                    { validator: validatePass, trigger: 'blur' }
                ],
                passwdCheck: [
                    { validator: validatePassCheck, trigger: 'blur' },
                    stringRule,
                    { validator: validatePass, trigger: 'blur' }
                ]
            }
        };
    },
    mounted(){
        this.$refs['formValidate'].resetFields();
    },
    methods: {
        submitConfig() {
            this.$refs['formValidate'].validate((valid) => {
                if (valid) {
                    this.$emit('update-passwd', this.formValidate);
                    this.modalData.status = false;
                }
            });
        }
    },
    components: { aButton }
};
</script>
