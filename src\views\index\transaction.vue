<template>
    <div class="main">
        <!-- C1C2链路时延 -->
        <header>
            <a-title title="链路历史时延走势"></a-title>
            <div class="slot-box">
                <!-- 选择产品 -->
                <h-select v-show="productList.length > 1"
                    v-model="modalInfo.formItem.productInstNo" class="line-box line-product" placeholder="选择产品"
                    :clearable="false" :disabled="loading" setDefSelect @on-change="quickSearch">
                    <h-option v-for="item in modalInfo.productList" :key="item.productInstNo" :value="item.productInstNo">
                        {{ item.productName }}</h-option>
                </h-select>

                <!-- 日期选择器 -->
                <h-date-picker v-model="modalInfo.formItem.date" class="line-box date" type="date" format="yyyy-MM-dd"
                    placement="bottom-end" placeholder="选择日期" :clearable="false" :disabled="loading" @on-change="quickSearch">
                </h-date-picker>

                <!-- 选择交易所 -->
                <h-select v-model="modalInfo.formItem.exchangeId" class="line-box" placeholder="选择交易所" :clearable="false"
                    :disabled="loading" setDefSelect @on-change="quickSearch">
                    <h-option v-for="item in exchangeIdList" :key="item" :value="item">
                        {{ getName(item) }}</h-option>
                </h-select>

                <!-- 选择链路 -->
                <h-select v-model="modalInfo.formItem.loopType" class="line-box" placeholder="链路状态" :clearable="false"
                    :disabled="loading" @on-change="quickSearch">
                    <h-option v-for="item in modelLoopList" :key="item.value" :value="item.value">{{ item.label }}</h-option>
                </h-select>

                <!-- 选择跨度 -->
                <h-select v-model="modalInfo.formItem.span" class="line-box" placeholder="选择跨度" :disabled="loading"
                    @on-change="quickSearch">
                    <h-option v-for="item in modalInfo.spanList" :key="item" :value="item">{{ spanLatencyDictDesc[item] }}</h-option>
                </h-select>

                <!-- 配置按钮 -->
                <h-icon class="icon-setting" name="android-settings" size="26" color="var(--font-color)" @on-click="showSettingModel">
                </h-icon>
            </div>
        </header>

        <h-spin v-show="loading" fix>
            <div class="demo-spin-icon-load">
                <h-icon name="load-c" size="18"></h-icon>
            </div>
            <div>Loading</div>
        </h-spin>
        <!-- echarts -->
        <div id="container" style="width: 97.5%;" class="echarts" :class="`${timeStamp.length}` === 0 ? 'hidden' : ''">
        </div>

        <!-- 单笔交易时延 -->
        <header>
            <a-title title="订单时延查询"></a-title>
            <div class="slot-box">
                <!-- GTID -->
                <h-input v-model="formItem.gtid" class="gtid line-box line-product" type="text" :disabled="lodingLink" placeholder="GTID" />
                <!-- 日期选择器 -->
                <h-date-picker v-model="formItem.date" class="line-box date" type="date" format="yyyy-MM-dd" placement="bottom-end"
                    :disabled="lodingLink" :clearable="false" placeholder="选择日期"></h-date-picker>
                <!-- 选择交易所 -->
                <h-select v-model="formItem.exchangeId" class="line-box" placeholder="选择交易所" :disabled="lodingLink"
                    :clearable="false">
                    <h-option v-for="item in exchangeIdList" :key="item" :value="item">{{
                        getName(item) }}</h-option>
                </h-select>
                <!-- 资金账号 -->
                <h-input v-model="formItem.accountId" class="line-box" type="text" :disabled="lodingLink" placeholder="资金账号" />
                <!-- 委托号 -->
                <h-input v-model="formItem.orderId" class="line-box" type="text" :disabled="lodingLink" placeholder="交易所申报编号" />
                <a-button type="dark" :loading="lodingLink" @click="getLinkData">查询</a-button>
            </div>
        </header>

        <div class="topo">
            <appTopo
                v-if="JSON.stringify(applicationViewMode) !== '{}'"
                ref="topo"
                :analyse="true"
                :template="applicationViewMode"
                @selectEdge="selectEdge" />
            <no-data  v-else text="该模型文件暂无拓扑结构"/>
        </div>
        <!-- 选择状态 -->
        <div v-if="JSON.stringify(applicationViewMode) !== '{}'" class="footer">
            <h-radio-group v-model="formItem.loopType" @on-change="checkLoop">
                <h-radio v-for="item in modelLoopList" :key="item.value" :label="item.value">{{ item.label }}</h-radio>
            </h-radio-group>
        </div>
        <!-- 多实例数据弹窗 -->
        <instance-time-delay-modal v-if="modalInstance.status" :modalInfo="modalInstance" @update="topoTimeDelay" />
        <!-- 报表生成 -->
        <report-confirm-modal v-if="reportInfo.status" :reportInfo="reportInfo" :modalInfo="modalInfo" />
        <!-- 配置弹出框 -->
        <setting-modal v-if="modalInfo.status" :modalInfo="modalInfo" :loading="loading" :modelLoopList="modelLoopList" @setInfoData="setInfoData" />
        <!-- 延时top列表 -->
        <top-list-modal v-if="modalTable.status" :modalInfo="modalTable" @getLinkData="getLinkData" />
    </div>
</template>

<script>
import _ from 'lodash';
import * as echarts from 'echarts';
import { formatDate, getExchangeName, isJSON, cutZero } from '@/utils/utils';
import { getSingleLinkData, getDelayTrendData, getProductManageList, getGelayTrendLinkCompareData, createAnalyseReport } from '@/api/httpApi';
import settingModal from '@/components/transaction/settingModal';
import topListModal from '@/components/transaction/topListModal';
import instanceTimeDelayModal from '@/components/transaction/instanceTimeDelayModal';
import reportConfirmModal from '@/components/transaction/reportConfirmModal';
import appTopo from '@/components/common/topo/appTopo';
import aTitle from '@/components/common/title/aTitle';
import aButton from '@/components/common/button/aButton';
import noData from '@/components/common/noData/noData';
import { mapState } from 'vuex';

let myChart = null;
export default {
    data() {
        const that = this;
        return {
            modalInfo: {
                status: false,
                formItem: {
                    // 公共字段
                    productInstNo: '',
                    time: ['09:15:00', '15:00:00'],
                    date: new Date(),
                    exchangeId: 'SZSE',
                    bizSysType: 1,
                    loopType: 'RtnCfmXchg',
                    seat: '',
                    accountId: '',
                    span: '',
                    interval: 1000, // 间隔时间，默认单位ms

                    // 统计分析
                    exchangeTime: 'ALL_DAY',
                    percentiles: [50, 99],
                    indicators: ['avg'],   // 线性指标

                    // 同比分析
                    compareDate: new Date(),
                    compareIndicator: 'avg'   // 同比指标
                },
                modelList: [],
                exchangeTimeMap: {},
                tabValue: 'statistical',
                productList: [], // 产品列表
                spanList: []
            },
            modalTable: {
                status: false,
                time: '',
                exchangeId: '',
                loopType: '',
                bizSysType: 0,
                span: '',
                productInstNo: '',
                seat: ''
            },
            modalInstance: {
                status: false,
                key: '',
                data: []
            },
            loading: false,
            lodingLink: false,
            timeStamp: [],
            formItem: { // 单秒数据查询参数
                gtid: '',
                date: new Date(),
                accountId: '',
                orderId: '',
                exchangeId: '',
                loopType: 'RtnCfmXchg'
            },
            option: {
                backgroundColor: '#262b40',
                grid: {
                    left: '2.5%',
                    right: '2.5%',
                    top: '5%',
                    bottom: '30',
                    containLabel: true
                },
                legend: {},
                // 提示框组件
                tooltip: {
                    trigger: 'axis',
                    enterable: true,
                    backgroundColor: 'rgba(88,94,106,0.50)',
                    borderColor: 'rgba(88,94,106,0.40)',
                    textStyle: {
                        color: '#fff',
                        fontSize: 12
                    },
                    formatter: function (params, ticket, callback) {
                        if (that.brushing) return;
                        const [x1, x2] = that.coordRange;
                        const x = params[0].dataIndex;
                        return (!isNaN(x1) && x >= x1 && x <= x2) ? that.generateHtmlStr(params, 1) : that.generateHtmlStr(params);
                    },
                    // eslint-disable-next-line max-params
                    position: function (point, params, dom, rect, size) {
                        // 鼠标坐标和提示框位置的参考坐标系是：以外层div的左上角那一点为原点，x轴向右，y轴向下
                        // 提示框位置
                        let x = 0; // x坐标位置
                        let y = 0; // y坐标位置

                        // 当前鼠标位置
                        const pointX = point[0];
                        const pointY = point[1];

                        // 提示框大小
                        const boxWidth = size.contentSize[0];
                        const boxHeight = size.contentSize[1];

                        // boxWidth > pointX 说明鼠标左边放不下提示框
                        if (boxWidth > pointX) {
                            x = 5;
                        } else {
                            // 左边放的下
                            x = pointX - boxWidth;
                        }

                        // boxHeight > pointY 说明鼠标上边放不下提示框
                        if (boxHeight > pointY) {
                            y = 5;
                        } else {
                            // 上边放得下
                            y = pointY - boxHeight;
                        }

                        return [x, y];
                    },
                    axisPointer: {
                        type: 'cross',
                        label: {
                            backgroundColor: '#6a7985'
                        }
                    }
                },
                brush: {
                    // toolbox: ['lineX'],
                    xAxisIndex: 0,
                    brushMode: 'single',
                    brushType: 'lineX',
                    // transformable: false,
                    removeOnClick: true,
                    brushStyle: {
                        borderWidth: 1,
                        color: 'rgba(88,94,106,0.40)',
                        borderColor: 'rgba(88,94,106,0.40)'
                    },
                    throttleType: 'debounce',
                    throttleDelay: 600,
                    outOfBrush: {
                        colorAlpha: 0.1
                    }
                },
                xAxis: {
                    type: 'category',
                    boundaryGap: false,
                    data: []
                },
                yAxis: {
                    type: 'value',
                    boundaryGap: false
                },
                dataZoom: [
                    {
                        type: 'inside',
                        start: 0,
                        end: 100
                    },
                    {
                        type: 'slider',
                        bottom: 5,
                        height: 20,
                        start: 0,
                        end: 100,
                        show: true
                        // showDataShadow: 'true'
                    }
                ]
            },
            reportInfo: {
                status: false,
                productName: '',
                bizSysType: '',
                sTime: '',
                eTime: '',
                title: ''
            },
            getName: getExchangeName,
            timeResultMap: {},
            coordRange: [], // 刷选区间
            brushing: false
        };
    },
    computed: {
        ...mapState({
            // TODO：参数已被删除，从/ldplt/api/v1/product/latency/trace-models接口，spans参数块，使用spanName转义spanAlias即可
            spanLatencyDictDesc: state => {
                return state?.apmDirDesc?.spanLatencyDictDesc || {};
            }
        }),
        exchangeIdList: function () {
            const info = this.modalInfo;
            return _.find(info.productList, ['productInstNo', info.formItem.productInstNo])?.exchangeIdList || [];
        },
        applicationViewMode: {
            get() {
                const modelList = this.modalInfo.modelList;
                return modelList.length ? modelList[_.findIndex(this.modelLoopList, o => {
                    return o.value === this.formItem.loopType;
                })] : {};
            }
        },
        modelLoopList: function() {
            const list = [];
            const modelList = this.modalInfo.modelList;
            if (modelList.length) {
                modelList.forEach(ele => {
                    list.push({
                        label: ele?.meta?.traceTypeAlias,
                        value: ele?.meta?.bizWorkloadTraceType
                    });
                });
            }
            this.modalInfo.formItem.loopType = _.find(list, ['value', 'RtnCfmXchg']) ? 'RtnCfmXchg' : list?.[0]?.value;
            return list;
        }
    },
    watch: {
        'modalInfo.formItem.productInstNo': {
            handler(newVal, oldVal) {
                if (newVal) {
                    const info = this.modalInfo;
                    const pList = info.productList;
                    const idx = _.findIndex(pList, o => {
                        return o.productInstNo === info.formItem.productInstNo;
                    });
                    const temp = pList[idx]?.template?.applicationViewMode;
                    this.modalInfo.modelList = (temp && isJSON(temp)) ? JSON.parse(temp) : [];
                    this.modalInfo.spanList = pList[idx].spans;
                }
            },
            deep: true
        }
    },
    methods: {
    // 生成悬浮弹窗
        generateHtmlStr(params, type = 0) {
            const style = `
            display: inline-block;
            margin-top: 5px;
            float: right;
            padding: 4px 6px;
            background: #2d8de5;
            border-radius: 4px;
            cursor: pointer;
            color: #d4d6dc;`;
            let htmlStr = '';
            if (type) {
                let [x1, x2] = this.coordRange;
                if ((x2 - x1) > 3600) x2 = x1 + 3600;
                const xName = `${this.option.xAxis.data[x1] } ~ ${this.option.xAxis.data[x2]}`;
                htmlStr += xName + '<br/>';
                htmlStr += `<span onclick="createReport('${ this.option.xAxis.data[x1] }', '${ this.option.xAxis.data[x2] }')" style="${style}">创建分析报表</span>`;
            } else {
                for (let i = 0; i < params.length; i++) {
                    const param = params[i];
                    const xName = param.name;// x轴的名称
                    const seriesName = param.seriesName;// 图例名称
                    const value = param.value;// y轴值
                    const color = param.color;// 图例颜色

                    if (i === 0) htmlStr += xName + '<br/>';// x轴的名称

                    htmlStr += '<div>';

                    // 为了保证和原来的效果一样，这里自己实现了一个点的效果
                    htmlStr += '<span style="margin-right:5px;display:inline-block;width:10px;height:10px;border-radius:5px;background-color:' + color + ';"></span>';

                    // 圆点后面显示的文本
                    htmlStr += seriesName + '：' + value + ' μs';
                    htmlStr += '</div>';
                }
                htmlStr += `<span onclick="wordClick('${params[0]?.axisValue}')" style="${style}">查看</span>`;
            }
            return htmlStr;
        },
        // 快捷查询
        quickSearch() {
            this.modalInfo.tabValue === 'statistical' ? this.getData() : this.getCompareTimeDelayData();
        },
        // 获取延时数据
        getLinkData(row) {
            if (row && row.orderId) {
                this.formItem.gtid = row.gtid;
                this.formItem.accountId = row.accoundId;
                this.formItem.orderId = row.orderId;
                this.formItem.exchangeId = this.modalInfo.formItem.exchangeId;
                this.formItem.loopType = this.modalInfo.formItem.loopType;
                this.formItem.date = this.modalInfo.formItem.date;
            }

            if (!this.formItem.gtid && (!this.formItem.accountId || !this.formItem.orderId || !this.formItem.exchangeId)) {
                this.$hMessage.warning('资金账号、委托号、交易所不得为空!');
                this.timeResultMap = {};
                this.topoTimeDelay();
                return;
            }

            this.lodingLink = true;
            const param = { ...this.formItem,
                productInstNo: this.modalInfo.formItem.productInstNo,
                // 兼容老数据
                bizSysType: _.find(this.modalInfo.productList, ['productInstNo', this.modalInfo.formItem.productInstNo]).bizSysType, date: formatDate(this.formItem.date) };
            getSingleLinkData(param).then((res) => {
                this.lodingLink = false;
                if (res.success) {
                    const { resultMap } = res?.data;
                    if (resultMap && Object.keys(resultMap).length) {
                        for (const x in resultMap) {
                            if (resultMap.hasOwnProperty(x)){
                                resultMap[x][0].selected = true;     // 默认选中第一个
                            }
                        }
                    }
                    this.timeResultMap = resultMap;
                    this.topoTimeDelay();
                } else {
                    this.$refs['topo'].init();
                    this.timeResultMap = {};
                    this.$hMessage.error(res.message || '查询订单不存在');
                }
            }).catch(() => {
                this.lodingLink = false;
            });
        },
        // 获取产品节点列表
        getManageList() {
            return new Promise((resolve, reject) => {
                getProductManageList().then(res => {
                    const { data } = res;
                    const list = [];
                    data.forEach(ele => {
                        if (ele.bizSysType !== '6' && ele?.template?.applicationViewMode) list.push(ele);
                    });
                    this.modalInfo.productList = list || [];
                    this.modalInfo.formItem.productInstNo = list?.[0]?.productInstNo;
                    this.modalInfo.formItem.exchangeId = list?.[0]?.exchangeIdList?.[0];
                    this.modalInfo.formItem.span = list?.[0]?.spans?.[0];
                    resolve(true);
                }).catch(err => {
                    this.modalInfo.productList = [];
                    reject(err);
                });
            });
        },
        // 初始化时延图表数据参数
        initTimeDelayParam() {
            myChart && myChart.clear();
            const paramList = ['indicators', 'percentiles', 'loopType', 'exchangeId', 'seat', 'accountId', 'span', 'interval', 'startTime', 'endTime'];
            let param = {};
            const formData = this.modalInfo.formItem;
            for (const i of paramList) {
                const idx = ['startTime', 'endTime'].indexOf(i);
                param[i] = idx > -1
                    ? `${formatDate(formData.date)} ${formData.time[idx]}`
                    : formData[i];
            }

            if (this.modalInfo.tabValue !== 'statistical') {
                param = Object.assign(param, {
                    indicators: [formData.compareIndicator],
                    percentiles: [],
                    compareDate: `${formatDate(this.modalInfo.formItem.compareDate)} 00:00:00`
                });
            }

            param.bizSysType = _.find(this.modalInfo.productList, ['productInstNo', formData.productInstNo]).bizSysType;
            param.productInstNo = formData.productInstNo;
            return param;
        },
        // 渲染echart数据
        drawEchart(data) {
            const seriesParams = Object.keys(data);
            const series = [];

            for (const i of seriesParams) {
                if (i === 'timeStamp') continue;
                const stu = i === 'compare';
                series.push({
                    name: i,
                    type: 'line',
                    symbol: 'none',
                    sampling: 'lttb',
                    data: data[i],
                    lineStyle: {
                        width: 1,
                        type: stu ? 'dotted' : ''
                    },
                    emphasis: {
                        lineStyle: {
                            width: 1
                        }
                    }
                });
            }

            // 设置y轴数据
            this.option.series = series;
            // 设置x轴数据
            this.option.xAxis.data = data.timeStamp;

            // 设置标志
            const percentiles = [];
            this.modalInfo.formItem.percentiles.sort();
            this.modalInfo.formItem.percentiles.forEach(res => {
                percentiles.push(`p${res}`);
            });
            this.option.legend.data = [...this.modalInfo.formItem.indicators, ...percentiles];
            if (this.modalInfo.formItem.tabValue !== 'statistical') {
                this.option.legend.data.push('compare');
            }

            myChart.setOption(this.option);
            myChart.dispatchAction({
            // 刷选模式的开关。使用此 action 可将当前鼠标变为可刷选状态。 事实上，点击 toolbox 中的 brush 按钮时，就是通过这个 action，将当前普通鼠标变为刷选器的。
                type: 'takeGlobalCursor',
                // 如果想变为“可刷选状态”，必须设置。不设置则会关闭“可刷选状态”。
                key: 'brush',
                brushOption: {
                // 参见 brush 组件的 brushType。如果设置为 false 则关闭“可刷选状态”。
                    brushType: 'lineX'
                }
            });
            /* 添加 */
            this.brushing = false;
            myChart.off('brush');
            myChart.on('brush', param => {
                this.brushing = true;
            });
            myChart.off('brushselected'); // 解绑事件处理函数（可根据情况而定是否需要，这里我这边会重绘几次表，所以需要解绑事件处理函数）。
            myChart.on('brushselected', (param) => {
                this.brushing = false;
                this.coordRange = param.batch?.[0]?.areas?.[0]?.coordRange || [];
            });
            window.onresize = () => {
                myChart.resize();
            };
        },
        // 获取图表数据
        getData() {
            return new Promise((resolve, reject) => {
            // 如果已经有数据在查询了，则跳过下一笔数据请求，直到第一笔查询结束
                if (this.modalInfo.status || this.loading || !this.modalInfo.productList.length) return;
                this.loading = true;
                const param = this.initTimeDelayParam();
                getDelayTrendData(param).then(res => {
                    if (res.success) {
                        this.timeStamp = res.data.delayTrend.timeStamp;
                        const data = res.data.delayTrend || {};
                        this.drawEchart(data);
                        this.loading = false;
                        resolve(true);
                    }
                    resolve(false);
                }).catch(err => {
                    reject(err);
                    this.loading = false;
                    console.log(err);
                    this.$hMessage.error('时延数据查询失败！');
                });
            });
        },
        // 获取mark数据
        getMarkData(data, text) {
            return {
                label: {
                    position: 'end',
                    formatter: `${text} ${data} μs`,
                    color: '#fff'
                },
                lineStyle: {
                    color: text === 'Max' ? '#13c2c2' : '#FACC14'
                },
                yAxis: Number(data)
            };
        },
        // 修改span数据索引
        setSpanIndexMap(key, val) {
            this.spanIndexMaps[key] = val;
        },
        // 显示时延订单弹窗
        showTopList(data) {
            const paramList = ['exchangeId', 'loopType', 'span', 'productInstNo', 'seat'];
            paramList.forEach(ele => {
                this.modalTable[ele] = this.modalInfo.formItem[ele];
            });
            this.modalTable.bizSysType = _.find(this.modalInfo.productList, ['productInstNo', this.modalInfo.formItem.productInstNo]).bizSysType;
            this.modalTable.time = data;
            this.modalTable.status = true;
        },
        // 显示设置弹窗
        showSettingModel() {
            this.modalInfo.status = true;
        },
        // 子组件修改modalInfoData的数据
        setInfoData(obj) {
            Object.keys(obj).forEach(key => {
                this.modalInfo.formItem[key] = obj.formItem[key];
            });
            this.modalInfo.status = obj.status;
            this.modalInfo.tabValue === 'statistical' ? this.getData() : this.getCompareTimeDelayData();
        },
        // 获取交易所列表
        async getConfigList() {
            return new Promise((resolve, reject) => {
                // TODO：getConfigInfo废弃
                // getConfigInfo().then(res => {
                //     this.modalInfo.exchangeTimeMap = res.data.exchangeTime;
                //     resolve(true);
                // }).catch(err => {
                //     reject(err);
                // });
            });
        },
        // 获取同比分析数据
        async getCompareTimeDelayData() {
        // 如果已经有数据在查询了，则跳过下一笔数据请求，直到第一笔查询结束
            if (this.modalInfo.status || this.loading) return;
            this.loading = true;
            try {
                const param = this.initTimeDelayParam();
                const { data } = await getGelayTrendLinkCompareData(param);
                const baseData = data.baseDelayTrend?.delayTrend,
                    compareData = data.compareDelayTrend?.delayTrend;

                if (baseData && compareData) {
                    const key = param.indicators[0];
                    this.drawEchart({
                        [key]: baseData[key] || [],
                        compare: compareData[key] || [],
                        timeStamp: baseData['timeStamp'] || []
                    });
                }
            } catch (err){
                this.$hMessage.error('获取同比分析数据失败！');
                console.log(err);
            }

            this.loading = false;
        },
        // topo Edge事件分析
        selectEdge(param, edges) {
            if (JSON.stringify(this.timeResultMap) !== '{}') {
                const item = _.find(edges, ['id', param.edges.at(0)]);
                const key = item?.attributes?.operationName || '';
                if (this.timeResultMap[key.toLowerCase()]?.length) {
                    this.modalInstance.status = true;
                    Object.assign(this.modalInstance, {
                        key: key.toLowerCase(),
                        data: this.timeResultMap[key.toLowerCase()]
                    });
                }
            }
        },
        // 更新topo edge实例时延数
        topoTimeDelay() {
            this.$refs['topo'].init();
            if (Object.keys(this.timeResultMap).length) {
                const edgeParams = {};
                for (const x in this.timeResultMap) {
                    if (this.timeResultMap.hasOwnProperty(x)){
                        const time = _.find(this.timeResultMap[x], ['selected', true])?.duration || 0;
                        edgeParams[x] = cutZero((time / 1000).toFixed(3)) + 'μs';
                    }
                }
                this.$nextTick(() => {
                    this.$refs['topo'].handleUpdateEdgeLabel(edgeParams);
                });
            }
        },
        // 切换委托回路
        checkLoop() {
            if (!this.formItem.gtid && (!this.formItem.accountId || !this.formItem.orderId || !this.formItem.exchangeId)) return;
            this.getLinkData();
        },
        // 创建分析报表
        async createAnalyseReport(sTime, eTime) {
            let param = {
                ...this.initTimeDelayParam(),
                loopType: 'RtnCfmXchg',
                testCaseName: '自由分析',
                sceneName: '应用时延分析',
                indicators: ['avg', 'max', 'min', 'stdDeviation'],
                percentiles: [50, 90, 95, 99]
            };

            const date = param.startTime.split(' ')[0];
            param = { ...param, startTime: `${date} ${sTime}`, endTime: `${date} ${eTime}` };
            const res = await createAnalyseReport(param);
            if (res.success) {
                this.$hMessage.success(`报表${res.data.instanceName}创建成功！`);
                this.goLink(res.data?.id, res.data.instanceName);
            } else this.$hMessage.error('报表创建失败!');
            this.quickSearch();
        }
    },
    async mounted() {
        this.$nextTick(() => {
            myChart = echarts.init(document.getElementById('container'), 'dark');
        });
        window.wordClick = (param) => {
            this.showTopList(`${formatDate(this.modalInfo.formItem.date)} ${param}`);
        };
        window.createReport = (sTime, eTime) => {
            this.reportInfo.sTime = `${formatDate(this.modalInfo.formItem.date) } ${sTime }`;
            this.reportInfo.eTime =  `${formatDate(this.modalInfo.formItem.date) } ${eTime }`;
            this.reportInfo.bizSysType = _.find(this.modalInfo.productList, ['productInstNo', this.modalInfo.formItem.productInstNo]).bizSysType;
            this.reportInfo.productName = _.find(this.modalInfo.productList, ['productInstNo', this.modalInfo.formItem.productInstNo]).productName;
            this.reportInfo.status = true;
        };
        await this.getConfigList();
        await this.getManageList();
        await this.getData();
    },
    components: { settingModal, topListModal, instanceTimeDelayModal, reportConfirmModal, appTopo, aTitle, aButton, noData }
};
</script>

<style lang="less" scoped>
@import "@/assets/css/transaction";
@import "@/assets/css/input";
</style>
