export const defaultConsumeCluster = [
    {
        value: 'allCluster',
        label: '全部消费者集群'
    }
];
export const defaultTopicList = [
    {
        value: 'allTopic',
        label: '全部主题'
    }
];
export const defaultTrend = {
    xaxis: [],
    mc_subscriber_message_backlog_quantity: [],
    mc_subscriber_handle_latency_avg: [],
    mc_subscriber_exec_latency_avg: [],
    mc_subscriber_queue_latency_avg: [],
    mc_subscriber_handle_latency_max: [],
    mc_subscriber_exec_latency_max: [],
    mc_subscriber_queue_latency_max: [],
    mc_subscriber_handle_latency_min: [],
    mc_subscriber_exec_latency_min: [],
    mc_subscriber_queue_latency_min: []
};
// 趋势指标与实时值属性名关系枚举
export const metricEnum = {
    mc_subscriber_message_backlog_quantity: 'TotalMsgInQueCount',
    mc_subscriber_handle_latency_avg: 'AvgTotalTimeUs',
    mc_subscriber_exec_latency_avg: 'AvgExecTimeUs',
    mc_subscriber_queue_latency_avg: 'AveQueueTimeUs',
    mc_subscriber_handle_latency_max: 'MaxTotalTimeUs',
    mc_subscriber_exec_latency_max: 'MaxExecTimeUs',
    mc_subscriber_queue_latency_max: 'MaxQueueTimeUs',
    mc_subscriber_handle_latency_min: 'MinTotalTimeUs',
    mc_subscriber_exec_latency_min: 'MinExecTimeUs',
    mc_subscriber_queue_latency_min: 'MinQueueTimeUs'
};

export const sortRuleList = [
    {
        value: 'TotalMsgInQueCount',
        label: '按消息积压数排序'
    },
    {
        value: 'AvgTotalTimeUs',
        label: '按客户端平均处理耗时排序'
    },
    {
        value: 'AveQueueTimeUs',
        label: '按客户端平均排队时间排序'
    },
    {
        value: 'AvgExecTimeUs',
        label: '按客户端平均执行时间排序'
    }
];

export const topList = [
    {
        value: 6,
        label: 'TOP6'
    },
    {
        value: 10,
        label: 'TOP10'
    },
    {
        value: 20,
        label: 'TOP20'
    }
];

// 查询方式枚举
export const queryTypeEnum = {
    'range:2:input': 'range',
    'last:1:input': 'last',
    'old:1:input': 'old'
};

// ------------------------------ 总览指标统计值计算方法 -----------------------------
// 数组去重计算总数方法
export const sumSet = (data, key) => {
    const dataArr = [];
    Array.isArray(data) && data.forEach(item => {
        dataArr.push(item[key]);
    });
    const newArr = Array.from(new Set(dataArr));
    return newArr.length;
};
// 多主机数组合并去重后计算总数
export const batchSumSet = (array, key) => {
    const keyArr = [];
    Array.isArray(array) && array.forEach(item => {
        Array.isArray(item?.Result) && item.Result.forEach(ele => {
            keyArr.push(ele[key]);
        });
    });
    const newArr = Array.from(new Set(keyArr));
    return newArr.length;
};
// 集群计算汇总数据方法
export const batchSum = (array, key, callback) => {
    let sumCount = 0;
    Array.isArray(array) && array.forEach(item => {
        sumCount += callback(item?.Result, key);
    });
    return sumCount;
};
// 总数计算sum方法(数组中直接获取总数数据)
export const sum = (data, key) => {
    let sumCount = 0;
    Array.isArray(data) && data.forEach(item => {
        sumCount += item?.[key] || 0;
    });
    return sumCount;
};

// GetTopicSubPubInfo 需要去除ThreadNo=-1的数据在再整合
export const sumFilterRemove = (data, key) => {
    let sumCount = 0;
    Array.isArray(data) && data.forEach(item => {
        if (item.ThreadNo !== -1) {
            sumCount += item[key];
        }
    });
    return sumCount;
};
// 总数计算 每项总数-1 方法
export const sumReduce = (data, key) => {
    let sumCount = 0;
    Array.isArray(data) && data.forEach(item => {
        sumCount = sumCount + item[key] - 1;
    });
    return sumCount;
};
// 订阅、推送线程名区分后计算总数方法
export const filterSum = (data, key) => {
    let sumCount = 0;
    Array.isArray(data) && data.forEach(item => {
        if (item?.ThreadType.indexOf(key) > -1) {
            sumCount += item?.MsgInQueue || 0;
        }
    });
    return sumCount;
};
// 将多个主机Result的数据合并到一个数组(并根据多个key去重)
export const mutiHostMergeArr = (array) => {
    const mergeArr = [];
    Array.isArray(array) && array.forEach(item => {
        Array.isArray(item?.Result) && item.Result.forEach(ele => {
            mergeArr.push(ele);
        });
    });
    return mergeArr;
};
// GetTopicSubPubInfo数组重复发布者，消息数取最大值那个(取ThreadNo === 0)
export const getTopicPubMsgCount = (array) => {
    let sumCount = 0;
    const topicPubMsgObj = {};
    Array.isArray(array) && array.forEach(item => {
        if (item.ThreadNo === 0) {
            if (Object.keys(topicPubMsgObj).includes(item.Topic)) {
                topicPubMsgObj[item.Topic] = item.PubMsgCount > topicPubMsgObj[item.Topic] ? item.PubMsgCount : topicPubMsgObj[item.Topic];
            } else {
                topicPubMsgObj[item.Topic] = item.PubMsgCount;
            }
        }
    });
    Object.keys(topicPubMsgObj).forEach(key => {
        sumCount += topicPubMsgObj[key];
    });
    return sumCount;
};

// GetPubMsgNo数组去除重复项
export const removeDuplicates = (array) => {
    const uniqueArray = [];
    for (const item of array) {
        const duplicateIndex = uniqueArray.findIndex(obj => {
            return obj.PubName === item.PubName && obj.TopicPartition == item.TopicPartition;
        });
        if (duplicateIndex === -1) {
            uniqueArray.push(item);
        } else {
            // 比较重复项NextMsgNo大小，取大的那项
            if (item?.NextMsgNo > uniqueArray?.[duplicateIndex]?.NextMsgNo) {
                uniqueArray[duplicateIndex].NextMsgNo = item?.NextMsgNo;
            }
        }
    }
    return uniqueArray;
};

// 取一个ip结果的功能号数据处理 - 多个ip结果中取有值那个
export const handleMutiIpFunc = (funcs, key = 'Result') => {
    let validList = [];
    for (const item of funcs) {
        if (item?.[key] && !item[key].ErrorNo && Object.keys(item?.[key])?.length) {
            validList = item[key];
            break;
        }
    }
    return validList;
};

// 根据功能名称生成mc请求param
export const generateApiParam = (ipPortList, funcNameList) => {
    const param = [];
    Array.isArray(ipPortList) && ipPortList.forEach(ipPortItem => {
        Array.isArray(funcNameList) && funcNameList.forEach(item => {
            param.push({
                manageProxyIp: ipPortItem.manageProxyIp,
                manageProxyPort: ipPortItem.manageProxyPort,
                instanceName: ipPortItem.instanceName,
                pluginName: 'ldp_mc',
                funcName: item
            });
        });
    });
    return param;
};
