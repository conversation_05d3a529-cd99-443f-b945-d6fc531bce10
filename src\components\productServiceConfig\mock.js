export function getLdpManageApis(param) {
    const manageList = [
        {
            pluginName: 'ldp_logproxy',
            funcName: 'AddAllMemberToCluster',
            funcNameCn: '强制将当前集群所有节点加入集群',
            describe: '强制将当前集群所有节点加入集群，这之后应当调用StartElection管理功能开始选主。一个集群只需要调用任意一个节点的此管理功能。若集群主机在线/集群中存在有RCM_ROLE_INACTIVE的备机/指定的RCM上下文不是备机/指定的RCM上下文正在竞选中，此管理功能调用均无效。应当调用RCM仲裁对象（位于ldp_arb插件）所在进程的管理功能。',
            method: 'POST'
        },
        {
            pluginName: 'ldp_logproxy',
            funcName: 'GetContextInfo',
            funcNameCn: '获取普通上下文的信息',
            describe: '获取普通上下文的信息，目前可以获取到上下文的Id、名字Name、身份角色Role、是否单机IsSingleton、集群名TierName、上下文是否启动IsStart、传输模式TransMode、所属中心Zone、仲裁优先级ArbPriority。',
            method: 'GET'
        },
        {
            pluginName: 'ldp_logproxy',
            funcName: 'GetContextInfoOfCtxMP',
            funcNameCn: '获取多进程上下文的信息',
            describe: '获取多进程上下文的信息，展示多进程上下文的Id、Name、InstanceName、是否单机模式，和当前的身份角色等相关信息。',
            method: 'GET'
        },
        {
            pluginName: 'ldp_rcm',
            funcName: 'AddAllMemberToCluster',
            funcNameCn: '允许指定备机加入集群',
            describe: '若某个备机已被通过 RejectFollowerFromCluster 管理功能拒绝加入集群，指定该备机上下文名，此后允许该备机加入集群。只会对主机本次启动中，已连接过当前主机的备机生效。应当调用是主机且RCM集群同步对象（位于ldp_copy插件）所在进程的管理功能。目前仅支持消息复制模式的RCM上下文集群。该操作仅是撤销 RejectFollowerFromCluster 对该备机的影响，后续其是否能够真正加入集群，仍由该备机与主机的同步状态控制，不受本操作影响本操作的反向操作见 RejectFollowerFromCluster 管理功能说明。',
            method: 'POST'
        },
        {
            pluginName: 'ldp_rcm',
            funcName: 'AdmitFollowerToCluster',
            funcNameCn: '强制将当前集群所有节点加入集群',
            describe: '强制将当前集群所有节点加入集群，这之后应当调用StartElection管理功能开始选主。一个集群只需要调用任意一个节点的此管理功能。若集群主机在线/集群中存在有RCM_ROLE_INACTIVE的备机/指定的RCM上下文不是备机/指定的RCM上下文正在竞选中，此管理功能调用均无效。应当调用RCM仲裁对象（位于ldp_arb插件）所在进程的管理功能。',
            method: 'POST'
        }
    ];
    const res = {
        code: '200',
        message: '成功',
        data: manageList,
        success: true
    };

    return new Promise((resolve, reject) => {
        const delay = 1000; // 1 second delay
        setTimeout(() => {
            resolve(res);
        }, delay);
    });
}

export function getManageMeta(param) {
    const res = {
        code: '200',
        message: '成功',
        data: {
            pluginName: '插件名称',
            funcName: '管理功能名称',
            funcNameCn: '中文名称',
            remark: '备注',
            description: '说明',
            version: '版本',
            updateDate: '更新时间',
            provider: '提供者，默认hs-ldp',
            accessUrl: '访问URL',
            schema: {
                request: {
                    type: 'object',
                    properties: {
                        plugin: {
                            remark: '插件名称',
                            type: 'string'
                        }
                    }
                },
                response: {
                    type: 'object',  // 参数类型
                    properties: {
                        response: { // 参数名称
                            type: 'array', // 参数类型
                            remark: 'xxx', // 参数说明
                            items: {
                                type: 'object',
                                properties: {
                                    pluginName: { // 参数名称
                                        type: 'string', // 参数类型
                                        remark: '插件名称', // 参数说明
                                        '//': '存在enumValues字段时，该参数为下拉选择框',
                                        enumValues: [
                                            {
                                                0: '关闭'
                                            },
                                            {
                                                1: '开启'
                                            }
                                        ]
                                    },
                                    instanceNo: {
                                        type: 'number',
                                        remark: '实例号'
                                    },
                                    mgrList: {
                                        type: 'array',
                                        remark: '功能列表',
                                        items: {
                                            type: 'string',
                                            remark: '功能名称'
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            },
            example: {
                request: {
                    '//': '原始请求参数范例'
                },
                response: {
                    '//': '原始响应参数范例'
                }
            }
        },
        success: true
    };

    return new Promise((resolve, reject) => {
        const delay = 1000; // 1 second delay
        setTimeout(() => {
            resolve(res);
        }, delay);
    });
}
