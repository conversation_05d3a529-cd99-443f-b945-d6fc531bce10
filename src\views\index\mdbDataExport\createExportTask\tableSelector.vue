<template>
  <div ref="table-selector" class="table-selector">
    <div class="bordered-card">
        <div class="bordered-card-title">
            可选表（{{ availableTables.length }}）
        </div>
        <div>
            <div class="bordered-card-search">
                <h-select v-model="instanceId" @on-change="onChangeInstance">
                    <h-option v-for="item in instanceList" :key="item.instanceId" :value="item.instanceId">{{ item.instanceNo }}</h-option>
                </h-select>
                <div class="bordered-card-search-line" />
                <h-input v-model="searchAvailable" placeholder="输入表名/中文名搜索"></h-input>
            </div>
            <a-simple-table
                showTitle
                border
                rowSelect
                :hasPage="false"
                :loading="loading"
                :columns="columns"
                :height="tableHeight"
                :tableData="filteredAvailableTables"
                @selection="getAvailableSelections">
            </a-simple-table>
        </div>
    </div>
    <div class="table-transfer-buttons">
        <a-button type="dark" @click="addSelected">
            <h-icon name="ios-arrow-forward" size="18"></h-icon>
        </a-button>
        <a-button type="dark" @click="removeSelected">
            <h-icon name="ios-arrow-back" size="18"></h-icon>
        </a-button>
    </div>
    <div class="bordered-card">
        <div class="bordered-card-title">
            已选表（{{ selectedTables.length }}）
        </div>
        <div>
            <h-input v-model="searchSelected" placeholder="输入表名/中文名搜索"></h-input>
            <a-simple-table
                showTitle
                border
                rowSelect
                :hasPage="false"
                :columns="columns"
                :height="tableHeight"
                :tableData="filteredSelectedTables"
                @selection="getSelectedSelections">
            </a-simple-table>
        </div>
    </div>
  </div>
</template>

<script>
import aSimpleTable from '@/components/common/table/aSimpleTable';
import aButton from '@/components/common/button/aButton';
import { getTables } from '@/api/httpApi';
import { MDB_NO_LOGIN } from '@/config/errorCode';

export default {
    name: 'TableSelector',
    components: {
        aSimpleTable, aButton
    },
    props: {
        // 支持的可选表数组
        supportAppearTables: {
            type: Array,
            default: () => []
        },
        // 已选表数组
        selectedAppearTables: {
            type: Array,
            default: () => []
        },
        productId: {
            type: String,
            default: ''
        },
        endpointId: {
            type: String
        },
        instanceList: {
            type: Array,
            default: []
        }
    },
    data() {
        return {
            tableHeight: 0, // 表格高度
            // instanceList: [],
            instanceId: null,
            availableTables: [], // 可选表数据
            selectedTables: [], // 已选表数据
            availableSelections: [], // 可选表选中行数据
            selectedSelections: [], // 已选表选中行数据
            searchAvailable: '', // 可选表搜索字段
            searchSelected: '', // 已选表搜索字段
            loading: false,
            columns: [ // 表格列配置
                {
                    type: 'selection',
                    width: 60,
                    align: 'center'
                },
                {
                    title: '表名',
                    key: 'tableName',
                    ellipsis: true,
                    minWidth: 120
                },
                {
                    title: '中文名',
                    key: 'tableChineseName',
                    ellipsis: true,
                    minWidth: 100
                },
                {
                    title: '节点',
                    key: 'instanceName',
                    width: 90,
                    ellipsis: true
                }
            ]
        };
    },
    computed: {
        // 过滤可选表数据，返回搜索匹配的可选表
        filteredAvailableTables() {
            // 搜索操作改变表格行数据，需清除选中行数据
            this.availableSelections = [];

            return this.filterTables(this.availableTables, this.searchAvailable);
        },
        // 过滤已选表数据，返回搜索匹配的已选表
        filteredSelectedTables() {
            // 搜索操作改变表格行数据，需清除选中行数据
            this.selectedSelections = [];

            return this.filterTables(this.selectedTables, this.searchSelected);
        },
        /**
         * 当前节点名
         */
        curInstanceName() {
            if (!this.instanceId) return null;
            return this.instanceList.find(item => item.instanceId === this.instanceId).instanceNo;
        }
    },
    methods: {
        /**
         * 切换实例，查询表格
         * @param {String} instanceId
         */
        async onChangeInstance(instanceId) {
            if (!instanceId) {
                this.availableTables = [];
                return;
            }
            try {
                this.loading = true;
                const res = await getTables({
                    instanceId,
                    hasUserFilter: true,
                    endpointId: this.endpointId,
                    productInstNo: this.productId
                });
                if (res?.code === MDB_NO_LOGIN) {
                    this.$emit('resetLogin');
                    return;
                }
                this.searchAvailable = '';
                if (res?.tableInfos) {
                    this.availableTables = res.tableInfos.reduce((pre, item) => {
                        item.instanceId = this.instanceId;
                        const found = this.selectedTables.find(it => `${it.tableName}${it.instanceName}` === `${item.tableName}${this.curInstanceName}`);
                        if (!found) {
                            pre.push({ ...item, instanceName: this.curInstanceName, instanceId: this.instanceId });
                        }
                        return pre;
                    }, []);
                } else {
                    this.availableTables = [];
                }
            } catch (error) {
                console.log('切换实例，查询表格失败,', error);
                this.availableTables = [];
            } finally {
                this.loading = false;
            }
        },
        // 获取可选表选中行数据
        getAvailableSelections(rows) {
            this.availableSelections = rows;
        },
        // 获取已选表选中行数据
        getSelectedSelections(rows) {
            this.selectedSelections = rows;
        },
        // 获取表格高度
        fetTableHeight() {
            this.tableHeight = this.$refs['table-selector']?.offsetHeight - 100;
        },
        // 过滤表格数据，返回匹配搜索关键词的表
        filterTables(tables, search) {
            if (!search) return [...tables];
            return tables.filter(item =>
                item.tableName?.includes(search) ||
                item.tableChineseName?.includes(search)
            );
        },
        // 将选中的可选表添加到已选表
        addSelected() {
            this.transferTables(this.availableSelections, this.availableTables, this.selectedTables);
            this.availableSelections = [];
        },
        // 将选中的已选表移回到可选表
        removeSelected() {
            if (this.selectedSelections.length) {
                // 检查当前是否选择了节点
                if (!this.instanceId) {
                    this.selectedTables = this.selectedTables.filter(item => !this.selectedSelections.find(it => `${item.instanceName}${item.instanceId}` === `${it.instanceName}${it.instanceId}`));
                    this.availableTables = [];
                    this.selectedSelections = [];
                } else {
                    // 检查是否在同一个节点下，同一个节点，才能移动到左边，否则只能删除
                    const deletedItems = [];
                    const moveItems = [];
                    this.selectedSelections.forEach(item => {
                        if (this.instanceId !== item.instanceId) {
                            deletedItems.push(item);
                        } else {
                            moveItems.push(item);
                        }
                    });
                    if (moveItems.length) {
                        this.availableTables.unshift(...moveItems);
                    }
                    this.selectedTables = this.selectedTables.filter(item => {
                        const uniqueKey = `${item.instanceId}${item.tableName}`;
                        const needMove = moveItems.find(m => `${m.instanceId}${m.tableName}` === uniqueKey);
                        const needDelete = deletedItems.find(m => `${m.instanceId}${m.tableName}` === uniqueKey);
                        return !needMove && !needDelete;
                    });
                }
            }
            this.$emit('update-tables', {
                availableTables: [...this.availableTables],
                selectedTables: [...this.selectedTables]
            });
            this.selectedSelections = [];
        },
        // 从一个表数组转移选中的表到另一个表数组
        transferTables(selections, fromTables, toTables) {
            if (!selections.length) return;
            selections.forEach(selection => {
                toTables.push(selection);
                const index = fromTables.findIndex(table =>
                    table?.tableName === selection?.tableName &&
                    table?.instanceName === selection?.instanceName
                );
                if (index > -1) {
                    fromTables.splice(index, 1);
                }
            });
            // 发出事件通知父组件表数据更新
            this.$emit('update-tables', {
                availableTables: [...this.availableTables],
                selectedTables: [...this.selectedTables]
            });
        }
    },
    mounted() {
        // 监听窗口resize事件，获取表格高度
        window.addEventListener('resize', this.fetTableHeight);
        this.$nextTick(() => {
            setTimeout(this.fetTableHeight, 300);
            this.instanceId = this.instanceList[0]?.instanceId;
        });
    },
    beforeDestroy() {
        // 组件销毁前移除窗口resize事件监听
        window.removeEventListener('resize', this.fetTableHeight);
    }
};
</script>

<style lang="less" scoped>
.table-selector {
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: space-between;
    /* stylelint-disable-next-line font-family-no-missing-generic-family-keyword */
    font-family: PingFangSC-Regular;
}

.bordered-card {
    flex: 1 1 0%; // 关键：允许收缩并设置基础尺寸为0
    min-width: 0; // 关键：覆盖默认min-width:auto行为
    height: 100%;
    background: var(--wrapper-color);
    border: var(--border);
    border-radius: 4px;
    padding: 0 12px;
    flex-shrink: 0;
    flex-grow: 1;

    &-search {
        display: flex;
        align-items: center;

        &-line {
            margin: 0 4px;
        }
    }

    .bordered-card-title {
        height: 44px;
        line-height: 44px;
        font-size: 14px;
        color: var(--font-color);
        padding-left: 12px;

        &::before {
            display: inline-block;
            position: relative;
            left: -10px;
            top: 4px;
            content: "";
            width: 4px;
            height: 16px;
            background: var(--link-color);
        }
    }

    /deep/ .h-checkbox-indeterminate .h-checkbox-inner {
        &::after {
            left: 3px;
            top: 6px;
        }
    }
}

.table-transfer-buttons {
    height: 100%;
    width: 70px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    gap: 15px;
    flex-shrink: 0;
}

.table-transfer-buttons /deep/ .h-btn {
    width: 36px;
    height: 36px;
    padding: 5px;
}
</style>
