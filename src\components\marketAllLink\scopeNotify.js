import '@/assets/css/scopeNotify.less';
// 选中区域悬浮弹窗
export default {
    name: 'scopeNotify',
    props: ['scopeNotifyInfo'],
    data() {
        return {
            // visible: false,
        };
    },
    watch: {},
    methods: {},
    render() {
        return (
            <div class="scope" vShow={this.scopeNotifyInfo.visible}>
                <p>
                    <i>时间范围：</i>
                    {this.scopeNotifyInfo.data.startTime} ～
                    {this.scopeNotifyInfo.data.endTime}
                </p>
                <p>
                    <i>{this.scopeNotifyInfo.data.original.name}最大值：</i>
                    {Math.max.apply(
                        Math,
                        this.scopeNotifyInfo.data.original.data
                    )}
                    ns
                </p>
                <p>
                    <i>{this.scopeNotifyInfo.data.original.name}最小值：</i>
                    {Math.min.apply(
                        Math,
                        this.scopeNotifyInfo.data.original.data
                    )}
                    ns
                </p>
                <p>
                    <i>{this.scopeNotifyInfo.data.contrast.name}最大值：</i>
                    {Math.max.apply(
                        Math,
                        this.scopeNotifyInfo.data.contrast.data
                    )}
                    ns
                </p>
                <p>
                    <i>{this.scopeNotifyInfo.data.contrast.name}最小值：</i>
                    {Math.min.apply(
                        Math,
                        this.scopeNotifyInfo.data.contrast.data
                    )}
                    ns
                </p>
                <span>透视</span>
            </div>
        );
    }
};
