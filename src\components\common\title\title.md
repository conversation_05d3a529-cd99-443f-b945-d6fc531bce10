## Title标题
封装带有slot的title组件，分为：apm-title。
#### 示例
```html
<a-title :title="`时延场景测试分析（${selectInstance.instanceName || ''} 测试实例详情）`">
  <slot>
    <div class="slot-box">
      <a-button type="dark" @click="instanceInfo.status = true">选择实例</a-button>
    </div>
  </slot>
</a-title>
```
#### Props
| **属性** | **说明** | **类型** | **默认值** |
| --- | --- | --- | --- |
| title | 标题 | String | '' |

#### 示例代码

```vue
<template>
  <div class="main">
        <a-title title="应用节点列表">
            <slot>
                <a-button class="btn-add" type="dark">添加</a-button>
            </slot>
        </a-title>
  </div>
</template>

<script>
import aTitle from '@/components/common/title/aTitle';
export default {
    data() {
        return {

        };
    },
    components: { aTitle }
};
</script>

```

