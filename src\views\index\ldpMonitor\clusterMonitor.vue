<template>
    <div class="tab-box">
        <h-tabs v-model="tabName" @on-click="handleTabChange(tabName)">
            <h-tab-pane label="集群状态" name="clusterStatus">
                <cluster-and-group-monitor ref="clusterStatus" :productInstNo="productInstNo" :productInfo="productInfo" type="cluster"/>
            </h-tab-pane>
            <h-tab-pane label="应用状态" name="appStatus">
                <cluster-and-group-monitor ref="appStatus" :productInstNo="productInstNo" :productInfo="productInfo" type="instance"/>
            </h-tab-pane>
        </h-tabs>
    </div>
</template>
<script>
import clusterAndGroupMonitor from '@/components/ldpMonitor/clusterAndGroupMonitor';
export default {
    props: {
        productInfo: {
            type: Object,
            default: () => { return {}; }
        },
        productInstNo: {
            type: String,
            default: ''
        }
    },
    components: { clusterAndGroupMonitor },
    data() {
        return {
            tabName: 'clusterStatus'
        };
    },
    mounted() {
        this.handleTabChange('clusterStatus');
    },
    methods: {
        handleTabChange(tabName) {
            this.tabName = tabName;
            this.clearPolling();
            this.$nextTick(async () => {
                await this.$refs[tabName].init();
            });
        },
        clearPolling() {
            const clusterStatus = this.$refs['clusterStatus'];
            const appStatus = this.$refs['appStatus'];
            clusterStatus && clusterStatus.clearPolling();
            appStatus && appStatus.clearPolling();
        }
    },
    watch: {
        productInstNo() {
            this.clearPolling();
            this.$nextTick(async () => {
                await this.$refs[this.tabName].init();
            });
        }
    },
    beforeDestroy(){
        this.clearPolling();
    }
};
</script>
<style lang="less" scoped>
.cluster-box {
    width: calc(100% - 33px);
    height: 100%;
}
</style>
