import Vis from 'vis';
import { getMarketModelConfig } from '@/api/httpApi';

export default {
    name: 'networkTopo',
    props: ['id', 'idx'],
    data() {
        return {
            network: null,
            ctx: null,
            nodes: [],
            edges: [],
            options: {
                edges: {
                    width: 1,
                    length: 60,
                    shadow: true,
                    smooth: {
                        enabled: true, // 设置连线是直线还是湾线还是贝塞尔
                        type: 'cubicBezier' // 'dynamic', 'continuous', 'discrete', 'diagonalCross', 'straightCross', 'horizontal', 'vertical', 'curvedCW', 'curvedCCW', 'cubicBezier'
                    },
                    arrows: {
                        to: {
                            enabled: true,
                            scaleFactor: 1,
                            type: 'arrow'
                        }
                    },
                    color: {
                        color: '#62caff',
                        highlight: '#aaf6ff',
                        hover: '#1fe1c6',
                        inherit: 'from',
                        opacity: 1.0
                    }
                },
                nodes: {
                    fixed: true,
                    font: {
                        color: '#fff'
                    }
                },
                physics: {
                    enabled: false
                },
                interaction: {
                    dragNodes: true, // 是否能拖动节点
                    dragView: true, // 是否能拖动画布
                    hover: true, // 鼠标移过后加粗该节点和连接线
                    multiselect: false, // 按 ctrl 多选
                    selectable: true, // 是否可以点击选择
                    selectConnectedEdges: false, // 选择节点后是否显示连接线
                    hoverConnectedEdges: false, // 鼠标滑动节点后是否显示连接线
                    zoomView: true // 是否能缩放画布
                },
                layout: {
                    randomSeed: 1,
                    // improvedLayout: true,
                    hierarchical: {
                        enabled: true,
                        levelSeparation: 200,
                        nodeSpacing: 100,
                        treeSpacing: 200,
                        blockShifting: true,
                        edgeMinimization: true,
                        // parentCentralization: true,
                        direction: 'LR', // UD, DU, LR, RL
                        sortMethod: 'directed' // hubsize, directed
                    }
                }
            },
            spanRelation: [],
            selectLinkId: '', // 选中链路
            rangeY: [0, 0], // 拓扑图高度范围
            rangeLink: [], // 链路坐标范围
            topHierarchy: [],
            bottomHierarchy: []
        };
    },
    methods: {
        async init() {
            const result = await this.getMarketModelConfig();
            if (result) this.drawing(result);
        },
        // 获取topo数据
        getMarketModelConfig() {
            return new Promise((resolve, reject) => {
                getMarketModelConfig({ exchangeId: this.id }).then(res => {
                    if (res.success) {
                        resolve(JSON.parse(res.data));
                        return;
                    }
                    resolve(false);
                    this.$hMessage.error(res.message);
                }).catch(err => {
                    reject(err);
                });
            });
        },
        // 初始化渲染节点
        drawing(data) {
            this.spanRelation = data.spanRelation;
            this.$emit('update', {
                key: this.spanRelation[0].spanId,
                name: this.spanRelation[0].name,
                operationName: this.spanRelation[0].operationName
            }, this.idx);

            data.objects.forEach((ele) => {
                const node = {
                    id: ele.index,
                    shape: 'image',
                    size: 30
                };
                const url = './static/allLinkTopo/';
                if (ele.name === '行情源') {
                    node.image = url + 'market.png';
                    node.size = 44;
                } else if (ele.name === '客户端') {
                    node.image = url + 'client.png';
                    node.size = 44;
                } else if (ele.name === '交换机') {
                    node.image = url + 'switch.png';
                    node.label = ele.name;
                } else {
                    node.image = {
                        unselected: url + 'normal.png',
                        selected: url + 'select.png'
                    };
                    // node.image.selected = url + "select.png";
                    node.label = ele.name;
                }

                this.nodes.push(node);
            });

            this.edges = data.edges;
            const networkData = {
                nodes: this.nodes,
                edges: this.edges
            };

            this.network = new Vis.Network(
                this.$refs['network-monitor'],
                networkData,
                this.options
            );

            const cvs =
                this.$refs['network-monitor'].childNodes[0].canvas;

            this.ctx = cvs.getContext('2d');

            this.network.on('afterDrawing', (param) => {
                this.rangeLink = []; // 重置链路相关数据
                this.topHierarchy = []; // 重置上方链路层级
                this.bottomHierarchy = []; // 重置下方链路层级
                const options = this.network.getPositions();
                // 计算拓扑图像最高最低点
                Object.keys(options).forEach((ele) => {
                    if (options[ele].y > this.rangeY[1]) {
                        this.rangeY[1] = options[ele].y;
                    }
                    if (options[ele].y < this.rangeY[0]) {
                        this.rangeY[0] = options[ele].y;
                    }
                });

                this.drawLink(options, this.selectLinkId);

                // 获取顶部底部的层级差
                const tMax = this.topHierarchy.length
                    ? Math.max(...this.topHierarchy)
                    : 0;
                const bMax = this.bottomHierarchy.length
                    ? Math.max(...this.bottomHierarchy)
                    : 0;
                const differential = bMax - tMax;

                const scale = this.network.getScale();

                // 移动画布
                this.network.moveTo({
                    position: { x: 0, y: 25 * differential + 15 / 2 }, // 默认将上面下方的15坐标顶回去
                    scale,
                    animation: false
                });
            });

            this.network.on('click', (param) => {
                for (const ele of this.rangeLink) {
                    const pos = param.pointer.canvas;
                    // 判断是否是有效点击
                    if (
                        pos.x > ele.x &&
                        pos.y > ele.y &&
                        pos.x < ele.x + ele.width &&
                        pos.y < ele.y + 20
                    ) {
                        if (this.selectLinkId !== ele.spanId) {
                            this.selectLinkId = ele.spanId;
                            this.$emit('update', {
                                key: ele.spanId,
                                name: ele.name,
                                operationName: ele.operationName
                            }, this.idx);
                            this.network.redraw();
                            break;
                        }
                    }
                }
            });
        },
        // 画弹窗
        // drawScope(x, y, data, name) {
        //     this.ctx.save();
        //     // 判断值长度
        //     const baseHeight = 80 + data.length * 40;

        //     this.ctx.beginPath();
        //     this.ctx.strokeStyle = 'rgba(88,94,106,0.90)';
        //     this.ctx.fillStyle = 'rgba(88,94,106,0.90)';

        //     // 画圆弧
        //     this.roundRect(this.ctx, x + 40, y - 60, 230, baseHeight - 40, 4);

        //     this.ctx.fill();
        //     this.ctx.stroke();

        //     this.ctx.font = '10px Arial';
        //     this.ctx.fillStyle = '#fff';

        //     this.ctx.fillText(name, x + 50, y - baseHeight + 152);

        //     data.forEach((item, index) => {
        //         this.ctx.fillText(
        //             `${item.instanceName}`,
        //             x + 50,
        //             y - baseHeight + 38 * (index + 1) + 145
        //         );
        //         this.ctx.fillText(
        //             `应用状态：${item.instanceName}    穿透时延：${item.penetrationDelay}μs`,
        //             x + 50,
        //             y - baseHeight + 38 * (index + 1.5) + 145
        //         );
        //     });

        //     // 画文字中的分割线
        //     this.ctx.beginPath();
        //     this.ctx.lineWidth = 0.5;
        //     this.ctx.strokeStyle = 'rgba(255,255,255,0.15)';
        //     this.ctx.moveTo(x + 40, y - baseHeight + 170);
        //     this.ctx.lineTo(x + 270, y - baseHeight + 170);
        //     this.ctx.closePath();

        //     this.ctx.stroke();

        //     this.ctx.restore();
        // },
        /**
         *  *  canvas文字自动换行
         *
         * 	ctx:  canvas绘图上下文
         * 	str:  需要绘制的文本内容
         * 	draw_width:  绘制后的文字显示宽度
         * 	lineNum:  最大行数，多出部分用'...'表示， 如果传-1可以达到自动换行效果
         * 	startX:  绘制文字的起点 X 轴坐标
         * 	startY:  绘制文字的起点 Y 轴坐标
         *	steps:  文字行间距
         */
        toFormateStr(ctx, data) {
            const { str, draw_width, lineNum, startX, startY, steps } = { ...data };
            ctx.save();
            ctx.font = '17px Arial';
            const strWidth = ctx.measureText(str).width; // 测量文本源尺寸信息（宽度）
            let startpoint = startY + 2;
            let keyStr = '';
            const sreLN = strWidth / draw_width;
            const liner = Math.ceil(sreLN); // 计算文本源一共能生成多少行
            const strlen = parseInt(str.length / sreLN, 10); // 等比缩放测量一行文本显示多少个字符

            // 若文本不足一行，则直接绘制，反之大于传入的最多行数（lineNum）以省略号（...）代替

            const textLeft = startX + (draw_width - strWidth) / 2;
            if (strWidth < draw_width) {
                ctx.fillStyle = '#202637';
                ctx.fillRect(textLeft, startpoint, strWidth, 25);
                ctx.fillStyle = '#CACFD4';
                ctx.fillText(str, textLeft, startpoint);
            } else {
                for (let i = 1; i < liner + 1; i++) {
                    const startPoint = strlen * (i - 1);
                    if (i < lineNum || lineNum == -1) {
                        keyStr = str.substr(startPoint, strlen);
                    } else {
                        keyStr = str.substr(startPoint, strlen - 5) + '...';
                        break;
                    }
                    ctx.fillStyle = '#202637';
                    ctx.fillRect(textLeft, startpoint, strWidth, 25);
                    ctx.fillStyle = '#CACFD4';
                    ctx.fillText(keyStr, textLeft, startpoint);

                    startpoint = startpoint + steps;
                }
            }

            ctx.restore();
        },
        // canvas圆角
        // roundRect(ctx, x, y, w, h, r) {
        //     ctx.beginPath();
        //     ctx.moveTo(x + r, y);

        //     // 右上角弧线
        //     ctx.arcTo(x + w, y, x + w, y + r, r);

        //     // 右下角弧线
        //     ctx.arcTo(x + w, y + h, x + w - r, y + h, r);

        //     // 左下角弧线
        //     ctx.arcTo(x, y + h, x, y + h - r, r);

        //     // 左上角弧线
        //     ctx.arcTo(x, y, x + r, y, r);
        // },
        // 画线和箭头
        drawLine(data) {
            this.ctx.save();

            this.ctx.lineWidth = 2;
            this.ctx.fillStyle = '#62CAFF';

            const x1 = data.pos1.x + data.size1 + 3;
            const x2 = data.pos2.x - data.size2 - 3;

            let y = 0;
            if (data.desc.position === 'top') {
                this.topHierarchy.push(data.desc.hierarchy);
                y = this.rangeY[0] - data.desc.hierarchy * 50;
            } else if (data.desc.position === 'bottom') {
                this.bottomHierarchy.push(data.desc.hierarchy);
                y = this.rangeY[1] + 15 + data.desc.hierarchy * 50;
            }

            const draw_width = x2 - x1;

            this.ctx.fillStyle = '#62CAFF';

            // 取消虚线样式
            this.ctx.setLineDash([]);

            // 画左右边界
            this.ctx.beginPath();
            this.ctx.moveTo(x1, y - 10);
            this.ctx.lineTo(x1, y + 10);
            this.ctx.closePath();
            this.ctx.stroke();

            this.ctx.beginPath();
            this.ctx.moveTo(x2, y - 10);
            this.ctx.lineTo(x2, y + 10);
            this.ctx.closePath();
            this.ctx.stroke();

            // 画横线
            this.ctx.beginPath();
            if (!data.solid) this.ctx.setLineDash([5, 15]);
            this.ctx.moveTo(x1, y);
            this.ctx.lineTo(x2, y);
            this.ctx.closePath();
            this.ctx.stroke();

            // 把每个链路矩形框边界范围放到数组里，方便每次调用比较点击位置
            const param = {
                x: x1,
                y: y - 10,
                width: x2 - x1,
                height: 20,
                spanId: data.spanId,
                name: data.name,
                operationName: data.operationName
            };
            this.rangeLink.push(param);

            // 画三角
            this.ctx.beginPath();
            this.ctx.moveTo(x2 - 20, y - 10); // 轨迹移动的起始位置
            this.ctx.lineTo(x2 - 20, y + 10); // 轨迹移动第一个位置坐标
            this.ctx.lineTo(x2, y); // 轨迹移动第二个位置坐标
            this.ctx.closePath();
            this.ctx.fill();

            // 写入文字
            this.toFormateStr(this.ctx, { str: data.name, draw_width, lineNum: -1, startX: x1, startY: y - 8, steps: 10 });

            this.ctx.restore();

        },
        // 画链路
        drawLink(options, id) {
            const id1 = id || this.spanRelation[0].spanId;
            this.spanRelation.forEach((ele, idx) => {
                const nodes = this.nodes;
                this.drawLine({
                    pos1: options[ele.from],
                    pos2: options[ele.to],
                    name: ele.name,
                    size1: nodes[ele.from].size,
                    size2: nodes[ele.to].size,
                    spanId: ele.spanId,
                    operationName: ele.operationName,
                    desc: ele.desc,
                    solid: id1 === ele.spanId
                });
            });
        }
    },
    async mounted() {
        this.init();
    },
    render() {

        return <div ref="network-monitor" style="height: 272px; cursor: pointer;"></div>;
    }
};
