/* eslint-disable no-unused-expressions */
/* eslint-disable max-params */
// eslint-disable-next-line max-params
// 用于MDB-SQL 机房-服务-集群-节点汇总
import { transferVal } from '@/utils/utils';

// 获取或创建 group
function getOrCreateGroup(groupList, key, label) {
    let group = groupList.find(g => g.key === key);
    if (!group) {
        group = { key, label, subGroupList: [] };
        groupList.push(group);
    }
    return group;
}

// 获取或创建 subGroup
function getOrCreateSubGroup(subGroupList, key, label) {
    let subGroup = subGroupList.find(sg => sg.key === key);
    if (!subGroup) {
        subGroup = { key, label, nodes: [] };
        subGroupList.push(subGroup);
    }
    return subGroup;
}

export const setMenuList = (allTodoInstanceList, roomTableData, clusterRoles, appTypeDictDesc, subGroupSimpleClick = false, groupSimpleClick = false) => {
    // 生成汇总数据
    const summary = {
        roomDefault: {
            menuId: 'roomDefault',
            menuName: '全部',
            groupList: []
        }
    };
    const processedInstanceIds = new Set();

    // 建立从实例ID到实例详细信息的映射
    const instanceMap = allTodoInstanceList.reduce((map, instance) => {
        map[instance?.id || instance?.instanceId] = instance;
        return map;
    }, {});

    // 辅函数
    const getClusterId = (instance) => {
        return {
            clusterId: !transferVal(instance?.clusterId) ? 'clusterDefault' : transferVal(instance?.clusterId),
            clusterName: !transferVal(instance?.clusterName) ? 'clusterDefault' : transferVal(instance?.clusterName)
        };
    };

    const handleInstance = (roomId, roomName, instance, instanceId) => {
        const services = instance?.services || [{
            serviceCode: 'serviceDefault',
            serviceName: '未知服务'
        }];

        for (const service of services) {
            const { clusterId, clusterName } = getClusterId(instance);
            const groupKey = groupSimpleClick ? `${service?.serviceCode}` : `${roomId},${service?.serviceCode}`;
            const groupName = appTypeDictDesc?.[service.serviceName] || service?.serviceName;
            const subGroupKey = subGroupSimpleClick ? `${clusterId}` : `${roomId},${clusterId}`;
            const subGroupLabel = clusterName === 'clusterDefault' ? '未知集群' : clusterName;

            if (!summary[roomId]) {
                summary[roomId] = { menuId: roomId, menuName: roomName, groupList: [] };
            }

            const group = getOrCreateGroup(summary[roomId].groupList, groupKey, groupName);
            const subGroup = getOrCreateSubGroup(group.subGroupList, subGroupKey, subGroupLabel);

            subGroup.nodes.push({
                label: instance?.instanceName || instance?.instanceNo,
                value: instanceId,
                badge: clusterRoles[instanceId]?.clusterRole === 'ARB_ACTIVE'
            });
        }
    };

    // 先处理所有实例并添加到 roomDefault
    allTodoInstanceList.forEach(instance => {
        const instanceId = instance?.id || instance?.instanceId;
        handleInstance('roomDefault', '全部', instance, instanceId);
        processedInstanceIds.add(instanceId);
    });

    // 处理已分配机房，节点在allTodoInstanceList中的key
    roomTableData.forEach(room => {
        room.instanceIds.forEach(instanceId => {
            const instance = instanceMap[instanceId];
            if (instance) {
                handleInstance(room.roomId, room?.roomNameAlias || room?.roomName, instance, instanceId);
            }
        });
    });

    // 转换成数组形式，并进行排序
    const menuList = Object.values(summary);

    // 对 `roomId` 排序，将 'roomDefault' 放在最前面
    menuList.sort((a, b) => {
        if (a.menuId === 'roomDefault') return -1;
        if (b.menuId === 'roomDefault') return 1;
        return b.menuName.localeCompare(a.menuName);
    });

    // 排序函数
    const sortGroups = (groupList) => {
        groupList.sort((a, b) => (a.key?.split(',')[1] || '').localeCompare(b.key?.split(',')[1] || ''));

        groupList.forEach(group => {
            group.subGroupList?.sort((a, b) => {
                if (a.key.includes('clusterDefault')) return -1;
                if (b.key.includes('clusterDefault')) return 1;
                return a.label.localeCompare(b.label);
            });

            group.subGroupList?.forEach(subGroup => {
                subGroup.nodes?.sort((a, b) => {
                    if (a.badge) return -1;
                    if (b.badge) return 1;
                    return a.label.localeCompare(b.label);
                });
            });
        });
    };

    // 对 groupList、subGroupList和 nodes 进行排序
    menuList.forEach(room => {
        sortGroups(room.groupList);
    });

    return menuList;
};

