@import "./input.less";

// 弹窗--button,table使用公共组件
/deep/.h-modal-header-inner {
    color: var(--font-color);
}

/deep/.h-modal-mask {
    background-color: var(--modal-mask-color);
}

/deep/.h-modal-body {
    padding: 16px;
}

/deep/.h-modal-confirm-head-icon {
    position: absolute;
    top: 0;
    left: 16px;
}

/deep/.h-modal-body p,
/deep/.h-modal-confirm-head-title {
    color: var(--font-color);
}

/deep/.h-modal-confirm-body {
    color: var(--font-body-color);
}

/deep/.h-modal-content {
    background: var(--wrapper-color);
    overflow: auto;
}

/deep/.h-modal-header {
    cursor: pointer;
    border-bottom: var(--border);
    padding: 14px 40px 14px 16px;
    line-height: 1;
}

/deep/.h-modal-footer {
    border-top: var(--border);
    padding: 12px 18px;
    text-align: right;
}

// 下拉框高度
/deep/.h-select-dropdown-content {
    max-height: 150px;
}
