## 左右布局 box

#### Props

| **属性**         | **说明**                                | **类型** | **默认值** |
| ---------------- | --------------------------------------- | -------- | ---------- |
| customMenu       | 左侧菜单自定义                          | Boolean  | false      |
| menuTitle        | 左侧菜单标题                            | String   | undefined  |
| footer           | 左侧菜单底部按钮文本                    | String   | undefined  |
| menuMinWidth     | 收起时左侧盒子最小宽度                  | number   | 0          |
| showSearch       | 是否显示搜索框                          | Boolean  | false      |
| showSearch       | 搜索框默认文本                          | String   | '查询'     |
| menuList         | 菜单列表                                | Array    | []         |
| titleAttribute   | 菜单项 title 所绑定的 menuList 的属性名 | String   | ''         |
| menuItemId       | 菜单项 id 所绑定的 menuList 的属性名    | String   | ''         |
| liClassAttribute | 菜单项图标绑定属性名                    | String   | ''         |

#### Events

| 事件名       | 说明                     | 返回值                   |
| ------------ | ------------------------ | ------------------------ |
| check-menu   | 切换菜单项时触发         | menuList 中选中项内容    |
| footer-click | 底部按钮点击时触发       | menuList 中选中项内容    |
| menu-fold    | 点击菜单展开收起图标触发 | 菜单收起：0，菜单展开：1 |

#### Methods

| **方法名** | **说明**                             | **用法/参数**              |
| ---------- | ------------------------------------ | -------------------------- |
| initMenu   | 菜单无搜索功能时需用此方法初始化菜单 | this.$refs.xxx.initMenu(); |

#### 示例程序

```vue
<template>
  <div class="main">
    <a-title title="产品集成管理"></a-title>
    <menu-layout
      ref="menu"
      menuTitle="已注册产品节点"
      :menuList="productList"
      titleAttribute="productName"
      @check-menu="checkModel"
    >
      <template v-slot:right>
        <h-tabs value="name1" class="product-box">
          <h-tab-pane label="产品信息" name="name1">
            <product-info-config></product-info-config>
          </h-tab-pane>
        </h-tabs>
      </template>
    </menu-layout>
  </div>
</template>

<script>
import aTitle from '@/components/common/title/aTitle';
import menuLayout from '@/components/common/menuLayout/menuLayout.js';
import productInfoConfig from '@/components/productIntegration/productInfoConfig';
export default {
  data() {
    return {
      productList: [
        { id: 0, productName: '华讯极速交易行情监控平台', selected: true },
        { id: 1, productName: '短信通道' },
        { id: 2, productName: 'APIDemo' },
        { id: 3, productName: 'LDP-UST证券极速交易系统#1' }
      ]
    };
  },
  methods: {
    checkModel(item) {
      console.log(item);
    }
  },
  components: { aBox, aTitle, productInfoConfig }
};
</script>

<style lang="less" scoped>
@import url('@/assets/css/tab.less');
.product-box {
  width: 100%;
}
</style>
```
