<!--
 * @Description: 应用集群信息弹窗
 * @Author: <PERSON><PERSON>
 * @Date: 2022-08-16 11:34:55
 * @LastEditTime: 2022-08-31 10:10:05
 * @LastEditors: <PERSON><PERSON> Ying
-->
<template>
    <div>
        <!-- 下发配置列表 -->
        <h-msg-box-safe v-model="modalData.status" :escClose="true" :mask-closable="false" title="应用集群信息"
            :footerHide="true" maxHeight="440" width="85">
            <div class="wrapper">
                <div class="wrapper-left">
                    <div class="title">证券前置集群</div>
                    <p><span>应用集群：</span>front</p>
                    <p><span>集群名称：</span>证券前置集群</p>
                    <p><span>集群实现方式：</span>成组</p>
                    <p><span>产品分片：</span>无</p>
                    <div class="title">应用实例</div>
                    <p>front#0</p>
                    <p>front#1</p>
                </div>
                <div class="wrapper-right">
                    <div class="title">证券前置实例</div>
                    <h-row style="padding: 5px 0 5px 32px;">
                        <h-col span="8">
                            <span>应用实例：</span>
                            front#0
                        </h-col>
                        <h-col span="8">
                            <span>应用版本：</span>
                            V1.3
                        </h-col>
                        <h-col span="8">
                            <span>应用名称：</span>
                            证券前置
                        </h-col>
                    </h-row>
                    <h-row style="padding: 0 0 5px 32px;">
                        <h-col span="8">
                            <span>应用类型：</span>
                            front
                        </h-col>
                        <h-col span="8">
                            <span>归属集群：</span>
                            front
                        </h-col>
                        <h-col span="8">
                            <span>集群身份：</span>
                            无
                        </h-col>
                    </h-row>
                    <div class="title">插件列表</div>
                    <h-tabs value="name2" style="margin: 0 10px 10px 32px;">
                        <h-tab-pane label="ldp_front" name="name1">
                            <h-input type="textarea" :rows="10" placeholder="请输入插件配置...">
                            </h-input>
                        </h-tab-pane>
                        <h-tab-pane label="ldp_mproxy" name="name2">
                            <h-input type="textarea" :rows="10" placeholder="请输入插件配置...">
                            </h-input>
                        </h-tab-pane>
                        <h-tab-pane label="ldp_logproxy" name="name3">
                            <h-input type="textarea" :rows="10" placeholder="请输入插件配置...">
                            </h-input>
                        </h-tab-pane>
                    </h-tabs>
                </div>
            </div>
        </h-msg-box-safe>
    </div>
</template>

<script>
export default {
    data() {
        return {
            modalData: {
                status: true
            },
            formItem: {}
        };
    }
};
</script>

<style lang="less" scoped>
@import url("./common.less");
</style>
