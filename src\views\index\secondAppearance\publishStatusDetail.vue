<template>
    <div class="container">
        <div v-if="hasTask" class="has-task">
            <h-icon name="load-c" color="#FFFFFF" class="loading-icon-load"></h-icon>
            {{taskDetail.title || '-'}}执行上场中
        </div>
        <div v-else class="no-task">
            <span class="text">当前无上场任务执行</span>
            <span class="division"></span>
            <span class="last-publish-text">最近一次上场：{{taskDetail.title  ? `${taskDetail.title}上场`: '无上场记录'}}</span>
        </div>
        <h-poptip placement="bottom-end" width="300">
            <h-button type="text">详情</h-button>
            <div slot="content" class="content">
                <div class="content-title">{{taskDetail.title  ? `${taskDetail.title}上场`: '无上场记录'}}</div>
                <div class="content-main">
                    <div class="left">上场信息</div>
                    <div class="division"></div>
                    <div class="right">
                        <div>
                            <span class="title">开始时间: </span>
                            <span class="text"> {{ taskDetail.startTime|| '-'}} </span>
                        </div>
                        <div>
                            <span class="title">结束时间: </span>
                            <span class="text"> {{taskDetail.endTime|| '-'}} </span>
                        </div>
                        <div>
                            <span class="title">需上场表数量: </span>
                            <span class="text"> {{taskDetail.totalCount !== undefined ? taskDetail.totalCount : '-'}} </span>
                        </div>
                        <div>
                            <span class="title">上场成功表数量: </span>
                            <span class="text"> {{taskDetail.successCount!== undefined ? taskDetail.successCount: '-'}} </span>
                        </div>
                        <div>
                            <span class="title">上场失败表数量: </span>
                            <span class="text"> {{taskDetail.failCount!== undefined ? taskDetail.failCount: '-'}}</span>
                        </div>
                    </div>
                </div>
            </div>
        </h-poptip>
    </div>
</template>

<script>
export default {
    name: 'PublishStatusDetail',
    props: {
        taskDetail: {
            type: Object,
            default: () => {}
        },
        hasTask: {
            type: Boolean,
            default: false
        }
    }
};
</script>

<style lang="less" scoped>
.container {
    font-size: 12px;
    color: #fff;
    float: right;
    display: flex;

    .has-task {
        .loading-icon-load {
            animation: ani-loading-spin 1s linear infinite;
            display: inline-block;
        }

        @keyframes ani-loading-spin {
            from {
                transform: rotate(0deg);
            }

            50% {
                transform: rotate(180deg);
            }

            to {
                transform: rotate(360deg);
            }
        }
    }

    .no-task {
        margin-top: 1px;
        display: flex;
        align-items: center;

        .text {
            font-size: 12px;
            color: #fff;
            text-align: right;
            line-height: 20px;
            font-weight: normal;
        }

        .division {
            width: 1px;
            height: 14px;
            background: #485565;
            margin: 0 8px;
        }

        .last-publish-text {
            font-size: 12px;
            color: #cacfd4;
            line-height: 20px;
            font-weight: normal;
        }
    }

    .h-btn-text {
        color: var(--link-color);
    }

    .h-btn-text:hover {
        color: var(--link-color);
        text-decoration: underline;
    }
    // 重写样式
    /deep/ .h-poptip {
        .h-poptip-popper {
            .h-poptip-content {
                .h-poptip-inner {
                    height: 153px;
                    background: rgba(88, 94, 106, 0.9);

                    .h-poptip-body {
                        padding: 0;
                    }
                }

                .h-poptip-arrow {
                    border-bottom-color: var(--poptip-bg-color) !important;
                }

                .h-poptip-arrow::after {
                    border-bottom-color: var(--poptip-bg-color) !important;
                }
            }
        }
    }

    .content {
        .content-title {
            font-size: 14px;
            color: #fff;
            line-height: 20px;
            font-weight: normal;
            height: 33px;
            padding: 7px 12px;
            border-bottom: 1px solid rgba(255, 255, 255, 0.15);
        }

        .content-main {
            height: 119px;
            display: flex;
            font-size: 12px;

            .left {
                height: 100%;
                width: 68px;
                padding: 6px 12px;
            }

            .division {
                width: 1px;
                background: rgba(255, 255, 255, 0.15);
                margin-top: 6px;
                margin-bottom: 15px;
            }

            .right {
                flex: 1;
                height: 100%;
                display: flex;
                padding: 6px 12px;
                justify-content: flex-start;
                flex-direction: column;
                gap: 2px;

                .title {
                    color: #cacfd4;
                }
            }
        }
    }
}

</style>
