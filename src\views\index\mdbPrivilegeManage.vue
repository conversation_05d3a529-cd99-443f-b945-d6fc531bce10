<template>
    <div class="main">
        <div class="title">
            <a-title title="MDB权限管理">
                <slot>
                    <span class="warning-title">
                        <h-icon :name="iconName" :color="iconColor" size="12"></h-icon>
                        <span class="text" :title="(headerMessage || '- ')+'(最新下发：'+ (hisPermissionsInfo.headerTime || '- ') + ' ' + headerStatus + '）'">
                            {{ headerMessage || '- ' }}(最新下发：{{hisPermissionsInfo.headerTime || '-'}} <a v-show="hisPermissionsInfo.type"  href="javascript:void(0)" @click="handleHisInfo">{{headerStatus}}</a>)</span>
                    </span>
                    <div class="title-select">
                        <h-dropdown
                            trigger="click"
                            @on-click="handleDropdownSelect">
                            <a-button type="dark" :loading="btnLoading">
                                权限操作
                                <h-icon name="unfold"></h-icon>
                            </a-button>
                            <h-dropdown-menu slot="list">
                                <h-dropdown-item key="issuePermissions" name="issuePermissions" :disabled="loading">
                                    <div class="dropdown-item-text" title="将本地用户权限下发至MDB" >同步权限</div>
                                </h-dropdown-item>
                                <h-dropdown-item key="viewMdbPermissions" name="viewMdbPermissions" :disabled="loading" style="border-bottom: 1px solid #444a60;">
                                    <div class="dropdown-item-text">查看MDB权限</div>
                                </h-dropdown-item>
                                <h-dropdown-item key="importPermissions" name="importPermissions" :disabled="loading">
                                    <div class="dropdown-item-text">导入权限</div>
                                </h-dropdown-item>
                                <h-dropdown-item key="downloadPermissions" name="downloadPermissions" :disabled="loading" style="border-bottom: 1px solid #444a60;">
                                    <div class="dropdown-item-text">导出权限</div>
                                </h-dropdown-item>
                                <h-dropdown-item key="editPermissions" name="editPermissions" disabled>
                                    <div class="dropdown-item-text">权限可编辑  &nbsp;&nbsp;&nbsp;&nbsp;<h-switch v-model="editable" size="small" @on-change="()=>handleDropdownSelect('editPermissions')"></h-switch></div>
                                </h-dropdown-item>
                            </h-dropdown-menu>
                        </h-dropdown>
                        <div class="split-solid"></div>
                        <h-select
                            v-show="productList.length > 1"
                            v-model="productInstNo"
                            placeholder="请选择"
                            placement="bottom"
                            :positionFixed="true"
                            :clearable="false"
                            @on-change="checkProduct">
                            <h-option v-for="item in productList" :key="item.id" :value="item.productInstNo">{{ item.productName }}</h-option>
                        </h-select>
                    </div>
                </slot>
            </a-title>
        </div>
        <a-loading v-if="loading"></a-loading>
        <h-tabs v-model="tabName" class="product-box" @on-click="tabClick(tabName)">
            <h-tab-pane v-for="item in editableTabs" :key="item.name" :label="item.describe" :name="item.name">
                <component :is="item.name" :ref="item.name" :productId="productInstNo" :editable="!editable" @check-status="checkEditStatus"></component>
            </h-tab-pane>
        </h-tabs>
        <!-- 权限下发 --> <!-- 最新权限一致结果 -->
        <unified-modal v-if="permissionsInfo.status" key="permissionsInfo" :modalInfo="permissionsInfo"></unified-modal>
        <!-- 权限查看 -->
        <query-mdb-Perm-info
            v-if="viewMdbPermissionsInfo.status"
            :modalInfo="viewMdbPermissionsInfo"
            :productId="productInstNo" />
        <!-- 导入规则 -->
        <import-permissions-info-modal
            v-if="importPermissionsInfo.status"
            :modalInfo="importPermissionsInfo"
            :productId="productInstNo"
            @import-permissions="handleImportPermissions"
         />
         <!-- 导入结果反馈 -->
         <unified-modal v-if="importPermissionsResInfo.status"  key="importPermissionsResInfo" :modalInfo="importPermissionsResInfo"></unified-modal>
    </div>
</template>

<script>
import _ from 'lodash';
import { mapState, mapActions } from 'vuex';
import { transferVal, exportFile, getCurrentDatetime } from '@/utils/utils';
import aTitle from '@/components/common/title/aTitle';
import aButton from '@/components/common/button/aButton';
import aLoading from '@/components/common/loading/aLoading';
import userManage from '@/components/mdbPrivilegeManage/userManage';
import roleManage from '@/components/mdbPrivilegeManage/roleManage';
import unifiedModal from '@/components/common/apmMsgBox/unifiedModal';
import queryMdbPermInfo from '@/components/mdbPrivilegeManage/modal/queryMdbPermInfo';
import importPermissionsInfoModal from '@/components/mdbPrivilegeManage/modal/importPermissionsInfoModal';
import importStatusTableIcon from '@/components/secondAppearance/importStatusTableIcon.vue'; // 有用
import { setIssuePerInfo, getHistoryIssuePerInfo, getConsistencyInfo, downloadPermissionsFile, importPermissionsFile, setMdbPerAuthConfig, getMdbPerAuthConfig } from '@/api/mdbPrivilegeApi';

export default {
    name: 'MdbPrivilegeManage',
    data() {
        const VERIFE_RESULT_LIST = [
            {
                value: 'FAIL',
                label: '下发失败',
                icon: 'error'
            },
            {
                value: 'SUCCESS',
                label: '下发成功',
                icon: 'success'
            }
        ];
        return {
            editable: true,
            productInstNo: '', // 选中的产品
            tabName: 'userManage', // tab默认选择
            loading: false,
            editableTabs: [
                {
                    name: 'userManage',
                    describe: '用户管理',
                    timerSwitch: false,
                    timerInterval: 10
                },
                {
                    name: 'roleManage',
                    describe: '角色管理',
                    timerSwitch: false,
                    timerInterval: 10
                }
            ],
            // 权限是否一致
            isLock: false,
            timer: null,
            // 最近一次权限状态结果
            hisPermissionsInfo: {
                consistent: '',
                type: '',
                headerTime: '',
                tableData: []
            },
            // 下发权限
            btnLoading: false,  // 下发权限按钮loading
            permissionsInfo: {
                status: false,
                type: '',
                title: '',
                width: '500',
                contentType: ['text', 'table'],
                contentText: '',
                useHeaderSlot: true,
                columns: [
                    { title: '集群', key: 'clusterName', ellipsis: true },
                    { title: '下发结果', key: 'result', minWidth: 280,
                        render: (h, params) => {
                            const type = params?.row?.status || '';
                            const resultObj = _.find(VERIFE_RESULT_LIST, ['value', type]) || {};
                            return (
                                <div title={(resultObj?.label || '-') + (params?.row?.message ? '，' + params?.row?.message : '')} class="h-table-cell-ellipsis">
                                    <importStatusTableIcon type={resultObj?.icon || ''} />
                                    {resultObj?.label || '-'}{params?.row?.message ? '，' + params?.row?.message : ''}
                                </div>
                            );
                        }
                    }
                ],
                tableData: []
            },
            // 查看MDB权限
            viewMdbPermissionsInfo: {
                status: false
            },
            // 导入权限
            importPermissionsInfo: {
                status: false
            },
            // 导入接口反馈
            importPermissionsResInfo: {
                status: false,
                modalTitle: '导入权限',
                type: '',
                title: '',
                width: '480',
                contentType: 'text',
                contentText: ''
            }
        };
    },
    mounted() {
        this.init();
    },
    beforeDestroy() {
        this.clearPolling();
    },
    computed: {
        ...mapState({
            productList: state => {
                return state.product.productListLight || [];
            }
        }),
        iconName() {
            const icons = {
                true: 'success1',
                false: 'prompt1',
                null: 'prompt1'
            };
            return icons[this.hisPermissionsInfo.consistent] || '';
        },
        iconColor() {
            const colors = {
                true: '#52C41A',
                false: '#FF9901',
                null: '#FF9901'
            };
            return colors[this.hisPermissionsInfo.consistent] || '';
        },
        headerMessage() {
            const messages = {
                true: 'MDB权限与本地权限一致。',
                false: 'MDB权限与本地权限不一致。',
                null: '暂时无法获取MDB权限，请稍后查看。'
            };
            return messages[this.hisPermissionsInfo.consistent] || '';
        },
        headerStatus(){
            const status = {
                success: '权限下发成功！',
                warning: '权限下发异常！',
                error: '权限下发失败！'
            };
            return status[this.hisPermissionsInfo.type] || '';
        }
    },
    methods: {
        ...mapActions({ getProductList: 'product/getProductListLight' }),
        // 初始化页面
        async init() {
            this.clearPolling();
            try {
                this.loading = true;
                await this.getProductList({ filter: 'excludeLdpApm' });
                const productInstNo = localStorage.getItem('productInstNo');
                this.productInstNo = _.find(this.productList, ['productInstNo', productInstNo])?.productInstNo || this.productList?.[0]?.productInstNo;
                this.setPolling();
            } catch (e) {
                console.error(e);
            } finally {
                this.loading = false;
            }
        },
        // 切换导航产品
        async checkProduct(item) {
            // 清空历史权限问题、以及初始请求一次
            this.resetPermissionsData();
            await this.getHistoryIssuePerInfo();
            localStorage.setItem('productInstNo', item);
            setTimeout(() => {
                this.loading = false;
                this.tabClick(this.tabName);
            }, 500);
        },
        // 切换tab
        tabClick(name) {
            this.$refs?.[name]?.[0] && this.$refs[name][0].initData();
        },
        // 轮询是否一致以及下发权限记录
        setPolling() {
            this.clearPolling();
            this.timer = setInterval(async () => {
                if (this.isLock) return;
                this.isLock = true;
                await this.getHistoryIssuePerInfo();
                this.isLock = false;
            }, 10000);
        },
        clearPolling() {
            this.timer && clearInterval(this.timer);
        },
        // 下发权限操作
        async handleDropdownSelect(name){
            // 下发权限
            if (name === 'issuePermissions'){
                this.handleIssuePermissions();
                return;
            }

            // 查看权限
            if (name === 'viewMdbPermissions'){
                this.handlViewMdbPermissions();
                return;
            }

            // 编辑
            if (name === 'editPermissions'){
                // this.editable已被修改，switch先修改值再触发change事件
                if (this.editable){
                    this.$hMsgBoxSafe.confirm({
                        title: '确定要开启权限编辑吗？',
                        content: '开启后可在线修改MDB权限内容（包括"用户"与"角色"）。若当前权限内容为"导入"产生，编辑可能导致主备权限版本不一致。请谨慎！',
                        onOk: async () => {
                            await this.changeEditStatus();
                        },
                        onCancel: () => {
                            this.editable = false; // 未修改前的值
                        }
                    });
                } else {
                    await this.changeEditStatus();
                }
            }

            // 导出
            if (name === 'downloadPermissions'){
                const res = await downloadPermissionsFile({ productId: this.productInstNo });
                if (!res?.code){
                    this.$hMessage.success('导出成功!');
                    exportFile(res, `ldpMdbAuth_${getCurrentDatetime()}`, 'enc');
                } else if (res?.code?.length === 8)  {
                    this.$hMessage.error(res?.message);
                }
            }

            // 导入
            if (name === 'importPermissions'){
                this.importPermissionsInfo.status = true;
                this.importPermissionsInfo.productId = this.productInstNo;
            }

        },
        // 导入权限
        async handleImportPermissions(obj){
            this.btnLoading = true;
            try {
                const res = await importPermissionsFile(obj);
                this.btnLoading = false;
                if (res?.code === '200'){
                    this.importPermissionsResInfo = {
                        ...this.importPermissionsResInfo,
                        status: true,
                        type: 'success',
                        title: '导入权限成功',
                        contentText: 'MDB权限导入成功。为保证主备机房间权限一致，导入权限后默认不可在线"编辑"权限内容。如需编辑，请至当前页面顶部"MDB权限管理-权限操作"中开启。'
                    };
                    this.editable = false; // 手动修改Switch值
                    await this.changeEditStatus();
                } if (res?.code?.length === 8) {
                    this.importPermissionsResInfo = {
                        ...this.importPermissionsResInfo,
                        status: true,
                        type: 'error',
                        title: '导入权限失败',
                        contentText: res?.message || '导入权限失败'
                    };
                }
            } catch (e) {
                console.error(e);
            } finally {
                this.btnLoading = false;
            }
        },
        //  最近一次权限状态结果
        async getHistoryIssuePerInfo() {
            const param = {
                productId: this.productInstNo
            };

            try {
                // 获取历史下发信息
                const [historyRes, consistentRes] = await Promise.all([
                    getHistoryIssuePerInfo(param),
                    getConsistencyInfo(param)
                ]);

                // 处理历史下发信息
                if (historyRes?.code === '200') {
                    if (param.productId !== this.productInstNo) return;
                    this.hisPermissionsInfo = {
                        ...this.hisPermissionsInfo,
                        type: this.getStatus(historyRes.data),
                        headerTime: historyRes.data?.startTime || '',
                        tableData: [...historyRes.data?.details || []]
                    };

                } else {
                    this.hisPermissionsInfo = {
                        ...this.hisPermissionsInfo,
                        type: '',
                        headerTime: '',
                        tableData: []
                    };
                }

                // 处理一致性信息
                if (consistentRes?.code === '200') {
                    if (param.productId !== this.productInstNo) return;
                    this.hisPermissionsInfo.consistent =  transferVal(consistentRes?.data?.consistent) ? consistentRes?.data?.consistent : null;;
                } else {
                    this.hisPermissionsInfo.consistent = '';
                }
            } catch (error) {
                console.error(error);
                this.resetPermissionsData();
            }
        },
        // 清空下发权限记录
        resetPermissionsData(){
            this.hisPermissionsInfo = {
                consistent: '',
                type: '',
                headerTime: '',
                tableData: []
            };
        },
        // 下发权限
        async handleIssuePermissions(){
            this.btnLoading = true;
            try {
                const param = {
                    productId: this.productInstNo
                };
                const res = await setIssuePerInfo(param);
                this.btnLoading = false;
                if (res?.code === '200'){
                    const type = this.getStatus(res?.data || {});
                    this.permissionsInfo = {
                        ...this.permissionsInfo,
                        status: true,
                        type: type,
                        title: this.getTitleByType(type),
                        contentText: this.getBodyMessageByType(type),
                        tableData: res?.data?.details || []
                    };
                } if (res.code?.length === 8) {
                    this.$hMessage.error(res.message);
                }
                this.tabClick(this.tabName);
            } catch (e) {
                console.error(e);
            } finally {
                this.btnLoading = false;
            }
        },
        // 获取下发状态
        getStatus(data) {
            if (!data) return '';
            if (data?.totalNum === null || data?.successNum === null || data?.failNum === null) {
                return 'error';
            }
            if (data?.totalNum === 0) {
                if (data?.successNum === 0 && data?.failNum === 0) {
                    return 'success';
                } else {
                    return 'error';
                }
            }
            if (data?.successNum === data?.totalNum) {
                return 'success';
            } else if (data?.failNum === data?.totalNum) {
                return 'error';
            } else {
                return 'warning';
            }
        },
        // 获取结果对象
        getResultObj(type) {
            const resultMap = {
                FAIL: { label: '下发失败', icon: 'closecircled', color: '#F5222D' },
                SUCCESS: { label: '下发成功', icon: 't-b-correctinformati', color: '#52C41A' }
            };
            return resultMap[type] || { label: '-', icon: '', color: '' };
        },
        // 查看最近一次记录
        handleHisInfo(){
            const type = this.hisPermissionsInfo?.type;
            this.permissionsInfo = {
                ...this.permissionsInfo,
                status: true,
                type: type,
                title: this.getTitleByType(type),
                contentText: this.getBodyMessageByType(type),
                tableData: this.hisPermissionsInfo?.tableData || []
            };
        },
        // 根据类型获取标题
        getTitleByType(type) {
            const titles = {
                success: '权限下发成功！',
                warning: '权限下发异常！',
                error: '权限下发失败！'
            };
            return titles[type] || '';
        },
        // 根据类型获取文本内容
        getBodyMessageByType(type) {
            const messages = {
                success: '用户权限下发成功！本次下发详细如下：',
                warning: '用户权限部分下发失败，请稍后重新尝试。本次下发详细如下：',
                error: '用户权限下发失败，请稍后重新尝试。本次下发详细如下：'
            };
            return messages[type] || '';
        },
        // 查看权限
        handlViewMdbPermissions(){
            this.viewMdbPermissionsInfo.status = true;
            this.viewMdbPermissionsInfo.productInstNo = this.productInstNo;
        },
        // 权限编辑查看
        async checkEditStatus(){
            try {
                const param = {
                    productId: this.productInstNo
                };
                const res = await getMdbPerAuthConfig(param);
                if (res?.code === '200'){
                    this.editable = res?.data?.editable;
                } if (res.code?.length === 8) {
                    this.$hMessage.error(res.message);
                }
            } catch (e) {
                console.error(e);
                this.editable = true;
            }
        },
        // 权限编辑修改
        async changeEditStatus(){
            try {
                const param = {
                    productId: this.productInstNo,
                    editable: this.editable
                };
                const res = await setMdbPerAuthConfig(param);
                if (res?.code === '200'){
                    this.$hMessage.success('权限可编辑状态修改成功!');
                } else if (res.code?.length === 8) {
                    this.$hMessage.error(res.message);
                    this.editable = !this.editable;  // 1、手动点击Switch开关时是线改变editable值再触发change事件，change失败需重新置为修改前的值；2、导入成功，但修改编辑配置失败
                } else {
                    this.editable = !this.editable; // 2、导入成功，但修改编辑配置失败
                }
                this.$nextTick(async () => {
                    await this.tabClick(this.tabName);
                });
            } catch (e) {
                console.error(e);
                this.editable = !this.editable; // 异常时回退
            }
        }
    },
    components: { aTitle, aButton, aLoading, userManage, roleManage, unifiedModal, queryMdbPermInfo, importPermissionsInfoModal }
};
</script>

<style lang="less" scoped>
@import url("@/assets/css/tab.less");
@import url("@/assets/css/input.less");
@import url("@/assets/css/menu.less");

.main {
    .title {
        p {
            display: inline-block;
            color: var(--font-color);
            font-size: var(--title-font-size);
            position: absolute;
            left: 50%;
            transform: translateX(-50%);
        }

        .warning-title {
            font-size: 12px;
            color: #fff;
            margin-left: 10px;
            display: flex;
            position: absolute;
            top: 0;
            left: 130px;

            .h-icon {
                position: relative;
                top: 0;
                margin-right: 4px;
            }

            .text {
                width: calc(100vw - 600px);
                display: inline-block;
                overflow: hidden;
                text-overflow: ellipsis;
                white-space: nowrap;
            }
        }

        .split-solid {
            width: 1px;
            background-color: #474e6f;
            height: 26px;
            margin-left: 2px;
            margin-right: 2px;
            display: inline-block;
            position: relative;
            top: 9px;
        }

        .title-select {
            float: right;
            margin-right: 4px;
            top: 5px;

            .h-select {
                width: 180px;
            }
        }
    }

    /deep/.h-dropdown-item {
        color: var(--font-color);
    }

    /deep/.h-dropdown-item-disabled {
        cursor: not-allowed;
    }

    /deep/.h-dropdown-item-disabled:hover {
        background: #262d43;
    }

    /deep/.h-dropdown-menu {
        padding: 0;
        background: #262d43;
        border: 1px solid #485565;
        box-shadow: 0 2px 6px 0 rgba(0, 0, 0, 0.1);
        border-radius: 4px;
    }

    /deep/.h-dropdown-item:hover {
        background: #1f3759;
    }

    /deep/ .dropdown-item-text {
        display: inline-block;
        margin-right: 10px;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
    }
}
</style>
