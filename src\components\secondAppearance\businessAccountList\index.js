import '../public.less';
import normalTable from '@/components/common/bestTable/normalTable/normalTable';
import aButton from '@/components/common/button/aButton';
import editConditionModal from '@/components/secondAppearance/businessAccountList/modal/editConditionModal.vue';
import importStatusTableIcon from '@/components/secondAppearance/importStatusTableIcon.vue';
import { EXECUTE_STATUS_OPTIONS } from '@/components/secondAppearance/constant';
import drawerContent from '@/components/secondAppearance/logDrawerContent.vue';
import { getLoadDataRules, getLoadDataStatus } from '@/api/brokerApi';
import publishModal from '@/components/secondAppearance/businessAccountList/modal/publishModal.vue';

export default {
    name: 'BusinessAccountList',
    components: {
        aButton,
        normalTable,
        publishModal,
        editConditionModal,
        importStatusTableIcon,
        drawerContent
    },
    props: {
        productId: {
            type: String,
            default: ''
        },
        clusterList: {
            type: Array,
            default: () => []
        }
    },
    computed: {
        formItems(){
            return [
                {
                    type: 'input',
                    label: '分片号',
                    key: 'shardingNo',
                    value: ''
                },
                {
                    type: 'select',
                    label: '集群名称',
                    key: 'clusterNames',
                    options: this.clusterList,
                    value: [],
                    multiple: true
                },
                {
                    type: 'select',
                    label: '执行状态',
                    key: 'execStatuses',
                    options: EXECUTE_STATUS_OPTIONS,
                    value: [],
                    multiple: true
                },
                {
                    type: 'input',
                    key: 'tableName',
                    label: '表名',
                    value: ''
                }
            ];
        }
    },
    data() {
        return {
            btnLoading: false,
            loading: false,
            columns: [
                {
                    type: 'selection',
                    width: 60,
                    align: 'center',
                    fixed: 'left'
                },
                {
                    title: '表名',
                    key: 'tableName',
                    width: 200,
                    ellipsis: true
                },
                {
                    width: 240,
                    title: '集群名称',
                    key: 'clusterName'
                },
                {
                    width: 240,
                    title: '分片号',
                    key: 'shardingNo'
                },
                {
                    width: 120,
                    title: '加载方式',
                    render: (_, params) => {
                        let loadingMethodDom = <div>暂无数据</div>;
                        switch (params.row.loadMode) {
                            case '1':
                                loadingMethodDom = <div>追加</div>;
                                break;
                            case '2':
                                loadingMethodDom = <div>覆盖</div>;
                                break;
                            case '3':
                                loadingMethodDom = <div>追加+覆盖</div>;
                                break;
                        }
                        return loadingMethodDom;
                    }
                },
                {
                    width: 120,
                    title: '执行状态',
                    render: (_, params) => {
                        const text = EXECUTE_STATUS_OPTIONS.find(status => status.value === params.row.execStatus)?.label || '';
                        let sqlTableIconType;
                        switch (params.row.execStatus){
                            case 'running':
                                sqlTableIconType = 'loading';
                                break;
                            case 'succeeded':
                                sqlTableIconType = 'success';
                                break;
                            case 'failed':
                                sqlTableIconType = 'error';
                                break;
                            case 'pending':
                                sqlTableIconType = 'offline';
                                break;
                        }
                        return <div>
                            <importStatusTableIcon type={sqlTableIconType}/>
                            {text}
                        </div>;
                    }
                },
                {
                    title: '条件',
                    key: 'loadSql',
                    ellipsis: true
                },
                {
                    title: '执行结果',
                    key: 'execDetail',
                    ellipsis: true
                },
                {
                    title: '操作',
                    key: 'action',
                    fixed: 'right',
                    width: 160,
                    render: (h, params) => {
                        return h('div', [
                            h('Button',
                                {
                                    props: {
                                        size: 'small',
                                        type: 'text',
                                        disabled: ['pending', 'running'].includes(params.row.execStatus)
                                    },
                                    on: {
                                        click: () => {
                                            this.logTableDrawerVisiable = true;
                                            this.logTableDrawerData = params.row;
                                        }
                                    }
                                },
                                '查看日志'
                            )
                        ]);
                    }
                }
            ],
            checkedItems: [], // 选中的表格行
            tableData: [],
            total: 0,
            msgBoxData: {},
            // 弹窗元数据
            modalInfo: {
                title: '', // 弹窗标题
                data: {},
                status: false, // 弹窗状态，是否关闭
                type: '' // 当前页面打开的弹窗类型:editCondition（编辑条件）、publish（上场）、viewLog（查看日志）
            },
            logTableDrawerData: {},
            logTableDrawerVisiable: false
        };
    },
    methods: {
        async initData() {
            this.$refs['business-account-table'].$_handleQuery();
            this.checkedItems = [];
        },
        // 获取所有上场表信息
        async getAllLoadDataRules(){
            let loadDataRulesData = [];
            const param = {
                productId: this.productId,
                whereCondition: 'fundAccount',
                shardingNo: '',
                clusterNames: [],
                execStatuses: [],
                tableName: ''
            };
            loadDataRulesData = (await getLoadDataRules(param))?.data || [];
            return loadDataRulesData;
        },
        // 调用接口 表查询
        async handleQuery(val){
            const param = {
                productId: this.productId,
                whereCondition: 'fundAccount',
                shardingNo: val?.shardingNo || '',
                clusterNames: val?.clusterNames.join(',') || [],
                execStatuses: val?.execStatuses.join(',') || [],
                tableName: val?.tableName || ''
            };
            this.checkedItems = [];
            this.tableData = [];
            try {
                this.loading = true;
                const loadDataRulesData = (await getLoadDataRules(param))?.data || [];
                const loadDataRuleStatus = (await getLoadDataStatus(param))?.data || [];
                // 数据聚合  通过 tableName 进行关联
                const result = loadDataRulesData.map((data => {
                    let mergedItem = {};
                    const status = loadDataRuleStatus?.execs?.find(exec => {
                        return  exec.tableName === data.tableName && exec.clusterName === data.clusterName;
                    });
                    if (status){
                        mergedItem = { ...data, ...status };
                    } else {
                        mergedItem = { ...data, execStatus: 'pending', execIds: [], execDetail: '-' };
                    }
                    // 如果处于执行状态处于上场中，则禁用多选
                    if (mergedItem.execStatus === 'running'){
                        mergedItem._disabled = true;
                    }
                    return mergedItem;
                })).filter(item => {
                    // 根据执行状态对表格数据进行过滤
                    if (val?.execStatuses?.length === 0){
                        return true;
                    }
                    return val?.execStatuses.indexOf(item.execStatus) !== -1;
                });
                this.tableData = result;
            } catch (err) {
                this.$hMessage.error(err.message);
            }
            this.loading = false;
        },
        async handleEditCondition(){
            this.modalInfo.type = 'editCondition';
            this.modalInfo.status = true;
            this.modalInfo.data = this.tableData?.[0] || {};
            this.modalInfo.title = '编辑条件';
        },
        async handlePublish(){
            if (this.checkedItems.length <= 0){
                this.$hMessage.warning('请选择需要上场的表!');
                return;
            }
            this.modalInfo.type = 'publish';
            try {
                this.btnLoading = true;
            } catch (error) {
                console.log(error);
            }
            this.btnLoading = false;
            this.modalInfo.status = true;
        },
        handleTableSelect(checkedItems){
            this.checkedItems = checkedItems;
        }
    },
    render() {

        const tableDom = <normal-table
            isSimpleTable={true}
            ref="business-account-table"
            formTitle="筛选条件"
            tableTitle="持久化数据表"
            formItems={this.formItems}
            columns={this.columns}
            loading={this.loading}
            tableData={this.tableData}
            hasSetTableColumns={false}
            showTitle={true}
            hasPage={false}
            v-on:query={this.handleQuery}
            v-on:selection={this.handleTableSelect}
        >
            <div class='table-slot-box' slot='btns'>
                <a-button
                    type="primary"
                    onClick={this.handleEditCondition}>全量编辑</a-button>
                <a-button
                    type="dark"
                    loading={this.btnLoading}
                    onClick={this.handlePublish}>批量上场</a-button>
            </div>
        </normal-table>;

        let msgBoxDom;
        switch (this.modalInfo.type) {
            case 'editCondition':
                msgBoxDom = <editConditionModal
                    productId={this.productId}
                    msgBoxData={this.msgBoxData}
                    modalInfo={this.modalInfo}
                    v-on:query={this.initData}
                />;
                break;
            case 'publish':
                msgBoxDom = <publishModal
                    productId={this.productId}
                    modalInfo={this.modalInfo}
                    checkedItems={this.checkedItems}
                    v-on:query={this.initData}
                />;
                break;
            default:
                msgBoxDom = '';
        }

        const logTableDrawer = <h-drawer
            v-model={this.logTableDrawerVisiable}
            width="742"
            title={'上场日志-' + this.logTableDrawerData.tableName}
        >
            <drawerContent
                drawerData={this.logTableDrawerData}
                productId={this.productId}
            />
        </h-drawer>;

        const styleDom = <style jsx>
            {
                `.table-slot-box > .h-btn { margin-right: 10px;}`
            }
        </style>;

        return <div class="topic-box">
            {tableDom}
            {msgBoxDom}
            {styleDom}
            {logTableDrawer}
        </div>;
    },
    mounted(){
        this.$nextTick(() => {
            this.$hCore.on('loadDataFinish', (whereCondition) => {
                // 上场任务已完成，需要刷新表格数据
                if (whereCondition === 'fundAccount') {
                    this.$refs['business-account-table'].$_handleQuery();
                }
            });
        });
    },
    beforeDestroy () {
        this.$hCore.off('loadDataFinish');
    }
};

