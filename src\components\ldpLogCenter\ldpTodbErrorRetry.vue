<template>
    <div ref="wrapper" class="error-log-box">
        <!-- 加载状态 -->
        <a-loading v-if="loading" style="z-index: 9;"></a-loading>
        <!-- 查询结果 -->
        <div ref='table-box' class="all-table-box">
            <h-form
                ref="formItems"
                class="form-box"
                :model="formItems"
                inline
                :col="4"
                style="background: var(--wrapper-color);"
                hoverValidate
                autoLabelTitle
            >
            <h-form-item prop="instance" :label-width="0" required>
                <h-select
                v-model="formItems.instance"
                style="width: 150px;"
                :clearable="true"
                showTitle
                placeholder="节点"
                @on-drop-change="handleDropChange"
                >
                <h-option v-for="item in todoInstanceList" :key="item.id" :value="item.id"
                    >{{ item.instanceName }}
                </h-option>
                </h-select>
            </h-form-item>
            <h-form-item prop="clusterName" :label-width="0">
                <h-select
                v-model="formItems.clusterName"
                style="width: 150px;"
                isString
                multiple
                multClearable
                collapseTags
                showTitle
                :clearable="true"
                placeholder="集群名称"
                >
                <h-option v-for="item in clusterList" :key="item.clusterName" :value="item.clusterName"
                    >{{ item.clusterName }}
                </h-option>
                </h-select>
            </h-form-item>
            <h-form-item label="" prop="btn" :label-width="0">
                <a-button
                type="primary"
                :disabled="!formItems.instance"
                @click="handleQuery"
                >查询</a-button>
            </h-form-item>
            </h-form>
            <div class="result-box" >
                <h-row >
                    <h-col class="table-box" span='24'>
                        <!-- 查询回库集群级别日志结果 -->
                        <obs-table
                        ref="clusterTableData"
                        :title="clusterTableTitle"
                        :height="clusterTableHeight"
                        border
                        :columns="clusterColumns"
                        :tableData="clusterTableData"
                        :loading="clusterLoading"
                        :hasPage="false"
                        showTitle
                        />
                        <br/>
                        <div class="page-box">
                        <a-button type="dark" size="small" :disabled="!historyFormItems.instance || clusterEndMsgNos.length <= 1 || buttonLoading"  @click="()=>handlePrev('cluster')"
                            >上一页</a-button
                        >&nbsp;&nbsp;
                        <a-button type="dark" size="small" :disabled="clusterTableData.length < pageSize || buttonLoading"  @click="()=>handleNext('cluster')"
                            >下一页</a-button
                        >
                        </div>
                    </h-col>
                </h-row>
            </div>
            <div ref="record-box" class="record-box">
                <info-sum-bar
                    :data="infoData"
                    :selectInfoId="selectInfoId"
                    selectedStyleType="border"
                    @info-bar-click="handleBarClick"
                    @button-click="handleButtonClick"
                ></info-sum-bar>
                <div class="table-box-bottom">
                    <obs-table
                        ref="taskTableData"
                        :height="errTaskTableHeight"
                        border
                        :columns="errTaskColumns"
                        :tableData="errTaskTableData"
                        :hasPage="false"
                        showTitle
                    />
                    <br/>
                    <div class="page-box">
                        <a-button type="dark" size="small" :disabled="!historyFormItems.instance || errTaskEndMsgNos.length <= 1 || buttonLoading"  @click="()=>handlePrev('task')"
                        >上一页</a-button
                        >&nbsp;&nbsp;
                        <a-button type="dark" size="small" :disabled="errTaskTableData.length < pageSize || buttonLoading"  @click="()=>handleNext('task')"
                        >下一页</a-button
                        >
                    </div>
                </div>
            </div>
        </div>
        <!-- 集群重试\资金号重试\批量重试\批量终止\单一终止 -->
        <revision-config-modal v-if="configInfo.status" :modalInfo="configInfo" @config="setRevisionConfig"/>
        <!-- 查询回库资金账号级别详细日志结果 -->
        <log-table-modal v-if="logTableInfo.status" ref="log-table" :modalInfo="logTableInfo"  @jump-tab="jumpTab" @config-status="handleConfigStatus"/>
    </div>
</template>

<script>
import _ from 'lodash';
import obsTable from '@/components/common/obsTable/obsTable';
import aButton from '@/components/common/button/aButton';
import aLoading from '@/components/common/loading/aLoading';
import infoSumBar from '@/components/common/infoBar/infoSumBar';
import { autoConvertTimeRender, formatPercent, transferVal } from '@/utils/utils';
import { getProductInstances, getProductClusters } from '@/api/productApi';
import revisionConfigModal from '@/components/ldpLogCenter/modal/revisionConfigModal';
import logTableModal from '@/components/ldpLogCenter/modal/logTableModal';
import { getManagerProxy } from '@/api/mcApi';
import importStatusTableIcon from '@/components/secondAppearance/importStatusTableIcon.vue'; // 组件导入

const MODALTYPES = {
    allSuspendTask: [
        {
            key: 'TotalCount',
            title: '全部账户：'
        },
        {
            key: 'ProcessingCount',
            title: '进行中：'
        },
        {
            key: 'SuccessedCount',
            title: '正常完成：'
        },
        {
            key: 'PausedCount',
            title: '终止完成：'
        }
    ]
};

export default {
    name: 'LdpLogCenter',
    props: {
        productInstNo: {
            type: String,
            default: ''
        }
    },
    components: {
        aButton,
        obsTable,
        aLoading,
        infoSumBar,
        revisionConfigModal,
        logTableModal
    },
    data() {
        const formatMethod = (val) => { return { value: transferVal(val) ? Number(val)?.toLocaleString() : '' }; };
        const renderProgress = (h, params) => {
            const percent = params?.row?.TotalCount ? formatPercent(params?.row?.ProcessedCount / params?.row?.TotalCount) : '-';
            const statusEnum = [
                {
                    value: '0',
                    label: '进行中',
                    icon: 'loading'
                },
                {
                    value: '1',
                    label: '正常完成',
                    icon: 'success'
                },
                {
                    value: '2',
                    label: '终止完成',
                    icon: 'error'
                }
            ];
            const resultObj = _.find(statusEnum, [
                'value',
                params?.row?.Status?.toString()
            ]);

            return <div title={resultObj?.label || '-'}>
                <importStatusTableIcon type={resultObj?.icon || ''} />
                <h-poptip autoPlacement trigger="hover" customTransferClassName="apm-poptip monitor-poptip">
                    <span>{percent || '-'}</span>
                    <div slot="content" class="pop-content" style="white-space: normal;">
                        {
                            this.errTaskTableArray.map((item) => {
                                return <p style="padding-bottom: 6px">{
                                    item.map((ele, idx) => {
                                        const result = params.row[ele.key] || '-';
                                        return <span style="display: inline-block;  width: 200px">
                                            <span style={{ paddingLeft: idx ? '20px' : '0' }}
                                            >
                                                {ele.label}：
                                            </span>
                                            <em style="font-style: normal">{result}</em>
                                        </span>;
                                    })
                                }</p>;
                            })
                        }
                    </div>
                </h-poptip>
            </div>;
        };
        return {
            loading: false,
            locateFieldList: [],
            formItems: {
                date: new Date(),
                instance: '',
                clusterName: ''
            },
            // 防止翻页过程中，应用节点等查询条件被清空，从而请求报错
            historyFormItems: {},
            // 回库节点列表
            allTodoInstanceList: [],
            todoInstanceList: [],
            // 核心集群列表
            clusterList: [],
            // 集群结果集
            clusterTableTitle: {
                label: `核心集群回库错误查询`
            },
            clusterLoading: false,
            clusterColumns: [
                {
                    title: '集群名称',
                    key: 'ClusterName',
                    ellipsis: true
                },
                {
                    title: '剩余错误账户数',
                    key: 'BizErrorToResendLocateValueCount',
                    ellipsis: true,
                    minWidth: 140
                },
                {
                    title: '错误账户总数',
                    key: 'BizErrorLocateValueCount',
                    ellipsis: true,
                    minWidth: 140
                },
                {
                    title: '剩余回库错误事务数',
                    key: 'BizErrorToResendTransCount',
                    minWidth: 140,
                    ellipsis: true
                },
                {
                    title: '回库错误事务总数',
                    key: 'BizErrorRspTotalCount',
                    ellipsis: true,
                    minWidth: 120
                },
                {
                    title: '操作',
                    key: 'action',
                    fixed: 'right',
                    width: 180,
                    render: (h, params) => {
                        return h('div', [
                            h('Button',
                                {
                                    props: {
                                        size: 'small',
                                        type: 'text'
                                    },
                                    on: {
                                        click: () => {
                                            this.handleConfigStatus(params?.row, 'clusterBulk');
                                        }
                                    }
                                },
                                '重试'
                            ),
                            h('Button',
                                {
                                    props: {
                                        size: 'small',
                                        type: 'text'
                                    },
                                    on: {
                                        click: () => {
                                            this.handleLogTableVisible(params?.row);
                                        }
                                    }
                                },
                                '账户'
                            ),
                            h('Button',
                                {
                                    props: {
                                        size: 'small',
                                        type: 'text'
                                    },
                                    on: {
                                        click: () => {
                                            this.jumpTab({
                                                instanceId: this.historyFormItems?.instance,
                                                locateField: '',
                                                locateValue: '',
                                                clusterName: params?.row?.ClusterName
                                            });
                                        }
                                    }
                                },
                                '日志'
                            )
                        ]);
                    }
                }
            ],
            clusterTableData: [],
            logTableInfo: {
                status: false
            },
            // 计算表格高度
            clusterTableHeight: 0,
            errTaskTableHeight: 0,
            buttonLoading: false,
            // 表格翻页
            clusterEndMsgNos: [], // 结束操作编号, 翻页使用
            errTaskEndMsgNos: [], // 结束操作编号, 翻页使用
            pageSize: 10,
            // 重试弹窗
            configInfo: {
                status: false,
                title: '',
                headerIcon: 'android-alert',
                headerTitle: '',
                key: 'revision-config',
                contentDic: [],
                contentObj: {},
                cancelText: '取消',
                okText: '确认'
            },
            // 错误重试任务
            infoData: {
                title: {
                    label: '最后重试执行状态',
                    slots: [
                        {
                            type: 'button',
                            buttonType: 'dark',
                            key: 'allClusterBulk',
                            value: '全部重试'
                        },
                        {
                            type: 'button',
                            buttonType: 'dark',
                            key: 'allSuspendTask',
                            value: '全部终止'
                        }
                    ]
                },
                direction: 'row',
                details: [
                    {
                        type: 'text',
                        title: '全部账户',
                        canClick: true,
                        infoId: 'all',
                        info: {
                            key: 'TotalCount',
                            value: '-',
                            formatMethod
                        }
                    },
                    {
                        type: 'text',
                        title: '进行中',
                        canClick: true,
                        infoId: '0',
                        info: {
                            key: 'ProcessingCount',
                            value: '-',
                            formatMethod
                        }
                    },
                    {
                        type: 'text',
                        title: '正常完成',
                        canClick: true,
                        infoId: '1',
                        info: {
                            key: 'SuccessedCount',
                            value: '-',
                            formatMethod
                        }
                    },
                    {
                        type: 'text',
                        title: '终止完成',
                        infoId: '2',
                        canClick: true,
                        info: {
                            key: 'PausedCount',
                            value: '-',
                            formatMethod
                        }
                    }
                ]
            },
            errTaskSum: {},
            selectInfoId: 'all',
            // 集群-资金信息
            errTaskLoading: false,
            errTaskColumns: [
                {
                    title: '集群名称',
                    key: 'ClusterName',
                    ellipsis: true
                },
                {
                    title: '账户',
                    key: 'LocateValue',
                    ellipsis: true
                },
                {
                    title: '需要重试的事务数',
                    key: 'TotalCount',
                    ellipsis: true
                },
                {
                    title: '重试已成功事务数',
                    key: 'ProcessedCount',
                    ellipsis: true
                },
                {
                    title: '执行进度',
                    key: 'ProcessedMsgNo',
                    ellipsis: true,
                    render: (h, params) => renderProgress(h, params)
                },
                {
                    title: '执行总耗时',
                    key: 'SpendTimeMilli',
                    ellipsis: true,
                    render: (h, params) => {
                        return autoConvertTimeRender(h, params, 'SpendTimeMilli', 'ms');
                    }
                }
            ],
            errTaskTableData: [],
            errTaskTableArray: [
                [{
                    key: 'StartMsgNo',
                    label: '开始重试消息序号'
                },
                {
                    key: 'StartTransNo',
                    label: '开始重试事务号'
                }],
                [{
                    key: 'EndMsgNo',
                    label: '结束重试消息号'
                },
                {
                    key: 'EndTransNo',
                    label: '结束的事务号'
                }],
                [{
                    key: 'LastMsgNo',
                    label: '最新重试消息号'
                },
                {
                    key: 'LastTransNo',
                    label: '最新重试的事务号'
                }]
            ],
            timer: null,
            isLock: false
        };
    },
    mounted() {
        this.$nextTick(() => {
            this.fetTableHeight();
        });
        window.addEventListener('resize', this.fetTableHeight);
    },
    beforeDestroy() {
        window.removeEventListener('resize', this.fetTableHeight);
        this.clearPolling();
    },
    computed: {
        instanceData(){
            return this.allTodoInstanceList.find(o => o?.id === this.historyFormItems?.instance);
        }
    },
    methods: {
        // 初始化
        async initData(){
            this.clearData();
            this.clearPolling();
            this.$refs?.['formItems'] && this.$refs['formItems'].resetValidate();
            this.loading = true;
            try {
                await this.getProductInstances();
                await this.getProductClusters();
                await this.getTodoList();

                // 跳转、历史缓存节点和集群不存在
                const id = this.todoInstanceList.find(o => o?.id === this.formItems?.instance)?.id || this.todoInstanceList?.[0]?.id || '';
                const clusters = this.clusterList.map(o => o?.clusterName);
                const clusterName = this.formItems?.clusterName?.split(',')?.filter(c => clusters?.includes(c))?.join(',') || '';
                this.formItems = {
                    ...this.formItems,
                    date: new Date(),
                    instance: id,
                    clusterName: clusterName
                };
                this.$nextTick(() => {
                    this.handleQuery();
                });
            } catch (e){
                console.error(e);
            } finally {
                this.loading = false;
            }
        },
        // 下拉重新获取
        async handleDropChange(stu){
            if (!stu) return;
            await this.getProductInstances();
            await this.getTodoList();
        },
        // 设置table高度
        fetTableHeight() {
            const h1 = this.$refs['table-box'].getBoundingClientRect().height;
            this.clusterTableHeight = h1 * 0.4 - 75;
            this.errTaskTableHeight = h1 * 0.6 - 230;
        },
        // 清空数据
        clearData(){
            this.historyFormItems = {};
            this.clearClusterData();
            this.cleartaskSpanData();
            this.cleartaskData();
        },
        // 清空回库结果集
        clearClusterData(){
            this.clusterTableData = [];
            this.clusterEndMsgNos = [];
        },
        cleartaskSpanData(){
            this.errTaskSum = {};
            this.infoData.details.forEach(o => {
                o.info.value = '';
            });
        },
        cleartaskData(){
            this.errTaskTableData = [];
            this.errTaskEndMsgNos = [];
        },
        // 页面刷新--刷新当前页
        async handleRefresh(){
            await this.getMonitor('polling');
        },
        // 查询回库集群级别日志结果
        handleQuery(){
            // 应用节点ip port影响页面所有管理功能的请求。不存在则清空数据、关闭轮询; 切换tab防止多次请求导致翻页记录错误
            if (!this.formItems.instance){
                this.formItems = {};
                this.clearData();
                this.clearPolling();
                return;
            }
            this.$refs['formItems'].validate(async (valid) => {
                if (valid) {
                    this.isLock = true;
                    this.clearPolling();
                    this.historyFormItems = { ...this.formItems };
                    this.clearClusterData();
                    this.cleartaskSpanData();
                    this.cleartaskData();
                    this.getLocateFieldList();
                    this.getMonitor();
                    this.getBizErrResendSpan();
                    this.$nextTick(() => {
                        this.handleBarClick(this.selectInfoId);
                    });
                    this.isLock = false;
                    this.setPolling();
                }
            });
        },
        // 跳转页处理
        handlePageChange(type, operation) {
            if (type === 'cluster') {
                this.getMonitor(operation);
            } else if (type === 'task') {
                this.getBizErrResendStatus(operation);
            }
        },
        // 上一页
        handlePrev(type){
            this.handlePageChange(type, 'minus');
        },
        // 下一页
        handleNext(type){
            this.handlePageChange(type, 'plus');
        },
        // 跳转日志
        jumpTab(params){
            this.$emit('jump-tab', params);
        },
        // ---------------------------   重试状态  --------------------------------------------------
        // 重试弹窗状态打开
        handleConfigStatus(row, type){
            if (type === 'locateBulk' || type === 'clusterBulk'){
                if (this.errTaskSum?.ProcessingCount) {
                    this.$hMessage.warning('已存在进行中的重试任务！');
                    return;
                };
                this.setConfigInfoStatus(row, type);
                return;
            }
        },
        setConfigInfoStatus(data, key){
            this.configInfo.key = key;
            this.configInfo.contentObj = { ...data };
            if (key === 'allSuspendTask'){
                this.configInfo.status = true;
                this.configInfo.title = '终止任务';
                this.configInfo.headerTitle = '您确认要终止当前正在执行中的重试任务吗？';
                this.configInfo.contentDic = MODALTYPES[key];
                return;
            }

            if (key === 'allClusterBulk'){
                this.configInfo.status = true;
                this.configInfo.title = '创建错误重试任务';
                this.configInfo.headerTitle = '您确认要对所有业务错误账户执行重试处理吗？';
                this.configInfo.contentDic = [];
                return;
            }

            this.setRevisionConfig(this.configInfo);
        },
        // 批量重试、批量终止的弹窗
        handleButtonClick(key){
            if (!this.instanceData?.id) return;
            if (key === 'allClusterBulk') {
                if (!this.clusterTableData?.length) {
                    this.$hMessage.warning('暂无集群需要重试！');
                    return;
                };
                if (this.errTaskSum?.ProcessingCount) {
                    this.$hMessage.warning('已存在进行中的重试任务！');
                    return;
                };
                this.setConfigInfoStatus({}, 'allClusterBulk');
                return;
            }

            if (key === 'allSuspendTask') {
                if (!this.errTaskSum?.ProcessingCount) {
                    this.$hMessage.warning('暂无进行中任务！');
                    return;
                };
                this.setConfigInfoStatus(this.errTaskSum, 'allSuspendTask');
                return;
            }
        },
        // 打开资金账户结果集弹窗
        handleLogTableVisible(row){
            this.logTableInfo.ClusterName = row?.ClusterName;
            this.logTableInfo.instanceId = this.historyFormItems?.instance;
            this.logTableInfo.ip = this.instanceData?.ip;
            this.logTableInfo.manageProxyPort = this.instanceData?.manageProxyPort;
            this.logTableInfo.locateFieldList = [...this.locateFieldList];
            this.logTableInfo.status = true;
        },
        // ---------------------------   汇总  --------------------------------------------------
        // 汇总数据点击事件
        async handleBarClick(id) {
            if (!this.instanceData?.id) return;
            this.cleartaskData();
            this.selectInfoId = id;
            this.getBizErrResendStatus();
        },
        // 设置汇总数据
        setTaskData(){
            this.infoData.details.forEach(o => {
                o.info.value =  this.errTaskSum[o?.info?.key];
            });
        },
        // 启动定时器查询
        setPolling() {
            this.clearPolling();
            this.timer = setInterval(async () => {
                if (this.isLock) return;
                this.isLock = true;
                await this.getBizErrResendSpan();
                await this.getBizErrResendStatus('polling');
                this.isLock = false;
            }, 10000);
        },
        clearPolling(){
            this.timer && clearInterval(this.timer);
        },

        // ---------------------------   管理功能接口  --------------------------------------------------
        // 接口请求------重试接口请求
        async setRevisionConfig(data){
            const key = data?.key;
            let params = {};
            // 资金账号重试
            if (key === 'locateBulk') {
                params = {
                    ResendFlag: true,
                    ClusterName: [data?.contentObj?.ClusterName],
                    LocateInfo: [
                        {
                            LocateField: data?.contentObj?.LocateField,
                            LocateValue: data?.contentObj?.LocateValue
                        }
                    ]
                };
            }
            // 批量重试
            if (key === 'allClusterBulk') {
                params = {
                    ResendFlag: true
                };
            }

            // 集群重试
            if (key === 'clusterBulk') {
                params = {
                    ResendFlag: true,
                    ClusterName: [data?.contentObj?.ClusterName]
                };
            }

            // 批量终止
            if (key === 'allSuspendTask'){
                params = {
                    ResendFlag: true
                };
            }

            // 集群、资金账户重试后需要刷新下表格
            key !== 'allSuspendTask' &&  await this.getForceSendBizErrRedo(params, key);
            key === 'allSuspendTask' &&  await this.getTerminateForceSendBizErrRedo(params);
        },
        // 接口请求------获取回库应用节点列表
        async getProductInstances() {
            const res = await getProductInstances({ productId: this.productInstNo });
            if (res.code === '200') {
                this.allTodoInstanceList = res?.data?.instances?.filter(o => o?.instanceIdentities?.includes('todb') && o?.clusterRole === 'ARB_ACTIVE') || [];
            } else {
                this.allTodoInstanceList = [];
            }
        },
        // 接口请求------获取应用集群信息
        async getProductClusters() {
            const res = await getProductClusters({ productId: this.productInstNo });
            if (res.code === '200') {
                this.clusterList = (res?.data?.appClusters || []).filter(o => o?.clusterInstanceIdentities?.includes('bizproc'));
            } else {
                this.clusterList = [];
            }
        },
        // 接口请求------是否有ldp_todb_v2插件
        async getTodoList(){
            if (!this.allTodoInstanceList?.length) {
                this.todoInstanceList = [];
                return;
            };
            const list = [];
            const param = this.allTodoInstanceList.map(o => {
                return {
                    manageProxyIp: o.ip,
                    manageProxyPort: o.manageProxyPort,
                    pluginName: 'ldp_mproxy',
                    funcName: 'QueryMgrList'
                };
            });
            const res = await getManagerProxy(JSON.stringify(param));
            Array.isArray(this.allTodoInstanceList) && this.allTodoInstanceList.forEach((item, index) => {
                if (res?.code === '200' && res.data?.[index]?.Response?.length) {
                    const data = res?.data?.[index]?.Response?.filter(o =>
                        o?.PluginName === 'ldp_todb' &&
                        ['GetBizErrorMsgInfo', 'GetMsgBodyInfo'].every(v => o?.MgrList.includes(v)))?.[0];
                    data && list.push(item);
                }
            });
            this.todoInstanceList = [...list];
        },
        // 接口请求------查看失败总数、账户总数等信息
        async getMonitor(type) {
            this.clusterLoading = true;
            this.buttonLoading = true;
            let endMsgNo = 0;
            if (type === 'minus'){
                endMsgNo = this.clusterEndMsgNos?.[this.clusterEndMsgNos?.length  - 3] || 0;
            } else if (type === 'polling'){
                endMsgNo = this.clusterEndMsgNos?.[this.clusterEndMsgNos?.length  - 2] || 0;
            } else {
                endMsgNo = this.clusterEndMsgNos?.[this.clusterEndMsgNos?.length - 1] || 0;
            }
            const param = [
                {
                    manageProxyIp: this.instanceData?.ip,
                    manageProxyPort: this.instanceData?.manageProxyPort,
                    pluginName: 'ldp_todb',
                    funcName: 'GetMonitor',
                    params: {
                        ClusterName: this.historyFormItems?.clusterName,
                        StartNo: endMsgNo,
                        Count: this.pageSize
                    }
                }
            ];
            try {
                const res = await getManagerProxy(JSON.stringify(param));
                if (res.code === '200') {
                    if (res?.data?.[0]?.ErrorNo){
                        this.$hMessage.error({
                            content: res.data?.[0]?.ErrorMsg || '查询失败',
                            duration: 2.5
                        });
                        type === 'polling' && this.clearPolling();
                        return;
                    }
                    if (type === 'minus') {
                        this.clusterEndMsgNos.pop();
                    } else if (type !== 'minus' && type !== 'polling') {
                        this.clusterEndMsgNos.push(res.data?.[0]?.NextNo);
                    }
                    this.clusterTableData = res.data?.[0]?.ClusterMonitor || [];
                } else {
                    this.clearPolling();
                }
            } catch (err) {
                console.error(err);
                this.clearPolling();
            } finally {
                this.clusterLoading = false;
                this.buttonLoading = false;
            }
        },
        // 接口请求------错误账户数据重试span字段
        async getBizErrResendSpan() {
            const param = [
                {
                    manageProxyIp: this.instanceData?.ip,
                    manageProxyPort: this.instanceData?.manageProxyPort,
                    pluginName: 'ldp_todb',
                    funcName: 'GetResendStatusStatistics'
                }
            ];
            try {
                const res = await getManagerProxy(JSON.stringify(param));
                if (this.instanceData?.ip !== param?.[0]?.manageProxyIp  || this.instanceData?.manageProxyPort !== param?.[0]?.manageProxyPort) return;
                if (res.code === '200') {
                    this.errTaskSum = res?.data?.[0] || {};
                    this.setTaskData();
                } else {
                    this.clearPolling();
                }
            } catch (err) {
                console.error(err);
                this.clearPolling();
            }
        },
        // 接口请求------错误账户数据重试表格
        // eslint-disable-next-line complexity
        async getBizErrResendStatus(type) {
            this.errTaskLoading = true;
            this.buttonLoading = true;
            let endMsgNo = 0;
            if (type === 'minus'){
                endMsgNo = this.errTaskEndMsgNos?.[this.errTaskEndMsgNos?.length  - 3] || 0;
            } else if (type === 'polling'){
                endMsgNo = this.errTaskEndMsgNos?.[this.errTaskEndMsgNos?.length  - 2] || 0;
            } else {
                endMsgNo = this.errTaskEndMsgNos?.[this.errTaskEndMsgNos?.length - 1] || 0;
            }
            const param = [
                {
                    manageProxyIp: this.instanceData?.ip,
                    manageProxyPort: this.instanceData?.manageProxyPort,
                    pluginName: 'ldp_todb',
                    funcName: 'GetBizErrResendStatus',
                    params: {
                        StartNo: endMsgNo,
                        Count: this.pageSize,
                        Status: this.selectInfoId === 'all' ? '' : Number(this.selectInfoId)
                    }
                }
            ];
            try {
                const res = await getManagerProxy(JSON.stringify(param));

                if (this.instanceData?.ip !== param?.[0]?.manageProxyIp ||
                    this.instanceData?.manageProxyPort !== param?.[0]?.manageProxyPort ||
                    endMsgNo !== param?.[0]?.params?.StartNo ||
                    (this.selectInfoId === 'all' ? '' : Number(this.selectInfoId)) !== param?.[0]?.params?.Status
                ) return;

                if (res.code === '200') {
                    if (res?.data?.[0]?.ErrorNo){
                        this.$hMessage.error({
                            content: res.data?.[0]?.ErrorMsg || '查询失败',
                            duration: 2.5
                        });
                        type === 'polling' && this.clearPolling();
                        return;
                    }
                    if (type === 'minus') {
                        this.errTaskEndMsgNos.pop();
                    } else if (type !== 'minus' && type !== 'polling') {
                        this.errTaskEndMsgNos.push(res.data?.[0]?.NextNo);
                    }
                    this.errTaskTableData = res.data?.[0]?.BizErrResendStatus || [];
                } else {
                    this.clearPolling();
                }
            } catch (err) {
                console.error(err);
                this.clearPolling();
            } finally {
                this.errTaskLoading = false;
                this.buttonLoading = false;
            }
        },
        // 接口请求------重试接口请求
        async getForceSendBizErrRedo(params, key){
            const param = [
                {
                    manageProxyIp: this.instanceData?.ip,
                    manageProxyPort: this.instanceData?.manageProxyPort,
                    pluginName: 'ldp_todb',
                    funcName: 'ForceSendBizErrRedo',
                    method: 'POST',
                    params: {
                        Request: { ...params }
                    }
                }
            ];
            try {
                const res = await getManagerProxy(JSON.stringify(param));
                if (res.code === '200') {
                    if (res?.data?.[0]?.ErrorNo === 0){
                        this.$hMessage.success('创建重试任务成功!');
                        // 手动获取一遍最新数据
                        this.$nextTick(() => {
                            this.getBizErrResendSpan();
                            this.$nextTick(() => {
                                this.handleBarClick(this.selectInfoId);
                            });
                        });

                        // 刷新下集群表格
                        if (key === 'clusterBulk' || key === 'allClusterBulk'){
                            this.handleRefresh();
                        }

                        // 刷新下资金账户表格
                        if (key === 'locateBulk') {
                            this.$refs['log-table'] && this.$refs['log-table'].handleRefresh();
                        }
                    } else {
                        this.$hMessage.error({
                            content: res.data?.[0]?.ErrorMsg || '创建重试任务失败!',
                            duration: 2.5
                        });
                    }
                }
            } catch (err) {
                console.error(err);
            }
        },
        // 接口请求------终止接口
        async getTerminateForceSendBizErrRedo(params){
            const param = [
                {
                    manageProxyIp: this.instanceData?.ip,
                    manageProxyPort: this.instanceData?.manageProxyPort,
                    pluginName: 'ldp_todb',
                    funcName: 'TerminateForceSendBizErrRedo',
                    method: 'POST',
                    params: {
                        Request: { ...params }
                    }
                }
            ];
            try {
                const res = await getManagerProxy(JSON.stringify(param));
                if (res.code === '200') {
                    if (res?.data?.[0]?.ErrorNo === 0){
                        this.$hMessage.success('终止任务成功!');
                        // 手动获取一遍最新数据
                        this.$nextTick(() => {
                            this.getBizErrResendSpan();
                            this.$nextTick(() => {
                                this.handleBarClick(this.selectInfoId);
                            });
                        });
                        // 刷新下集群表格
                        this.handleRefresh();
                    } else {
                        this.$hMessage.error({
                            content: res.data?.[0]?.ErrorMsg || '终止任务失败!',
                            duration: 2.5
                        });
                    }
                }
            } catch (err) {
                console.error(err);
            }
        },
        // localField列表
        async getLocateFieldList(){
            const name = await this.getModuleInfo();
            if (!name) {
                this.locateFieldList = [];
                return;
            };
            const param = [{
                manageProxyIp: this.instanceData?.ip,
                manageProxyPort: this.instanceData?.manageProxyPort,
                pluginName: 'ldp_todb',
                funcName: name + '_GetLocateFieldInfo'
            }];
            const res = await getManagerProxy(JSON.stringify(param));
            if (res?.code === '200'){
                this.locateFieldList = res?.data?.[0]?.LocateField || [];
                // this.locateFieldList = [{
                //     FieldName: 'fund_account',
                //     FieldDesc: '股东代码'
                // }];
            } else {
                this.locateFieldList = [];
            }
        },
        // 用于区分ldp_mdbredo  ldpgwredo
        async getModuleInfo(){
            let name = '';
            const param = [{
                manageProxyIp: this.instanceData?.ip,
                manageProxyPort: this.instanceData?.manageProxyPort,
                pluginName: 'ldp_todb',
                funcName: 'GetModuleInfo'
            }];

            if (!this.instanceData?.ip || !this.instanceData?.manageProxyPort) return '';

            const res = await getManagerProxy(JSON.stringify(param));
            if (res?.code === '200' && Array.isArray(res?.data?.[0]?.ModuleInfo)){
                name =  res?.data?.[0]?.ModuleInfo?.[0]?.Name;
            }

            return name;
        }
    }
};
</script>
<style lang="less" scoped>
@import url("@/assets/css/input.less");

/deep/ .h-input-group {
    border: 1px solid var(--base-color);
    border-radius: 4px;
}

/deep/ .h-input-group > .h-input-group-prepend {
    background-color: var(--input-bg-color);
    color: var(--font-color);
    border: none;
}

/deep/ .h-input-group > .h-input-group-append {
    background-color: var(--input-bg-color);
    color: var(--font-color);
    border: none;
    border-left: 1px solid var(--base-color);
}

/deep/ .h-input-group > .h-input {
    border: none;
}

.error-log-box {
    height: 100%;
    position: relative;
}

.all-table-box {
    height: 100%;
    overflow: auto;
    position: relative;

    /* stylelint-disable-next-line selector-class-pattern */
    /deep/.h-form-item-required .h-form-item-requiredIcon {
        display: none;
    }

    /deep/ .h-form-inline .h-form-item {
        display: inline-block;
        margin-right: 4px;
        vertical-align: top;
        margin-bottom: 0;
    }

    .form-box {
        position: absolute;
        top: 3px;
        right: 6px;
        z-index: 6;
    }

    .split-solid {
        position: relative;
        top: 4px;
        width: 1px;
        background-color: #474e6f;
        height: 26px;
        display: inline-block;
        margin-left: 0;
        margin-right: 3px;
    }
}

.result-box {
    width: 100%;

    .table-box {
        background: var(--wrapper-color);
        padding: 0 0 10px;
    }
}

.record-box {
    width: 100%;
    padding: 0 0 10px;
    background: var(--wrapper-color);
    margin-top: 10px;
}

.obs-table {
    margin-top: 0;
    padding: 0;

    /deep/ .h-table {
        margin: 0;
    }
}

.page-box {
    margin-right: 15px;
    color: var(--font-color);
    text-align: right;
}
</style>
