<template>
    <div>
        <h-drawer v-model="modalData.status" class="helper-drawer" :escClose="true" :mask-closable="true" title="配置说明" width="60" height="75">
            <Markdown :value="value" isPreview copyCode style="user-select: text;" />
        </h-drawer>
    </div>
</template>

<script>
import Markdown from 'vue-meditor';
export default ({
    props: {
        modalInfo: {
            type: Object,
            default: null
        }
    },
    data() {
        return {
            modalData: this.modalInfo,
            value: ` 路由字段配置说明如下：
            \n\n## 功能号：\n
            \n1. 必须配置。可配置多个。
            \n2. 支持英文星号*、英文问号?和数字(不为负值)。多个功能号使用英文分号;分隔。
            \n\n## 目标系统号：\n\
            \n1. 必须配置。可配置多个。
            \n2. 支持英文星号*和数字(不为负值、最大255)。多个功能号使用英文分号;。
            \n\n## 节点号：\n\
            \n1. 选配。
            \n2. 支持英文星号*和数字(不为负值、最大65535)。
            \n\n## 目标系统号：\n\
            \n1. 选配。
            \n2. 必须配置。只能选择1个。
            \n\n## 注意：\n\
            \n1. 英文星号*只能单独存在，不能与其他组合。
            \n例如：“12*”不支持，“12?*”不支持，“*”支持。
            \n2. 英文问号?必须连续且在最后（即问号后面不能再有数字）。一个问号代表一位。
            \n例如：“11???2”不支持，“??111??”不支持，“1111??”支持。
            `
        };
    },
    components: { Markdown }
});
</script>

<style lang="less" scoped>
@import url("@/assets/css/drawer.less");

.helper-drawer {
    /deep/ .markdown,
    /deep/ .markdown-content,
    /deep/ .markdown-preview, {
        background-color: unset !important;
        border: none !important;
    }

    /deep/ .markdown {
        height: auto !important;
    }

    /deep/ .markdown-preview div {
        background: unset !important;

        * {
            color: #fff;
        }
    }
}
</style>

