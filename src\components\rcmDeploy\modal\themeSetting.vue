<!-- eslint-disable vue/no-deprecated-slot-attribute -->
<template>
    <div class="theme-form">
        <h-form ref="themeFormValidate" :model="formValidate" :label-width="160" cols="2">
            <h-form-item label="心跳间隔(毫秒):" prop="heartbeatIntervalMilli" :required="required">
                <h-input v-if="!readOnly" v-model="formValidate.heartbeatIntervalMilli" :specialFilter="true" :specialDecimal="2"></h-input>
                <p v-else>{{ formValidate.heartbeatIntervalMilli }}</p>
            </h-form-item>
            <h-form-item label="心跳超时(毫秒):" prop="heartbeatTimeoutMilli" :required="required">
                <h-input v-if="!readOnly" v-model="formValidate.heartbeatTimeoutMilli" :specialFilter="true" :specialDecimal="2"></h-input>
                <p v-else>{{ formValidate.heartbeatTimeoutMilli }}</p>
            </h-form-item>
            <h-form-item label="ACK间隔(毫秒):" prop="ackIntervalMilli" :required="required">
                <h-input v-if="!readOnly" v-model="formValidate.ackIntervalMilli" :specialFilter="true" :specialDecimal="2"></h-input>
                <p v-else>{{ formValidate.ackIntervalMilli }}</p>
            </h-form-item>
            <h-form-item label="ACK超时(毫秒):" prop="ackTimeoutMilli" :required="required">
                <h-input v-if="!readOnly" v-model="formValidate.ackTimeoutMilli" :specialFilter="true" :specialDecimal="2"></h-input>
                <p v-else>{{ formValidate.ackTimeoutMilli }}</p>
            </h-form-item>
            <h-form-item label="使用共享内存:" :required="required">
                <h-switch v-if="!readOnly" v-model="formValidate.useSharedMemory" size="large">
                    <span slot="open">ON</span>
                    <span slot="close">OFF</span>
                </h-switch>
                <p v-else>{{ formValidate.useSharedMemory ? '是' : '否' }}</p>
            </h-form-item>
            <h-form-item label="是否采用异步发送:" prop="asynchronousRms" required>
                <h-switch v-model="formValidate.asynchronousRms" size="large">
                    <span slot="open">ON</span>
                    <span slot="close">OFF</span>
                </h-switch>
            </h-form-item>
            <h-form-item label="是否为旁路主题:" prop="hasBypass" required>
                <h-switch v-model="formValidate.hasBypass" size="large">
                    <span slot="open">ON</span>
                    <span slot="close">OFF</span>
                </h-switch>
            </h-form-item>
        </h-form>
    </div>
</template>

<script>
export default {
    props: {
        saveValidateData: {
            type: Object,
            default: {}
        },
        readOnly: {
            type: Boolean,
            default: false
        }
    },
    name: 'ThemeSetting',
    data() {
        return {
            formValidate: this.saveValidateData,
            required: true,
            zones: []
        };
    },
    mounted() {
        this.required = !this.readOnly;
    },
    methods: {
        init() {
            this.formValidate = { ...this.saveValidateData };
        },
        getFileData() {
            let data = '';
            this.$refs['themeFormValidate'].validate((valid) => {
                if (valid) {
                    data =  {
                        ...this.formValidate,
                        heartbeatIntervalMilli: Number(this.formValidate.heartbeatIntervalMilli),
                        heartbeatTimeoutMilli: Number(this.formValidate.heartbeatTimeoutMilli),
                        ackIntervalMilli: Number(this.formValidate.ackIntervalMilli),
                        ackTimeoutMilli: Number(this.formValidate.ackTimeoutMilli)
                    };
                }
            });
            return data;
        }
    }
};
</script>
<style lang="less" scoped>
/deep/ .h-switch {
    vertical-align: baseline;
}
</style>
