/*
 * 自定义Table组件样式--尽量使用a-table组件
*/
/deep/ .h-table {
    background: none;
    border-color: var(--table-border-color);
    margin: 10px 0;

    &::before {
        display: none;
    }

    &::after {
        display: none;
    }
}

/deep/ .h-table-wrapper {
    width: 100%;
    height: auto;
    max-height: calc(100% - 50px);
    border: none;
}

/deep/ .h-table th {
    background: var(--primary-color);
    border-bottom-color: #31364a;
    border-right-color: #383c51;
    color: var(--font-color);
    font-size: var(--font-size);
    font-weight: var(--font-weight);
}

/deep/ .h-table td {
    border-bottom: 1px solid #31364a;
    border-right: 0;
    background: var(--input-bg-color);
    color: var(--font-color);
}

/deep/ .h-table-row-checked,
/deep/ .h-table-row-highlight td {
    background: #1f3759 !important;
}

/deep/ .h-table-row-hover,
/deep/ .h-table-row-hover td {
    background: #284871 !important;
}

/deep/ .h-table-row-hover td button {
    color: var(--table-button-hover-bgcolor) !important;
}

/deep/ .h-page {
    & > li {
        background-color: var(--primary-color);
        border-color: var(--border-color);

        & > a {
            color: var(--font-color);
        }
    }
}

/deep/ .h-page-options {
    .h-select .h-select-selection,
    .h-select-selection {
        background-color: var(--main-color);
        border-color: var(--border-color);
        color: var(--font-color);
    }
}

/deep/ .h-page-item-active {
    background-color: var(--link-color) !important;
    border-color: var(--link-color) !important;
}

/deep/ .h-page-options-elevator {
    color: var(--font-color);

    & > input {
        background-color: var(--input-bg-color);
        border-color: var(--border-color);
        color: var(--font-color);
    }
}

/deep/ td .h-btn-text {
    color: var(--link-color);
}

/deep/ .h-table-row-hover .h-btn-text {
    color: var(--font-color);
}

/deep/ td .h-btn-text:hover {
    color: var(--link-color);
    text-decoration: underline;
}

/deep/ .h-table-tiptext {
    width: 100% !important;
}

/deep/ .h-table-wrapper > .h-spin-fix {
    position: absolute;
    top: 0;
    left: 0;
    z-index: 8;
    width: 100%;
    height: 100%;
    border: var(--table-border);
    background-color: var(--input-bg-color);
}

/deep/ .h-table-fixed-body-shadow {
    border: none;
}

/deep/ .h-page-item-jump-next::after,
/deep/ .h-page-tem-jump-prev::after {
    content: "\2022\2022\2022";
}

/deep/ .h-table-fixed-right-patch {
    background-color: var(--input-bg-color);
    border-bottom: var(--table-border);
}
