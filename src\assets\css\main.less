.main {
    display: flex;
    flex-direction: column;
    width: 100vw;
    height: 100vh;
    padding: 8px;
    box-sizing: border-box;
    background: var(--main-color);
    overflow-y: auto;

    .header {
        position: relative;
        width: 100%;
        height: 48px;
        padding: 0 0 0 15px;
        background: var(--primary-color);
        border-radius: var(--border-radius);
        color: var(--font-color);
        font-size: var(--title-font-size);
        line-height: 48px;
    }

    // 容器
    .wrapper {
        width: 100%;
        height: calc(100% - 100px);
        box-sizing: border-box;
        padding: 10px 10px 0;
        margin: 0 auto;
        border-radius: var(--border-radius);
        overflow: hidden;
        background: var(--wrapper-color);
    }

    .wrapper-flex {
        display: flex;
        width: 100%;
        height: calc(100% - 100px);
        box-sizing: border-box;
        margin: 0 auto;
        border-radius: var(--border-radius);
        overflow: hidden;
        background: var(--wrapper-color);
    }

    .main-top {
        width: 100%;
        height: 44px;
        margin: 0 auto;
        padding-left: 20px;
        background: var(--primary-color);
        position: relative;
        overflow: hidden;

        .currenttime,
        p {
            color: var(--font-color);
            font-size: var(--title-font-size);
            line-height: 44px;
            text-align: center;
        }

        .currenttime {
            position: absolute;
            left: 15px;
            line-height: 44px;
        }
    }

    // 标题input
    .securities {
        position: absolute;
        width: auto;
        top: 6px;
        right: 10px;
        min-width: 200px;
    }
}

.clearfix::after {
    content: "";
    display: block;
    visibility: hidden;
    clear: both;
}

::-webkit-scrollbar {
    width: 4px;
}
/* 右下角交汇区域 */
::-webkit-scrollbar-corner {
    background: transparent; /* 或 transparent */
}

.ellipsis {
    overflow: hidden; //超出的文本隐藏
    text-overflow: ellipsis; //溢出用省略号显示
    white-space: nowrap; //溢出不换行
}
