<template>
    <div>
        <h-drawer ref="drawer-box" v-model="modalData.status" title="死信消息" width="60" @on-close="handleDrawerClose">
            <h-spin v-if="loading" fix>
                <h-icon name="load-c" size="18" class="demo-spin-icon-load"></h-icon>
                <div>加载中</div>
            </h-spin>
            <div v-if="tableData.length">
                <div class="time-info">
                    死信消息ID范围：<span class="bold-text">{{beginMsgId}} ~ {{endMsgId}}</span>
                    ，列表最多展示最近 <span class="bold-text">1000</span> 条数据
                </div>
                <div class="msg-info">
                    <h-simple-table
                        :columns="columns"
                        :data="tableData"
                        showTitle
                        :height="tableHeight"
                        class="table-info">
                    </h-simple-table>
                </div>
            </div>
            <no-data v-else isWhite />
        </h-drawer>

        <!-- 查看消息内容 -->
        <mc-msg-content-modal v-if="msgInfo.status" :modalInfo="msgInfo"></mc-msg-content-modal>
    </div>
</template>

<script>
import noData from '@/components/common/noData/noData';
import mcMsgContentModal from '@/components/mcDataObservation/modal/mcMsgContentModal.vue';
import { getMsgBackward, downloadMcMsg } from '@/api/mcApi';
export default {
    name: 'McMsgListDrawer',
    components: { noData, mcMsgContentModal },
    props: {
        modalInfo: {
            type: Object,
            default: null
        }
    },
    data() {
        return {
            modalData: this.modalInfo,
            loading: false,
            columns: [
                {
                    title: '死信消息序号',
                    key: 'msgId',
                    minWidth: 90,
                    ellipsis: true
                },
                {
                    title: '主题',
                    key: 'topicName',
                    ellipsis: true
                },
                {
                    title: '分区',
                    key: 'partitionNo',
                    ellipsis: true
                },
                {
                    title: '生产者',
                    key: 'producerName',
                    ellipsis: true,
                    render: (h, params) => {
                        const producerName = JSON.stringify(params.row.producerName).split('"')[1];
                        return h('div', {
                            attrs: {
                                title: producerName
                            },
                            style: {
                                'word-break': 'keep-all',
                                'white-space': 'nowrap',
                                overflow: 'hidden',
                                'text-overflow': 'ellipsis'
                            }
                        }, [
                            h('span', producerName)
                        ]);
                    }
                },
                {
                    title: '发布序号',
                    key: 'produceMsgId',
                    ellipsis: true
                },
                {
                    title: '发布时间',
                    key: 'publishTime',
                    ellipsis: true
                },
                {
                    title: '消息大小',
                    key: 'msgLen',
                    ellipsis: true
                },
                {
                    title: '过滤条件',
                    key: 'filter',
                    ellipsis: true
                },
                {
                    title: '消息内容',
                    key: 'msg',
                    width: 100,
                    render: (h, params) => {
                        return h('div', [
                            h(
                                'Button',
                                {
                                    props: {
                                        type: 'text',
                                        size: 'small'
                                    },
                                    style: {
                                        color: 'var(--link-color)'
                                    },
                                    on: {
                                        click: () => {
                                            this.showTopicMsgInfo(params?.row);
                                        }
                                    }
                                },
                                '查看'
                            )
                        ]);
                    }
                }
            ],
            tableData: [],
            beginMsgId: '',
            endMsgId: '',
            tableHeight: 0,
            msgInfo: {
                status: false
            }
        };
    },
    mounted() {
        this.handleDrawerOpen();
        window.addEventListener('resize', this.fetTableHeight);
    },
    beforeDestroy() {
        window.removeEventListener('resize', this.fetTableHeight);
    },
    methods: {
        // 打开弹窗
        async handleDrawerOpen() {
            this.loading = true;
            this.fetTableHeight();
            await this.getTableInfo();
            setTimeout(() => {
                this.loading = false;
            }, 200);
        },
        async getTableInfo() {
            this.loading = true;
            try {
                const params = this.modalData.params;
                const res = await getMsgBackward(params);
                if (res.code === '200'){
                    this.tableData = res?.data?.list || [];
                    this.beginMsgId = res?.data?.beginMsgId ?? '-';
                    this.endMsgId = res?.data?.endMsgId ?? '-';
                } else if (res.code.length === 8){
                    this.tableData = [];
                    this.$hMessage.error(res.message);
                }
                this.loading = false;
            } catch (err) {
                this.tableData = [];
                this.loading = false;
            }
        },
        // 消息内容下载
        async handleMcMsgDownload(row) {
            try {
                const paramObj = this.modalData?.params;
                const params = {
                    productId: paramObj?.productId,
                    appClusterId: paramObj?.appClusterId,
                    topicName: paramObj?.topicName,
                    partitionNo: paramObj?.partitionNo,
                    msgId: row?.msgId
                };
                const res = await downloadMcMsg(params);
                const objUrl = window.URL.createObjectURL(new Blob([res], { type: 'text/plain' }));
                // 通过创建a标签实现
                const link = document.createElement('a');
                link.href = objUrl;
                // 对下载的文件命名
                const fileName = `${params.topicName}-${params.partitionNo}-${params.msgId}消息内容.txt`;
                link.setAttribute('download', fileName);
                document.body.appendChild(link);
                link.click();
                document.body.removeChild(link);
                window.URL.revokeObjectURL(objUrl);
            } catch (err) {
                console.error(err);
            }
        },
        // 查看消息详情
        showTopicMsgInfo(row) {
            this.msgInfo.params = {
                msg: row?.msg
            };
            this.msgInfo.status = true;
        },
        // 关闭侧弹窗 清理数据
        handleDrawerClose() {
            this.modalData.status = false;
            this.beginMsgId = '';
            this.endMsgId = '';
            this.columns = [];
            this.tableData = [];
        },
        fetTableHeight() {
            this.tableHeight = this.$refs['drawer-box']?.$el?.offsetTop - 140;
        }
    }
};
</script>

<style lang="less" scoped>
.time-info {
    height: 32px;
    line-height: 32px;
    background: #f7f7f7;
    border-radius: 4px;
    margin-bottom: 10px;
    padding: 0 12px;
    color: #999;

    .bold-text {
        color: #333;
        font-weight: 600;

        & > .h-icon {
            position: relative;
            top: 2px;
        }
    }
}
</style>
