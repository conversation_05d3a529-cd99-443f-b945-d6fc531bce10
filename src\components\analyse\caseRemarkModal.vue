<template>
    <div>
        <!-- 下发配置列表 -->
        <h-msg-box-safe
            v-model="modalData.status"
            :escClose="true"
            :mask-closable="false"
            title="更新实例备注"
            width="70"
            height="60"
        >
            <h-form ref="formValidate" :model="formItems" label-position="left" :label-width="90">
                <h-form-item label="标题" prop="title" required>
                    <h-input v-model="formItems.title" placeholder="请输入标题名称" onkeydown="if(event.keyCode==13){event.keyCode=0;event.returnValue=false;}"></h-input>
                </h-form-item>
                <h-form-item label="内容" prop="context">
                    <div class="markdown">
                        <Markdown v-model="formItems.context" :height="500" :toolbars="options" />
                    </div>
                </h-form-item>
            </h-form>

            <template v-slot:footer>
                <a-button @click="modalData.status = false">取消</a-button>
                <a-button type="primary" :loading="loading" @click="submitConfig">确定</a-button>
            </template>
        </h-msg-box-safe>
    </div>
</template>

<script>
import Markdown from 'vue-meditor';
import aButton from '@/components/common/button/aButton';
import { updateCaseInfo } from '@/api/httpApi';
export default {
    props: {
        modalInfo: {
            type: Object,
            default: null
        }
    },
    data() {
        return {
            modalData: this.modalInfo,
            loading: false,

            options: {
                exportmd: false,
                importmd: false,
                print: false,
                uploadImage: false,
                image: false,
                theme: false
            },
            formItems: {
                ...this.modalInfo.info
            }
        };
    },
    methods: {
        submitConfig() {
            const that = this;
            this.$refs['formValidate'].validate(async (valid) => {
                if (valid) {
                    if (this.formItems.title.length > 20) {
                        this.$hMessage.error('字符长度数不得超过20！');
                        return;
                    }
                    that.loading = true;
                    try {
                        const res = await updateCaseInfo({
                            testCaseInstanceId: this.modalInfo.testCaseInstanceId,
                            remark: { ...this.formItems }
                        });
                        if (res.success) {
                            this.$emit('update', this.modalInfo.testCaseInstanceId);
                            that.$hMessage.success('备注更新成功!');
                            that.modalInfo.status = false;
                        } else {
                            that.loading = false;
                            this.$hMessage.error('备注更新失败!');
                        }
                    } catch (err) {
                        that.loading = false;
                    }
                }
            });
        }
    },
    components: {
        Markdown,
        aButton
    }
};
</script>
