## Button按钮
基于HUI Button封装适用于SEE项目的公共Button组件，分为：apm-button。
http://hui.hundsun.com/

#### Props
| **属性** | **说明** | **类型** | **默认值** |
| --- | --- | --- | --- |
| disabled | 设置按钮为禁用状态 | 设置按钮为禁用状态 | false |
| icon | 设置按钮的图标类型 | String | '' |
| loading | 设置按钮为加载中状态 | Boolean | false |
| long | 开启后，按钮的长度为 **100%** | Boolean | false |
| nativeType | 设置 button 原生的 type，可选值为 button、submit、reset | String | button |
| size | 按钮大小，可选值为 large 和 small 或者不设置 | String | undefined  |
| shape | 按钮形状，可选值为 circle 或者不设置 | String |  undefined  |
| title | 鼠标 hover 到 button 显示 title 文字信息 | String | '' |
| type | 按钮类型，可选值为dark、 primary、ghost、dashed、text、info、success、warning、error、transparent 或者不设置 | String |  undefined    |
| @onClick | 绑定按钮点击事件 | / | / |

#### Methods
| **方法名** | **说明** | **用法/参数** |
| --- | --- | --- |
| click | 按钮点击事件 | / |

#### 代码示例

```vue
<template>
  <div>
    <!-- 按钮类型 -->
	<a-button type="dark">添加</a-button>
    <a-button type="ghost">添加</a-button>
    <a-button type="warning">添加</a-button>
     
    <!-- 按钮形状及图标 -->
    <a-button shape="circle" icon="search">搜索</a-button>
    <a-button icon="search">搜索</a-button>  
      
    <!-- 按钮尺寸 -->
	<a-button type="primary" size="large">large</a-button>
    <a-button type="primary">Default</a-button>
    <a-button type="primary" size="small">small</a-button>
      
     <!-- 加载中 -->
	<a-button type="primary" loading>loading</a-button>
      <a-button type="primary" :loading="loading">loading</a-button> 
      
     <!-- 禁用 -->
	<a-button type="primary" disabled>disabled</a-button> 
	<a-button type="primary" :disabled="disabled">disabled</a-button> 
      
     <!-- 按钮长度 -->
	<a-button type="primary" long>long</a-button> 
      
   	 <!-- 悬浮内容 -->
     <a-button type="primary" title="这是按钮悬浮内容">hovew</a-button> 
      
     <!-- 绑定点击事件 -->
     <a-button type="primary" @onClick="click">click</a-button> 
  </div>
</template>

<script>
import aButton from '@/components/common/button/aButton';
export default {
    data() {
        return {
            loading: true,
            disabled: true,
        }
    }
    methods: {
        click() {
            // ...
        }
    }
    components: { aButton }
};
</script>
```