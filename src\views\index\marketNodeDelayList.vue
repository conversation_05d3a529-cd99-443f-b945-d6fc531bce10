<!--
 * @Description: 行情穿透时延分析
 * @Author: <PERSON><PERSON>
 * @Date: 2022-11-29 13:24:24
 * @LastEditTime: 2023-06-28 13:18:36
 * @LastEditors: <PERSON><PERSON>
-->
<template>
    <div class="main">
        <a-title title="行情产品节点穿透时延分析">
            <slot>
                <h-select v-show="productList.length > 1" v-model="productInstNo" class="title-single-select"
                    placeholder="请选择" :positionFixed="true"
                    :clearable="false" @on-change="checkProduct">
                    <h-option v-for="item in productList" :key="item.id" :value="item.productInstNo">{{
                        item.productName
                    }}</h-option>
                </h-select>
            </slot>
        </a-title>
        <a-loading v-if="loading1" style="top: 55px; height: calc(100% - 55px);" />
        <normal-title-table ref="table" title="汇总查询" :formItems="formItems" :columns="columns" :loading="loading"
            :tableData="tableData" :total="total" @query="handleQuery" />
    </div>
</template>

<script>
import aLoading from '@/components/common/loading/aLoading';
import aTitle from '@/components/common/title/aTitle';
import normalTitleTable from '@/components/common/bestTable/normalTitleTable';
import { mapState, mapActions } from 'vuex';
import { getMarketProductSummary } from '@/api/httpApi';
import { getTraceModels }  from '@/api/productApi';
import { formatDate, cutZero } from '@/utils/utils';
import _ from 'lodash';

export default {
    name: 'MarketNodeDelayList',
    data() {
        return {
            productInstNo: '',
            productInfo: {},
            formItems: [],
            columns: [
                {
                    title: '交易日',
                    key: 'tradeDate',
                    minWidth: 100
                }, {
                    title: '最小(min)',
                    key: 'min'
                }, {
                    title: '1分位数(p1)',
                    key: 'p1'
                }, {
                    title: '中位数(p50)',
                    key: 'p50'
                }, {
                    title: '平均数(avg)',
                    key: 'avg'
                }, {
                    title: '95分位数(p95)',
                    key: 'p95',
                    minWidth: 110
                }, {
                    title: '99分位数(p99)',
                    key: 'p99',
                    minWidthidth: 110
                }, {
                    title: '最大(max)',
                    key: 'max'
                }, {
                    title: '统计总数',
                    key: 'total'
                }
            ],
            tableData: [],
            total: 0,
            loading: false,
            loading1: false
        };
    },
    async mounted() {
        await this.init();
    },
    methods: {
        ...mapActions({ getProductList: 'product/getProductListLight' }),
        async init() {
            this.loading1 = true;
            try {
                await this.getProductList({ filter: 'supportMarketLatency' });
                const productInstNo = localStorage.getItem('productInstNo');
                this.productInstNo = _.find(this.productList, ['productInstNo', productInstNo])?.productInstNo || this.productList?.[0]?.productInstNo;
            } catch (e) {
                console.error(e);
            } finally {
                this.loading1 = false;
            }
        },
        // 获取应用节点列表
        async getTraceModels() {
            try {
                const res = await getTraceModels({ productId: this.productInfo.id });
                if (res.code === '200') {
                    const formItems = res?.data?.[0]?.bizTags || [];
                    this.formItems = [
                        {
                            type: 'daterange',
                            key: 'dateRange',
                            label: '交易日期',
                            value: [new Date(), new Date()],
                            required: true
                        },
                        {
                            type: 'timerange',
                            key: 'timeRange',
                            label: '交易时间',
                            value: ['09:15:00', '15:00:00'],
                            required: true,
                            placement: 'bottom-end'
                        },
                        ...formItems
                    ];
                }
            } catch (e){
                this.formItems = [];
            }
        },
        // 查询产品详细信息
        async checkProduct(val) {
            this.productInfo = val ? _.find(this.productList, ['productInstNo', val]) : this.productList[0];
            localStorage.setItem('productInstNo', this.productInfo.productInstNo);
            this.loading1 = true;
            await this.getTraceModels();
            this.loading1 = false;
            this.$nextTick(async () => {
                await this.$refs['table'] && this.$refs['table'].$_handleResetPageDataAndQuery();
            });
        },
        // 汇总查询
        async handleSumQuery(val) {
            try {
                const param = {
                    productInstNo: this.productInstNo,
                    exchangeId: val?.exchangeId || '',
                    startDate: val.dateRange ? formatDate(val.dateRange[0]) : '',
                    endDate: val.dateRange ? formatDate(val.dateRange[1]) : '',
                    startTime: val.timeRange[0] || '',
                    endTime: val.timeRange[1] || '',
                    interval: 1000,
                    marketDataType: val?.marketDataType || '',
                    indicators: ['avg', 'min', 'p1', 'p50', 'p95', 'p99', 'max'], // 指标列表
                    instanceType: val.instanceType || '',
                    instanceName: val.instanceName === 'all' ? '' : val.instanceName,
                    page: val.page,
                    pageSize: val.pageSize
                };
                this.loading = true;
                const res = await getMarketProductSummary(param);
                this.loading = false;
                if (res.success) {
                    this.tableData = res.data.list || [];
                    this.tableData.forEach(item => {
                        Object.keys(item).forEach(ele => {
                            if (ele !== 'total' && ele !== 'tradeDate') item[ele] = cutZero((Number(item[ele]) / 1000 || 0).toFixed(3)) + 'μs';
                        });
                    });
                    this.total = res.data.totalCount;
                } else {
                    this.$hMessage.success('查询失败!');
                }
            } catch (err) {
                this.loading = false;
                this.$hMessage.error(err.message);
                this.tableData = [];
                console.log(err);
            }
        },
        async handleQuery(val) {
            await this.handleSumQuery(val);
            // 更新表格列和侧边栏多选项数据
            this.$refs['table'].$_init();
        }
    },
    computed: {
        ...mapState({
            productList: state => {
                return state.product.productListLight;
            }
        })
    },
    components: { aLoading, aTitle, normalTitleTable }
};
</script>
<style lang="less" scoped>

.main {
    width: 100%;
    height: 100%;
    overflow: hidden;
}
</style>
