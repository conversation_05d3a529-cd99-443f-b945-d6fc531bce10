<template>
    <div v-if="rcmId" class="rem-overview">
        <div ref="rcm-content" class="rem-overview-content">
            <!-- 物料生成范围 -->
            <a-title title="物料生成范围">
                <slot>
                    <a-button type="dark" class="btn-add" @click="updateMaterialModal">配置</a-button>
                </slot>
            </a-title>
            <div v-if="rcmInfo.materialRange && Object.keys(rcmInfo.materialRange).length" class="product-info">
                <p><span>传输地址：</span>{{rcmInfo.materialRange.multicastAddr.minValue}} - {{rcmInfo.materialRange.multicastAddr.maxValue}}</p>
                <p><span>传输端口：</span>{{rcmInfo.materialRange.localPort.minValue}} - {{rcmInfo.materialRange.localPort.maxValue}}</p>
                <p><span>集群同步地址：</span>{{rcmInfo.materialRange.syncAddr.minValue}} - {{rcmInfo.materialRange.syncAddr.maxValue}}</p>
                <p><span>集群同步端口：</span>{{rcmInfo.materialRange.syncPort.minValue}} - {{rcmInfo.materialRange.syncPort.maxValue}}</p>
                <p><span>补缺端口：</span>{{rcmInfo.materialRange.multicastPort.minValue}} - {{rcmInfo.materialRange.multicastPort.maxValue}}</p>
                <p><span>上下文ID范围：</span>{{rcmInfo.materialRange.contextId.minValue}} - {{rcmInfo.materialRange.contextId.maxValue}}</p>
                <p><span>主题ID范围：</span>{{rcmInfo.materialRange.transportId.minValue}} - {{rcmInfo.materialRange.transportId.maxValue}}</p>
                <p><span>分区范围：</span>{{rcmInfo.materialRange.partitionNo.minValue}} - {{rcmInfo.materialRange.partitionNo.maxValue}}</p>
            </div>
            <no-data v-else style="height: 150px;"></no-data>

            <!-- 监控服务 -->
            <a-title title="监控服务">
                <slot>
                    <a-button type="dark" class="btn-add" @click="updateLdpAdmin('add')">新增</a-button>
                </slot>
            </a-title>
            <a-table
                ref="table"
                :tableData="tableData"
                :loading="loading"
                :columns="columns"
                :hasPage="false"
                showTitle >
            </a-table>

        </div>
        <a-loading v-if="loading"></a-loading>

        <!-- 修改物料生成范围信息 -->
        <update-rcm-material-modal v-if="materialRangeInfo.status" :modalInfo="materialRangeInfo"
            @update="getRcmOverview">
        </update-rcm-material-modal>

        <!-- 修改ldpadmin信息弹窗 -->
        <updata-ldpadmin-modal v-if="ldpAdminInfo.status" :modalInfo="ldpAdminInfo"
            @update="getRcmOverview"/>
    </div>
</template>

<script>
import { getRcmOverview, deleteldpAdminConfig } from '@/api/rcmApi';
import aTable from '@/components/common/table/aTable';
import aTitle from '@/components/common/title/aTitle';
import aButton from '@/components/common/button/aButton';
import aLoading from '@/components/common/loading/aLoading';
import noData from '@/components/common/noData/noData';
import updateRcmMaterialModal from '@/components/rcmDeploy/modal/overview/updateRcmMaterialModal';
import updataLdpadminModal from '@/components/rcmDeploy/modal/overview/updataLdpadminModal';

export default {
    name: 'RcmOverview',
    components: { aTitle, aButton, aTable, aLoading, noData, updateRcmMaterialModal, updataLdpadminModal },
    props: {
        rcmId: {
            type: String,
            default: ''
        }
    },
    data() {
        return {
            // ldpadmin信息
            columns: [
                {
                    title: '地址',
                    key: 'addr',
                    ellipsis: true
                },
                {
                    title: '端口',
                    key: 'port',
                    ellipsis: true
                },
                {
                    title: '操作',
                    key: 'action',
                    fixed: 'right',
                    width: 110,
                    render: (h, params) => {
                        return h('div', [
                            h('Button',
                                {
                                    props: {
                                        size: 'small',
                                        type: 'text'
                                    },
                                    on: {
                                        click: () => { this.updateLdpAdmin('edit', params.row); }
                                    }
                                },
                                '修改'
                            ),
                            h('Button',
                                {
                                    props: {
                                        size: 'small',
                                        type: 'text'
                                    },
                                    on: {
                                        click: () => {
                                            this.$hMsgBoxSafe.confirm({
                                                title: `删除`,
                                                content: `您确认要删除该行配置吗？`,
                                                onOk: async () => {
                                                    this.deleteLdpAdmin(params.row.id);
                                                }
                                            });
                                        }
                                    }
                                },
                                '删除'
                            )
                        ]);
                    }
                }
            ],
            tableData: [],

            // 弹窗
            rcmInfo: {
                materialRange: {},
                topicStatistics: {},
                singletonContextStatistics: {},
                clusterContextStatistics: {},
                rcmTemplateStatistics: {}
            },
            manageRcmInfo: {
                status: false,
                rcmId: '',
                productInstName: '',
                publishInfo: ''
            },
            materialRangeInfo: {
                status: false,
                rcmId: '',
                materialRange: {}
            },
            ldpAdminInfo: {
                status: false,
                addr: '',
                port: '',
                defaultInfo: null
            },
            loading: false
        };
    },
    methods: {
        // 初始化总览页面
        initData() {
            this.getRcmOverview();
        },
        // 获取rcm产品实例总览基础信息
        getRcmOverview() {
            // 清理总览数据
            this.rcmInfo = {
                materialRange: {},
                topicStatistics: {},
                singletonContextStatistics: {},
                clusterContextStatistics: {},
                rcmTemplateStatistics: {}
            };
            this.loading = true;
            const param = {
                id: this.rcmId
            };
            getRcmOverview(param).then(res => {
                if (res.code === '200' && this.rcmId === param.id) {
                    if (res.data && Object.keys(res.data).length){
                        this.rcmInfo = res.data;
                        this.tableData = res.data?.rcmManageConfig?.servers || [];
                    }
                }
            }).finally(() => {
                this.loading = false;
            });
        },
        // 更新rcm物料范围信息
        updateMaterialModal() {
            // 开启弹窗修改
            this.materialRangeInfo.materialRange = this.rcmInfo.materialRange;
            this.materialRangeInfo.rcmId = this.rcmId;
            this.materialRangeInfo.status = true;
        },
        // 更新ldpadmin信息
        async updateLdpAdmin(type, row) {
            this.ldpAdminInfo.rcmId = this.rcmId;
            this.ldpAdminInfo.type = type;
            this.ldpAdminInfo.id = row?.id;
            this.ldpAdminInfo.addr = row?.addr || '';
            this.ldpAdminInfo.port = row?.port || '';
            this.ldpAdminInfo.status = true;
        },
        // 删除ldpadmin信息
        async deleteLdpAdmin(id) {
            const res = await deleteldpAdminConfig(
                { rcmId: this.rcmId, id }
            );
            if (res.code === '200') {
                this.initData();
                this.$hMessage.success({
                    content: `删除成功`,
                    duration: 3
                });
            }
        }
    }
};
</script>

<style lang="less" scoped>
@import url("@/assets/css/input.less");

.rem-overview {
    position: relative;
    width: 100%;
    height: 100%;
    overflow: hidden;

    .rem-overview-content {
        width: 100%;
        height: 100%;
        overflow: auto;

        .btn-add {
            position: absolute;
            right: 6px;
            top: 5px;
        }

        .product-info {
            flex: 1;
            width: 100%;
            height: auto;
            padding: 10px 0 20px;
            word-wrap: break-word;

            & > p {
                display: inline-block;
                color: var(--font-color);
                padding-top: 10px;
                padding-right: 30px;
                line-height: 15px;

                & > span {
                    padding-left: 13px;
                    color: var(--font-opacity-color);
                }
            }

            .edit-icon {
                cursor: pointer;
            }
        }

        .rcm-info-group {
            display: flex;
            flex-direction: row;

            .rcm-info {
                flex: 1;
                margin: 5px;
                padding: 0 0 20px;

                & > p {
                    color: var(--font-color);
                    padding-top: 20px;
                    line-height: 15px;

                    & > span {
                        padding-left: 13px;
                        color: var(--font-opacity-color);
                    }
                }

                & > .apm-title {
                    white-space: nowrap;
                    overflow: hidden;
                    text-overflow: ellipsis;
                }
            }
        }

        .context-instance-list {
            color: var(--font-color);

            .context-list {
                margin: 10px;
                display: flex;
                height: 100px;

                .classify-label {
                    width: 200px;
                    background: var(--primary-color);
                    text-align: center;
                    padding: 10px;
                    margin-right: 10px;
                    border-radius: 5px;

                    & > .h-row {
                        padding: 3px 0;
                        white-space: nowrap;
                        overflow: hidden;
                        text-overflow: ellipsis;

                        .h-col {
                            white-space: nowrap;
                            overflow: hidden;
                            text-overflow: ellipsis;
                        }
                    }

                    & > .list-title {
                        margin-bottom: 8px;
                        white-space: nowrap;
                        overflow: hidden;
                        text-overflow: ellipsis;
                    }
                }

                .context-instances {
                    position: relative;
                    flex: 1;
                    height: 100px;
                    background: var(--primary-color);
                    padding: 10px;
                    border-radius: 5px;

                    .instances {
                        display: inline-block;
                        width: calc(100% - 100px);
                        height: 100%;
                        padding: 0 10px 0 0;
                        border-right: 1px solid var(--base-color);
                        overflow: hidden;

                        .ctx-image {
                            display: inline-block;
                            filter: blur(0);
                            width: 36px;
                            height: 36px;
                            margin: 1px 3px;

                            &:hover {
                                cursor: pointer;
                                content: url("static/selectCtx.png");
                            }
                        }

                        & > span {
                            position: absolute;
                            right: 140px;
                            top: 65px;
                            color: var(--link-color);

                            &:hover {
                                cursor: pointer;
                                color: #54e8fc;
                            }
                        }
                    }

                    .button {
                        position: absolute;
                        right: 15px;
                        top: 50%;
                        transform: translateY(-50%);
                    }
                }
            }
        }
    }
}
</style>
