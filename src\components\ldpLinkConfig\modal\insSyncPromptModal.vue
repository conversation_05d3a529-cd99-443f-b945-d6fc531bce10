 <!--
    * @Description: 应用节点同步提示弹窗
-->
<template>
    <div>
        <h-msg-box-safe
            v-model="modalData.status"
            :closable="false"
            :mask-closable="false"
            :width="420">
            <h-spin v-if="loading" fix>
                <h-icon name="load-c" size="18" class="demo-spin-icon-load"></h-icon>
                <div>正在识别节点信息，请稍等......</div>
            </h-spin>

            <template v-slot:header>
                <div v-if="identifySuccess" class="header-info">
                    <h-icon
                        name="prompt"
                        color="var(--link-color)"
                        size=24>
                    </h-icon>
                    <span class="title-info">确定要同步节点信息吗？</span>
                </div>
                <div v-else class="header-info">
                    <h-icon
                        name="error"
                        color="#F5222D"
                        size=16>
                    </h-icon>
                    <span class="title-info">识别失败</span>
                </div>
            </template>

            <div v-if="identifySuccess" class="body-content">
                <div class="body-text">
                    识别到运行中的LDP应用节点与当前本地应用节点存在以下差异，请确定是否要同步：
                </div>
                <div class="statistic">
                    <span>删除：{{ insStatistics.deleteInstanceCount }}</span>
                    <span>新增：{{ insStatistics.addInstanceCount }}</span>
                    <span>更新：{{ insStatistics.updateInstanceCount }}</span>
                    <span v-if="insStatistics.failedInstanceCount">
                        识别失败：{{ insStatistics.failedInstanceCount }}
                    </span>
                    <a-button
                        v-if="mainBtnText !== '查看详情'"
                        type="text"
                        class="text-button"
                        @click="handleSubmitBtn('查看详情')">
                        查看详情
                    </a-button>
                </div>
            </div>
            <div v-else>
                <div class="body-text">
                    {{ failedContent || '节点识别超时或失败，请重试!' }}
                </div>
            </div>

            <template v-slot:footer>
                <div v-if="identifySuccess">
                    <a-button
                        @click="modalData.status = false"
                        >取消
                    </a-button>
                    <a-button
                        type="primary"
                        :loading="btnLoading"
                        @click="handleSubmitBtn(mainBtnText)">
                        {{ mainBtnText }}
                    </a-button>
                </div>
                <div v-else>
                    <a-button
                        @click="modalData.status = false"
                        >关闭
                    </a-button>
                    <a-button
                        type="primary"
                        :loading="btnLoading"
                        @click="getSyncData">
                        重试
                    </a-button>
                </div>
            </template>
        </h-msg-box-safe>
    </div>
</template>

<script>
import aButton from '@/components/common/button/aButton';
import { getZkProductInstances, updateBatchAppInstance } from '@/api/productApi';
export default {
    name: 'InsSyncPromptModal',
    components: { aButton },
    props: {
        modalInfo: {
            type: Object,
            default: null
        },
        productInfo: {
            type: Object
        }
    },
    data() {
        return {
            modalData: this.modalInfo,
            mainBtnText: '确定',
            insStatistics: {},
            loading: false,
            btnLoading: false,
            identifySuccess: true,
            failedContent: ''
        };
    },
    mounted() {
        this.getSyncData();
    },
    methods: {
        // 获取同步信息
        getSyncData() {
            this.loading = true;
            this.failedContent = '';
            this.insStatistics = {};

            const param = {
                productId: this.productInfo.id
            };
            getZkProductInstances(param).then(res => {
                if (res.code === '200') {
                    this.identifySuccess = true;

                    this.insStatistics = res?.data || {};
                    // 成功时-主按钮内容
                    if (this.insStatistics?.deleteInstanceCount > 0 || this.insStatistics?.failedInstanceCount > 0) {
                        this.mainBtnText = '查看详情';
                    } else {
                        this.mainBtnText = '确定';
                    }
                } else if (res.code?.length === 8) {
                    this.failedContent = res.message;
                    this.identifySuccess = false;
                }
            }).catch(() => {
                this.identifySuccess = false;
            }).finally(() => {
                this.loading = false;
            });
        },
        // 确认同步 / 查看详情
        handleSubmitBtn(type) {
            if (type === '确定') {
                // 同步节点 - 无删除和未识别可直接同步
                // 传参包含 新增、更新、无变化应用节点
                const insData = [
                    ...(this.insStatistics?.addInstances || []),
                    ...(this.insStatistics?.updateInstances || []),
                    ...(this.insStatistics?.noChangeInstances || [])
                ];
                this.syncInstanceData(insData);

            } else if (type === '查看详情') {
                this.$emit('view-detail', this.insStatistics);
                this.modalInfo.status = false;
            }
        },
        // 同步节点接口
        async syncInstanceData(data) {
            this.btnLoading = true;
            try {
                const params = {
                    productId: this.productInfo.id,
                    instances: data
                };
                const res = await updateBatchAppInstance(params);
                if (res.code === '200') {
                    this.$emit('update', this.modalData.productId);
                    this.$hMessage.success('应用节点信息列表已更新！');
                    this.modalInfo.status = false;
                } else if (res.code.length === 8) {
                    this.$hMessage.error({
                        content: res.message,
                        duration: 3
                    });
                }
            } finally {
                this.btnLoading = false;
            }
        }
    }
};
</script>

<style lang="less" scoped>
.header-info {
    font-weight: 500;
    display: flex;
    align-items: center;

    & > .h-icon {
        position: relative;
    }

    .title-info {
        line-height: 30px;
        font-size: 14px;
        font-weight: 600;
        vertical-align: top;
        padding: 0 5px;
    }
}

.body-content {
    .statistic {
        span {
            padding-right: 10px;
        }

        .text-button {
            color: var(--link-color);
            padding: 6px;
        }
    }
}

/deep/ .h-modal-body {
    padding: 10px 32px 20px 45px;
}

.demo-spin-icon-load {
    animation: ani-demo-spin 1s linear infinite;
    display: inline-block;
}

@keyframes ani-demo-spin {
    from {
        transform: rotate(0deg);
    }

    50% {
        transform: rotate(180deg);
    }

    to {
        transform: rotate(360deg);
    }
}
</style>
