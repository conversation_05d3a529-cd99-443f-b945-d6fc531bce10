export const COLUMNS = [
    {
        title: '发送端上下文',
        key: 'sendContextName',
        ellipsis: true,
        align: 'left',
        headerTooltip: true,
        minWidth: 130
    },
    {
        title: '缓存积压消息数',
        key: 'sendCacheBacklogMsgSize',
        ellipsis: true,
        align: 'right',
        headerTooltip: true,
        minWidth: 120
    },
    {
        title: '网络积压消息数',
        key: 'sendNetBacklogMsgSize',
        ellipsis: true,
        align: 'right',
        headerTooltip: true,
        minWidth: 120
    },
    {
        title: '已接收应答消息序号',
        key: 'sendLastAckMsgNo',
        ellipsis: true,
        align: 'right',
        headerTooltip: true,
        minWidth: 150
    },
    {
        title: '主题分区',
        key: 'topicPartition',
        ellipsis: true,
        align: 'center',
        headerTooltip: true,
        minWidth: 100,
        render: (h, params) => {
            const text = `${params?.row?.topicName}#${params?.row?.partitionNo}`;
            return h('span', {
                attrs: {
                    title: text
                }
            }, text);
        }
    },
    {
        title: '接收端上下文',
        key: 'receiveContextName',
        ellipsis: true,
        align: 'left',
        headerTooltip: true,
        minWidth: 130
    },
    {
        title: '缓存积压消息数',
        key: 'receiveCacheBacklogMsgSize',
        ellipsis: true,
        align: 'right',
        headerTooltip: true,
        minWidth: 120
    },
    {
        title: '最慢Acker',
        key: 'acker',
        ellipsis: true,
        align: 'center',
        headerTooltip: true,
        minWidth: 100
    },
    {
        title: '已应答消息序号',
        key: 'receiveNextMsgNo',
        ellipsis: true,
        align: 'right',
        headerTooltip: true,
        minWidth: 120
    }
];

export const TARGET_OPTIONS = [
    {
        value: 'sendCacheBacklogMsgSize',
        label: '按缓存积压消息数排序'
    },
    {
        value: 'sendNetBacklogMsgSize',
        label: '按网络积压消息数排序'
    }
];

export const TOPN_OPTIONS = [
    {
        value: 10,
        label: 'Top10'
    },
    {
        value: 30,
        label: 'Top30'
    },
    {
        value: 50,
        label: 'Top50'
    }
];
