/*
 * @Description: 集群拓扑
 * @Author: <PERSON><PERSON>
 * @Date: 2022-08-15 10:41:08
 * @LastEditTime: 2024-07-17 11:08:05
 * @LastEditors: yingzx38608 <EMAIL>
 */
import { getTopoInstanceNode, getTopoSessions } from '@/api/topoApi';
import { edgeStyle, nodeStyle } from '@/config/rcmDefaultConfig';
import connectivityDrawer from '@/components/ldpMonitor/modal/connectivityDrawer.vue';
import * as echarts from 'echarts';
import _ from 'lodash';
import './topo.less';
let myChart = null;
// 上下文角色字典
const CTX_ROLE_HOST = ['SINGLE', 'LEADER', 'ACTIVE'];
const CTX_ROLE_STANDBY = ['INIT', 'SYNC', 'FOLLOWER', 'INACTIVE'];

export default {
    name: 'ldpNodeTopo',
    components: { connectivityDrawer },
    props: {
        productId: {
            type: String,
            default: ''
        },
        type: {
            type: String,
            default: ''
        },
        rcmId: {
            type: String,
            default: ''
        },
        rcmName: {
            type: String,
            default: ''
        },
        nodes: {
            type: Array,
            default: () => []
        },
        edges: {
            type: Array,
            default: () => []
        },
        queryParam: {
            type: Object,
            default: () => {}
        }
    },
    data() {
        return {
            drawerInfo: {
                status: false
            }
        };
    },
    computed: {
        appWorkStatusDict() {
            return this.$store?.state?.apmDirDesc?.appWorkStatusDict || {};
        },
        connectStatusDict() {
            return this.$store?.state?.apmDirDesc?.connectStatusDict || {};
        }
    },
    mounted() {
        // 查询节点
        window.handleSearchNode = (id) => {
            const arg = {
                nodeIds: this.type === 'context' ? id : [id]
            };
            this.$hCore.trigger('topo-jump-global-event', {
                type: 'nodeType',
                arg
            });
        };
        // 跳转上下文配置
        window.rcmConfigGoLink = (url, contextName) => {
            const query = {
                rcmId: this.rcmId,
                activeTabName: 'rcmDeployContext',
                contextName
            };
            this.$hCore.navigate(url, { history: true }, query);
        };

        window.topoRcmObsGoLink = (title, syncMode) => {
            if (!title || syncMode !== 0) return;
            this.$emit('rcmObsGoLink', title);
        };

        window.clusterGoLink = (url, id) => {
            const query = {
                instanceId: id
            };
            this.$hCore.navigate(url, { history: true }, query);
        };

        window.getMonitorEvents = (id) => {
            this.$emit('getMonitorEvents', id, this.type);
        };

        window.searchContextLinks = (context) => {
            this.$emit('searchContextLinks', context);
        };
        // 打开上下文运行指标
        window.openManageInfo = (name, mode) => {
            this.$emit('handleOpenManageInfo', name, mode);
        };
        window.addEventListener('resize', this.resize, false);
        myChart = echarts.init(this.$refs.chart, '#262B40');
    },
    methods: {
        init() {
            const nodes = [], links = [], categories = [];
            this.nodes.forEach(ele => {
                const stuText = this.appWorkStatusDict[ele.target.status] || ele.target.status;
                nodes.push({
                    id: ele.id,
                    name: ele.name,
                    symbolSize: 20,
                    category: stuText,
                    info: ele.target,
                    itemStyle: {
                        color: nodeStyle[ele.target.status]
                    }
                });
                if (categories.indexOf(stuText) === -1) {
                    categories.push({
                        name: stuText,
                        itemStyle: {
                            color: nodeStyle[ele.target.status]
                        }
                    });
                }
            });
            this.edges.forEach(ele => {
                links.push({
                    source: ele.fromId,
                    target: ele.toId,
                    lineStyle: {
                        color: edgeStyle[ele.target.status]?.color,
                        type: edgeStyle[ele.target.status]?.type
                    },
                    symbol: ['none', this.type === 'service' ? 'none' : ele.fromId === ele.toId ? 'none' : 'arrow'],
                    info: ele.targets || []
                });
            });
            const graph = {
                links,
                nodes,
                categories
            };
            const that = this;

            const option = {
                tooltip: {
                    trigger: 'item',
                    enterable: true,
                    confine: true,
                    backgroundColor: 'rgba(88,94,106,0.70)',
                    textStyle: {
                        color: '#fff'
                    },
                    triggerOn: 'click',
                    formatter: function(params, ticket, callback) {
                        let html = '';
                        if (params.dataType === 'node') {
                            if (that.type === 'service') {
                                that.getAppInfo(params.data?.id, function(content) {
                                    const { data } = content;
                                    const liStyle = 'white-space: normal; padding: 0 0 8px 8px; line-height: 12px;';
                                    const titleStyle = 'padding-right: 10px; line-height: 12px; border-right: 1px solid rgba(255, 255, 255, 0.15); font-weight: bold;';

                                    const isDataImportEnabled = data?.operateInfo?.dataImport;
                                    const isDataSyncdEnabled = data?.operateInfo?.dataSync;

                                    html += `<div style="font-size: 12px; user-select: text;">
                                                    <div style="display: flex; justify-content: space-between; border-bottom: 1px solid #ccc; padding-bottom: 4px; font-weight: 500;">
                                                        <span>${params.data.name}</span>
                                                        <span style="cursor: pointer;" onmouseover="this.style.color='#298dff';" onmouseout="this.style.color='#fff';" onclick="getMonitorEvents('${data?.baseInfo?.serviceCode}')" >告警：${data?.runningInfo?.alertNumber ?? '-'}</span>
                                                    </div>
                                                    <div>
                                                        <div style="display: flex; margin-top: 10px;">
                                                            <div style='${titleStyle}'>基础信息:</div>
                                                            <ul>
                                                                <li style='${liStyle}'><span style="color: #cacfd4;">服务名: </span> ${params.data.name || '-'}</li>
                                                                <li style='${liStyle}'><span style="color: #cacfd4;">下一层集群个数: </span> ${data?.baseInfo?.clusterNum || '-'}</li>
                                                                <li style='${liStyle} padding-bottom: 0;'><span style="color: #cacfd4;">分片数:</span> ${data?.baseInfo?.shardingNum || '-'}</li>
                                                            </ul>
                                                        </div>`;
                                    if (isDataImportEnabled || isDataSyncdEnabled) {
                                        // 添加快捷导航部分
                                        html += `<div style="display: flex; margin-top: 10px;">
                                                            <div style='${titleStyle}'>快捷导航:</div>
                                                            <ul>`;

                                        if (isDataImportEnabled) {
                                            html += `<li style='${liStyle}'><span style="color: #cacfd4;">核心数据上场:</span> <span onclick="clusterGoLink('/accordMonitor', '${data?.baseInfo?.id}')" style="color: #2d8de5; cursor: pointer;">查看</span></li>`;
                                        }

                                        if (isDataSyncdEnabled) {
                                            html += `<li style='${liStyle}'><span style="color: #cacfd4;">核心数据同步:</span> <span onclick="clusterGoLink('/accordObservation', '${data?.baseInfo?.id}')" style="color: #2d8de5; cursor: pointer;">查看</span></li>`;
                                        }
                                        html += `</ul>
                                                            </div>`;
                                    }

                                    html += `   </div>
                                                </div>`;
                                    callback(ticket, html);
                                });
                            } else if (that.type === 'cluster') {
                                that.getAppInfo(params.data?.id, function(content) {
                                    const { data } = content;
                                    const liStyle = 'white-space: normal; padding: 0 0 8px 8px; line-height: 12px;';
                                    const titleStyle = 'padding-right: 10px; line-height: 12px; border-right: 1px solid rgba(255, 255, 255, 0.15); font-weight: bold;';
                                    // 集群下的应用节点
                                    let quickFeature = '';
                                    quickFeature = `<div style="display: flex; margin-top: 10px;">
                                    <div style='${titleStyle}'>集群成员:</div>
                                    <table cellspacing="0" align="center" style="margin: 0 0 8px 10px;">
                                        <tr style="background: #6c727e8f">
                                            <th width="100">应用节点</th>
                                            <th width="60">集群角色</th>
                                            <th width="100">节点状态</th>
                                            <th width="100">节点信息</th>
                                        </tr>`;
                                    if (data?.membersInfo?.length) {
                                        data.membersInfo.forEach(v => {
                                            quickFeature += `<tr>
                                            <td align="center" title=${v.instanceName || '-'}>${v.instanceName || '-'}</td>
                                            <td align="center" title=${that.$store.state.apmDirDesc?.appInstanceClusterRoleDict[v?.clusterRole] || '-'}>${that.$store.state.apmDirDesc?.appInstanceClusterRoleDict[v?.clusterRole] || '-'}</td>
                                            <td align="center">${that.appWorkStatusDict?.[v.status] || '-'}</td>
                                            <td align="center"><span onclick="clusterGoLink('/ldpDataObservation', '${v.id}')" style="color: #2d8de5; cursor: pointer;">查看</span></td>
                                        </tr>`;
                                        });
                                    } else {
                                        quickFeature += `<tr>
                                        <td style="color: #cacfd4;">暂无数据</td>
                                    </tr>`;
                                    }
                                    quickFeature +=  `</table></div>`;

                                    html += `<div style="font-size: 12px; user-select: text;">
                                    <div style="display: flex; justify-content: space-between; border-bottom: 1px solid #ccc; padding-bottom: 4px; font-weight: 500;">
                                        <span>${params.data.name}</span>
                                        <span style="cursor: pointer;" onmouseover="this.style.color='#298dff';" onmouseout="this.style.color='#fff';" onclick="getMonitorEvents('${data?.baseInfo?.id}')" >告警：${data?.runningInfo?.alertNumber ?? '-'}</span>
                                    </div>
                                    <div>
                                        <div style="display: flex; margin-top: 10px;">
                                            <div style='${titleStyle}'>基础信息:</div>
                                            <ul>
                                                <li style='${liStyle}'><span style="color: #cacfd4;">集群名称: </span> ${params.data.name}</li>
                                                <li style='${liStyle}'><span style="color: #cacfd4;">集群模式:</span> ${ that.$store.state?.apmDirDesc?.clusterTypeDict?.[data?.baseInfo?.clusterType] || '-'}</li>
                                            </ul>
                                        </div>
                                        <div style="display: flex; margin-top: 10px;">
                                            <div style='${titleStyle}'>运行状态:</div>
                                            <ul>
                                                <li style='${liStyle}'><span style="color: #cacfd4;">集群成员个数: </span> ${data?.runningInfo?.memberNumber.toString() || '0'}</li>
                                                <li style='${liStyle}'><span style="color: #cacfd4;">存活成员个数: </span> ${data?.runningInfo?.survivingMember?.toString() || '0'}</li>
                                                <li style='${liStyle}'><span style="color: #cacfd4;">集群状态: </span> ${ params.data.category || '-'}</li>
                                                <li style='${liStyle}'><span style="color: #cacfd4;">最后响应时间: </span> ${data?.runningInfo?.heartbeatTime || '-'}</li>
                                                <li style='${liStyle} padding-bottom: 0;'><span style="color: #cacfd4;">连接关系:</span> <span onclick="handleSearchNode('${params?.data?.id}')" style="color: #2d8de5; cursor: pointer;">查看</span></li>
                                            </ul>
                                        </div>
                                        ${quickFeature}
                                    </div>
                                </div>`;
                                    callback(ticket, html);
                                });
                            } else if (that.type === 'instance') {
                                that.getAppInfo(params.data?.id, function(content) {
                                    const { data } = content;
                                    const liStyle = 'white-space: normal; padding: 0 0 8px 8px; line-height: 12px;';
                                    const titleStyle = 'padding-right: 10px; line-height: 12px; border-right: 1px solid rgba(255, 255, 255, 0.15); font-weight: bold;';
                                    // 快捷跳转
                                    const hasMemoryMdb = data?.operateInfo?.memoryMdb;
                                    const hasMemoryTable = data?.operateInfo?.memoryTable;
                                    const hasManageApi = data?.operateInfo?.manageApi;
                                    const hasLdpDataObservation = data?.operateInfo?.instanceObservation;
                                    const quickFeature  = `
                                            <div style="display: flex; margin-top: 10px;">
                                                <div style='${titleStyle}'>快捷导航:</div>
                                                <ul>
                                                    ${hasMemoryTable ? `<li style='${liStyle}'><span style="color: #cacfd4;">内存数据管理:</span> <span onclick="clusterGoLink('/ldpTable', '${data?.baseInfo?.id}')" style="color: #2d8de5; cursor: pointer;">查看</span></li>` : ''}
                                                    ${hasMemoryMdb ? `<li style='${liStyle}'><span style="color: #cacfd4;">内存数据管理:</span> <span onclick="clusterGoLink('/sqlTable', '${data?.baseInfo?.id}')" style="color: #2d8de5; cursor: pointer;">查看</span></li>` : ''}
                                                    ${hasManageApi ? `<li style='${liStyle}'><span style="color: #cacfd4;">管理功能:</span> <span onclick="clusterGoLink('/managementQuery', '${data?.baseInfo?.id}')" style="color: #2d8de5; cursor: pointer;">查看</span></li>` : ''}
                                                    ${hasLdpDataObservation ? `<li style='${liStyle}'><span style="color: #cacfd4;">实例观测:</span> <span onclick="clusterGoLink('/ldpDataObservation', '${data?.baseInfo?.id}')" style="color: #2d8de5; cursor: pointer;">查看</span></li>` : ''}
                                                    <li style='${liStyle} padding-bottom: 0;'><span style="color: #cacfd4;">连接关系:</span> <span onclick="handleSearchNode('${params.data?.id}')" style="color: #2d8de5; cursor: pointer;">查看</span></li>
                                                </ul>
                                            </div>`;

                                    html += `<div style="font-size: 12px; user-select: text;">
                                    <div style="display: flex; justify-content: space-between; border-bottom: 1px solid #ccc; padding-bottom: 4px; font-weight: 500;">
                                        <span>${params.data.name}</span>
                                        <span style="cursor: pointer;" onmouseover="this.style.color='#298dff';" onmouseout="this.style.color='#fff';" onclick="getMonitorEvents('${data?.baseInfo?.id}')" >告警：${data?.runningInfo?.alertNumber ?? '-'}</span>
                                    </div>
                                    <div>
                                        <div style="display: flex; margin-top: 10px;">
                                            <div style='${titleStyle}'>节点信息:</div>
                                            <ul>
                                                <li style='${liStyle}'><span style="color: #cacfd4;">应用节点名: </span> ${params.data.name}</li>
                                                <li style='${liStyle}'><span style="color: #cacfd4;">应用节点类型:</span> ${that.$store?.state?.apmDirDesc?.appTypeDictDesc?.[data?.baseInfo?.instanceType] || data?.baseInfo?.instanceType || '-'}</li>
                                                <li style='${liStyle}'><span style="color: #cacfd4;">应用节点版本: </span> ${data?.baseInfo?.version || '-'}</li>
                                                <li style='${liStyle} padding-bottom: 0;'><span style="color: #cacfd4;">应用开发平台:</span> ${data?.baseInfo?.developPlatform || '-'}</li>
                                            </ul>
                                        </div>
                                        <div style="display: flex; margin-top: 10px;">
                                            <div style='${titleStyle}'>运行状态:</div>
                                            <ul>
                                                <li style='${liStyle}'><span style="color: #cacfd4;">部署服务器:</span> ${data?.runningInfo?.ip || '-'}</li>
                                                <li style='${liStyle}'><span style="color: #cacfd4;">集群角色: </span> ${that.$store.state.apmDirDesc?.appInstanceClusterRoleDict[data?.runningInfo?.clusterRole] || '-'}</li>
                                                <li style='${liStyle}'><span style="color: #cacfd4;">管理端口: </span> ${data?.runningInfo?.port || '-'}</li>
                                                <li style='${liStyle}'><span style="color: #cacfd4;">节点状态: </span> ${that.appWorkStatusDict[data?.runningInfo?.status] || '-'}</li>
                                                <li style='${liStyle}'><span style="color: #cacfd4;">业务状态: </span> ${that.getBizStatusDesc(data?.runningInfo?.bizStatus) || '-'}</li>
                                                <li style='${liStyle} padding-bottom: 0;'><span style="color: #cacfd4;">最后心跳时间:</span> ${data?.runningInfo?.heartbeatTime || '-'}</li>
                                            </ul>
                                        </div>
                                        ${quickFeature}
                                    </div>
                                </div>`;
                                    callback(ticket, html);
                                });
                            } else if (that.type === 'context') {
                                const contextDetail = params.data?.info;
                                // eslint-disable-next-line complexity
                                that.getAppInfo(params.data?.id, function(content) {
                                    const contentData = content?.data || {};
                                    const liStyle = 'white-space: normal; padding: 0 0 8px 8px; line-height: 12px;';
                                    const titleStyle = 'padding-right: 10px; line-height: 12px; border-right: 1px solid rgba(255, 255, 255, 0.15); font-weight: bold;';

                                    // 快捷跳转
                                    const ctxRole = CTX_ROLE_HOST.includes(contentData?.baseInfo?.role) ? '主' : CTX_ROLE_STANDBY.includes(contentData?.baseInfo?.role) ? '备' : '-';
                                    const defaultMode = contentData?.baseInfo?.syncMode || 0;
                                    const syncMode =  defaultMode === 1 ? '消息复制' : '状态机复制';
                                    html += `<div style="font-size: 12px; user-select: text;">
                                    <div style="border-bottom: 1px solid #ccc; padding-bottom: 4px; font-weight: 500;">${params.data.name}</div>
                                    <div>
                                        <div style="display: flex; margin-top: 10px;">
                                            <div style='${titleStyle}'>基础信息:</div>
                                            <ul>
                                                <li style='${liStyle}'><span style="color: #cacfd4;">上下文ID: </span> <span onclick="topoRcmObsGoLink('${contextDetail?.name || '-'}',${defaultMode})" class="${defaultMode === 0 ? 'text-link' : ''}"> ${contentData?.baseInfo?.contextId || '-'}</span></li>
                                                <li style='${liStyle}'><span style="color: #cacfd4;">上下文名称: </span> <span onclick="topoRcmObsGoLink('${contextDetail?.name || '-'}',${defaultMode})" class="${defaultMode === 0 ? 'text-link' : ''}"> ${contextDetail?.name || '-'}</span></li>
                                                <li style='${liStyle}'><span style="color: #cacfd4;">集群名称: </span> ${contextDetail?.tierName || '-'}</li>
                                                <li style='${liStyle}'><span style="color: #cacfd4;">角色: </span> ${ctxRole || '-'}</li>
                                                <li style='${liStyle}'><span style="color: #cacfd4;">上下文版本: </span> ${contentData?.baseInfo?.version || '-'}</li>
                                                <li style='${liStyle} padding-bottom: 0;'><span style="color: #cacfd4;">同步模式: </span> ${syncMode}</li>
                                            </ul>
                                        </div>
                                        <div style="display: flex; margin-top: 10px;">
                                            <div style='${titleStyle}'>运行状态:</div>
                                            <ul>
                                                <li style='${liStyle}'><span style="color: #cacfd4;">是否在线: </span> ${params.data?.category || '-'}</li>
                                                <li style='${liStyle} padding-bottom: 0;'><span style="color: #cacfd4;">最后心跳时间: </span> ${contentData?.runningInfo?.heartbeatTime || '-'}</li>
                                            </ul>
                                        </div>
                                        <div style="display: flex; margin-top: 10px;">
                                            <div style='${titleStyle}'>快捷导航:</div>
                                            <ul>
                                                <li style='${liStyle}'><span style="color: #cacfd4;">配置详情: </span> <span onclick="rcmConfigGoLink('/rcmDeploy', '${contextDetail?.id || ''}')" style="color: #2d8de5; cursor: pointer;">查看</span></li>
                                                <li style='${liStyle}'><span style="color: #cacfd4;">运行指标: </span> <span onclick="openManageInfo('${params.data.name}', ${defaultMode})" style="color: #2d8de5; cursor: pointer;">查看</span></li>
                                                <li style='${liStyle} padding-bottom: 0;'><span style="color: #cacfd4;">连接关系: </span> <span onclick="handleSearchNode('${contextDetail?.name || ''}')" style="color: #2d8de5; cursor: pointer;">查看</span></li>
                                            </ul>
                                        </div>
                                    </div>
                                </div>`;
                                    callback(ticket, html);
                                });
                                return `<div class="pop-content" style="width: 260px; height: 274px;">
                                <div style="border-bottom: 1px solid #ccc; padding-bottom: 4px; font-size: 12px; font-weight: 500;">${params.data.name}</div>
                                <div style="width: 260px; height: 200px; font-size: 16px; display: flex; justify-content: center; align-items: center;">Loading...</div>
                            </div>`;

                            }
                            return `<div class="pop-content" style="width: 260px; height: 274px;">
                                <div style="border-bottom: 1px solid #ccc; padding-bottom: 4px; font-size: 12px; font-weight: 500;">${params.data.name}</div>
                                <div style="width: 260px; height: 200px; font-size: 16px; display: flex; justify-content: center; align-items: center;">Loading...</div>
                            </div>`;
                        } else {
                            // 查看连接关系弹窗
                            const { data } = params;
                            that.openDrawer(data);
                        }
                    }
                },
                legend: [
                    {
                        data: [...new Set(graph.categories.map(function (a) {
                            return a.name;
                        }))],
                        textStyle: {
                            color: '#fff'
                        }
                    }
                ],
                animationDurationUpdate: 1500,
                animationEasingUpdate: 'quinticInOut',
                series: [
                    {
                        name: '',
                        type: 'graph',
                        layout: 'circular',
                        circular: {
                            rotateLabel: false
                        },
                        data: graph.nodes,
                        links: graph.links,
                        categories: graph.categories,
                        roam: true,
                        edgeSymbolSize: 8,
                        label: {
                            show: true,
                            width: 80,
                            overflow: 'break',
                            // rotate: 10,
                            color: '#fff',
                            position: 'bottom',
                            formatter: '{b}'
                        },
                        labelLayout: {
                            draggable: true
                        },
                        lineStyle: {
                            color: 'source',
                            curveness: 0.3
                        },
                        emphasis: {
                            focus: 'adjacency',
                            lineStyle: {
                                width: 10
                            }
                        }
                    }
                ]
            };
            myChart.setOption(option);
        },
        resize: _.throttle(() => {
            myChart && myChart.resize();
        }, 500),
        // 获取图例选中状态
        getLegendSelectedStatus() {
            const legends = myChart.getOption()?.legend || [];
            const selectedLegends = [];

            // 通过value值找到对应key值
            const getKeyByValue = (value) => {
                return Object.entries(this.appWorkStatusDict).find(
                    ([_, val]) => val === value
                )[0];
            };

            legends.forEach(legend => {
                const data = legend.data || [];
                const selected = legend.selected || {};

                data.forEach(name => {
                    // 默认未设置时为 true(选中)
                    if (selected[name] !== false) {
                        selectedLegends.push(getKeyByValue(name));
                    }
                });
            });

            return selectedLegends;
        },

        async getAppInfo(id, callback) {
            const param = {
                nodeId: id,
                productId: this.productId,
                type: this.type
            };
            if (this.type === 'context') {
                param.rcmId = this.rcmId;
            }
            const res = await getTopoInstanceNode(param);
            callback(res);
        },

        // 应用状态墙和应用topo页面字典与核心数据上场业务状态字典不一致
        getBizStatusDesc(key) {
            switch (key) {
                case 1:
                    return '初始状态';
                case 2:
                    return '数据上场中';
                case 3:
                    return '数据上场完成';
                case 4:
                    return '系统就绪';
                case 5:
                    return '数据下场中';
                case 6:
                    return '数据下场完成';
                case 7:
                    return '系统已下线';
                case 17:
                    return '数据上场失败';
                case 18:
                    return '数据下场失败';
                case 19:
                    return '回切中';
                case 20:
                    return '已回切';
                default:
                    return '';
            }
        },

        /**
         * 查看全部节点连接关系
         */
        viewAllConnection(data) {
            this.drawerInfo = {
                status: true,
                ...data
            };
        },

        // 获取上下文连接信息接口集
        getRcmApiList(source, target) {
            // 特殊处理-线条为双箭头时需要同时获取source->target 以及 target->source的数据
            const apiList = [
                getTopoSessions({
                    rcmId: this.rcmId,
                    senderNode: source,
                    receiverNode: target,
                    productId: this.productId,
                    type: 'context',
                    clusterName: this.queryParam?.clusterName,
                    syncMode: this.queryParam?.syncMode
                })
            ];

            // 判断对端连线是否存在-存在则请求
            for (const item of this.edges) {
                if (item.fromId == target && item.toId == source) {
                    apiList.push(
                        getTopoSessions({
                            rcmId: this.rcmId,
                            senderNode: target,
                            receiverNode: source,
                            productId: this.productId,
                            type: 'context',
                            clusterName: this.queryParam?.clusterName,
                            syncMode: this.queryParam?.syncMode
                        })
                    );
                    break;
                }
            }
            return apiList;
        },

        /**
         * 打开连接信息弹窗
         */
        openDrawer(data) {
            if (this.type === 'context') {
                const { source, target } = data;
                const rcmApiList = this.getRcmApiList(source, target);
                this.drawerInfo = {
                    status: true,
                    nodeType: this.type,
                    rcmId: this.rcmId,
                    rcmName: this.rcmName,
                    source: source,
                    target: target,
                    rcmApiList: rcmApiList
                };
            } else {
                // 获取连线两端节点名称
                const sourceName = (this.nodes || []).find(o => o.id === data.source)?.name;
                const targetName = (this.nodes || []).find(o => o.id === data.target)?.name;
                this.drawerInfo = {
                    status: true,
                    nodeType: this.type,
                    nodeIds: [data.source, data.target],
                    source: sourceName,
                    target: targetName,
                    ...(this.queryParam?.clusterId && { clusterId: this.queryParam.clusterId }),
                    nodeStatuses: this.getLegendSelectedStatus(),
                    // 点击拓扑连线查看连接关系，且发、收节点不同时，需要过滤自收自发的连接关系
                    excludeSenderReceiverSame: data.source !== data.target
                };
            }
        }
    },
    render() {
        return <div
            class="rcm-chart"
            ref="chart"
            style="height: calc(100% - 42px); width: 100%">
            {
                this.drawerInfo.status && <connectivityDrawer
                    productId={this.productId}
                    modalInfo={this.drawerInfo}
                />
            }
        </div>;
    },
    beforeDestroy() {
        window.removeEventListener('resize', this.resize);
    }
};
