.memory-tab-box {
    display: flex;
    width: 100%;
    height: calc(100% - 20px);
    overflow-x: auto;

    & > li {
        width: 240px;
        height: 100%;
        margin-right: 10px;
        border: 1px solid #d9d9d9;
        border-radius: 4px;
        flex-shrink: 0;

        & > section, {
            border-bottom: 1px solid #d9d9d9;
        }

        & > .struct-name {
            display: flex;
            justify-content: space-between;
            width: 100%;
            height: 36px;
            padding: 0 4px;
            line-height: 36px;

            & > span {
                width: 50%;
                flex: 1;
                overflow: hidden;
                text-overflow: ellipsis;
                white-space: nowrap;

                & > .h-input-wrapper {
                    width: 32px;
                    height: 25px;
                    vertical-align: baseline;

                    & > input {
                        height: 25px;
                    }
                }
            }
        }

        & > ul {
            height: calc(100% - 40px) !important;
            padding: 0 6px 10px;
            cursor: pointer;
            overflow-y: hidden;
            overflow-x: hidden;

            & > li {
                display: flex;
                justify-content: space-between;
                line-height: 25px;

                &:hover {
                    color: var(--link-color);
                }

                & > span {
                    overflow: hidden;
                    text-overflow: ellipsis;
                    white-space: nowrap;
                }

                & > .struct-key {
                    padding-right: 8px;
                    flex: 1;
                }

                & > .struct-value {
                    max-width: 50%;
                }
            }

            .li-parent-item {
                &:hover {
                    color: #ccc;
                }
            }

            .li-edit {
                // display: block;
                position: relative;
                height: 65px;
                margin-top: 8px;
                padding: 6px;
                background: #f8f8f8;
                line-height: 14px;

                .struct-value-input {
                    position: absolute;
                    top: 26px;
                    right: 5px;
                    display: block;
                    width: 150px;
                    height: 25px;
                    text-align: right;
                }
            }

            .li-active {
                background: #d8edff;
                color: var(--link-color);
                font-weight: 600;
            }
        }

        & > ul:hover {
            overflow-y: overlay;
        }

        .scrollbar::-webkit-scrollbar {
            width: 2px;
        }

        .scrollbar::-webkit-scrollbar-track-piece {
            background-color: #fff;
        }

        /* 滚动条的内层滑轨背景颜色 */

        .scrollbar::-webkit-scrollbar-track {
            background-color: #fff;
        }

        /* 滚动条的外层滑轨背景颜色 */

        .scrollbar::-webkit-scrollbar-thumb {
            background-color: #d4d8e2;
        }

        /* 滚动条的内层滑块颜色 */

        .scrollbar::-webkit-scrollbar-button {
            background-color: #fff;
            display: none;
        }

        /* 滑轨两头的监听按钮颜色 */
    }

    .li-parent {
        background: #f0f3f8;
    }
}
