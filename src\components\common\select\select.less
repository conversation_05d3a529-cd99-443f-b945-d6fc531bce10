.a-select-error {
    .h-select > .h-select-left {
        border: 1px solid var(--error-color) !important;
    }

    .h-tooltip {
        .h-tooltip-popper[x-placement^="bottom"] .h-tooltip-arrow {
            border-bottom-color: var(--error-color);
        }

        .h-tooltip-popper[x-placement^="top"] .h-tooltip-arrow {
            border-top-color: var(--error-color);
        }

        .h-tooltip-popper[x-placement^="left"] .h-tooltip-arrow {
            border-left-color: var(--error-color);
        }

        .h-tooltip-popper[x-placement^="right"] .h-tooltip-arrow {
            border-right-color: var(--error-color);
        }

        .h-tooltip-inner {
            min-height: 20px;
            padding: 5px;
            background-color: var(--error-color);
            white-space: normal;
        }
    }
}

