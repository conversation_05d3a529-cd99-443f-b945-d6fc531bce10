.apm-box {
    display: flex;
    width: 100%;
    height: calc(100% - 53px);
    min-width: 900px;
    margin-top: 15px;
    border-radius: var(--border-radius);
    background: var(--wrapper-color);

    .left-box {
        height: 100%;
        background: var(--wrapper-color);
        border-right: 1px solid var(--box-color);
        will-change: transform;
        transition: width 0.2s;
        z-index: 6;

        .menu {
            position: relative;
            width: 100%;
            height: 100%;
            border-right: 1px solid var(--base-border-color);

            & > .h-input-wrapper {
                height: 44px;
                padding: 8px 10px 0;
            }

            & > .header-menu {
                width: 100%;
                height: 44px;
                line-height: 44px;
                font-size: var(--font-size);
                color: var(--font-color);
                border-bottom: 1px solid var(--base-border-color);
                white-space: nowrap;
                text-overflow: ellipsis;
                overflow: hidden;
            }

            .h-menu-submenu > .h-menu {
                overflow-y: hidden;
            }

            .h-menu-item {
                padding-left: 30px;
                cursor: pointer;

                .text-method {
                    position: relative;
                }

                .text-method-left {
                    position: absolute;
                    left: 0;
                    top: 5px;
                    width: 22px;
                    height: 22px;
                    line-height: 23px;
                    text-align: center;
                    display: inline-block;
                    background: #1f3759;
                    border-radius: 4px;
                    user-select: none;
                }

                .text-method-left-chi {
                    top: 9px;
                }

                .text-method-right {
                    margin-left: 30px;
                }
            }

            .h-menu-item:hover {
                .text-method-left {
                    background: #202637;
                }
            }

            .h-menu-item-selected {
                .text-method-left {
                    background: #202637;
                }
            }


            & > .menu-ul {
                width: 100%;
                height: calc(100% - 54px);
                margin-top: 10px;
                overflow: auto;

                li {
                    position: relative;
                    overflow: hidden;
                    width: 100%;
                    height: 33px;
                    color: var(--font-color);
                    font-size: var(--font-size-base);
                    line-height: 33px;
                    padding: 0 5px 0 29px;
                    white-space: nowrap;
                    text-overflow: ellipsis;
                    cursor: pointer;

                    &::before {
                        content: "·";
                        position: absolute;
                        left: 14px;
                        top: -2px;
                        font-size: 40px;
                    }

                    &:hover {
                        background: var(--link-opacity-color);

                        &::after {
                            position: absolute;
                            top: 0;
                            left: 0;
                            content: "";
                            width: 4px;
                            height: 33px;
                            background: var(--link-color);
                        }
                    }
                }

                .menu-selected {
                    background: var(--link-opacity-color);

                    &::after {
                        position: absolute;
                        left: 0;
                        top: 0;
                        content: "";
                        width: 4px;
                        height: 33px;
                        background: var(--link-color);
                    }
                }

                .pro-environ::before {
                    .environ("\4EA7");
                }

                .test-environ::before {
                    .environ("\6D4B");
                }
            }

            .menu-footer {
                position: absolute;
                bottom: 5px;
                margin: 0 10px;
                width: calc(100% - 20px);
            }
        }

        .x-resizer {
            position: absolute;
            top: 0;
            bottom: 0;
            right: -2px;
            width: 2px;
            user-select: none;
            cursor: col-resize;
            z-index: 9;

            &:hover {
                background-color: var(--base-color);
            }
        }

        .cate-switch {
            position: absolute;
            top: 50%;
            right: -13px;
            height: 44px;
            width: 12px;
            margin-top: -22px;
            line-height: 44px;
            text-align: center;
            background: rgba(209, 216, 229, 0.2);
            border-radius: 0 4px 4px 0;
            cursor: pointer;
            transform: perspective(0.5em) rotateY(10deg);
            z-index: 10;

            &:hover {
                background: rgba(209, 216, 229, 0.4);
            }
        }

        .environ(@text) {
            display: flex;
            justify-content: center;
            align-items: center;
            width: 22px;
            height: 22px;
            content: @text !important;
            position: absolute;
            left: 4px;
            top: 5px;
            font-size: 12px;
            font-weight: 600;
            border-radius: 50%;
            text-align: center;
            border: 2px solid rgb(192, 196, 203);
            color: rgb(192, 196, 203);
            transform: scale(0.75, 0.75);
        }
    }

    .right-box {
        flex: 1;
        position: relative;
        background: var(--wrapper-color);
        min-width: 75%;
    }
}
