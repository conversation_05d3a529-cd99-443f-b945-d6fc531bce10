<template>
  <div>
    <h-msg-box-safe
        v-model="modalInfo.status"
        :mask-closable="false"
        :escClose="true"
        title="导入用例"
        width="40"
        :maxHeight="400"
        @on-open="cleanFileData"
    >
        <h-form
            ref="forms"
            :model="formItem"
            :label-width="80"
            >
            <h-form-item label="用户协议" prop="userProtocol" required>
                <h-select
                    v-model="formItem.userProtocol"
                    :clearable="false"
                    :positionFixed="true"
                    :setDefSelect="true">
                    <h-option
                        v-for="item in protocolList" :key="item.value" :value="item.value"
                        >{{ item.label }}
                    </h-option>
                </h-select>
            </h-form-item>
            <h-form-item label="上传文件" prop="uploadFile" required>
                <h-upload
                    ref="upload"
                    action=""
                    :before-upload="handleUpload"
                    :accept="formItem.userProtocol === 'LDP_MSG' ? '.json' : ''"
                    >
                    <h-button type="ghost" icon="upload">上传文件</h-button>
                </h-upload>
                <div v-if="file !== null">
                    <!-- 上传成功 -->
                    <div v-if="upLoadStatus === 2"
                        class="file-success">
                        <span>
                            <h-icon name="document"></h-icon>
                            {{file.name}}
                        </span>
                        <h-icon
                            class="icon-success"
                            name="success"
                            color="var(--success-color)"
                            style="float: right;">
                        </h-icon>
                        <h-icon
                            class="icon-error"
                            name="delete"
                            color="var(--error-color)"
                            style="float: right;"
                            @on-click="(e) => deleteUploadFile(e)">
                        </h-icon>
                    </div>
                    <!-- 上传失败 -->
                    <div v-if="upLoadStatus === 3" class="file-error">
                        <h-icon name="document"></h-icon>
                        {{file.name}}
                        <h-icon
                            name="delete"
                            color="var(--error-color)"
                            style="float: right;"
                            @on-click="(e) => deleteUploadFile(e)">
                        </h-icon>
                        <h-poptip style="float: right;"
                            class="apm-poptip popo-icon-pos"
                            trigger="hover"
                            placement="top-end"
                            :content="errorDes">
                            <h-icon name="warning_fill" color="var(--warning-color)"></h-icon>
                        </h-poptip>
                    </div>
                </div>
            </h-form-item>
            <h-form-item v-if="formItem.useCaseDes" label="用例信息" prop="useCaseDes">
                <h-input
                    v-model="formItem.useCaseDes"
                    type="textarea"
                    disabled
                    :autosize="{ minRows: 2, maxRows: 4 }"
                ></h-input>
            </h-form-item>
        </h-form>
        <p slot="footer">
            <h-button @click="cancelMethod">取消</h-button>
            <h-button type="primary" @click="confirmImportCase">确定</h-button>
        </p>
    </h-msg-box-safe>
  </div>
</template>
<script>
import { isJSON } from '@/utils/utils';
import { importUseCaseFile } from '@/api/networkApi';
export default {
    name: 'ImportUseCaseModal',
    props: {
        modalData: {
            type: Object,
            default: function() {
                return {};
            }
        }
    },
    data() {
        return {
            modalInfo: this.modalData,
            loading: false,
            btnLoading: false,
            protocolList: [
                {
                    value: 'LDP_MSG',
                    label: 'LDPMSG'
                }
            ],
            formItem: {
                userProtocol: 'LDP_MSG',
                uploadFile: null,
                useCaseDes: ''
            },
            isSelectFile: false,
            upLoadStatus: 0,
            errorDes: '文件内容校验不通过,请上传正确的json文件',
            file: null
        };
    },
    methods: {
        cancelMethod() {
            this.modalInfo.status = false;
        },
        cleanFileData() {
            this.file = null;
            this.formItem.uploadFile = null;
            this.formItem.useCaseDes = '';
            this.upLoadStatus = 0;
            this.errorDes = '';
        },
        // 文件上传处理
        handleUpload(file) {
            this.cleanFileData();
            this.file = file;
            this.getActionData(file);
            this.$refs['forms'].resetValidate();
            return false;
        },
        // 解析获取文件内容
        getActionData (file) {
            // 文件大小校验 最大15MB
            if (file.size > 15 * 1024 * 1024) {
                this.upLoadStatus = 3;
                this.errorDes = '文件大小超出15MB';
                this.$hMessage.error('文件大小超出15MB');
                return;
            }
            const that = this;
            const reader = new FileReader();// 新建一个FileReader
            reader.readAsText(file, 'UTF-8');// 读取文件
            reader.onload = function (evt) { // 读取完文件之后会回来这里
                const fileString = evt.target.result; // 读取文件内容
                if (isJSON(fileString)) {
                    that.formItem.uploadFile = fileString;
                    const useCaseDes = that.getUseCaseDes(JSON.parse(fileString));
                    that.formItem.useCaseDes = useCaseDes.funcIDsStr;
                    if (useCaseDes.caseErr) {
                        that.upLoadStatus = 3;
                        that.errorDes = useCaseDes.caseErr;
                        that.$hMessage.error(useCaseDes.caseErr);
                    } else {
                        that.upLoadStatus = 2;
                    }
                } else {
                    that.upLoadStatus = 3;
                    that.errorDes = 'json文件内容格式不正确';
                    that.$hMessage.error(that.errorDes);
                }
            };
        },
        // 解析json内容获取用例信息
        getUseCaseDes(json) {
            const funcIDs = [];
            let funcIDsStr = '';
            let caseErr = '';

            if (Array.isArray(json?.FuncNums) && json?.FuncNums?.length) {
                json.FuncNums.forEach(funcNum => {
                    funcIDs.push(funcNum.FuncID);
                });
                funcIDsStr = funcIDs.join(', ');
            } else {
                caseErr = '未识别到用例信息';
            }

            if (funcIDs.some(item => item === null || item === undefined || String(item)?.trim() === '')) {
                caseErr = '检测到非法用例名';
            }

            return { funcIDsStr, caseErr };
        },

        // 删除文件
        deleteUploadFile(e) {
            e && e.stopPropagation();
            this.cleanFileData();
        },
        // 确认导入用例
        confirmImportCase() {
            // 校验json内容
            if (this.upLoadStatus === 3) {
                this.$hMessage.error(this.errorDes);
                return;
            }

            this.$refs['forms'].validate(async (vaild) => {
                if (vaild) {
                    // 调用导入接口
                    const params = {
                        productId: this.modalData.productId,
                        protocol: this.modalData.protocol,
                        context: this.formItem.uploadFile
                    };
                    try {
                        const res = await importUseCaseFile(params);
                        if (res.code === '200') {
                            this.$hMessage.success('用例导入成功');
                            this.modalInfo.status = false;
                            // 更新列表
                            this.$emit('update');
                        } else if (res.code?.length === 8) {
                            this.$hMessage.error(res.message);
                        }
                    } catch (err) {
                        console.error(err);
                    }
                }
            });
        }
    }
};
</script>
<style lang="less" scoped>
.file-success {
    margin: 5px 0 0;
    cursor: pointer;

    .icon-success {
        display: inline-block;
    }

    .icon-error {
        display: none;
    }
}

.file-success:hover {
    background: #e9f3ff;
}

.file-success:hover .icon-error {
    display: inline-block;
}

.file-success:hover .icon-success {
    display: none;
}

.file-error {
    color: var(--error-color);
    margin: 5px 0 0;
    cursor: pointer;
}

/deep/ .h-poptip-body-content-inner {
    color: var(--font-color);
}
</style>
