import fetch from './fetch';
import { isJSON, objectToQueryString } from '@/utils/utils';

const prefix = window['LOCAL_CONFIG']['API_HOME'] || '/';
/**
 * 创建mc产品实例
 */
export function setMcProductInstance(param) {
    return fetch().post(`${prefix}/mc/product`, param);
}

/**
 * 删除mc产品实例
 */
export function delMcProductNode(param) {
    return fetch().post(`${prefix}/product/manage/delete`, param);
}

/**
 * 获取mc产品节点列表
 */
export function getMcProductList() {
    return fetch().get(`${prefix}/mc/products`);
}

/**
 * 获取管理功能接口
 */
export async function getManagerProxy(param, timeout) {
    const res = await fetch({ timeout: timeout }).post(`${prefix}/ldp-manager-proxy/batch`, param);
    if (res?.code === '200'){
        const jsonParsedData = res.data.map(item => (item && isJSON(item)) ? JSON.parse(item) : {});
        return {
            ...res,
            data: jsonParsedData
        };
    }
    return res;
}

// 根据功能名称生成请求param
export function generateApiParam(ipPortList, funcNameList, pluginName) {
    const param = [];
    Array.isArray(ipPortList) && ipPortList.forEach(ipPortItem => {
        Array.isArray(funcNameList) && funcNameList.forEach(item => {
            param.push({
                manageProxyIp: ipPortItem.ip,
                manageProxyPort: ipPortItem.manageProxyPort,
                pluginName: pluginName,
                funcName: item
            });
        });
    });
    return param;
}

// Mc接口请求---接口以 Result 汇总返回  与 ldp观测返回存在差异
// eslint-disable-next-line max-params
export async function getMcAPi(ipPortList, funcNameList, pluginName, isLoop = true) {
    const data = {};
    const param = generateApiParam(ipPortList, funcNameList, pluginName);
    const res = await getManagerProxy(JSON.stringify(param));
    Array.isArray(param) && param.forEach((item, index) => {
        data[item.funcName] = data?.[item?.funcName] || [];
        if (res.code === '200') {
            isLoop && res.data?.[index]?.Result?.length && data[item.funcName].push(...res.data[index].Result);
            !isLoop && res.data?.[index]?.Result?.length && (data[item.funcName] = [...res.data[index].Result]);
        }
    });
    return data;
}

/**
 * 应用实例观测dashboard配置
 */
export function getDashboardConfig(param) {
    return fetch().get(`${prefix}/observables/dashboards/configs?${objectToQueryString(param)}`);
}

//  --------------------------------------------------------  消费积压  -------------------------------------------------------------

/**
 * 获取MC消费者集群消息处理变化趋势
 */
export function getMcMetricsTrend(param) {
    return fetch().post(`${prefix}/product/observation/mc/subscriber/handle-msg/trend`, param);
}

//  --------------------------------------------------------  消息回溯  -------------------------------------------------------------

/**
 * 获取消息回溯查询结果列表
 */
export function getMsgBackward(param) {
    return fetch().get(`${prefix}/product/observation/mc/msg-backwards?${objectToQueryString(param)}`);
}

/**
 * 下载消息回溯消息内容接口
 */
export function downloadMcMsg(param) {
    return fetch({ responseType: 'blob' }).get(`${prefix}/product/observation/mc/msg-backward/msg-content/download?${objectToQueryString(param)}`);
}
