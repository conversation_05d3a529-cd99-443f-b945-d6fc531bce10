<!-- 
  国际化迁移示例文件
  展示如何将硬编码的中文文案替换为国际化键值对
-->

<template>
  <div class="migration-example">
    <!-- ========== 迁移前后对比 ========== -->
    
    <!-- 【迁移前】硬编码中文 -->
    <div class="before-migration" style="display: none;">
      <h-button type="primary">查询</h-button>
      <h-button type="warning">编辑</h-button>
      <h-button type="error">删除</h-button>
      
      <h-select placeholder="请选择节点">
        <h-option value="node1">节点1</h-option>
        <h-option value="node2">节点2</h-option>
      </h-select>
      
      <div class="status-text">
        <span>状态：运行中</span>
        <span>数据加载中...</span>
      </div>
      
      <h-table :data="tableData">
        <h-table-column prop="name" label="名称"></h-table-column>
        <h-table-column prop="status" label="状态"></h-table-column>
        <h-table-column prop="createTime" label="创建时间"></h-table-column>
      </h-table>
      
      <div v-if="!tableData.length" class="no-data">
        暂无数据
      </div>
    </div>

    <!-- 【迁移后】使用国际化 -->
    <div class="after-migration">
      <!-- 通用操作按钮 -->
      <h-button type="primary">{{ $t('common.query') }}</h-button>
      <h-button type="warning">{{ $t('common.edit') }}</h-button>
      <h-button type="error">{{ $t('common.delete') }}</h-button>
      
      <!-- 选择器 -->
      <h-select :placeholder="$t('common.pleaseSelect')">
        <h-option value="node1">{{ $t('pages.managementQuery.node1') }}</h-option>
        <h-option value="node2">{{ $t('pages.managementQuery.node2') }}</h-option>
      </h-select>
      
      <!-- 状态显示 -->
      <div class="status-text">
        <span>{{ $t('common.status') }}：{{ $t('common.running') }}</span>
        <span>{{ $t('common.loading') }}...</span>
      </div>
      
      <!-- 表格 -->
      <h-table :data="tableData">
        <h-table-column prop="name" :label="$t('common.name')"></h-table-column>
        <h-table-column prop="status" :label="$t('common.status')"></h-table-column>
        <h-table-column prop="createTime" :label="$t('common.createTime')"></h-table-column>
      </h-table>
      
      <!-- 空数据提示 -->
      <div v-if="!tableData.length" class="no-data">
        {{ $t('common.noData') }}
      </div>
      
      <!-- 页面特定文案 -->
      <div class="page-specific">
        <h3>{{ $t('pages.managementQuery.title') }}</h3>
        <p>{{ $t('pages.managementQuery.description') }}</p>
      </div>
      
      <!-- 组件特定文案 -->
      <div class="component-specific">
        <span>{{ $t('components.ldpTable.exportData') }}</span>
        <span>{{ $t('components.common.confirmOperation') }}</span>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'MigrationExample',
  data() {
    return {
      tableData: [
        {
          name: 'Node-001',
          status: 'running',
          createTime: '2024-01-01 10:00:00'
        }
      ]
    };
  },
  methods: {
    // ========== JavaScript中的国际化使用 ==========
    
    // 【迁移前】硬编码中文
    showOldMessage() {
      this.$hMessage.success('操作成功');
      this.$hMessage.error('操作失败');
      this.$hMessage.warning('请先选择数据');
    },
    
    showOldConfirm() {
      this.$hMsgBoxSafe.confirm({
        title: '确认删除',
        content: '确定要删除选中的数据吗？此操作不可恢复。',
        onOk: () => {
          console.log('用户确认删除');
        }
      });
    },
    
    // 【迁移后】使用国际化
    showNewMessage() {
      this.$hMessage.success(this.$t('common.operationSuccess'));
      this.$hMessage.error(this.$t('common.operationFailed'));
      this.$hMessage.warning(this.$t('common.pleaseSelectData'));
    },
    
    showNewConfirm() {
      this.$hMsgBoxSafe.confirm({
        title: this.$t('common.confirmDelete'),
        content: this.$t('common.confirmDeleteContent'),
        onOk: () => {
          console.log(this.$t('common.userConfirmed'));
        }
      });
    },
    
    // 动态文案示例
    showDynamicMessage(count) {
      // 使用参数插值
      const message = this.$t('common.selectedCount', { count });
      this.$hMessage.info(message);
    },
    
    // 条件文案示例
    getStatusText(status) {
      const statusMap = {
        'running': this.$t('common.running'),
        'stopped': this.$t('common.stopped'),
        'error': this.$t('common.error')
      };
      return statusMap[status] || this.$t('common.unknown');
    },
    
    // 表单验证文案
    validateForm() {
      const rules = {
        name: [
          { required: true, message: this.$t('common.validation.required') },
          { min: 2, max: 20, message: this.$t('common.validation.lengthRange', { min: 2, max: 20 }) }
        ],
        email: [
          { required: true, message: this.$t('common.validation.required') },
          { type: 'email', message: this.$t('common.validation.email') }
        ]
      };
      return rules;
    }
  },
  
  // 计算属性中使用国际化
  computed: {
    pageTitle() {
      return this.$t('pages.managementQuery.title');
    },
    
    tableColumns() {
      return [
        { prop: 'name', label: this.$t('common.name') },
        { prop: 'status', label: this.$t('common.status') },
        { prop: 'createTime', label: this.$t('common.createTime') },
        { 
          prop: 'actions', 
          label: this.$t('common.actions'),
          render: (h, { row }) => {
            return [
              h('h-button', {
                props: { size: 'small', type: 'primary' },
                on: { click: () => this.editRow(row) }
              }, this.$t('common.edit')),
              h('h-button', {
                props: { size: 'small', type: 'error' },
                on: { click: () => this.deleteRow(row) }
              }, this.$t('common.delete'))
            ];
          }
        }
      ];
    }
  }
};
</script>

<style scoped>
.migration-example {
  padding: 20px;
}

.before-migration {
  border: 2px solid #ff4757;
  padding: 15px;
  margin-bottom: 20px;
  background-color: #ffe6e6;
}

.after-migration {
  border: 2px solid #2ed573;
  padding: 15px;
  background-color: #e6ffe6;
}

.status-text {
  margin: 10px 0;
}

.status-text span {
  margin-right: 15px;
}

.no-data {
  text-align: center;
  color: #999;
  padding: 20px;
}

.page-specific,
.component-specific {
  margin-top: 15px;
  padding: 10px;
  background-color: rgba(255, 255, 255, 0.5);
  border-radius: 4px;
}

.page-specific h3 {
  margin: 0 0 10px 0;
  color: #2c3e50;
}

.component-specific span {
  display: inline-block;
  margin-right: 10px;
  padding: 5px 10px;
  background-color: #3498db;
  color: white;
  border-radius: 3px;
  font-size: 12px;
}
</style>

<!-- 
  使用说明：
  
  1. 导入国际化文件：
     在 main.js 或相应的入口文件中更新导入路径：
     import customLocales from '@/locales/optimized';
  
  2. 常用国际化路径：
     - common.query: 查询
     - common.add: 添加
     - common.edit: 编辑
     - common.delete: 删除
     - common.save: 保存
     - common.cancel: 取消
     - common.success: 成功
     - common.failed: 失败
     - common.loading: 加载中
     - common.noData: 暂无数据
     - common.pleaseSelect: 请选择
     - common.pleaseInput: 请输入
  
  3. 页面特定文案：
     - pages.{pageName}.{key}
     例如：pages.managementQuery.pleaseSelect
  
  4. 组件特定文案：
     - components.{componentName}.{key}
     例如：components.ldpTable.export
  
  5. 参数插值：
     this.$t('common.selectedCount', { count: 5 })
     对应的文案：'已选择 {count} 项'
  
  6. 在模板中使用：
     {{ $t('common.query') }}
  
  7. 在JavaScript中使用：
     this.$t('common.operationSuccess')
-->
