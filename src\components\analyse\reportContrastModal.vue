<template>
    <div>
        <!-- 下发配置列表 -->
        <h-msg-box-safe
            v-model="modalData.status"
            :escClose="true"
            :mask-closable="false"
            title="报表对比"
            width="70"
            @on-open="getCollections"
        >
            <div class="wrapper">
                <div class="title">选择对比实例</div>

            </div>
        </h-msg-box-safe>
    </div>
</template>

<script>
import { getInstanceList } from '@/api/httpApi';

export default ({
    props: {
        modalInfo: {
            type: Object,
            default: null
        }
    },
    data() {
        return {
            loading: false,
            modalData: this.modalInfo

        };
    },
    methods: {
        getCollections() {
            const param = {
                testCaseInstanceName: this.testCaseInstanceName,
                ...this.turnPage
            };
            getInstanceList(param).then(res => {
                if (res.success) {
                    this.tableData = res.data.list;
                    this.total = res.data.totalCount;
                }
            });
        },
        submitConfig() {
            if (this.selectedInfo) {
                this.$emit('update', this.selectedInfo);
            }
        },
        handleSelection(item) {
            this.selectedInfo = item;
        }
    }
});
</script>

<style lang="less" scoped>
/deep/ .h-modal-body {
    padding: 16px 32px;
}

.title {
    margin-bottom: 16px;
}
</style>
