<template>
    <div>
        <!-- 下发配置列表 -->
        <h-msg-box-safe
            v-model="modalData.status"
            :escClose="true"
            :mask-closable="false"
            allowCopy
            title="选择测试实例"
            width="60"
            maxHeight="400"
            @on-open="getCollections"
        >
            <div class="title">
                <h-input v-model="testCaseInstanceName" icon="search" placeholder="搜索测试报告..." style="width: 300px;" @on-enter="getCollections" @on-click="getCollections">
                </h-input>
            </div>
            <a-table
                ref="table"
                maxHeight="290"
                :tableData='tableData'
                :columns="columns"
                :hasPage="true"
                :total="total"
                :highlightRow="true"
                :hasDarkClass="false"
                :showSizer="false"
                :showElevator="false"
                :simple="true"
                @onCurrentChange="handleSelection"
                @query='getCollections'
            />
            <template v-slot:footer>
                <a-button @click="modalData.status = false">取消</a-button>
                <a-button type="primary" @click="submitConfig">确定</a-button>
            </template>
        </h-msg-box-safe>
    </div>
</template>

<script>
import { getInstanceList } from '@/api/httpApi';
import aButton from '@/components/common/button/aButton';
import aTable from '@/components/common/table/aTable';
export default ({
    props: {
        modalInfo: {
            type: Object,
            default: null
        }
    },
    data() {
        return {
            modalData: this.modalInfo,
            testCaseInstanceName: '',
            columns: [
                {
                    title: '实例名称',
                    key: 'instanceName'
                },
                {
                    title: '产品节点',
                    key: 'productInstName'
                },
                {
                    title: '用例名称',
                    key: 'testCaseName'
                },
                {
                    title: '场景名称',
                    key: 'sceneName'
                },
                {
                    title: '测试日期',
                    key: 'startTime'
                }
            ],
            tableData: [],
            total: 0,
            selectedInfo: ''
        };
    },
    methods: {
        getCollections() {
            const param = {
                testCaseInstanceName: this.testCaseInstanceName,
                ...this.$refs['table']?.getPageData()
            };
            getInstanceList(param).then(res => {
                if (res.success) {
                    this.tableData = res.data.list;
                    this.total = res.data.totalCount;
                }
            });
        },
        submitConfig() {
            if (this.selectedInfo) {
                this.$emit('update', this.selectedInfo);
            } else {
                this.$hMessage.warning('请选择实例！');
            }
        },
        handleSelection(item) {
            this.selectedInfo = item;
        }
    },
    components: {  aButton, aTable }
});
</script>

<style lang="less" scoped>
/deep/ .h-modal-body {
    padding: 16px 32px;
    overflow-y: hidden !important;
}

/deep/ .h-page {
    margin: 10px 0 0;
    text-align: right;
}

.title {
    margin-bottom: 16px;
}
</style>
