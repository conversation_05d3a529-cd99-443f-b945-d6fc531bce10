<template>
    <div>
        <h-msg-box-safe
            v-model="modalData.status"
            :escClose="true"
            :mask-closable="false"
            title="执行确认"
            width="60">
            <div>
                <a-tips type="warning" tipText="请确认SQL执行内容与执行对象。"></a-tips>
                <div style="margin: 15px 0;">执行对象：{{ serviceName || '-' }}</div>
                <h-table
                    ref="table"
                    maxHeight="290"
                    :data='tableData'
                    :columns="columns"
                ></h-table>
            </div>
            <template v-slot:footer>
                <h-checkbox
                    v-model="noConfim"
                    style="margin-right: 10px;"
                    @on-change="handleConfirm">不再确认</h-checkbox>
                <h-button @click="modalData.status = false">
                    取消
                </h-button>
                <h-button
                    type="primary"
                    @click="handleSubmit">确认</h-button>
            </template>
        </h-msg-box-safe>
    </div>
</template>

<script>
import _ from 'lodash';
import aTips from '@/components/common/apmTips/aTips';
import { tryGetSqlPretix } from '@/utils/utils';
import { getMemorySqlRouter } from '@/api/memoryApi';
export default ({
    props: {
        modalInfo: {
            type: Object,
            default: null
        }
    },
    data() {
        return {
            modalData: this.modalInfo,
            serviceName: '',
            instances: [],
            clusterList: [],
            sqls: '',
            columns: [{
                key: 'sql',
                title: '执行SQL内容'
            }, {
                key: 'instances',
                title: '执行节点'
            }],
            tableData: [],
            interest: [],
            noConfim: false
        };
    },
    components: { aTips },
    mounted() {
        this.serviceName = this.modalInfo?.instances?.[0]?.serviceName;
        this.instances = this.modalInfo?.instances || [];
        this.clusterList = [...new Set(this.instances.map(o => o.clusterName))];
        this.interest = this.clusterList;
        this.sqls = this.modalInfo.sqlList.join('\n');
        this.getMemorySqlRouter();
    },
    methods: {
        handleConfirm() {
            localStorage.setItem('apm.mdbsql.performConfirm', !this.noConfim);
        },
        // 获取sql路由节点
        async getMemorySqlRouter() {
            this.tableData = [];
            const routes = [];
            this.modalInfo.sqlList.forEach(ele => {
                // SQL执行，APM 自动识别用户SQL是读SQL：SELECT，还是写操作集群中主节点：UPDATE、DELETE、INSERT；并根据读写类型备策略为随机任选一个备。
                const pretix = tryGetSqlPretix(ele);
                const instances = this.getInstancesByRoute(pretix);
                routes.push({
                    sql: ele,
                    instanceIds: instances.map(o => o.value)
                });
            });
            try {
                const res = await getMemorySqlRouter({
                    productInstNo: localStorage.getItem('productInstNo'),
                    routes
                });
                if (res.success) {
                    Array.isArray(res.data) && res.data.forEach(ele => {
                        const labelList = [];
                        const instanceList = [];
                        ele.instanceIds.forEach(item => {
                            const data = _.find(this.instances, ['value', item]);
                            labelList.push(data?.label);
                            instanceList.push(data);
                        });
                        const node = {
                            sql: ele.sql,
                            instances: labelList.join(),
                            instanceList: instanceList
                        };
                        this.tableData.push(node);
                    });
                }
            } catch (error) {
                console.error(error);
            }
        },
        // 根据路由规则判定instances
        getInstancesByRoute(pretix) {
            let instances = [...this.instances]; // 默认节点
            // 判断路由模式---集群、服务
            const sqlRoute = localStorage.getItem('apm.mdbsql.sqlRoute') ? localStorage.getItem('apm.mdbsql.sqlRoute') : 'onlyMaster';
            if (sqlRoute === 'first') {
                if (pretix?.toUpperCase() === 'SELECT') {
                    instances = instances.filter(o => !o.badge);
                } else if (pretix?.toUpperCase() === 'UPDATE' || pretix?.toUpperCase() === 'DELETE' || pretix?.toUpperCase() === 'INSERT'){
                    instances = instances.filter(o => o.badge);
                }
            } else if (sqlRoute === 'onlyMaster') {
                instances = instances.filter(o => o.badge);
            }
            return instances;
        },
        handleSubmit() {
            this.modalData.status = false;
            this.$emit('handleSqlRun', null, this.tableData);
        }
    }
});
</script>

<style lang="less" scoped>
/deep/ .h-modal-body {
    padding: 15px;

    & > .box-records {
        margin-top: 10px;
        height: calc(100% - 42px);
        overflow-y: auto;
    }
}

/deep/ .h-input-disabled {
    color: #777 !important;
}
</style>
