.header-time {
    display: flex;
    justify-content: space-between;
    width: 100%;
    height: 36px;
    margin: 10px auto;
    padding: 12px;
    box-sizing: border-box;
    background: var(--wrapper-color);
    border-radius: var(--border-radius);

    & > div {
        position: relative;
        background: #ffffff1a;
        height: 12px;
        border-radius: 2px;
        cursor: pointer;

        & > span {
            position: absolute;
            top: -3px;
            left: 50%;
            width: 100%;
            text-align: center;
            transform: translateX(-50%);
            color: var(--font-opacity-color);
        }
    }


    .progress {
        background: var(--box-color);
    }

    .progress-disclick {
        cursor: not-allowed;
    }

    .progress-selected {
        height: 16px;
        font-weight: 600;
        font-size: 14px;
        margin-top: -2px;
        border: 1px solid var(--link-color);

        & > span {
            color: var(--font-color);
        }
    }

    .progress:hover {
        height: 16px;
        font-weight: 600;
        font-size: 14px;
        margin-top: -2px;
    }

    .progress-bar {
        display: inline-block;
        position: absolute;
        height: 100%;
        border-radius: 2px;
        transition-property: all;
        transition-duration: 1s;
        opacity: 0.8;
        background: linear-gradient(46deg, #21cff2, #1a74ea 100%, #1a74ea 100%);
    }
}
