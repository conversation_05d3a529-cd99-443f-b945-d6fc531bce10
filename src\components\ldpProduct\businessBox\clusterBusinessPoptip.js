import './businessPoptip.less';
import aTable from '@/components/common/table/aTable';
export default {
    name: 'clusterBusinessPoptip',
    components: { aTable },
    props: {
        placement: {
            type: String,
            default: 'right'
        },
        node: {
            type: Object,
            default: function() {
                return {};
            }
        }
    },
    data() {
        return {
        };
    },
    mounted() {
    },
    methods: {
        getStatusDesc(key) {
            switch (key) {
                case 'runing':
                    return '运行中';

                case 'warning':
                    return '警告';

                case 'exception':
                    return '异常';

                case 'stop':
                    return '停止';

                default:
                    return '未托管';
            }
        }
    },
    render() {
        const node = this.node;
        const instanceName = node?.target?.resourceName || node?.target?.runningInfo?.instanceName || '-';
        const instanceDesc = this.$store.state.apmDirDesc?.appTypeDictDesc?.[node?.target?.baseInfo?.clusterInstanceType] || node?.target?.baseInfo?.clusterInstanceType || '-';
        const baseInfo = node?.target?.baseInfo || {};
        const runningInfo = node?.target?.runningInfo || {};
        return <h-poptip
            trigger="click"
            placement={this.placement}
            transfer={true}
            customTransferClassName="pop-business"
            title={`${instanceDesc}(${instanceName})`}
            style="width: 100%;">
            <div class="pop-business-label">
                <p>{instanceName}</p>
                {runningInfo?.survivingMemberNum?.toString() && <div class="pop-business-value">{runningInfo?.survivingMemberNum?.toString() || '0'}</div>}
            </div>
            <div class="pop-content large-pop-content" slot="content">
                <div class="pop-content-info">
                    <div class="info-title">基础信息</div>
                    <ul>
                        <li>
                            <span>集群名称：</span>{ baseInfo?.clusterName || '-'}
                        </li>
                        <li>
                            <span>集群模式：</span>{ this.$store.state?.apmDirDesc?.clusterTypeDict?.[baseInfo?.clusterType] || '-'}
                        </li>
                    </ul>
                </div>
                <br />
                <div class="pop-content-info">
                    <div class="info-title">运行状态</div>
                    <ul>
                        <li>
                            <span>集群成员个数：</span>{ baseInfo.runningInfo?.memberNumber.toString() || '0'}
                        </li>
                        <li>
                            <span>存活成员个数：</span>{ runningInfo?.survivingMemberNum?.toString() || '0'}
                        </li>
                        <li>
                            <span>集群状态：</span>{ runningInfo?.status ? this.getStatusDesc(runningInfo?.status) : '-'}
                        </li>
                        <li>
                            <span>最后响应时间：</span>{ runningInfo?.heartbeatTime || '-'}
                        </li>
                    </ul>
                </div>
                <br />
                <div class="pop-content-info">
                    <div class="info-title">集群成员</div>
                    <div class="info-role">
                        <h-row style="background: #6c727e8f">
                            <h-col span="7">应用节点</h-col>
                            <h-col span="1">&nbsp;</h-col>
                            <h-col span="7">仲裁节点</h-col>
                            <h-col span="1">&nbsp;</h-col>
                            <h-col span="7">集群角色</h-col>
                        </h-row>
                        {
                            baseInfo?.members?.length ? baseInfo.members.map((v) => {
                                return  <h-row>
                                    <h-col span="7" title={v.instanceName || '-'}>{v.instanceName || '-'}</h-col>
                                    <h-col span="1">&nbsp;</h-col>
                                    <h-col span="7" title={v.instanceArbName || '-'}>{v.instanceArbName || '-'}</h-col>
                                    <h-col span="1">&nbsp;</h-col>
                                    <h-col span="7" title={this.$store.state.apmDirDesc?.appInstanceClusterRoleDict[v?.clusterRole] || '-'}>{this.$store.state.apmDirDesc?.appInstanceClusterRoleDict[v?.clusterRole] || '-'}</h-col>
                                </h-row>;
                            }) :  <h-row>
                                <h-col span="8">&nbsp;</h-col>
                                <h-col span="8" style="color: #cacfd4;">暂无数据</h-col>
                                <h-col span="8">&nbsp;</h-col>
                            </h-row>
                        }
                    </div>
                </div>
            </div>
        </h-poptip>;
    }
};
