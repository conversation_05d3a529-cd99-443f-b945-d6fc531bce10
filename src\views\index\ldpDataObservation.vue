<template>
    <div class="main">
        <div class="main-top">
            <span class="currenttime">今天是: {{ currentTime }}</span>
            <p>{{ $getProductType(productInfo.productType) }}</p>
            <h-select v-show="productList.length > 1" v-model="productInstNo" class="securities"
                placeholder="请选择" :positionFixed="true" :clearable="false"
                @on-change="checkProduct">
                <h-option v-for="item in productList" :key="item.id" :value="item.productInstNo">{{ item.productName
                }}</h-option></h-select>
        </div>
        <a-loading v-if="loading"></a-loading>
        <div v-if="menuList.length" class="container">
            <menu-layout ref="menu-layout" customMenu @menu-fold="menuFold">
                <template v-slot:menu>
                    <div class="menu">
                        <div class="header-menu" :style="{ padding: menuFoldStatus ? '0' : '0px 10px' }">已托管应用节点</div>
                        <h-menu theme="dark" :active-name="selectMenu" style="height: calc(100% - 44px);"
                            :open-names="openMenu" @on-select="selectMenuChange">
                            <submenu v-for="item in menuList" :key="item.groupName" :name="item.groupName">
                                <template v-slot:title>{{ item.groupName }}
                                </template>
                                <h-menu-item v-for="child in item.menuItems" :key="child.id" :name="child.id">
                                    <span>{{ child.instanceName }}</span>
                                </h-menu-item>
                            </submenu>
                        </h-menu>
                    </div>
                </template>
                <template v-slot:right>
                    <h-tabs v-if="selectMenu && editableTabs.length" ref="tabs" v-model="tabName"
                        :showArrow="maxTabNum ? true : false" @on-click="tabClick(tabName)">
                        <h-tab-pane v-for="item in editableTabs" :key="item.name" :label="item.describe" :name="item.name">
                            <component :is='item.name' :ref="item.name" :nodeData="menu" :pollTime="pollTime"
                                @get-core-ip="getCoreIp" @clear="clearPolling"></component>
                        </h-tab-pane>
                    </h-tabs>
                    <no-data v-else></no-data>
                </template>
            </menu-layout>
        </div>
        <div v-else style="height: calc(100% - 110px);">
            <no-data></no-data>
        </div>
    </div>
</template>

<script>
import _ from 'lodash';
import { formatDate, getSpanWidth } from '@/utils/utils';
import { mapState, mapActions } from 'vuex';
import { getProductInstances } from '@/api/productApi';
import { getDashboardConfig } from '@/api/mcApi';
import aLoading from '@/components/common/loading/aLoading';
import noData from '@/components/common/noData/noData';
import menuLayout from '@/components/common/menuLayout/menuLayout';
import nodeConfig from '@/components/ldpDataObservation/front/nodeConfig.vue';
import backendStat from '@/components/ldpDataObservation/front/backendStat.vue';
import clientAccess from '@/components/ldpDataObservation/front/clientAccess.vue';
import nodeInfo from '@/components/ldpDataObservation/nodeInfo.vue';
import zkConfig from '@/components/ldpDataObservation/zkConfig.vue';
import nodeDeploy from '@/components/ldpDataObservation//core/nodeDeploy.vue';
import funNumProcess from '@/components/ldpDataObservation/core/funNumProcess.vue';
import threadQueue from '@/components/ldpDataObservation/core/threadQueue.vue';
import connectionInfo from '@/components/ldpDataObservation/core/connectionInfo.vue';
import backendExecute from '@/components/ldpDataObservation/todb/backendExecute.vue';
import backendConfig from '@/components/ldpDataObservation/todb/backendConfig.vue';
import todbConnection from '@/components/ldpDataObservation/todb/todbConnection.vue';
import backendMonitorInfo from '@/components/ldpDataObservation/todb/backendMonitorInfo.vue';
import backendQuery from '@/components/ldpDataObservation/todb/backendQuery.vue';
import groupInfo from '@/components/ldpDataObservation/offer/groupInfo.vue';
import statisticsInfo from '@/components/ldpDataObservation/offer/statisticsInfo.vue';
import offerThreadQueue from '@/components/ldpDataObservation/offer/offerThreadQueue.vue';
import offerBaseInfo from '@/components/ldpDataObservation/offer/offerBaseInfo.vue';

export default {
    components: {
        noData, menuLayout, aLoading,
        nodeConfig, backendStat, clientAccess, nodeInfo, zkConfig,
        funNumProcess, threadQueue, connectionInfo, nodeDeploy,
        backendExecute, backendConfig, todbConnection, backendMonitorInfo, backendQuery,
        groupInfo, statisticsInfo, offerThreadQueue, offerBaseInfo
    },
    data() {
        return {
            editableTabs: [],
            pollTime: 0,
            loading: false,
            currentTime: new Date(),
            productInstNo: '',
            productInfo: {},
            tabName: 'nodeInfo',
            selectMenu: '', // menu默认选择
            menu: {}, // 选中的menu信息
            menuFoldStatus: false,
            menuList: [],
            openMenu: [], // 默认展开
            timer: null,
            isFirstRender: true,
            maxTabNum: 0
        };
    },
    async mounted() {
        // 监听当前标签页是否活跃
        await this.init();
        window.addEventListener('resize', this.resize);
    },
    beforeDestroy() {
        this.clearPolling(this.timer);
        window.removeEventListener('resize', this.resize);
    },
    computed: {
        ...mapState({
            productList: (state) => {
                return state.product.productListLight || [];
            }
        }),
        // 交易时间节点
        timeNode: function () {
            return [{ startTime: '09:15:00', endTime: '09:30:00', name: '集合竞价' },
                { startTime: '09:30:00', endTime: '11:30:00', name: '早盘竞价交易' },
                { startTime: '11:30:00', endTime: '13:00:00', name: '盘休' },
                { startTime: '13:00:00', endTime: '14:57:00', name: '午盘竞价交易' },
                { startTime: '14:57:00', endTime: '15:00:00', name: '盘后定价' }];
        },
        appTypeDictDesc() {
            return this.$store?.state?.apmDirDesc?.appTypeDictDesc || {};
        },
        // 核心分类
        coreTypeDict() {
            return this.$store?.state?.apmDirDesc?.coreTypeDict || {};
        }
    },
    methods: {
        ...mapActions({ getProductList: 'product/getProductListLight' }),
        // 菜单展开收起状态
        menuFold(status) {
            this.menuFoldStatus = status;
            setTimeout(() => {
                this.maxTabNum = this.getMaxTabNum();
            }, 500);
        },
        resize() {
            this.maxTabNum = this.getMaxTabNum();
        },
        // 初始化页面
        async init() {
            try {
                this.loading = true;
                await this.getProductList({ filter: 'excludeLdpApm' });
                const productInstNo = localStorage.getItem('productInstNo');
                this.productInstNo = _.find(this.productList, ['productInstNo', productInstNo])?.productInstNo || this.productList?.[0]?.productInstNo;
                this.currentTime = formatDate(new Date());
                setTimeout(() => {
                    this.loading = false;
                }, 1000);
            } catch (e) {
                this.loading = false;
                console.error(e);
            }
        },
        // 切换产品
        async checkProduct(val) {
            this.clearData();
            this.clearPolling();
            this.loading = true;
            if (this.productList.length) {
                this.loading = true;
                this.productInfo = val ? _.find(this.productList, ['productInstNo', val]) : this.productList[0];
                localStorage.setItem('productInstNo', this.productInfo.productInstNo);
                await this.getProductInstances();
                this.getMenuList();
                this.$nextTick(async() => {
                    // 跳转
                    const instance = _.find(this.productInfo.instances || [], ['id', this.$route.query.instanceId]);
                    const menuId = this.isFirstRender ? instance?.id || this.menuList?.[0]?.menuItems?.[0]?.id : this.menuList?.[0]?.menuItems?.[0]?.id;
                    if (menuId) {
                        const groupName = this.getMenuIndex(menuId) || this.menuList?.[0]?.groupName;
                        this.tabName = this.isFirstRender ? this.$route.query.tabName || '' : '';
                        await this.selectMenuChange(menuId);
                        this.openMenu = [groupName];
                    }
                    this.isFirstRender = false;
                });
            }
            setTimeout(() => {
                this.loading = false;
            }, 1000);
        },
        // 跳转id所在的groupName
        getMenuIndex(id) {
            for (const menu of Object.values(this.menuList)) {
                const items = _.find(menu.menuItems, ['id', id]);
                if (items) {
                    return menu.groupName;
                }
            }
            return '';
        },
        // 获取核心ip用于回库查询数据
        getCoreIp(callback) {
            for (const instance of Object.values(this.productInfo.instances)) {
                if (!instance?.instanceIdentities?.includes('bizproc')) continue;
                const manageProxyPort = instance?.manageProxyPort || '';
                callback(manageProxyPort);
                break;
            }
        },
        // 手动清空数据
        clearData() {
            this.menu = {};
            this.selectMenu = '';
            this.menuList = [];
            this.openMenu = [];
            this.productInfo = {};
            this.tabName = '';
            this.pollTime = 0;
            this.editableTabs = [];
        },
        // 获取应用节点列表
        async getProductInstances() {
            const res = await getProductInstances({ productId: this.productInfo.id });
            if (res.code === '200') {
                this.productInfo.instances = res?.data?.instances || [];
            }
        },
        // 获取 menuList
        getMenuList() {
            const menuList = {};
            const instances = this.productInfo.instances;
            instances.forEach((v) => {
                menuList[v.instanceType] = menuList[v.instanceType] || {};
                menuList[v.instanceType].type = v.instanceType;
                menuList[v.instanceType].groupName = this.appTypeDictDesc[v.instanceType] || v.instanceType;
                menuList[v.instanceType].menuItems = menuList[v.instanceType].menuItems || [];
                menuList[v.instanceType].menuItems.push({ ...v });
            });
            this.menuList = Object.values(menuList);

            // 判断左侧菜单少于一个选项时，隐藏菜单
            this.$nextTick(() => {
                if (this.$refs['menu-layout']) {
                    const isOnlyOneItem = this.menuList?.length <= 1 && (this.menuList?.[0]?.menuItems?.length <= 1);
                    this.$refs['menu-layout'].setMenuStatus(!this.menuList?.length || isOnlyOneItem);
                }
            });
        },
        // 切换列表
        async selectMenuChange(id) {
            this.menu = _.find(this.productInfo.instances, ['id', id]);
            this.selectMenu = id;
            if (!this.menu?.manageProxyIp || !this.menu?.manageProxyPort) {
                this.$hMessage.error('当前应用节点不支持观测！请重新选择节点');
                this.menu = {};
                this.editableTabs = [];
                return;
            }
            await this.getDashboardConfig();
            this.resize();

            const tab = _.find(this.editableTabs, ['name', this.tabName]);
            this.tabName = tab ? this.tabName : this.editableTabs?.[0]?.name || 'nodeInfo';
            this.$nextTick(async () => {
                this.clearPolling(this.timer);
                await this.tabClick(this.tabName);
            });
        },
        async getDashboardConfig() {
            const res = await getDashboardConfig({
                productId: this.productInfo.id,
                type: this.menu.instanceType,
                developPlatform: this.menu.developPlatform
            });
            if (res.code === '200') {
                this.editableTabs = res?.data?.filter(v => v?.visible === true) || [];
            }
        },
        // 切换tab
        async tabClick(name) {
            this.tabName = name;
            this.clearPolling(this.timer);
            const currentTab = _.find(this.editableTabs, ['name', name]);
            currentTab?.timerSwitch && await this.setPolling(name, currentTab?.timerInterval || 5);
            this.$nextTick(async () => {
                this.$refs?.[name]?.[0] && await this.$refs[name][0].initData();
            });
        },
        // 轮询调用接口构建页面
        async setPolling(name, timerInterval) {
            this.pollTime = timerInterval;
            this.timer = setInterval(() => {
                this.$refs?.[name]?.[0] && this.$refs[name][0].getFileData();
            }, timerInterval * 1000);
        },
        // 清理定时器
        clearPolling(time) {
            const timer = time || this.timer;
            if (timer){
                for (let i = 1; i <= timer; i++) {
                    clearInterval(i);
                }
            }
        },
        // 获取可见tab最大个数
        getMaxTabNum() {
            const tabsRef = this.$refs.tabs;
            let totalWidth = 0;
            let num = 0;
            if (tabsRef && tabsRef?.$el?.querySelectorAll('.h-tabs-nav-container')?.[0]) {
                const navWidth = tabsRef.$el.querySelectorAll('.h-tabs-nav-container')[0]?.offsetWidth - 10;
                for (const [index, tab] of Object.entries(this.editableTabs)) {
                    const width = getSpanWidth(tab.describe) + 80;
                    totalWidth += width;
                    if (totalWidth > navWidth) {
                        num = index - 1;
                        break;
                    }
                }
            }
            return num < 0 ? 0 : num;
        }
    }
};
</script>

<style lang="less" scoped>
@import url("@/assets/css/input.less");
@import url("@/assets/css/tab.less");
@import url("@/assets/css/menu.less");

.main {
    .container {
        height: calc(100% - 60px);
        border-radius: var(--border-radius);
        background: none;
        margin-top: 10px;

        & > .apm-box {
            height: 100%;
            min-width: 900px;
            margin-top: 0;
            background: none;
        }

        /deep/ .right-box {
            background: none;
            padding: 0 10px;
        }

        /deep/ .h-tabs-bar {
            margin-bottom: 5px;
        }

        /deep/ .h-tabs-content-wrap {
            height: calc(100% - 50px);
        }
    }
}
</style>
