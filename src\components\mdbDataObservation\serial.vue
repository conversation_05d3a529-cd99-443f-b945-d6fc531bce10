<template>
    <div ref="tab-box" class="tab-box">
        <a-loading v-if="loading"></a-loading>
        <div v-else>
            <!-- Sequences -->
            <obs-table
                ref="table1"
                :title="dirTitle"
                :maxHeight="tableHeight"
                :tableData="dirTableData"
                :columns="dirColumns"
                :notSetWidth="true"
                :autoHeadWidth="true"
                :hasPage="false"
                @check-change="handleCheckChange" />
        </div>
    </div>
</template>

<script>
import { getManagerProxy } from '@/api/mcApi';
import aLoading from '@/components/common/loading/aLoading';
import obsTable from '@/components/common/obsTable/obsTable';
export default {
    props: {
        nodeData: {
            type: Object,
            default: () => { }
        }
    },
    components: { aLoading, obsTable },
    data() {
        return {
            loading: false,
            tableHeight: 0,
            dirTitle: {
                label: 'Sequences'
            },
            dirColumns: [
                {
                    title: 'No',
                    key: 'No'
                },
                {
                    title: 'Start',
                    key: 'Start'
                },
                {
                    title: 'Increment',
                    key: 'Increment'
                },
                {
                    title: 'SyncInterval',
                    key: 'SyncInterval'
                },
                {
                    title: 'Current',
                    key: 'Current'
                },
                {
                    title: 'LastSync',
                    key: 'LastSync'
                }
            ],
            dirTableData: []
        };
    },
    mounted() {
        this.fetTableHeight();
        window.addEventListener('resize', this.fetTableHeight);
    },
    beforeDestroy() {
        window.removeEventListener('resize', this.fetTableHeight);
    },
    methods: {
        async initData() {
            this.loading = true;
            await this.getFileData();
            this.loading = false;
        },
        async getFileData() {
            try {
                const { sequences } = await this.getAPi();
                this.dirTableData = sequences.Sequences;
            } catch (error) {
                this.$emit('clear');
            }
        },
        // 接口请求
        async getAPi() {
            const data = {
                sequences: {}
            };
            const param = [
                {
                    manageProxyIp: this.nodeData.ip,
                    manageProxyPort: this.nodeData.manageProxyPort,
                    pluginName: 'ldp_bizproc',
                    funcName: 'uftmdb_data_GetMdbSequenceInfo'
                }
            ];
            const res = await getManagerProxy(JSON.stringify(param));
            if (res.code === '200') {
                !res.data?.[0]?.ErrorNo && Object.keys(res.data?.[0]).length && (data.sequences = res.data[0]);
            }
            return data;
        },
        handleCheckChange(val, key) {
            this.$emit('check-change', val, key);
        },
        // 设置table高度
        fetTableHeight() {
            this.tableHeight = this.$refs['tab-box']?.offsetHeight - 64;
        }
    }
};
</script>

<style lang="less" scoped>
.tab-box {
    height: 100%;
    overflow: hidden;

    .product-info {
        width: 100%;
        padding: 10px 0;
        word-wrap: break-word;
        overflow: auto;

        & > p {
            display: inline-block;
            color: var(--font-color);
            padding-top: 10px;
            padding-right: 20px;
            line-height: 15px;

            & > span {
                padding-left: 13px;
                color: var(--font-opacity-color);
            }
        }
    }

    /deep/ .vue-grid-item.vue-grid-placeholder {
        background: var(--base-color);
    }
}
</style>
