<template>
    <div>
        <h-msg-box-safe v-model="modalData.status" :closable="false" :mask-closable="false" title="导入SQL文件" width="70" height="75" allowCopy>
            <h-upload
                action=""
                accept=".sql"
                :before-upload="handleUpload">
                <h-button type="ghost" icon="upload">选择要执行的文件</h-button>
            </h-upload>
            <div v-if="file !== null" style="position: absolute; left: 200px; top: 83px;">
                待执行文件：{{ file.name }}
                <h-button type="primary" :loading="loadRuning" style="margin-left: 10px;" @click="() => {handleSqlFile()}">
                    {{ loadRuning ? "执行中" : "执行SQL文件" }}
                </h-button>
            </div>
            <h-simple-table
                :columns="columns"
                :data="tableData"
                :show-header="true"
                showTitle
                :disabled-hover="true"
                :maxHeight="400"
                style="margin-top: 15px;"
            ></h-simple-table>
            <template v-slot:footer>
                <h-button @click="cancleMsg">取消</h-button>
            </template>
        </h-msg-box-safe>
    </div>
</template>
<script>
import { getSqlListFromSqlStr, tryGetSqlPretix } from '@/utils/utils';
import { getMemoryDataBySql } from '@/api/memoryApi';
export default {
    props: {
        modalInfo: {
            type: Object,
            default: null
        }
    },
    data() {
        return {
            modalData: this.modalInfo,
            file: null,
            sqlCode: '',
            loadingStatus: false,
            loadRuning: false,  // 执行sql状态
            columns: [
                {
                    title: '执行SQL',
                    key: 'sql',
                    ellipsis: true
                },
                {
                    title: '执行结果',
                    key: 'status',
                    width: 120,
                    render: (h, params) => {
                        if (typeof (params.row.status) === 'boolean') {
                            if (params.row.status) {
                                return h('div', '成功');
                            } else {
                                return h('icon', {
                                    class: ['rotation'],
                                    props: {
                                        name: 'load-d'
                                    }
                                }, '运行中');
                            }
                            // 服务模式
                        } else if (params.row.status === 'allError') {
                            return h('span', {
                                style: {
                                    color: '#2d8de5'
                                },
                                on: {
                                    click: () => {
                                        this.$emit('errorInfo', params.row.instanceErrorMsg);
                                    }
                                }
                            }, '失败');
                        } else if (params.row.status === 'partialError') {
                            return h('span', {
                                style: {
                                    color: '#2d8de5'
                                },
                                on: {
                                    click: () => {
                                        this.$emit('errorInfo', params.row.instanceErrorMsg);
                                    }
                                }
                            }, '部分失败');
                        }
                        return h('div', [
                            h(
                                'Poptip',
                                {
                                    class: 'apm-poptip',
                                    props: {
                                        title: '错误详情',
                                        placement: 'left',
                                        width: 400,
                                        trigger: 'hover',
                                        transfer: true,
                                        positionFixed: true
                                    }
                                },
                                [
                                    h(
                                        'Button',
                                        {
                                            props: {
                                                size: 'small',
                                                type: 'text'
                                            },
                                            style: {
                                                textDecoration: 'underline',
                                                padding: 0,
                                                color: '#ff4b4b'
                                            }
                                        },
                                        '执行异常'
                                    ),
                                    h(
                                        'div',
                                        {
                                            slot: 'content',
                                            class: ''
                                        },
                                        [
                                            h(
                                                'pre',
                                                {},
                                                JSON.stringify(params.row.status, null, 4)
                                            )
                                        ]
                                    )
                                ]
                            )
                        ]);
                    }
                }
            ],
            tableData: []
        };
    },
    mounted() {
        window.addEventListener('beforeunload', this.handleBeforeLoad);
        this.tableData = [];
    },
    beforeDestroy() {
        window.removeEventListener('beforeunload', this.handleBeforeLoad);
    },
    methods: {
        handleBeforeLoad(e) {
            e.preventDefault();
 	        e.returnValue = '';
        },
        // 默认SELECT添加limit
        addLimitToSQL(sql) {
            let newSql = '';
            const selectPattern = /\bSELECT\b/i;
            const limitPattern = /\bLIMIT\b/i;
            const list = sql.split(';');
            if (list[0].trim()) {
                if (!selectPattern.test(list[0]) || limitPattern.test(list[0])) {
                    newSql = list[0] + ';';
                } else if (selectPattern.test(list[0])) {
                    newSql = list[0] + ' LIMIT 0, 1000;';
                }
            }
            return newSql;
        },
        handleUpload(file) {
            const that = this;
            this.loadingStatus = true;
            const reader = new FileReader();
            // 读取文件中的内容，执行读文件函数，设置编码格式。
            reader.readAsText(file, 'UTF-8');
            // 读取文件，得到文件内容。
            reader.onload = (e) => {
                that.loadingStatus = false;
                that.sqlCode = e.target.result;
            };
            this.file = file;
            return false;
        },
        // 根据路由规则判定instances
        getInstancesByRoute(pretix, type) {
            let instances = [...this.modalInfo.instance]; // 默认节点
            if (type === 'service' || type === 'cluster'){
            // 判断路由模式---集群、服务
                const sqlRoute = localStorage.getItem('apm.mdbsql.sqlRoute') ? localStorage.getItem('apm.mdbsql.sqlRoute') : 'first';
                if (sqlRoute === 'first') {
                    if (pretix?.toUpperCase() === 'SELECT') {
                        instances = this.modalInfo.instance.filter(o => !o.badge);
                    } else if (pretix?.toUpperCase() === 'UPDATE' || pretix?.toUpperCase() === 'DELETE' || pretix?.toUpperCase() === 'INSERT'){
                        instances = this.modalInfo.instance.filter(o => o.badge);
                    }
                } else if (sqlRoute === 'onlyMaster') {
                    instances = this.modalInfo.instance.filter(o => o.badge);
                }
            }
            return instances;
        },
        // 设置SQL执行参数
        setSqlExecutionParams(param, type, instances) {
            if (type === 'service') {
                param.instanceIds = instances.map(item => item.value).join();
                param.serviceCode = this.modalData.serviceCode;
            } else {
                param.instanceId = instances?.[0]?.value;
            }
        },
        async handleSqlFile(catchList) {
            const sqlList = catchList || getSqlListFromSqlStr(this.sqlCode);
            if (!sqlList.length) {
                this.$hMessage.error('SQL输入不能为空！');
                return;
            }
            this.loadRuning = true;
            const param = {
                productInstNo: localStorage.getItem('productInstNo'),
                endpointId: this.modalInfo.endpointId,
                page: 1,
                pageSize: 10,
                performSqlTimeout: this.modalInfo.timeout * 1000
            };
            if (!catchList) this.tableData = [];
            // 当前选择为集群还是节点
            const type = this.modalInfo.type;
            while (sqlList.length) {
                if (!this.modalData.status) break;
                const item = sqlList.splice(0, 1)[0];
                param.sql = this.addLimitToSQL(item);

                const pretix = tryGetSqlPretix(param.sql);

                const instances = this.getInstancesByRoute(pretix, type);
                // 设置SQL执行参数
                this.setSqlExecutionParams(param, type, instances);

                try {
                    const node = {
                        sql: param.sql,
                        status: false
                    };
                    this.tableData.push(node);
                    const res = await getMemoryDataBySql(param, param.performSqlTimeout + 2000);

                    if (res.success) {
                        if (type === 'service') {
                            const instanceErrorMsg = res?.data?.instanceErrorMsg;
                            const isAllError = (instanceErrorMsg || [])?.length === instances.length;
                            const status = !(instanceErrorMsg || [])?.length ? true : isAllError ? 'allError' : 'partialError';
                            this.tableData.at(-1).status = status;
                            this.tableData.at(-1).instanceErrorMsg = instanceErrorMsg;
                        } else {
                            this.tableData.at(-1).status = true;
                        }
                    } else {
                        this.tableData.at(-1).status = res;
                        if (!sqlList.length) {
                            this.$hMessage.error(`${param.sql}' 执行异常!`);
                        } else {
                            this.$hMsgBoxSafe.confirm({
                                title: `SQL执行异常`,
                                okText: '继续',
                                cancelText: '终止',
                                content: `'${param.sql}' 执行异常，您确定要继续执行剩余SQL吗？`,
                                onOk: () => {
                                    this.handleSqlFile(sqlList);
                                },
                                onCancel: () => {
                                    return;
                                }
                            });
                        }
                        break;
                    }
                } catch (error) {
                    console.error(error);
                }
            }
            this.loadRuning = false;
        },
        // 取消
        cancleMsg() {
            if (this.loadRuning) {
                this.$hMsgBoxSafe.confirm({
                    title: `异常终止`,
                    okText: '继续',
                    cancelText: '终止',
                    content: `您有批量SQL正在执行，是否要强制终止？`,
                    onOk: () => {
                        return;
                    },
                    onCancel: () => {
                        this.modalData.status = false;
                        return;
                    }
                });
            } else {
                this.modalData.status = false;
            }
        }
    }
};
</script>
<style>
.h-upload-goto-add.h-upload {
    position: relative;
    padding-bottom: 50px;
}

.h-upload-goto-add .h-upload-self {
    position: absolute;
    bottom: 0;
    right: 0;
}

.rotation {
    display: inline-block;
    margin-left: 10px;
    animation: rotate 2s linear infinite;
}

@keyframes rotate {
    from {
        transform: rotate(0deg);
    }

    to {
        transform: rotate(359deg);
    }
}
</style>
