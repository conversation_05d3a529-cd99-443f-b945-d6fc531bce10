<template>
    <div class="tab-box">
        <a-loading v-if="loading"></a-loading>
        <div v-else>
            <!-- 总览 -->
            <info-grid :gridData="redoOverview"></info-grid>
            <div v-if="infoData.details.length" >
                <info-scroll-bar :data="infoData" :selectInfoId="selectInfoId" @info-bar-click="handleBarClicK"></info-scroll-bar>
                <!-- 处理详情指标 -->
                <info-grid v-if="selectInfoId" :gridData="redoDetail"></info-grid>
            </div>
        </div>
    </div>
</template>

<script>
import _ from 'lodash';
import aLoading from '@/components/common/loading/aLoading';
import infoGrid from '@/components/common/infoBar/infoGrid';
import infoScrollBar from '@/components/common/infoBar/infoScrollBar';
import { getManagerProxy } from '@/api/mcApi';
import { formatChartNumber } from '@/utils/utils';

export default {
    props: {
        nodeData: {
            type: Object,
            default: () => { }
        },
        pollTime: {
            type: Number,
            default: 0
        }
    },
    components: { aLoading, infoGrid, infoScrollBar },
    data() {
        const reSendColumns = [
            {
                title: '错误类',
                key: 'errorType',
                ellipsis: true
            },
            {
                title: '重发间隔(s)',
                key: 'ResendIntervalSec',
                ellipsis: true
            },
            {
                title: '最大重发次数',
                key: 'MaxSendCount',
                minWidth: 115,
                ellipsis: true
            },
            {
                title: '所有redo重发次数',
                key: 'ReSendCount',
                minWidth: 135,
                ellipsis: true
            }
        ];
        const detailColumns = [
            {
                title: '类型/指标',
                key: 'type',
                ellipsis: true
            },
            {
                title: '重试次数',
                key: 'RetryCount',
                ellipsis: true
            },
            {
                title: '丢弃次数',
                key: 'DropCount',
                ellipsis: true
            }
        ];
        return {
            loading: true,
            // 回库Redo处理总览
            redoOverview: {
                title: {
                    label: '回库Redo处理总览',
                    slots: [
                        {
                            type: 'text', // 文本
                            label: '主备状态',
                            value: '-'
                        }
                    ]
                },
                layout: [
                    { x: 0, y: 0, w: 2, h: 4.4, i: '1' },
                    { x: 0, y: 1, w: 2, h: 4.3, i: '2' },
                    { x: 0, y: 2, w: 2, h: 4.3, i: '3' },
                    { x: 2, y: 0, w: 4, h: 13, i: '4' },
                    { x: 6, y: 0, w: 6, h: 13, i: '5' }
                ],
                details: [
                    {
                        type: 'text',
                        title: 'RecvRemoterReqTotalCount',
                        info: {
                            key: 'RecvRemoterReqTotalCount',
                            value: '-'
                        }
                    },
                    {
                        type: 'text',
                        title: 'RecvRemoterRspTotalCount',
                        info: {
                            key: 'RecvRemoterRspTotalCount',
                            value: '-'
                        }
                    },
                    {
                        type: 'text',
                        title: 'RecvRemoterInvalidTotalCount',
                        info: {
                            key: 'RecvRemoterInvalidTotalCount',
                            value: '-'
                        }
                    },
                    {
                        type: 'description',
                        title: '回库文件队列状态',
                        infoDic: [
                            {
                                label: 'SentSucc',
                                key: 'SentSucc',
                                span: 12
                            },
                            {
                                label: 'SentFail',
                                key: 'SentFail',
                                span: 12
                            },
                            {
                                label: 'PutSucc',
                                key: 'PutSucc',
                                span: 12
                            },
                            {
                                label: 'PutFail',
                                key: 'PutFail',
                                span: 12
                            },
                            {
                                label: 'WriteIndex',
                                key: 'WriteIndex',
                                span: 12
                            },
                            {
                                label: 'ReadIndex',
                                key: 'ReadIndex',
                                span: 12
                            },
                            {
                                label: 'GetSucc',
                                key: 'GetSucc',
                                span: 24
                            },
                            {
                                label: 'ClockGettime',
                                key: 'ClockGettime',
                                span: 24
                            }
                        ],
                        info: {
                            SentSucc: '-',
                            SentFail: '-',
                            PutSucc: '-',
                            PutFail: '-',
                            WriteIndex: '-',
                            ReadIndex: '-',
                            GetSucc: '-',
                            ClockGettime: '-'
                        }
                    },
                    {
                        type: 'table',
                        title: '重发错误处理跟踪',
                        info: {
                            tableData: [],
                            height: '203',
                            columns: reSendColumns
                        }
                    }
                ]
            },
            // 核心集群redo处理详情
            infoData: {
                title: {
                    label: '核心集群redo处理详情',
                    slots: [
                        {
                            type: 'text',
                            label: '核心集群数',
                            value: '-'
                        }
                    ]
                },
                direction: 'row',
                configData: {
                    scrollX: true,
                    probeType: 3
                },
                details: []
            },
            selectInfoId: '',
            // redoDetail
            redoDetail: {
                layout: [
                    { x: 0, y: 0, w: 4, h: 13, i: '1' },
                    { x: 4, y: 0, w: 8, h: 13, i: '2' },
                    { x: 0, y: 1, w: 4, h: 13, i: '3' },
                    { x: 4, y: 1, w: 8, h: 13, i: '4' }
                ],
                details: [
                    {
                        type: 'obj',
                        title: '核心集群消息处理吞吐',
                        infoDic: [
                            {
                                label: 'RecvRemoterReqCount',
                                key: 'RecvRemoterReqCount'
                            }
                        ],
                        info: {
                            RecvRemoterReqCount: '-'
                        }
                    },
                    {
                        type: 'chart',
                        info: {
                            basicOpiton: {
                                chartType: 'axis',
                                lineDeploy: [
                                    {
                                        name: 'RecvRemoterReqCount',
                                        type: 'line'
                                    }
                                ],
                                yAxiDeploy: [
                                    {
                                        name: '吞吐量',
                                        axisLabel: {
                                            formatter: formatChartNumber
                                        }
                                    }
                                ]
                            },
                            additionalOpiton: {
                                backgroundColor: '#2d334c'
                            },
                            chartData: {
                                xData: [],
                                data: {
                                    RecvRemoterReqCount: []
                                }
                            }
                        }
                    },
                    {
                        type: 'obj',
                        title: 'Redo分阶段处理观测指标',
                        infoDic: [
                            {
                                label: 'IgnoreRecvReqCount',
                                key: 'IgnoreRecvReqCount'
                            },
                            {
                                label: 'Filter1RejectCount',
                                key: 'Filter1RejectCount'
                            },
                            {
                                label: 'LocateErrorCount',
                                key: 'LocateErrorCount'
                            },
                            {
                                label: 'Filter2RejectCount',
                                key: 'Filter2RejectCount'
                            },
                            {
                                label: 'DealErrorCount',
                                key: 'DealErrorCount'
                            }
                        ],
                        info: {
                            IgnoreRecvReqCount: '-',
                            Filter1RejectCount: '-',
                            LocateErrorCount: '-',
                            Filter2RejectCount: '-',
                            DealErrorCount: '-'
                        }
                    },
                    {
                        type: 'table',
                        title: '重发错误处理跟踪',
                        info: {
                            tableData: [],
                            columns: detailColumns,
                            height: '203'
                        }
                    }
                ]
            },
            getMonitor: {},
            time: 5 * 60
        };
    },
    methods: {
        async initData() {
            this.loading = true;
            this.selectInfoId = '';
            this.getMonitor = {};
            this.cleanRedoDetail();
            await this.getFileData();
            this.loading = false;
        },
        // 点击事件
        handleBarClicK(id){
            if (this.selectInfoId !== id){
                this.cleanRedoDetail();
            }
            this.selectInfoId = id;
            this.getFileData();
        },
        // 回库Redo处理总
        setRedoOverview(getMonitor, getFileQueueInfo){
            this.redoOverview.title.slots[0].value = getMonitor.Role;
            this.redoOverview.details[0].info.value = getMonitor.RecvRemoterReqTotalCount;
            this.redoOverview.details[1].info.value = getMonitor.RecvRemoterRspTotalCount;
            this.redoOverview.details[2].info.value = getMonitor.RecvRemoterInvalidTotalCount;
            this.redoOverview.details[3].info = { ...getFileQueueInfo };
            this.redoOverview.details[4].info.tableData = [];
            const tableData = [];
            for (const [key, item] of Object.entries(getMonitor?.ResultHandler || {})){
                tableData.push({ errorType: key, ...item });
            }
            this.redoOverview.details[4].info.tableData = [...tableData];
        },
        // 构建redo处理数据
        setClusterMonitor(infodata, getClusterInfo, newTime){
            this.infoData.title.slots[0].value = '-';
            this.infoData.details = [];
            const details = [];
            infodata.forEach((info) => {
                const clusterInfo =  _.find(getClusterInfo, ['ClusterName', info.ClusterName]) || {};
                details.push(
                    {
                        type: 'obj',
                        title: info.ClusterName,
                        infoId: info.ClusterName,
                        poptipInfo: {
                            title: '集群信息',
                            content: {
                                ClusterNo: clusterInfo?.ClusterNo,
                                TopicName: clusterInfo?.TopicName,
                                PartitionNo: clusterInfo?.PartitionNo,
                                MinTransNo: clusterInfo?.MinTransNo,
                                TransportId: clusterInfo?.TransportId,
                                TxContextId: clusterInfo?.TxContextId,
                                ClusterName: clusterInfo?.ClusterName,
                                FrontEndName: clusterInfo?.FrontEndName,
                                RecvStatus: clusterInfo?.RecvStatus,
                                StartOnce: clusterInfo?.RecvStatus
                            }
                        },
                        canClick: true,
                        infoDic: [
                            {
                                label: 'MinTransNo',
                                key: 'MinTransNo'
                            },
                            {
                                label: 'LastRecvTransNo',
                                key: 'LastRecvTransNo'
                            },
                            {
                                label: 'LastRecvTime',
                                key: 'LastRecvTime'
                            }
                        ],
                        info: { ...info }
                    });
            });
            this.infoData.title.slots[0].value = details.length;
            this.infoData.details = [...details];
            const infoIds = details.map(v => v?.infoId);
            this.selectInfoId = infoIds.indexOf(this.selectInfoId) !== -1 ? this.selectInfoId : this.infoData.details?.[0]?.infoId || '';
            this.setRedoDetail(this.selectInfoId, newTime);
        },
        // 点击事件-展示数据
        setRedoDetail(id, newTime){
            const clusterInfo =  _.find(this.getMonitor?.ClusterMonitor || [], ['ClusterName', id]) || {};
            this.setChartData(clusterInfo?.RecvRemoterReqCount, newTime);
            this.redoDetail.details[0].info.RecvRemoterReqCount = clusterInfo?.RecvRemoterReqCount;
            this.redoDetail.details[2].info = { ...clusterInfo };
            this.redoDetail.details[3].info.tableData = [];
            const typesList = {};
            Object.keys(clusterInfo)?.length && Object.keys(clusterInfo).forEach(v => {
                const key = v.indexOf('RetryCount') !== -1 ? v.replace('RetryCount', '') : v.indexOf('DropCount') !== -1 ? v.replace('DropCount', '') : '';
                key && (typesList[key] = {
                    type: key,
                    RetryCount: clusterInfo[key + 'RetryCount'],
                    DropCount: clusterInfo[key + 'DropCount']
                });
            });
            this.redoDetail.details[3].info.tableData = [...Object.values(typesList)] || [];
        },
        // 点击事件-- 清理数据
        cleanRedoDetail(){
            this.redoDetail.details[0].info.RecvRemoterReqCount = '-';
            this.redoDetail.details[2].info = {};
            this.redoDetail.details[3].info.tableData = [];
            this.redoDetail.details[1].info.chartData.xData = [];
            this.redoDetail.details[1].info.chartData.data.RecvRemoterReqCount = [];
        },
        // chart数据
        setChartData(recvRemoterReqCount, newTime){
            const index = this.redoDetail.details[1].info.chartData.xData.indexOf(newTime);
            this.$set(this.redoDetail.details[1].info.chartData.data.RecvRemoterReqCount, index, recvRemoterReqCount);
        },
        // chart时间轴
        setChartTime(){
            const chartData = this.redoDetail.details[1].info.chartData;
            if (chartData.xData.length > (this.time / this.pollTime)) {
                chartData.xData.shift();
                chartData.data.RecvRemoterReqCount.shift();
            }
            const newTime = this.$getCurrentLocalTime();
            chartData.xData.indexOf(newTime) === -1 && chartData.xData.push(newTime);
            return newTime;
        },
        // 构造页面数据
        async getFileData() {
            // chart时间轴
            const newTime = this.setChartTime();
            const { getMonitor, getFileQueueInfo, getClusterInfo } = await this.getAPi();
            this.getMonitor = getMonitor;
            // 回库Redo处理总览
            this.setRedoOverview(getMonitor, getFileQueueInfo);
            // 核心集群redo处理详情
            this.setClusterMonitor(getMonitor?.ClusterMonitor || [], getClusterInfo, newTime);
        },
        // 接口请求
        async getAPi() {
            const data = {
                getMonitor: {},
                getFileQueueInfo: {},
                getClusterInfo: []
            };
            const param = [
                {
                    manageProxyIp: this.nodeData.ip,
                    manageProxyPort: this.nodeData.manageProxyPort,
                    pluginName: 'ldp_todb',
                    funcName: 'GetMonitor'
                },
                {
                    manageProxyIp: this.nodeData.ip,
                    manageProxyPort: this.nodeData.manageProxyPort,
                    pluginName: 'ldp_todb',
                    funcName: 'GetFileQueueInfo'
                },
                {
                    manageProxyIp: this.nodeData.ip,
                    manageProxyPort: this.nodeData.manageProxyPort,
                    pluginName: 'ldp_todb',
                    funcName: 'GetClusterInfo'
                }
            ];
            try {
                const res = await getManagerProxy(JSON.stringify(param));
                if (res.code === '200') {
                    !res.data?.[0]?.ErrorNo && Object.keys(res.data?.[0]).length  && (data.getMonitor = res.data[0]);
                    !res.data?.[1]?.ErrorNo && Object.keys(res.data?.[1]).length  && (data.getFileQueueInfo = res.data[1]);
                    res.data?.[2]?.clusterInfo?.length && (data.getClusterInfo = res.data[2].clusterInfo);
                }
                return data;
            } catch (err) {
                this.$emit('clear');
            }
        }
    }
};
</script>
