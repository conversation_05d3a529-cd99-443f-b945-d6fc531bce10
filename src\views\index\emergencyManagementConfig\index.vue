<template>
    <div class="main">
        <div class="title">
            <a-title title="配置应急修改">
                <slot>
                    <h-select v-show="productList.length > 1" v-model="productInstNo" placeholder="请选择"
                        class="securities" placement="bottom"
                        :positionFixed="true" :clearable="false" @on-change="checkProduct">
                        <h-option v-for="item in productList" :key="item.id" :value="item.productInstNo">{{
                        item.productName }}</h-option>
                    </h-select>
                </slot>
            </a-title>
        </div>
        <a-loading v-if="loading"></a-loading>
        <div class="container">
            <menu-layout ref="menu-layout" customMenu @menu-fold="menuFold">
                <template v-slot:menu>
                    <div class="menu">
                        <div class="header-menu" :style="{ padding: menuFoldStatus ? '0' : '0px 10px' }">
                            <h-input
                                v-model="searchVal"
                                icon="search"
                                clearable
                                placeholder="输入节点名称"
                                @on-change="onChangeSearch"
                            />
                            <div class="container-left-line"></div>
                        </div>
                        <h-menu v-if="appTreeList.length > 0 && computedAppTreeList.length > 0" :key="searchVal" ref="menu" theme="dark" :active-name="selectMenu" style="height: calc(100%);"
                            :openNames="openMenu" @on-select="selectMenuChange" @on-open-change="onOnOpenChange">
                            <submenu v-for="item in computedAppTreeList" :key="item.id" :name="item.id">
                                <template v-slot:title>{{ item.title }}
                                </template>
                                <h-menu-item v-for="child in item.children" :key="child.id" :name="child.id">
                                    <span>{{ child.title }}</span>
                                </h-menu-item>
                            </submenu>
                        </h-menu>
                        <no-data v-if="computedAppTreeList.length === 0 && !menuFoldStatus" />
                    </div>
                </template>
                <template v-slot:right>
                    <div class="container-right">
                        <route-config v-if="node !== null" ref="config" :key="node && node.id" :node="node" />
                        <no-data v-else />
                    </div>
                </template>
            </menu-layout>
        </div>
    </div>
</template>
<script>
import _ from 'lodash';
import { mapState, mapActions } from 'vuex';
import aTitle from '@/components/common/title/aTitle';
import aLoading from '@/components/common/loading/aLoading';
import menuLayout from '@/components/common/menuLayout/menuLayout';
import { getProductInstances } from '@/api/productApi';
import RouteConfig from './routeConfig.vue';
import noData from '@/components/common/noData/noData';
export default {
    components: {
        aTitle, menuLayout, aLoading, RouteConfig, noData
    },
    data() {
        return {
            productInstNo: '', // 选中的产品
            loading: false,
            selectMenu: null,
            node: null,
            productInfo: {},
            searchVal: '',
            appTreeList: [],
            openMenu: [],
            menuFoldStatus: false
        };
    },
    computed: {
        ...mapState({
            productList: state => {
                return state.product.productListLight || [];
            }
        }),
        computedAppTreeList() {
            if (!this.searchVal) {
                return this.appTreeList;
            }
            const val = this.searchVal.toLowerCase();
            return (this.appTreeList || []).reduce((pre, item) => {
                const temp = { ...item };
                const newChildren = [];
                temp.children.forEach(child => {
                    const title = String(child.title || '').toLowerCase();
                    if (title.includes(val)) {
                        child.expand = true;
                        newChildren.push(child);
                    }
                });
                temp.children = newChildren;
                if (newChildren.length) {
                    item.expand = true;
                    temp.expand = true;
                    return [...pre, temp];
                }
                return pre;
            }, []);
        }
    },
    mounted() {
        this.init();
    },
    methods: {
        ...mapActions({ getProductList: 'product/getProductListLight' }),
        menuFold(status) {
            this.menuFoldStatus = status;
        },
        async selectMenuChange(id) {
            this.selectMenu = id;
            for (const item of this.appTreeList) {
                let found = false;
                for (const sub of item.children) {
                    if (sub.id === id) {
                        this.node = sub;
                        found = true;
                        break;
                    }
                }
                if (found) {
                    break;
                }
            }
        },
        async init() {
            try {
                this.loading = true;
                await this.getProductList({ filter: 'excludeLdpApm' });
                const productInstNo = localStorage.getItem('productInstNo');
                this.productInstNo = _.find(this.productList, ['productInstNo', productInstNo])?.productInstNo || this.productList?.[0]?.productInstNo;
                const productInfo = _.find(this.productList, ['productInstNo', this.productInstNo]);
                this.productInfo = productInfo || {};
            } catch (e) {
                console.error(e);
            } finally {
                this.loading = false;
            }
        },
        onOnOpenChange(names) {
            this.openMenu = names;
        },
        /**
         * 切换导航产品
         * @param {String} productInstNo
         */
        checkProduct(productInstNo) {
            this.searchVal = '';
            localStorage.setItem('productInstNo', productInstNo);
            this.productInstNo = productInstNo;
            const productInfo = _.find(this.productList, ['productInstNo', this.productInstNo]);
            this.productInfo = productInfo || {};
            this.callGetProductInstances();
        },
        async callGetProductInstances() {
            try {
                const res = await getProductInstances({ productId: this.productInstNo });
                if (res.code === '200') {
                    const instances = (res?.data?.instances || []).filter(item => item.instanceIdentities && item.instanceIdentities.includes('front'));
                    const instancesObj = {};
                    instances.forEach(item => {
                        if (instancesObj[item.instanceType]) {
                            instancesObj[item.instanceType].push(item);
                        } else {
                            instancesObj[item.instanceType] = [item];
                        }
                    });
                    const { appTypeDictDesc } =  this.$store?.state?.apmDirDesc || {};
                    const result = [];
                    Object.keys(instancesObj).forEach((key, index) => {
                        instancesObj[key].forEach(instance => {
                            instance.title = instance.instanceName;
                        });
                        result.push({
                            id: key,
                            title: appTypeDictDesc[key] ?? key,
                            children: instancesObj[key],
                            selected: false,
                            expand: index === 0
                        });
                    });
                    this.appTreeList = result;
                    if (result.length) {
                        this.$nextTick(() => {
                            this.openMenu = [result[0]?.id];
                            this.selectMenu = result[0].children?.[0]?.id;
                            this.node = result[0].children?.[0];
                        });
                    } else {
                        this.reset();
                    }
                } else {
                    this.reset();
                }
            } catch (error) {
                console.log('初始化配置应急失败', error);
                this.reset();
            }
        },
        reset() {
            this.appTreeList = [];
            this.node = null;
            this.selectMenu = null;
        },
        onChangeSearch() {
            this.$nextTick(() => {
                this.openMenu = this.computedAppTreeList.map(item => item.id);
            });
        }
    }
};
</script>
<style lang="less" scoped>
@import url("@/assets/css/tab.less");
@import url("@/assets/css/input.less");
@import url("@/assets/css/menu.less");

.container {
    display: flex;
    height: 100%;

    &-left {
        width: 200px;
        padding: 8px;
        background: #262d43;

        &-line {
            background: #31364a;
            margin-top: 8px;
            margin-bottom: 15px;
            height: 1px;
            width: 100%;
        }

        &-wrap {
            overflow-y: auto;
            height: calc(100% - 100px);
            padding-bottom: 100px;
        }
    }

    &-right {
        flex: 1;
        margin-left: 15px;
    }

    /deep/ .h-tree-arrow {
        i {
            color: #fff;
        }
    }

    /deep/ .apm-box {
        background-color: unset !important;
        overflow-y: hidden;
        height: calc(100% - 7px);

        .container-right {
            height: 100%;

            .footer {
                display: flex;
                align-items: center;
                justify-content: center;
                border-top: 1px solid #444a60;
                height: 44px;
                background: #262d43;
            }
        }

        .right-box {
            background-color: unset !important;
        }
    }

    /deep/ .h-btn-disable[disabled="disabled"] {
        background: #33394e;
        border: none;
        color: #969797;

        &:hover {
            background: #33394e;
            border: none;
            color: #969797;
        }
    }

    /deep/ .h-btn-text {
        background-color: unset !important;

        &:hover {
            background-color: unset !important;
            color: #969797;
        }
    }
}
</style>
