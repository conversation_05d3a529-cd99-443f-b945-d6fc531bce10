<template>
    <div class="main">
        <!-- 头部标题 -->
        <a-title :title="`场景：${sceneName}`">
            <slot>
                <div class="slot-box">
                    <a-button v-if="sceneId" type="dark" @click="exportFile">导出场景</a-button>
                    <a-button  type="dark" @click="openUploadModal">导入场景</a-button>
                    <a-button type="dark" @click="addScene">新建场景</a-button>
                </div>
            </slot>
        </a-title>
        <div v-if="!sceneId" style="height: calc(100% - 50px);">
            <no-data text="场景信息为空,请新建或导入场景信息"/>
        </div>
        <!-- 测试工具实例配置 -->
        <analyse-config-shell
            v-show="sceneId"
            ref="analyse-config-shell"
            :sceneId='sceneId'
            :productList="productList"
            :caseList="caseList"
            @query-instance="queryInstance"
            @set-scene-info="setSceneInfo"
        ></analyse-config-shell>
        <!-- 测试时延数据 -->
        <analyse-case-instance
            v-show="sceneId"
            ref="analyse-case-instance"
            :sceneId='sceneId'
            :productList="productList"
            :caseList="caseList"
        ></analyse-case-instance>
        <!-- 新建场景 -->
        <add-scene-modal v-if="addSceneInfo.status" :modalInfo="addSceneInfo" @update="updateScene" />
        <!-- 导入文件 -->
        <import-file-modal v-if="fileInfo.status" :modalInfo="fileInfo" />
    </div>
</template>

<script>
import aTitle from '@/components/common/title/aTitle';
import aButton from '@/components/common/button/aButton';
import noData from '@/components/common/noData/noData';
import addSceneModal from '@/components/analyse/addSceneModal';
import analyseCaseInstance from '@/components/analyseConfig/analyseCaseInstance';
import analyseConfigShell from '@/components/analyseConfig/analyseConfigShell';
import importFileModal from '@/components/analyse/importFileModal.vue';
import { exportSceneApi, getTestManageList } from '@/api/httpApi';
import { exportFile } from '@/utils/utils';

export default {
    data() {
        return {
            IMG_HOME: this.IMG_HOME,
            sceneId: '',            // 场景ID
            sceneName: '',          // 场景Name
            apiDemoInfo: '',        // apidemo基本信息
            caseList: [],           // 用例列表
            addSceneInfo: {
                status: false
            },
            fileInfo: {
                status: false
            },
            productList: []
        };
    },
    async mounted() {
        // 查询产品节点
        await this.getTestManageList();
        // 查询场景信息
        await this.$refs['analyse-config-shell'].querySceneInfo();
    },
    methods: {
        // 获取产品节点信息
        getTestManageList() {
            return new Promise((resolve, reject) => {
                getTestManageList().then(res => {
                    if (res.success) {
                        this.productList = res.data;
                        resolve(true);
                    } else {
                        resolve(false);
                    }
                });
            });
        },
        // 查询场景信息后更新信息
        setSceneInfo(val){
            this.sceneId = val.sceneId;
            this.sceneName = val.sceneName;
            this.apiDemoInfo = val.apiDemoInfo;
            this.caseList = val.caseList;
        },
        // 新建场景
        addScene() {
            this.addSceneInfo.status = true;
        },
        // 导出文件
        async exportFile() {
            if (!this.sceneId) {
                this.$hMessage.error('请先新建测试场景!');
                return;
            }
            const res = await exportSceneApi(this.sceneId);
            if (res.success) {
                await exportFile(res.data.context, res.data.fileName, 'txt');
                this.$hMessage.success('导出场景数据成功!');
                return;
            }
            this.$hMessage.error('导出场景失败!');
        },
        // 打开导入场景窗口
        openUploadModal() {
            this.fileInfo.status = true;
        },
        // 更新场景ID
        updateScene(id) {
            this.sceneId = id;
            this.$refs['analyse-config-shell'].querySceneInfo();
        },
        // 获取实例列表
        queryInstance(id, items, isChange) {
            this.$refs['analyse-case-instance'].queryInstance(id, items, isChange);
        }
    },
    components: { aButton, aTitle, noData, analyseConfigShell, addSceneModal, analyseCaseInstance, importFileModal }
};
</script>

<style lang="less" scoped>
@import url("@/assets/css/input.less");

.main {
    .slot-box {
        float: right;
        padding-right: 10px;
    }

    @media screen and (max-height: 620px) {
        .header {
            height: 38px;
            line-height: 38px;
            font-size: var(--font-size);
        }

        .header::before {
            display: inline-block;
            position: relative;
            left: -5px;
            top: 6px;
            content: "";
            width: 5px;
            height: 25px;
            background: var(--link-color);
        }
    }
}
</style>
