/*
 * @Description: 
 * @Author: <PERSON><PERSON>
 * @Date: 2023-03-20 15:31:23
 * @LastEditTime: 2023-03-21 10:24:08
 * @LastEditors: Zale Ying
 */
module.exports = {
    extends: [require.resolve("@toolkit-js/iconfig/lib/stylelintrc-hs")],
    overrides: [
        {
            files: ["**/*.{html,vue}"],
            customSyntax: "postcss-html",
        },
        {
            files: ["**/*.less"],
            customSyntax: "postcss-less",
        },
        {
            files: ["**/*.css"],
            customSyntax: "postcss",
        },
    ],
    rules: {
        indentation: 4,
        "plugin/z-index-value-constraint": {
            min: 0,
            max: 100,
        },
        "no-descending-specificity": null,
        'max-nesting-depth':null,
        "linebreaks":null
    },
};
